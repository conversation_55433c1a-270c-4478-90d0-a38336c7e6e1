<view class="assigned-appointments-container">
  <!-- 顶部状态栏 -->
  <view class="page-header">
    <view class="back-icon" bindtap="goBack">
      <text class="iconfont icon-back">←</text>
    </view>
    <view class="header-title">我的预约</view>
    <view class="placeholder"></view>
  </view>
  
  <!-- 状态筛选区域 -->
  <view class="status-filter">
    <scroll-view scroll-x="true" class="filter-scroll" enhanced="true" show-scrollbar="{{false}}">
      <view 
        class="filter-item {{currentStatus === '' ? 'active' : ''}}" 
        data-status="" 
        bindtap="filterByStatus"
      >全部</view>
      <view 
        class="filter-item {{currentStatus === 'confirmed' ? 'active' : ''}}" 
        data-status="confirmed" 
        bindtap="filterByStatus"
      >待核销</view>
      <view 
        class="filter-item {{currentStatus === 'pending' ? 'active' : ''}}" 
        data-status="pending" 
        bindtap="filterByStatus"
      >待确认</view>
      <view 
        class="filter-item {{currentStatus === 'completed' ? 'active' : ''}}" 
        data-status="completed" 
        bindtap="filterByStatus"
      >已完成</view>
      <view 
        class="filter-item {{currentStatus === 'cancelled' ? 'active' : ''}}" 
        data-status="cancelled" 
        bindtap="filterByStatus"
      >已取消</view>
    </scroll-view>
  </view>

  <!-- 内容区域 - 使用scroll-view使其可滚动 -->
  <scroll-view 
    scroll-y="true" 
    class="content-scroll" 
    bindscrolltolower="loadMoreAppointments" 
    enhanced="true" 
    show-scrollbar="true" 
    bounces="true"
    enable-back-to-top="true"
    scroll-anchoring="true"
    refresher-enabled="{{true}}"
    refresher-threshold="{{100}}"
    refresher-triggered="{{isRefreshing}}"
    bindrefresherrefresh="onPullDownRefresh"
  >
    <view class="content">
      <!-- 加载中 -->
      <view class="loading" wx:if="{{isLoading}}">
        <view class="loading-icon"></view>
        <view class="loading-text">加载中...</view>
      </view>

      <!-- 空状态 -->
      <view class="empty-state" wx:if="{{!isLoading && appointmentList.length === 0}}">
        <view class="empty-icon">📋</view>
        <view class="empty-text">暂无指定给您的预约</view>
        <view class="empty-tips">客户指定您的预约将在这里显示</view>
      </view>

      <!-- 预约列表 -->
      <view class="appointment-list" wx:if="{{!isLoading && appointmentList.length > 0}}">
        <block wx:for="{{appointmentList}}" wx:key="_id">
          <view class="appointment-item">
            <view class="appointment-header">
              <view class="appointment-status {{item.status}}">{{item.statusText}}</view>
              <view class="appointment-time">{{item.date}} {{item.time}}</view>
            </view>
            
            <view class="appointment-info">
              <view class="info-row">
                <view class="info-label">服务项目:</view>
                <view class="info-value">{{item.serviceName}}</view>
              </view>
              
              <view class="info-row">
                <view class="info-label">服务价格:</view>
                <view class="info-value price">¥{{item.servicePrice}}</view>
              </view>
              
              <view class="info-row" wx:if="{{item.phoneNumber}}">
                <view class="info-label">联系电话:</view>
                <view class="info-value phone-container">
                  <text>{{item.maskedPhone}}</text>
                  <view class="call-btn" wx:if="{{item.status === 'pending'}}" catch:tap="callPhone" data-phone="{{item.phoneNumber}}">拨打</view>
                </view>
              </view>
              
              <view class="info-row" wx:if="{{item.createTimeFormatted}}">
                <view class="info-label">预约时间:</view>
                <view class="info-value">{{item.createTimeFormatted}}</view>
              </view>
              
              <!-- 核销信息 -->
              <view wx:if="{{item.verified}}">
                <view class="info-row">
                  <view class="info-label">核销状态:</view>
                  <view class="info-value verified">已核销</view>
                </view>
                <view class="info-row" wx:if="{{item.verifyTime}}">
                  <view class="info-label">核销时间:</view>
                  <view class="info-value">{{item.verifyTime}}</view>
                </view>
                <!-- 只在已完成状态下显示核销码 -->
                <view class="info-row" wx:if="{{item.verifyCode && item.status === 'completed'}}">
                  <view class="info-label">核销码:</view>
                  <view class="info-value">{{item.verifyCode}}</view>
                </view>
              </view>
              <view wx:else>
                <view class="info-row">
                  <view class="info-label">核销状态:</view>
                  <view class="info-value unverified">未核销</view>
                </view>
                <!-- 移除未核销状态下的核销码显示 -->
              </view>

              <!-- 参考图片预览 -->
              <view class="reference-images" wx:if="{{item.imageUrls && item.imageUrls.length > 0}}">
                <view class="info-label">参考图片:</view>
                <view class="image-preview">
                  <image 
                    wx:for="{{item.imageUrls}}" 
                    wx:for-item="imgUrl" 
                    wx:key="index" 
                    src="{{imgUrl}}" 
                    mode="aspectFill" 
                    class="preview-image"
                    bindtap="previewImage"
                    data-urls="{{item.imageUrls}}"
                    data-current="{{imgUrl}}"
                  ></image>
                </view>
              </view>
            </view>
          </view>
        </block>
        
        <!-- 加载更多 -->
        <view class="load-more" wx:if="{{hasMoreData && !isLoadingMore}}">
          <text>上拉加载更多</text>
        </view>
        <view class="loading-more" wx:if="{{isLoadingMore}}">
          <view class="loading-icon small"></view>
          <text>加载更多...</text>
        </view>
        <view class="load-more" wx:if="{{!hasMoreData && appointmentList.length > 0}}">
          <text>已显示全部数据</text>
        </view>
      </view>
    </view>
  </scroll-view>
</view> 