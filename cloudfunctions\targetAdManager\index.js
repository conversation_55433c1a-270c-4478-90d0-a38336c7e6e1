const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const _ = db.command

// 定义常量
const COLLECTION_NAME = 'targetAds' // 指向广告集合名称

// 初始化集合函数
async function initCollections() {
  try {
    // 检查并创建targetAds集合
    try {
      await db.createCollection(COLLECTION_NAME)
      console.log('创建targetAds集合成功')
    } catch (err) {
      // 如果集合已存在，会抛出错误，这是正常的
      console.log('targetAds集合已存在或创建失败:', err.message)
    }

    return true
  } catch (err) {
    console.error('初始化集合失败:', err)
    return false
  }
}

/**
 * 指向广告管理云函数
 * 支持的操作：
 * - getConfig: 获取广告配置
 * - updateConfig: 更新广告配置
 */
// 云函数入口函数
exports.main = async (event, context) => {
  // 初始化集合
  await initCollections()

  const { action } = event

  try {
    switch (action) {
      case 'getConfig':
        return await getConfig(event)
      case 'updateConfig':
        return await updateConfig(event)
      default:
        return {
          success: false,
          message: '不支持的操作类型'
        }
    }
  } catch (error) {
    console.error('云函数执行错误:', error)
    return {
      success: false,
      message: '服务器内部错误',
      error: error.message
    }
  }
}

/**
 * 获取广告配置
 */
async function getConfig(event) {
  try {
    // 获取当前启用的广告配置
    const result = await db.collection(COLLECTION_NAME)
      .where({
        enabled: true
      })
      .orderBy('updateTime', 'desc')
      .limit(1)
      .get()
    
    if (result.data.length > 0) {
      return {
        success: true,
        data: result.data[0]
      }
    } else {
      return {
        success: true,
        data: null,
        message: '暂无启用的广告配置'
      }
    }
  } catch (error) {
    console.error('获取广告配置失败:', error)
    return {
      success: false,
      message: '获取广告配置失败',
      error: error.message
    }
  }
}

/**
 * 更新广告配置
 */
async function updateConfig(event) {
  const { imageUrl, jumpUrl, triggerTime, enabled } = event
  
  try {
    // 验证必要参数（jumpUrl可以为空，表示无跳转状态）
    if (!imageUrl || triggerTime === undefined || triggerTime === null) {
      return {
        success: false,
        message: '缺少必要参数：imageUrl, triggerTime'
      }
    }
    
    const now = new Date()
    
    // 先禁用所有现有配置
    await db.collection(COLLECTION_NAME)
      .where({
        enabled: true
      })
      .update({
        data: {
          enabled: false,
          updateTime: now
        }
      })

    // 创建新的广告配置
    const newConfig = {
      imageUrl: imageUrl,
      jumpUrl: jumpUrl || '', // 确保jumpUrl至少是空字符串，支持无跳转状态
      triggerTime: parseInt(triggerTime),
      enabled: enabled !== false, // 默认启用
      createTime: now,
      updateTime: now
    }

    const result = await db.collection(COLLECTION_NAME).add({
      data: newConfig
    })
    
    return {
      success: true,
      data: {
        _id: result._id,
        ...newConfig
      },
      message: '广告配置更新成功'
    }
  } catch (error) {
    console.error('更新广告配置失败:', error)
    return {
      success: false,
      message: '更新广告配置失败',
      error: error.message
    }
  }
}
