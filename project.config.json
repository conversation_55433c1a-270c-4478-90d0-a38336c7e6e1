{"appid": "wx03b63fba056c0b08", "compileType": "miniprogram", "setting": {"coverView": true, "es6": true, "postcss": true, "minified": true, "enhance": true, "showShadowRootInWxmlPanel": true, "packNpmRelationList": [], "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "useMultiFrameRuntime": true, "useIsolateContext": true, "ignoreUploadUnusedFiles": true, "hotReload": {"disableHotReloadNotification": true}, "condition": true, "compileWorklet": false, "uglifyFileName": false, "uploadWithSourceMap": true, "packNpmManually": false, "minifyWXSS": true, "minifyWXML": true, "localPlugins": false, "disableUseStrict": false, "useCompilerPlugins": false, "swc": false, "disableSWC": true}, "condition": {}, "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}, "cloudfunctionRoot": "cloudfunctions/", "packOptions": {"ignore": [], "include": []}, "projectArchitecture": "multiPlatform", "simulatorPluginLibVersion": {"wxext14566970e7e9f62": "3.6.5-34"}, "libVersion": "3.8.6", "cloudfunctionTemplateRoot": "cloudfunctionTemplate/"}