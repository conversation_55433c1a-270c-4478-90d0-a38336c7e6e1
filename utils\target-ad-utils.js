/**
 * 指向广告工具函数
 */

/**
 * 处理跳转逻辑
 * @param {string} jumpUrl 跳转地址
 */
function handleJump(jumpUrl) {
  if (!jumpUrl) {
    console.warn('跳转地址为空');
    return;
  }

  try {
    // TabBar页面使用switchTab
    const tabBarPages = [
      '/pages/index/index',
      '/pages/gallery/gallery', 
      '/pages/appointment/appointment',
      '/pages/my/my'
    ];
    
    if (tabBarPages.includes(jumpUrl)) {
      wx.switchTab({
        url: jumpUrl,
        success: () => {
          console.log('TabBar页面跳转成功:', jumpUrl);
        },
        fail: (err) => {
          console.error('TabBar页面跳转失败:', err);
          wx.showToast({
            title: '跳转失败',
            icon: 'none'
          });
        }
      });
    } else if (jumpUrl.startsWith('/pages/')) {
      // 普通页面使用navigateTo
      wx.navigateTo({
        url: jumpUrl,
        success: () => {
          console.log('页面跳转成功:', jumpUrl);
        },
        fail: (err) => {
          console.error('页面跳转失败:', err);
          // 如果navigateTo失败，尝试使用redirectTo
          wx.redirectTo({
            url: jumpUrl,
            success: () => {
              console.log('页面重定向成功:', jumpUrl);
            },
            fail: (redirectErr) => {
              console.error('页面重定向也失败:', redirectErr);
              wx.showToast({
                title: '跳转失败',
                icon: 'none'
              });
            }
          });
        }
      });
    } else if (jumpUrl.startsWith('http://') || jumpUrl.startsWith('https://')) {
      // 外部链接处理
      wx.showModal({
        title: '提示',
        content: '即将跳转到外部链接',
        success: (res) => {
          if (res.confirm) {
            // 复制链接到剪贴板
            wx.setClipboardData({
              data: jumpUrl,
              success: () => {
                wx.showToast({
                  title: '链接已复制',
                  icon: 'success'
                });
              }
            });
          }
        }
      });
    } else {
      console.warn('不支持的跳转地址格式:', jumpUrl);
      wx.showToast({
        title: '不支持的链接格式',
        icon: 'none'
      });
    }
  } catch (error) {
    console.error('跳转处理失败:', error);
    wx.showToast({
      title: '跳转失败',
      icon: 'none'
    });
  }
}

/**
 * 验证跳转地址格式
 * @param {string} jumpUrl 跳转地址
 * @returns {boolean} 是否有效
 */
function validateJumpUrl(jumpUrl) {
  if (!jumpUrl || typeof jumpUrl !== 'string') {
    return false;
  }

  // 支持的格式
  const patterns = [
    /^\/pages\/\w+\/\w+$/,  // 小程序页面路径
    /^https?:\/\/.+/        // HTTP/HTTPS链接
  ];

  return patterns.some(pattern => pattern.test(jumpUrl));
}

/**
 * 格式化触发时间显示
 * @param {number} seconds 秒数
 * @returns {string} 格式化后的时间字符串
 */
function formatTriggerTime(seconds) {
  if (seconds < 60) {
    return `${seconds}秒`;
  } else if (seconds < 3600) {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return remainingSeconds > 0 ? `${minutes}分${remainingSeconds}秒` : `${minutes}分钟`;
  } else {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return minutes > 0 ? `${hours}小时${minutes}分钟` : `${hours}小时`;
  }
}

/**
 * 获取常用触发时间选项
 * @returns {Array} 时间选项数组
 */
function getTriggerTimeOptions() {
  return [
    { label: '30秒', value: 30 },
    { label: '60秒', value: 60 },
    { label: '90秒', value: 90 },
    { label: '2分钟', value: 120 },
    { label: '3分钟', value: 180 },
    { label: '5分钟', value: 300 }
  ];
}

module.exports = {
  handleJump,
  validateJumpUrl,
  formatTriggerTime,
  getTriggerTimeOptions
};
