/* pages/admin/business/business.wxss */

/* 修改配色方案为更高级、简洁的风格 - 小程序兼容版 */
/* 主色调：深灰色 #4a4a4a */
/* 浅色调：浅灰色背景 #f5f5f7 */
/* 强调色：类苹果蓝 #0070c9 */
/* 浅蓝色背景 rgba(0, 112, 201, 0.1) */
/* 成功色：绿色 #34c759 */
/* 危险色：红色 #ff3b30 */
/* 文字主色 #333333 */
/* 次要文字色 #666666 */
/* 浅色文字 #999999 */
/* 边框颜色 #e0e0e0 */

/* 容器样式 */
.business-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100vh;
  background-color: #f5f5f7;
  padding-top: 290rpx; /* 适当减小顶部内边距 */
  box-sizing: border-box;
}

/* 顶部标题栏 */
.header {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  padding-top: 120rpx;  /* 增加顶部内边距，从90rpx增加到120rpx，确保在状态栏下方 */
  background-color: #ffffff;
  color: #333333;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.back-icon {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  font-weight: bold;
  color: #333333;
}

.title {
  flex: 1;
  text-align: center;
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
  margin-right: 60rpx;
}

/* 视图切换标签优化 */
.view-tabs {
  display: flex;
  background-color: #fff;
  padding: 20rpx 30rpx;
  border-radius: 6rpx;
  margin: 20rpx 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  position: fixed;
  top: 170rpx; /* 从90rpx增加到170rpx，确保在标题栏下方 */
  left: 0;
  right: 0;
  z-index: 99;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 15rpx 0;
  font-size: 28rpx;
  color: #666666;
  position: relative;
  transition: all 0.2s ease;
}

.tab-item.active {
  color: #0070c9;
  font-weight: 500;
}

/* 支出管理标签页特殊样式 */
.tab-item.active[data-view="expense"] {
  color: #0070c9;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 3rpx;
  background-color: #0070c9;
  border-radius: 1.5rpx;
}

/* 支出管理标签页下划线特殊样式 */
.tab-item.active[data-view="expense"]::after {
  background-color: #0070c9;
}

/* 日期筛选区域 */
.date-filter {
  background-color: #fff;
  padding: 20rpx;
  margin: 40rpx 30rpx 20rpx 30rpx; /* 减小顶部外边距，从80rpx减少到40rpx */
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  border-radius: 6rpx; /* 恢复四周圆角 */
}

.date-shortcuts {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

/* 日期选择快捷按钮 */
.shortcut-btn {
  flex: 1;
  text-align: center;
  padding: 15rpx 0;
  margin: 0 10rpx;
  background-color: #f5f5f7;
  color: #666666;
  border-radius: 4rpx;
  font-size: 28rpx;
  transition: all 0.2s ease;
}

.shortcut-btn.active {
  background-color: rgba(0, 112, 201, 0.1);
  color: #0070c9;
  font-weight: 500;
}

.date-range-picker {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  padding: 10rpx 0;
  border-top: 1rpx solid #f0f0f0;
}

.date-picker-item {
  display: flex;
  align-items: center;
  margin-right: 20rpx;
  margin-bottom: 10rpx;
}

.date-label {
  font-size: 28rpx;
  color: #666;
  margin-right: 10rpx;
}

.date-value {
  flex: 0.6; /* 减小日期列的宽度，与表头一致 */
  text-align: center;
  color: #333333;
  font-size: 24rpx;
  padding: 0 8rpx; /* 添加一些内边距 */
  font-weight: bold; /* 加粗显示 */
  background-color: #f9f9f9; /* 轻微的背景色 */
  border-radius: 4rpx; /* 统一的小圆角 */
  margin: 0 4rpx; /* 添加一些外边距 */
}

.date-header {
  flex: 0.6; /* 减小日期列的宽度 */
  text-align: center;
  background-color: #f5f5f5; /* 轻微的背景色，与日期值匹配 */
  border-radius: 4rpx; /* 统一的小圆角 */
  padding: 0 8rpx; /* 添加一些内边距 */
  margin: 0 4rpx; /* 添加一些外边距 */
}

/* 查询按钮 */
.query-btn {
  padding: 10rpx 30rpx;
  background-color: #0070c9;
  color: #fff;
  border-radius: 4rpx;
  font-size: 28rpx;
  margin-left: auto;
}

/* 员工筛选 */
.staff-filter {
  display: flex;
  align-items: center;
  margin-top: 20rpx;
  padding-top: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}

.filter-label {
  font-size: 28rpx;
  color: #666;
  margin-right: 10rpx;
}

.filter-picker {
  background-color: #f5f5f5;
  padding: 10rpx 20rpx;
  border-radius: 4rpx; /* 统一使用小圆角 */
  font-size: 28rpx;
  color: #333;
  display: flex;
  align-items: center;
}

.picker-arrow {
  font-size: 20rpx;
  margin-left: 10rpx;
  color: #999;
}

.clear-filter {
  width: 50rpx;
  height: 50rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 32rpx;
}

/* 加载中 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

/* 业绩内容区域 */
.business-content {
  flex: 1;
  overflow-y: auto;
  padding: 0 20rpx 30rpx;
}

/* 统计卡片 */
.stats-card {
  background-color: #fff;
  padding: 25rpx;
  margin-bottom: 25rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  border-radius: 6rpx;
}

/* 所有卡片标题的通用样式 */
.stats-card-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  position: relative;
  padding-left: 20rpx;
  border-left: 6rpx solid #0070c9;
}

/* 业绩概览标题特殊样式 */
.stats-card-title.overview-title {
  padding-left: 20rpx;
  border-left: 6rpx solid #0070c9;
  font-size: 36rpx;
}

/* 业绩概览和支出概览特殊样式 */
.stats-basic {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  padding: 5rpx 0;
}

/* 净利润特殊样式 - 放大并置顶 */
.stats-basic-item.profit-item {
  width: 100%;
  margin-bottom: 30rpx;
  padding: 20rpx;
  border-radius: 6rpx;
  background-color: rgba(39, 174, 96, 0.08);
  border-left: 8rpx solid #27ae60;
  box-shadow: 0 4rpx 12rpx rgba(39, 174, 96, 0.15);
  transition: all 0.3s ease;
}

.stats-basic-item.profit-item.negative {
  background-color: rgba(192, 57, 43, 0.08);
  border-left: 8rpx solid #c0392b;
  box-shadow: 0 4rpx 12rpx rgba(192, 57, 43, 0.15);
}

.stats-basic-item.profit-item .stats-basic-value {
  font-size: 56rpx;
  color: #27ae60;
  font-weight: bold;
}

.stats-basic-item.profit-item.negative .stats-basic-value {
  color: #c0392b;
}

.stats-basic-item.profit-item .stats-basic-label {
  font-size: 32rpx;
  color: #27ae60;
  font-weight: bold;
}

.stats-basic-item.profit-item.negative .stats-basic-label {
  color: #c0392b;
}

/* 其他数据项样式 */
.stats-basic-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 15rpx;
  width: 33.33%;
  box-sizing: border-box;
  margin-bottom: 20rpx;
  border-radius: 6rpx;
  background-color: #f9f9f9;
  transition: all 0.2s ease;
}

.stats-basic-item:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.08);
}

.stats-basic-value {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.stats-basic-label {
  font-size: 24rpx;
  color: #555;
  font-weight: 500;
}

/* 不同类型的数据项样式 */
/* 现金收入 */
.stats-basic-item.income-item {
  background-color: rgba(46, 204, 113, 0.08);
  border-left: 4rpx solid #2ecc71;
}

.stats-basic-item.income-item .stats-basic-value {
  color: #2ecc71;
}

.stats-basic-item.income-item .stats-basic-label {
  color: #2ecc71;
}

/* 充值收入 */
.stats-basic-item.recharge-income-item {
  background-color: rgba(52, 152, 219, 0.08);
  border-left: 4rpx solid #3498db;
}

.stats-basic-item.recharge-income-item .stats-basic-value {
  color: #3498db;
}

.stats-basic-item.recharge-income-item .stats-basic-label {
  color: #3498db;
}

/* 余额消费 */
.stats-basic-item.balance-income-item {
  background-color: rgba(127, 140, 141, 0.08);
  border-left: 4rpx solid #7f8c8d;
}

.stats-basic-item.balance-income-item .stats-basic-value {
  color: #7f8c8d;
}

.stats-basic-item.balance-income-item .stats-basic-label {
  color: #7f8c8d;
}

/* 总提成 */
.stats-basic-item.commission-item {
  background-color: rgba(230, 126, 34, 0.08);
  border-left: 4rpx solid #e67e22;
}

.stats-basic-item.commission-item .stats-basic-value {
  color: #e67e22;
}

.stats-basic-item.commission-item .stats-basic-label {
  color: #e67e22;
}

/* 总支出 */
.stats-basic-item.expense-item {
  background-color: rgba(231, 76, 60, 0.08);
  border-left: 4rpx solid #e74c3c;
}

.stats-basic-item.expense-item .stats-basic-value {
  color: #e74c3c;
}

.stats-basic-item.expense-item .stats-basic-label {
  color: #e74c3c;
}

/* 积分支出 */
.stats-basic-item.points-expense-item {
  background-color: rgba(155, 89, 182, 0.08);
  border-left: 4rpx solid #9b59b6;
}

.stats-basic-item.points-expense-item .stats-basic-value {
  color: #9b59b6;
}

.stats-basic-item.points-expense-item .stats-basic-label {
  color: #9b59b6;
}

/* 退款支出 */
.stats-basic-item.refund-expense-item {
  background-color: rgba(231, 76, 60, 0.08);
  border-left: 4rpx solid #e74c3c;
}

.stats-basic-item.refund-expense-item .stats-basic-value {
  color: #e74c3c;
}

.stats-basic-item.refund-expense-item .stats-basic-label {
  color: #e74c3c;
}

/* 推广佣金支出 */
.stats-basic-item.promotion-commission-item {
  background-color: rgba(255, 152, 0, 0.08);
  border-left: 4rpx solid #ff9800;
}

.stats-basic-item.promotion-commission-item .stats-basic-value {
  color: #ff9800;
}

.stats-basic-item.promotion-commission-item .stats-basic-label {
  color: #ff9800;
}

/* 订单数量 */
.stats-basic-item.orders-item {
  background-color: rgba(52, 73, 94, 0.08);
  border-left: 4rpx solid #34495e;
}

.stats-basic-item.orders-item .stats-basic-value {
  color: #34495e;
}

.stats-basic-item.orders-item .stats-basic-label {
  color: #34495e;
}

/* 员工统计 */
.staff-stats {
  width: 100%;
  overflow-x: auto;
}

.staff-stats-header {
  display: flex;
  background-color: #f5f5f5;
  padding: 20rpx 0;
  font-weight: bold;
  font-size: 28rpx;
  border-bottom: 2rpx solid #eee;
}

.staff-name-header {
  flex: 2;
  text-align: center;
}

.staff-orders-header, 
.staff-income-header, 
.staff-balance-header,
.staff-commission-header, 
.staff-action-header {
  flex: 1;
  text-align: center;
}

.staff-stats-item {
  display: flex;
  padding: 20rpx 0;
  font-size: 28rpx;
  border-bottom: 2rpx solid #eee;
}

.staff-name {
  flex: 2;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.staff-orders, 
.staff-income, 
.staff-balance,
.staff-commission, 
.staff-action {
  flex: 1;
  text-align: center;
}

.staff-income {
  color: #2ecc71;
  font-weight: bold;
}

.staff-balance {
  color: #7f8c8d;
  font-weight: bold;
}

.staff-commission {
  color: #e67e22;
  font-weight: bold;
}

/* 空状态 */
.empty-staff, .empty-orders {
  padding: 50rpx 0;
  text-align: center;
  color: #999;
  font-size: 24rpx;
}

/* 详情按钮 */
.view-detail-btn {
  display: inline-block;
  padding: 6rpx 20rpx;
  background-color: #0070c9;
  color: #fff;
  border-radius: 4rpx;
  font-size: 24rpx;
}

/* 日期统计 */
.date-stats {
  width: 100%;
  overflow-x: auto;
}

.date-stats-header {
  display: flex;
  background-color: #f5f5f5;
  padding: 20rpx 0;
  font-weight: bold;
  font-size: 24rpx;
  border-bottom: 2rpx solid #eee;
}

.date-header, 
.date-orders-header, 
.date-income-header, 
.date-balance-header,
.date-recharge-header, 
.date-commission-header, 
.date-expense-header,
.date-refund-header,
.date-profit-header {
  flex: 1;
  text-align: center;
  padding: 0 5rpx;
}

.date-profit-header {
  color: #27ae60;
  font-weight: bold;
  background-color: rgba(39, 174, 96, 0.08);
}

.date-expense-header, .date-refund-header {
  color: #e74c3c;
  background-color: rgba(231, 76, 60, 0.05);
}

.date-stats-item {
  display: flex;
  padding: 20rpx 0;
  font-size: 24rpx;
  border-bottom: 2rpx solid #eee;
}

.date-value, 
.date-orders, 
.date-income, 
.date-balance,
.date-recharge, 
.date-commission, 
.date-expense,
.date-refund,
.date-profit {
  flex: 1;
  text-align: center;
  padding: 0 5rpx;
}

.date-income {
  color: #2ecc71;
  font-weight: bold;
}

.date-balance {
  color: #7f8c8d;
  font-weight: bold;
}

.date-recharge {
  color: #3498db;
  font-weight: bold;
}

.date-commission {
  color: #e67e22;
  font-weight: bold;
}

.date-expense, .date-refund, .expense-color {
  color: #e74c3c;
  font-weight: bold;
}

.date-profit {
  font-weight: bold;
  padding: 5rpx;
  border-radius: 4rpx;
}

.date-profit.positive {
  color: #27ae60;
  background-color: rgba(39, 174, 96, 0.08);
}

.date-profit.negative {
  color: #c0392b;
  background-color: rgba(192, 57, 43, 0.08);
}

/* 服务项目统计 */
.service-stats {
  width: 100%;
  overflow-x: auto;
}

.service-stats-header {
  display: flex;
  background-color: #f5f5f5;
  padding: 20rpx 0;
  font-weight: bold;
  font-size: 28rpx;
  border-bottom: 2rpx solid #eee;
}

.service-name-header {
  flex: 3;
  text-align: center;
}

.service-count-header, 
.service-cash-header,
.service-balance-header,
.service-total-header {
  flex: 1;
  text-align: center;
}

.service-stats-item {
  display: flex;
  padding: 20rpx 0;
  font-size: 28rpx;
  border-bottom: 2rpx solid #eee;
}

.service-name {
  flex: 3;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #333333;
  font-weight: 500;
  background-color: rgba(0, 0, 0, 0.03);
  padding: 4rpx 8rpx;
  border-radius: 4rpx;
}

.service-count, 
.service-cash,
.service-balance,
.service-total {
  flex: 1;
  text-align: center;
}

.service-cash {
  color: #2ecc71;
  font-weight: bold;
}

.service-balance {
  color: #7f8c8d;
  font-weight: bold;
}

.service-total {
  color: #9b59b6;
  font-weight: bold;
}

/* 员工列表 */
.staff-list {
  background-color: #fff;
  border-radius: 6rpx; /* 统一圆角 */
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  margin-bottom: 20rpx;
}

.staff-list-header, .staff-list-item {
  display: flex;
  padding: 15rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  font-size: 24rpx; /* 减小字体大小，与日期统计保持一致 */
  align-items: center; /* 垂直居中 */
}

.staff-list-header {
  font-weight: bold;
  color: #666;
  background-color: #f9f9f9; /* 添加轻微背景色 */
  padding: 12rpx 8rpx; /* 与日期统计保持一致 */
  border-radius: 4rpx; /* 统一的小圆角 */
  margin-bottom: 8rpx;
}

.staff-name-header, .staff-name {
  flex: 2;
  padding: 0 8rpx;
}

.staff-name {
  color: #333333; /* 使用主文字颜色 */
}

.staff-phone-header, .staff-phone {
  flex: 2;
  text-align: center;
  padding: 0 8rpx;
}

.staff-phone {
  color: #666666; /* 使用次要文字颜色 */
}

.staff-commission-header, .staff-commission-rate {
  flex: 1;
  text-align: center;
  padding: 0 8rpx;
}

.staff-commission-rate {
  color: #0070c9; /* 使用主按钮颜色，使其突出显示 */
  font-weight: 500;
}

.staff-action-header, .staff-action {
  flex: 1;
  text-align: center;
  padding: 0 8rpx;
}

/* 员工业绩详情弹窗 */
.staff-performance-panel {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s ease;
}

.staff-performance-panel.show {
  opacity: 1;
  pointer-events: auto;
}

.panel-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
}

.panel-content {
  position: relative;
  width: 90%;
  max-width: 650rpx;
  max-height: 90%;
  background-color: #fff;
  border-radius: 10rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  background-color: #f5f5f5;
  border-bottom: 1rpx solid #eee;
}

.panel-title {
  font-size: 32rpx;
  font-weight: bold;
}

.panel-close {
  font-size: 40rpx;
  color: #999;
  padding: 0 10rpx;
}

.panel-body {
  flex: 1;
  padding: 20rpx;
  overflow-y: auto;
}

/* 业绩概览样式 */
.performance-overview {
  display: flex;
  justify-content: space-around;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #eee;
  margin-bottom: 20rpx;
}

.overview-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.overview-value {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.overview-label {
  font-size: 24rpx;
  color: #666;
}

/* 支付方式统计样式 */
.payment-stats {
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #eee;
}

.payment-stats-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #34495e;
  margin-bottom: 20rpx;
  padding-left: 10rpx;
  border-left: 6rpx solid #007AFF;
}

.payment-stats-content {
  display: flex;
  justify-content: space-between;
}

.payment-stats-item {
  width: 48%;
  border-radius: 8rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.cash-stats {
  background-color: #f8f9fa;
  border: 1rpx solid #e9ecef;
}

.balance-stats {
  background-color: #f0f7ff;
  border: 1rpx solid #d0e6ff;
}

.payment-stats-header {
  padding: 15rpx 0;
  text-align: center;
  font-size: 26rpx;
  font-weight: bold;
}

.cash-stats .payment-stats-header {
  background-color: #4CAF50;
  color: white;
}

.balance-stats .payment-stats-header {
  background-color: #9E9E9E;
  color: white;
}

.payment-stats-data {
  display: flex;
  justify-content: space-around;
  padding: 15rpx 0;
}

.payment-data-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.payment-data-value {
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 5rpx;
}

.cash-stats .payment-data-value {
  color: #2ecc71;
  font-weight: bold;
}

.balance-stats .payment-data-value {
  color: #7f8c8d;
  font-weight: bold;
}

.payment-data-label {
  font-size: 22rpx;
  color: #666;
}

/* 订单支付方式样式 */
.order-payment {
  color: #666;
}

.cash-payment {
  color: #2ecc71;
  font-weight: bold;
}

.balance-payment {
  color: #7f8c8d;
  font-weight: bold;
}

/* 订单列表样式 */
.performance-orders {
  margin-top: 20rpx;
}

.orders-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  padding-left: 10rpx;
  border-left: 6rpx solid #007AFF;
}

.orders-header {
  display: flex;
  background-color: #f5f5f5;
  padding: 15rpx 0;
  font-size: 24rpx;
  font-weight: bold;
  color: #666;
}

.order-date-header,
.order-payment-header,
.order-price-header,
.order-commission-header {
  flex: 1;
  text-align: center;
  padding: 0 5rpx;
}

.order-service-header {
  flex: 2;
  text-align: center;
  padding: 0 5rpx;
}

.order-item {
  display: flex;
  padding: 15rpx 0;
  font-size: 24rpx;
  border-bottom: 1rpx solid #eee;
}

.order-date,
.order-payment,
.order-price,
.order-commission {
  flex: 1;
  text-align: center;
  padding: 0 5rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.order-service {
  flex: 2;
  text-align: center;
  padding: 0 5rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 订单明细表格表头的文字颜色样式 */
.orders-header .order-date-header,
.orders-header .order-service-header,
.orders-header .order-payment-header,
.orders-header .order-price-header,
.orders-header .order-commission-header {
  color: #34495e;
  font-weight: bold;
}

/* 订单明细表格文字颜色调整 */
.order-date {
  color: #333333;
  font-weight: 500;
}

.order-service {
  color: #333333;
  font-weight: 500;
}

.order-price {
  color: #2ecc71;
  font-weight: bold;
}

.order-commission {
  color: #e67e22;
  font-weight: bold;
}

/* 支出列表样式 */
.expense-list {
  width: 100%;
}

.expense-list-header {
  display: flex;
  background-color: #f9f9f9;
  padding: 15rpx 0;
  font-size: 24rpx;
  color: #666;
  font-weight: bold;
  border-bottom: 1rpx solid #eee;
}

.expense-date-header {
  flex: 2;
  text-align: center;
}

.expense-staff-header {
  flex: 1;
  text-align: center;
}

.expense-amount-header {
  flex: 1;
  text-align: center;
}

.expense-remark-header {
  flex: 2;
  text-align: center;
}

.expense-action-header {
  flex: 1;
  text-align: center;
}

.expense-list-item {
  display: flex;
  padding: 20rpx 0;
  font-size: 26rpx;
  color: #333;
  border-bottom: 1rpx solid #eee;
  align-items: center;
}

.expense-date {
  flex: 2;
  text-align: center;
  font-size: 24rpx;
}

.expense-staff {
  flex: 1;
  text-align: center;
}

.expense-amount {
  flex: 1;
  text-align: center;
  color: #e05c65; /* 使用更柔和的红色 */
  font-weight: bold;
}

.expense-remark {
  flex: 2;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding: 0 10rpx;
}

.expense-action {
  flex: 1;
  text-align: center;
}

.empty-expense {
  padding: 50rpx 0;
  text-align: center;
  color: #999;
  font-size: 28rpx;
}

/* 支出详情弹窗样式 */
.expense-detail-panel {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  visibility: hidden;
  opacity: 0;
  transition: all 0.3s;
}

.expense-detail-panel.show {
  visibility: visible;
  opacity: 1;
}

.expense-detail-panel .panel-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0; /* 添加right: 0确保满屏 */
  width: 100% !important;
  max-width: none !important; /* 移除最大宽度限制 */
  height: 80%;
  background-color: #fff;
  border-radius: 30rpx 30rpx 0 0;
  overflow: hidden;
  transform: translateY(100%);
  transition: transform 0.3s ease;
  margin: 0 !important; /* 移除可能的外边距 */
  padding: 0; /* 移除可能的内边距 */
  box-sizing: border-box; /* 确保盒模型正确 */
}

.expense-detail-panel.show .panel-content {
  transform: translateY(0);
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.panel-title {
  font-size: 34rpx;
  font-weight: bold;
  color: #333;
}

.panel-close {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  color: #999;
}

.panel-body {
  height: calc(100% - 120rpx);
  overflow-y: auto;
  padding: 30rpx;
}

.expense-detail-info {
  margin-bottom: 30rpx;
}

.detail-item {
  display: flex;
  margin-bottom: 20rpx;
}

.detail-label {
  width: 120rpx;
  color: #666;
  font-size: 28rpx;
}

.detail-value {
  flex: 1;
  color: #333;
  font-size: 28rpx;
}

.expense-images {
  margin-top: 30rpx;
}

.images-title {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
}

.images-container {
  display: flex;
  flex-wrap: wrap;
}

.expense-image {
  width: 180rpx;
  height: 180rpx;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
  border-radius: 8rpx;
}

.no-images {
  padding: 30rpx 0;
  text-align: center;
  color: #999;
  font-size: 28rpx;
}

.expense-actions {
  margin-top: 40rpx;
  display: flex;
  justify-content: center;
}

/* 删除按钮 */
.delete-btn {
  padding: 15rpx 40rpx;
  background-color: #ff3b30;
  color: #fff;
  font-size: 28rpx;
  border-radius: 4rpx;
}

/* 支出管理视图中总记录数项的样式 */
.stats-basic-item.records-item {
  background-color: rgba(108, 117, 125, 0.05); /* 轻微的灰色背景 */
  border-left: 3rpx solid #6c757d; /* 左边灰色边框 */
}

.stats-basic-item.records-item .stats-basic-value {
  color: #6c757d; /* 记录数显示灰色 */
}

.stats-basic-item.records-item .stats-basic-label {
  color: #6c757d; /* 记录数标签也显示灰色 */
}

/* 支出详情金额颜色 */
.expense-detail-info .detail-item:nth-child(2) .detail-value {
  color: #dc3545; /* 支出金额显示红色 */
  font-weight: bold;
}

/* 调整红色为柔和的红色 */
.stats-basic-item.expense-item .stats-basic-value,
.stats-basic-item.expense-item .stats-basic-label,
.expense-amount,
.staff-income,
.expense-detail-info .detail-item:nth-child(2) .detail-value {
  color: #ff3b30; /* 使用标准红色 */
}

/* 日期业绩中的支出列 */
.date-expense {
  color: #ff3b30; /* 使用标准红色 */
  font-weight: 500;
}

/* 支出管理视图中的卡片标题 */
.currentView-expense .stats-card-title:not(.overview-title) {
  border-left: 6rpx solid #ff3b30; /* 支出管理视图中的标题使用红色装饰线 */
}

/* 支出概览标题特殊样式 */
.currentView-expense .stats-card-title.overview-title {
  color: #ff3b30; /* 支出概览标题使用红色 */
  font-weight: bold;
}

/* 充值收入样式 */
.stats-basic-item.recharge-income-item {
  background-color: rgba(52, 152, 219, 0.08);
  border-left: 4rpx solid #3498db;
  box-shadow: 0 2rpx 8rpx rgba(52, 152, 219, 0.1);
}

/* 日期业绩表格中的充值列 */
.date-recharge-header {
  flex: 1;
  text-align: center;
  font-weight: 500;
  color: #007AFF;
}

.date-recharge {
  flex: 1;
  text-align: center;
  color: #007AFF;
}

/* 表头样式增强 */
.staff-stats-header, 
.date-stats-header,
.service-stats-header {
  font-weight: bold;
  color: #34495e;
}

/* 支付方式统计标题 */
.payment-stats-title {
  color: #34495e;
}

/* 不同类型的数据项标签样式 */
.income-item .stats-basic-label {
  color: #27ae60;
}

.recharge-income-item .stats-basic-label {
  color: #2980b9;
}

.balance-income-item .stats-basic-label {
  color: #5f6c6d;
}

.commission-item .stats-basic-label {
  color: #d35400;
}

.expense-item .stats-basic-label {
  color: #c0392b;
}

.profit-item .stats-basic-label {
  color: #27ae60;
}

.profit-item.negative .stats-basic-label {
  color: #c0392b;
}

/* 确保表格中的标题也有足够的对比度 */
.staff-name-header, 
.staff-orders-header, 
.staff-income-header, 
.staff-balance-header,
.staff-commission-header, 
.staff-action-header,
.date-header, 
.date-orders-header, 
.date-income-header, 
.date-balance-header,
.date-recharge-header, 
.date-commission-header, 
.date-expense-header, 
.date-profit-header,
.service-name-header,
.service-count-header, 
.service-cash-header,
.service-balance-header,
.service-total-header {
  color: #34495e;
  font-weight: bold;
}

/* 订单列表标题样式 */
.orders-title {
  color: #34495e;
}

/* 支付方式统计数据标签 */
.payment-data-label {
  font-size: 22rpx;
  color: #555;
  font-weight: 500;
}

.cash-stats .payment-data-label {
  color: #27ae60;
}

.balance-stats .payment-data-label {
  color: #5f6c6d;
}

/* 统一所有按钮的圆角 */
.shortcut-btn, 
.query-btn, 
.view-detail-btn, 
.delete-btn {
  border-radius: 4rpx;
}

/* 统一所有卡片的圆角 */
.stats-card,
.date-filter,
.view-tabs,
.filter-picker {
  border-radius: 6rpx;
}

/* 统一所有数据项的圆角 */
.stats-basic-item,
.date-value,
.date-header {
  border-radius: 4rpx;
}

/* 统一所有按钮的样式 */
.view-detail-btn, 
.query-btn {
  border-radius: 4rpx;
  font-size: 24rpx;
  display: inline-block;
  text-align: center;
  box-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
  color: #fff;
}

.view-detail-btn, 
.query-btn {
  background-color: #3498db;
}

.delete-btn {
  background-color: #e74c3c;
  font-size: 28rpx;
  padding: 15rpx 40rpx;
}

.view-detail-btn {
  padding: 6rpx 20rpx;
}

.query-btn {
  padding: 10rpx 30rpx;
  font-size: 28rpx;
  margin-left: auto;
}

.view-detail-btn:active, 
.delete-btn:active,
.query-btn:active,
.shortcut-btn.active {
  opacity: 0.9;
  transform: translateY(1rpx);
}

/* 修复日期选择器和查询按钮的样式 */
.date-filter .shortcut-btn.active {
  background-color: rgba(52, 152, 219, 0.1);
  color: #3498db;
}

/* 为加载中动画更新颜色 */
.loading-spinner {
  border-top-color: #3498db;
}

/* 支出相关元素统一使用标准红色 */
.expense-amount,
.expense-detail-info .detail-item:nth-child(2) .detail-value,
.date-expense,
.currentView-expense .stats-card-title.overview-title {
  color: #e74c3c !important;
  font-weight: bold;
}

/* 修复删除按钮样式 */
.delete-btn {
  background-color: #e74c3c;
  font-size: 28rpx;
  padding: 15rpx 40rpx;
  color: #fff;
  border-radius: 4rpx;
  display: inline-block;
  text-align: center;
  box-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

/* 员工订单数量加强显示 */
.staff-orders {
  color: #34495e;
  font-weight: bold;
  background-color: rgba(52, 73, 94, 0.08);
  padding: 4rpx 8rpx;
  border-radius: 4rpx;
  border: 1rpx solid rgba(52, 73, 94, 0.2);
}

/* 日期订单数量加强显示 */
.date-orders {
  color: #34495e;
  font-weight: bold;
  background-color: rgba(52, 73, 94, 0.08);
  padding: 4rpx 8rpx;
  border-radius: 4rpx;
  border: 1rpx solid rgba(52, 73, 94, 0.2);
}

/* 服务项目次数加强显示 */
.service-count {
  color: #34495e;
  font-weight: bold;
  background-color: rgba(52, 73, 94, 0.08);
  padding: 4rpx 8rpx;
  border-radius: 4rpx;
  border: 1rpx solid rgba(52, 73, 94, 0.2);
}

/* 增强表头中订单数/次数字段的显示 */
.staff-orders-header,
.date-orders-header,
.service-count-header {
  color: #34495e;
  font-weight: bold;
  background-color: rgba(52, 73, 94, 0.05);
}

/* 员工业绩详情弹窗中的订单数显示优化 */
.overview-item:first-child .overview-value {
  color: #34495e;
  font-weight: bold;
  background-color: rgba(52, 73, 94, 0.08);
  padding: 4rpx 16rpx;
  border-radius: 4rpx;
  border: 1rpx solid rgba(52, 73, 94, 0.2);
  display: inline-block;
}

.payment-data-item:first-child .payment-data-value {
  background-color: rgba(52, 73, 94, 0.08);
  padding: 2rpx 8rpx;
  border-radius: 4rpx;
  border: 1rpx solid rgba(52, 73, 94, 0.2);
  display: inline-block;
}

/* 退款支出项目样式 */
.refund-expense-item {
  background-color: rgba(231, 76, 60, 0.1);
  border: 1px solid rgba(231, 76, 60, 0.2);
}

.date-refund {
  flex: 0.9;
  text-align: center;
  font-size: 24rpx;
  color: #e74c3c !important;
  font-weight: 500 !important;
}

.date-refund-header {
  flex: 0.9;
  text-align: center;
  font-size: 24rpx;
  color: #34495e;
  font-weight: bold;
} 

/* 支出管理样式 */
.expense-color {
  color: #ff3b30;
}

.withdrawal-item .stats-basic-value {
  color: #07c160;
}

.expense-list {
  margin-top: 20rpx;
} 