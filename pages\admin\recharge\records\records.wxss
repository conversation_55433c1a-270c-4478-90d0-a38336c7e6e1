/* 容器 */
.records-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 30rpx;
  box-sizing: border-box;
  position: relative;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

/* 状态栏安全区域 */
.status-bar {
  width: 100%;
  height: 88rpx;
  background-color: #ffffff;
}

/* 头部导航栏 */
.header {
  width: 100%;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #ffffff;
  position: relative;
  padding: 0 30rpx;
  box-sizing: border-box;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.back-btn {
  width: 70rpx;
  height: 70rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.arrow-left {
  width: 20rpx;
  height: 20rpx;
  border-top: 4rpx solid #4a4a4a;
  border-left: 4rpx solid #4a4a4a;
  transform: rotate(-45deg);
}

.header-title {
  font-size: 34rpx;
  font-weight: 500;
  color: #4a4a4a;
  flex: 1;
  text-align: center;
}

.placeholder-btn {
  width: 60rpx;
  height: 60rpx;
  opacity: 0;
}

.filter-btn {
  padding: 10rpx 20rpx;
  background-color: #f0f0f0;
  color: #333;
  border-radius: 8rpx;
  font-size: 28rpx;
}

/* 搜索框 */
.search-box {
  display: flex;
  padding: 20rpx;
  background-color: #ffffff;
  border-bottom: 1rpx solid #eee;
}

.search-input {
  flex: 1;
  height: 70rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
}

.search-btn {
  width: 120rpx;
  height: 70rpx;
  line-height: 70rpx;
  text-align: center;
  background-color: #07c160;
  color: #fff;
  border-radius: 8rpx;
  margin-left: 20rpx;
  font-size: 28rpx;
}

/* 标签页 */
.tab-container {
  background-color: #ffffff;
  margin-bottom: 20rpx;
}

.tabs {
  display: flex;
  white-space: nowrap;
  border-bottom: 1rpx solid #eee;
}

.tab-item {
  display: inline-block;
  padding: 20rpx 30rpx;
  font-size: 28rpx;
  position: relative;
  color: #666;
}

.tab-item.active {
  color: #07c160;
  font-weight: 500;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background-color: #07c160;
}

/* 记录列表 */
.records-list {
  background-color: #ffffff;
  margin: 20rpx;
  border-radius: 12rpx;
  overflow: hidden;
  max-height: calc(100vh - 338rpx); /* 预留状态栏、头部、搜索框和标签页的高度 */
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

.list-header {
  display: flex;
  padding: 20rpx;
  background-color: #f9f9f9;
  border-bottom: 1rpx solid #eee;
  font-size: 24rpx;
  color: #999;
}

.header-item {
  flex: 1;
  text-align: center;
}

.record-item {
  padding: 20rpx;
  border-bottom: 1rpx solid #eee;
  position: relative;
}

.item-content {
  display: flex;
  flex-direction: column;
}

.item-row {
  display: flex;
  margin-bottom: 10rpx;
}

.item-row .label {
  width: 150rpx;
  color: #666;
  font-size: 26rpx;
}

.item-row .value {
  flex: 1;
  color: #333;
  font-size: 26rpx;
}

.order-id, .user-id {
  font-size: 24rpx;
  color: #666;
  word-break: break-all;
}

.status {
  padding: 4rpx 10rpx;
  border-radius: 6rpx;
  font-size: 24rpx;
  display: inline-block;
}

.status.pending {
  background-color: #fef0e5;
  color: #ff9500;
}

.status.verified {
  background-color: #e6ffed;
  color: #52c41a;
}

.status.cancelled {
  background-color: #f5f5f5;
  color: #999;
}

/* 加载更多 */
.load-more, .no-more {
  text-align: center;
  padding: 30rpx 0;
  color: #999;
  font-size: 24rpx;
}

/* 无数据提示 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
  background-color: #ffffff;
  margin: 20rpx;
  border-radius: 12rpx;
}

.empty-text {
  color: #999;
  font-size: 28rpx;
  margin-top: 20rpx;
}

/* 加载中 */
.loading-container {
  padding: 40rpx;
  text-align: center;
}

.loading {
  display: inline-block;
  color: #999;
  font-size: 28rpx;
}

/* 筛选弹窗 */
.filter-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  visibility: hidden;
  opacity: 0;
  transition: all 0.3s;
}

.filter-modal.show {
  visibility: visible;
  opacity: 1;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  border-radius: 24rpx 24rpx 0 0;
  transform: translateY(100%);
  transition: transform 0.3s;
}

.filter-modal.show .modal-content {
  transform: translateY(0);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #eee;
}

.modal-header text {
  font-size: 32rpx;
  font-weight: 500;
}

.close-icon {
  font-size: 40rpx;
  color: #999;
  padding: 10rpx;
}

.filter-form {
  padding: 30rpx;
}

.form-item {
  margin-bottom: 20rpx;
}

.form-label {
  font-size: 28rpx;
  margin-bottom: 10rpx;
  color: #333;
}

.picker {
  height: 80rpx;
  line-height: 80rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333;
}

.filter-btns {
  display: flex;
  margin-top: 40rpx;
}

.btn-reset, .btn-apply {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  font-size: 30rpx;
  border-radius: 8rpx;
}

.btn-reset {
  background-color: #f5f5f5;
  color: #333;
  margin-right: 10rpx;
}

.btn-apply {
  background-color: #07c160;
  color: #fff;
  margin-left: 10rpx;
} 