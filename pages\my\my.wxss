/* pages/my/my.wxss */

/* 整体容器 */
.my-container {
  min-height: 100vh;
  background: var(--bg-gradient);
  padding: 0; /* 移除整体容器的内边距 */
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}

/* 用户信息区域 */
.user-info-section {
  margin-top: 0; /* 移除顶部外边距，使其紧贴顶部 */
  padding: 40rpx 30rpx; /* 调整内边距，左右添加内边距 */
  background: rgba(255, 255, 255, 0.1);
  border-radius: 0; /* 移除圆角，改为直角 */
  box-shadow: none; /* 移除阴影效果 */
  margin-bottom: 30rpx; /* 保留底部外边距 */
  padding-top: 120rpx; /* 为顶部状态栏留出空间 */
}

.user-info-container {
  display: flex;
  align-items: center;
  position: relative;
}

.user-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
}

.user-detail {
  margin-left: 30rpx;
  display: flex;
  flex-direction: column;
}

.user-nickname {
  font-size: 36rpx;
  color: #ffffff;
  font-weight: 500;
  margin-bottom: 10rpx;
}

.user-welcome {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.7);
}

/* 退出登录按钮容器 - 位于胶囊按钮下方，椭圆形设计 */
.logout-btn-container {
  position: absolute;
  right: 12rpx; /* 调整为与微信胶囊按钮垂直对齐 */
  top: 180rpx; /* 继续向胶囊下方移动20rpx */
  width: 174rpx; /* 修改为与微信胶囊一样的宽度 */
  height: 60rpx; /* 减少高度，变成椭圆形 */
  border-radius: 30rpx; /* 调整圆角以适应椭圆形 */
  background: rgba(0, 0, 0, 0.3); /* 修改为透明的黑色 */
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all 0.3s ease;
  backdrop-filter: blur(10rpx);
  -webkit-backdrop-filter: blur(10rpx);
  border: none;
  z-index: 10; /* 确保在最上层，避免被其他元素遮挡 */
}

.logout-btn-container:active {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-50%) scale(0.95);
}

.logout-icon {
  width: 36rpx; /* 稍微增大图标尺寸 */
  height: 36rpx;
  opacity: 0.9;
}

/* 登录区域 */
.login-container {
  display: flex;
  flex-direction: row; /* 改为横向布局，与登录后的用户信息容器一致 */
  align-items: center;
  position: relative;
  overflow: hidden; /* 确保内容不溢出 */
  transition: opacity 0.3s; /* 添加过渡效果 */
}

.login-container:active {
  opacity: 0.8; /* 点击时降低透明度 */
}

.default-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  margin-bottom: 0; /* 移除底部外边距 */
  margin-right: 30rpx; /* 添加右侧外边距，与登录后的用户头像一致 */
  background-color: rgba(255, 255, 255, 0.2);
}

/* 添加登录信息区域样式 */
.login-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.login-title {
  font-size: 36rpx;
  color: #ffffff;
  font-weight: 500;
  margin-bottom: 8rpx; /* 稍微减少间距 */
  line-height: 1.2; /* 添加行高控制 */
}

.login-tip {
  font-size: 26rpx; /* 稍微增大字体 */
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.3; /* 添加行高控制 */
  margin-top: 2rpx; /* 添加小的上边距，确保对齐 */
}

.login-button {
  background-color: var(--accent-color);
  color: #ffffff;
  border: none;
  border-radius: 40rpx;
  font-size: 30rpx;
  padding: 0 40rpx; /* 修改内边距 */
  margin-top: 0; /* 移除顶部外边距 */
  min-width: 120rpx; /* 减小最小宽度 */
  height: 70rpx; /* 设置固定高度 */
  line-height: 70rpx; /* 设置行高与高度一致，使文本垂直居中 */
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 确保登录按钮没有默认样式 */
.login-button::after {
  border: none;
}

/* 功能列表 */
.function-list {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15rpx;
  overflow: visible;
  width: 100%; /* 使用100%宽度，因为它已经在refreshable-area内部，refreshable-area已经设置了正确的宽度和边距 */
  margin: 0; /* 移除外边距，因为refreshable-area已经设置了正确的外边距 */
  position: relative;
}

.function-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: visible;
}

/* 确保按钮类型的功能项与普通功能项样式一致 */
button.function-item {
  background: transparent;
  font-size: inherit;
  text-align: left;
  line-height: normal;
  border-radius: 0;
  margin: 0;
  padding: 30rpx;
  width: 100%;
  box-sizing: border-box;
  color: inherit;
  font-weight: normal;
}

button.function-item::after {
  border: none;
}

.function-item:last-child {
  border-bottom: none;
}

.function-icon-container {
  width: 60rpx;
  height: 60rpx;
  border-radius: 30rpx;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 20rpx;
}

.function-icon {
  width: 36rpx;
  height: 36rpx;
}

/* 功能信息样式 */
.function-info {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.function-name {
  font-size: 30rpx;
  color: #ffffff;
}

/* 添加箭头图标样式 */
.arrow-icon {
  width: 24rpx;
  height: 24rpx;
  opacity: 0.7;
}

/* 引导提示气泡样式 */
.guide-tip {
  position: absolute;
  z-index: 100;
  right: 80rpx;
  bottom: -100rpx;
  transform: none;
  background-color: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px);
  padding: 16rpx 20rpx;
  border-radius: 12rpx;
  color: #fff;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.15);
  max-width: 220rpx;
  animation: pulse 1.5s ease infinite;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  pointer-events: auto;
}

.tip-arrow {
  position: absolute;
  top: -10rpx;
  left: 50%;
  margin-left: -5rpx;
  width: 0;
  height: 0;
  border-left: 10rpx solid transparent;
  border-right: 10rpx solid transparent;
  border-bottom: 10rpx solid rgba(255, 255, 255, 0.25);
  border-top: none;
}

.tip-never-show {
  font-size: 24rpx;
  text-align: center;
  font-weight: 500;
  color: #ffffff;
  margin-bottom: 12rpx;
  padding-bottom: 4rpx;
}

.tip-content {
  line-height: 1.3;
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.7);
  text-align: center;
}

.tip-close {
  position: absolute;
  top: 2rpx;
  right: 8rpx;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

/* 提示气泡动画 */
@keyframes pulse {
  0% {
    opacity: 0.8;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.8;
  }
}

/* 客服项特殊样式 - 完全重置button的默认样式 */
.customer-service-item {
  position: relative;
  overflow: visible;
  margin: 0;
  padding: 30rpx;
  border-bottom: 1rpx solid rgba(255, 255, 255, 0.1);
  background: transparent;
  text-align: left;
  line-height: normal;
  border-radius: 0;
  font-size: inherit;
  color: inherit;
  display: flex;
  align-items: center;
  width: 100%;
  box-sizing: border-box;
  font-weight: normal;
  /* 重置button的默认样式 */
  min-height: 0;
  border: none;
}

/* 重置button伪元素 */
.customer-service-item::after {
  border: none !important;
  background: none !important;
  content: none !important;
  transform: none !important;
  position: static !important;
  width: auto !important;
  height: auto !important;
}

/* 完全重置客服按钮内部的样式 */
.customer-service-item .function-icon-container {
  width: 60rpx;
  height: 60rpx;
  border-radius: 30rpx;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.customer-service-item .function-info {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.customer-service-item .function-name {
  font-size: 30rpx;
  color: #ffffff;
}

.customer-service-item .arrow-icon {
  width: 24rpx;
  height: 24rpx;
  opacity: 0.7;
}

/* 占位图标样式 */
.placeholder-icon {
  width: 36rpx;
  height: 36rpx;
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
}

/* 底部版本信息 */
.footer {
  width: 100%;
  text-align: center;
  padding: 20rpx 0 40rpx;
  margin-top: 20rpx;
  background-color: transparent;
}

.version-info {
  font-size: 24rpx;
  color: #999999;
}

/* 用户信息编辑弹窗容器 - 用于监听键盘高度变化 */
.edit-modal-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9000;
  pointer-events: none;
}

/* 用户信息编辑弹窗 */
.edit-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: flex-start;
  padding-top: 180rpx;
  z-index: 9999;
  transition: opacity 0.4s ease-in-out;
  pointer-events: auto;
  animation: fadeIn 0.4s ease-in-out;
}

/* 关闭动画样式 */
.edit-modal.closing {
  opacity: 0;
  animation: none;
}

/* 键盘弹出时的样式 - 现在不需要改变位置，因为初始位置已经设置好了 */
.edit-modal.keyboard-active {
  /* 移除之前的位置调整，保持与非激活状态一致 */
  transition: none;
}

.edit-content {
  width: 85%;
  background-color: #222;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
  max-height: 90vh;
  position: relative;
  animation: scaleIn 0.4s ease-in-out;
  transition: transform 0.4s ease-in-out, opacity 0.4s ease-in-out;
}

/* 内容关闭动画 */
.edit-content.closing {
  opacity: 0;
  transform: scale(0.95);
  animation: none;
}

/* 移除这些不必要的过渡效果 */
.edit-header,
.edit-body,
.edit-footer,
.avatar-section,
.info-item-row {
  will-change: auto;
  transform: none;
}

.edit-header {
  padding: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid rgba(255, 255, 255, 0.1);
}

.edit-title {
  font-size: 36rpx;
  color: #fff;
  font-weight: 500;
}

.edit-close {
  font-size: 48rpx;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1;
}

/* 调试信息样式 */
.debug-info {
  padding: 8rpx 30rpx;
  background-color: rgba(0, 0, 0, 0.5);
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  text-align: center;
}

/* 编辑区域样式 */
.edit-body {
  padding: 30rpx;
  max-height: 60vh;
  overflow-y: auto;
}

/* 键盘弹出时的编辑区域样式 */
.edit-body-focused {
  padding: 20rpx 30rpx;
}

.avatar-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 40rpx;
}

.edit-avatar {
  width: 160rpx;
  height: 160rpx;
  border-radius: 80rpx;
  margin-bottom: 16rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
}

.avatar-hint {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.6);
}

.info-item {
  margin-bottom: 30rpx;
}

.info-label {
  width: 160rpx; /* 稍微减少标签宽度 */
  color: #888;
  font-size: 26rpx; /* 稍微减小字体 */
  display: flex;
  align-items: center; /* 添加垂直居中对齐 */
  height: 100%; /* 确保标签占据整个高度 */
}

.info-input {
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 10rpx;
  height: 80rpx;
  padding: 0 20rpx;
  color: #fff;
  width: 100%;
  box-sizing: border-box;
}

/* 新增的文本域样式 */
.info-textarea {
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 10rpx;
  min-height: 80rpx;
  padding: 20rpx;
  color: #fff;
  width: 100%;
  box-sizing: border-box;
  line-height: 1.4;
}

.edit-footer {
  padding: 15rpx 30rpx;
  display: flex;
  justify-content: space-between;
  border-top: 1rpx solid rgba(255, 255, 255, 0.1);
}

/* 键盘弹出时的底部按钮区域 */
.edit-footer-focused {
  padding: 10rpx 30rpx;
  border-top: none;
}

.cancel-btn,
.save-btn {
  width: 45%;
  border-radius: 6rpx; /* 减小圆角 */
  height: 70rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  font-size: 30rpx;
  line-height: 1;
  padding: 0;
}

.cancel-btn {
  background-color: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.8);
}

.save-btn {
  background-color: var(--accent-color, #007aff);
  color: #fff;
}

/* 隐藏非当前编辑的输入项 */
.hide-item {
  display: none;
}

/* 横向布局的表单项 */
.info-item-row {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 10rpx;
  height: 80rpx;
  overflow: hidden;
  position: relative;
}

/* 行内标签样式 */
.info-label-inline {
  flex: none;
  width: 80rpx;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  text-align: center;
  background-color: rgba(255, 255, 255, 0.05);
  height: 100%;
  line-height: 80rpx;
  padding: 0 10rpx;
  box-sizing: border-box;
}

/* 行内输入框样式 */
.info-input-inline {
  flex: 1;
  height: 80rpx;
  padding: 0 20rpx;
  color: #fff;
  box-sizing: border-box;
  background: transparent;
  font-size: 30rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 呼吸式淡入动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* 呼吸式缩放动画 */
@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 添加预约记录面板样式 */
/* 移除appointments-panel样式，不再需要 */

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30rpx 0;
  margin: 20rpx 0;
}

.loading {
  width: 80rpx;
  height: 80rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #07c160;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.loading-text {
  margin-top: 20rpx;
  color: #999;
  font-size: 28rpx;
}

.no-appointments {
  padding: 30rpx 0;
  text-align: center;
  color: #999;
  font-size: 28rpx;
  margin: 20rpx 0;
}

/* 移除appointments-list样式，不再需要嵌套容器 */

.appointments-container {
  width: 100%;
  box-sizing: border-box;
  padding: 0 20rpx; /* 添加左右内边距 */
  background-color: #f5f5f5; /* 浅灰色背景 */
  max-height: 80vh; /* 限制最大高度为视窗高度的80% */
  overflow: hidden; /* 防止内容溢出 */
}

.appointments-scroll {
  max-height: calc(80vh - 20rpx); /* 减去内边距 */
  width: 100%;
  box-sizing: border-box;
  padding: 10rpx 0;
  overflow-y: auto; /* 允许垂直滚动 */
}

.appointment-item {
  margin-bottom: 20rpx;
  position: relative;
  min-height: 100rpx; /* 设置最小高度 */
  display: flex;
  flex-direction: column;
}

/* 麦当劳风格的订单卡片 */
.appointment-card {
  background-color: #ffffff;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

/* 订单卡片头部 */
.appointment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  background-color: #ffffff;
}

.appointment-title {
  display: flex;
  align-items: center;
}

.appointment-id {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
}

.appointment-status {
  font-size: 24rpx;
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  background-color: #f2f2f2;
  color: #666666;
}

.appointment-status.completed {
  color: #07c160;
  background-color: rgba(7, 193, 96, 0.1);
}

.appointment-status.cancelled {
  color: #ff4d4f;
  background-color: rgba(255, 77, 79, 0.1);
}

.appointment-status.pending,
.appointment-status.confirmed {
  color: #ff9800;
  background-color: rgba(255, 152, 0, 0.1);
}

/* 分割线 */
.divider {
  height: 2rpx;
  background-color: #f2f2f2;
  margin: 0 24rpx;
}

/* 订单内容 */
.appointment-content {
  padding: 24rpx;
}

.content-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start; /* 改为顶部对齐，防止内容过多时挤压 */
  margin-bottom: 16rpx;
  font-size: 28rpx;
  color: #666666;
  flex-wrap: wrap; /* 允许内容换行 */
}

.service-row {
  margin-bottom: 24rpx;
}

.row-label {
  color: #999999;
}

.row-value {
  color: #333333;
  text-align: right;
  font-weight: 500;
  max-width: 60%; /* 限制最大宽度 */
  word-break: break-all; /* 允许在任意字符间断行 */
}

.status-pending {
  color: #ff9800;
  font-weight: bold;
}

.service-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  flex: 1;
}

.service-price {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
}

.original-price {
  text-decoration: line-through;
  color: #999999;
}

/* 核销码样式 */
.verify-code-container {
  margin-top: 24rpx;
  padding: 30rpx 20rpx;
  background-color: #ffffff;
  border-radius: 8rpx;
  text-align: center;
  box-shadow: 0 4rpx 12rpx rgba(24, 144, 255, 0.2); /* 蓝色阴影效果 */
  border: 1rpx solid rgba(24, 144, 255, 0.3); /* 淡蓝色边框 */
}

.verify-code-label {
  font-size: 26rpx;
  color: #666666;
  margin-bottom: 12rpx;
  display: block;
  font-weight: 500;
}

.verify-code {
  font-size: 60rpx;
  font-weight: bold;
  color: #1890FF; /* 使用鲜亮的蓝色 - 支付宝蓝 */
  letter-spacing: 6rpx;
}

/* 底部按钮区 */
.appointment-footer {
  padding: 20rpx 24rpx;
  border-top: 2rpx solid #f2f2f2;
  display: flex;
  justify-content: flex-end;
  flex-shrink: 0; /* 防止被压缩 */
}

.cancel-btn, .delete-btn {
  padding: 0;
  background-color: transparent;
  font-size: 28rpx;
  line-height: 1.5;
  border: none;
  text-align: center;
}

.cancel-btn {
  color: #ff9800;
}

.delete-btn {
  color: #ff4d4f;
}

.cancel-btn::after, .delete-btn::after {
  border: none;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx 0;
}

.loading {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #ff9800;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #999999;
}

/* 无预约记录 */
.no-appointments {
  padding: 60rpx 0;
  text-align: center;
  color: #999999;
  font-size: 28rpx;
}

/* 更紧凑的按钮容器 */
.appointment-actions.compact {
  display: flex;
  justify-content: center; /* 改为居中对齐 */
  margin-top: 15rpx;
  margin-bottom: 5rpx;
}

.appointment-actions.compact .action-button {
  margin: 0 10rpx; /* 左右间距相等 */
  padding: 0 24rpx; /* 增加内边距 */
  height: 60rpx; /* 增加高度 */
  line-height: 58rpx;
  font-size: 26rpx; /* 增加字体大小 */
  border-radius: 30rpx;
  font-weight: 500; /* 增加字重 */
}

/* iOS风格的核销码显示 */
.appointment-verify-code-center {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin: 40rpx 0 30rpx;
  padding: 0;
}

.verify-code-value-xlarge {
  font-size: 90rpx;
  color: #333;
  font-weight: 500;
  letter-spacing: 8rpx;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen,
    Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
  margin-bottom: 10rpx;
}

/* iOS风格的预约时间行 */
.ios-time-row {
  text-align: center;
  margin-bottom: 30rpx;
}

.ios-time {
  font-size: 28rpx;
  color: #8e8e93;
  font-weight: normal;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen,
    Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
}

/* iOS风格的服务人员行 */
.ios-staff-row {
  text-align: center;
  margin-bottom: 30rpx;
}

.ios-staff {
  font-size: 42rpx; /* 调整为更合适的大小，比预约码小但仍然醒目 */
  color: #f1c40f; /* 明显的黄色 */
  font-weight: 500; /* 粗体 */
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen,
    Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
}

/* iOS风格的服务名称和操作按钮行 */
.ios-service-action-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 0;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  position: relative;
}

.ios-service-name {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen,
    Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
}

.ios-action-buttons {
  display: flex;
  align-items: center;
  position: absolute;
  right: 0;
}

.ios-action-btn {
  font-size: 32rpx; /* 与项目名称字体大小一致 */
  padding: 4rpx 12rpx;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen,
    Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
}

.ios-action-btn.modify {
  color: #007aff;
}

.ios-action-btn.cancel {
  color: #fff; /* 白色文字，与蓝色背景形成良好对比 */
  border: none;
  background-color: rgba(151, 3, 3, 0.51); /* 淡黄色背景，iOS风格 */
  backdrop-filter: blur(10px); /* 玻璃质感模糊效果 */
  -webkit-backdrop-filter: blur(10px); /* Safari 支持 */
  padding: 4rpx 12rpx; /* 与已完成标签相同的内边距 */
  font-size: 26rpx; /* 与已完成标签相同的字体大小 */
  font-weight: bold; /* 与已完成标签相同的字体粗细 */
  border-radius: 6rpx; /* 减小圆角至6rpx */
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1); /* 轻微阴影增加立体感 */
  display: flex; /* 添加弹性布局 */
  justify-content: center; /* 水平居中 */
  align-items: center; /* 垂直居中 */
  text-align: center; /* 确保文字居中 */
}

/* 价格和时间同行显示 */
.price-time-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  background-color: rgba(0, 0, 0, 0.02);
  padding: 12rpx 16rpx;
  border-radius: 8rpx;
}

.appointment-price-inline {
  font-size: 14px;
  color: #ff4d4f;
  font-weight: bold;
}

.appointment-time-inline {
  display: flex;
  justify-content: flex-end;
  flex: 1;
}

.appointment-time-inline .appointment-date,
.appointment-time-inline .appointment-hour {
  font-size: 14px;
  color: #666;
  margin-left: 10rpx;
}

/* 调整已完成订单的卡片样式 */
.appointment-item .appointment-content {
  background-color: #fff;
  overflow: hidden;
  border-radius: 0; /* 全部改为直角 */
  margin: 0 auto; /* 水平居中 */
  width: 90%; /* 设置宽度为父容器的90% */
  max-width: 650rpx; /* 设置最大宽度 */
  box-sizing: border-box;
}

/* iOS风格卡片 - 右侧直角 */
/* 移除ios-style-card样式，不再需要 */

.debug-btn {
  background-color: #f1c40f;
  color: #333;
  text-align: center;
  padding: 12rpx 0;
  margin-top: 20rpx;
  margin-bottom: 8rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  font-weight: 500;
  width: 100%;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
}

/* 隐形入口模块样式 */
.hidden-entry-container {
  position: absolute;
  top: 0;
  left: 0;
  height: 70%; /* 减小高度至70%，确保不遮挡底部按钮 */
  display: flex;
  justify-content: space-between;
  pointer-events: auto;
  z-index: 10;
  width: 100%;
  background-color: transparent; /* 完全透明 */
  border-radius: 12rpx;
  overflow: hidden;
}

.admin-entry,
.staff-entry {
  width: 50%;
  height: 100%; /* 确保占满整个入口容器的高度 */
  opacity: 0; /* 完全透明 */
  border-radius: 0;
  position: relative; /* 保持相对定位 */
}

.admin-entry {
  background-color: transparent; /* 完全透明背景 */
}

.admin-entry::after {
  content: ""; /* 移除文字 */
  display: none; /* 确保不显示 */
}

.staff-entry {
  background-color: transparent; /* 完全透明背景 */
}

.staff-entry::after {
  content: ""; /* 移除文字 */
  display: none; /* 确保不显示 */
}

/* 创建一个堆叠容器，用于放置重叠的元素 */
.stacked-container {
  position: relative;
  width: 100%; /* 使用100%宽度，因为它已经在refreshable-area内部，refreshable-area已经设置了正确的宽度和边距 */
  margin: 0 0 25rpx 0; /* 保留底部外边距，移除左右外边距 */
  height: 369rpx; /* 将现有高度419rpx再减少12%: 419 * 0.88 = 368.72 ≈ 369 */
  border-radius: 16rpx; /* 保持圆角不变 */
  overflow: hidden;
  box-shadow: none; /* 移除阴影效果 */
  margin-top: -10rpx; /* 保持原有的向上移动距离 */
}

/* 当前预约卡片样式 - 修改为绝对定位 */
.active-appointment-card {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 5;
  background-color: #ffffff;
  padding: 18rpx; /* 进一步减小内边距 */
  border-radius: 10rpx; /* 减小圆角 */
  box-sizing: border-box;
  overflow-y: auto; /* 保持滚动以适应更多内容 */
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx; /* 进一步减小底部间距 */
  padding-bottom: 8rpx; /* 进一步减小底部内边距 */
  border-bottom: 1rpx solid #f0f0f0;
}

.card-title {
  font-size: 30rpx; /* 进一步减小字体大小 */
  font-weight: 500;
  color: #333333;
}

.view-all {
  font-size: 22rpx; /* 减小字体大小 */
  color: #07c160;
}

/* 核销码区域样式 */
.verify-code-container {
  background-color: #f8f8f8;
  padding: 20rpx; /* 保持内边距 */
  border-radius: 10rpx; /* 保持圆角 */
  margin-bottom: 20rpx; /* 保持底部间距 */
  text-align: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.12); /* 增强阴影效果 */
  position: relative;
  border: 1rpx solid rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

/* 移除覆盖层，避免遮挡内容 */

.verify-code-label {
  font-size: 26rpx; /* 减小字体大小 */
  color: #666;
  margin-bottom: 8rpx; /* 保持底部间距 */
  position: relative;
  z-index: 2;
}

.verify-code-value {
  font-size: 54rpx; /* 增大字体大小 */
  font-weight: bold;
  color: #333333;
  letter-spacing: 8rpx; /* 稍微增加字符间距 */
  margin: 12rpx 0; /* 增加上下间距 */
  position: relative;
  z-index: 2;
  display: block;
  /* 添加文字阴影效果 */
  text-shadow: 1rpx 1rpx 3rpx rgba(0, 0, 0, 0.1);
}

/* 广告区域样式 - 修改为绝对定位 */
.ad-card {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1; /* 最底层 */
  overflow: hidden;
  border-radius: 20rpx;
  box-shadow: none; /* 移除阴影效果 */
}

.ad-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.ad-swiper {
  width: 100%;
  height: 100%; /* 确保广告占满整个容器高度 */
}

/* 自定义广告指示点样式 */
.ad-swiper .wx-swiper-dot {
  width: 12rpx; /* 进一步减小指示点大小 */
  height: 12rpx;
  border-radius: 6rpx;
  background: rgba(255, 255, 255, 0.5);
  margin-bottom: 12rpx; /* 进一步减小底部间距 */
}

.ad-swiper .wx-swiper-dot-active {
  width: 18rpx; /* 进一步减小活动指示点宽度 */
  background: #ffffff;
}

/* 底部信息区域样式 */
.bottom-info-area {
  width: 100%;
  height: 100%;
  position: relative;
  border-radius: 12rpx;
  overflow: hidden;
}

/* 调整现有样式 */
.footer {
  margin-bottom: 20rpx;
}

/* 余额管理相关样式 */
.balance-container {
  background: rgba(255, 255, 255, 0.15); /* 增加透明度，原来是0.1 */
  border-radius: 15rpx; /* 增加圆角，原来是12rpx */
  margin: 0 40rpx 30rpx; /* 设置左右边距各为40rpx，与refreshable-area一致 */
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08); /* 添加轻微阴影 */
}

/* 标签栏样式 */
.balance-tabs {
  display: flex;
  justify-content: space-around;
  height: 84rpx; /* 增加高度，原来是80rpx */
  border-bottom: 1rpx solid rgba(255, 255, 255, 0.15); /* 增加边框透明度，原来是0.1 */
}

.balance-tab {
  flex: 1;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8); /* 增加文字亮度，原来是0.7 */
  position: relative;
}

.balance-tab.active {
  color: #ffffff;
  font-weight: 500;
}

/* 所有标签都添加底部指示条 */
.balance-tab.active::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 48rpx; /* 增加宽度，原来是40rpx */
  height: 4rpx;
  background-color: #ffffff;
  border-radius: 2rpx;
}

/* 内容区域样式 */
.balance-content {
  padding: 0;
  max-height: 0;
  overflow: hidden;
  transition: all 0.3s ease;
  background-color: #ffffff;
  width: 100%;
}

/* 展开状态 */
.balance-content.expanded {
  padding: 0;
  max-height: 1000rpx;
  background-color: transparent;
}

/* 折叠状态 */
.balance-content.collapsed {
  padding: 0;
  max-height: 0;
}

/* 余额页面样式 */
.balance-page {
  display: none;
}

.balance-page.active {
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: #ffffff;
  padding: 20rpx 0; /* 添加上下内边距 */
}

.balance-amount-container {
  margin-bottom: 20rpx;
  text-align: center;
  width: 90%; /* 减少宽度，原来是100% */
  background-color: #f9f9f9;
  padding: 35rpx 20rpx; /* 增加上下内边距，原来是30rpx */
  border-radius: 15rpx; /* 增加圆角，原来是12rpx */
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05); /* 添加轻微阴影 */
}

/* 添加余额引导文字样式 */
.balance-guide-text {
  margin-top: 20rpx; /* 增加上边距，原来是15rpx */
  font-size: 26rpx; /* 增加字体大小，原来是24rpx */
  color: #999999;
  text-align: center;
}

.balance-guide-link {
  color: #07c160;
  display: inline-block;
  padding: 0 6rpx; /* 增加左右内边距，原来是4rpx */
  font-weight: 500; /* 添加字体粗细 */
}

.balance-label {
  font-size: 30rpx; /* 增加字体大小，原来是28rpx */
  color: #666666;
  margin-bottom: 18rpx; /* 增加底部边距，原来是15rpx */
  display: block;
}

.balance-amount {
  display: flex;
  align-items: baseline;
  justify-content: center;
}

.balance-currency {
  font-size: 34rpx; /* 增加字体大小，原来是32rpx */
  color: #333333;
  margin-right: 4rpx;
}

.balance-value {
  font-size: 64rpx; /* 增加字体大小，原来是60rpx */
  font-weight: 500;
  color: #07c160;
}

/* 充值优惠页面样式 */
.recharge-page {
  display: none;
}

.recharge-page.active {
  display: block;
  width: 100%;
  background: linear-gradient(
    to top,
    rgb(60, 60, 60),
    #000000
  ); /* 调整渐变颜色 */
  padding: 7rpx 0; /* 减少一半的上下内边距，从15rpx减少到7rpx */
  border-radius: 0 0 15rpx 15rpx; /* 添加底部圆角 */
}

.recharge-plans-list {
  display: flex;
  flex-direction: column;
  padding: 0 15rpx; /* 保留左右内边距不变 */
  max-height: 620rpx; /* 增加高度，原来是600rpx */
  overflow-y: auto;
  width: 100%;
  box-sizing: border-box;
}

.plan-card {
  width: 100%;
  margin-bottom: 7rpx; /* 减少一半的底部外边距，从15rpx减少到7rpx */
  margin-top: 7rpx; /* 减少一半的顶部外边距，从15rpx减少到7rpx */
  border-radius: 8rpx; /* 添加圆角 */
  overflow: hidden;
  box-shadow: none; /* 移除容器阴影 */
  position: relative;
  transition: transform 0.2s;
  background-color: transparent; /* 保持透明背景 */
  border: none;
}

.plan-card:active {
  transform: scale(0.97); /* 调整缩放比例，原来是0.98 */
}

.plan-card-image {
  width: 100%;
  height: 198rpx; /* 从220rpx减少到198rpx，减少了1/10的高度 */
  display: block;
  object-fit: contain; /* 从cover改为contain，确保图片完整显示 */
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.2); /* 给图片添加边缘阴影 */
  border-radius: 8rpx; /* 给图片添加圆角 */
}

.recharge-plan-item {
  margin-bottom: 20rpx;
  background-color: #ffffff;
  border-radius: 8rpx;
  padding: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.plan-content {
  flex: 1;
}

.plan-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 10rpx;
}

.plan-details {
  display: flex;
  flex-wrap: wrap;
}

.plan-price,
.plan-bonus,
.plan-total {
  display: flex;
  align-items: center;
  margin-right: 20rpx;
  margin-bottom: 6rpx;
}

.price-label,
.bonus-label,
.total-label {
  font-size: 24rpx;
  color: #999999;
  margin-right: 8rpx;
}

.price-value {
  font-size: 28rpx;
  color: #333333;
}

.bonus-value {
  font-size: 28rpx;
  color: #ff6b6b;
}

.total-value {
  font-size: 28rpx;
  color: #07c160;
  font-weight: 500;
}

.plan-action {
  padding-left: 20rpx;
}

.plan-btn {
  display: inline-block;
  padding: 10rpx 20rpx;
  background-color: #07c160;
  color: #ffffff;
  font-size: 26rpx;
  border-radius: 6rpx;
}

/* 充值记录页面样式 */
.record-page {
  display: none;
}

.record-page.active {
  display: block;
  background-color: #ffffff;
  padding: 20rpx 15rpx;
  border-radius: 0 0 20rpx 20rpx;
}

.recharge-records-list {
  max-height: 620rpx;
  overflow-y: auto;
  padding: 12rpx;
  box-sizing: border-box;
  background-color: #fcfcfc;
}

/* 充值记录项目样式 - 高端优雅风格 */
.record-item {
  margin-bottom: 24rpx;
  background-color: #ffffff;
  border-radius: 8rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  position: relative;
  overflow: hidden;
}

/* 充值记录日期样式 */
.record-date {
  display: flex;
  justify-content: space-between;
  color: #8a8f99;
  font-size: 22rpx;
  padding: 16rpx 24rpx;
  border-bottom: 1rpx solid #f0f2f5;
  background-color: #fafbfc;
}

/* 充值记录内容区域 */
.record-content {
  padding: 28rpx 24rpx;
  position: relative;
}

/* 充值记录标题 */
.record-title {
  font-size: 34rpx;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 22rpx;
  padding-right: 120rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  letter-spacing: 1rpx;
}

/* 充值记录详情 */
.record-details {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  margin-bottom: 20rpx;
  position: relative;
}

.record-info {
  display: flex;
  align-items: center;
  margin-right: 36rpx;
  margin-bottom: 12rpx;
}

.record-label {
  font-size: 24rpx;
  color: #8a8f99;
  margin-right: 10rpx;
}

.record-value {
  font-size: 30rpx;
  color: #3d4b66;
  font-weight: 600;
}

/* 充值记录状态标签样式 */
.record-status {
  position: absolute;
  top: 0;
  right: 0;
  font-size: 22rpx;
  padding: 6rpx 18rpx;
  border-radius: 4rpx;
}

.record-status.verified {
  background-color: #f0f7f4;
  color: #34a77c;
  border: 1rpx solid rgba(52, 167, 124, 0.2);
}

.record-status.pending {
  background-color: #fff8f0;
  color: #e6a23c;
  border: 1rpx solid rgba(230, 162, 60, 0.2);
}

.record-status.cancelled {
  background-color: #f5f5f5;
  color: #999999;
  border: 1rpx solid rgba(153, 153, 153, 0.2);
}

/* 核销码样式 */
.record-code {
  margin-top: 24rpx;
  padding: 28rpx 20rpx;
  background-color: #f8fafc;
  border-radius: 6rpx;
  text-align: center;
  border: 1rpx solid #e8edf2;
  position: relative;
}

.code-label {
  font-size: 26rpx;
  color: #8a8f99;
  display: block;
  margin-bottom: 16rpx;
  letter-spacing: 2rpx;
}

.code-value {
  font-size: 56rpx;
  font-weight: 700;
  color: #2c3e50;
  letter-spacing: 10rpx;
  display: block;
  position: relative;
  padding: 10rpx 0;
}

/* 添加核销码前后的装饰元素 */
.code-value::before,
.code-value::after {
  content: "";
  position: absolute;
  height: 4rpx;
  width: 40rpx;
  background: #d0d7de;
  top: 50%;
  transform: translateY(-50%);
}

.code-value::before {
  left: 20%;
}

.code-value::after {
  right: 20%;
}

/* 充值记录操作按钮 */
.record-actions {
  margin-top: 24rpx;
  text-align: center;
}

.cancel-recharge-btn {
  background-color: #ff4d4f;
  color: #ffffff;
  border: none;
  border-radius: 6rpx;
  padding: 16rpx 32rpx;
  font-size: 28rpx;
  font-weight: 500;
  min-width: 160rpx;
  transition: all 0.3s ease;
}

.cancel-recharge-btn:active {
  background-color: #d9363e;
  transform: scale(0.98);
}

.cancel-recharge-btn::after {
  border: none;
}

.delete-recharge-btn {
  background-color: #999999;
  color: #ffffff;
  border: none;
  border-radius: 6rpx;
  padding: 16rpx 32rpx;
  font-size: 28rpx;
  font-weight: 500;
  min-width: 160rpx;
  transition: all 0.3s ease;
}

.delete-recharge-btn:active {
  background-color: #666666;
  transform: scale(0.98);
}

.delete-recharge-btn::after {
  border: none;
}

/* 充值方案光效动画 */
@keyframes plan-shimmer {
  0% {
    transform: translateX(-100%) skewX(-20deg);
  }
  40% {
    transform: translateX(300%) skewX(-20deg);
  }
  100% {
    transform: translateX(300%) skewX(-20deg);
  }
}

.plan-shimmer-effect {
  position: relative;
  overflow: hidden;
}

.plan-shimmer-effect::before {
  content: "";
  position: absolute;
  top: 0;
  left: -50%;
  width: 80%;
  height: 100%;
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.3) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  animation: plan-shimmer 3.9s ease-in-out -1.9s infinite;
  z-index: 11; /* 确保在卡片阴影效果之上 */
  pointer-events: none; /* 确保不会影响点击事件 */
}

/* 充值优惠页面的特殊样式 */
.recharge-page .loading-text {
  color: #ffffff;
}

/* 删除旧的空状态样式 */
/* .recharge-page .no-plans {
  color: #ffffff;
} */

/* 空状态容器通用样式 - 适用于余额、充值优惠和充值记录 */
.empty-balance-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx 20rpx;
  text-align: center;
  height: 200rpx;
}

.empty-balance-label {
  font-size: 30rpx;
  color: #ffffff;
  margin-bottom: 20rpx;
  opacity: 0.9;
}

.empty-balance-amount {
  margin: 10rpx 0 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.empty-balance-icon {
  font-size: 80rpx;
  line-height: 1;
}

.empty-balance-tip {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-top: 10rpx;
}

/* 充值记录页面空状态文字特殊样式 */
.record-page .empty-balance-tip {
  color: #3d4b66; /* 使用高端深蓝灰色 */
  font-weight: 500; /* 添加字体粗细，增强显示效果 */
  font-size: 28rpx;
  letter-spacing: 1rpx;
}

/* 充值优惠页面空状态文字特殊样式 */
.recharge-page .empty-balance-tip {
  color: #07c160; /* 使用与余额数字相同的绿色 */
  font-weight: 500; /* 添加字体粗细，增强显示效果 */
}

/* 充值计划详情样式 */
.plan-detail-image {
  width: 100%;
  height: 300rpx;
  border-radius: 8rpx;
  margin-bottom: 20rpx;
}

.plan-detail-info {
  padding: 10rpx 0;
}

.plan-detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
  padding: 12rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.plan-detail-label {
  font-size: 28rpx;
  color: #666;
}

.plan-detail-value {
  font-size: 32rpx;
  font-weight: 500;
}

.price-color {
  color: #333;
}

.bonus-color {
  color: #ff6b6b;
}

.total-color {
  color: #0070c9;
}

/* 旧的plan-description样式已被新的深色主题样式替代 */

/* 模态对话框样式 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: center;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 1;
}

.modal-content {
  width: 80%;
  background-color: #fff;
  border-radius: 12rpx;
  position: relative;
  z-index: 2;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.15);
}

.modal-header {
  padding: 30rpx 30rpx 20rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  border-bottom: none;
}

.modal-title {
  font-size: 36rpx;
  font-weight: 500;
  color: #333;
  text-align: center;
}

.modal-close {
  font-size: 40rpx;
  color: #999;
  line-height: 1;
}

.modal-body {
  padding: 20rpx 30rpx 30rpx;
  max-height: 70vh;
  overflow-y: auto;
}

.modal-footer {
  padding: 0 30rpx 30rpx;
  border-top: none;
  display: flex;
  justify-content: center;
}

/* ==================== 充值成功弹窗样式 ==================== */

/* 充值成功弹窗容器 - 高冷深色主题 */
.eco-success-modal {
  animation: breatheIn 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
  transform-origin: center center;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.5), 0 8rpx 24rpx rgba(0, 0, 0, 0.3), 0 0 0 2rpx rgba(255, 204, 51, 0.5), 0 0 40rpx rgba(255, 204, 51, 0.4), 0 0 80rpx rgba(255, 204, 51, 0.2);
  border-radius: 16rpx;
  overflow: hidden;
  background: #0f0f0f;
  max-width: 580rpx;
  width: 88%;
  max-height: 75vh;
  position: relative;
  border: 1rpx solid #2a2a2a;
}

/* 呼吸进入动画 - 优化版 */
@keyframes breatheIn {
  0% {
    opacity: 0;
    transform: scale(0.85) translateY(40rpx);
  }
  60% {
    opacity: 0.9;
    transform: scale(1.02) translateY(-8rpx);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* 呼吸退出动画 - 优化版 */
@keyframes breatheOut {
  0% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
  40% {
    opacity: 0.8;
    transform: scale(1.02) translateY(-8rpx);
  }
  100% {
    opacity: 0;
    transform: scale(0.85) translateY(40rpx);
  }
}

/* 弹窗关闭时的动画类 */
.eco-success-modal.closing {
  animation: breatheOut 0.4s cubic-bezier(0.55, 0.055, 0.675, 0.19) forwards;
}

/* 弹窗头部样式 - 深色简约设计 */
.eco-header {
  background: #1a1a1a;
  color: white;
  padding: 36rpx 24rpx 32rpx;
  text-align: center;
  position: relative;
  border-bottom: 1rpx solid #2a2a2a;
}

.eco-header .modal-title {
  color: #ffcc33;
  font-size: 34rpx;
  font-weight: 600;
  margin-bottom: 6rpx;
  display: block;
}

.modal-subtitle {
  color: rgba(255, 255, 255, 0.5);
  font-size: 24rpx;
  font-weight: 400;
  display: block;
}

/* 方案信息卡片样式 - 深色主题 */
.plan-info-card {
  margin: 20rpx 16rpx;
  background: #1a1a1a;
  border-radius: 12rpx;
  padding: 24rpx 20rpx;
  border: 1rpx solid #2a2a2a;
}

.plan-title-section {
  margin-bottom: 20rpx;
  text-align: center;
  background: transparent;
  padding: 16rpx 0;
}

.plan-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #ffcc33;
  margin-bottom: 6rpx;
  display: block;
}

.plan-description {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.5);
  line-height: 1.3;
  display: block;
}

/* 金额详情样式 - 深色主题 */
.amount-details {
  background: #1a1a1a;
  border-radius: 8rpx;
  padding: 16rpx;
  border: 1rpx solid #2a2a2a;
}

.amount-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8rpx 0;
}

.amount-label {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.6);
  font-weight: 400;
}

.amount-value {
  font-size: 26rpx;
  font-weight: 600;
}

.recharge-amount {
  color: #ffffff;
}

.bonus-amount {
  color: #ffcc33;
}

.bonus-row {
  border-bottom: 1rpx dashed #2a2a2a;
  margin-bottom: 6rpx;
  padding-bottom: 10rpx;
}

.amount-divider {
  height: 1rpx;
  background: #2a2a2a;
  margin: 6rpx 0;
}

.total-row {
  background: #2a2a2a;
  margin: 6rpx -16rpx -16rpx;
  padding: 12rpx 16rpx;
  border-radius: 0 0 8rpx 8rpx;
}

.total-label {
  color: rgba(255, 255, 255, 0.7);
  font-size: 24rpx;
}

.total-amount {
  color: #ffcc33;
  font-size: 28rpx;
  font-weight: 700;
}

/* 核销码容器样式 - 简洁纯色设计 */
.eco-verify-container {
  background: #000000;
  margin: 20rpx 16rpx 24rpx;
  padding: 24rpx 20rpx;
  border-radius: 12rpx;
  text-align: center;
  position: relative;
  border: 1rpx solid #1a1a1a;
}

/* 核销码标题 */
.verify-code-title {
  color: #ffcc33;
  font-size: 24rpx;
  font-weight: 600;
  margin-bottom: 16rpx;
  display: block;
}

/* 核销码数字 - 高对比度设计 */
.eco-verify-code {
  font-size: 72rpx;
  font-weight: 800;
  color: #ffffff;
  letter-spacing: 12rpx;
  margin: 20rpx 0;
  display: block;
  text-align: center;
  position: relative;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.5);
  animation: pulse 3s ease-in-out infinite;
}

/* 核销码脉冲动画 - 简约版本 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

/* 核销码提示文字 */
.verify-code-tip {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-top: 16rpx;
  display: block;
  text-align: center;
  position: relative;
}

/* 核销码提示区域 */
.verify-note {
  margin-top: 20rpx;
  padding: 12rpx 16rpx;
  background: rgba(255, 204, 51, 0.1);
  border-radius: 8rpx;
  border: 1rpx solid rgba(255, 204, 51, 0.2);
}

.note-text {
  font-size: 20rpx;
  color: rgba(255, 204, 51, 0.8);
  line-height: 1.3;
  text-align: center;
}

/* 旧的充值信息样式已被新的方案信息卡片替代 */

/* 弹窗底部按钮样式 - 深色简约设计 */
.modal-footer {
  display: flex;
  padding: 0 20rpx 24rpx;
  gap: 12rpx;
}

/* 取消按钮样式 */
.eco-cancel-btn {
  flex: 1;
  height: 80rpx;
  background: #2a2a2a;
  color: rgba(255, 255, 255, 0.6);
  border: 1rpx solid #333333;
  border-radius: 8rpx;
  font-size: 28rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.eco-cancel-btn:active {
  transform: translateY(1rpx);
  background: #333333;
  color: rgba(255, 255, 255, 0.7);
}

/* 确认按钮样式 */
.eco-confirm-btn {
  flex: 1;
  height: 80rpx;
  background: #f3dfc4;
  color: #333333;
  border: none;
  border-radius: 8rpx;
  font-size: 28rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  box-shadow: 0 4rpx 12rpx rgba(243, 223, 196, 0.15);
}

.eco-confirm-btn:active {
  transform: translateY(1rpx);
  background: rgba(243, 223, 196, 0.8);
}

/* 按钮文字样式 */
.btn-text {
  position: relative;
}

/* 预约信息卡片 */
.appointment-info-card {
  background-color: #ffffff;
  border-radius: 8rpx;
  padding: 15rpx;
  position: relative;
  z-index: 3;
}

.info-row {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
  position: relative;
}

.info-label {
  font-size: 24rpx;
  color: #888;
  margin-right: 10rpx;
}

.info-value {
  font-size: 26rpx;
  color: #333;
  flex: 1;
}

.service-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 10rpx;
}

.total-price {
  color: #ff4d4f;
  font-weight: 500;
}

/* 预约状态标签 */
.appointment-status {
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 4rpx;
  margin-left: 10rpx;
  position: relative;
  z-index: 7; /* 确保状态标签在最上层 */
}

.appointment-status.pending {
  background-color: #fff7e6;
  color: #fa8c16;
  border: 1rpx solid #ffd591;
}

.appointment-status.confirmed {
  background-color: #e6f7ff;
  color: #1890ff;
  border: 1rpx solid #91d5ff;
}

/* 主页面最前端展示的当前预约内容专属样式 */
.main-latest-appointment-card {
  position: relative;
  overflow: visible;
  background: none; /* 移除背景色，使用图片元素作为背景 */
  border-radius: 16rpx;
  box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.3);
  padding: 22rpx 20rpx 18rpx 20rpx;
  width: 100%;
  min-height: unset;
  max-height: unset;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  border: none; /* 移除边框 */
}

/* 添加背景图片样式 */
.appointment-bg-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1; /* 确保背景在内容之下 */
  border-radius: 16rpx; /* 保持与容器相同的圆角 */
}

.main-latest-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
}

.main-latest-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.main-latest-view-all {
  font-size: 22rpx;
  color: #07c160;
}

.main-latest-verify-code {
  background: linear-gradient(135deg, #fffbe6 0%, #e6f7ff 100%);
  border-radius: 8rpx;
  margin-bottom: 8rpx;
  padding: 10rpx 0 6rpx 0;
  text-align: center;
  box-shadow: 0 2rpx 8rpx rgba(24, 144, 255, 0.06);
}

.main-latest-verify-label {
  font-size: 20rpx;
  color: #888;
  display: block;
  margin-bottom: 2rpx;
}

.main-latest-verify-value {
  font-size: 76rpx;
  color: #fa8c16;
  font-weight: bold;
  letter-spacing: 14rpx;
  text-shadow: 1rpx 1rpx 4rpx rgba(250, 140, 22, 0.08);
  display: block;
  line-height: 1.1;
  width: 100%;
  max-width: 100%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  text-align: center;
  margin-bottom: 8rpx;
}

.main-latest-info {
  padding: 0;
  margin: 0;
}

.main-latest-row {
  display: flex;
  align-items: center;
  margin-bottom: 4rpx;
}

.main-latest-label {
  font-size: 20rpx;
  color: #888;
  min-width: 80rpx;
}

.main-latest-value {
  font-size: 22rpx;
  color: #333;
  flex: 1;
}

.main-latest-service {
  font-size: 24rpx;
  font-weight: 500;
  color: #333;
  flex: 1;
}

.main-latest-price {
  color: #ff4d4f;
  font-weight: 600;
}

.main-latest-actions {
  display: flex;
  justify-content: center;
  margin-top: 8rpx;
}

.main-latest-cancel-btn {
  min-width: 180rpx;
  height: 52rpx;
  line-height: 52rpx;
  font-size: 22rpx;
  border-radius: 18rpx; /* 增大圆角 */
  background: transparent; /* 设置为透明背景 */
  color: #f39c12; /* 改为黄色文字 */
  box-shadow: none; /* 移除阴影 */
  border: none; /* 移除边框 */
  text-align: center; /* 文字居中对齐 */
  display: flex;
  justify-content: center; /* 内容居中对齐 */
  align-items: center;
  font-weight: 600;
  letter-spacing: 2rpx;
  padding: 0 10rpx; /* 调整内边距 */
}

.main-latest-verify-code-no-bg {
  width: 100%;
  text-align: left; /* 改为左对齐 */
  margin: 4rpx 0 10rpx 0; /* 调整上下边距 */
  padding: 0 20rpx; /* 添加左右内边距 */
}

.main-latest-verify-value-no-bg {
  font-size: 90rpx; /* 增加字体大小，原值为70rpx */
  color: #3a2a1c; /* 深棕色 */
  font-weight: bold;
  letter-spacing: 10rpx; /* 减小字间距 */
  text-shadow: 1rpx 1rpx 5rpx rgba(255, 255, 255, 0.5);
  display: block;
  line-height: 1.2;
  width: 100%;
  max-width: 100%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  text-align: left; /* 左对齐 */
  margin-bottom: 5rpx;
  padding-left: 50rpx; /* 向右移动50rpx */
  margin-top: -5rpx; /* 向上移动20rpx，原值为15rpx */
}

/* 小巧整洁的基础信息 */
.small-info {
  padding: 0 2rpx;
  margin: 0;
}

.small-label {
  font-size: 16rpx;
  color: #aaa;
  min-width: 60rpx;
}

.small-value {
  font-size: 18rpx;
  color: #444;
}

.small-service {
  font-size: 20rpx;
  font-weight: 500;
  color: #333;
  flex: 1;
}

/* 取消预约按钮右下角 */
.main-latest-actions.right-bottom {
  position: absolute;
  right: 18rpx;
  bottom: 16rpx;
  margin: 0;
  z-index: 10;
  display: flex;
  justify-content: flex-end;
  width: auto;
  background: none;
}

.main-latest-appointment-card {
  position: relative;
  overflow: visible;
}

/* 优化预约信息排版 */
.better-layout {
  padding: 0 6rpx;
  margin: 0;
}

.better-service-row {
  justify-content: center;
  margin-bottom: 10rpx;
}

.better-service {
  font-size: 28rpx;
  font-weight: 600;
  color: #3a2a1c; /* 修改为深棕色，适合浅色背景 */
  text-align: center;
  width: 100%;
  padding: 4rpx 0;
  position: relative;
}

/* 修改服务名称下方的装饰线 */
.better-service::after {
  content: "";
  position: absolute;
  bottom: -6rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 2rpx;
  background: linear-gradient(
    90deg,
    transparent,
    #6e4a32,
    transparent
  ); /* 更改为棕色系，与背景协调 */
}

.better-time-price-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6rpx;
  padding: 8rpx 16rpx;
  background: rgba(255, 255, 255, 0.3); /* 半透明白色背景 */
  border-radius: 8rpx;
  margin-top: 8rpx;
}

.better-price-label {
  font-size: 20rpx;
  color: #5a3d28; /* 深棕色 */
  margin-right: 4rpx;
  margin-left: 0; /* 移除左边距，确保与项目名称左对齐 */
  padding-top: 10rpx; /* 添加顶部内边距，使其与价格数字垂直居中对齐 */
}

.better-price-value {
  font-size: 26rpx; /* 增大字体大小，原来是22rpx */
  color: #5a3d28; /* 深棕色 */
  font-weight: 600;
  margin-top: 10rpx; /* 向下移动10rpx */
}

.better-time-label {
  font-size: 20rpx;
  color: #5a3d28; /* 深棕色 */
  margin-right: 4rpx;
  margin-left: 0; /* 移除左边距，确保与项目名称左对齐 */
}

.better-time-value {
  font-size: 22rpx;
  color: #5a3d28; /* 深棕色 */
}

/* 优化底部按钮 */
.better-bottom-btn {
  position: absolute;
  left: -59rpx; /* 向右移动15rpx，原值为-74rpx */
  right: auto; /* 移除右侧定位 */
  bottom: 18rpx;
  display: flex;
  justify-content: flex-start; /* 左对齐 */
  background: transparent; /* 确保背景完全透明 */
  z-index: 10;
}

.better-cancel-btn {
  min-width: 180rpx;
  height: 52rpx;
  line-height: 52rpx;
  font-size: 22rpx;
  border-radius: 18rpx; /* 增大圆角 */
  background: transparent; /* 将背景改为完全透明 */
  color: #f39c12; /* 保持金黄色文字 */
  box-shadow: none; /* 移除阴影 */
  border: none; /* 移除边框 */
  text-align: center; /* 文字居中对齐 */
  display: flex;
  justify-content: center; /* 内容居中对齐 */
  align-items: center;
  font-weight: 600;
  letter-spacing: 2rpx;
  padding: 0 10rpx; /* 调整内边距 */
}

.fade-image {
  transition: opacity 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 新的布局样式 */
.new-layout {
  padding: 0 0 0 50rpx; /* 再向右移动20rpx，从30rpx增加到50rpx */
  margin: 0;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

/* 核销码样式调整 - 缩小并左对齐 */
.main-latest-verify-code-no-bg {
  width: 100%;
  text-align: left;
  margin: 4rpx 0 10rpx 0;
  padding: 0 20rpx;
}

.main-latest-verify-value-no-bg {
  font-size: 90rpx; /* 增加字体大小，原值为70rpx */
  color: #3a2a1c; /* 深棕色 */
  font-weight: bold;
  letter-spacing: 10rpx; /* 减小字间距 */
  text-shadow: 1rpx 1rpx 5rpx rgba(255, 255, 255, 0.5);
  display: block;
  line-height: 1.2;
  width: 100%;
  max-width: 100%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  text-align: left; /* 左对齐 */
  margin-bottom: 5rpx;
  padding-left: 50rpx; /* 向右移动50rpx */
  margin-top: -5rpx; /* 向上移动20rpx，原值为15rpx */
}

/* 项目名称样式 */
.new-service {
  font-size: 28rpx;
  font-weight: 600;
  color: #3a2a1c;
  text-align: left;
  width: 100%;
  padding: 4rpx 0;
  margin-bottom: 5rpx;
}

/* 预约时间样式 */
.main-latest-row {
  display: flex;
  align-items: center;
  margin-bottom: 5rpx;
  padding-left: 0; /* 确保没有左内边距 */
}

.new-time-label {
  font-size: 22rpx;
  color: #5a3d28;
  margin-right: 8rpx;
}

.new-time-value {
  font-size: 22rpx;
  color: #5a3d28;
}

/* 价格展示样式 */
.new-price-label {
  font-size: 22rpx;
  color: #5a3d28;
  margin-right: 8rpx;
}

.new-price-value {
  font-size: 22rpx;
  color: #5a3d28;
  font-weight: 600;
}

/* 预约时间样式 */
.main-latest-row {
  display: flex;
  align-items: center;
  margin-bottom: 5rpx;
  padding-left: 0; /* 确保没有左内边距 */
}

.better-time-label {
  font-size: 20rpx;
  color: #5a3d28; /* 深棕色 */
  margin-right: 4rpx;
  margin-left: 0; /* 移除左边距，确保与项目名称左对齐 */
}

.better-time-value {
  font-size: 22rpx;
  color: #5a3d28; /* 深棕色 */
}

/* 价格展示样式 */
.better-price-label {
  font-size: 20rpx;
  color: #5a3d28; /* 深棕色 */
  margin-right: 4rpx;
  margin-left: 0; /* 移除左边距，确保与项目名称左对齐 */
}

/* 添加空状态提示文字样式 */
.empty-ad-tip {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.05);
  color: #999;
  font-size: 28rpx;
  text-align: center;
}

/* 添加可下拉刷新区域的样式 */
.refreshable-area {
  width: calc(100% - 80rpx); /* 与余额容器保持一致的宽度 */
  margin: 0 40rpx; /* 左右各添加40rpx的外边距 */
  height: auto;
  min-height: 800rpx; /* 设置一个合适的最小高度，确保能够滚动 */
  background: transparent;
  /* 确保不影响原有布局 */
  position: relative;
  z-index: 1;
}

/* 确保scroll-view内部的内容样式不变 */
.refreshable-area .function-list,
.refreshable-area .stacked-container {
  width: 100%;
  margin: 0;
  padding: 0;
}

/* 服务人员信息样式 */
.better-staff-label {
  font-size: 22rpx; /* 增大字体大小，原来是20rpx */
  color: #5a3d28; /* 深棕色 */
  margin-right: 4rpx;
  margin-left: 40rpx; /* 增加左边距，向后移动更多，原来是35rpx */
  padding-top: 10rpx; /* 添加顶部内边距，使其与价格标签垂直对齐 */
}

.better-staff-value {
  font-size: 22rpx;
  color: #5a3d28; /* 深棕色 */
  font-weight: 500;
  margin-top: 10rpx; /* 添加顶部边距，与价格值保持一致 */
} /* 
==================== 投诉建议样式 ==================== */

/* 投诉建议面板 */
.suggestions-panel {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease-in-out;
  background-color: #f8f8f8;
  border-radius: 0 0 12rpx 12rpx;
}

.suggestions-panel.show {
  max-height: 70vh; /* 限制最大高度为视窗高度的70% */
  background-color: #f8f8f8;
  border-radius: 0 0 12rpx 12rpx;
}

/* 投诉建议滚动视图 */
.suggestions-scroll-view {
  height: 100%;
  max-height: 70vh; /* 限制滚动视图的最大高度 */
}

/* 建议区块 */
.suggestion-section {
  margin: 20rpx;
  padding: 24rpx;
  background-color: #ffffff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.suggestion-section:first-child {
  margin-top: 30rpx;
}

.suggestion-section:last-child {
  margin-bottom: 30rpx;
}

/* 区块标题 */
.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.section-icon {
  width: 36rpx;
  height: 36rpx;
  margin-right: 12rpx;
}

.unread-count {
  margin-left: 12rpx;
  padding: 4rpx 12rpx;
  background-color: #ff3b30;
  color: white;
  font-size: 20rpx;
  border-radius: 12rpx;
  min-width: 24rpx;
  text-align: center;
}

/* 联系按钮 */
.contact-buttons {
  display: flex;
  gap: 20rpx;
}

.contact-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  font-weight: 500;
  border: none;
  transition: all 0.2s ease;
}

.contact-btn::after {
  border: none;
}

.phone-btn {
  background: linear-gradient(135deg, #07c160, #05a850);
  color: white;
}

.phone-btn:active {
  background: linear-gradient(135deg, #05a850, #048a44);
}

.wechat-btn {
  background: linear-gradient(135deg, #1aad19, #169917);
  color: white;
}

.wechat-btn:active {
  background: linear-gradient(135deg, #169917, #128a14);
}

.btn-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 8rpx;
}

/* 建议表单 */
.suggestion-form {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.suggestion-input {
  width: 100%;
  min-height: 200rpx;
  padding: 20rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 8rpx;
  font-size: 28rpx;
  line-height: 1.5;
  background-color: #fafafa;
  box-sizing: border-box;
}

.suggestion-input:focus {
  border-color: #07c160;
  background-color: #ffffff;
}

/* 上传按钮 */
.upload-section {
  display: flex;
  gap: 20rpx;
}

.upload-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16rpx;
  border: 2rpx dashed #ccc;
  border-radius: 8rpx;
  background-color: #fafafa;
  font-size: 26rpx;
  color: #666;
  transition: all 0.2s ease;
}

.upload-btn::after {
  border: none;
}

.upload-btn:active {
  border-color: #07c160;
  background-color: rgba(7, 193, 96, 0.05);
  color: #07c160;
}

/* 已上传文件预览 */
.uploaded-files {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.uploaded-images,
.uploaded-videos {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.uploaded-item {
  position: relative;
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
  overflow: hidden;
}

.uploaded-image,
.uploaded-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.remove-btn {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  width: 32rpx;
  height: 32rpx;
  background-color: #ff3b30;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  font-weight: bold;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}

/* 联系信息 */
.contact-info {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.phone-input {
  padding: 20rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 8rpx;
  font-size: 28rpx;
  background-color: #fafafa;
}

.phone-input:focus {
  border-color: #07c160;
  background-color: #ffffff;
}

.anonymous-option {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.anonymous-text {
  font-size: 26rpx;
  color: #666;
}

/* 提交按钮 */
.submit-btn {
  padding: 24rpx;
  background: linear-gradient(135deg, #07c160, #05a850);
  color: white;
  border-radius: 8rpx;
  font-size: 30rpx;
  font-weight: 600;
  border: none;
  transition: all 0.2s ease;
}

.submit-btn::after {
  border: none;
}

.submit-btn:active {
  background: linear-gradient(135deg, #05a850, #048a44);
  transform: translateY(1rpx);
}

.submit-btn[disabled] {
  background: #ccc;
  color: #999;
  transform: none;
}

/* 回复区域 */
.reply-section {
  display: flex;
  flex-direction: column;
  height: 600rpx;
}

.conversation-list {
  flex: 1;
  padding: 20rpx 0;
  margin-bottom: 20rpx;
}

.empty-conversation {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300rpx;
  color: #999;
}

.empty-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 16rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 26rpx;
}

/* 消息项 */
.message-item {
  margin-bottom: 24rpx;
}

.message {
  display: flex;
  align-items: flex-start;
  gap: 16rpx;
}

.user-message {
  flex-direction: row-reverse;
}

.message-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

.message-avatar image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.message-bubble {
  max-width: 70%;
  padding: 16rpx 20rpx;
  border-radius: 16rpx;
  position: relative;
}

.user-message .message-bubble {
  background-color: #07c160;
  color: white;
  border-bottom-right-radius: 4rpx;
}

.admin-message .message-bubble {
  background-color: #f0f0f0;
  color: #333;
  border-bottom-left-radius: 4rpx;
}

.message-content {
  font-size: 28rpx;
  line-height: 1.4;
  word-wrap: break-word;
}

.message-images {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
  margin-top: 12rpx;
}

.message-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
  object-fit: cover;
}

.message-time {
  font-size: 20rpx;
  opacity: 0.7;
  margin-top: 8rpx;
  text-align: right;
}

.user-message .message-time {
  text-align: left;
}

/* 回复输入区域 */
.reply-input-section {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 20rpx;
  background-color: #f8f8f8;
  border-radius: 8rpx;
}

.reply-input {
  flex: 1;
  padding: 16rpx 20rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 24rpx;
  font-size: 28rpx;
  background-color: white;
}

.reply-input:focus {
  border-color: #07c160;
}

.send-btn {
  padding: 16rpx 24rpx;
  background-color: #07c160;
  color: white;
  border-radius: 24rpx;
  font-size: 26rpx;
  border: none;
  transition: all 0.2s ease;
}

.send-btn::after {
  border: none;
}

.send-btn:active {
  background-color: #05a850;
}

.send-btn[disabled] {
  background-color: #ccc;
  color: #999;
}

/* 箭头旋转动画 */
.arrow-icon.rotate {
  transform: rotate(90deg);
  transition: transform 0.3s ease;
}

.arrow-icon {
  transition: transform 0.3s ease;
} /* =====
=============== 投诉建议样式 ==================== */

/* 投诉建议面板 */
.suggestions-panel {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease-in-out;
  background-color: #f8f8f8;
  border-radius: 0 0 12rpx 12rpx;
}

.suggestions-panel.show {
  max-height: 70vh; /* 限制最大高度为视窗高度的70% */
  background-color: #f8f8f8;
  border-radius: 0 0 12rpx 12rpx;
}

/* 投诉建议滚动视图 */
.suggestions-scroll-view {
  height: 100%;
  max-height: 70vh; /* 限制滚动视图的最大高度 */
}

/* 建议区块 */
.suggestion-section {
  margin: 20rpx;
  padding: 24rpx;
  background-color: #ffffff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.suggestion-section:first-child {
  margin-top: 30rpx;
}

.suggestion-section:last-child {
  margin-bottom: 30rpx;
}

/* 区块标题 */
.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.section-icon {
  width: 36rpx;
  height: 36rpx;
  margin-right: 12rpx;
}

.unread-count {
  margin-left: 12rpx;
  padding: 4rpx 12rpx;
  background-color: #ff3b30;
  color: white;
  font-size: 20rpx;
  border-radius: 12rpx;
  min-width: 32rpx;
  text-align: center;
}

/* 联系方式按钮 */
.contact-buttons {
  display: flex;
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.contact-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
  border: none;
}

.phone-btn {
  background-color: #e8f5e8; /* 非常淡的绿色背景 */
  color: #4a7c59; /* 深绿色文字 */
}

.wechat-btn {
  background-color: #e8f4e8; /* 非常淡的绿色背景 */
  color: #4a7c59; /* 深绿色文字 */
}

.btn-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 12rpx;
}

/* 建议表单 */
.suggestion-form {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.suggestion-input {
  min-height: 200rpx;
  padding: 20rpx;
  border: 2rpx solid #eee;
  border-radius: 12rpx;
  font-size: 28rpx;
  line-height: 1.5;
  background-color: #fafafa;
}

.suggestion-input:focus {
  border-color: #007aff;
  background-color: white;
}

/* 上传区域 */
.upload-section {
  display: flex;
  gap: 20rpx;
}

.upload-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx;
  border: 2rpx dashed #ddd;
  border-radius: 12rpx;
  background-color: #fafafa;
  font-size: 26rpx;
  color: #666;
}

.upload-btn:active {
  background-color: #f0f0f0;
}

/* 已上传文件预览 */
.uploaded-files {
  margin-top: 20rpx;
}

.uploaded-images,
.uploaded-videos {
  display: flex;
  flex-wrap: wrap;
  gap: 15rpx;
  margin-bottom: 20rpx;
}

.uploaded-item {
  position: relative;
  width: 150rpx;
  height: 150rpx;
  border-radius: 12rpx;
  overflow: hidden;
}

.uploaded-image,
.uploaded-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.remove-btn {
  position: absolute;
  top: -10rpx;
  right: -10rpx;
  width: 40rpx;
  height: 40rpx;
  background-color: #ff3b30;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: bold;
}

/* 联系信息 */
.contact-info {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.phone-input {
  padding: 20rpx;
  border: 2rpx solid #eee;
  border-radius: 12rpx;
  font-size: 28rpx;
  background-color: #fafafa;
}

.phone-input:focus {
  border-color: #007aff;
  background-color: white;
}

.anonymous-option {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.anonymous-text {
  font-size: 28rpx;
  color: #333;
}

/* 提交按钮 */
.submit-btn {
  padding: 24rpx;
  background-color: #007aff;
  color: white;
  border-radius: 12rpx;
  font-size: 30rpx;
  font-weight: 500;
  border: none;
  margin-top: 20rpx;
}

.submit-btn[disabled] {
  background-color: #ccc;
  color: #999;
}

/* 对话记录 */
.conversation-list {
  max-height: 400rpx;
  overflow-y: auto;
  padding: 20rpx;
  background-color: #f8f8f8;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
}

.empty-conversation {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60rpx 20rpx;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

.message-item {
  margin-bottom: 30rpx;
}

.message {
  display: flex;
  align-items: flex-start;
  max-width: 80%;
}

.user-message {
  align-self: flex-end;
  flex-direction: row-reverse;
}

.admin-message {
  align-self: flex-start;
}

.message-avatar {
  width: 60rpx;
  height: 60rpx;
  margin: 0 15rpx;
}

.message-avatar image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}

.message-bubble {
  background: #f0f0f0;
  padding: 20rpx;
  border-radius: 16rpx;
  max-width: 100%;
}

.user-message .message-bubble {
  background: #007aff;
  color: white;
}

.message-content {
  font-size: 28rpx;
  line-height: 1.5;
  margin-bottom: 10rpx;
}

.message-images {
  display: flex;
  flex-wrap: wrap;
  gap: 10rpx;
  margin-bottom: 10rpx;
}

.message-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
}

.message-time {
  font-size: 22rpx;
  opacity: 0.7;
}

/* 回复输入区域 */
.reply-input-section {
  display: flex;
  gap: 20rpx;
  align-items: flex-end;
}

.reply-input {
  flex: 1;
  padding: 20rpx;
  border: 2rpx solid #eee;
  border-radius: 12rpx;
  font-size: 28rpx;
  background-color: #fafafa;
  min-height: 80rpx;
}

.reply-input:focus {
  border-color: #007aff;
  background-color: white;
}

.send-btn {
  padding: 20rpx 30rpx;
  background-color: #007aff;
  color: white;
  border-radius: 12rpx;
  font-size: 28rpx;
  border: none;
  height: 80rpx;
}

.send-btn[disabled] {
  background-color: #ccc;
  color: #999;
}

/* ==================== 投诉建议悬浮弹窗样式 ==================== */

/* 弹窗遮罩 */
.suggestions-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  display: flex;
  align-items: flex-end; /* 改为底部对齐，让弹窗从底部弹出 */
  justify-content: center;
  width: 100%;
}

.suggestions-modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  animation: fadeIn 0.3s ease-out;
}

/* 弹窗内容 */
.suggestions-modal-content {
  position: relative;
  width: 100%;
  height: calc(100vh - 180rpx); /* 固定高度，从胶囊按钮下方开始 */
  background: white;
  border-radius: 20rpx;
  display: flex;
  flex-direction: column;
  transform: translateY(100%); /* 保持从底部滑入的动画 */
  transition: transform 0.3s ease-out;
  margin: 0;
}

.suggestions-modal-content.show {
  transform: translateY(0); /* 显示时移动到正常位置 */
}

/* 添加关闭动画效果 */
.suggestions-modal.closing .suggestions-modal-content {
  transform: translateY(100%); /* 关闭时向下滑出 */
}

/* 弹窗头部 */
.suggestions-modal-header {
  display: flex;
  justify-content: center; /* 标题居中 */
  align-items: center;
  padding: 30rpx 40rpx;
  border-bottom: 1rpx solid #eee;
  background: white;
  border-radius: 20rpx 20rpx 0 0;
  position: relative; /* 为关闭按钮的绝对定位提供参考 */
}

.suggestions-modal-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.suggestions-modal-close {
  position: absolute; /* 绝对定位 */
  right: 40rpx; /* 距离右边40rpx */
  top: 50%; /* 垂直居中 */
  transform: translateY(-50%); /* 垂直居中调整 */
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  color: #999;
  background: #f5f5f5;
  border-radius: 50%;
}

/* 弹窗主体内容 */
.suggestions-modal-body {
  flex: 1;
  overflow-y: auto;
  padding: 8rpx 0; /* 减少上下内边距 */
}

/* ==================== 聊天界面样式 ==================== */

/* 聊天容器 */
.suggestions-modal .chat-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
  position: relative;
  height: calc(100% - 140rpx); /* 减去输入框的可见高度 */
  overflow: hidden;
}

/* 聊天消息区域 */
.suggestions-modal .chat-messages {
  flex: 1;
  padding: 0 24rpx;
  position: relative;
  height: 100%;
}

/* 空状态 */
.suggestions-modal .empty-chat-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 40rpx;
  text-align: center; /* 确保文本居中 */
  position: absolute; /* 使用绝对定位确保在容器中居中 */
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%); /* 使用transform居中 */
  width: 80%; /* 控制宽度 */
}

.suggestions-modal .empty-chat-container .empty-icon {
  width: 180rpx;
  height: 180rpx;
  margin-bottom: 30rpx;
  opacity: 0.5;
}

.suggestions-modal .empty-chat-container .empty-text-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.suggestions-modal .empty-chat-container .empty-text {
  font-size: 32rpx;
  font-weight: 500;
  color: #666;
  line-height: 1.8; /* 增加行高使两行文本间距更合适 */
  text-align: center; /* 确保文本居中 */
  max-width: 100%; /* 确保文本不溢出容器 */
  padding: 0 20rpx; /* 添加内边距 */
  display: block; /* 确保每行文本独立显示 */
}

/* 消息列表 */
.suggestions-modal .message-list {
  padding: 16rpx 0;
}

/* 消息项 */
.suggestions-modal .message-item {
  display: flex;
  margin-bottom: 24rpx;
  padding: 0 8rpx;
}

/* 最后一条消息增加底部间距，避免被输入框遮挡 */
.suggestions-modal .message-item:last-child {
  margin-bottom: 165rpx; /* 增加5rpx，让最后一条消息与输入框有更舒适的距离 */
}

.suggestions-modal .user-message {
  justify-content: flex-start;
  flex-direction: row-reverse;
}

.suggestions-modal .admin-message {
  justify-content: flex-start;
}

.suggestions-modal .message-avatar-container {
  margin: 0 16rpx;
}

/* 大幅度调整管理员消息（左侧）的头像容器边距，向左移动 */
.suggestions-modal .admin-message .message-avatar-container {
  margin-left: -22rpx; /* 使用负边距向左移动 */
}

.suggestions-modal .message-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 8rpx;
}

.suggestions-modal .user-message .message-avatar {
  border-radius: 50%;
}

.suggestions-modal .message-content {
  max-width: 70%;
  display: flex;
  flex-direction: column;
  position: relative;
}

.suggestions-modal .user-message .message-content {
  max-width: 65%;
}

.suggestions-modal .message-bubble {
  padding: 20rpx 24rpx;
  border-radius: 12rpx;
  background-color: #ffffff;
  margin-bottom: 8rpx;
  box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.1);
  position: relative;
}

.suggestions-modal .user-message .message-bubble {
  border-bottom-right-radius: 4rpx;
  background-color: #007AFF;
}

.suggestions-modal .admin-message .message-bubble {
  border-bottom-left-radius: 4rpx;
  background-color: #ffffff;
}

.suggestions-modal .message-text {
  font-size: 30rpx;
  line-height: 1.5;
  color: #333;
  word-break: break-word;
}

.suggestions-modal .user-message .message-text {
  color: #ffffff;
}

.suggestions-modal .message-time {
  font-size: 24rpx;
  color: #999;
  align-self: flex-end;
  margin-top: 4rpx;
}

/* 媒体文件样式 */
.suggestions-modal .message-images {
  display: flex;
  flex-wrap: wrap;
  margin-top: 8rpx;
  gap: 8rpx;
  background-color: transparent;
  border-radius: 8rpx;
  overflow: hidden;
}

.suggestions-modal .message-image {
  width: 240rpx;
  height: 240rpx;
  border-radius: 8rpx;
  object-fit: cover;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
}

.suggestions-modal .message-videos {
  margin-top: 8rpx;
}

.suggestions-modal .message-video {
  width: 100%;
  max-width: 400rpx;
  border-radius: 8rpx;
  margin-top: 8rpx;
}

/* 输入区域 */
.suggestions-modal .chat-input-area {
  display: flex;
  align-items: center;
  padding: 16rpx 24rpx 84rpx 24rpx; /* 底部增加填充，向下延伸 */
  background-color: #ffffff;
  border-top: 1rpx solid #eeeeee;
  min-height: 88rpx;
  width: 100%;
  box-sizing: border-box;
  position: fixed;
  bottom: 0; /* 恢复到底部 */
  left: 0;
  right: 0;
  z-index: 1000;
}

/* 媒体选择按钮 */
.suggestions-modal .media-btn {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background-color: transparent;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16rpx;
  transition: background-color 0.2s;
  position: relative;
}

.suggestions-modal .media-btn:active {
  background-color: rgba(0, 0, 0, 0.05);
}

.suggestions-modal .media-icon {
  font-size: 40rpx;
  line-height: 1;
  color: #666;
  font-weight: 300;
  text-align: center;
}

.suggestions-modal .input-container {
  flex: 1;
  background-color: #f5f5f5;
  border-radius: 36rpx;
  padding: 12rpx 24rpx;
  margin-right: 16rpx;
  overflow: hidden;
}

.suggestions-modal .message-input {
  width: 100%;
  min-height: 60rpx;
  max-height: 240rpx;
  font-size: 30rpx;
  line-height: 1.5;
  color: #333;
  padding: 0 12rpx;
  text-align: left;
}

.suggestions-modal .message-input::placeholder {
  color: #999;
  text-align: left;
}

.suggestions-modal .send-btn {
  min-width: 120rpx;
  height: 72rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #cccccc !important;
  color: #999999 !important;
  border-radius: 36rpx;
  font-size: 28rpx;
  padding: 0 30rpx;
  transition: all 0.2s ease;
}

.suggestions-modal .send-btn.active {
  background-color: #007aff !important; /* 蓝色 */
  color: #ffffff !important;
  box-shadow: 0 2rpx 6rpx rgba(0, 122, 255, 0.3);
}

/* 登录弹窗相关样式 */
.login-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center; /* 垂直居中 */
  justify-content: center; /* 水平居中 */
}

.login-modal .modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.75);
  backdrop-filter: blur(5px);
  z-index: 1001;
}

.login-modal .modal-content {
  width: 80%; /* 宽度为屏幕的80% */
  max-width: 600rpx; /* 设置最大宽度 */
  background-color: #1a1a1a;
  border-radius: 20rpx; /* 四周都是圆角 */
  overflow: hidden;
  position: relative;
  z-index: 1002;
  box-shadow: 0 0 30rpx rgba(0, 0, 0, 0.5); /* 四周阴影 */
  display: flex;
  flex-direction: column;
  max-height: 60vh;
  transform: none; /* 移除任何可能的transform */
  margin: 0; /* 移除可能的外边距 */
  animation: fadeIn 0.3s ease-out; /* 使用淡入动画 */
}

/* 添加淡入动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 登录弹窗头部样式 */
.login-modal .modal-header {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx 30rpx 30rpx;
  border-bottom: 1rpx solid #2a2a2a;
  position: relative;
}

.login-modal .modal-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #ffffff;
  letter-spacing: 1rpx;
  padding-top: 16rpx;
  text-align: center;
}

.login-modal .modal-close {
  font-size: 42rpx;
  color: #999999;
  padding: 10rpx 15rpx;
  height: 44rpx;
  width: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  position: absolute;
  right: 20rpx;
  top: 40rpx;
}

.login-modal .modal-close:active {
  background-color: rgba(255, 255, 255, 0.15);
}

/* 登录弹窗主体样式 */
.login-modal .modal-body {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30rpx 40rpx 20rpx;
}

/* 登录弹窗提示文字样式 */
.login-modal .login-tip {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin: 20rpx 0 40rpx;
  text-align: center;
  width: 100%;
}

.login-modal .login-tip-text {
  font-size: 32rpx;
  color: #ffffff;
  font-weight: 500;
  text-align: center;
  margin-bottom: 20rpx;
  padding: 0 20rpx;
}

/* 登录按钮样式 */
.login-btn {
  width: 80%;
  height: 90rpx;
  line-height: 90rpx;
  background-color: #07c160; /* 微信官方绿色 */
  color: #ffffff;
  font-size: 32rpx;
  font-weight: 500;
  border-radius: 45rpx;
  text-align: center;
  margin: 20rpx auto 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(7, 193, 96, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  border: none;
}



























