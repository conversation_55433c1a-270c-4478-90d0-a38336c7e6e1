const app = getApp();

Page({
  data: {
    isLoading: true,
    galleryList: [],
    currentPage: 1,
    pageSize: 10,
    totalItems: 0,
    hasMore: false,
    uploadModalVisible: false,
    currentGalleryInfo: null
  },

  onLoad: function (options) {
    wx.setNavigationBarTitle({
      title: '画廊管理'
    });
    this.loadGalleryList();
  },

  onShow: function () {
    // 页面显示时刷新数据
    if (!this.data.isLoading) {
      this.refreshList();
    }
  },

  // 加载画廊列表
  loadGalleryList: function (page = 1) {
    this.setData({
      isLoading: true,
      currentPage: page
    });

    console.log('开始加载画廊列表，页码:', page);
    wx.cloud.callFunction({
      name: 'galleryManager',
      data: {
        action: 'getGalleryList',
        data: {
          page: page,
          pageSize: this.data.pageSize
        }
      }
    }).then(res => {
      console.log('获取画廊列表结果:', res.result);
      const result = res.result;
      if (result.success) {
        const { list, total } = result.data;
        
        // 处理日期数据格式，确保可以在前端正确显示
        const processedList = list.map(item => {
          const processedItem = { ...item };
          
          // 格式化日期字段为可读字符串
          if (processedItem.visibilityChangeTime) {
            if (typeof processedItem.visibilityChangeTime === 'string') {
              processedItem.visibilityChangeTimeStr = new Date(processedItem.visibilityChangeTime).toLocaleString();
            } else if (processedItem.visibilityChangeTime['$date']) {
              processedItem.visibilityChangeTimeStr = new Date(processedItem.visibilityChangeTime['$date']).toLocaleString();
            }
          }
          
          if (processedItem.updateTime) {
            if (typeof processedItem.updateTime === 'string') {
              processedItem.updateTimeStr = new Date(processedItem.updateTime).toLocaleString();
            } else if (processedItem.updateTime['$date']) {
              processedItem.updateTimeStr = new Date(processedItem.updateTime['$date']).toLocaleString();
            }
          }
          
          if (processedItem.createTime) {
            if (typeof processedItem.createTime === 'string') {
              processedItem.createTimeStr = new Date(processedItem.createTime).toLocaleString();
            } else if (processedItem.createTime['$date']) {
              processedItem.createTimeStr = new Date(processedItem.createTime['$date']).toLocaleString();
            }
          }
          
          return processedItem;
        });
        
        // 如果是第一页，直接设置列表数据
        // 如果是加载更多，则追加数据
        const galleryList = page === 1 ? processedList : this.data.galleryList.concat(processedList);
        
        this.setData({
          galleryList: galleryList,
          totalItems: total,
          hasMore: galleryList.length < total,
          isLoading: false
        });
        
        // 打印首个项目的时间信息用于调试
        if (galleryList.length > 0) {
          const firstItem = galleryList[0];
          console.log('首个列表项的时间信息:', {
            visibilityChangeTime: firstItem.visibilityChangeTime,
            updateTime: firstItem.updateTime,
            createTime: firstItem.createTime
          });
        }
      } else {
        this.showToast('获取画廊列表失败：' + result.message);
        this.setData({
          isLoading: false
        });
      }
    }).catch(err => {
      console.error('加载画廊列表失败', err);
      this.showToast('加载画廊列表失败，请稍后重试');
      this.setData({
        isLoading: false
      });
    });
  },

  // 刷新列表
  refreshList: function () {
    console.log('刷新画廊列表');
    this.setData({
      currentPage: 1,
      galleryList: []
    });
    this.loadGalleryList(1);
  },

  // 加载更多
  loadMore: function () {
    if (this.data.hasMore && !this.data.isLoading) {
      this.loadGalleryList(this.data.currentPage + 1);
    }
  },

  // 添加新画廊项目
  addNewGallery: function () {
    this.setData({
      currentGalleryInfo: null,
      uploadModalVisible: true
    });
  },

  // 编辑画廊项目
  editGallery: function (e) {
    const galleryId = e.currentTarget.dataset.id;
    const gallery = this.data.galleryList.find(item => item._id === galleryId);
    
    if (gallery) {
      console.log('准备编辑画廊:', gallery);
      
      // 设置画廊信息，同时设置editMode为true
      this.setData({
        currentGalleryInfo: gallery,
        uploadModalVisible: true
      });
    } else {
      this.showToast('画廊项目不存在');
    }
  },

  // 查看画廊项目详情
  viewGalleryDetail: function (e) {
    const galleryId = e.currentTarget.dataset.id;
    const gallery = this.data.galleryList.find(item => item._id === galleryId);
    const galleryDetailModal = this.selectComponent('#galleryDetailModal');
    
    if (galleryDetailModal && gallery) {
      console.log('查看画廊详情:', gallery);
      galleryDetailModal.showModal({
        id: gallery._id,
        mainTitle: gallery.mainTitle,
        subTitle: gallery.subTitle,
        coverImage: gallery.coverUrl,
        authorAvatar: '/static/logo.png',
        author: '今禧美学',
        description: gallery.description || '',
        detailImages: gallery.detailImages || []
      });
    } else if (!gallery) {
      this.showToast('未找到画廊项目');
    } else if (!galleryDetailModal) {
      this.showToast('详情组件未加载');
    }
  },

  // 切换画廊项目的显示状态
  toggleGalleryVisibility(e) {
    const galleryId = e.currentTarget.dataset.id;
    const currentVisibility = e.currentTarget.dataset.visible;
    const newVisibility = !currentVisibility;
    
    wx.showLoading({
      title: newVisibility ? '正在显示...' : '正在隐藏...',
      mask: true
    });
    
    // 准备请求数据
    const updateData = {
      isVisible: newVisibility,
      visibilityChangeTime: new Date() // 添加显示状态变更时间
    };
    
    // 调用云函数更新显示状态
    wx.cloud.callFunction({
      name: 'galleryManager',
      data: {
        action: 'updateGallery',
        data: {
          id: galleryId,
          updateData: updateData
        }
      }
    }).then(res => {
      wx.hideLoading();
      
      if (res.result && res.result.success) {
        // 操作成功
        wx.showToast({
          title: newVisibility ? '已显示' : '已隐藏',
          icon: 'success',
          duration: 1500
        });
        
        // 刷新列表
        this.refreshList();
      } else {
        // 操作失败
        wx.showToast({
          title: res.result && res.result.message ? res.result.message : '操作失败',
          icon: 'none',
          duration: 2000
        });
      }
    }).catch(err => {
      console.error('切换显示状态失败:', err);
      wx.hideLoading();
      wx.showToast({
        title: '操作失败，请重试',
        icon: 'none',
        duration: 2000
      });
    });
  },

  // 删除画廊项目
  deleteGallery: function (e) {
    const galleryId = e.currentTarget.dataset.id;
    
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这个画廊项目吗？此操作不可恢复。',
      confirmColor: '#FF3B30',
      success: (res) => {
        if (res.confirm) {
          this.performDeleteGallery(galleryId);
        }
      }
    });
  },

  // 执行删除操作
  performDeleteGallery: function (galleryId) {
    wx.showLoading({
      title: '删除中...',
      mask: true
    });

    wx.cloud.callFunction({
      name: 'galleryManager',
      data: {
        action: 'deleteGalleryItem',
        data: {
          _id: galleryId
        }
      }
    }).then(res => {
      wx.hideLoading();
      const result = res.result;
      
      if (result.success) {
        // 从列表中移除已删除项
        const galleryList = this.data.galleryList.filter(item => item._id !== galleryId);
        
        this.setData({
          galleryList: galleryList,
          totalItems: this.data.totalItems - 1
        });
        
        this.showToast('删除成功');
      } else {
        this.showToast('删除失败：' + result.message);
      }
    }).catch(err => {
      wx.hideLoading();
      console.error('删除画廊项目失败', err);
      this.showToast('删除失败，请稍后重试');
    });
  },

  // 关闭上传/编辑模态框
  closeUploadModal: function () {
    this.setData({
      uploadModalVisible: false
    });
  },

  // 上传/编辑成功回调
  onUploadSuccess: function (e) {
    this.setData({
      uploadModalVisible: false
    });
    
    // 刷新列表
    this.refreshList();
    
    // 显示成功提示
    this.showToast(e.detail.isEdit ? '更新成功' : '上传成功');
  },

  // 返回上一页
  navigateBack: function () {
    wx.navigateBack({
      delta: 1
    });
  },

  // 显示提示信息
  showToast: function (message, icon = 'none') {
    wx.showToast({
      title: message,
      icon: icon,
      duration: 2000
    });
  },

  // 处理下拉刷新
  onPullDownRefresh: function () {
    this.refreshList();
    wx.stopPullDownRefresh();
  },

  // 处理页面上拉触底事件
  onReachBottom: function () {
    this.loadMore();
  }
}); 