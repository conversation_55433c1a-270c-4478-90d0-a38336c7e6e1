const app = getApp()

Page({
  data: {
    contentList: [],
    isLoading: true,
    currentTab: 'gallery' // 默认选中gallery内容
  },

  onLoad() {
    // 检查管理员登录状态
    if (!app.globalData.isAdmin) {
      this.redirectToLogin()
      return
    }
    
    // 加载内容数据
    this.loadContentData()
  },
  
  onShow() {
    // 每次显示页面时检查管理员状态
    if (!app.globalData.isAdmin) {
      this.redirectToLogin()
      return
    }
  },
  
  // 重定向到登录页
  redirectToLogin() {
    wx.showToast({
      title: '请先登录',
      icon: 'none'
    })
    
    setTimeout(() => {
      wx.navigateTo({
        url: '/pages/admin/login'
      })
    }, 1500)
  },
  
  // 切换内容类型标签
  switchTab(e) {
    const tab = e.currentTarget.dataset.tab
    
    if (tab !== this.data.currentTab) {
      this.setData({
        currentTab: tab,
        isLoading: true,
        contentList: []
      }, () => {
        this.loadContentData()
      })
    }
  },
  
  // 加载内容数据
  loadContentData() {
    const { currentTab } = this.data
    
    // 显示加载状态
    this.setData({ isLoading: true })
    
    // 根据当前标签加载不同类型的内容
    if (currentTab === 'gallery') {
      this.loadGalleryContent()
    } else if (currentTab === 'banner') {
      this.loadBannerContent()
    } else if (currentTab === 'notice') {
      this.loadNoticeContent()
    }
  },
  
  // 加载画廊内容
  loadGalleryContent() {
    // 这里调用云函数获取内容数据
    wx.cloud.callFunction({
      name: 'getGalleryContent',
      success: res => {
        console.log('获取画廊内容成功:', res)
        
        // 处理内容数据
        if (res.result && res.result.data) {
          this.setData({
            contentList: res.result.data,
            isLoading: false
          })
        } else {
          this.setData({
            contentList: [],
            isLoading: false
          })
        }
      },
      fail: err => {
        console.error('获取画廊内容失败:', err)
        // 开发阶段使用模拟数据
        this.setData({
          contentList: this.getMockGalleryData(),
          isLoading: false
        })
      }
    })
  },
  
  // 加载Banner内容
  loadBannerContent() {
    // 临时使用模拟数据
    setTimeout(() => {
      this.setData({
        contentList: this.getMockBannerData(),
        isLoading: false
      })
    }, 500)
  },
  
  // 加载公告内容
  loadNoticeContent() {
    // 临时使用模拟数据
    setTimeout(() => {
      this.setData({
        contentList: this.getMockNoticeData(),
        isLoading: false
      })
    }, 500)
  },
  
  // 添加新内容
  addNewContent() {
    wx.navigateTo({
      url: `/pages/admin/content/edit/edit?type=${this.data.currentTab}`
    })
  },
  
  // 编辑内容
  editContent(e) {
    const { id } = e.currentTarget.dataset
    wx.navigateTo({
      url: `/pages/admin/content/edit/edit?type=${this.data.currentTab}&id=${id}`
    })
  },
  
  // 删除内容
  deleteContent(e) {
    const { id, title } = e.currentTarget.dataset
    
    wx.showModal({
      title: '确认删除',
      content: `确定要删除"${title}"吗？`,
      success: (res) => {
        if (res.confirm) {
          // 调用删除API
          wx.showLoading({
            title: '删除中...'
          })
          
          // 模拟删除操作
          setTimeout(() => {
            wx.hideLoading()
            
            // 更新列表（从当前列表中移除已删除项）
            const newList = this.data.contentList.filter(item => item._id !== id)
            this.setData({
              contentList: newList
            })
            
            wx.showToast({
              title: '删除成功',
              icon: 'success'
            })
          }, 1000)
        }
      }
    })
  },
  
  // 返回管理主页
  navigateBack() {
    wx.navigateBack()
  },
  
  // 模拟画廊数据
  getMockGalleryData() {
    return [
      {
        _id: 'gallery1',
        title: '作品集合一',
        coverUrl: '/static/images/gallery1.jpg',
        type: 'gallery',
        createTime: '2023-12-01',
        updateTime: '2023-12-20',
        status: 1
      },
      {
        _id: 'gallery2',
        title: '作品集合二',
        coverUrl: '/static/images/gallery2.jpg',
        type: 'gallery',
        createTime: '2023-11-15',
        updateTime: '2023-12-18',
        status: 1
      },
      {
        _id: 'gallery3',
        title: '美甲展示专辑',
        coverUrl: '/static/images/gallery3.jpg',
        type: 'gallery',
        createTime: '2023-10-20',
        updateTime: '2023-12-15',
        status: 1
      }
    ]
  },
  
  // 模拟Banner数据
  getMockBannerData() {
    return [
      {
        _id: 'banner1',
        title: '新春优惠活动',
        imageUrl: '/static/images/banner1.jpg',
        linkUrl: '/pages/promotion/detail?id=1',
        type: 'banner',
        createTime: '2023-12-10',
        updateTime: '2023-12-20',
        status: 1
      },
      {
        _id: 'banner2',
        title: '美甲新品上市',
        imageUrl: '/static/images/banner2.jpg',
        linkUrl: '/pages/product/detail?id=2',
        type: 'banner',
        createTime: '2023-11-20',
        updateTime: '2023-12-15',
        status: 1
      }
    ]
  },
  
  // 模拟公告数据
  getMockNoticeData() {
    return [
      {
        _id: 'notice1',
        title: '店铺营业时间调整通知',
        content: '尊敬的顾客，本店从2024年1月1日起，营业时间调整为10:00-22:00，感谢您的支持！',
        type: 'notice',
        createTime: '2023-12-20',
        updateTime: '2023-12-20',
        status: 1
      },
      {
        _id: 'notice2',
        title: '会员积分活动',
        content: '即日起至2024年2月底，会员消费每满100元赠送20积分，积分可兑换多种礼品！',
        type: 'notice',
        createTime: '2023-12-15',
        updateTime: '2023-12-15',
        status: 1
      }
    ]
  }
}) 