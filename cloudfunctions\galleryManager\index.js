// 云函数入口文件
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

// 获取数据库引用，使用管理员权限
const db = cloud.database({ env: cloud.DYNAMIC_CURRENT_ENV });
const _ = db.command;
const $ = db.command.aggregate;

// 常量定义
const COLLECTION_NAME = 'gallery';
const CATEGORY_COLLECTION = 'galleryCategories'; // 新增分类集合名称
const MAX_LIMIT = 100;

// 检查并创建集合及字段
async function ensureCollectionAndFields(adminDB) {
  try {
    // 确保画廊集合存在
    await adminDB.createCollection(COLLECTION_NAME).catch(() => {});
    
    // 确保分类集合存在
    await adminDB.createCollection(CATEGORY_COLLECTION).catch(() => {});
    
    // 创建索引
    try {
      await adminDB.collection(COLLECTION_NAME).createIndex({
        isVisible: 1,
        order: 1,
        createTime: -1
      });
      
      // 为分类集合创建索引
      await adminDB.collection(CATEGORY_COLLECTION).createIndex({
        order: 1,
        createTime: -1
      });
    } catch (err) {
      console.log('索引可能已存在:', err);
    }
    
    // 设置集合权限为所有用户可读
    try {
      await cloud.callFunction({
        name: 'configManager',
        data: {
          action: 'setCollectionPermission',
          data: {
            collectionName: COLLECTION_NAME,
            readAccess: 'EVERYONE',
            writeAccess: 'OWNER'
          }
        }
      });
      console.log('已设置gallery集合权限为所有用户可读');
    } catch (err) {
      console.error('设置集合权限失败:', err);
    }
  } catch (err) {
    console.error('确保集合存在时出错:', err);
  }
}

// 检查管理员权限
async function checkAdminPermission(context) {
  try {
    const openid = context.OPENID;
    console.log('检查管理员权限，OPENID:', openid);
    
    // 查询system_users集合
    const userResult = await db.collection('system_users')
      .where({
        openid: openid,
        isAdmin: true
      })
      .get();
    
    if (userResult.data && userResult.data.length > 0) {
      console.log('用户是管理员');
      return true;
    }
    
    console.log('用户不是管理员');
    return false;
  } catch (error) {
    console.error('检查管理员权限失败:', error);
    return false;
  }
}

// 添加画廊项目
async function addGalleryItem(data, context, adminDB) {
  try {
    // 解构数据，添加categoryId字段
    const { mainTitle, subTitle, coverUrl, detailImages, content, categoryId } = data;
    
    // 验证必填字段
    if (!mainTitle || !coverUrl) {
      return {
        success: false,
        code: 'INVALID_PARAMS',
        message: '标题和封面图不能为空'
      };
    }
    
    // 获取创建者信息
    const openId = context && context.OPENID ? context.OPENID : '';
    
    // 添加新画廊项目
    const result = await adminDB.collection(COLLECTION_NAME).add({
      data: {
        mainTitle,
        subTitle,
        coverUrl,
        detailImages: detailImages || [],
        content: content || '',
        categoryId: categoryId || '', // 添加分类ID字段
        isVisible: true, // 默认可见
        order: 0, // 默认排序值
        creatorId: openId, // 添加创建者ID
        createTime: db.serverDate(),
        updateTime: db.serverDate()
      }
    });
    
    return {
      success: true,
      data: {
        _id: result._id
      },
      message: '添加画廊项目成功'
    };
  } catch (err) {
    console.error('添加画廊项目失败:', err);
    return {
      success: false,
      code: 'ADD_GALLERY_ITEM_FAILED',
      message: '添加画廊项目失败:' + err.message
    };
  }
}

// 更新画廊项目
async function updateGalleryItem(data, context, adminDB) {
  try {
    // 解构数据，添加更多支持的字段
    const { id, _id, mainTitle, subTitle, coverUrl, detailImages, content, order, categoryId, isVisible, updateData } = data;
    
    // 获取真正的ID (支持id或_id参数)
    const galleryId = id || _id;
    
    if (!galleryId) {
      return {
        success: false,
        code: 'INVALID_PARAMS',
        message: '画廊项目ID不能为空'
      };
    }
    
    // 检查画廊项目是否存在
    const gallery = await adminDB.collection(COLLECTION_NAME).doc(galleryId).get();
    if (!gallery.data) {
      return {
        success: false,
        code: 'GALLERY_ITEM_NOT_FOUND',
        message: '画廊项目不存在'
      };
    }
    
    // 获取更新者信息
    const openId = context && context.OPENID ? context.OPENID : '';
    
    // 构建更新数据
    let finalUpdateData;
    
    // 如果提供了updateData对象，直接使用它
    if (updateData) {
      finalUpdateData = { ...updateData };
      
      // 添加更新时间和更新者
      finalUpdateData.updateTime = db.serverDate();
      finalUpdateData.lastUpdatedBy = openId;
      
      // 如果更新了可见性，添加可见性变更时间
      if (finalUpdateData.isVisible !== undefined) {
        finalUpdateData.visibilityChangeTime = db.serverDate();
      }
    } else {
      // 传统方式构建更新数据
      finalUpdateData = {
        updateTime: db.serverDate(),
        lastUpdatedBy: openId // 记录最后更新者
      };
      
      if (mainTitle !== undefined) finalUpdateData.mainTitle = mainTitle;
      if (subTitle !== undefined) finalUpdateData.subTitle = subTitle;
      if (coverUrl !== undefined) finalUpdateData.coverUrl = coverUrl;
      if (detailImages !== undefined) finalUpdateData.detailImages = detailImages;
      if (content !== undefined) finalUpdateData.content = content;
      if (order !== undefined) finalUpdateData.order = order;
      if (categoryId !== undefined) finalUpdateData.categoryId = categoryId; // 更新分类ID
      
      // 如果更新了可见性，添加可见性变更时间
      if (isVisible !== undefined) {
        finalUpdateData.isVisible = isVisible;
        finalUpdateData.visibilityChangeTime = db.serverDate();
      }
    }
    
    // 更新画廊项目
    await adminDB.collection(COLLECTION_NAME).doc(galleryId).update({
      data: finalUpdateData
    });
    
    return {
      success: true,
      message: '更新画廊项目成功'
    };
  } catch (err) {
    console.error('更新画廊项目失败:', err);
    return {
      success: false,
      code: 'UPDATE_GALLERY_ITEM_FAILED',
      message: '更新画廊项目失败:' + err.message
    };
  }
}

// 删除画廊项目
async function deleteGalleryItem(data, context, adminDB) {
  try {
    if (!data._id) {
      return {
        success: false,
        code: 'MISSING_ID',
        message: '缺少画廊项目ID'
      };
    }

    // 检查项目是否存在
    const item = await adminDB.collection(COLLECTION_NAME).doc(data._id).get();
    if (!item.data) {
      return {
        success: false,
        code: 'GALLERY_ITEM_NOT_FOUND',
        message: '画廊项目不存在'
      };
    }

    // 获取操作者信息（仅记录日志用）
    const openId = context && context.OPENID ? context.OPENID : '';
    
    // 记录删除日志（可选）
    try {
      await adminDB.collection('operation_logs').add({
        data: {
          operation: 'delete_gallery',
          targetId: data._id,
          operatorId: openId,
          itemData: item.data, // 保存被删除的数据，便于恢复
          operationTime: db.serverDate()
        }
      });
    } catch (logErr) {
      console.error('记录删除日志失败，但继续执行删除操作:', logErr);
    }

    // 删除数据
    await adminDB.collection(COLLECTION_NAME).doc(data._id).remove();

    return {
      success: true,
      message: '删除画廊项目成功'
    };
  } catch (err) {
    console.error('删除画廊项目失败', err);
    return {
      success: false,
      code: 'DELETE_GALLERY_ITEM_FAILED',
      message: '删除画廊项目失败：' + err.message
    };
  }
}

// 获取画廊列表
async function getGalleryList(data, adminDB) {
  try {
    const { page = 1, pageSize = 10, isVisible } = data;
    const skip = (page - 1) * pageSize;
    
    // 构建查询条件
    const query = {};
    if (isVisible !== undefined) {
      query.isVisible = isVisible;
    }

    console.log('管理后台获取画廊列表，查询条件:', JSON.stringify(query));

    // 获取总数
    const countResult = await adminDB.collection(COLLECTION_NAME)
      .where(query)
      .count();
    const total = countResult.total;

    console.log('管理后台符合条件的画廊总数:', total);

    // 获取数据列表 - 更新排序逻辑，与前端保持一致
    // 按照优先级：visibilityChangeTime > updateTime > createTime > order
    const listResult = await adminDB.collection(COLLECTION_NAME)
      .where(query)
      .orderBy('visibilityChangeTime', 'desc') // 首先按显示状态变更时间降序排序
      .orderBy('updateTime', 'desc') // 然后按更新时间降序排序
      .orderBy('createTime', 'desc') // 再按创建时间降序排序
      .orderBy('order', 'asc') // 最后按顺序号升序排序
      .skip(skip)
      .limit(pageSize)
      .get();

    console.log(`管理后台获取到${listResult.data.length}条画廊数据`);
    
    // 打印前三条数据的ID和标题，便于调试
    if (listResult.data && listResult.data.length > 0) {
      const previewData = listResult.data.slice(0, 3).map(item => ({
        _id: item._id,
        mainTitle: item.mainTitle,
        isVisible: item.isVisible,
        creatorId: item.creatorId || '未知',
        visibilityChangeTime: item.visibilityChangeTime || null,
        updateTime: item.updateTime || null,
        createTime: item.createTime || null
      }));
      console.log('管理后台前三条数据预览:', JSON.stringify(previewData));
    }
    
    return {
      success: true,
      data: {
        list: listResult.data,
        page,
        pageSize,
        total
      },
      message: '获取画廊列表成功'
    };
  } catch (err) {
    console.error('获取画廊列表失败', err);
    return {
      success: false,
      code: 'GET_GALLERY_LIST_FAILED',
      message: '获取画廊列表失败：' + err.message
    };
  }
}

// 将云存储的fileID转换为临时URL
async function convertFileIDsToURLs(items) {
  if (!items || items.length === 0) {
    return [];
  }
  
  try {
    // 收集所有需要转换的fileID
    const fileIDsToConvert = [];
    
    // 遍历所有画廊项目，收集coverUrl和detailImages中的fileID
    items.forEach(item => {
      if (item.coverUrl && item.coverUrl.startsWith('cloud://')) {
        fileIDsToConvert.push(item.coverUrl);
      }
      
      if (item.detailImages && item.detailImages.length > 0) {
        item.detailImages.forEach(imgUrl => {
          if (imgUrl && imgUrl.startsWith('cloud://')) {
            fileIDsToConvert.push(imgUrl);
          }
        });
      }
    });
    
    // 如果没有需要转换的fileID，直接返回原始数据
    if (fileIDsToConvert.length === 0) {
      return items;
    }
    
    console.log(`需要转换${fileIDsToConvert.length}个云存储fileID为临时URL`);
    
    // 调用云函数获取临时URL
    const result = await cloud.getTempFileURL({
      fileList: fileIDsToConvert
    });
    
    // 创建fileID到URL的映射
    const fileIDToURLMap = {};
    result.fileList.forEach(file => {
      fileIDToURLMap[file.fileID] = file.tempFileURL;
    });
    
    // 替换所有项目中的fileID为临时URL
    const processedItems = items.map(item => {
      const newItem = { ...item };
      
      // 替换封面图URL
      if (newItem.coverUrl && fileIDToURLMap[newItem.coverUrl]) {
        newItem.coverUrl = fileIDToURLMap[newItem.coverUrl];
      }
      
      // 替换详情图片URL
      if (newItem.detailImages && newItem.detailImages.length > 0) {
        newItem.detailImages = newItem.detailImages.map(imgUrl => {
          return fileIDToURLMap[imgUrl] || imgUrl;
        });
      }
      
      return newItem;
    });
    
    console.log('已成功转换所有fileID为临时URL');
    return processedItems;
  } catch (err) {
    console.error('转换fileID为临时URL失败:', err);
    // 出错时返回原始数据
    return items;
  }
}

// 获取画廊项目详情
async function getGalleryDetail(data, adminDB) {
  try {
    if (!data._id) {
      return {
        success: false,
        code: 'MISSING_ID',
        message: '缺少画廊项目ID'
      };
    }

    // 获取详情
    const result = await adminDB.collection(COLLECTION_NAME).doc(data._id).get();
    
    if (!result.data) {
      return {
        success: false,
        code: 'GALLERY_ITEM_NOT_FOUND',
        message: '画廊项目不存在'
      };
    }
    
    // 将fileID转换为临时URL，确保所有用户都能访问图片
    const processedItems = await convertFileIDsToURLs([result.data]);
    const processedData = processedItems[0];

    return {
      success: true,
      data: processedData,
      message: '获取画廊项目详情成功'
    };
  } catch (err) {
    console.error('获取画廊项目详情失败', err);
    return {
      success: false,
      code: 'GET_GALLERY_DETAIL_FAILED',
      message: '获取画廊项目详情失败：' + err.message
    };
  }
}

// 根据ID获取文章详情（用于分享功能）
async function getArticleById(data, adminDB) {
  try {
    if (!data.id) {
      return {
        success: false,
        code: 'MISSING_ID',
        message: '缺少文章ID'
      };
    }

    // 获取详情
    const result = await adminDB.collection(COLLECTION_NAME).doc(data.id).get();
    
    if (!result.data) {
      return {
        success: false,
        code: 'ARTICLE_NOT_FOUND',
        message: '文章不存在'
      };
    }
    
    // 将fileID转换为临时URL，确保所有用户都能访问图片
    const processedItems = await convertFileIDsToURLs([result.data]);
    const processedData = processedItems[0];

    return {
      success: true,
      data: processedData,
      message: '获取文章详情成功'
    };
  } catch (err) {
    console.error('获取文章详情失败:', err);
    return {
      success: false,
      code: 'GET_ARTICLE_BY_ID_FAILED',
      message: '获取文章详情失败：' + err.message
    };
  }
}

// 切换画廊项目可见状态
async function toggleGalleryVisibility(data, context, adminDB) {
  try {
    if (!data._id) {
      return {
        success: false,
        code: 'MISSING_ID',
        message: '缺少画廊项目ID'
      };
    }

    // 先获取当前状态
    const item = await adminDB.collection(COLLECTION_NAME).doc(data._id).get();
    if (!item.data) {
      return {
        success: false,
        code: 'GALLERY_ITEM_NOT_FOUND',
        message: '画廊项目不存在'
      };
    }

    // 获取操作者信息
    const openId = context && context.OPENID ? context.OPENID : '';

    // 更新为相反的状态
    const newVisibility = !item.data.isVisible;
    await adminDB.collection(COLLECTION_NAME).doc(data._id).update({
      data: {
        isVisible: newVisibility,
        updateTime: db.serverDate(),
        lastUpdatedBy: openId, // 记录最后更新者
        lastVisibilityChange: db.serverDate() // 记录最后可见性变更时间
      }
    });

    return {
      success: true,
      data: {
        isVisible: newVisibility
      },
      message: `画廊项目已${newVisibility ? '显示' : '隐藏'}`
    };
  } catch (err) {
    console.error('切换画廊项目可见状态失败', err);
    return {
      success: false,
      code: 'TOGGLE_GALLERY_VISIBILITY_FAILED',
      message: '切换画廊项目可见状态失败：' + err.message
    };
  }
}

// 获取可见的画廊列表（前端页面使用）
async function getVisibleGalleryList(data, adminDB) {
  try {
    // 解构数据
    const { page = 1, pageSize = 10, categoryId, afterTimestamp, forceReload, includeVideoInfo = false } = data || {};
    
    // 查询条件
    const query = {
      isVisible: true // 只获取可见的画廊项目
    };
    
    // 如果指定了分类ID，添加分类筛选条件
    if (categoryId) {
      query.categoryId = categoryId;
    }
    
    // 如果指定了时间戳，只获取更新的数据
    if (afterTimestamp) {
      query.createTime = {
        $gt: new Date(afterTimestamp)
      };
    }
    
    // 构建排序条件 - 优先按visibilityChangeTime排序，其次是updateTime和createTime
    const sortOptions = {};
    
    // 优先使用visibilityChangeTime进行排序
    sortOptions.visibilityChangeTime = -1; // 降序：最新变更的在前
    
    // 其次使用updateTime
    sortOptions.updateTime = -1; // 降序：最近更新的在前
    
    // 再次使用order
    sortOptions.order = 1; // 升序：数字小的在前
    
    // 最后使用createTime
    sortOptions.createTime = -1; // 降序：最新创建的在前
    
    // 查询总数
    const countResult = await adminDB.collection(COLLECTION_NAME).where(query).count();
    const total = countResult.total;
    
    // 如果没有数据，直接返回空列表
    if (total === 0) {
      return {
        success: true,
        data: {
          list: [],
          total: 0
        },
        message: '获取画廊列表成功'
      };
    }
    
    // 计算分页参数
    const skip = (page - 1) * pageSize;
    const limit = Math.min(pageSize, 100); // 最多每页100条
    
    // 查询数据
    const result = await adminDB.collection(COLLECTION_NAME)
      .where(query)
      .orderBy('visibilityChangeTime', 'desc') // 首先按visibilityChangeTime降序排序
      .orderBy('updateTime', 'desc') // 然后按updateTime降序排序
      .orderBy('order', 'asc') // 再按order升序排序
      .orderBy('createTime', 'desc') // 最后按createTime降序排序
      .skip(skip)
      .limit(limit)
      .get();
    
    // 处理结果列表
    let galleryList = result.data;
    
    // 如果需要包含视频信息，查询相关视频数据
    if (includeVideoInfo && galleryList.length > 0) {
      console.log('请求包含视频信息，开始查询视频数据');
      
      // 获取所有画廊项的ID
      const galleryIds = galleryList.map(item => item._id);
      
      try {
        // 查询视频集合，获取与画廊项关联的视频信息
        const videoResult = await adminDB.collection('videos')
          .where({
            galleryId: _.in(galleryIds),
            isVisible: true
          })
          .get();
        
        // 创建视频映射表，便于快速查找
        const videoMap = {};
        if (videoResult && videoResult.data) {
          videoResult.data.forEach(video => {
            if (video.galleryId) {
              videoMap[video.galleryId] = video;
            }
          });
        }
        
        console.log(`找到 ${Object.keys(videoMap).length} 个关联视频`);
        
        // 将视频信息添加到画廊项中
        galleryList = galleryList.map(item => {
          const video = videoMap[item._id];
          if (video) {
            return {
              ...item,
              videoInfo: {
                _id: video._id,
                title: video.title || '',
                description: video.description || '',
                coverUrl: video.coverUrl || '',
                duration: video.duration || 0,
                // 不返回实际视频URL，只返回封面图和基本信息
                hasVideo: true
              }
            };
          }
          return item;
        });
      } catch (videoErr) {
        console.error('获取视频信息失败:', videoErr);
        // 即使获取视频信息失败，也继续返回画廊列表
      }
    }
    
    return {
      success: true,
      data: {
        list: galleryList,
        total: total
      },
      message: '获取画廊列表成功'
    };
  } catch (err) {
    console.error('获取可见画廊列表失败:', err);
    return {
      success: false,
      code: 'GET_VISIBLE_GALLERY_LIST_FAILED',
      message: '获取可见画廊列表失败:' + err.message
    };
  }
}

// 获取分类列表
async function getCategoryList(adminDB) {
  try {
    // 获取所有分类
    const result = await adminDB.collection(CATEGORY_COLLECTION)
      .orderBy('order', 'asc')
      .get();
    
    return {
      success: true,
      data: result.data,
      message: '获取分类列表成功'
    };
  } catch (err) {
    console.error('获取分类列表失败:', err);
    return {
      success: false,
      code: 'GET_CATEGORY_LIST_FAILED',
      message: '获取分类列表失败:' + err.message
    };
  }
}

// 添加分类
async function addCategory(data, adminDB) {
  try {
    const { name, order = 0 } = data;
    
    if (!name) {
      return {
        success: false,
        code: 'INVALID_PARAMS',
        message: '分类名称不能为空'
      };
    }
    
    // 检查是否已存在同名分类
    const existingCategory = await adminDB.collection(CATEGORY_COLLECTION)
      .where({
        name: name
      })
      .get();
    
    if (existingCategory.data.length > 0) {
      return {
        success: false,
        code: 'CATEGORY_EXISTS',
        message: '该分类名称已存在'
      };
    }
    
    // 添加新分类
    const result = await adminDB.collection(CATEGORY_COLLECTION).add({
      data: {
        name,
        order,
        createTime: db.serverDate()
      }
    });
    
    return {
      success: true,
      data: {
        _id: result._id
      },
      message: '添加分类成功'
    };
  } catch (err) {
    console.error('添加分类失败:', err);
    return {
      success: false,
      code: 'ADD_CATEGORY_FAILED',
      message: '添加分类失败:' + err.message
    };
  }
}

// 更新分类
async function updateCategory(data, adminDB) {
  try {
    const { _id, name, order } = data;
    
    if (!_id) {
      return {
        success: false,
        code: 'INVALID_PARAMS',
        message: '分类ID不能为空'
      };
    }
    
    // 检查分类是否存在
    const category = await adminDB.collection(CATEGORY_COLLECTION).doc(_id).get();
    if (!category.data) {
      return {
        success: false,
        code: 'CATEGORY_NOT_FOUND',
        message: '分类不存在'
      };
    }
    
    // 如果要更新名称，检查是否与其他分类重名
    if (name) {
      const existingCategory = await adminDB.collection(CATEGORY_COLLECTION)
        .where({
          name: name,
          _id: db.command.neq(_id)
        })
        .get();
      
      if (existingCategory.data.length > 0) {
        return {
          success: false,
          code: 'CATEGORY_EXISTS',
          message: '该分类名称已存在'
        };
      }
    }
    
    // 构建更新数据
    const updateData = {};
    if (name !== undefined) updateData.name = name;
    if (order !== undefined) updateData.order = order;
    
    // 更新分类
    await adminDB.collection(CATEGORY_COLLECTION).doc(_id).update({
      data: updateData
    });
    
    return {
      success: true,
      message: '更新分类成功'
    };
  } catch (err) {
    console.error('更新分类失败:', err);
    return {
      success: false,
      code: 'UPDATE_CATEGORY_FAILED',
      message: '更新分类失败:' + err.message
    };
  }
}

// 删除分类
async function deleteCategory(data, adminDB) {
  try {
    const { _id } = data;
    
    if (!_id) {
      return {
        success: false,
        code: 'INVALID_PARAMS',
        message: '分类ID不能为空'
      };
    }
    
    // 检查分类是否存在
    const category = await adminDB.collection(CATEGORY_COLLECTION).doc(_id).get();
    if (!category.data) {
      return {
        success: false,
        code: 'CATEGORY_NOT_FOUND',
        message: '分类不存在'
      };
    }
    
    // 检查是否有画廊项目使用了该分类
    const galleryItems = await adminDB.collection(COLLECTION_NAME)
      .where({
        categoryId: _id
      })
      .count();
    
    if (galleryItems.total > 0) {
      return {
        success: false,
        code: 'CATEGORY_IN_USE',
        message: `无法删除该分类，因为该分类下有${galleryItems.total}个画廊项目`
      };
    }
    
    // 删除分类
    await adminDB.collection(CATEGORY_COLLECTION).doc(_id).remove();
    
    return {
      success: true,
      message: '删除分类成功'
    };
  } catch (err) {
    console.error('删除分类失败:', err);
    return {
      success: false,
      code: 'DELETE_CATEGORY_FAILED',
      message: '删除分类失败: ' + err.message
    };
  }
}

// 云函数入口函数
exports.main = async (event, context) => {
  // 确保使用管理员权限
  const adminDB = cloud.database({ env: cloud.DYNAMIC_CURRENT_ENV });
  
  // 检查管理员权限
  const isAdmin = await checkAdminPermission(context);
  
  // 记录请求信息
  console.log('请求信息:', {
    action: event.action,
    isAdmin: isAdmin,
    openid: context.OPENID
  });
  
  // 如果是管理员API但用户不是管理员，返回错误
  if (event.action !== 'getVisibleGalleryList' && !isAdmin) {
    return {
      success: false,
      code: 'NO_PERMISSION',
      message: '没有管理员权限'
    };
  }
  
  // 确保集合和字段存在
  await ensureCollectionAndFields(adminDB);
  
  const { action, data = {} } = event;
  
  // 根据action调用对应的处理函数
  switch (action) {
    case 'addGallery':
    case 'addGalleryItem':
      return await addGalleryItem(data, context, adminDB);
    case 'updateGallery':
    case 'updateGalleryItem':
      return await updateGalleryItem(data, context, adminDB);
    case 'deleteGalleryItem':
      return await deleteGalleryItem(data, context, adminDB);
    case 'getGalleryList':
      return await getGalleryList(data, adminDB);
    case 'getGalleryDetail':
      return await getGalleryDetail(data, adminDB);
    case 'getArticleById':
      return await getArticleById(data, adminDB);
    case 'toggleGalleryVisibility':
      return await toggleGalleryVisibility(data, context, adminDB);
    case 'getVisibleGalleryList':
      return await getVisibleGalleryList(data, adminDB);
    // 新增分类相关的操作
    case 'getCategoryList':
      return await getCategoryList(adminDB);
    case 'addCategory':
      return await addCategory(data, adminDB);
    case 'updateCategory':
      return await updateCategory(data, adminDB);
    case 'deleteCategory':
      return await deleteCategory(data, adminDB);
    default:
      return {
        success: false,
        code: 'INVALID_ACTION',
        message: '无效的操作类型'
      };
  }
}; 