<view class="carousel-container">
  <!-- 顶部导航 -->
  <view class="nav-header">
    <view class="back-icon" bindtap="navigateBack">
      <view class="back-arrow"></view>
    </view>
    <view class="page-title">轮播图管理</view>
    <view class="header-actions"></view>
  </view>
  
  <!-- 可滚动区域 -->
  <scroll-view scroll-y="true" class="scroll-area">
    <!-- 轮播图列表 -->
    <view class="carousel-list">
      <block wx:if="{{loading}}">
        <view class="loading-container">
          <view class="loading-spinner"></view>
          <text class="loading-text">加载中...</text>
        </view>
      </block>
      <block wx:elif="{{carousels.length === 0}}">
        <view class="empty-container">
          <text class="empty-text">暂无轮播图，点击添加按钮创建</text>
        </view>
      </block>
      <block wx:else>
        <view class="carousel-items">
          <view wx:for="{{carousels}}" wx:key="id" class="carousel-item">
            <view class="carousel-card">
              <view class="carousel-order">{{item.order}}</view>
              <image class="carousel-image" src="{{item.imageUrl}}" mode="aspectFill"></image>
              <view class="carousel-info">
                <view class="carousel-title">{{item.title}}</view>
              </view>
              <view class="carousel-actions">
                <view class="action-btn edit-btn" bindtap="showEditForm" data-carousel="{{item}}">编辑</view>
                <view class="action-btn delete-btn" bindtap="deleteCarousel" data-carousel="{{item}}">删除</view>
              </view>
              <view class="order-actions">
                <view class="order-btn up-btn {{item.order === 1 ? 'disabled' : ''}}" bindtap="moveUp" data-carousel="{{item}}">↑</view>
                <view class="order-btn down-btn {{item.order === carousels.length ? 'disabled' : ''}}" bindtap="moveDown" data-carousel="{{item}}">↓</view>
              </view>
            </view>
          </view>
        </view>
      </block>
      
      <!-- 安全区域，确保内容不被底部遮挡 -->
      <view class="safe-bottom-area"></view>
    </view>
  </scroll-view>
  
  <!-- 悬浮添加按钮 -->
  <view class="add-button" bindtap="showAddForm">
    <text class="add-icon">+</text>
  </view>
  
  <!-- 添加轮播图表单 -->
  <view class="form-modal {{showAddForm ? 'show' : ''}}">
    <view class="form-mask" bindtap="closeForm"></view>
    <view class="form-content">
      <view class="form-header">
        <text class="form-title">添加轮播图</text>
        <view class="form-close" bindtap="closeForm">×</view>
      </view>
      <view class="form-body">
        <view class="form-item">
          <text class="form-label">标题</text>
          <input class="form-input" placeholder="" value="{{formData.title}}" bindinput="inputChange" data-field="title"></input>
        </view>
        <view class="form-item">
          <text class="form-label">图片</text>
          <view class="image-uploader" bindtap="chooseImage">
            <image wx:if="{{tempImagePath}}" class="uploaded-image" src="{{tempImagePath}}" mode="aspectFill"></image>
            <view class="upload-placeholder {{tempImagePath ? 'hide-placeholder' : ''}}">
              <text class="upload-icon">+</text>
              <text class="upload-text">点击上传图片</text>
            </view>
          </view>
        </view>
        <view class="form-item">
          <text class="form-label">排序</text>
          <input class="form-input" type="number" placeholder="" value="{{formData.order}}" bindinput="inputChange" data-field="order"></input>
        </view>
      </view>
      <view class="form-footer">
        <view class="form-btn cancel-btn" bindtap="closeForm">取消</view>
        <view class="form-btn confirm-btn" bindtap="submitAddForm">确认</view>
      </view>
    </view>
  </view>
  
  <!-- 编辑轮播图表单 -->
  <view class="form-modal {{showEditForm ? 'show' : ''}}">
    <view class="form-mask" bindtap="closeForm"></view>
    <view class="form-content">
      <view class="form-header">
        <text class="form-title">编辑轮播图</text>
        <view class="form-close" bindtap="closeForm">×</view>
      </view>
      <view class="form-body">
        <view class="form-item">
          <text class="form-label">标题</text>
          <input class="form-input" placeholder="" value="{{formData.title}}" bindinput="inputChange" data-field="title"></input>
        </view>
        <view class="form-item">
          <text class="form-label">图片</text>
          <view class="image-uploader" bindtap="chooseImage">
            <image wx:if="{{tempImagePath}}" class="uploaded-image" src="{{tempImagePath}}" mode="aspectFill"></image>
            <image wx:elif="{{formData.imageUrl}}" class="uploaded-image" src="{{formData.imageUrl}}" mode="aspectFill"></image>
            <view class="upload-placeholder">
              <text class="upload-icon">+</text>
              <text class="upload-text {{(tempImagePath || formData.imageUrl) ? 'hide-text' : ''}}">点击上传图片</text>
            </view>
          </view>
        </view>
        <view class="form-item">
          <text class="form-label">排序</text>
          <input class="form-input" type="number" placeholder="" value="{{formData.order}}" bindinput="inputChange" data-field="order"></input>
        </view>
      </view>
      <view class="form-footer">
        <view class="form-btn cancel-btn" bindtap="closeForm">取消</view>
        <view class="form-btn confirm-btn" bindtap="submitEditForm">确认</view>
      </view>
    </view>
  </view>
</view> 