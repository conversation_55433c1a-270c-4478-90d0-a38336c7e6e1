// ================== 云开发初始化 (请勿修改) ==================
// @feature: 云开发环境初始化
// @version: 1.0.0
// @warning: 以下代码已完成云开发初始化功能，请勿修改

const { ENV_ID } = require('../config/index');

/**
 * 初始化云开发环境
 */
const initCloud = () => {
  if (!wx.cloud) {
    console.error('请使用 2.2.3 或以上的基础库以使用云能力');
    return false;
  }
  
  // 初始化云开发
  wx.cloud.init({
    env: ENV_ID,
    traceUser: true, // 是否将用户访问记录到云开发控制台
  });
  
  console.log('云开发环境初始化成功', ENV_ID);
  return true;
};

module.exports = {
  initCloud
};

// ================== 在此线以下添加新功能 ================== 