<view class="launch-admin-container">
  <view class="page-header">
    <view class="back-icon" bindtap="navigateBack">
      <text class="iconfont icon-back">←</text>
    </view>
    <view class="header-title">广告管理</view>
  </view>
  
  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{isLoading}}">
    <view class="loading-spinner"></view>
    <view class="loading-text">加载中...</view>
  </view>
  
  <!-- 广告图片上传区域 -->
  <view class="launch-content" wx:if="{{!isLoading}}">
    <view class="upload-tip">广告画面需要上传两张图片，将按顺序显示形成过渡动画效果</view>
    
    <!-- 第一张图片 -->
    <view class="image-section">
      <view class="section-title">第一张图片 (launch_first)</view>
      <view class="image-uploader" bindtap="uploadFirstImage">
        <image 
          wx:if="{{launchImages.first.path}}" 
          class="preview-image" 
          src="{{launchImages.first.path}}" 
          mode="aspectFill"
        ></image>
        <view wx:else class="upload-placeholder">
          <view class="upload-icon">+</view>
          <view class="upload-text">点击上传图片</view>
        </view>
      </view>
      
      <!-- 上传进度 -->
      <view class="upload-progress" wx:if="{{launchImages.first.uploading}}">
        <view class="progress-bar">
          <view class="progress-inner" style="width: {{launchImages.first.progress}}%;"></view>
        </view>
        <view class="progress-text">{{launchImages.first.progress}}%</view>
      </view>
      
      <!-- 上传状态 -->
      <view class="upload-status" wx:if="{{launchImages.first.uploaded}}">
        <text class="status-icon">✓</text>
        <text class="status-text">已上传</text>
      </view>
    </view>
    
    <!-- 第二张图片 -->
    <view class="image-section">
      <view class="section-title">第二张图片 (launch_second)</view>
      <view class="image-uploader" bindtap="uploadSecondImage">
        <image 
          wx:if="{{launchImages.second.path}}" 
          class="preview-image" 
          src="{{launchImages.second.path}}" 
          mode="aspectFill"
        ></image>
        <view wx:else class="upload-placeholder">
          <view class="upload-icon">+</view>
          <view class="upload-text">点击上传图片</view>
        </view>
      </view>
      
      <!-- 上传进度 -->
      <view class="upload-progress" wx:if="{{launchImages.second.uploading}}">
        <view class="progress-bar">
          <view class="progress-inner" style="width: {{launchImages.second.progress}}%;"></view>
        </view>
        <view class="progress-text">{{launchImages.second.progress}}%</view>
      </view>
      
      <!-- 上传状态 -->
      <view class="upload-status" wx:if="{{launchImages.second.uploaded}}">
        <text class="status-icon">✓</text>
        <text class="status-text">已上传</text>
      </view>
    </view>

    <!-- 底部提示 -->
    <view class="help-section">
      <view class="help-title">提示说明</view>
      <view class="help-content">
        <view class="help-item">1. 图片建议尺寸为 750×1334 像素</view>
        <view class="help-item">2. 上传完成后会自动保存到云端</view>
        <view class="help-item">3. 更新图片后，用户下次打开小程序将看到新的广告</view>
      </view>
    </view>
    
    <!-- 底部安全区域，确保内容不被遮挡 -->
    <view class="safe-bottom-area"></view>
  </view>

  <!-- 初始空状态 -->
  <view class="empty-state" wx:if="{{!isLoading && (!launchImages.first.path && !launchImages.second.path)}}">
  </view>
</view> 