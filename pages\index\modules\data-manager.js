/**
 * 数据管理模块
 * 负责页面数据状态管理、缓存处理、状态同步和数据验证
 * @version 1.0.0
 */

const BaseModule = require('./base-module');
const cacheManager = require('../utils/cache-manager');
const dataSyncManager = require('../utils/data-sync');
const { 
  DEFAULT_PAGE_DATA, 
  DEFAULT_VIDEO_DATA,
  CACHE_CONFIG,
  PAGE_STATES,
  ERROR_TYPES 
} = require('../constants/index-constants');

class DataManagerModule extends BaseModule {
  constructor(pageContext) {
    super(pageContext);
    this.moduleName = 'DataManager';
    this.cacheKeys = {
      VIDEO_LIST: CACHE_CONFIG.VIDEO_LIST_CACHE_KEY,
      VIDEO_URL_PREFIX: CACHE_CONFIG.VIDEO_URL_CACHE_PREFIX,
      LAST_REFRESH: 'last_refresh_time'
    };
  }

  /**
   * 初始化数据管理模块
   */
  init() {
    try {
      // console.log('[DataManager] 初始化数据管理模块');
      
      // 初始化页面数据状态
      this.initPageData();
      
      // 清理过期缓存
      this.cleanExpiredCache();
      
      this.initialized = true;
      // console.log('[DataManager] 数据管理模块初始化完成');
    } catch (error) {
      this.handleError(error, 'init');
    }
  }

  /**
   * 初始化页面数据状态
   */
  initPageData() {
    try {
      // 获取当前页面数据
      const currentData = this.data;
      
      // 合并默认数据和当前数据，确保所有必要字段都存在
      const initialData = {
        ...DEFAULT_PAGE_DATA,
        ...currentData,
        // 确保关键字段有正确的初始值
        videoList: currentData.videoList || [],
        urlCache: currentData.urlCache || {},
        urlFetchingIds: currentData.urlFetchingIds || [],
        app: this.page.app || getApp()
      };

      // 验证数据完整性
      this.validatePageData(initialData);
      
      // console.log('[DataManager] 页面数据初始化完成');
    } catch (error) {
      this.handleError(error, 'initPageData');
    }
  }

  /**
   * 更新页面数据
   * @param {string|object} key - 数据键名或数据对象
   * @param {*} value - 数据值（当key为字符串时使用）
   * @param {function} callback - 更新完成回调
   */
  updateData(key, value, callback) {
    try {
      let updateData = {};
      
      if (typeof key === 'object') {
        updateData = key;
        callback = value; // 第二个参数是回调函数
      } else {
        updateData[key] = value;
      }

      // 验证更新数据
      this.validateUpdateData(updateData);
      
      // 安全更新数据
      this.safeSetData(updateData, callback);
      
      // 触发数据更新事件
      this.emit('dataUpdate', { keys: Object.keys(updateData), data: updateData });
      
    } catch (error) {
      this.handleError(error, 'updateData');
    }
  }

  /**
   * 获取页面数据
   * @param {string} key - 数据键名，不传则返回所有数据
   * @returns {*} 数据值
   */
  getData(key) {
    try {
      if (key) {
        return this.data[key];
      }
      return this.data;
    } catch (error) {
      this.handleError(error, 'getData');
      return null;
    }
  }

  /**
   * 缓存数据到本地存储
   * @param {string} key - 缓存键名
   * @param {*} data - 要缓存的数据
   * @param {number} expireTime - 过期时间（毫秒），可选
   */
  cacheData(key, data, expireTime) {
    try {
      const cacheItem = {
        data: data,
        timestamp: Date.now(),
        expireTime: expireTime || CACHE_CONFIG.CACHE_EXPIRE_TIME
      };

      wx.setStorageSync(key, cacheItem);
      console.log(`[DataManager] 数据已缓存: ${key}`);
    } catch (error) {
      this.handleError(error, 'cacheData');
    }
  }

  /**
   * 从本地存储获取缓存数据
   * @param {string} key - 缓存键名
   * @returns {*} 缓存的数据，如果不存在或已过期则返回null
   */
  getCachedData(key) {
    try {
      const cacheItem = wx.getStorageSync(key);
      
      if (!cacheItem) {
        return null;
      }

      // 检查是否过期
      const now = Date.now();
      if (now - cacheItem.timestamp > cacheItem.expireTime) {
        // 删除过期缓存
        wx.removeStorageSync(key);
        console.log(`[DataManager] 缓存已过期并删除: ${key}`);
        return null;
      }

      console.log(`[DataManager] 获取缓存数据: ${key}`);
      return cacheItem.data;
    } catch (error) {
      this.handleError(error, 'getCachedData');
      return null;
    }
  }

  /**
   * 清理过期缓存
   */
  cleanExpiredCache() {
    try {
      // 使用缓存管理器清理过期缓存
      cacheManager.cleanExpiredCache();
      
      // 清理页面内存中的URL缓存
      const urlCache = this.getData('urlCache') || {};
      const cleanedUrlCache = {};
      let cleanedCount = 0;
      const now = Date.now();

      Object.keys(urlCache).forEach(videoId => {
        const cacheItem = urlCache[videoId];
        if (cacheItem && cacheItem.timestamp) {
          const age = now - cacheItem.timestamp;
          if (age < CACHE_CONFIG.URL_CACHE_EXPIRE_TIME) {
            cleanedUrlCache[videoId] = cacheItem;
          } else {
            cleanedCount++;
          }
        }
      });

      if (cleanedCount > 0) {
        this.updateData('urlCache', cleanedUrlCache);
        console.log(`[DataManager] 清理了 ${cleanedCount} 个页面内存URL缓存`);
      }
      
    } catch (error) {
      this.handleError(error, 'cleanExpiredCache');
    }
  }

  /**
   * 清理本地存储中的过期缓存
   */
  cleanLocalStorageCache() {
    try {
      const storageInfo = wx.getStorageInfoSync();
      const now = Date.now();
      let cleanedCount = 0;

      storageInfo.keys.forEach(key => {
        try {
          // 只处理我们的缓存键
          if (key.startsWith(CACHE_CONFIG.VIDEO_LIST_CACHE_KEY) || 
              key.startsWith(CACHE_CONFIG.VIDEO_URL_CACHE_PREFIX)) {
            const cacheItem = wx.getStorageSync(key);
            if (cacheItem && cacheItem.timestamp) {
              const age = now - cacheItem.timestamp;
              if (age > cacheItem.expireTime) {
                wx.removeStorageSync(key);
                cleanedCount++;
              }
            }
          }
        } catch (e) {
          // 忽略单个缓存项的错误
          console.warn(`[DataManager] 清理缓存项失败: ${key}`, e);
        }
      });

      if (cleanedCount > 0) {
        console.log(`[DataManager] 清理了 ${cleanedCount} 个本地存储缓存`);
      }
    } catch (error) {
      this.handleError(error, 'cleanLocalStorageCache');
    }
  }

  /**
   * 清除所有缓存
   */
  clearCache() {
    try {
      // 清除页面内存缓存
      this.updateData({
        urlCache: {},
        urlFetchingIds: []
      });

      // 清除本地存储缓存
      const storageInfo = wx.getStorageInfoSync();
      let clearedCount = 0;

      storageInfo.keys.forEach(key => {
        try {
          if (key.startsWith(CACHE_CONFIG.VIDEO_LIST_CACHE_KEY) || 
              key.startsWith(CACHE_CONFIG.VIDEO_URL_CACHE_PREFIX)) {
            wx.removeStorageSync(key);
            clearedCount++;
          }
        } catch (e) {
          console.warn(`[DataManager] 清除缓存项失败: ${key}`, e);
        }
      });

      console.log(`[DataManager] 已清除所有缓存，共 ${clearedCount} 项`);
    } catch (error) {
      this.handleError(error, 'clearCache');
    }
  }

  /**
   * 验证页面数据完整性
   * @param {object} data - 要验证的数据
   */
  validatePageData(data) {
    const requiredFields = [
      'videoList', 'loading', 'firstLoading', 'hasMore', 
      'page', 'pageSize', 'urlCache', 'urlFetchingIds'
    ];

    requiredFields.forEach(field => {
      if (!(field in data)) {
        console.warn(`[DataManager] 缺少必要字段: ${field}`);
        throw new Error(`Missing required field: ${field}`);
      }
    });
  }

  /**
   * 验证更新数据
   * @param {object} updateData - 要验证的更新数据
   */
  validateUpdateData(updateData) {
    // 验证视频列表数据
    if (updateData.videoList) {
      if (!Array.isArray(updateData.videoList)) {
        throw new Error('videoList must be an array');
      }
      
      // 验证视频数据结构
      updateData.videoList.forEach((video, index) => {
        if (!video.id) {
          console.warn(`[DataManager] 视频数据缺少ID: index ${index}`, video);
        }
      });
    }

    // 验证页码数据
    if (updateData.page !== undefined && (typeof updateData.page !== 'number' || updateData.page < 0)) {
      throw new Error('page must be a non-negative number');
    }

    // 验证缓存数据
    if (updateData.urlCache && typeof updateData.urlCache !== 'object') {
      throw new Error('urlCache must be an object');
    }
  }

  /**
   * 同步全局数据
   */
  syncGlobalData() {
    try {
      // 使用数据同步管理器进行同步
      dataSyncManager.syncToPage(this.page);
      console.log('[DataManager] 全局数据同步完成');
    } catch (error) {
      this.handleError(error, 'syncGlobalData');
    }
  }

  /**
   * 同步页面数据到全局
   * @param {string|Array} keys - 要同步的数据键，不传则同步所有
   */
  syncToGlobal(keys) {
    try {
      dataSyncManager.syncToGlobal(this.page, keys);
      console.log('[DataManager] 页面数据同步到全局完成');
    } catch (error) {
      this.handleError(error, 'syncToGlobal');
    }
  }

  /**
   * 双向数据同步
   * @param {string|Array} keys - 要同步的数据键
   */
  bidirectionalSync(keys) {
    try {
      dataSyncManager.bidirectionalSync(this.page, keys);
      console.log('[DataManager] 双向数据同步完成');
    } catch (error) {
      this.handleError(error, 'bidirectionalSync');
    }
  }

  /**
   * 添加数据同步规则
   * @param {string} key - 数据键名
   * @param {object} rule - 同步规则
   */
  addSyncRule(key, rule) {
    try {
      dataSyncManager.addSyncRule(key, rule);
      console.log(`[DataManager] 添加同步规则: ${key}`);
    } catch (error) {
      this.handleError(error, 'addSyncRule');
    }
  }

  /**
   * 监听数据变化事件
   * @param {string} event - 事件名称
   * @param {function} listener - 监听器函数
   */
  onDataChange(event, listener) {
    try {
      dataSyncManager.on(event, listener);
      console.log(`[DataManager] 添加数据变化监听器: ${event}`);
    } catch (error) {
      this.handleError(error, 'onDataChange');
    }
  }

  /**
   * 异步同步数据（添加到同步队列）
   * @param {string} type - 同步类型：'toGlobal', 'toPage', 'bidirectional'
   * @param {string|Array} keys - 要同步的数据键
   * @param {object} options - 同步选项
   */
  asyncSync(type, keys, options = {}) {
    try {
      dataSyncManager.addToSyncQueue({
        type: type,
        pageContext: this.page,
        keys: keys,
        options: options,
        priority: options.priority || 0
      });
      
      console.log(`[DataManager] 添加异步同步任务: ${type}`);
    } catch (error) {
      this.handleError(error, 'asyncSync');
    }
  }

  /**
   * 获取数据同步状态
   * @returns {object} 同步状态信息
   */
  getSyncStatus() {
    try {
      return dataSyncManager.getSyncStatus();
    } catch (error) {
      this.handleError(error, 'getSyncStatus');
      return {};
    }
  }

  /**
   * 重置页面数据到初始状态
   */
  resetData() {
    try {
      const resetData = {
        ...DEFAULT_PAGE_DATA,
        app: getApp() // 保持app引用
      };

      this.safeSetData(resetData);
      console.log('[DataManager] 页面数据已重置');
    } catch (error) {
      this.handleError(error, 'resetData');
    }
  }

  /**
   * 缓存视频列表数据
   * @param {Array} videoList - 视频列表
   * @param {object} options - 缓存选项
   */
  cacheVideoList(videoList, options = {}) {
    try {
      // 使用缓存管理器缓存视频列表
      const success = cacheManager.cacheVideoList(videoList, options);
      
      if (success) {
        // 更新最后刷新时间
        this.updateData('lastRefreshTime', Date.now());
        console.log('[DataManager] 视频列表缓存成功');
      }
      
      return success;
    } catch (error) {
      this.handleError(error, 'cacheVideoList');
      return false;
    }
  }

  /**
   * 获取缓存的视频列表
   * @param {object} options - 获取选项
   * @returns {Array|null} 视频列表或null
   */
  getCachedVideoList(options = {}) {
    try {
      return cacheManager.getCachedVideoList(options);
    } catch (error) {
      this.handleError(error, 'getCachedVideoList');
      return null;
    }
  }

  /**
   * 缓存视频URL
   * @param {string} videoId - 视频ID
   * @param {string} videoUrl - 视频URL
   * @param {object} options - 缓存选项
   */
  cacheVideoUrl(videoId, videoUrl, options = {}) {
    try {
      // 同时缓存到缓存管理器和页面内存
      const success = cacheManager.cacheVideoUrl(videoId, videoUrl, options);
      
      if (success) {
        // 更新页面内存缓存
        const urlCache = this.getData('urlCache') || {};
        urlCache[videoId] = {
          url: videoUrl,
          timestamp: Date.now()
        };
        this.updateData('urlCache', urlCache);
      }
      
      return success;
    } catch (error) {
      this.handleError(error, 'cacheVideoUrl');
      return false;
    }
  }

  /**
   * 获取缓存的视频URL
   * @param {string} videoId - 视频ID
   * @returns {string|null} 视频URL或null
   */
  getCachedVideoUrl(videoId) {
    try {
      // 先从页面内存缓存获取
      const urlCache = this.getData('urlCache') || {};
      const memoryCache = urlCache[videoId];
      
      if (memoryCache && memoryCache.url) {
        // 检查内存缓存是否过期
        const age = Date.now() - memoryCache.timestamp;
        if (age < CACHE_CONFIG.URL_CACHE_EXPIRE_TIME) {
          return memoryCache.url;
        }
      }
      
      // 从缓存管理器获取
      return cacheManager.getCachedVideoUrl(videoId);
    } catch (error) {
      this.handleError(error, 'getCachedVideoUrl');
      return null;
    }
  }

  /**
   * 批量缓存视频URL
   * @param {object} urlMap - 视频ID到URL的映射
   */
  batchCacheVideoUrls(urlMap) {
    try {
      const successCount = cacheManager.batchCacheVideoUrls(urlMap);
      
      // 同时更新页面内存缓存
      const urlCache = this.getData('urlCache') || {};
      const timestamp = Date.now();
      
      Object.entries(urlMap).forEach(([videoId, videoUrl]) => {
        urlCache[videoId] = {
          url: videoUrl,
          timestamp: timestamp
        };
      });
      
      this.updateData('urlCache', urlCache);
      console.log(`[DataManager] 批量缓存视频URL完成: ${successCount} 个`);
      
      return successCount;
    } catch (error) {
      this.handleError(error, 'batchCacheVideoUrls');
      return 0;
    }
  }

  /**
   * 批量获取缓存的视频URL
   * @param {Array} videoIds - 视频ID数组
   * @returns {object} 视频ID到URL的映射
   */
  batchGetCachedVideoUrls(videoIds) {
    try {
      const urlMap = {};
      const urlCache = this.getData('urlCache') || {};
      const now = Date.now();
      
      // 先从页面内存缓存获取
      videoIds.forEach(videoId => {
        const memoryCache = urlCache[videoId];
        if (memoryCache && memoryCache.url) {
          const age = now - memoryCache.timestamp;
          if (age < CACHE_CONFIG.URL_CACHE_EXPIRE_TIME) {
            urlMap[videoId] = memoryCache.url;
          }
        }
      });
      
      // 获取未命中的视频ID
      const missedIds = videoIds.filter(id => !urlMap[id]);
      
      if (missedIds.length > 0) {
        // 从缓存管理器批量获取
        const cachedUrls = cacheManager.batchGetCachedVideoUrls(missedIds);
        Object.assign(urlMap, cachedUrls);
      }
      
      return urlMap;
    } catch (error) {
      this.handleError(error, 'batchGetCachedVideoUrls');
      return {};
    }
  }

  /**
   * 获取缓存统计信息
   * @returns {object} 缓存统计信息
   */
  getCacheStats() {
    try {
      const cacheStats = cacheManager.getCacheStats();
      const cacheSize = cacheManager.getCacheSize();
      const memoryCache = this.getData('urlCache') || {};
      
      return {
        ...cacheStats,
        ...cacheSize,
        memoryCacheSize: Object.keys(memoryCache).length
      };
    } catch (error) {
      this.handleError(error, 'getCacheStats');
      return {};
    }
  }

  /**
   * 获取页面状态摘要
   * @returns {object} 状态摘要
   */
  getStateSummary() {
    try {
      const data = this.getData();
      const cacheStats = this.getCacheStats();
      
      return {
        videoCount: data.videoList ? data.videoList.length : 0,
        loading: data.loading,
        hasMore: data.hasMore,
        currentPage: data.page,
        isSearching: data.isSearching,
        cacheSize: Object.keys(data.urlCache || {}).length,
        lastRefreshTime: data.lastRefreshTime,
        cacheStats: cacheStats
      };
    } catch (error) {
      this.handleError(error, 'getStateSummary');
      return {};
    }
  }

  /**
   * 销毁模块，清理资源
   */
  destroy() {
    try {
      console.log('[DataManager] 销毁数据管理模块');
      super.destroy();
    } catch (error) {
      this.handleError(error, 'destroy');
    }
  }
}

module.exports = DataManagerModule;