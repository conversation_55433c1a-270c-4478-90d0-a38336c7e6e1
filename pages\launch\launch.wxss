/**
 * 启动页样式
 * 控制初始加载画面的显示和动画效果
 */

/* ========== 基础结构 ========== */

/* 启动页占位样式 */
.launch-placeholder {
  width: 100%;
  height: 100vh;
}

/* 启动页全屏容器 - 控制整体背景和位置 */
.launch-fullscreen-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 99999;
  touch-action: none; /* 禁止所有触摸操作 */
  overflow: hidden; /* 防止内容溢出 */
  background: var(--launch-container-bg, transparent); /* 使用变量控制背景 */
}

/* 启动页内容容器 - 控制内部元素布局 */
.launch-container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  opacity: 1;
  transition: opacity var(--launch-fade-out-transition, 0.5s) ease;
  touch-action: none; /* 禁止所有触摸操作 */
}

/* ========== 图片样式 ========== */

/* 启动页图片基础样式 */
.launch-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  transition: opacity 1.2s ease-in-out;
  z-index: 1; /* 确保图片在背景之上 */
}

/* 通用图片显示状态 */
.launch-image.show {
  opacity: 1;
}

/* 第一张图片样式 */
.first-image {
  z-index: 1;
  mix-blend-mode: var(--launch-image-blend-mode, normal);
}

/* 第一张图片加载完成状态 */
.first-image.loaded {
  opacity: var(--launch-image-opacity, 1);
  transition: opacity 1.2s ease-in-out;
}

/* 第二张图片样式 */
.second-image {
  z-index: 2;
  opacity: 0;
  mix-blend-mode: var(--launch-image-blend-mode, normal);
}

/* 第二张图片动画显示状态 */
.second-image.animate-second {
  opacity: var(--launch-image-opacity, 1);
  animation: none; /* 禁用动画，只使用transition */
  transition: opacity 1.8s ease-in-out;
}

/* 背景图片样式 */
.background-image {
  z-index: 5;
  opacity: 1; /* 始终显示 */
}

/* ========== 错误提示 ========== */

/* 错误提示 */
.error-message {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: var(--launch-error-text, var(--text-primary));
  background-color: var(--launch-error-bg, var(--bg-mask));
  padding: 20rpx 40rpx;
  border-radius: 10rpx;
  z-index: 100;
  text-align: center;
  font-size: 28rpx;
}

/* 移除旧的加载提示样式，使用新的loading-indicator组件替代 */

/* ========== 动画效果 ========== */

/* 旋转动画（仍需保留，用于其他需要旋转效果的元素） */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 淡入动画 */
@keyframes fadeIn {
  0% { opacity: 0; }
  100% { opacity: 1; }
}

/* 启动页淡出动画 */
.launch-container.fade-out {
  opacity: 0;
  transition: opacity var(--launch-fade-out-transition, 0.5s) ease-in-out;
}

/* 闪光效果动画 - 启动页专用 */
@keyframes launch-shimmer {
  0% {
    transform: translateX(-100%) skewX(-15deg);
  }
  42% { /* 约1.1秒完成移动 */
    transform: translateX(300%) skewX(-15deg);
  }
  100% { /* 剩余时间保持不动，形成停顿效果 */
    transform: translateX(300%) skewX(-15deg);
  }
}

/* 闪光效果样式 - 启动页专用 */
.launch-shimmer-effect {
  position: relative;
  overflow: hidden;
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

.launch-shimmer-effect::before {
  content: '';
  position: absolute;
  top: 0;
  left: -50%;
  width: 70%;
  height: 100%;
  background: linear-gradient(
    90deg, 
    rgba(255, 255, 255, 0) 0%, 
    rgba(255, 255, 255, 0.1) 50%, 
    rgba(255, 255, 255, 0) 100%
  );
  animation: launch-shimmer 2.6s linear infinite;
  z-index: 10;
}

/* ========== 覆盖层和页面控制 ========== */

/* 启动页覆盖层 - 确保在导航栏之上 */
.launch-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  z-index: 99999 !important;
  background-color: var(--launch-overlay-bg, transparent);
}

/* 启动页整体页面 */
.launch-page {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9999;
}

/* 页面淡出效果 */
.launch-page.fade-out {
  opacity: 0;
  transition: opacity var(--launch-fade-out-transition, 0.5s) ease-in-out;
} 