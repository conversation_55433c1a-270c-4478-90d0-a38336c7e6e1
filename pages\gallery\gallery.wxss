/* 添加页面级固定 */
page {
  height: 100%;
  width: 100%;
  position: fixed;  /* 固定整个页面，防止页面级滚动 */
  overflow: hidden; /* 防止页面级滚动 */
  background: none; /* 移除页面背景，使用固定背景层 */
}

/* 固定的渐变背景层 */
.fixed-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--gallery-bg); /* 使用与页面相同的渐变背景 */
  z-index: 1; /* 最底层 */
}

/* 添加下拉刷新提示样式 */
.refresh-tip {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10rpx 20rpx;
  color: #ffffff;
  font-size: 26rpx;
  background-color: rgba(0, 0, 0, 0.7);
  border-radius: 8rpx;
  margin: 20rpx auto;
  z-index: 10;
}

.refresh-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 10rpx;
  border: 3rpx solid rgba(255, 255, 255, 0.3);
  border-top: 3rpx solid #ffffff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 自定义下拉刷新指示器 */
.custom-refresh-indicator {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 80rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.7);
  color: #fff;
  z-index: 4; /* 确保在容器之上 */
  transform: translateY(-100%);
  transition: transform 0.3s ease;
}

.custom-refresh-indicator.refreshing {
  transform: translateY(0);
}

.refresh-spinner {
  width: 32rpx;
  height: 32rpx;
  border: 3rpx solid rgba(255, 255, 255, 0.3);
  border-top: 3rpx solid #ffffff;
  border-radius: 50%;
  margin-right: 10rpx;
  animation: spin 1s linear infinite;
}

.refresh-text {
  font-size: 26rpx;
  color: #ffffff;
}

/* 下拉刷新时的容器状态 */
.main-scroll.refreshing {
  opacity: 0.8;
}

/* 页面容器 */
.container {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
  opacity: 0; /* 默认隐藏，加载完成后显示 */
  transform: translateY(20rpx); /* 默认位置略微下移 */
  transition: opacity 0.5s ease-out, transform 0.5s ease-out; /* 增加动画时长和更平滑的缓动效果 */
  padding-top: 0; /* 移除顶部填充，让导航栏组件控制 */
  background-color: transparent; /* 透明背景，显示下方的固定背景层 */
  z-index: 3; /* 在加载动画之上 */
}

/* 容器显示动画 */
.container.show {
  opacity: 1;
  transform: translateY(0);
}

/* 添加新的样式类，用于从标签栏切换时应用 */
.container.from-tab {
  opacity: 1;
  transform: translateY(0);
  transition: none; /* 禁用动画过渡 */
}

/* 主要滚动区域 */
.main-scroll {
  width: 100%;
  height: 100%;
  position: relative;
  overflow-y: scroll;
  -webkit-overflow-scrolling: touch;
  padding-bottom: env(safe-area-inset-bottom);
  /* 增强滚动控制 */
  scroll-behavior: smooth;
  overscroll-behavior: contain;
  will-change: scroll-position;
  /* 添加可见性过渡效果 */
  opacity: 1; /* 修改：默认可见 */
  transform: translateY(0); /* 修改：默认位置正常 */
  transition: opacity 0.3s ease-in-out;
}

/* 添加滚动区域可见状态 */
.main-scroll.visible {
  opacity: 1;
  transform: translateY(0);
}

/* 主要滚动区域 */
.main-scroll.from-tab {
  opacity: 1;
  transform: translateY(0);
  transition: none; /* 禁用动画过渡 */
}

.info-list-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 0rpx 30rpx;
  /* 设置顶部内边距，动态适应导航栏高度 */
  padding-top: 240rpx; /* 从200rpx增加到240rpx，让第一个信息列表向下移动 */
  /* 设置底部间距为精确的导航栏高度+安全区域，不添加额外空间 */
  padding-bottom: var(--tab-bar-height);
  box-sizing: border-box;
  align-items: center;
}

/* 确保最后一个列表项底部有适当空间，但不会过多 */
.info-list:last-child {
  margin-bottom: calc(env(safe-area-inset-bottom) + 20rpx);
}

.info-list {
  position: relative;
  width: 677.98rpx;
  height: 831.88rpx; /* 修改高度，使宽高比与750:920相同 */
  margin-bottom: 40rpx;
  margin-left: auto;
  margin-right: auto;
  background: transparent; /* 移除背景色 */
  border-radius: var(--gallery-card-border-radius);
  box-shadow: none; /* 移除阴影 */
  opacity: 1;
  transform: translateY(0);
  transition: transform 0.3s ease, opacity 0.3s ease;
  transform-origin: center center;
}

/* 添加触摸状态样式 */
.info-list.touched {
  transform: scale(var(--gallery-card-scale)); /* 移除Y轴位移，只保留缩放 */
}

.info-list.moving-up {
  transform: scale(var(--gallery-card-scale)) translateY(-8rpx);
}

.info-list.moving-down {
  transform: scale(var(--gallery-card-scale)) translateY(8rpx);
}

.info-list.released {
  transform: scale(1);
  transition: transform 0.3s ease-out;
}

/* 修改：创建新的动画，从屏幕下方向上滑入 */
@keyframes slideInFromBottom {
  0% {
    opacity: 0.5;
    transform: translateY(50rpx);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 替换原有的 slideInFromRight 动画 */
.info-list.fade-in {
  opacity: 1;
  transform: translateY(0);
  animation: slideInFromBottom 0.6s ease-out forwards;
}

/* 淡出动画 */
.info-list.fade-out {
  opacity: 0;
  transform: translateY(-30rpx);
  transition: opacity 0.3s ease, transform 0.3s ease;
}

.info-list.fade-in.touched {
  transform: scale(var(--gallery-card-scale));
}

.info-list.fade-in.moving-up {
  transform: scale(var(--gallery-card-scale)) translateY(-8rpx);
}

.info-list.fade-in.moving-down {
  transform: scale(var(--gallery-card-scale)) translateY(8rpx);
}

.info-list.fade-in.released {
  transform: scale(1);
  transition: transform 0.3s ease-out;
}

.info-list:active {
  transform: scale(0.98);
}

/* 图片容器 */
.image-container {
  position: absolute;
  width: 677.98rpx; /* 保持与列表项相同宽度 */
  height: 831.88rpx; /* 修改高度，使宽高比与750:920相同 */
  top: 0;
  left: 0;
  background-color: transparent; /* 移除背景色 */
  border-radius: var(--gallery-card-border-radius);
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
}

.cover-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  border-radius: var(--gallery-card-border-radius); /* 添加圆角 */
}

/* 玻璃效果容器 - 修改 */
.glass-container {
  position: absolute;
  left: 50%;
  bottom: 10rpx; /* 修改：从50rpx减少到10rpx，使容器向下移动 */
  transform: translateX(-50%);
  width: 610.69rpx; /* 保持宽度不变 */
  height: 170rpx; /* 修改：从190rpx减少10%到170rpx */
  padding: 15rpx 36rpx; /* 保持上下内边距不变 */
  background: rgba(29, 29, 29, 0.5); /* 修改：增加背景不透明度，使其更加明显 */
  backdrop-filter: blur(10px);
  border-radius: 36rpx; /* 保持圆角不变 */
  box-shadow: none; /* 修改：移除阴影 */
  border: none; /* 修改：移除边框 */
  z-index: 3;
  display: flex;
  flex-direction: column;
  justify-content: space-between; /* 保持内容分布均匀 */
  box-sizing: border-box; /* 保持盒模型设置不变 */
}

/* 主标题 - 修改 */
.main-title-first {
  position: relative;
  width: 100%;
  height: auto;
  max-height: 130rpx; /* 保持增加后的高度 */
  overflow: visible; /* 修改：从hidden改为visible，允许内容溢出显示 */
  box-sizing: border-box;
  z-index: 10; /* 确保主标题显示在最上层 */
  background: transparent; /* 修改：从black改为transparent，移除黑色背景 */
  margin-top: 13rpx; /* 修改：从33rpx减少到13rpx，使容器向上移动20rpx */
  margin-bottom: 12rpx;
  padding-right: 40rpx; /* 修改：从80rpx减少到40rpx，确保不会遮挡分享图标 */
  padding-left: 8rpx;
}

/* 底部信息栏 - 修改 */
.bottom-info-bar {
  display: flex;
  justify-content: space-between;
  align-items: flex-end; /* 修改：从center改为flex-end，使所有子元素底部对齐 */
  width: 100%;
  height: 44rpx;
  margin-top: 10rpx; /* 修改：从30rpx减少到10rpx，使容器向上移动20rpx */
  margin-bottom: 15rpx;
  position: relative;
  background: transparent; /* 修改：从半透明绿色改为透明 */
}

/* 左侧信息区域 - 修改 */
.left-info {
  display: flex;
  align-items: flex-end; /* 修改：从center改为flex-end，使内部元素底部对齐 */
  height: 100%;
}

/* Logo图标样式 - 修改 */
.logo-icon {
  width: 37.4rpx;
  height: 37.4rpx;
  margin-right: 8rpx;
  object-fit: contain;
  vertical-align: bottom; /* 修改：从middle改为bottom，使图标底部对齐 */
  padding: 2rpx;
  display: inline-block; /* 修改：使用inline-block而不是flex */
}

/* 副标题文字样式 - 修改 */
.font_2 {
  font-size: 20rpx; /* 修改：从23rpx改为20rpx，统一字体大小 */
  font-family: Poppins;
  line-height: 20rpx; /* 修改：与字体大小一致 */
  color: var(--gallery-description-color);
  word-break: break-word;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.8);
  vertical-align: bottom; /* 保持底部对齐 */
  
  /* 增强多行文本截断的兼容性 */
  display: inline-block; /* 修改：从flex改为inline-block */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 180rpx;
  transform: translateY(-10rpx); /* 修改：从-5rpx改为-10rpx，再向上移动5rpx */
}

/* 定位图标 - 修改 */
.location-icon {
  display: flex;
  justify-content: center;
  align-items: flex-end; /* 修改：从center改为flex-end，使内部元素底部对齐 */
  height: 100%;
}

.location-icon .icon-image {
  width: 40rpx; /* 保持宽度不变 */
  height: 40rpx; /* 保持高度不变 */
  object-fit: contain;
  vertical-align: bottom; /* 修改：从middle改为bottom，使图标底部对齐 */
  display: inline-block; /* 添加inline-block显示 */
}

/* 定位图标文字样式 - 修改 */
.location-text {
  font-size: 20rpx; /* 修改：从18rpx改为20rpx，统一字体大小 */
  color: var(--gallery-description-color);
  margin-left: 8rpx; /* 保持左侧间距不变 */
  line-height: 20rpx; /* 修改：与字体大小一致 */
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.8);
  vertical-align: bottom; /* 保持底部对齐 */
  display: inline-block; /* 修改：从flex改为inline-block */
  transform: translateY(-10rpx); /* 修改：从-5rpx改为-10rpx，再向上移动5rpx */
}

/* 添加点击效果 */
.location-icon:active {
  opacity: 0.7;
  transform: scale(0.95); /* 修改：只保留缩放效果 */
}

/* 营业时间样式 - 修改 */
.business-hours {
  display: flex;
  align-items: flex-end; /* 修改：从center改为flex-end，使内部元素底部对齐 */
  height: 100%;
}

.business-icon {
  width: 40rpx; /* 保持宽度不变 */
  height: 40rpx; /* 保持高度不变 */
  margin-right: 8rpx; /* 保持右侧间距不变 */
  vertical-align: bottom; /* 修改：从middle改为bottom，使图标底部对齐 */
  display: inline-block; /* 添加inline-block显示 */
}

/* 营业时间文字样式 - 修改 */
.business-text {
  font-size: 20rpx; /* 修改：从18rpx改为20rpx，统一字体大小 */
  color: var(--gallery-description-color);
  line-height: 20rpx; /* 修改：与字体大小一致 */
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.8);
  vertical-align: bottom; /* 修改：从middle改为bottom，使文字底部对齐 */
  display: inline-block; /* 修改：添加inline-block显示 */
  transform: translateY(-10rpx); /* 修改：从-5rpx改为-10rpx，再向上移动5rpx */
}

/* 调整副标题样式 */
.main-title-second {
  position: relative;
  width: 100%;
  height: 60rpx; /* 修改：从55rpx增加到60rpx，增加容器高度 */
  overflow: hidden;
  box-sizing: border-box;
  z-index: 3;
  background: transparent;
  padding-right: 130rpx; /* 保持右侧内边距不变 */
  margin-bottom: 5rpx; /* 修改：从10rpx减少到5rpx，使容器向上移动 */
}

/* 副标题容器 */
.subtitle-container {
  display: flex;
  align-items: center; /* 保持居中对齐 */
  height: 100%;
  padding-bottom: 0rpx; /* 保持底部内边距不变 */
  margin-top: 12rpx; /* 修改：从8rpx增加到12rpx，使整个容器继续向下移动 */
  margin-bottom: 15rpx; /* 保持底部外边距不变 */
}

/* 标题文字样式 */
.font {
  font-size: 35rpx; /* 保持字体大小不变 */
  font-family: Poppins;
  line-height: 39rpx; /* 保持行高不变 */
  font-weight: 600;
  color: var(--gallery-title-color);
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.8);
  background: transparent; /* 保持透明背景 */
  position: relative; /* 保持相对定位 */
  z-index: 11; /* 提高z-index，确保在主标题容器之上 */
  
  /* 移除文本截断限制 */
  display: block; /* 修改：从-webkit-box改为block，完全移除行数限制 */
  overflow: visible; /* 保持可见溢出 */
  white-space: normal; /* 保持正常换行 */
  padding-top: 3rpx; /* 保持顶部内边距 */
  padding-bottom: 3rpx; /* 添加底部内边距 */
}

/* 将description区域设为不显示 */
.description {
  display: none;
}

/* 头像 - 修改 */
.avatar {
  display: none; /* 隐藏原来的头像 */
}

/* 创作者描述 - 修改 */
.creator-desc {
  display: none; /* 隐藏创作者描述，因为已经在副标题中显示 */
}

.text_4 {
  color: var(--gallery-creator-color);
  font-size: 24rpx;
  font-family: Poppins;
  line-height: 24rpx;
  font-weight: 400;
  letter-spacing: -0.2rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.5);
}

/* 左上角图标 - 修改 */
.decoration-1 {
  display: none; /* 隐藏左上角图标 */
}

/* 右上角图标 - 修改 */
.decoration-2 {
  display: none; /* 隐藏右上角图标 */
}

/* 为点击图标和省略号图标分别设置不同的尺寸 */
.decoration-1 .icon-image {
  width: 40rpx; /* 调整图标大小 */
  height: 40rpx; /* 调整图标大小 */
  object-fit: contain;
  filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.3));
}

/* 为点击图标和省略号图标分别设置不同的尺寸 */
.decoration-2 .icon-image {
  width: 40rpx; /* 调整图标大小 */
  height: 40rpx; /* 调整图标大小 */
  object-fit: contain;
  filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.3));
}

/* 加载状态样式 */
.empty,
.no-more,
.loading-more {
  display: flex;
  justify-content: center;
  padding: 20rpx 0;
  width: 100%;
  margin-bottom: 20rpx;
}

/* 为loading-more提供较小的高度，避免占用过多空间 */
.loading-more {
  height: 100rpx;
}

/* 为"没有更多了"提示添加淡入淡出动画 */
.no-more {
  animation: fadeIn 0.5s ease;
  opacity: var(--gallery-hint-text-opacity);
}

.empty-text,
.no-more-text {
  font-size: 24rpx;
  color: var(--gallery-hint-text-color);
  opacity: var(--gallery-hint-text-opacity);
}

/* 淡入动画关键帧 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10rpx);
  }
  to {
    opacity: var(--gallery-hint-text-opacity);
    transform: translateY(0);
  }
}

/* 添加旋转动画关键帧 */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 添加脉冲动画关键帧 */
@keyframes galleryPulse {
  0% {
    transform: scale(var(--gallery-pulse-scale-min, 0.95));
    opacity: var(--gallery-pulse-opacity-min, 0.7);
  }
  50% {
    transform: scale(var(--gallery-pulse-scale-max, 1.05));
    opacity: var(--gallery-pulse-opacity-max, 1);
  }
  100% {
    transform: scale(var(--gallery-pulse-scale-min, 0.95));
    opacity: var(--gallery-pulse-opacity-min, 0.7);
  }
}

/* 下拉刷新样式 */
::-webkit-scrollbar {
  width: 0;
  height: 0;
  color: transparent;
}

/* 简化空状态样式 */
.empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
}

.empty-text {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.6);
}

/* 优化加载体验，增加平滑过渡效果 */
.fade-transition {
  transition: opacity 0.3s ease-in-out;
}

/* 增加加载中的内容显示效果 */
.loading-content-placeholder {
  opacity: 0.7;
  position: relative;
}

.loading-content-placeholder::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  animation: loading-shimmer 1.5s infinite;
}

@keyframes loading-shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* 全页面加载状态 */
.loading-full {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300rpx;
  width: 100%;
  margin-top: 100rpx;
}

.loading-full .loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.2);
  border-top: 4rpx solid #ffffff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

.loading-full .loading-text {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.7);
}

/* 全屏加载状态 */
.loading-full-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: transparent; /* 透明背景，显示下方的固定背景层 */
  z-index: 2; /* 在固定背景之上，容器之下 */
}

.loading-full-screen .loading-spinner {
  width: 80rpx;
  height: 80rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.15);
  border-top: 4rpx solid rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
  box-shadow: 0 0 10rpx rgba(0, 0, 0, 0.1);
}

.loading-full-screen .loading-text {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* 修改分类导航栏的样式代码，确保在最上层 */
:host {
  /* 确保导航栏始终在最上层 */
  z-index: 5;
  position: relative;
}

/* 分享按钮容器样式 */
.share-button-container {
  position: absolute;
  top: 25rpx; /* 保持顶部位置不变 */
  right: 20rpx; /* 修改：从30rpx减少到20rpx，使其更靠近右侧边缘 */
  transform: none; /* 保持transform不变 */
  width: 43.35rpx; /* 保持宽度不变 */
  height: 43.35rpx; /* 保持高度不变 */
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 12; /* 修改：从9增加到12，确保它在主标题容器(z-index:10)之上 */
}

/* 添加点击效果 */
.share-button-container:active {
  opacity: 0.7;
  transform: scale(0.95); /* 修改：移除translateX(-50%) */
}