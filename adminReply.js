function adminReply(data) {
  console.log("adminReply接收到的参数:", data);
  
  // 检查基本参数
  if (!data) {
    return { code: 400, message: "缺少参数" };
  }
  
  const { suggestionId, userId, content, images, adminInfo } = data;
  
  // 必须有userId或suggestionId
  if (!userId && !suggestionId) {
    return { code: 400, message: "必须提供用户ID或建议ID" };
  }
  
  return db.runTransaction(async transaction => {
    try {
      // 尝试查找或创建建议
      let suggestion;
      let suggestionRef;
      let newSuggestionCreated = false;
      
      if (suggestionId) {
        // 使用现有建议ID
        suggestionRef = db.collection("suggestions").doc(suggestionId);
        suggestion = await transaction.get(suggestionRef);
        
        // 检查是否存在
        if (!suggestion.data && userId) {
          // 建议不存在但有userId，可以创建
          console.log("建议不存在，创建新建议:", suggestionId);
          suggestion = {
            data: {
              _id: suggestionId,
              userId: userId,
              status: "pending",
              createTime: new Date(),
              updateTime: new Date(),
              conversations: []
            }
          };
          newSuggestionCreated = true;
        } else if (!suggestion.data) {
          return { code: 404, message: "未找到建议，且无法创建新记录" };
        }
      } else if (userId) {
        // 只有userId，创建新建议
        console.log("创建新建议，userId:", userId);
        
        // 生成临时ID进行查询
        const tempId = `temp_${Date.now()}`;
        suggestionRef = db.collection("suggestions").doc(tempId);
        
        suggestion = {
          data: {
            userId: userId,
            status: "pending",
            createTime: new Date(),
            updateTime: new Date(),
            conversations: []
          }
        };
        newSuggestionCreated = true;
      }
      
      // 构建新的对话内容
      const newConversation = {
        id: Date.now().toString(),
        sender: "admin",
        content: content || "",
        createTime: new Date(),
        isRead: false,
        adminInfo: adminInfo || { name: "管理员" }
      };
      
      // 添加图片
      if (images && images.length > 0) {
        newConversation.images = images;
      }
      
      // 添加到对话列表
      const conversations = suggestion.data.conversations || [];
      conversations.push(newConversation);
      
      // 更新或创建建议
      const updateData = {
        status: "replied",
        updateTime: new Date(),
        conversations: conversations
      };
      
      // 如果是新建议，添加userId字段
      if (newSuggestionCreated) {
        updateData.userId = userId;
        
        // 如果是全新建议，需要添加而不是更新
        if (!suggestionId) {
          const result = await db.collection("suggestions").add({
            data: {
              ...updateData,
              createTime: new Date()
            }
          });
          return { 
            code: 0, 
            message: "成功创建新建议并回复", 
            suggestionId: result._id 
          };
        } else {
          // 使用指定ID创建
          await db.collection("suggestions").doc(suggestionId).set({
            data: {
              ...updateData,
              createTime: new Date()
            }
          });
          return { 
            code: 0, 
            message: "成功创建指定ID的建议并回复" 
          };
        }
      } else {
        // 更新现有建议
        await suggestionRef.update({
          data: updateData
        });
        return { code: 0, message: "回复成功" };
      }
    } catch (err) {
      console.error("回复建议失败:", err);
      return { code: 500, message: "回复失败: " + err.message };
    }
  });
}
