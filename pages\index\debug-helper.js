/**
 * 调试辅助工具
 * 用于诊断和修复首页问题
 */

/**
 * 诊断页面状态
 * @param {Object} pageContext - 页面上下文
 */
function diagnosePage(pageContext) {
  console.log('=== 页面诊断开始 ===');
  
  // 检查页面数据
  const data = pageContext.data;
  console.log('页面数据状态:', {
    videoListLength: data.videoList ? data.videoList.length : 0,
    loading: data.loading,
    firstLoading: data.firstLoading,
    showContent: data.showContent,
    isRefreshing: data.isRefreshing,
    hasMore: data.hasMore,
    page: data.page
  });
  
  // 检查模块状态
  if (pageContext.mainController) {
    const moduleStatus = pageContext.mainController.getModulesStatus();
    console.log('模块状态:', moduleStatus);
  } else {
    console.warn('主控制器未初始化');
  }
  
  // 检查API兼容性
  if (pageContext.apiCompatibility) {
    const compatReport = pageContext.apiCompatibility.getCompatibilityReport();
    console.log('API兼容性报告:', compatReport);
  } else {
    console.warn('API兼容层未初始化');
  }
  
  console.log('=== 页面诊断结束 ===');
}

/**
 * 强制刷新页面数据
 * @param {Object} pageContext - 页面上下文
 */
function forceRefreshData(pageContext) {
  console.log('=== 强制刷新数据 ===');
  
  try {
    // 重置页面状态
    pageContext.setData({
      loading: true,
      firstLoading: false,
      showContent: false,
      isRefreshing: false
    });
    
    // 调用视频列表加载
    if (pageContext.apiCompatibility) {
      pageContext.apiCompatibility.handleMethod('loadVideoList', [true]);
    } else if (pageContext.mainController) {
      pageContext.mainController.loadInitialData();
    } else {
      console.error('无法找到数据加载方法');
    }
  } catch (error) {
    console.error('强制刷新失败:', error);
  }
}

/**
 * 修复显示问题
 * @param {Object} pageContext - 页面上下文
 */
function fixDisplayIssues(pageContext) {
  console.log('=== 修复显示问题 ===');
  
  try {
    // 确保内容显示
    pageContext.setData({
      showContent: true,
      loading: false,
      firstLoading: false
    });
    
    // 如果有视频数据但不显示，强制更新
    if (pageContext.data.videoList && pageContext.data.videoList.length > 0) {
      console.log('检测到视频数据，强制更新显示');
      pageContext.setData({
        videoList: [...pageContext.data.videoList] // 触发数据更新
      });
    }
  } catch (error) {
    console.error('修复显示问题失败:', error);
  }
}

/**
 * 检查并修复循环刷新问题
 * @param {Object} pageContext - 页面上下文
 */
function fixRefreshLoop(pageContext) {
  console.log('=== 检查循环刷新问题 ===');
  
  try {
    // 强制停止刷新状态
    if (pageContext.data.isRefreshing) {
      console.log('检测到刷新状态，强制停止');
      pageContext.setData({
        isRefreshing: false
      });
      wx.stopPullDownRefresh();
    }
    
    // 检查是否有重复的定时器或监听器
    // 这里可以添加更多的检查逻辑
    
  } catch (error) {
    console.error('修复循环刷新问题失败:', error);
  }
}

/**
 * 完整的问题修复流程
 * @param {Object} pageContext - 页面上下文
 */
function fixAllIssues(pageContext) {
  console.log('=== 开始完整修复流程 ===');
  
  // 1. 诊断当前状态
  diagnosePage(pageContext);
  
  // 2. 修复循环刷新
  fixRefreshLoop(pageContext);
  
  // 3. 修复显示问题
  fixDisplayIssues(pageContext);
  
  // 4. 如果仍然没有数据，强制刷新
  setTimeout(() => {
    if (!pageContext.data.videoList || pageContext.data.videoList.length === 0) {
      console.log('数据仍然为空，执行强制刷新');
      forceRefreshData(pageContext);
    }
  }, 1000);
  
  console.log('=== 修复流程完成 ===');
}

module.exports = {
  diagnosePage,
  forceRefreshData,
  fixDisplayIssues,
  fixRefreshLoop,
  fixAllIssues
};