Component({
  properties: {
    show: {
      type: Boolean,
      value: false
    },
    type: {
      type: String,
      value: 'default' // 可选: default, transparent
    },
    size: {
      type: String,
      value: 'medium' // 可选: small, medium, large
    }
  },
  
  data: {
    sizeClass: 'medium'
  },
  
  observers: {
    'size': function(size) {
      // 根据size属性设置对应的CSS类
      this.setData({
        sizeClass: size || 'medium'
      });
    }
  }
}); 