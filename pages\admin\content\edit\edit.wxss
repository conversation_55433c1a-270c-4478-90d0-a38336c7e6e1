.edit-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
}

.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  background-color: #ffffff;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  position: sticky;
  top: 0;
  z-index: 100;
}

.back-icon {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  color: #333333;
}

.header-title {
  font-size: 34rpx;
  font-weight: bold;
  color: #333333;
}

.save-btn {
  font-size: 30rpx;
  color: #ff9a9e;
}

.loading-container {
  padding: 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.loading-spinner {
  width: 80rpx;
  height: 80rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #ff9a9e;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 20rpx;
  color: #999999;
  font-size: 28rpx;
}

.form-container {
  flex: 1;
  padding: 20rpx;
}

.form-group {
  margin-bottom: 30rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.form-label {
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 15rpx;
}

.form-input {
  height: 80rpx;
  background-color: #f5f5f5;
  border-radius: 12rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
}

.form-textarea {
  width: 100%;
  height: 200rpx;
  background-color: #f5f5f5;
  border-radius: 12rpx;
  padding: 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}

.image-uploader {
  width: 100%;
  height: 300rpx;
  background-color: #f5f5f5;
  border-radius: 12rpx;
  overflow: hidden;
  position: relative;
}

.banner-uploader {
  height: 200rpx;
}

.preview-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.upload-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.upload-icon {
  font-size: 50rpx;
  color: #999999;
  margin-bottom: 10rpx;
}

.upload-text {
  font-size: 26rpx;
  color: #999999;
}

.upload-progress {
  margin-top: 10rpx;
}

.progress-bar {
  width: 100%;
  height: 6rpx;
  background-color: #f0f0f0;
  border-radius: 3rpx;
  overflow: hidden;
}

.progress-inner {
  height: 100%;
  background: linear-gradient(to right, #ff9a9e, #fad0c4);
  transition: width 0.3s;
}

.progress-text {
  font-size: 24rpx;
  color: #999999;
  text-align: right;
  margin-top: 5rpx;
}

.status-switch {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.status-text {
  font-size: 28rpx;
  color: #333333;
}

.switch-container {
  width: 80rpx;
  height: 40rpx;
  background-color: #e0e0e0;
  border-radius: 20rpx;
  position: relative;
  transition: background-color 0.3s;
}

.switch-container.active {
  background-color: #ff9a9e;
}

.switch-circle {
  width: 36rpx;
  height: 36rpx;
  background-color: #ffffff;
  border-radius: 50%;
  position: absolute;
  top: 2rpx;
  left: 2rpx;
  transition: transform 0.3s;
  box-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.1);
}

.switch-container.active .switch-circle {
  transform: translateX(40rpx);
}

.submit-bar {
  padding: 20rpx 30rpx 40rpx;
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(to right, #ff9a9e, #fad0c4);
  color: #ffffff;
  border-radius: 12rpx;
  font-size: 32rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.btn-hover {
  opacity: 0.8;
} 