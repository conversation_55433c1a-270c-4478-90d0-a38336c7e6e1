/**
 * 全局配置文件
 * 所有云函数和前端共用的配置项
 */

// 云环境ID - 从环境变量读取，如未设置则使用默认值
exports.ENV_ID = process.env.CLOUD_ENV || 'cloud1-3gn5xcxz610ebe61';

// 小程序ID - 从环境变量读取，如未设置则使用默认值
exports.APP_ID = process.env.APP_ID || 'wx03b63fba056c0b08';

// 云存储配置
exports.CLOUD_STORAGE_CONFIG = {
  // COS 配置 - 从环境变量读取
  secretId: process.env.COS_SECRET_ID || 'AKIDaCaiaBXcRzIL8fuuf9NN8drpWx83i6E7',
  secretKey: process.env.COS_SECRET_KEY || 'g6FMIxMVNyINFrCH5dv63kAKErEO2Vkl',
  bucket: process.env.COS_BUCKET || 'naildidi-1360370592',
  region: process.env.COS_REGION || 'ap-guangzhou',
  
  // 存储桶访问域名 - 从环境变量读取或使用默认值
  baseUrl: process.env.COS_BASE_URL || 'https://naildidi-1360370592.cos.ap-guangzhou.myqcloud.com/',
  
  // 临时链接有效期（秒）- 从环境变量读取或使用默认值
  tempUrlExpire: parseInt(process.env.COS_TEMP_URL_EXPIRE || '3600'),
  
  // 文件夹配置 - 从环境变量读取或使用默认值
  dirs: {
    video: process.env.VIDEO_DIR || '视频列表',            // 视频文件文件夹
    cover: process.env.COVER_DIR || '视频列表封面图',      // 视频封面图文件夹
    launch: process.env.LAUNCH_DIR || '启动页',           // 启动页图片文件夹
    article: process.env.DETAIL_DIR || '视频列表详情'     // 视频详情HTML文件夹
  },
  
  // 支持的文件格式 - 从环境变量读取或使用默认值
  supportedFormats: {
    video: (process.env.SUPPORTED_VIDEO_FORMATS || '.mp4,.mov').split(','),
    image: (process.env.SUPPORTED_IMAGE_FORMATS || '.webp,.jpg,.jpeg,.png').split(',')
  }
};

// 文件命名规范
exports.FILE_NAMING = {
  // 启动页图片命名规范
  launch: {
    // 图片名称（不含扩展名）- 必须使用这两个名称
    first: 'launch1',    // 第一张启动图
    second: 'launch2',   // 第二张启动图
    // 支持的图片格式（按优先级排序）
    formats: ['webp', 'jpg', 'jpeg', 'png']
  },
  
  // 视频文件命名规范 - 格式: 序号_标题_副标题_播放量_video.mp4
  // 示例: 001_冥想音乐_轻音乐_2000_video.mp4
  video: {
    pattern: '{序号}_{标题}_{副标题}_{播放量}_video.mp4',
    example: '001_冥想音乐_轻音乐_2000_video.mp4'
  },
  
  // 视频封面图命名规范 - 必须与对应视频文件的前缀保持一致
  // 示例: 001_冥想音乐_轻音乐_2000_cover.jpg
  cover: {
    pattern: '{序号}_{标题}_{副标题}_{播放量}_cover.jpg',
    example: '001_冥想音乐_轻音乐_2000_cover.jpg'
  },
  
  // 视频详情HTML文件命名规范 - 使用视频序号命名
  // 示例: 001.html
  article: {
    pattern: '{序号}.html',
    example: '001.html'
  }
};

// 启动页配置
exports.LAUNCH_CONFIG = {
  // 启动页图片配置
  images: {
    // 图片名称（不含扩展名）
    first: exports.FILE_NAMING.launch.first,
    second: exports.FILE_NAMING.launch.second,
    // 支持的图片格式（按优先级排序）
    formats: exports.FILE_NAMING.launch.formats
  },
  // 动画时间配置（毫秒）
  animation: {
    displayDuration: 1500,     // 第一张图片显示时长(1.5秒)
    transitionDuration: 2300,  // 过渡动画时长(2.3秒)
    autoJumpDelay: 3000,       // 自动跳转延时(3秒)
    fadeOutDuration: 800,      // 淡出动画持续时间
    imageTransitionDelay: 700  // 图片切换延迟(0.7秒)
  }
};

// 网络请求配置
exports.REQUEST_CONFIG = {
  timeout: parseInt(process.env.REQUEST_TIMEOUT || '10000'),
  header: {
    'content-type': 'application/json'
  },
  // 缓存配置
  cache: {
    enable: process.env.ENABLE_CACHE !== 'false',              // 是否启用缓存
    maxAge: parseInt(process.env.CACHE_MAX_AGE || '3600000'),  // 缓存时间（毫秒）
    maxSize: parseInt(process.env.CACHE_MAX_SIZE || '50')      // 最大缓存条数
  }
}; 

// 视频管理配置
exports.VIDEO_CONFIG = {
  // 数据库配置
  db: {
    collection: process.env.VIDEO_COLLECTION || 'video_list',   // 视频集合名称
    sortField: process.env.VIDEO_SORT_FIELD || 'sortOrder',     // 排序字段名
    sortDirection: process.env.VIDEO_SORT_DIRECTION || 'asc'    // 排序方向：asc或desc
  },
  
  // 分页配置
  pagination: {
    frontendPageSize: parseInt(process.env.FRONTEND_PAGE_SIZE || '10'),  // 前端每页视频数
    adminPageSize: parseInt(process.env.ADMIN_PAGE_SIZE || '20')         // 后台每页视频数
  },
  
  // 存储配置
  storage: {
    // 存储路径配置
    paths: {
      videos: process.env.VIDEO_DIR ? `${process.env.VIDEO_DIR}/` : '视频列表/',      // 视频文件路径
      covers: process.env.COVER_DIR ? `${process.env.COVER_DIR}/` : '视频列表封面图/', // 封面图路径
      details: process.env.DETAIL_DIR ? `${process.env.DETAIL_DIR}/` : '视频列表详情/' // 详情图路径
    },
    // 临时链接有效期（秒）
    urlExpiration: parseInt(process.env.VIDEO_URL_EXPIRATION || '7200')
  }
};

// 管理员配置
exports.ADMIN_CONFIG = {
  // 管理员OpenID列表
  adminOpenIds: (process.env.ADMIN_OPENIDS || '').split(',').filter(id => id),
  
  // 默认至少包含云函数创建者为管理员
  defaultAdmin: true,
  
  // 管理操作日志配置
  logging: {
    enable: process.env.ADMIN_LOG_ENABLE !== 'false',     // 是否启用操作日志
    collection: process.env.ADMIN_LOG_COLLECTION || 'admin_logs' // 日志集合名称
  }
}; 