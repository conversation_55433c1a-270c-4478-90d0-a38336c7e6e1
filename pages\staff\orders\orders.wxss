/* 员工订单页面样式 */
.orders-container {
  background-color: #f5f5f5;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  height: 100vh;
}

/* 顶部状态栏样式 */
.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  padding-top: 90rpx;
  background-color: #ffffff;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.back-icon {
  font-size: 40rpx;
  color: #333333;
  width: 60rpx;
}

.header-title {
  font-size: 34rpx;
  font-weight: bold;
  color: #333333;
  flex: 1;
  text-align: center;
  position: relative;
  padding-bottom: 10rpx;
}

.header-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 6rpx;
  background-color: #333333;
  border-radius: 3rpx;
}

.placeholder {
  width: 60rpx;
}

/* 新的标题栏样式 */
.title-bar {
  background-color: #ffffff;
  padding: 25rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  display: flex;
  justify-content: center;
}

.title-text {
  font-size: 30rpx;
  font-weight: bold;
  color: #333333;
  position: relative;
  padding-bottom: 10rpx;
}

.title-text::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 6rpx;
  background-color: #333333;
  border-radius: 3rpx;
}

/* 滚动区域样式 - 调整高度 */
.content-scroll {
  flex: 1;
  height: calc(100vh - 130rpx); /* 减去头部的高度，因为移除了标题栏 */
}

/* 内容区域样式 */
.content {
  padding: 30rpx;
}

/* 加载中样式 */
.loading {
  padding: 100rpx 0;
  text-align: center;
}

.loading-icon {
  display: inline-block;
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #ff6b81;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #999999;
  margin-top: 20rpx;
}

/* 空状态样式 */
.empty-state {
  padding: 100rpx 0;
  text-align: center;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 32rpx;
  color: #333333;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.empty-tips {
  font-size: 26rpx;
  color: #999999;
}

/* 订单列表样式 */
.order-list {
  padding-bottom: 40rpx;
}

.order-item {
  background-color: #ffffff;
  border-radius: 16rpx;
  margin-bottom: 30rpx;
  padding: 30rpx;
  padding-bottom: 40rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.order-number {
  font-size: 28rpx;
  color: #333333;
  font-weight: bold;
}

.order-status {
  font-size: 24rpx;
  padding: 4rpx 12rpx;
  border-radius: 4rpx;
  background-color: rgba(0, 112, 201, 0.1);
  color: #0070c9;
}

.order-status.completed {
  background-color: rgba(52, 199, 89, 0.1);
  color: #34c759;
}

.order-status.cancelled {
  background-color: rgba(255, 59, 48, 0.1);
  color: #ff3b30;
}

/* 支付方式标签样式 */
.payment-method {
  font-size: 24rpx;
  padding: 4rpx 12rpx;
  border-radius: 4rpx;
  text-align: center;
}

.payment-method.cash {
  background-color: rgba(0, 112, 201, 0.1);
  color: #0070c9;
}

.payment-method.balance {
  background-color: rgba(76, 175, 80, 0.1);
  color: #4caf50;
}

.order-info {
  padding: 20rpx 0 0;
}

.info-row {
  display: flex;
  margin-bottom: 16rpx;
}

.info-label {
  width: 160rpx;
  font-size: 26rpx;
  color: #666666;
}

.info-value {
  flex: 1;
  font-size: 26rpx;
  color: #333333;
}

/* 提成金额相关样式 */
.info-value.commission {
  color: #ff6b81;
  font-weight: bold;
}

.info-value.total-price {
  color: #333333;
  font-weight: bold;
}

.order-price-container {
  display: flex;
  align-items: center;
}

.price-label {
  font-size: 26rpx;
  color: #666666;
  margin-right: 10rpx;
}

.price-summary {
  font-size: 26rpx;
  color: #333333;
  font-weight: bold;
}

.price-summary .price-text {
  color: #ff6b81;
}

.order-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}

.order-time {
  font-size: 24rpx;
  color: #999999;
}

.order-price {
  font-size: 32rpx;
  font-weight: bold;
  color: #ff6b81;
}