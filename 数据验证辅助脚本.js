/**
 * 小程序数据统计验证辅助脚本
 * 用于验证各模块数据的准确性和一致性
 */

// 云函数调用辅助函数
function callCloudFunction(name, data) {
  return new Promise((resolve, reject) => {
    wx.cloud.callFunction({
      name: name,
      data: data,
      success: resolve,
      fail: reject
    });
  });
}

/**
 * 验证员工提成计算
 * @param {string} staffId 员工ID
 * @param {number} servicePrice 服务价格
 * @param {string} paymentMethod 支付方式 'cash' | 'balance'
 * @param {number} expectedCommission 预期提成
 */
async function validateCommissionCalculation(staffId, servicePrice, paymentMethod, expectedCommission) {
  try {
    // 获取员工信息
    const staffResult = await callCloudFunction('staffManager', {
      type: 'admin',
      action: 'getStaffDetail',
      data: { staffId }
    });
    
    if (staffResult.result.code !== 0) {
      return { success: false, message: '获取员工信息失败' };
    }
    
    const staff = staffResult.result.data.staff;
    const commissionRate = paymentMethod === 'balance' 
      ? staff.balanceCommissionRate 
      : staff.commissionRate;
    
    const calculatedCommission = servicePrice * commissionRate;
    const isCorrect = Math.abs(calculatedCommission - expectedCommission) < 0.01;
    
    return {
      success: isCorrect,
      data: {
        staffName: staff.name,
        servicePrice,
        paymentMethod,
        commissionRate,
        calculatedCommission,
        expectedCommission,
        difference: calculatedCommission - expectedCommission
      },
      message: isCorrect ? '提成计算正确' : '提成计算错误'
    };
  } catch (error) {
    return { success: false, message: '验证过程出错: ' + error.message };
  }
}

/**
 * 验证员工业绩统计数据
 * @param {string} staffId 员工ID
 * @param {string} startDate 开始日期
 * @param {string} endDate 结束日期
 */
async function validateStaffPerformance(staffId, startDate, endDate) {
  try {
    // 获取员工业绩统计
    const performanceResult = await callCloudFunction('staffManager', {
      type: 'admin',
      action: 'getBusinessStats',
      data: { staffId, startDate, endDate }
    });
    
    if (performanceResult.result.code !== 0) {
      return { success: false, message: '获取业绩统计失败' };
    }
    
    const businessData = performanceResult.result.data;
    
    // 直接查询staff_performance集合进行对比
    const directQuery = await wx.cloud.database()
      .collection('staff_performance')
      .where({
        staffId: staffId,
        completeTime: wx.cloud.database().command.and(
          wx.cloud.database().command.gte(startDate),
          wx.cloud.database().command.lte(endDate)
        )
      })
      .get();
    
    // 手工计算统计数据
    let manualStats = {
      totalOrders: 0,
      totalCashIncome: 0,
      totalBalanceIncome: 0,
      totalCashCommission: 0,
      totalBalanceCommission: 0,
      totalCommission: 0
    };
    
    directQuery.data.forEach(record => {
      manualStats.totalOrders++;
      if (record.paymentMethod === 'balance') {
        manualStats.totalBalanceIncome += record.servicePrice;
        manualStats.totalBalanceCommission += record.commission;
      } else {
        manualStats.totalCashIncome += record.servicePrice;
        manualStats.totalCashCommission += record.commission;
      }
      manualStats.totalCommission += record.commission;
    });
    
    // 对比统计结果
    const comparison = {
      totalOrders: {
        system: businessData.totalOrders,
        manual: manualStats.totalOrders,
        match: businessData.totalOrders === manualStats.totalOrders
      },
      totalCashIncome: {
        system: businessData.totalCashIncome,
        manual: manualStats.totalCashIncome,
        match: Math.abs(businessData.totalCashIncome - manualStats.totalCashIncome) < 0.01
      },
      totalBalanceIncome: {
        system: businessData.totalBalanceIncome,
        manual: manualStats.totalBalanceIncome,
        match: Math.abs(businessData.totalBalanceIncome - manualStats.totalBalanceIncome) < 0.01
      },
      totalCommission: {
        system: businessData.totalCommission,
        manual: manualStats.totalCommission,
        match: Math.abs(businessData.totalCommission - manualStats.totalCommission) < 0.01
      }
    };
    
    const allMatch = Object.values(comparison).every(item => item.match);
    
    return {
      success: allMatch,
      data: {
        systemStats: businessData,
        manualStats: manualStats,
        comparison: comparison
      },
      message: allMatch ? '员工业绩统计数据一致' : '员工业绩统计数据不一致'
    };
    
  } catch (error) {
    return { success: false, message: '验证过程出错: ' + error.message };
  }
}

/**
 * 验证充值统计数据
 * @param {string} startDate 开始日期
 * @param {string} endDate 结束日期
 */
async function validateRechargeStats(startDate, endDate) {
  try {
    // 获取充值统计
    const rechargeResult = await callCloudFunction('rechargeManager', {
      type: 'admin',
      action: 'getRechargeStats',
      data: { startDate, endDate }
    });
    
    if (rechargeResult.result.code !== 0) {
      return { success: false, message: '获取充值统计失败' };
    }
    
    // 直接查询充值记录
    const directQuery = await wx.cloud.database()
      .collection('recharge_records')
      .where({
        status: 'verified',
        verifyTime: wx.cloud.database().command.and(
          wx.cloud.database().command.gte(new Date(startDate)),
          wx.cloud.database().command.lte(new Date(endDate))
        )
      })
      .get();
    
    // 手工计算
    let manualStats = {
      totalAmount: 0,
      totalBonus: 0,
      totalCount: 0
    };
    
    directQuery.data.forEach(record => {
      manualStats.totalAmount += record.amount;
      manualStats.totalBonus += record.bonus || 0;
      manualStats.totalCount++;
    });
    
    const systemStats = rechargeResult.result.data.stats;
    const comparison = {
      totalAmount: Math.abs(systemStats.totalAmount - manualStats.totalAmount) < 0.01,
      totalBonus: Math.abs(systemStats.totalBonus - manualStats.totalBonus) < 0.01,
      totalCount: systemStats.totalCount === manualStats.totalCount
    };
    
    const allMatch = Object.values(comparison).every(match => match);
    
    return {
      success: allMatch,
      data: {
        systemStats,
        manualStats,
        comparison
      },
      message: allMatch ? '充值统计数据一致' : '充值统计数据不一致'
    };
    
  } catch (error) {
    return { success: false, message: '验证过程出错: ' + error.message };
  }
}

/**
 * 验证用户余额变化
 * @param {string} openid 用户openid
 * @param {number} expectedBalance 预期余额
 */
async function validateUserBalance(openid, expectedBalance) {
  try {
    const userResult = await callCloudFunction('rechargeManager', {
      type: 'user',
      action: 'getUserBalance',
      openid: openid
    });
    
    if (userResult.result.code !== 0) {
      return { success: false, message: '获取用户余额失败' };
    }
    
    const actualBalance = userResult.result.data.totalBalance;
    const isCorrect = Math.abs(actualBalance - expectedBalance) < 0.01;
    
    return {
      success: isCorrect,
      data: {
        expectedBalance,
        actualBalance,
        difference: actualBalance - expectedBalance
      },
      message: isCorrect ? '用户余额正确' : '用户余额不正确'
    };
    
  } catch (error) {
    return { success: false, message: '验证过程出错: ' + error.message };
  }
}

/**
 * 综合数据一致性验证
 * @param {Object} testConfig 测试配置
 */
async function comprehensiveValidation(testConfig) {
  const results = {
    commissionTests: [],
    performanceTests: [],
    rechargeTests: [],
    balanceTests: [],
    summary: {
      totalTests: 0,
      passedTests: 0,
      failedTests: 0
    }
  };
  
  try {
    // 提成计算验证
    if (testConfig.commissionTests) {
      for (const test of testConfig.commissionTests) {
        const result = await validateCommissionCalculation(
          test.staffId, 
          test.servicePrice, 
          test.paymentMethod, 
          test.expectedCommission
        );
        results.commissionTests.push({ ...test, result });
        results.summary.totalTests++;
        if (result.success) results.summary.passedTests++;
        else results.summary.failedTests++;
      }
    }
    
    // 员工业绩验证
    if (testConfig.performanceTests) {
      for (const test of testConfig.performanceTests) {
        const result = await validateStaffPerformance(
          test.staffId, 
          test.startDate, 
          test.endDate
        );
        results.performanceTests.push({ ...test, result });
        results.summary.totalTests++;
        if (result.success) results.summary.passedTests++;
        else results.summary.failedTests++;
      }
    }
    
    // 充值统计验证
    if (testConfig.rechargeTests) {
      for (const test of testConfig.rechargeTests) {
        const result = await validateRechargeStats(test.startDate, test.endDate);
        results.rechargeTests.push({ ...test, result });
        results.summary.totalTests++;
        if (result.success) results.summary.passedTests++;
        else results.summary.failedTests++;
      }
    }
    
    // 用户余额验证
    if (testConfig.balanceTests) {
      for (const test of testConfig.balanceTests) {
        const result = await validateUserBalance(test.openid, test.expectedBalance);
        results.balanceTests.push({ ...test, result });
        results.summary.totalTests++;
        if (result.success) results.summary.passedTests++;
        else results.summary.failedTests++;
      }
    }
    
    results.summary.successRate = (results.summary.passedTests / results.summary.totalTests * 100).toFixed(2) + '%';
    
    return results;
    
  } catch (error) {
    return { success: false, message: '综合验证过程出错: ' + error.message };
  }
}

/**
 * 生成测试报告
 * @param {Object} validationResults 验证结果
 */
function generateTestReport(validationResults) {
  let report = '# 数据统计验证测试报告\n\n';
  report += `## 测试概要\n`;
  report += `- 总测试数: ${validationResults.summary.totalTests}\n`;
  report += `- 通过测试: ${validationResults.summary.passedTests}\n`;
  report += `- 失败测试: ${validationResults.summary.failedTests}\n`;
  report += `- 成功率: ${validationResults.summary.successRate}\n\n`;
  
  // 提成计算测试结果
  if (validationResults.commissionTests.length > 0) {
    report += '## 提成计算测试结果\n';
    validationResults.commissionTests.forEach((test, index) => {
      report += `### 测试 ${index + 1}: ${test.result.success ? '✅ 通过' : '❌ 失败'}\n`;
      report += `- 员工: ${test.result.data?.staffName || 'N/A'}\n`;
      report += `- 服务价格: ${test.servicePrice}元\n`;
      report += `- 支付方式: ${test.paymentMethod}\n`;
      report += `- 预期提成: ${test.expectedCommission}元\n`;
      if (test.result.data) {
        report += `- 实际提成: ${test.result.data.calculatedCommission}元\n`;
        report += `- 差异: ${test.result.data.difference}元\n`;
      }
      report += `- 结果: ${test.result.message}\n\n`;
    });
  }
  
  // 员工业绩测试结果
  if (validationResults.performanceTests.length > 0) {
    report += '## 员工业绩统计测试结果\n';
    validationResults.performanceTests.forEach((test, index) => {
      report += `### 测试 ${index + 1}: ${test.result.success ? '✅ 通过' : '❌ 失败'}\n`;
      report += `- 时间范围: ${test.startDate} 至 ${test.endDate}\n`;
      report += `- 结果: ${test.result.message}\n\n`;
    });
  }
  
  return report;
}

// 使用示例
const testConfig = {
  commissionTests: [
    { staffId: 'staff_id_1', servicePrice: 100, paymentMethod: 'cash', expectedCommission: 30 },
    { staffId: 'staff_id_1', servicePrice: 200, paymentMethod: 'balance', expectedCommission: 40 }
  ],
  performanceTests: [
    { staffId: 'staff_id_1', startDate: '2025-01-01', endDate: '2025-01-31' }
  ],
  rechargeTests: [
    { startDate: '2025-01-01', endDate: '2025-01-31' }
  ],
  balanceTests: [
    { openid: 'user_openid_1', expectedBalance: 120 }
  ]
};

// 导出函数供外部使用
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    validateCommissionCalculation,
    validateStaffPerformance,
    validateRechargeStats,
    validateUserBalance,
    comprehensiveValidation,
    generateTestReport
  };
}
