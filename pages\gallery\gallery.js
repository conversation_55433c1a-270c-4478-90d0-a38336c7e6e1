// 获取应用实例
const app = getApp()
const { ENV_ID } = require('../../config/index')

Page({
  data: {
    articles: [], // 当前显示的文章列表
    loading: false, // 加载状态
    overflowIndices: [], // 文本溢出状态
    showNoMoreTip: false, // 是否显示"没有更多了"提示
    noMoreTipTimer: null,  // 提示计时器
    showContent: false, // 内容显示动画控制，默认为false，加载完成后设置为true
    isFirstLoad: true, // 是否首次加载
    isFromTab: false,  // 是否从Tab切换进入，类似于主页的isFromLaunch

    isRefreshing: false, // 是否正在下拉刷新
    scrollTop: 0, // 滚动位置
    
    // 分类相关
    currentCategory: 'all', // 当前分类
    categoryList: [
      { id: 'all', name: '全部' }
      // 其他分类将从云函数动态获取
    ],
    
    // 缓存数据
    allArticles: [], // 所有已加载的文章
    categoryArticles: {}, // 按分类存储的文章缓存
    touchStartY: 0,
    
    // 分页相关
    currentPage: 1,
    pageSize: 10,
    hasMore: true,
    
    // 添加app引用以便在wxml中访问globalData
    app: getApp(),

    isCategoryLoading: false,
    lastRefreshTime: null,
    sharedArticleId: null,
    
    // 下拉刷新相关
    refresherPullingDistance: 0, // 下拉距离

    // 指向广告相关
    showTargetAd: false,
    targetAdData: {
      imageUrl: '',
      jumpUrl: ''
    },

    // 画廊详情弹窗相关
    showGalleryDetail: false,
    selectedGallery: null
  },

  onLoad(options) {
    const app = getApp();
    
    // 保存app引用到this.app，而不是重新定义app变量
    this.app = app;
    
    console.log('画廊页面加载，参数:', options);
    
    // 引入设备工具函数
    const deviceUtils = require('../../utils/device.js');
    
    // 处理分享参数
    if (options.sharer && options.contentType && options.contentId) {
      console.log('检测到分享参数:', {
        sharer: options.sharer,
        contentType: options.contentType,
        contentId: options.contentId
      });
      
      // 获取设备标识 - 优先使用URL中传递的设备标识，如果没有则生成新的
      let deviceIdentifier;
      if (options.deviceIdentifier) {
        // 使用URL中传递的设备标识
        deviceIdentifier = decodeURIComponent(options.deviceIdentifier);
        console.log('使用URL传递的设备标识:', deviceIdentifier);
      } else {
        // 生成新的设备标识
        deviceIdentifier = deviceUtils.getDeviceIdentifier();
        console.log('生成新的设备标识:', deviceIdentifier);
      }
      
      // 验证分享并奖励积分
      wx.cloud.callFunction({
        name: 'pointsManager',
        data: {
          action: 'verifyShareAndAddPoints',
          data: {
            sharer: options.sharer,
            deviceIdentifier: deviceIdentifier,
            contentId: options.contentId,
            contentType: options.contentType
          }
        }
      }).then(res => {
        console.log('分享验证结果:', res.result);
        if (res.result && res.result.code === 0) {
          if (res.result.alreadyRewarded) {
            console.log('该内容已被此设备打开过，不重复奖励积分');
          } else {
            console.log('分享积分添加成功，积分:', res.result.pointsAdded);
          }
        } else {
          console.error('分享积分未添加:', res.result ? res.result.message : '未知原因');
        }
      }).catch(err => {
        console.error('分享验证失败:', err);
      });
    }
    
    // 如果有文章ID参数，打开文章详情
    if (options.articleId) {
      console.log('检测到文章ID参数:', options.articleId);
      this.setData({
        sharedArticleId: options.articleId
      });
      
      // 延迟加载文章详情，确保页面已经准备好
      setTimeout(() => {
        this.openArticleById(options.articleId);
      }, 500);
    }
    
    // 标记gallery加载已开始，防止后续重复加载
    if (app && app.globalData) {
      app.globalData.isTabSwitching = false; // 先重置切换标记，避免状态不一致
    }
    
    // 先设置页面显示状态，再进行数据加载以避免白屏
    const isFirstLoad = !(app && app.globalData && app.globalData.galleryLoaded);
    
    // 检查是否是从启动页跳转而来
    const isFromLaunch = app && app.globalData && app.globalData.isFromLaunch;
    
    // 检查是否是从Tab切换进入 - 优先使用全局状态
    const isFromTab = app && app.globalData && (app.globalData.isTabSwitching || app.globalData.currentTabIndex === 0);
    
    if (app.globalData.debugMode) {
      console.log('加载状态：isFirstLoad=', isFirstLoad, 'isFromLaunch=', isFromLaunch, 'isFromTab=', isFromTab);
    }
    
    // 修改：总是立即显示内容，不管来源
    this.setData({
      showContent: true, // 始终设置为true，立即显示内容
      isFirstLoad: isFirstLoad,
      isFromTab: isFromTab || isFromLaunch
    }, () => {
      if (app.globalData.debugMode) {
        console.log('onLoad设置showContent=', this.data.showContent);
      }
    });
    
    // 确保云开发已初始化，但不在此处等待
    if (!wx.cloud) {
      console.error('请使用 2.2.3 或以上的基础库以使用云能力');
      return;
    }
    
    // 异步初始化云环境
    wx.cloud.init({
      env: ENV_ID,
      traceUser: true
    });
    
    // 检查是否有预加载的画廊数据
    if (isFromLaunch && app.globalData.galleryArticles) {
      console.log('检测到启动页预加载的画廊数据，直接使用');
      
      // 使用预加载的画廊数据
      this.setData({
        allArticles: app.globalData.galleryArticles.allArticles || [],
        articles: app.globalData.galleryArticles.currentArticles || [],
        categoryArticles: {
          'all': app.globalData.galleryArticles.allArticles || []
        },
        loading: false,
        showContent: true
      }, () => {
              console.log('已使用预加载数据，文章数量:', this.data.articles.length);
      
      // 如果文章数量为0，确保不会一直显示加载状态
      if (this.data.articles.length === 0) {
        setTimeout(() => {
          this.setData({
            loading: false,
            showContent: true
          });
        }, 1000); // 1秒后自动结束加载状态
      }
      
      // 加载分类列表，但不阻塞内容显示
        this.loadCategoryList().then(categoryList => {
          if (app.globalData.debugMode) {
            console.log('分类列表加载完成:', categoryList);
          }
        }).catch(err => {
          console.error('分类列表加载失败:', err);
        });
        
        // 在后台继续加载更多数据，延长延迟时间，避免影响初始渲染
        setTimeout(() => {
          this.loadArticlesInBackground(false);
        }, 5000); // 增加到5秒，让页面有充分时间完成初始渲染
        
        // 触发文章准备就绪事件
        this.onArticlesReady();
      });
    } else {
      // 没有预加载数据，走正常加载流程
      // 加载分类列表
      this.loadCategoryList().then(categoryList => {
        if (app.globalData.debugMode) {
          console.log('分类列表加载完成:', categoryList);
        }
        
        // 启动数据加载的异步任务
        this._initializePageData(isFirstLoad, isFromTab || isFromLaunch);
      }).catch(err => {
        console.error('分类列表加载失败:', err);
        this.setData({ showContent: true }); // 出错时也显示页面内容
        wx.showToast({
          title: '加载分类列表失败，请重试',
          icon: 'none'
        });
      });
    }
    
    // 移除延时定时器，确保内容立即显示
    // 如果需要确保内容显示，直接设置，不使用定时器
    if (!this.data.showContent) {
      if (app.globalData.debugMode) {
        console.log('确保showContent为true，立即显示内容');
      }
      this.setData({ showContent: true });
    }

    // 监听指向广告显示事件
    this.setupTargetAdListener();

    // 监听画廊详情显示事件
    this.setupGalleryDetailListener();
  },
  
  // 加载分类列表
  loadCategoryList() {
    if (app.globalData.debugMode) {
      console.log('开始加载分类列表');
    }
    
    // 添加加载状态标记
    this.setData({
      isCategoryLoading: true
    });
    
    // 使用Promise包装云函数调用，方便处理结果
    return new Promise((resolve, reject) => {
      // 添加超时处理
      const timeoutId = setTimeout(() => {
        console.error('加载分类列表超时');
        this.setData({
          isCategoryLoading: false
        });
        this.setDefaultCategories();
        reject(new Error('加载分类列表超时'));
      }, 10000); // 10秒超时
      
      wx.cloud.callFunction({
        name: 'galleryManager',
        data: {
          action: 'getCategoryList'
        }
      }).then(res => {
        clearTimeout(timeoutId); // 清除超时定时器
        if (app.globalData.debugMode) {
          console.log('获取分类列表响应成功，分类数量:', 
            res.result && res.result.data ? res.result.data.length : 0);
        }
        
        if (res.result && res.result.success && res.result.data) {
          // 转换分类列表格式
          const serverCategories = res.result.data || [];
          if (app.globalData.debugMode) {
            console.log('服务器返回分类数量:', serverCategories.length);
          }
          
          const categories = serverCategories.map(item => ({
            id: item._id,
            name: item.name
          }));
          
          // 添加"全部"分类
          const categoryList = [
            { id: 'all', name: '全部' },
            ...categories
          ];
          
          if (app.globalData.debugMode) {
            console.log('处理后的分类列表:', categoryList);
          }
          
          // 更新分类列表
          this.setData({
            categoryList: categoryList,
            isCategoryLoading: false
          });
          
          // 初始化分类缓存
          const categoryArticles = {};
          categoryList.forEach(category => {
            categoryArticles[category.id] = [];
          });
          
          this.setData({ categoryArticles }, () => {
            if (app.globalData.debugMode) {
              console.log('分类数据更新完成');
            }
          });
          
          resolve(categoryList);
        } else {
          console.error('获取分类列表失败:', res.result ? res.result.message : '未知错误');
          this.setData({
            isCategoryLoading: false
          });
          // 使用默认分类列表
          this.setDefaultCategories();
          reject(new Error('获取分类列表返回格式错误'));
        }
      }).catch(err => {
        clearTimeout(timeoutId); // 清除超时定时器
        console.error('获取分类列表失败:', err);
        this.setData({
          isCategoryLoading: false
        });
        // 使用默认分类列表
        this.setDefaultCategories();
        reject(err);
      });
    }).catch(err => {
      if (app.globalData.debugMode) {
        console.log('分类列表加载处理异常:', err.message);
      }
      return this.data.categoryList; // 返回当前分类列表
    });
  },
  
  // 设置默认分类列表
  setDefaultCategories() {
    const defaultCategoryList = [
      { id: 'all', name: '全部' },
      { id: 'type1', name: '轻奢美甲' }, 
      { id: 'type2', name: '美睫' },
      { id: 'type3', name: '纹绣' },
      { id: 'type4', name: '皮肤管理' }
    ];
    
    this.setData({
      categoryList: defaultCategoryList
    });
    
    // 初始化分类缓存
    const categoryArticles = {};
    defaultCategoryList.forEach(category => {
      categoryArticles[category.id] = [];
    });
    
    this.setData({ categoryArticles });
  },
  
  // 分离数据加载逻辑到独立函数，避免阻塞UI
  async _initializePageData(isFirstLoad, isFromTab) {
    try {
      // 记录开始时间
      const startTime = Date.now();
      
      if (app.globalData.debugMode) {
        console.log('初始化页面数据, isFirstLoad=', isFirstLoad, 'isFromTab=', isFromTab);
      }
      
      // 尝试从全局状态恢复数据，避免重复加载
      if (!isFirstLoad || isFromTab) {
        if (app && app.globalData && app.globalData.galleryArticles) {
          if (app.globalData.debugMode) {
            console.log('从全局缓存恢复数据');
          }
          
          // 从全局状态恢复数据
          this.setData({
            allArticles: app.globalData.galleryArticles.allArticles || [],
            categoryArticles: app.globalData.galleryArticles.categoryArticles || {},
            articles: app.globalData.galleryArticles.currentArticles || [],
            currentCategory: app.globalData.galleryArticles.currentCategory || 'all',
            loading: false
          }, () => {
            if (app.globalData.debugMode) {
              console.log('已从缓存恢复数据，当前分类:', this.data.currentCategory, '文章数量: ', this.data.articles.length);
            }
          });
          
          // 设置showContent为true，显示内容
          if (!this.data.showContent) {
            this.setData({ showContent: true });
          }
          
          if (app.globalData.debugMode) {
            console.log('从缓存恢复数据完成');
          }
          
          // 无需触发onArticlesReady，因为数据已经准备好
          this.onArticlesReady();
          
          return true;
        }
      }
      
      // 加载所有文章
      const loadSuccess = await this.loadAllArticles(isFirstLoad);
      
      // 设置全局加载状态
      if (isFirstLoad && loadSuccess && app && app.globalData) {
        app.globalData.galleryLoaded = true;
      }
      
      // 确保showContent为true
      if (!this.data.showContent) {
        if (app.globalData.debugMode) {
          console.log('数据加载完成后确保showContent为true');
        }
        this.setData({ showContent: true });
      }
      
      // 记录结束时间
      const endTime = Date.now();
      const loadTime = endTime - startTime;
      
      if (app.globalData.debugMode) {
        console.log('页面数据初始化完成，耗时:', loadTime, 'ms');
      }
      
      return loadSuccess;
    } catch (error) {
      console.error('初始化页面数据失败:', error);
      
      // 确保在出错时也设置showContent和loading状态
      this.setData({
        showContent: true,
        loading: false
      });
      
      return false;
    }
  },

  // 一次性加载所有文章，并按分类存储到缓存
  async loadAllArticles(forceReload = false) {
    if (app.globalData.debugMode) {
      console.log('开始加载所有文章', forceReload ? '(强制刷新)' : '');
    }
    
    this.setData({ loading: true });
    
    try {
      // 添加超时处理
      let timeoutId;
      const timeoutPromise = new Promise((_, reject) => {
        timeoutId = setTimeout(() => {
          reject(new Error('加载文章列表超时'));
        }, 15000); // 15秒超时
      });
      
      // 创建云函数调用Promise - 使用galleryManager云函数获取画廊数据
      const cloudFunctionPromise = new Promise((resolve, reject) => {
        if (app.globalData.debugMode) {
          console.log('准备调用云函数getVisibleGalleryList，参数:', {
            categoryId: this.data.currentCategory !== 'all' ? this.data.currentCategory : undefined,
            forceReload: forceReload
          });
        }
        
        wx.cloud.callFunction({
          name: 'galleryManager',
          data: {
            action: 'getVisibleGalleryList',
            data: {
              page: 1,
              pageSize: 100, // 一次获取较多数据
              categoryId: this.data.currentCategory !== 'all' ? this.data.currentCategory : undefined,
              forceReload: forceReload, // 传递强制刷新标记
              timestamp: Date.now() // 添加时间戳防止缓存
            }
          },
          success: (res) => {
            if (app.globalData.debugMode) {
              console.log('云函数调用成功，返回数据长度:', res.result && res.result.data && res.result.data.list ? res.result.data.list.length : 0);
            }
            resolve(res);
          },
          fail: (err) => {
            console.error('云函数调用失败:', err);
            reject(err);
          }
        });
      });
      
      // 使用Promise.race竞争，谁先完成就用谁的结果
      const result = await Promise.race([cloudFunctionPromise, timeoutPromise]);
      clearTimeout(timeoutId); // 清除超时定时器
      
      if (app.globalData.debugMode) {
        console.log('云函数响应状态:', result && result.result ? (result.result.success ? '成功' : '失败') : '无效响应');
      }
      
      // 检查返回结果结构
      if (!result || !result.result) {
        console.error('云函数返回结果格式错误:', result);
        this.setData({ 
          loading: false,
          isFirstLoad: false
        });
        wx.showToast({
          title: '加载文章失败：返回结果格式错误',
          icon: 'none',
          duration: 2000
        });
        return false;
      }
      
      // 打印完整的返回结果结构
      if (app.globalData.debugMode) {
        console.log('云函数返回结果结构:', {
          success: result.result.success,
          code: result.result.code,
          message: result.result.message,
          data: result.result.data ? {
            list: Array.isArray(result.result.data.list) ? `数组(${result.result.data.list.length}项)` : '非数组',
            total: result.result.data.total
          } : '无数据'
        });
        
        // 打印更多调试信息
        if (result.result.data && result.result.data.list) {
          console.log('获取到的文章数据示例:', result.result.data.list.length > 0 ? 
            { id: result.result.data.list[0]._id, title: result.result.data.list[0].mainTitle } : '无数据');
        }
      }
      
      // 修改判断条件，检查success字段而不是code字段
      if (result.result.success === true && result.result.data && result.result.data.list) {
        const serverArticles = result.result.data.list || [];
        if (app.globalData.debugMode) {
          console.log('获取到服务器文章数量:', serverArticles.length);
        }
        
        if (!serverArticles || serverArticles.length === 0) {
          if (app.globalData.debugMode) {
            console.log('没有获取到任何文章');
          }
          this.setData({ 
            loading: false,
            isFirstLoad: false, // 即使没有数据也设置首次加载完成
            lastRefreshTime: Date.now(), // 记录刷新时间
            showContent: true, // 确保显示内容（空状态），而不是一直加载
            // 重要：当没有数据时清空现有数据
            allArticles: [],
            categoryArticles: this.initEmptyCategoryArticles(),
            articles: []
          });
          return true; // 返回成功，只是没有数据
        }
        
        // 处理文章数据 - 直接使用galleryManager返回的数据结构
        const processedArticles = serverArticles.map(item => {
          // 确保时间戳是数字类型
          let timestamp = Date.now();
          if (item.createTime) {
            if (typeof item.createTime === 'string') {
              timestamp = new Date(item.createTime).getTime();
            } else if (item.createTime instanceof Date) {
              timestamp = item.createTime.getTime();
            } else if (item.createTime['$date']) {
              // 处理MongoDB日期格式，使用中括号语法
              timestamp = new Date(item.createTime['$date']).getTime();
            }
          }
          
          // 检查是否有更新时间字段，如果有则用updateTime代替createTime
          let updateTimestamp = null;
          if (item.updateTime) {
            if (typeof item.updateTime === 'string') {
              updateTimestamp = new Date(item.updateTime).getTime();
            } else if (item.updateTime instanceof Date) {
              updateTimestamp = item.updateTime.getTime();
            } else if (item.updateTime['$date']) {
              // 使用中括号语法访问$date属性
              updateTimestamp = new Date(item.updateTime['$date']).getTime();
            }
          }
          
          // 如果有更新时间且更新时间比创建时间更近，使用更新时间
          if (updateTimestamp && updateTimestamp > timestamp) {
            timestamp = updateTimestamp;
          }
          
          // 检查是否有显示状态变更时间字段
          let visibilityChangeTimestamp = null;
          if (item.visibilityChangeTime) {
            if (typeof item.visibilityChangeTime === 'string') {
              visibilityChangeTimestamp = new Date(item.visibilityChangeTime).getTime();
            } else if (item.visibilityChangeTime instanceof Date) {
              visibilityChangeTimestamp = item.visibilityChangeTime.getTime();
            } else if (item.visibilityChangeTime['$date']) {
              // 使用中括号语法访问$date属性
              visibilityChangeTimestamp = new Date(item.visibilityChangeTime['$date']).getTime();
            }
          }
          
          // 如果有显示状态变更时间且变更时间比其他时间更近，使用变更时间
          if (visibilityChangeTimestamp && visibilityChangeTimestamp > timestamp) {
            timestamp = visibilityChangeTimestamp;
          }
          
          return {
            id: item._id,
            originalId: item._id,
            title: item.mainTitle || '',
            subtitle: item.subTitle || '',
            description: '',
            coverImage: item.coverUrl || '/static/logo.png',
            content: item.content || '',
            detailImages: item.detailImages || [],
            authorName: '---今禧美学---',
            authorAvatar: '/static/logo.png',
            type: 'gallery',
            timestamp: timestamp,
            viewCount: Math.floor(Math.random() * 100), // 随机视图数
            order: item.order || 0,
            isVisible: item.isVisible,
            visibilityChangeTime: visibilityChangeTimestamp, // 保存可见性变更时间
            categoryId: item.categoryId || '' // 保存分类ID
          };
        });
        
        if (app.globalData.debugMode) {
          console.log('处理后的文章数据示例:', processedArticles.length > 0 ? 
            { id: processedArticles[0].id, title: processedArticles[0].title } : '无数据');
        }
        
        // 对文章进行排序：
        // 1. 优先按照时间戳（包括更新和显示状态变更时间）从新到旧排序
        // 2. 如果时间戳相同，则按照顺序号从小到大排序
        processedArticles.sort((a, b) => {
          if (a.timestamp !== b.timestamp) {
            return b.timestamp - a.timestamp; // 按时间戳降序排列（新的在前）
          }
          return a.order - b.order; // 时间戳相同时按顺序号升序
        });
        
              // 更新全部文章缓存
      const updatedAllArticles = processedArticles;
      
      // 更新所有分类的缓存
      const updatedCategoryArticles = {};
      
      // 为每个分类过滤文章
      this.data.categoryList.forEach(category => {
        const categoryId = category.id;
        
        if (categoryId === 'all') {
          // 全部分类包含所有文章
          updatedCategoryArticles[categoryId] = updatedAllArticles;
        } else {
          // 根据分类过滤文章
          updatedCategoryArticles[categoryId] = updatedAllArticles.filter(article => article.categoryId === categoryId);
        }
      });
      
      // 更新当前分类的文章
      const currentCategory = this.data.currentCategory;
      const currentArticles = updatedCategoryArticles[currentCategory] || [];
      
      // 更新UI状态 - 确保重置所有加载状态
      this.setData({
        allArticles: updatedAllArticles,
        categoryArticles: updatedCategoryArticles,
        articles: currentArticles,
        loading: false,
        isFirstLoad: false,
        scrollTop: 0, // 确保滚动回到顶部
        lastRefreshTime: Date.now() // 记录刷新时间
        }, () => {
          // 文章加载完成后检查文本溢出
          this.onArticlesReady();
          
          // 持久化缓存数据到全局变量
          if (app && app.globalData) {
            app.globalData.galleryArticles = {
              allArticles: updatedAllArticles,
              categoryArticles: updatedCategoryArticles,
              currentArticles: currentArticles,
              currentCategory: currentCategory,
              lastRefreshTime: Date.now()
            };
            
            // 标记画廊已加载
            app.globalData.galleryLoaded = true;
            
            if (app.globalData.debugMode) {
              console.log('已将画廊数据缓存到全局变量');
            }
          }
        });
        
        return true; // 加载成功
      } else {
        console.error('获取文章列表失败:', result.result ? result.result.message : '未知错误');
        this.setData({ 
          loading: false,
          isFirstLoad: false
        });
        
        // 显示错误提示
        wx.showToast({
          title: result.result && result.result.message ? result.result.message : '获取文章列表失败',
          icon: 'none',
          duration: 2000
        });
        
        return false; // 加载失败
      }
    } catch (err) {
      console.error('加载文章出错:', err);
      this.setData({ 
        loading: false,
        isFirstLoad: false
      });
      
      // 显示错误提示
      wx.showToast({
        title: '加载文章失败，请重试',
        icon: 'none',
        duration: 2000
      });
      
      return false; // 加载失败
    }
  },

  // 创建空的分类文章缓存对象
  initEmptyCategoryArticles() {
    const emptyCategoryArticles = {};
    this.data.categoryList.forEach(category => {
      emptyCategoryArticles[category.id] = [];
    });
    return emptyCategoryArticles;
  },

  // 处理分类变更
  onCategoryChange(e) {
    const categoryId = e.detail.categoryId;
    const prevCategory = this.data.currentCategory;
    
    // 如果切换到相同分类，不做任何操作
    if (categoryId === prevCategory) {
      return;
    }
    
    // 只在开发环境或需要调试时输出日志
    if (app.globalData.debugMode) {
      console.log(`【分类切换】从 ${prevCategory} 切换到 ${categoryId}`);
    }
    
    // 设置新的当前分类，并始终重置滚动位置
    this.setData({
      currentCategory: categoryId,
      scrollTop: 0, // 重置滚动位置
      loading: true // 显示加载状态
    });
    
    // 检查是否有缓存数据
    if (this.data.categoryArticles[categoryId] && this.data.categoryArticles[categoryId].length > 0) {
      // 使用缓存数据快速显示
      if (app.globalData.debugMode) {
        if (categoryId === 'all') {
          console.log('【分类切换】全部分类: 使用本地缓存，文章数量:', this.data.categoryArticles[categoryId].length);
        } else {
          console.log('【分类切换】使用缓存数据显示分类:', categoryId);
        }
      }
      
      const articlesToShow = this.data.categoryArticles[categoryId];
      
      // 使用setTimeout确保UI先更新分类，再显示文章
      setTimeout(() => {
        this.setData({
          articles: articlesToShow,
          loading: false,
          showContent: true // 确保内容可见
        }, () => {
          // 检查文本溢出
          this.onArticlesReady();
          
          if (app.globalData.debugMode) {
            if (categoryId === 'all') {
              console.log('【分类切换】全部分类: 使用本地缓存完成，当前articles长度:', this.data.articles.length);
            }
          }
          
          // 更新全局缓存中的当前分类
          if (app && app.globalData && app.globalData.galleryArticles) {
            app.globalData.galleryArticles.currentCategory = categoryId;
            app.globalData.galleryArticles.currentArticles = articlesToShow;
          }
        });
      }, 50); // 短延迟确保UI更新
    } else {
      // 没有缓存，从服务器加载该分类的数据
      if (app.globalData.debugMode) {
        console.log('【分类切换】没有缓存数据，从服务器加载分类:', categoryId);
      }
      this.loadCategoryData(categoryId);
    }
  },
  
  // 加载特定分类的数据
  loadCategoryData(categoryId) {
    if (app.globalData.debugMode) {
      console.log(`【加载分类】开始加载分类数据: ${categoryId}`);
    }
    
    // 从服务器加载该分类的数据
    wx.cloud.callFunction({
      name: 'galleryManager',
      data: {
        action: 'getVisibleGalleryList',
        data: {
          page: 1,
          pageSize: 100,
          categoryId: categoryId !== 'all' ? categoryId : undefined,
          timestamp: Date.now() // 添加时间戳防止缓存
        }
      }
    }).then(res => {
      // 只在成功或失败时输出关键日志
      if (res.result && res.result.success && res.result.data && res.result.data.list) {
        if (app.globalData.debugMode) {
          console.log(`【加载分类】服务器返回成功，分类[${categoryId}]获取到${res.result.data.list.length}条数据`);
        }
        
        const serverArticles = res.result.data.list || [];
        
        // 处理文章数据
        const processedArticles = serverArticles.map(item => {
          // 确保时间戳是数字类型
          let timestamp = Date.now();
          if (item.createTime) {
            if (typeof item.createTime === 'string') {
              timestamp = new Date(item.createTime).getTime();
            } else if (item.createTime instanceof Date) {
              timestamp = item.createTime.getTime();
            } else if (item.createTime['$date']) {
              // 处理MongoDB日期格式，使用中括号语法
              timestamp = new Date(item.createTime['$date']).getTime();
            }
          }
          
          return {
            id: item._id,
            originalId: item._id,
            title: item.mainTitle || '',
            subtitle: item.subTitle || '',
            description: '',
            coverImage: item.coverUrl || '/static/logo.png',
            content: item.content || '',
            detailImages: item.detailImages || [],
            authorName: '---今禧美学---',
            authorAvatar: '/static/logo.png',
            type: 'gallery',
            timestamp: timestamp,
            viewCount: Math.floor(Math.random() * 100),
            order: item.order || 0,
            isVisible: item.isVisible,
            categoryId: item.categoryId || ''
          };
        });
        
        // 更新分类缓存
        const categoryArticles = this.data.categoryArticles;
        categoryArticles[categoryId] = processedArticles;
        
        // 如果是全部分类，也更新allArticles
        if (categoryId === 'all') {
          this.setData({
            allArticles: processedArticles
          });
        }
        
                  // 仅在当前分类仍然是请求的分类时更新UI
          if (this.data.currentCategory === categoryId) {
            this.setData({
              articles: processedArticles,
              categoryArticles: categoryArticles,
              loading: false,
              showContent: true, // 确保内容可见
              scrollTop: 0, // 重置滚动位置
              lastRefreshTime: Date.now()
            }, () => {
              // 检查文本溢出
              this.onArticlesReady();
              
              // 持久化缓存数据到全局变量
              if (app && app.globalData) {
                app.globalData.galleryArticles = {
                  allArticles: this.data.allArticles,
                  categoryArticles: categoryArticles,
                  currentArticles: processedArticles,
                  currentCategory: categoryId,
                  lastRefreshTime: Date.now()
                };
                
                // 标记画廊已加载
                app.globalData.galleryLoaded = true;
                
                if (app.globalData.debugMode) {
                  console.log('已将画廊数据缓存到全局变量');
                }
              }
              
              // 确保已经停止任何刷新动作
              wx.stopPullDownRefresh();
            });
          } else {
            // 如果用户已经切换到其他分类，只更新缓存
            this.setData({
              categoryArticles: categoryArticles
            });
          }
      } else {
        // 处理失败情况
        console.error('获取分类文章失败:', res.result ? res.result.message : '未知错误');
        
        // 仅在当前分类仍然是请求的分类时更新UI状态
        if (this.data.currentCategory === categoryId) {
          this.setData({ loading: false });
          
          // 如果没有缓存数据，显示空状态
          if (!this.data.articles.length) {
            this.setData({ articles: [] });
          }
          
          wx.showToast({
            title: res.result && res.result.message ? res.result.message : '获取分类文章失败',
            icon: 'none',
            duration: 2000
          });
        }
      }
    }).catch(err => {
      console.error('获取分类文章出错:', err);
      
      // 仅在当前分类仍然是请求的分类时更新UI状态
      if (this.data.currentCategory === categoryId) {
        this.setData({ loading: false });
        
        // 如果没有缓存数据，显示空状态
        if (!this.data.articles.length) {
          this.setData({ articles: [] });
        }
        
        wx.showToast({
          title: '获取分类文章失败，请重试',
          icon: 'none',
          duration: 2000
        });
      }
    });
  },

  // 页面显示时检查文本溢出
  onShow() {
    if (app) {
      app.globalData.currentTabIndex = 0;
    }
    if (typeof this.getTabBar === 'function') {
      this.getTabBar().setData({
        selected: 0  // gallery 页面对应的索引为 0（现在是第一个标签）
      });
    }
    
    // 无论首次加载还是再次进入，都立即显示内容，避免加载动画
    this.setData({ 
      showContent: true,
      isFromTab: true  // 设置为从标签栏进入模式，禁用加载动画
    }, () => {
      if (app && app.globalData && app.globalData.debugMode) {
        console.log('onShow设置showContent=', this.data.showContent);
      }
    });
    
    // 文本溢出检查
    this.checkTextOverflow();
    
    // 检查是否有待打开的文章
    if (app.globalData.pendingArticleToOpen) {
      const articleInfo = app.globalData.pendingArticleToOpen;
      app.globalData.pendingArticleToOpen = null; // 清除待打开文章
      
      // 减少延迟时间，加快文章详情打开速度
      setTimeout(() => {
        this.openArticleDetail(articleInfo);
      }, 100); // 减少到100ms，保留最小延迟确保UI准备就绪
    }
    
    // 移除自动刷新逻辑，只在初次加载时（articles为空）才加载数据
    if (this.data.articles.length === 0 && !this.data.loading) {
      if (app && app.globalData && app.globalData.debugMode) {
        console.log('onShow: 文章列表为空，加载初始数据');
      }
      this.setData({ loading: true });
      this.loadArticlesInBackground(false); // 不强制刷新，使用可能的缓存
    }
  },

  onHide() {
    // 页面隐藏时不再重置显示状态，保持数据缓存
    // 只在首次加载时才在onHide时设置showContent为false
    if (this.data.isFirstLoad) {
      this.setData({ showContent: false });
    }
  },
  
  // 文章列表加载完成后检查文本溢出
  onArticlesReady() {
    // 延迟执行，确保DOM已完全渲染
    setTimeout(() => {
      this.checkTextOverflow()
      
      // 处理从分享链接进入的情况
      if (this.data.sharedArticleId) {
        // 查找对应的文章
        const article = this.data.articles.find(item => 
          item._id === this.data.sharedArticleId || item.id === this.data.sharedArticleId
        );
        
        if (article) {
          // 打开文章详情
          this.openArticleDetail(article);
          
          // 清除分享ID，避免重复打开
          this.setData({
            sharedArticleId: null
          });
        }
      }
    }, 500)
  },
  
  // 检查描述文本是否溢出，并添加相应的类
  checkTextOverflow() {
    const query = wx.createSelectorQuery()
    query.selectAll('.font_2').boundingClientRect()
    query.exec(res => {
      if (!res || !res[0] || !res[0].length) return
      
      res[0].forEach((item, index) => {
        // 使用新的 API 获取窗口信息
        const windowInfo = wx.getWindowInfo()
        // 检查实际高度是否超过设定的最大高度
        // 行高52rpx × 3行 = 156rpx
        const maxHeight = 156 * windowInfo.windowWidth / 750
        
        if (item.height >= maxHeight) {
          // 获取当前项的索引，用于设置overflow类
          const descElem = wx.createSelectorQuery().select(`.info-list:nth-child(${index + 1}) .font_2`)
          if (descElem) {
            // 将元素的class设置为包含overflow
            this.setData({
              [`overflowIndices[${index}]`]: true
            })
          }
        }
      })
    })
  },

  // 下拉刷新处理 - 修改为完全刷新模式，确保能够移除已隐藏的内容
  onPullDownRefresh() {
    // 只在调试模式下输出详细日志
    if (app.globalData.debugMode) {
      console.log('下拉刷新开始执行 - 完全刷新模式');
    }
    
    // 如果正在加载中，不执行刷新
    if (this.data.loading || this.data.isRefreshing) {
      if (app.globalData.debugMode) {
        console.log('已有加载任务正在执行，跳过本次刷新');
      }
      wx.stopPullDownRefresh();
      return;
    }
    
    // 设置刷新状态并重置滚动位置
    this.setData({ 
      isRefreshing: true,
      scrollTop: 0 // 确保回到顶部
    });
    
    // 检查是否需要节流（避免频繁刷新）
    const now = Date.now();
    const lastRefreshTime = this.data.lastRefreshTime || 0;
    const refreshInterval = 3000; // 降低到3秒，让测试更容易
    
    if (now - lastRefreshTime < refreshInterval) {
      if (app.globalData.debugMode) {
        console.log('刷新过于频繁，跳过本次刷新');
      }
      wx.showToast({
        title: '刷新太频繁，请稍后再试',
        icon: 'none',
        duration: 1500
      });
      wx.stopPullDownRefresh();
      this.setData({ isRefreshing: false });
      return;
    }
    
    // 创建刷新超时保护，确保不会一直处于刷新状态
    const refreshTimeout = setTimeout(() => {
      if (this.data.isRefreshing) {
        console.log('刷新超时，强制结束刷新状态');
        wx.stopPullDownRefresh();
        this.setData({ 
          isRefreshing: false,
          loading: false,
          showContent: true
        });
      }
    }, 10000); // 10秒后强制结束刷新状态
    
    // 播放刷新动画效果，至少显示800ms让用户看到反馈
    setTimeout(() => {
      // 首先刷新分类列表，然后刷新文章内容
      this.loadCategoryList().then((categoryList) => {
        if (app.globalData.debugMode) {
          console.log('下拉刷新：分类列表刷新完成，开始刷新文章内容');
        }
        
        // 确保分类列表更新到UI
        this.setData({
          categoryList: categoryList
        });
        
        // 调用完全刷新方法，重新获取所有数据
        return this.loadAllArticles(true);
      }).then((success) => {
        // 清除超时定时器
        clearTimeout(refreshTimeout);
        
        if (app.globalData.debugMode) {
          console.log('完全刷新完成:', success ? '成功' : '失败');
        }
        
        // 确保重置滚动位置并停止刷新状态
        wx.stopPullDownRefresh();
        this.setData({ 
          isRefreshing: false,
          lastRefreshTime: Date.now(),
          scrollTop: 0 // 再次确保滚动位置重置
        });
        
        // 成功时不显示任何提示，失败时才显示提示
        if (!success) {
          wx.showToast({
            title: '刷新失败，请重试',
            icon: 'none',
            duration: 2000
          });
        }
      }).catch((err) => {
        // 清除超时定时器
        clearTimeout(refreshTimeout);
        
        console.error('刷新数据加载失败:', err);
        // 停止系统下拉刷新
        wx.stopPullDownRefresh();
        this.setData({ 
          isRefreshing: false,
          lastRefreshTime: Date.now()
        });
        
        wx.showToast({
          title: '刷新失败，请重试',
          icon: 'none',
          duration: 2000
        });
      });
    }, 800); // 延迟800ms，确保动画有足够时间显示
  },

  // 手动刷新按钮点击事件
  onManualRefresh() {
    if (app && app.globalData && app.globalData.debugMode) {
      console.log('用户点击手动刷新按钮');
    }
    
    wx.showToast({
      title: '正在刷新...',
      icon: 'loading',
      duration: 2000
    });
    
    // 重新加载分类和文章
    this.loadCategoryList().then(categoryList => {
      if (app && app.globalData && app.globalData.debugMode) {
        console.log('手动刷新：分类列表加载完成');
      }
      // 强制刷新，确保获取最新数据并移除已隐藏内容
      return this.loadAllArticles(true);
    }).then(success => {
      if (app && app.globalData && app.globalData.debugMode) {
        console.log('手动刷新：文章加载完成，状态:', success);
      }
      if (success) {
        wx.showToast({
          title: '刷新成功',
          icon: 'success',
          duration: 1500
        });
      } else {
        wx.showToast({
          title: '刷新失败，请重试',
          icon: 'none',
          duration: 2000
        });
      }
    }).catch(err => {
      console.error('手动刷新失败:', err);
      wx.showToast({
        title: '刷新失败，请重试',
        icon: 'none',
        duration: 2000
      });
    });
  },

  // 新增后台加载方法，避免清空现有内容
  loadArticlesInBackground(forceReload = false) {
    if (app.globalData.debugMode) {
      console.log('在后台加载更多文章数据');
    }
    
    // 如果已经在刷新中，不要重复加载
    if (this.data.isRefreshing) {
      if (app.globalData.debugMode) {
        console.log('已经在刷新中，跳过后台加载');
      }
      return;
    }
    
    // 不显示加载状态，在后台静默加载
    wx.cloud.callFunction({
      name: 'galleryManager',
      data: {
        action: 'getVisibleGalleryList',
        data: {
          page: 1,
          pageSize: 20, // 加载更多数据
          categoryId: this.data.currentCategory !== 'all' ? this.data.currentCategory : undefined,
          forceReload: forceReload,
          timestamp: Date.now()
        }
      }
    }).then(res => {
      // 如果页面正在刷新，不处理结果
      if (this.data.isRefreshing) {
        if (app.globalData.debugMode) {
          console.log('页面正在刷新，跳过后台加载结果处理');
        }
        return;
      }
      
      if (res.result && res.result.success === true && res.result.data && res.result.data.list) {
        const serverArticles = res.result.data.list || [];
        
        if (serverArticles.length > 0) {
          // 处理文章数据，确保格式一致
          const processedArticles = serverArticles.map(item => {
            // 确保时间戳是数字类型
            let timestamp = Date.now();
            if (item.createTime) {
              if (typeof item.createTime === 'string') {
                timestamp = new Date(item.createTime).getTime();
              } else if (item.createTime instanceof Date) {
                timestamp = item.createTime.getTime();
              } else if (item.createTime['$date']) {
                timestamp = new Date(item.createTime['$date']).getTime();
              }
            }
            
            return {
              id: item._id,
              originalId: item._id,
              title: item.mainTitle || '',
              subtitle: item.subTitle || '',
              description: '',
              coverImage: item.coverUrl || '/static/logo.png',
              content: item.content || '',
              detailImages: item.detailImages || [],
              authorName: '---今禧美学---',
              authorAvatar: '/static/logo.png',
              type: 'gallery',
              timestamp: timestamp,
              viewCount: Math.floor(Math.random() * 100),
              order: item.order || 0,
              isVisible: item.isVisible,
              categoryId: item.categoryId || ''
            };
          });
          
          // 更新全局缓存和本地数据
          const allArticles = processedArticles;
          const categoryArticles = { ...this.data.categoryArticles };
          categoryArticles['all'] = allArticles;
          
          // 更新数据，但保持当前显示的文章不变，除非当前没有文章
          this.setData({
            allArticles: allArticles,
            categoryArticles: categoryArticles,
            articles: this.data.articles.length > 0 ? this.data.articles : allArticles.slice(0, this.data.pageSize)
          });
          
          // 更新全局缓存
          if (app && app.globalData) {
            app.globalData.galleryArticles = {
              allArticles: allArticles,
              categoryArticles: categoryArticles,
              currentArticles: this.data.articles,
              currentCategory: this.data.currentCategory
            };
          }
          
          // console.log('后台加载完成，更新了缓存数据，总文章数:', allArticles.length);
        }
      }
    }).catch(err => {
      console.error('后台加载文章失败:', err);
    });
  },

  // 点击文章处理
  onTapArticle(e) {
    const article = e.currentTarget.dataset.article
    console.log('点击文章:', article)
    
    // 更严格的数据验证
    if (!article) {
      console.error('文章数据为空')
      wx.showToast({
        title: '文章数据缺失',
        icon: 'none'
      })
      return
    }
    
    // 验证ID
    if (!article.id && !article._id) {
      console.error('文章缺少ID字段，无法查看详情', article)
      wx.showToast({
        title: '文章ID缺失',
        icon: 'none'
      })
      return
    }
    
    const articleId = article.id || article._id
    const title = article.title || article.mainTitle || ''
    const subtitle = article.subtitle || article.subTitle || ''
    const coverImage = article.coverImage || article.coverUrl || '/static/logo.png'
    
    const galleryDetailModal = this.selectComponent('#galleryDetailModal')
    if (galleryDetailModal) {
      console.log('打开详情模态框，文章ID:', articleId)
      galleryDetailModal.showModal({
        id: articleId, 
        mainTitle: title,
        subTitle: subtitle,
        coverImage: coverImage,
        authorAvatar: article.authorAvatar || '/static/logo.png',
        author: article.authorName || '---今禧美学---',
        description: article.description || '',
        detailImages: article.detailImages || [] // 确保传递detailImages数组
      })
    }
  },

  onGalleryDetailClose() {
    console.log('弹窗关闭')
  },

  onNavbarControl(e) {
    const { action } = e.detail;
    // 只在调试模式下输出日志
    if (app && app.globalData && app.globalData.debugMode) {
      console.log('导航栏控制:', action);
    }
    
    // 将导航栏控制事件传递给弹窗组件
    const detailModal = this.selectComponent('#detailModal');
    if (detailModal) {
      detailModal.onNavigationBarControl(action);
    }
  },

  // 处理分享按钮点击
  onShareArticle(e) {
    // 获取文章信息
    const articleInfo = e.detail.videoInfo;
    console.log('分享按钮点击，文章信息:', articleInfo);
    
    // 验证文章信息完整性
    if (!articleInfo || (!articleInfo.id && !articleInfo._id)) {
      console.error('分享的文章信息不完整');
      return;
    }
    
    const articleId = articleInfo.id || articleInfo._id;
    console.log('准备分享文章，ID:', articleId);
  },

  // 阻止事件冒泡
  stopPropagation(e) {
    e.stopPropagation();
  },

  onShareAppMessage(options) {
    console.log('触发分享事件:', options);
    
    // 引入设备工具函数
    const deviceUtils = require('../../utils/device.js');
    
    // 判断是否是从分享按钮触发
    if (options.from === 'button') {
      // 获取按钮上的数据
      const articleInfo = options.target.dataset.video;
      console.log('分享文章信息:', articleInfo);
      
      if (!articleInfo || (!articleInfo.id && !articleInfo._id)) {
        console.error('分享的文章信息不完整');
        return {
          title: '灵感相册',
          path: '/pages/gallery/gallery'
        };
      }
      
      const articleId = articleInfo.id || articleInfo._id;
      const shareTitle = articleInfo.title || articleInfo.mainTitle || '灵感相册';
      const shareImage = articleInfo.coverImage || articleInfo.coverUrl || '/static/default-service.png';
      
      console.log('构建分享参数:', {
        contentType: 'article',
        contentId: articleId,
        title: shareTitle,
        path: `/pages/gallery/gallery?articleId=${articleId}`,
        imageUrl: shareImage
      });
      
      // 使用全局分享辅助函数（同步版本）
      return getApp().shareWithPointsSync({
        contentType: 'article',
        contentId: articleId,
        title: shareTitle,
        path: `/pages/gallery/gallery?articleId=${articleId}`,
        imageUrl: shareImage
      });
    }
    
    // 默认分享内容
    return {
      title: '灵感相册',
      path: '/pages/gallery/gallery'
    };
  },

  // 页面离开时清理计时器
  onUnload() {
    if (this.data.noMoreTipTimer) {
      clearTimeout(this.data.noMoreTipTimer)
    }
  },

  // 触摸开始时的处理函数
  handleTouchStart(e) {
    // 记录触摸起始位置
    const touch = e.touches[0];
    this.touchStartY = touch.clientY;
    
    // 获取当前触摸的列表项索引
    const index = e.currentTarget.dataset.index;
    let articles = this.data.articles;
    
    // 更新被触摸项的状态
    if (index !== undefined && articles[index]) {
      articles[index].isTouched = true;
      articles[index].isMoving = false;
      articles[index].isReleased = false;
      articles[index].touchDirection = 'none';
      
      this.setData({
        articles: articles
      });
    }
  },
  
  // 触摸移动时的处理函数
  handleTouchMove(e) {
    if (!this.touchStartY) return;
    
    const touch = e.touches[0];
    const currentY = touch.clientY;
    const diffY = currentY - this.touchStartY;
    
    // 获取当前触摸的列表项索引
    const index = e.currentTarget.dataset.index;
    let articles = this.data.articles;
    
    // 判断滑动方向，设置相应的状态
    if (Math.abs(diffY) > 5 && index !== undefined && articles[index]) {
      const direction = diffY > 0 ? 'down' : 'up';
      
      articles[index].isMoving = true;
      articles[index].touchDirection = direction;
      
      this.setData({
        articles: articles
      });
    }
  },
  
  // 触摸结束时的处理函数
  handleTouchEnd(e) {
    // 获取当前触摸的列表项索引
    const index = e.currentTarget.dataset.index;
    let articles = this.data.articles;
    
    if (index !== undefined && articles[index]) {
      articles[index].isTouched = false;
      articles[index].isMoving = false;
      articles[index].isReleased = true;
      
      this.setData({
        articles: articles
      });
      
      // 300ms后移除释放状态，完成动画
      setTimeout(() => {
        if (this.data.articles[index]) {
          articles = this.data.articles;
          articles[index].isReleased = false;
          this.setData({
            articles: articles
          });
        }
      }, 300);
    }
    
    // 重置触摸起始位置
    this.touchStartY = 0;
  },



  // 打开文章详情弹窗
  openArticleDetail: function(articleInfo) {
    if (!articleInfo) {
      console.error('文章信息为空')
      wx.showToast({
        title: '文章数据缺失',
        icon: 'none'
      })
      return
    }
    
    // 验证ID
    if (!articleInfo.id && !articleInfo._id) {
      console.error('文章缺少ID字段，无法查看详情', articleInfo)
      wx.showToast({
        title: '文章ID缺失',
        icon: 'none'
      })
      return
    }
    
    const articleId = articleInfo.id || articleInfo._id
    
    // 获取文章详情弹窗组件
    const galleryDetailModal = this.selectComponent('#galleryDetailModal');
    if (galleryDetailModal) {
      // 调用组件的showModal方法，传入文章信息
      galleryDetailModal.showModal({
        id: articleId, 
        mainTitle: articleInfo.title || articleInfo.mainTitle || '',
        subTitle: articleInfo.subtitle || articleInfo.subTitle || '',
        coverImage: articleInfo.coverImage || articleInfo.coverUrl || '/static/logo.png',
        authorAvatar: articleInfo.authorAvatar || '/static/logo.png',
        author: articleInfo.authorName || articleInfo.author || '---今禧美学---',
        description: articleInfo.description || '',
        detailImages: articleInfo.detailImages || []
      });
    } else {
      console.error('未找到文章详情弹窗组件');
      wx.showToast({
        title: '打开文章失败',
        icon: 'none'
      });
    }
  },

  // 导航到地图页面
  navigateToMap(e) {
    // 在微信小程序中，使用e.mark.catchThisTap = true或直接在wxml中使用catchtap
    // 不需要调用e.stopPropagation()
    
    // 直接调用wx.openLocation打开地图导航，而不是跳转到地图页面
    wx.openLocation({
      latitude: 23.144894,  // 广州越秀区广园西路121号美博城的纬度
      longitude: 113.270213, // 广州越秀区广园西路121号美博城的经度
      name: '指家科技',
      address: '广州市越秀区广园西路121号美博城',
      scale: 18
    });
  },

  // 添加下拉刷新过程中的处理方法
  onRefresherPulling(e) {
    // 获取下拉距离
    if (e && e.detail && typeof e.detail.dy === 'number') {
      this.setData({
        refresherPullingDistance: e.detail.dy
      });
    }
    
    // 这里可以根据下拉距离做一些动画效果，但目前我们只需要处理事件避免报错
    if (app.globalData.debugMode) {
      console.log('下拉刷新中，距离:', this.data.refresherPullingDistance);
    }
  },

  // 根据ID打开文章详情
  openArticleById(articleId) {
    console.log('尝试打开文章详情，ID:', articleId);
    
    // 查找文章数据
    const allArticles = this.data.allArticles || [];
    const article = allArticles.find(item => 
      item._id === articleId || item.id === articleId
    );
    
    if (article) {
      console.log('在本地数据中找到文章:', article);
      this.openArticleDetail(article);
    } else {
      console.log('本地数据中未找到文章，从服务器获取');
      // 只在非分享进入时显示加载提示
      const isFromShare = this.data.sharedArticleId;
      if (!isFromShare) {
        wx.showLoading({
          title: '加载文章...',
          mask: true
        });
      }
      
      // 如果在当前数据中找不到，尝试从服务器获取
      wx.cloud.callFunction({
        name: 'galleryManager',
        data: {
          action: 'getArticleById',
          id: articleId
        }
      }).then(res => {
        // 只在非分享进入时隐藏加载提示
        const isFromShare = this.data.sharedArticleId;
        if (!isFromShare) {
          wx.hideLoading();
        }
        console.log('获取文章详情结果:', res.result);
        console.log('请求的文章ID:', articleId);
        console.log('云函数返回的完整结果:', res);
        
        if (res.result && res.result.success && res.result.data) {
          console.log('成功获取文章详情:', res.result.data);
          this.openArticleDetail(res.result.data);
        } else {
          // 增强错误日志，帮助调试
          console.error('文章获取失败详细信息:', {
            articleId: articleId,
            result: res.result,
            success: res.result ? res.result.success : 'undefined',
            data: res.result ? res.result.data : 'undefined',
            message: res.result ? res.result.message : 'undefined',
            code: res.result ? res.result.code : 'undefined'
          });
          
          // 分享进入时完全静默处理，不显示任何错误提示
          const isFromShare = this.data.sharedArticleId;
          if (!isFromShare) {
            wx.showToast({
              title: '文章不存在',
              icon: 'none'
            });
          } else {
            // 分享进入时，如果文章获取失败，尝试在所有文章中查找
            console.log('分享进入时文章获取失败，尝试在本地数据中查找');
            this.tryFindArticleInLocalData(articleId);
          }
        }
      }).catch(err => {
        // 只在非分享进入时隐藏加载提示
        const isFromShare = this.data.sharedArticleId;
        if (!isFromShare) {
          wx.hideLoading();
        }
        console.error('获取文章详情失败:', err);
        
        // 分享进入时不显示错误提示，静默处理
        if (!isFromShare) {
          wx.showToast({
            title: '获取文章失败',
            icon: 'none'
          });
        }
      });
    }
  },

  // 尝试在本地数据中查找文章（用于分享进入时的备用方案）
  tryFindArticleInLocalData(articleId) {
    console.log('尝试在本地数据中查找文章:', articleId);
    
    // 在当前页面的文章列表中查找
    const articles = this.data.articles || [];
    const foundArticle = articles.find(article => 
      article._id === articleId || 
      article.id === articleId ||
      String(article._id) === String(articleId) ||
      String(article.id) === String(articleId)
    );
    
    if (foundArticle) {
      console.log('在本地数据中找到文章:', foundArticle);
      this.openArticleDetail(foundArticle);
    } else {
      console.log('本地数据中也未找到文章，尝试重新加载数据');
      // 重新加载画廊数据，然后再次尝试查找
      this.loadGalleryData().then(() => {
        // 数据加载完成后再次尝试查找
        setTimeout(() => {
          const updatedArticles = this.data.articles || [];
          const retryFoundArticle = updatedArticles.find(article => 
            article._id === articleId || 
            article.id === articleId ||
            String(article._id) === String(articleId) ||
            String(article.id) === String(articleId)
          );
          
          if (retryFoundArticle) {
            console.log('重新加载后找到文章:', retryFoundArticle);
            this.openArticleDetail(retryFoundArticle);
          } else {
            console.log('重新加载后仍未找到文章，可能文章已被删除');
            // 这里不显示任何错误提示，保持静默
          }
        }, 1000);
      }).catch(err => {
        console.error('重新加载画廊数据失败:', err);
        // 静默处理错误
      });
    }
  },

  // ================== 指向广告相关方法 ==================

  // 设置指向广告事件监听
  setupTargetAdListener() {
    const app = getApp();
    if (app.globalData.eventCenter) {
      app.globalData.eventCenter.on('showTargetAd', (data) => {
        console.log('[Gallery] 收到显示指向广告事件:', data);

        // 只在当前页面是目标页面时显示广告
        const currentRoute = 'pages/gallery/gallery';
        if (!data.targetPage || data.targetPage === currentRoute) {
          this.setData({
            showTargetAd: true,
            targetAdData: {
              imageUrl: data.imageUrl || '',
              jumpUrl: data.jumpUrl || ''
            }
          });
        }
      });

      // 监听全局关闭广告事件
      app.globalData.eventCenter.on('closeTargetAdGlobally', () => {
        console.log('[Gallery] 收到全局关闭广告事件');
        this.setData({
          showTargetAd: false,
          targetAdData: {
            imageUrl: '',
            jumpUrl: ''
          }
        });
      });

      // 监听显示画廊详情事件（来自指向广告跳转）
      app.globalData.eventCenter.on('showGalleryDetail', (data) => {
        console.log('[Gallery] 收到显示画廊详情事件:', data);
        if (data.position && data.title) {
          // 新的位置+标题方式
          this.showGalleryDetailByPosition(data.position, data.title);
        } else if (data.articleId) {
          // 兼容旧的ID方式
          this.showGalleryDetailById(data.articleId);
        }
      });
    }
  },

  // 根据画廊ID显示画廊详情
  showGalleryDetailById(articleId) {
    const { galleryItems } = this.data;
    const gallery = galleryItems.find(item => item._id === articleId);

    if (gallery) {
      console.log('[Gallery] 找到画廊，显示详情:', gallery);
      this.setData({
        selectedGallery: gallery,
        showGalleryDetail: true
      });
    } else {
      console.warn('[Gallery] 未找到指定的画廊:', articleId);
      wx.showToast({
        title: '内容不存在',
        icon: 'none'
      });
    }
  },

  // 指向广告关闭事件
  onTargetAdClose() {
    console.log('[Gallery] 指向广告关闭');
    this.setData({
      showTargetAd: false,
      targetAdData: {
        imageUrl: '',
        jumpUrl: ''
      }
    });
  },

  // 设置画廊详情事件监听
  setupGalleryDetailListener() {
    const app = getApp();
    if (app.globalData.eventCenter) {
      app.globalData.eventCenter.on('showGalleryDetail', (data) => {
        console.log('[Gallery] 收到显示画廊详情事件:', data);
        if (data.articleId) {
          this.showGalleryDetailById(data.articleId);
        }
      });
    }
  },

  // 根据画廊ID显示画廊详情
  showGalleryDetailById(articleId, retryCount = 0) {
    const app = getApp();
    const maxRetries = 3;

    console.log(`[Gallery] 尝试显示画廊详情，ID: ${articleId}, 重试次数: ${retryCount}`);

    // 尝试从多个数据源查找画廊
    let gallery = null;

    // 1. 从当前页面的文章列表查找
    if (this.data.articles && this.data.articles.length > 0) {
      gallery = this.data.articles.find(item => item._id === articleId || item.id === articleId);
      if (gallery) {
        console.log('[Gallery] 从当前文章列表找到画廊:', gallery.title || gallery.mainTitle);
      }
    }

    // 2. 如果没找到，从所有文章缓存查找
    if (!gallery && this.data.allArticles && this.data.allArticles.length > 0) {
      gallery = this.data.allArticles.find(item => item._id === articleId || item.id === articleId);
      if (gallery) {
        console.log('[Gallery] 从所有文章缓存找到画廊:', gallery.title || gallery.mainTitle);
      }
    }

    // 3. 如果还没找到，从全局数据查找
    if (!gallery && app.globalData.galleryArticles && app.globalData.galleryArticles.allArticles) {
      gallery = app.globalData.galleryArticles.allArticles.find(item => item._id === articleId || item.id === articleId);
      if (gallery) {
        console.log('[Gallery] 从全局数据找到画廊:', gallery.title || gallery.mainTitle);
      }
    }

    if (gallery) {
      console.log('[Gallery] 找到画廊，显示详情:', {
        _id: gallery._id,
        id: gallery.id,
        title: gallery.title || gallery.mainTitle,
        hasContent: !!gallery.content,
        hasCoverUrl: !!gallery.coverUrl,
        fullGalleryData: gallery
      });

      // 确保画廊数据有正确的ID字段供详情弹窗使用
      const galleryWithId = {
        ...gallery,
        id: gallery.id || gallery._id // 确保有id字段
      };

      this.setData({
        selectedGallery: galleryWithId,
        showGalleryDetail: true
      });
    } else {
      console.warn('[Gallery] 未找到指定的画廊:', articleId);
      console.log('[Gallery] 数据状态:', {
        currentArticlesLength: this.data.articles ? this.data.articles.length : 0,
        allArticlesLength: this.data.allArticles ? this.data.allArticles.length : 0,
        globalArticlesLength: app.globalData.galleryArticles ? app.globalData.galleryArticles.allArticles.length : 0,
        retryCount: retryCount
      });

      // 如果数据还没加载完成且未达到最大重试次数，等待后重试
      if (retryCount < maxRetries &&
          ((!this.data.articles || this.data.articles.length === 0) ||
           (!this.data.allArticles || this.data.allArticles.length === 0))) {
        console.log(`[Gallery] 画廊数据可能还在加载，${1 + retryCount}秒后重试`);
        setTimeout(() => {
          this.showGalleryDetailById(articleId, retryCount + 1);
        }, (1 + retryCount) * 1000); // 递增延迟时间
        return;
      }

      // 如果有数据但找不到指定画廊，显示错误
      wx.showToast({
        title: '内容不存在',
        icon: 'none'
      });
    }
  },

  // 根据位置显示画廊详情（新的灵活方式）
  showGalleryDetailByPosition(position, title) {
    console.log(`[Gallery] 尝试显示第${position}个画廊，标题: ${title}`);

    const { articles, allArticles } = this.data;
    const app = getApp();

    // 尝试从当前页面的文章列表查找
    let gallery = null;
    if (articles && articles.length >= position) {
      gallery = articles[position - 1]; // 数组索引从0开始，位置从1开始
      console.log('[Gallery] 从当前文章列表找到画廊:', gallery.title || gallery.mainTitle);
    }

    // 如果没找到，从所有文章缓存查找
    if (!gallery && allArticles && allArticles.length >= position) {
      gallery = allArticles[position - 1];
      console.log('[Gallery] 从所有文章缓存找到画廊:', gallery.title || gallery.mainTitle);
    }

    // 如果还没找到，从全局数据查找
    if (!gallery && app.globalData.galleryArticles && app.globalData.galleryArticles.allArticles &&
        app.globalData.galleryArticles.allArticles.length >= position) {
      gallery = app.globalData.galleryArticles.allArticles[position - 1];
      console.log('[Gallery] 从全局数据找到画廊:', gallery.title || gallery.mainTitle);
    }

    // 如果还是没找到，尝试按标题匹配
    if (!gallery) {
      const searchInList = (list) => {
        return list.find(item =>
          (item.title && item.title.includes(title)) ||
          (item.mainTitle && item.mainTitle.includes(title))
        );
      };

      if (articles && articles.length > 0) {
        gallery = searchInList(articles);
        if (gallery) {
          console.log('[Gallery] 通过标题匹配找到画廊:', gallery.title || gallery.mainTitle);
        }
      }

      if (!gallery && allArticles && allArticles.length > 0) {
        gallery = searchInList(allArticles);
        if (gallery) {
          console.log('[Gallery] 从所有文章缓存通过标题匹配找到画廊:', gallery.title || gallery.mainTitle);
        }
      }

      if (!gallery && app.globalData.galleryArticles && app.globalData.galleryArticles.allArticles) {
        gallery = searchInList(app.globalData.galleryArticles.allArticles);
        if (gallery) {
          console.log('[Gallery] 从全局数据通过标题匹配找到画廊:', gallery.title || gallery.mainTitle);
        }
      }
    }

    if (gallery) {
      console.log('[Gallery] 找到画廊，显示详情:', {
        _id: gallery._id,
        id: gallery.id,
        title: gallery.title || gallery.mainTitle,
        position: position
      });

      // 确保画廊数据有正确的ID字段供详情弹窗使用
      const galleryWithId = {
        ...gallery,
        id: gallery.id || gallery._id
      };

      this.setData({
        selectedGallery: galleryWithId,
        showGalleryDetail: true
      });
    } else {
      console.warn(`[Gallery] 未找到第${position}个画廊或标题包含"${title}"的画廊`);
      console.log('[Gallery] 数据状态:', {
        currentArticlesLength: articles ? articles.length : 0,
        allArticlesLength: allArticles ? allArticles.length : 0,
        globalArticlesLength: app.globalData.galleryArticles ? app.globalData.galleryArticles.allArticles.length : 0
      });

      wx.showToast({
        title: '内容不存在',
        icon: 'none'
      });
    }
  },

  // 画廊详情弹窗关闭事件
  onGalleryDetailClose() {
    console.log('[Gallery] 画廊详情弹窗关闭');
    this.setData({
      showGalleryDetail: false,
      selectedGallery: null
    });
  }
})