const app = getApp()

Page({
  data: {
    adminInfo: null,
    menuList: [
      {
        id: 'plans',
        name: '充值方案管理',
        icon: '/static/icons/recharge.png',
        url: '/pages/admin/recharge/plans/plans'
      },
      {
        id: 'records',
        name: '充值记录管理',
        icon: '/static/icons/record.png',
        url: '/pages/admin/recharge/records/records'
      },
      {
        id: 'consumption',
        name: '余额消费记录',
        icon: '/static/icons/consumption.png',
        url: '/pages/admin/recharge/consumption/consumption'
      }
    ]
  },

  onLoad() {
    // 检查管理员登录状态
    if (!app.globalData.isAdmin) {
      this.redirectToLogin()
      return
    }
    
    // 获取管理员信息
    this.setData({
      adminInfo: app.globalData.adminInfo || {
        username: '管理员'
      }
    })
  },
  
  onShow() {
    // 每次显示页面时检查管理员状态
    if (!app.globalData.isAdmin) {
      this.redirectToLogin()
    }
  },
  
  // 重定向到登录页
  redirectToLogin() {
    wx.showToast({
      title: '请先登录',
      icon: 'none'
    })
    
    setTimeout(() => {
      wx.redirectTo({
        url: '/pages/admin/login'
      })
    }, 1500)
  },
  
  // 导航到功能页面
  navigateToFunction(e) {
    const { url } = e.currentTarget.dataset
    
    if (!url) {
      wx.showToast({
        title: '功能开发中',
        icon: 'none'
      })
      return
    }
    
    wx.navigateTo({
      url: url
    })
  },
  
  // 返回上一页
  navigateBack() {
    wx.navigateBack({
      delta: 1
    })
  }
}) 