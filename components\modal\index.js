/* global Component getCurrentPages wx */

Component({
  properties: {
    visible: {
      type: Boolean,
      value: false,
      observer: function(newVal) {
        if (newVal) {
          this._notifyNavbarHide();
          this._hideTabBar();
        } else {
          this._notifyNavbarShow();
          this._showTabBar();
        }
      }
    },
    title: {
      type: String,
      value: ''
    },
    showHeader: {
      type: Boolean,
      value: true
    },
    contentClass: {
      type: String,
      value: ''
    },
    maskClosable: {
      type: Boolean,
      value: true
    },
    fullWidth: {
      type: Boolean,
      value: false
    }
  },

  data: {},

  lifetimes: {
    attached() {
      // 尝试获取页面实例
      const pages = getCurrentPages();
      this.page = pages[pages.length - 1];
      
      // 确保初始状态是隐藏的
      this.setData({
        visible: false
      });
      
      // 确保导航栏显示和TabBar显示
      this._notifyNavbarShow();
      this._showTabBar();
    },
    detached() {
      // 确保在组件销毁时恢复导航栏显示
      this._notifyNavbarShow();
      this._showTabBar();
    }
  },

  methods: {
    onClose() {
      this.setData({ visible: false });
      this.triggerEvent('close');
    },

    onMaskTap() {
      if (this.properties.maskClosable) {
        this.onClose();
      }
    },

    // 通知当前页面隐藏导航栏
    _notifyNavbarHide() {
      if (this.page && this.page.hideNavbar) {
        this.page.hideNavbar();
      }

      // 发送全局事件，通知所有导航栏组件
      this.triggerEvent('navbarControl', { action: 'hide' }, { bubbles: true, composed: true });
    },

    // 通知当前页面显示导航栏
    _notifyNavbarShow() {
      if (this.page && this.page.showNavbar) {
        this.page.showNavbar();
      }

      // 发送全局事件，通知所有导航栏组件
      this.triggerEvent('navbarControl', { action: 'show' }, { bubbles: true, composed: true });
    },
    
    // 隐藏底部tabBar
    _hideTabBar() {
      // 只操作自定义tabBar
      const tabBar = this.getTabBar();
      if (tabBar) {
        tabBar.hide();
      }
    },
    
    // 显示底部tabBar
    _showTabBar() {
      // 只操作自定义tabBar
      const tabBar = this.getTabBar();
      if (tabBar) {
        tabBar.show();
      }
    }
  }
}); 