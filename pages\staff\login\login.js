const app = getApp()

Page({
  /**
   * 页面的初始数据
   */
  data: {
    phoneNumber: '',
    password: '',
    isLoading: false,
    pageReady: false // 添加页面准备状态标记
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 检查是否已登录
    const staffInfo = wx.getStorageSync('staffInfo');
    const isStaff = wx.getStorageSync('isStaff');
    
    if (staffInfo && isStaff) {
      // 已登录，直接跳转到员工首页，不显示登录页面
      wx.redirectTo({
        url: '/pages/staff/index/index'
      });
    } else {
      // 未登录，显示登录页面
      this.setData({
        pageReady: true
      });
    }
  },

  /**
   * 输入手机号
   */
  inputPhoneNumber(e) {
    this.setData({
      phoneNumber: e.detail.value
    });
  },

  /**
   * 输入密码
   */
  inputPassword(e) {
    this.setData({
      password: e.detail.value
    });
  },

  /**
   * 登录
   */
  login() {
    const { phoneNumber, password } = this.data;
    
    // 表单验证
    if (!phoneNumber) {
      wx.showToast({
        title: '请输入手机号',
        icon: 'none'
      });
      return;
    }
    
    if (!password) {
      wx.showToast({
        title: '请输入密码',
        icon: 'none'
      });
      return;
    }

    // 显示加载状态
    this.setData({
      isLoading: true
    });

    // 调用云函数验证登录
    wx.cloud.callFunction({
      name: 'staffManager',
      data: {
        type: 'staff',
        action: 'staffLogin',
        data: {
          phoneNumber,
          password
        }
      },
      success: res => {
        console.log('登录结果', res);
        
        if (res.result && res.result.code === 0) {
          // 登录成功
          const staffInfo = res.result.data;
          
          // 保存员工信息到本地
          wx.setStorageSync('staffInfo', staffInfo);
          wx.setStorageSync('isStaff', true);
          
          // 同时设置到全局数据
          app.globalData.staffInfo = staffInfo;
          app.globalData.isStaff = true;
          
          // 直接跳转到员工首页，不显示登录成功提示
          wx.redirectTo({
            url: '/pages/staff/index/index',
            complete: () => {
              // 确保加载状态被正确重置
              this.setData({
                isLoading: false
              });
            }
          });
        } else {
          // 登录失败
          wx.showToast({
            title: res.result && res.result.message ? res.result.message : '登录失败，请检查账号密码',
            icon: 'none'
          });
          // 重置加载状态
          this.setData({
            isLoading: false
          });
        }
      },
      fail: err => {
        console.error('登录失败', err);
        wx.showToast({
          title: '登录失败，请稍后重试',
          icon: 'none'
        });
        // 重置加载状态
        this.setData({
          isLoading: false
        });
      }
    });
  },

  /**
   * 返回上一页
   */
  goBack() {
    wx.navigateBack();
  },

  /**
   * 跳转到员工首页
   */
  redirectToStaffIndex() {
    wx.redirectTo({
      url: '/pages/staff/index/index'
    });
  }
}) 