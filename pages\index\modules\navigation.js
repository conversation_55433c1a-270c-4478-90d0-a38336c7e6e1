/**
 * 导航控制模块
 * 负责导航栏控制、页面滚动处理、UI状态管理和页面过渡效果
 * @version 1.0.0
 */

const BaseModule = require('./base-module');
const { 
  SCROLL_CONFIG,
  ANIMATION_CONFIG,
  PAGE_EVENTS,
  ERROR_TYPES 
} = require('../constants/index-constants');
const { 
  throttle, 
  createScrollManager, 
  safeScrollToTop,
  createBatchSetData
} = require('../utils/ui-utils');

class NavigationModule extends BaseModule {
  constructor(pageContext) {
    super(pageContext);
    this.moduleName = 'Navigation';
    
    // 滚动状态管理
    this.lastScrollTop = 0;
    this.scrollDirection = 'none'; // 'up', 'down', 'none'
    this.scrollVelocity = 0;
    this.scrollTimer = null;
    
    // 导航栏状态
    this.navbarVisible = true;
    this.navbarTransitioning = false;
    
    // 节流函数
    this.throttledHandleScroll = this.throttle(this.handleScrollInternal.bind(this), SCROLL_CONFIG.SCROLL_THROTTLE_DELAY);
  }

  /**
   * 初始化导航控制模块
   */
  init() {
    try {
      // 减少导航控制模块初始化日志
      // console.log('[Navigation] 初始化导航控制模块');
      
      // 初始化导航状态
      this.initNavigationState();
      
      // 设置页面过渡效果
      this.setupPageTransitions();
      
      this.initialized = true;
      // 减少导航控制模块初始化完成日志
      // console.log('[Navigation] 导航控制模块初始化完成');
    } catch (error) {
      this.handleError(error, 'init');
    }
  }

  /**
   * 初始化导航状态
   */
  initNavigationState() {
    try {
      const currentData = this.data;
      
      // 确保必要的导航状态字段存在
      const requiredFields = {
        forceHideNavbar: currentData.forceHideNavbar || false,
        navigationHeight: currentData.navigationHeight || 0,
        showContent: currentData.showContent !== undefined ? currentData.showContent : false
      };

      // 只更新缺失的字段
      const updateData = {};
      Object.keys(requiredFields).forEach(key => {
        if (currentData[key] === undefined) {
          updateData[key] = requiredFields[key];
        }
      });

      if (Object.keys(updateData).length > 0) {
        this.safeSetData(updateData);
      }

      // 初始化导航栏状态
      this.navbarVisible = !this.data.forceHideNavbar;

      // 减少导航状态初始化完成日志
      // console.log('[Navigation] 导航状态初始化完成');
    } catch (error) {
      this.handleError(error, 'initNavigationState');
    }
  }

  /**
   * 处理页面滚动事件
   * @param {object} e - 滚动事件对象
   */
  handleScroll(e) {
    try {
      // 使用节流处理滚动事件
      this.throttledHandleScroll(e);
    } catch (error) {
      this.handleError(error, 'handleScroll');
    }
  }

  /**
   * 内部滚动处理逻辑
   * @param {object} e - 滚动事件对象
   */
  handleScrollInternal(e) {
    try {
      // 如果正在刷新或处于搜索状态，不处理滚动
      if (this.data.isRefreshing || this.data.searchFocused) {
        return;
      }

      const scrollTop = e.detail.scrollTop;
      const scrollDiff = scrollTop - this.lastScrollTop;
      
      // 计算滚动速度
      this.scrollVelocity = Math.abs(scrollDiff);
      
      // 更新滚动方向
      if (scrollDiff > 0) {
        this.scrollDirection = 'down';
      } else if (scrollDiff < 0) {
        this.scrollDirection = 'up';
      }

      // 导航栏显隐逻辑
      this.updateNavbarVisibility(scrollTop, scrollDiff);
      
      // 更新滚动位置
      this.lastScrollTop = scrollTop;
      
      // 触发滚动事件
      this.emit(PAGE_EVENTS.PAGE_SCROLL, {
        scrollTop: scrollTop,
        scrollDiff: scrollDiff,
        direction: this.scrollDirection,
        velocity: this.scrollVelocity
      });

      // 设置滚动结束检测
      this.setScrollEndDetection();
      
    } catch (error) {
      this.handleError(error, 'handleScrollInternal');
    }
  }

  /**
   * 更新导航栏可见性
   * @param {number} scrollTop - 当前滚动位置
   * @param {number} scrollDiff - 滚动差值
   */
  updateNavbarVisibility(scrollTop, scrollDiff) {
    try {
      let shouldHideNavbar = false;

      // 向上滚动显示导航栏，向下滚动隐藏导航栏
      if (scrollDiff < 0) { 
        // 向上滚动
        shouldHideNavbar = false;
      } else if (scrollDiff > 0 && scrollTop > SCROLL_CONFIG.NAVBAR_HIDE_THRESHOLD) { 
        // 向下滚动且不在顶部
        shouldHideNavbar = true;
      }

      // 只在状态真正改变时更新
      if (shouldHideNavbar !== this.data.forceHideNavbar) {
        this.safeSetData({ forceHideNavbar: shouldHideNavbar });
        this.navbarVisible = !shouldHideNavbar;

        // 触发导航栏状态变化事件
        this.emit('navbarVisibilityChange', {
          visible: this.navbarVisible,
          scrollTop: scrollTop,
          direction: this.scrollDirection
        });

        // 禁用频繁的导航栏状态更新日志
        // console.log('[Navigation] 导航栏状态更新:', this.navbarVisible ? '显示' : '隐藏');
      }
    } catch (error) {
      this.handleError(error, 'updateNavbarVisibility');
    }
  }

  /**
   * 设置滚动结束检测
   */
  setScrollEndDetection() {
    try {
      // 清除之前的定时器
      if (this.scrollTimer) {
        clearTimeout(this.scrollTimer);
      }

      // 设置新的定时器
      this.scrollTimer = setTimeout(() => {
        // 触发滚动结束事件
        this.emit('scrollEnd', {
          finalScrollTop: this.lastScrollTop,
          direction: this.scrollDirection,
          velocity: this.scrollVelocity
        });

        // 重置滚动状态
        this.scrollDirection = 'none';
        this.scrollVelocity = 0;
      }, SCROLL_CONFIG.SCROLL_END_DELAY);
    } catch (error) {
      this.handleError(error, 'setScrollEndDetection');
    }
  }

  /**
   * 导航栏控制
   * @param {object} e - 事件对象
   */
  onNavbarControl(e) {
    try {
      const action = e.detail.action;
      
      console.log('[Navigation] 导航栏控制:', action);
      
      if (action === 'hide') {
        this.hideNavbar();
      } else if (action === 'show') {
        this.showNavbar();
      } else if (action === 'toggle') {
        this.toggleNavbar();
      }
    } catch (error) {
      this.handleError(error, 'onNavbarControl');
    }
  }

  /**
   * 隐藏导航栏
   * @param {boolean} animated - 是否使用动画
   */
  hideNavbar(animated = true) {
    try {
      if (!this.navbarVisible) return;

      this.navbarTransitioning = true;
      
      this.safeSetData({ 
        forceHideNavbar: true 
      }, () => {
        this.navbarVisible = false;
        this.navbarTransitioning = false;
        
        // 触发导航栏隐藏事件
        this.emit('navbarHidden', { animated });
      });

      console.log('[Navigation] 导航栏已隐藏');
    } catch (error) {
      this.handleError(error, 'hideNavbar');
    }
  }

  /**
   * 显示导航栏
   * @param {boolean} animated - 是否使用动画
   */
  showNavbar(animated = true) {
    try {
      if (this.navbarVisible) return;

      this.navbarTransitioning = true;
      
      this.safeSetData({ 
        forceHideNavbar: false 
      }, () => {
        this.navbarVisible = true;
        this.navbarTransitioning = false;
        
        // 触发导航栏显示事件
        this.emit('navbarShown', { animated });
      });

      console.log('[Navigation] 导航栏已显示');
    } catch (error) {
      this.handleError(error, 'showNavbar');
    }
  }

  /**
   * 切换导航栏显示状态
   */
  toggleNavbar() {
    try {
      if (this.navbarVisible) {
        this.hideNavbar();
      } else {
        this.showNavbar();
      }
    } catch (error) {
      this.handleError(error, 'toggleNavbar');
    }
  }

  /**
   * 滚动到顶部
   * @param {object} options - 滚动选项
   */
  scrollToTop(options = {}) {
    try {
      const {
        duration = SCROLL_CONFIG.SCROLL_TO_TOP_DURATION,
        animated = true
      } = options;

      // 方法1: 使用wx.pageScrollTo
      if (animated) {
        wx.pageScrollTo({
          scrollTop: 0,
          duration: duration,
          success: () => {
            console.log('[Navigation] 页面已滚动到顶部');
            this.emit('scrollToTopComplete', { method: 'pageScrollTo' });
          },
          fail: (error) => {
            console.warn('[Navigation] pageScrollTo失败，尝试备用方案:', error);
            this.scrollToTopFallback();
          }
        });
      } else {
        this.scrollToTopFallback();
      }
    } catch (error) {
      this.handleError(error, 'scrollToTop');
      this.scrollToTopFallback();
    }
  }

  /**
   * 滚动到顶部的备用方案
   */
  scrollToTopFallback() {
    try {
      // 方法2: 使用scroll-view的scrollTop属性
      wx.createSelectorQuery()
        .select('.main-scroll')
        .node()
        .exec((res) => {
          if (res && res[0] && res[0].node) {
            res[0].node.scrollTop = 0;
            console.log('[Navigation] 使用备用方案滚动到顶部');
            this.emit('scrollToTopComplete', { method: 'fallback' });
          }
        });
    } catch (error) {
      this.handleError(error, 'scrollToTopFallback');
    }
  }

  /**
   * 设置页面过渡效果
   */
  setupPageTransitions() {
    try {
      // 设置内容显示动画
      if (!this.data.showContent) {
        setTimeout(() => {
          this.showContent();
        }, ANIMATION_CONFIG.CONTENT_SHOW_DURATION);
      }
    } catch (error) {
      this.handleError(error, 'setupPageTransitions');
    }
  }

  /**
   * 显示页面内容
   * @param {boolean} animated - 是否使用动画
   */
  showContent(animated = true) {
    try {
      if (this.data.showContent) return;

      this.safeSetData({ 
        showContent: true 
      }, () => {
        // 触发内容显示事件
        this.emit('contentShown', { animated });
        // console.log('[Navigation] 页面内容已显示');
      });
    } catch (error) {
      this.handleError(error, 'showContent');
    }
  }

  /**
   * 隐藏页面内容
   * @param {boolean} animated - 是否使用动画
   */
  hideContent(animated = true) {
    try {
      if (!this.data.showContent) return;

      this.safeSetData({ 
        showContent: false 
      }, () => {
        // 触发内容隐藏事件
        this.emit('contentHidden', { animated });
        console.log('[Navigation] 页面内容已隐藏');
      });
    } catch (error) {
      this.handleError(error, 'hideContent');
    }
  }

  /**
   * 处理页面过渡
   * @param {string} transition - 过渡类型
   * @param {object} options - 过渡选项
   */
  handlePageTransition(transition, options = {}) {
    try {
      const {
        duration = ANIMATION_CONFIG.PAGE_TRANSITION_DURATION,
        easing = ANIMATION_CONFIG.DEFAULT_EASING
      } = options;

      switch (transition) {
        case 'fadeIn':
          this.showContent(true);
          break;
        case 'fadeOut':
          this.hideContent(true);
          break;
        case 'slideUp':
          this.showNavbar(true);
          this.showContent(true);
          break;
        case 'slideDown':
          this.hideNavbar(true);
          break;
        default:
          console.warn('[Navigation] 未知的过渡类型:', transition);
      }

      // 触发过渡事件
      this.emit('pageTransition', {
        transition: transition,
        duration: duration,
        easing: easing
      });
    } catch (error) {
      this.handleError(error, 'handlePageTransition');
    }
  }

  /**
   * 更新UI状态
   * @param {object} state - UI状态对象
   */
  updateUIState(state) {
    try {
      if (!state || typeof state !== 'object') {
        console.warn('[Navigation] 无效的UI状态对象');
        return;
      }

      const validFields = ['forceHideNavbar', 'showContent', 'navigationHeight'];
      const updateData = {};

      // 过滤有效字段
      Object.keys(state).forEach(key => {
        if (validFields.includes(key)) {
          updateData[key] = state[key];
        }
      });

      if (Object.keys(updateData).length > 0) {
        this.safeSetData(updateData);

        // 更新内部状态
        if ('forceHideNavbar' in updateData) {
          this.navbarVisible = !updateData.forceHideNavbar;
        }

        // 触发UI状态更新事件
        this.emit('uiStateUpdated', {
          updates: updateData
        });

        console.log('[Navigation] UI状态已更新:', Object.keys(updateData));
      }
    } catch (error) {
      this.handleError(error, 'updateUIState');
    }
  }

  /**
   * 节流函数
   * @param {function} fn - 要节流的函数
   * @param {number} delay - 延迟时间
   * @returns {function} 节流后的函数
   */
  throttle(fn, delay) {
    let lastCall = 0;
    return function(...args) {
      const now = Date.now();
      if (now - lastCall >= delay) {
        fn.apply(this, args);
        lastCall = now;
      }
    };
  }

  /**
   * 防抖函数
   * @param {function} fn - 要防抖的函数
   * @param {number} delay - 延迟时间
   * @returns {function} 防抖后的函数
   */
  debounce(fn, delay) {
    let timer = null;
    return function(...args) {
      if (timer) clearTimeout(timer);
      timer = setTimeout(() => {
        fn.apply(this, args);
      }, delay);
    };
  }

  /**
   * 获取导航状态
   * @returns {object} 导航状态信息
   */
  getNavigationStatus() {
    try {
      return {
        navbarVisible: this.navbarVisible,
        navbarTransitioning: this.navbarTransitioning,
        forceHideNavbar: this.data.forceHideNavbar,
        showContent: this.data.showContent,
        navigationHeight: this.data.navigationHeight,
        lastScrollTop: this.lastScrollTop,
        scrollDirection: this.scrollDirection,
        scrollVelocity: this.scrollVelocity
      };
    } catch (error) {
      this.handleError(error, 'getNavigationStatus');
      return {};
    }
  }

  /**
   * 重置导航状态
   */
  resetNavigationState() {
    try {
      // 重置滚动状态
      this.lastScrollTop = 0;
      this.scrollDirection = 'none';
      this.scrollVelocity = 0;

      // 重置导航栏状态
      this.navbarVisible = true;
      this.navbarTransitioning = false;

      // 重置页面状态
      this.safeSetData({
        forceHideNavbar: false,
        showContent: true,
        navigationHeight: 0
      });

      // 清除定时器
      if (this.scrollTimer) {
        clearTimeout(this.scrollTimer);
        this.scrollTimer = null;
      }

      // 触发状态重置事件
      this.emit('navigationStateReset');

      console.log('[Navigation] 导航状态已重置');
    } catch (error) {
      this.handleError(error, 'resetNavigationState');
    }
  }

  /**
   * 监听UI状态变化
   * @param {string} field - 状态字段名
   * @param {function} callback - 回调函数
   */
  watchUIState(field, callback) {
    try {
      if (!field || typeof callback !== 'function') return;

      const eventName = `uiState:${field}`;
      this.on(eventName, callback);

      console.log('[Navigation] 开始监听UI状态:', field);
    } catch (error) {
      this.handleError(error, 'watchUIState');
    }
  }

  /**
   * 取消监听UI状态变化
   * @param {string} field - 状态字段名
   * @param {function} callback - 回调函数
   */
  unwatchUIState(field, callback) {
    try {
      if (!field) return;

      const eventName = `uiState:${field}`;
      this.off(eventName, callback);

      console.log('[Navigation] 停止监听UI状态:', field);
    } catch (error) {
      this.handleError(error, 'unwatchUIState');
    }
  }

  /**
   * 触发UI状态变化事件
   * @param {string} field - 状态字段名
   * @param {*} value - 新值
   * @param {*} oldValue - 旧值
   */
  notifyUIStateChange(field, value, oldValue) {
    try {
      if (!field) return;

      const eventName = `uiState:${field}`;
      this.emit(eventName, { field, value, oldValue });

      console.log('[Navigation] UI状态变化通知:', field, value);
    } catch (error) {
      this.handleError(error, 'notifyUIStateChange');
    }
  }

  /**
   * 批量更新UI状态
   * @param {object} stateUpdates - 状态更新对象
   * @param {boolean} notify - 是否触发通知
   */
  batchUpdateUIState(stateUpdates, notify = true) {
    try {
      if (!stateUpdates || typeof stateUpdates !== 'object') {
        console.warn('[Navigation] 无效的状态更新对象');
        return;
      }

      const validFields = ['forceHideNavbar', 'showContent', 'navigationHeight', 'isRefreshing'];
      const updateData = {};
      const oldValues = {};

      // 记录旧值并过滤有效字段
      Object.keys(stateUpdates).forEach(key => {
        if (validFields.includes(key)) {
          oldValues[key] = this.data[key];
          updateData[key] = stateUpdates[key];
        }
      });

      if (Object.keys(updateData).length > 0) {
        this.safeSetData(updateData, () => {
          // 更新内部状态
          if ('forceHideNavbar' in updateData) {
            this.navbarVisible = !updateData.forceHideNavbar;
          }

          // 触发批量状态更新事件
          this.emit('batchUIStateUpdate', {
            updates: updateData,
            oldValues: oldValues
          });

          // 触发单个字段变化通知
          if (notify) {
            Object.keys(updateData).forEach(key => {
              this.notifyUIStateChange(key, updateData[key], oldValues[key]);
            });
          }

          console.log('[Navigation] 批量UI状态更新完成:', Object.keys(updateData));
        });
      }
    } catch (error) {
      this.handleError(error, 'batchUpdateUIState');
    }
  }

  /**
   * 设置UI主题
   * @param {string} theme - 主题名称 ('light', 'dark', 'auto')
   */
  setUITheme(theme) {
    try {
      const validThemes = ['light', 'dark', 'auto'];
      if (!validThemes.includes(theme)) {
        console.warn('[Navigation] 无效的主题:', theme);
        return;
      }

      this.currentTheme = theme;
      wx.setStorageSync('ui_theme', theme);

      // 触发主题变化事件
      this.emit('themeChange', { theme });

      console.log('[Navigation] UI主题已设置:', theme);
    } catch (error) {
      this.handleError(error, 'setUITheme');
    }
  }

  /**
   * 获取当前UI主题
   * @returns {string} 当前主题
   */
  getUITheme() {
    try {
      return this.currentTheme || wx.getStorageSync('ui_theme') || 'light';
    } catch (error) {
      this.handleError(error, 'getUITheme');
      return 'light';
    }
  }

  /**
   * 设置UI布局模式
   * @param {string} layout - 布局模式 ('normal', 'compact', 'comfortable')
   */
  setUILayout(layout) {
    try {
      const validLayouts = ['normal', 'compact', 'comfortable'];
      if (!validLayouts.includes(layout)) {
        console.warn('[Navigation] 无效的布局模式:', layout);
        return;
      }

      this.currentLayout = layout;
      wx.setStorageSync('ui_layout', layout);

      // 触发布局变化事件
      this.emit('layoutChange', { layout });

      console.log('[Navigation] UI布局模式已设置:', layout);
    } catch (error) {
      this.handleError(error, 'setUILayout');
    }
  }

  /**
   * 获取当前UI布局模式
   * @returns {string} 当前布局模式
   */
  getUILayout() {
    try {
      return this.currentLayout || wx.getStorageSync('ui_layout') || 'normal';
    } catch (error) {
      this.handleError(error, 'getUILayout');
      return 'normal';
    }
  }

  /**
   * 适应屏幕方向变化
   * @param {string} orientation - 屏幕方向 ('portrait', 'landscape')
   */
  adaptToOrientation(orientation) {
    try {
      const isLandscape = orientation === 'landscape';
      
      // 根据屏幕方向调整UI
      const orientationUpdates = {
        navigationHeight: isLandscape ? 0 : this.data.navigationHeight
      };

      this.batchUpdateUIState(orientationUpdates);

      // 触发方向变化事件
      this.emit('orientationChange', { 
        orientation: orientation,
        isLandscape: isLandscape
      });

      console.log('[Navigation] 已适应屏幕方向:', orientation);
    } catch (error) {
      this.handleError(error, 'adaptToOrientation');
    }
  }

  /**
   * 响应系统状态变化
   * @param {object} systemState - 系统状态
   */
  respondToSystemState(systemState) {
    try {
      const {
        networkType,
        batteryLevel,
        memoryWarning,
        backgroundMode
      } = systemState;

      // 根据网络状态调整UI
      if (networkType === 'none') {
        this.emit('networkDisconnected');
      } else if (networkType === '2g' || networkType === '3g') {
        this.emit('slowNetwork', { networkType });
      }

      // 根据电量调整UI
      if (batteryLevel < 20) {
        this.emit('lowBattery', { batteryLevel });
      }

      // 内存警告处理
      if (memoryWarning) {
        this.emit('memoryWarning');
        // 可以在这里清理一些缓存
      }

      // 后台模式处理
      if (backgroundMode) {
        this.emit('enterBackground');
      }

      console.log('[Navigation] 系统状态响应完成');
    } catch (error) {
      this.handleError(error, 'respondToSystemState');
    }
  }

  /**
   * 优化UI性能
   */
  optimizeUIPerformance() {
    try {
      // 清理不必要的动画
      if (this.scrollVelocity > 50) {
        // 高速滚动时减少动画
        this.emit('highSpeedScroll', { velocity: this.scrollVelocity });
      }

      // 内存优化
      if (this.lastScrollTop > 10000) {
        // 滚动距离过大时触发优化
        this.emit('longScrollOptimization', { scrollTop: this.lastScrollTop });
      }

      console.log('[Navigation] UI性能优化完成');
    } catch (error) {
      this.handleError(error, 'optimizeUIPerformance');
    }
  }

  /**
   * 获取UI性能指标
   * @returns {object} 性能指标
   */
  getUIPerformanceMetrics() {
    try {
      return {
        scrollVelocity: this.scrollVelocity,
        scrollPosition: this.lastScrollTop,
        navbarTransitions: this.navbarTransitioning ? 1 : 0,
        theme: this.getUITheme(),
        layout: this.getUILayout(),
        timestamp: Date.now()
      };
    } catch (error) {
      this.handleError(error, 'getUIPerformanceMetrics');
      return {};
    }
  }

  /**
   * 保存UI偏好设置
   * @param {object} preferences - 偏好设置
   */
  saveUIPreferences(preferences) {
    try {
      const validPrefs = {
        theme: preferences.theme,
        layout: preferences.layout,
        autoHideNavbar: preferences.autoHideNavbar,
        animationEnabled: preferences.animationEnabled
      };

      // 过滤无效值
      Object.keys(validPrefs).forEach(key => {
        if (validPrefs[key] === undefined) {
          delete validPrefs[key];
        }
      });

      wx.setStorageSync('ui_preferences', validPrefs);

      // 应用偏好设置
      if (validPrefs.theme) {
        this.setUITheme(validPrefs.theme);
      }
      if (validPrefs.layout) {
        this.setUILayout(validPrefs.layout);
      }

      // 触发偏好设置保存事件
      this.emit('preferencesUpdated', { preferences: validPrefs });

      console.log('[Navigation] UI偏好设置已保存');
    } catch (error) {
      this.handleError(error, 'saveUIPreferences');
    }
  }

  /**
   * 加载UI偏好设置
   * @returns {object} 偏好设置
   */
  loadUIPreferences() {
    try {
      const preferences = wx.getStorageSync('ui_preferences') || {};
      
      // 应用加载的偏好设置
      if (preferences.theme) {
        this.setUITheme(preferences.theme);
      }
      if (preferences.layout) {
        this.setUILayout(preferences.layout);
      }

      console.log('[Navigation] UI偏好设置已加载');
      return preferences;
    } catch (error) {
      this.handleError(error, 'loadUIPreferences');
      return {};
    }
  }

  /**
   * 处理水平移动事件（触摸事件）
   * @param {object} e - 触摸事件对象
   */
  catchHorizontalMove(e) {
    try {
      // 阻止水平滑动，防止页面左右滑动
      // 这个方法主要用于防止在某些设备上的意外滑动
      console.log('[Navigation] 处理水平移动事件');
      
      // 可以在这里添加具体的触摸处理逻辑
      // 比如记录触摸位置、处理手势等
      
      return false; // 阻止默认行为
    } catch (error) {
      this.handleError(error, 'catchHorizontalMove');
      return false;
    }
  }

  /**
   * 销毁模块，清理资源
   */
  destroy() {
    try {
      console.log('[Navigation] 销毁导航控制模块');
      
      // 清除定时器
      if (this.scrollTimer) {
        clearTimeout(this.scrollTimer);
        this.scrollTimer = null;
      }
      
      // 重置状态
      this.resetNavigationState();
      
      super.destroy();
    } catch (error) {
      this.handleError(error, 'destroy');
    }
  }
}

module.exports = NavigationModule;