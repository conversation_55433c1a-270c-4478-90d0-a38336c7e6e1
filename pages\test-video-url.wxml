<view class="container">
  <view class="header">
    <text class="title">视频链接测试工具</text>
  </view>
  
  <view class="input-section">
    <view class="input-group">
      <text class="label">视频ID:</text>
      <input class="input" type="text" placeholder="输入视频ID" value="{{videoId}}" bindinput="onInputChange" data-field="videoId" />
    </view>
    
    <view class="input-group">
      <text class="label">baseId:</text>
      <input class="input" type="text" placeholder="输入baseId" value="{{baseId}}" bindinput="onInputChange" data-field="baseId" />
    </view>
    
    <view class="input-group">
      <text class="label">videoKey:</text>
      <input class="input" type="text" placeholder="输入videoKey" value="{{videoKey}}" bindinput="onInputChange" data-field="videoKey" />
    </view>
    
    <view class="button-group">
      <button class="btn" bindtap="testCloudFunction" loading="{{isLoading}}">测试云函数</button>
      <button class="btn" bindtap="testDirectConstruction" loading="{{isLoading}}">测试直接构造</button>
    </view>
    
    <view class="input-group">
      <text class="label">自定义URL:</text>
      <input class="input" type="text" placeholder="输入自定义URL" value="{{customUrl}}" bindinput="onInputChange" data-field="customUrl" />
      <button class="btn-small" bindtap="testCustomUrl">测试</button>
    </view>
  </view>
  
  <view class="result-section">
    <view class="result-header">
      <text class="subtitle">当前视频URL:</text>
      <view class="url-actions">
        <button class="btn-small" bindtap="copyVideoUrl">复制</button>
        <button class="btn-small" bindtap="playVideo">播放</button>
      </view>
    </view>
    <view class="url-display">
      <text class="url-text">{{videoUrl || '尚未获取URL'}}</text>
    </view>
    
    <view wx:if="{{videoUrl}}" class="video-container">
      <video id="testVideo" src="{{videoUrl}}" controls bindplay="onVideoPlay" binderror="onVideoError"></video>
    </view>
  </view>
  
  <view class="log-section">
    <view class="log-header">
      <text class="subtitle">测试日志:</text>
      <button class="btn-small" bindtap="clearResults">清空</button>
    </view>
    <scroll-view class="log-container" scroll-y>
      <view wx:for="{{testResults}}" wx:key="index" class="log-item">
        <text>{{item}}</text>
      </view>
    </scroll-view>
  </view>
</view> 