# 预约与员工系统开发文档

## 目录

1. [系统概述](#系统概述)
2. [数据库集合](#数据库集合)
3. [云函数](#云函数)
4. [预约流程](#预约流程)
5. [员工系统](#员工系统)
6. [核销流程](#核销流程)
7. [管理员功能](#管理员功能)

## 系统概述

本系统主要包含两大功能模块：预约功能和员工系统。预约功能允许用户在小程序中进行服务预约，员工系统则用于员工登录、查看业绩、核销订单等操作。两个系统通过云函数和数据库集合进行交互。

## 数据库集合

系统使用以下数据库集合：

### 1. `orders` 集合

存储所有预约订单信息。

主要字段：
- `_id`: 订单ID
- `userId`: 用户ID
- `openid`: 用户的微信openid
- `serviceId`: 服务ID
- `serviceName`: 服务名称
- `price`: 价格
- `totalAmount`: 总金额
- `appointmentDate`: 预约日期
- `appointmentTime`: 预约时间段
- `status`: 订单状态（pending待支付, confirmed已确认, completed已完成, cancelled已取消）
- `isPaid`: 是否已支付
- `paymentTime`: 支付时间
- `createTime`: 创建时间
- `updateTime`: 更新时间
- `verifyCode`: 核销码
- `staffId`: 处理该订单的员工ID
- `completeTime`: 完成时间

### 2. `staff` 集合

存储员工信息。

主要字段：
- `_id`: 员工ID
- `name`: 员工姓名
- `phoneNumber`: 手机号（用于登录）
- `password`: 密码
- `avatar`: 头像URL
- `status`: 状态（active活跃, inactive禁用）
- `commissionRate`: 提成比例
- `createTime`: 创建时间
- `updateTime`: 更新时间

### 3. `staff_performance` 集合

存储员工业绩记录。

主要字段：
- `_id`: 记录ID
- `staffId`: 员工ID
- `orderId`: 订单ID
- `date`: 日期
- `amount`: 金额
- `commission`: 提成金额
- `serviceName`: 服务名称

## 云函数

系统使用以下云函数：

### 1. `appointmentManager`

负责处理预约相关的所有操作。

**路径**: `/cloudfunctions/appointmentManager/`

主要功能：
- `createAppointment`: 创建预约
- `getAppointmentsByUser`: 获取用户的预约记录
- `getAppointmentDetail`: 获取预约详情
- `cancelAppointment`: 取消预约
- `getAvailableTimeSlots`: 获取可用的预约时间段
- `generateVerifyCode`: 生成核销码

### 2. `orderManager`

负责处理订单相关的操作，特别是员工端的订单处理。

**路径**: `/cloudfunctions/orderManager/`

主要功能：
- `getStaffPendingOrders`: 获取员工待处理订单
- `getStaffOrders`: 获取员工已处理订单
- `getOrderDetail`: 获取订单详情
- `completeOrder`: 完成订单
- `verifyOrderByCode`: 通过核销码验证订单

### 3. `staffManager`

负责处理员工账号和业绩相关的操作。

**路径**: `/cloudfunctions/staffManager/`

主要功能：
- `staffLogin`: 员工登录
- `getPerformance`: 获取员工业绩数据
- `getMyPerformance`: 获取个人业绩记录
- `addStaff`: 添加员工（管理员功能）
- `updateStaff`: 更新员工信息（管理员功能）
- `deleteStaff`: 删除员工（管理员功能）
- `getStaffList`: 获取员工列表（管理员功能）

## 预约流程

### 1. 用户发起预约

**前端页面**: `/pages/appointment/appointment`

流程：
1. 用户选择服务项目
2. 选择预约日期和时间段
3. 填写联系信息
4. 提交预约

**调用云函数**:
```javascript
wx.cloud.callFunction({
  name: 'appointmentManager',
  data: {
    action: 'createAppointment',
    serviceId: serviceId,
    appointmentDate: date,
    appointmentTime: time,
    // 其他预约信息
  }
})
```

### 2. 生成预约订单

**云函数处理**:
1. 创建订单记录
2. 生成唯一的核销码
3. 更新订单状态为待支付
4. 返回订单信息

**核心代码**:
```javascript
// 在appointmentManager云函数中
async function createAppointment(event, context) {
  // 生成核销码
  const verifyCode = generateRandomCode(6);
  
  // 创建订单
  const orderData = {
    userId: wxContext.OPENID,
    serviceId: event.serviceId,
    serviceName: event.serviceName,
    price: event.price,
    appointmentDate: event.appointmentDate,
    appointmentTime: event.appointmentTime,
    status: 'pending',
    isPaid: false,
    verifyCode: verifyCode,
    createTime: db.serverDate(),
    updateTime: db.serverDate()
  };
  
  // 插入数据库
  const result = await db.collection('orders').add({
    data: orderData
  });
  
  return {
    success: true,
    data: {
      orderId: result._id,
      verifyCode: verifyCode
    }
  };
}
```

### 3. 支付完成后更新订单状态

**前端页面**: `/pages/payment/payment`

**调用云函数**:
```javascript
wx.cloud.callFunction({
  name: 'appointmentManager',
  data: {
    action: 'updateOrderStatus',
    orderId: orderId,
    status: 'confirmed',
    isPaid: true
  }
})
```

## 员工系统

### 1. 员工登录

**前端页面**: `/pages/staff/login/login`

**调用云函数**:
```javascript
wx.cloud.callFunction({
  name: 'staffManager',
  data: {
    type: 'staff',
    action: 'staffLogin',
    data: {
      phoneNumber: phoneNumber,
      password: password
    }
  }
})
```

**云函数处理**:
```javascript
// 在staffManager云函数中
async function staffLogin(data) {
  const { phoneNumber, password } = data;
  
  // 查询员工信息
  const staffResult = await staffCollection.where({
    phoneNumber: phoneNumber,
    password: password
  }).get();
  
  if (staffResult.data.length === 0) {
    return {
      code: 401,
      message: '手机号或密码错误'
    };
  }
  
  const staff = staffResult.data[0];
  
  // 检查员工状态
  if (staff.status === 'inactive') {
    return {
      code: 403,
      message: '账号已被禁用'
    };
  }
  
  // 返回员工信息（不包含密码）
  const { password: _, ...staffInfo } = staff;
  
  return {
    code: 0,
    data: staffInfo,
    message: '登录成功'
  };
}
```

### 2. 员工首页

**前端页面**: `/pages/staff/index/index`

**调用云函数**:
```javascript
wx.cloud.callFunction({
  name: 'staffManager',
  data: {
    type: 'staff',
    action: 'getPerformance',
    data: {
      staffId: staffInfo._id
    }
  }
})
```

## 核销流程

### 1. 员工验证页面

**前端页面**: `/pages/staff/verify/verify`

功能：
1. 输入核销码验证订单
2. 查看待验证订单列表

### 2. 通过核销码验证订单

**调用云函数**:
```javascript
wx.cloud.callFunction({
  name: 'orderManager',
  data: {
    action: 'verifyOrderByCode',
    verifyCode: code,
    staffId: staffId
  }
})
```

**云函数处理**:
```javascript
// 在orderManager云函数中
async function verifyOrderByCode(event, context) {
  const { verifyCode, staffId } = event;
  
  // 查找对应核销码的订单
  const orderResult = await db.collection('orders')
    .where({
      verifyCode: verifyCode
    })
    .get();
  
  if (orderResult.data.length === 0) {
    return {
      success: false,
      message: '核销码无效'
    };
  }
  
  const order = orderResult.data[0];
  
  // 检查订单状态
  if (order.status === 'completed') {
    return {
      success: false,
      message: '订单已完成'
    };
  }
  
  if (order.status === 'cancelled') {
    return {
      success: false,
      message: '订单已取消'
    };
  }
  
  if (order.status !== 'confirmed') {
    return {
      success: false,
      message: '订单未确认，无法验证'
    };
  }
  
  // 更新订单状态
  const now = new Date();
  const completeTime = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')} ${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}`;
  
  await db.collection('orders').doc(order._id).update({
    data: {
      status: 'completed',
      staffId: staffId,
      completeTime: completeTime
    }
  });
  
  return {
    success: true,
    message: '订单已完成',
    data: order
  };
}
```

### 3. 查看待验证订单

**调用云函数**:
```javascript
wx.cloud.callFunction({
  name: 'orderManager',
  data: {
    action: 'getStaffPendingOrders',
    staffId: staffId
  }
})
```

## 管理员功能

### 1. 添加员工

**前端页面**: `/pages/admin/staff/add`

**调用云函数**:
```javascript
wx.cloud.callFunction({
  name: 'staffManager',
  data: {
    type: 'admin',
    action: 'addStaff',
    data: {
      name: name,
      phoneNumber: phoneNumber,
      password: password,
      commissionRate: commissionRate
    }
  }
})
```

**云函数处理**:
```javascript
// 在staffManager云函数中
async function addStaff(data) {
  const { name, phoneNumber, password, commissionRate } = data;
  
  // 检查手机号是否已存在
  const existingStaff = await staffCollection.where({
    phoneNumber: phoneNumber
  }).get();
  
  if (existingStaff.data.length > 0) {
    return {
      code: 409,
      message: '该手机号已被注册'
    };
  }
  
  // 创建员工记录
  const staffData = {
    name,
    phoneNumber,
    password,
    avatar: data.avatar || '',
    status: 'active',
    commissionRate: commissionRate || 0.3,
    createTime: db.serverDate(),
    updateTime: db.serverDate()
  };
  
  const result = await staffCollection.add({
    data: staffData
  });
  
  return {
    code: 0,
    data: {
      staffId: result._id
    },
    message: '添加员工成功'
  };
}
```

### 2. 管理员查看员工列表

**前端页面**: `/pages/admin/staff/staff`

**调用云函数**:
```javascript
wx.cloud.callFunction({
  name: 'staffManager',
  data: {
    type: 'admin',
    action: 'getStaffList'
  }
})
```

## 开发注意事项

1. **云函数部署**：修改云函数后需要重新部署才能生效。
2. **数据库集合**：系统会自动检查并创建必要的集合，无需手动创建。
3. **员工账号**：需要通过管理员后台添加员工账号，员工才能登录系统。
4. **核销码**：核销码在创建预约时自动生成，用户可在订单详情页查看。
5. **业绩计算**：员工完成订单后，系统会自动计算业绩并更新到员工业绩记录中。

## 后续开发建议

1. 完善员工业绩统计功能，增加按日、周、月筛选的详细报表。
2. 增加员工排班功能，与预约系统联动。
3. 增加订单评价功能，用户可对服务进行评分和评价。
4. 增强数据安全性，对密码等敏感信息进行加密存储。
5. 优化核销流程，增加扫码核销功能。 