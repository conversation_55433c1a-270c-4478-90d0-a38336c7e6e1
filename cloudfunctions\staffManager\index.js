// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV }) // 使用当前云环境
const db = cloud.database()
const staffCollection = db.collection('staff')
const staffPerformanceCollection = db.collection('staff_performance')
const _ = db.command

// 获取北京时间的辅助函数
function getBJTime() {
  // 获取当前UTC时间
  const now = new Date();
  // 计算北京时间（UTC+8）
  const bjTime = new Date(now.getTime() + 8 * 60 * 60 * 1000);
  return bjTime;
}

// 日期格式化辅助函数 - 仅提取日期部分
function formatDate(date) {
  // 确保我们有一个有效的日期对象
  const d = date instanceof Date ? date : new Date(date);
  if (isNaN(d.getTime())) {
    console.error('无效的日期对象:', date);
    return '';
  }
  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const day = String(d.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
}

// 日期时间格式化辅助函数
function formatDateTime(date) {
  // 确保我们有一个有效的日期对象
  const d = date instanceof Date ? date : new Date(date);
  return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}-${String(d.getDate()).padStart(2, '0')} ${String(d.getHours()).padStart(2, '0')}:${String(d.getMinutes()).padStart(2, '0')}`;
}

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID
  
  console.log('staffManager云函数被调用，参数：', event);
  
  try {
    // 初始化数据库集合
    await initCollections();
  } catch (err) {
    console.error('初始化集合过程中出错，但将继续执行:', err);
  }
  
  const { action, type } = event;
  
  // 管理员API
  if (type === 'admin') {
    switch (action) {
      case 'addStaff': return addStaff(event.data);
      case 'updateStaff': return updateStaff(event.data);
      case 'deleteStaff': return deleteStaff(event.data);
      case 'getStaffList': return getStaffList(event.data || {});
      case 'getStaffDetail': return getStaffDetail(event.data);
      case 'getStaffPerformance': return getStaffPerformance(event.data);
      case 'getStaffVerifiedAppointments': return getStaffVerifiedAppointments(event.data);
      case 'getBusinessStats': return getBusinessStats(event.data);
      case 'getStaffRechargeStats': return getStaffRechargeStats(event.data);
      default: return { code: 400, message: '无效的操作' };
    }
  }
  
  // 员工API
  if (type === 'staff') {
    switch (action) {
      case 'staffLogin': return staffLogin(event.data, context);
      case 'getMyPerformance': return getMyPerformance(event.data);
      case 'getPerformance': return getPerformance(event.data);
      case 'getPerformanceByDateRange': return getPerformanceByDateRange(event.data);
      case 'getStaffVerifiedAppointments':
        console.log('处理员工核销记录请求:', event.data);
        return getStaffVerifiedAppointments(event.data);
      case 'getStaffRechargeStats':
        console.log('处理员工充值统计请求:', event.data);
        return getStaffRechargeStats(event.data);
      case 'getStaffStatus': return getStaffStatus(event.data);
      case 'updateServiceStatus': return updateServiceStatus(event.data);
      case 'getStaffCommissions': return getStaffCommissions(event.data);
      default: return { code: 400, message: '无效的操作' };
    }
  }
  
  return { code: 400, message: '无效的请求类型' };
}

// 初始化数据库集合
async function initCollections() {
  try {
    // 获取所有集合列表
    let collections;
    try {
      collections = await db.listCollections().get();
    } catch (err) {
      console.error('获取集合列表失败:', err);
      // 如果无法获取集合列表，尝试直接创建集合
      await createCollectionsSafely();
      return true;
    }
    
    const collectionNames = collections.data.map(collection => collection.name);
    console.log('现有集合列表:', collectionNames);
    
    // 需要创建的集合列表
    const requiredCollections = ['staff', 'staff_performance'];
    
    // 检查并创建每个必需的集合
    for (const collectionName of requiredCollections) {
      if (!collectionNames.includes(collectionName)) {
        try {
          await db.createCollection(collectionName);
          console.log(`成功创建集合: ${collectionName}`);
        } catch (err) {
          console.error(`创建集合 ${collectionName} 失败:`, err);
          // 继续尝试创建其他集合，不中断流程
        }
      } else {
        console.log(`集合 ${collectionName} 已存在`);
      }
    }
    
    return true;
  } catch (err) {
    console.error('初始化集合失败:', err);
    // 即使初始化失败，也继续执行后续代码
    return false;
  }
}

// 安全创建集合的辅助函数
async function createCollectionsSafely() {
  const collections = ['staff', 'staff_performance'];
  
  for (const collection of collections) {
    try {
      await db.createCollection(collection);
      console.log(`创建集合成功: ${collection}`);
    } catch (err) {
      // 如果错误是因为集合已存在，这是正常的
      if (err.message && err.message.indexOf('collection already exists') !== -1) {
        console.log(`集合 ${collection} 已存在`);
      } else {
        console.error(`创建集合 ${collection} 失败:`, err);
      }
    }
  }
}

// 添加员工
async function addStaff(data) {
  const { name, phoneNumber, password, commissionRate, balanceCommissionRate } = data;
  
  if (!name || !phoneNumber || !password) {
    return {
      code: 400,
      message: '缺少必要参数'
    };
  }
  
  try {
    // 检查手机号是否已存在
    const existingStaff = await staffCollection.where({
      phoneNumber: phoneNumber
    }).get();
    
    if (existingStaff.data.length > 0) {
      return {
        code: 409,
        message: '该手机号已被注册'
      };
    }
    
    // 创建员工记录
    const staffData = {
      name,
      phoneNumber,
      password, // 实际应用中应该加密存储
      avatar: data.avatar || '',
      status: 'active',
      commissionRate: commissionRate || 0.3, // 默认30%提成（现金支付）
      balanceCommissionRate: balanceCommissionRate || 0.2, // 默认20%提成（余额支付）
      createTime: db.serverDate(),
      updateTime: db.serverDate()
    };
    
    const result = await staffCollection.add({
      data: staffData
    });
    
    return {
      code: 0,
      data: {
        staffId: result._id
      },
      message: '添加员工成功'
    };
  } catch (err) {
    console.error('添加员工失败', err);
    return {
      code: -1,
      message: '添加员工失败: ' + err.message
    };
  }
}

// 更新员工信息
async function updateStaff(data) {
  const { staffId, name, phoneNumber, password, commissionRate, balanceCommissionRate, status } = data;
  
  if (!staffId) {
    return {
      code: 400,
      message: '缺少员工ID'
    };
  }
  
  try {
    // 检查员工是否存在
    try {
      await staffCollection.doc(staffId).get();
    } catch (err) {
      return {
        code: 404,
        message: '员工不存在'
      };
    }
    
    // 准备更新数据
    const updateData = {
      updateTime: db.serverDate()
    };
    
    if (name) updateData.name = name;
    if (phoneNumber) updateData.phoneNumber = phoneNumber;
    if (password) updateData.password = password; // 实际应用中应该加密存储
    if (commissionRate !== undefined) updateData.commissionRate = commissionRate;
    if (balanceCommissionRate !== undefined) updateData.balanceCommissionRate = balanceCommissionRate;
    if (status) updateData.status = status;
    if (data.avatar) updateData.avatar = data.avatar;
    
    // 更新员工信息
    await staffCollection.doc(staffId).update({
      data: updateData
    });
    
    return {
      code: 0,
      message: '更新员工信息成功'
    };
  } catch (err) {
    console.error('更新员工信息失败', err);
    return {
      code: -1,
      message: '更新员工信息失败: ' + err.message
    };
  }
}

// 删除员工
async function deleteStaff(data) {
  const { staffId } = data;
  
  if (!staffId) {
    return {
      code: 400,
      message: '缺少员工ID'
    };
  }
  
  try {
    // 检查员工是否存在
    try {
      await staffCollection.doc(staffId).get();
    } catch (err) {
      return {
        code: 404,
        message: '员工不存在'
      };
    }
    
    // 检查是否有关联的业绩记录
    const performances = await staffPerformanceCollection.where({
      staffId: staffId
    }).count();
    
    if (performances.total > 0) {
      // 如果有业绩记录，不删除员工，而是将状态设置为inactive
      await staffCollection.doc(staffId).update({
        data: {
          status: 'inactive',
          updateTime: db.serverDate()
        }
      });
      
      return {
        code: 0,
        message: '员工已停用，由于存在业绩记录，员工信息未删除'
      };
    }
    
    // 如果没有业绩记录，可以直接删除
    await staffCollection.doc(staffId).remove();
    
    return {
      code: 0,
      message: '删除员工成功'
    };
  } catch (err) {
    console.error('删除员工失败', err);
    return {
      code: -1,
      message: '删除员工失败: ' + err.message
    };
  }
}

// 获取员工列表
async function getStaffList(data) {
  const { status } = data || {};
  
  try {
    let query = staffCollection;
    
    // 如果指定了状态，则按状态筛选
    if (status) {
      query = query.where({
        status: status
      });
    }
    
    // 按创建时间倒序排列
    const staff = await query.orderBy('createTime', 'desc').get();
    
    return {
      code: 0,
      data: {
        list: staff.data || []
      },
      message: '获取员工列表成功'
    };
  } catch (err) {
    console.error('获取员工列表失败', err);
    return {
      code: -1,
      message: '获取员工列表失败: ' + err.message
    };
  }
}

// 获取员工详情
async function getStaffDetail(data) {
  const { staffId } = data;
  
  if (!staffId) {
    return {
      code: 400,
      message: '缺少员工ID'
    };
  }
  
  try {
    // 获取员工信息
    const staff = await staffCollection.doc(staffId).get();
    
    if (!staff.data) {
      return {
        code: 404,
        message: '员工不存在'
      };
    }
    
    // 获取员工业绩统计
    const performances = await staffPerformanceCollection.where({
      staffId: staffId
    }).get();
    
    // 计算总业绩和提成
    let totalAmount = 0;
    let totalCommission = 0;
    let completedCount = 0;
    
    performances.data.forEach(item => {
      totalAmount += item.servicePrice;
      totalCommission += item.commission;
      completedCount++;
    });
    
    // 返回员工详情和业绩统计
    return {
      code: 0,
      data: {
        staff: staff.data,
        performance: {
          totalAmount,
          totalCommission,
          completedCount
        }
      },
      message: '获取员工详情成功'
    };
  } catch (err) {
    console.error('获取员工详情失败', err);
    return {
      code: -1,
      message: '获取员工详情失败: ' + err.message
    };
  }
}

// 获取员工业绩
async function getStaffPerformance(data) {
  const { staffId, startDate, endDate } = data;
  
  if (!staffId) {
    return {
      code: 400,
      message: '缺少员工ID'
    };
  }
  
  try {
    // 解析日期
    let startDateTime, endDateTime;
    
    if (startDate && endDate) {
      startDateTime = new Date(startDate);
      endDateTime = new Date(endDate);
      
      // 确保日期有效
      if (isNaN(startDateTime.getTime()) || isNaN(endDateTime.getTime())) {
        return {
          code: 400,
          message: '日期格式无效'
        };
      }
      
      // 设置结束日期为当天的23:59:59
      endDateTime.setHours(23, 59, 59, 999);
      
      console.log('查询员工业绩日期范围:', {
        startDate: formatDate(startDateTime),
        endDate: formatDate(endDateTime),
        startDateTimeStr: startDate,
        endDateTimeStr: endDate
      });
    }
    
    // 获取所有该员工的业绩记录
    let query = staffPerformanceCollection.where({
      staffId: staffId
    });
    
    // 获取业绩记录
    const performanceRecords = await query.get();
    
    console.log('获取到的员工业绩记录总数量:', performanceRecords.data.length);
    
    // 过滤日期范围内的记录
    let filteredRecords = performanceRecords.data;
    
    if (startDate && endDate) {
      filteredRecords = performanceRecords.data.filter(record => {
        // 解析记录的日期
        let recordDate;
        
        if (record.completeTime) {
          if (record.completeTime instanceof Date) {
            recordDate = record.completeTime;
          } else if (typeof record.completeTime === 'object' && record.completeTime._seconds) {
            // 处理 Firestore 时间戳
            recordDate = new Date(record.completeTime._seconds * 1000);
          } else if (typeof record.completeTime === 'string') {
            // 处理字符串格式的日期时间 (YYYY-MM-DD HH:MM)
            recordDate = new Date(record.completeTime);
          } else {
            console.log('无法解析日期格式:', record.completeTime);
            return false; // 跳过无效日期的记录
          }
        } else {
          console.log('记录缺少completeTime字段:', record);
          return false; // 跳过没有日期的记录
        }
        
        if (!recordDate || isNaN(recordDate.getTime())) {
          console.log('无法解析日期:', record.completeTime);
          return false; // 跳过无效日期的记录
        }
        
        // 检查记录是否在指定日期范围内
        return recordDate >= startDateTime && recordDate <= endDateTime;
      });
      
      console.log('日期范围内的员工业绩记录数量:', filteredRecords.length);
    }
    
    // 按日期降序排序
    filteredRecords.sort((a, b) => {
      const dateA = new Date(a.completeTime);
      const dateB = new Date(b.completeTime);
      return dateB - dateA;
    });
    
    // 计算总业绩和提成
    let totalAmount = 0;
    let totalCommission = 0;
    
    // 按支付方式分类统计
    let cashAmount = 0;
    let cashCommission = 0;
    let balanceAmount = 0;
    let balanceCommission = 0;
    let cashCount = 0;
    let balanceCount = 0;
    
    filteredRecords.forEach(item => {
      totalAmount += item.servicePrice || 0;
      totalCommission += item.commission || 0;
      
      // 按支付方式分类统计
      if (item.paymentMethod === 'balance') {
        balanceAmount += item.servicePrice || 0;
        balanceCommission += item.commission || 0;
        balanceCount++;
      } else {
        // 默认为现金支付
        cashAmount += item.servicePrice || 0;
        cashCommission += item.commission || 0;
        cashCount++;
      }
    });
    
    // 格式化数字为两位小数
    totalAmount = parseFloat(totalAmount.toFixed(2));
    totalCommission = parseFloat(totalCommission.toFixed(2));
    cashAmount = parseFloat(cashAmount.toFixed(2));
    cashCommission = parseFloat(cashCommission.toFixed(2));
    balanceAmount = parseFloat(balanceAmount.toFixed(2));
    balanceCommission = parseFloat(balanceCommission.toFixed(2));
    
    // 格式化记录中的金额
    const formattedRecords = filteredRecords.map(item => {
      return {
        ...item,
        servicePrice: parseFloat((item.servicePrice || 0).toFixed(2)),
        commission: parseFloat((item.commission || 0).toFixed(2))
      };
    });
    
    return {
      code: 0,
      data: {
        list: formattedRecords,
        totalAmount,
        totalCommission,
        count: filteredRecords.length,
        // 添加按支付方式分类的统计
        cashStats: {
          amount: cashAmount,
          commission: cashCommission,
          count: cashCount
        },
        balanceStats: {
          amount: balanceAmount,
          commission: balanceCommission,
          count: balanceCount
        }
      },
      message: '获取员工业绩成功'
    };
  } catch (err) {
    console.error('获取员工业绩失败', err);
    return {
      code: -1,
      message: '获取员工业绩失败: ' + err.message
    };
  }
}

// 员工登录
async function staffLogin(data, context) {
  const { phoneNumber, password } = data;

  if (!phoneNumber || !password) {
    return {
      code: 400,
      message: '缺少必要参数'
    };
  }

  try {
    // 获取微信上下文，获取openid
    const wxContext = cloud.getWXContext();
    const openid = wxContext.OPENID;

    console.log('员工登录，openid:', openid);

    // 查询员工信息
    const staffResult = await staffCollection.where({
      phoneNumber: phoneNumber,
      password: password  // 实际应用中应该使用加密比对
    }).get();

    if (staffResult.data.length === 0) {
      return {
        code: 401,
        message: '手机号或密码错误'
      };
    }

    const staff = staffResult.data[0];

    // 检查员工状态
    if (staff.status === 'inactive') {
      return {
        code: 403,
        message: '账号已被禁用'
      };
    }

    // 如果员工记录中没有openid，则更新添加
    if (!staff.openid && openid) {
      console.log('更新员工openid:', staff._id, openid);
      await staffCollection.doc(staff._id).update({
        data: {
          openid: openid,
          updateTime: db.serverDate()
        }
      });

      // 更新返回的员工信息
      staff.openid = openid;
    }

    // 返回员工信息（不包含密码）
    const { password: _, ...staffInfo } = staff;

    return {
      code: 0,
      data: staffInfo,
      message: '登录成功'
    };
  } catch (err) {
    console.error('员工登录失败', err);
    return {
      code: -1,
      message: '登录失败: ' + err.message
    };
  }
}

// 获取我的业绩
async function getMyPerformance(data) {
  const { staffId, timeRange } = data;
  
  if (!staffId) {
    return {
      code: 400,
      message: '缺少员工ID'
    };
  }
  
  try {
    // 检查员工是否存在
    try {
      await staffCollection.doc(staffId).get();
    } catch (err) {
      return {
        code: 404,
        message: '员工不存在'
      };
    }
    
    // 构建查询条件
    const query = {
      staffId: staffId
    };
    
    // 根据时间范围过滤
    if (timeRange) {
      if (timeRange.start) {
        query.date = _.gte(timeRange.start);
      }
      if (timeRange.end) {
        if (query.date) {
          query.date = _.and(query.date, _.lte(timeRange.end));
        } else {
          query.date = _.lte(timeRange.end);
        }
      }
    }
    
    // 获取业绩记录
    const performanceResult = await staffPerformanceCollection
      .where(query)
      .orderBy('date', 'desc')
      .get();
    
    return {
      code: 0,
      data: performanceResult.data,
      message: '获取业绩成功'
    };
  } catch (err) {
    console.error('获取业绩失败', err);
    return {
      code: -1,
      message: '获取业绩失败: ' + err.message
    };
  }
}

// 获取员工业绩数据
async function getPerformance(data) {
  const { staffId, period } = data;

  console.log('getPerformance 接收到的参数:', data);
  console.log('员工ID:', staffId);

  if (!staffId) {
    return {
      code: 400,
      message: '缺少员工ID'
    };
  }

  try {
    // 检查员工是否存在
    let staffInfo;
    try {
      console.log('尝试查询员工，ID:', staffId);
      const staffResult = await staffCollection.doc(staffId).get();
      console.log('查询员工结果:', staffResult);

      if (!staffResult.data) {
        console.log('员工数据为空');
        return {
          code: 404,
          message: '员工不存在'
        };
      }
      staffInfo = staffResult.data;
      console.log('找到员工信息:', staffInfo);
    } catch (err) {
      console.error('查询员工失败:', err);
      return {
        code: 404,
        message: '员工不存在: ' + err.message
      };
    }
    
    // 获取当前日期（北京时间）
    const now = getBJTime(); // 使用北京时间
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const todayStr = formatDate(today);
    
    // 获取昨天日期
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    const yesterdayStr = formatDate(yesterday);
    
    // 获取本周起始日期（周一）
    const dayOfWeek = now.getDay() || 7; // 将周日的0转换为7
    const mondayOffset = dayOfWeek - 1; // 计算到周一的天数差
    const thisWeekStart = new Date(today);
    thisWeekStart.setDate(thisWeekStart.getDate() - mondayOffset);
    const thisWeekStartStr = formatDate(thisWeekStart);
    
    // 获取本月起始日期
    const thisMonthStart = new Date(now.getFullYear(), now.getMonth(), 1);
    const thisMonthStartStr = formatDate(thisMonthStart);
    
    console.log('计算业绩的日期范围:', {
      today: todayStr,
      yesterday: yesterdayStr,
      thisWeekStart: thisWeekStartStr,
      thisMonthStart: thisMonthStartStr,
      currentTime: formatDateTime(now)
    });
    
    // 从 staff_performance 集合中获取业绩数据
    const performanceRecords = await staffPerformanceCollection
      .where({
        staffId: staffId
      })
      .get();
    
    console.log('获取到的业绩记录数量:', performanceRecords.data.length);
    
    // 初始化业绩统计
    let todayIncome = 0;
    let todayCommission = 0;
    let yesterdayIncome = 0;
    let yesterdayCommission = 0;
    let weekIncome = 0;
    let weekCommission = 0;
    let monthIncome = 0;
    let monthCommission = 0;
    let totalIncome = 0;
    let totalCommission = 0;
    
    // 遍历业绩记录，计算各时间段的业绩
    performanceRecords.data.forEach((record, index) => {
      // 计算总提成和总收入
      totalIncome += record.servicePrice || 0;
      totalCommission += record.commission || 0;
      
      // 获取记录的日期
      let recordDate;
      let recordDateStr;
      
      console.log(`处理记录 ${index + 1}/${performanceRecords.data.length}:`, {
        appointmentId: record.appointmentId,
        completeTime: record.completeTime,
        commission: record.commission,
        paymentMethod: record.paymentMethod || 'cash' // 默认为现金支付
      });
      
      // 解析日期
      if (record.completeTime) {
        if (record.completeTime instanceof Date) {
          recordDate = record.completeTime;
        } else if (typeof record.completeTime === 'object' && record.completeTime._seconds) {
          // 处理 Firestore 时间戳
          recordDate = new Date(record.completeTime._seconds * 1000);
        } else if (typeof record.completeTime === 'string') {
          // 处理字符串格式的日期时间 (YYYY-MM-DD HH:MM)
          try {
            if (record.completeTime.includes(' ')) {
              // 包含时间部分的日期字符串
              recordDate = new Date(record.completeTime);
            } else {
              // 仅包含日期部分的字符串
              recordDate = new Date(record.completeTime);
            }
          } catch (err) {
            console.error(`解析日期字符串失败: ${record.completeTime}`, err);
            recordDate = null;
          }
        } else {
          console.log(`记录 ${index + 1} 的completeTime格式无法识别:`, record.completeTime);
          recordDate = null;
        }
      } else {
        console.log(`记录 ${index + 1} 缺少completeTime字段:`, record);
        recordDate = null;
      }
      
      // 如果无法解析日期，跳过此记录
      if (!recordDate || isNaN(recordDate.getTime())) {
        console.log(`记录 ${index + 1} 的日期无效:`, record.completeTime);
        return; // 跳过无效日期的记录
      }
      
      // 格式化日期为YYYY-MM-DD格式，用于比较
      recordDateStr = formatDate(recordDate);
      
      console.log(`记录 ${index + 1} 的日期比较:`, {
        recordDateStr: recordDateStr,
        todayStr: todayStr,
        isToday: recordDateStr === todayStr
      });
      
      // 根据日期计算业绩
      // 今日业绩
      if (recordDateStr === todayStr) {
        todayIncome += record.servicePrice || 0;
        todayCommission += record.commission || 0;
        console.log(`记录 ${index + 1} 是今日业绩，累加:`, {
          servicePrice: record.servicePrice,
          commission: record.commission,
          todayIncome: todayIncome,
          todayCommission: todayCommission
        });
      }
      
      // 昨日业绩
      if (recordDateStr === yesterdayStr) {
        yesterdayIncome += record.servicePrice || 0;
        yesterdayCommission += record.commission || 0;
        console.log(`记录 ${index + 1} 是昨日业绩，累加:`, {
          yesterdayIncome: yesterdayIncome,
          yesterdayCommission: yesterdayCommission
        });
      }
      
      // 本周业绩
      const recordTime = recordDate.getTime();
      const weekStartTime = thisWeekStart.getTime();
      if (recordTime >= weekStartTime) {
        weekIncome += record.servicePrice || 0;
        weekCommission += record.commission || 0;
        console.log(`记录 ${index + 1} 是本周业绩，累加:`, {
          weekIncome: weekIncome,
          weekCommission: weekCommission
        });
      }
      
      // 本月业绩
      const monthStartTime = thisMonthStart.getTime();
      if (recordTime >= monthStartTime) {
        monthIncome += record.servicePrice || 0;
        monthCommission += record.commission || 0;
        console.log(`记录 ${index + 1} 是本月业绩，累加:`, {
          monthIncome: monthIncome,
          monthCommission: monthCommission
        });
      }
    });
    
    console.log('普通业绩统计结果:', {
      todayCommission,
      yesterdayCommission,
      weekCommission,
      monthCommission,
      totalCommission
    });
    
    // 获取充值推广佣金记录
    console.log('获取充值推广佣金记录，员工ID:', staffId);

    // 从 commission_settlements 集合中获取充值推广佣金记录
    try {
      const commissionRecords = await db.collection('commission_settlements')
        .where({
          promoterStaffId: staffId,
          status: 'pending' // 只统计待结算的佣金
        })
        .get();
          
        console.log('获取到的充值推广佣金记录数量:', commissionRecords.data.length);
        
        // 遍历佣金记录，添加到业绩统计中
        commissionRecords.data.forEach((record, index) => {
          // 计算总佣金
          totalCommission += record.amount || 0;
          
          // 获取记录的日期
          let recordDate;
          let recordDateStr;
          
          // 解析日期
          if (record.createTime) {
            if (record.createTime instanceof Date) {
              recordDate = record.createTime;
            } else if (typeof record.createTime === 'object' && record.createTime._seconds) {
              recordDate = new Date(record.createTime._seconds * 1000);
            } else if (typeof record.createTime === 'string') {
              recordDate = new Date(record.createTime);
            } else {
              console.log(`佣金记录 ${index + 1} 的createTime格式无法识别:`, record.createTime);
              recordDate = null;
            }
          } else {
            console.log(`佣金记录 ${index + 1} 缺少createTime字段:`, record);
            recordDate = null;
          }
          
          // 如果无法解析日期，跳过此记录
          if (!recordDate || isNaN(recordDate.getTime())) {
            console.log(`佣金记录 ${index + 1} 的日期无效:`, record.createTime);
            return; // 跳过无效日期的记录
          }
          
          // 格式化日期为YYYY-MM-DD格式，用于比较
          recordDateStr = formatDate(recordDate);
          
          console.log(`佣金记录 ${index + 1} 的日期比较:`, {
            recordDateStr: recordDateStr,
            amount: record.amount,
            todayStr: todayStr,
            isToday: recordDateStr === todayStr
          });
          
          // 根据日期计算业绩
          // 今日佣金
          if (recordDateStr === todayStr) {
            todayCommission += record.amount || 0;
            console.log(`佣金记录 ${index + 1} 是今日佣金，累加:`, {
              amount: record.amount,
              todayCommission: todayCommission
            });
          }
          
          // 昨日佣金
          if (recordDateStr === yesterdayStr) {
            yesterdayCommission += record.amount || 0;
            console.log(`佣金记录 ${index + 1} 是昨日佣金，累加:`, {
              amount: record.amount,
              yesterdayCommission: yesterdayCommission
            });
          }
          
          // 本周佣金
          const recordTime = recordDate.getTime();
          const weekStartTime = thisWeekStart.getTime();
          if (recordTime >= weekStartTime) {
            weekCommission += record.amount || 0;
            console.log(`佣金记录 ${index + 1} 是本周佣金，累加:`, {
              amount: record.amount,
              weekCommission: weekCommission
            });
          }
          
          // 本月佣金
          const monthStartTime = thisMonthStart.getTime();
          if (recordTime >= monthStartTime) {
            monthCommission += record.amount || 0;
            console.log(`佣金记录 ${index + 1} 是本月佣金，累加:`, {
              amount: record.amount,
              monthCommission: monthCommission
            });
          }
        });
        
        console.log('添加佣金后的业绩统计结果:', {
          todayCommission,
          yesterdayCommission,
          weekCommission,
          monthCommission,
          totalCommission
        });
    } catch (err) {
      console.error('获取充值推广佣金记录失败:', err);
      // 失败不中断，继续返回普通业绩
    }
    
    // 返回业绩统计
    return {
      code: 0,
      data: {
        todayIncome: parseFloat(todayIncome.toFixed(2)),
        todayCommission: parseFloat(todayCommission.toFixed(2)),
        yesterdayIncome: parseFloat(yesterdayIncome.toFixed(2)),
        yesterdayCommission: parseFloat(yesterdayCommission.toFixed(2)),
        weekIncome: parseFloat(weekIncome.toFixed(2)),
        weekCommission: parseFloat(weekCommission.toFixed(2)),
        monthIncome: parseFloat(monthIncome.toFixed(2)),
        monthCommission: parseFloat(monthCommission.toFixed(2)),
        totalIncome: parseFloat(totalIncome.toFixed(2)),
        totalCommission: parseFloat(totalCommission.toFixed(2))
      },
      message: '获取业绩成功'
    };
  } catch (err) {
    console.error('获取业绩失败', err);
    return {
      code: -1,
      message: '获取业绩失败: ' + err.message
    };
  }
}

// 获取员工核销过的预约记录
async function getStaffVerifiedAppointments(data) {
  const { staffId, status, paymentMethod } = data;
  
  if (!staffId) {
    return {
      code: 400,
      message: '缺少员工ID'
    };
  }
  
  try {
    // 检查员工是否存在并获取员工信息（包括提成比例）
    let staffInfo;
    let staffCommissionRate = 0.3; // 默认提成比例
    let balanceCommissionRate = 0.2; // 默认余额支付提成比例
    
    try {
      const staffResult = await staffCollection.doc(staffId).get();
      if (!staffResult.data) {
        return {
          code: 404,
          message: '员工不存在'
        };
      }
      staffInfo = staffResult.data;
      
      // 获取员工的提成比例
      if (staffInfo.commissionRate !== undefined) {
        staffCommissionRate = staffInfo.commissionRate;
        console.log(`获取到员工 ${staffInfo.name}(${staffId}) 的现金提成比例: ${staffCommissionRate * 100}%`);
      }
      
      // 获取员工的余额支付提成比例
      if (staffInfo.balanceCommissionRate !== undefined) {
        balanceCommissionRate = staffInfo.balanceCommissionRate;
        console.log(`获取到员工 ${staffInfo.name}(${staffId}) 的余额提成比例: ${balanceCommissionRate * 100}%`);
      }
    } catch (err) {
      return {
        code: 404,
        message: '员工不存在'
      };
    }
    
    // 构建查询条件 - 从 appointments 集合获取数据
    const appointmentsCollection = db.collection('appointments');
    const query = {
      verifyStaffId: staffId,
      verified: true
    };
    
    // 根据状态过滤
    if (status) {
      query.status = status;
    }
    
    // 根据支付方式过滤
    if (paymentMethod === 'cash' || paymentMethod === 'balance') {
      query.paymentMethod = paymentMethod;
    }
    
    console.log('查询核销记录，条件：', query);
    
    // 获取预约记录，按核销时间降序排序
    const appointmentsResult = await appointmentsCollection
      .where(query)
      .orderBy('verifyTime', 'desc')
      .get();
    
    console.log('从appointments集合查询到的记录数量:', appointmentsResult.data.length);
    
    // 获取员工业绩记录，用于获取提成信息
    const performanceResult = await staffPerformanceCollection
      .where({
        staffId: staffId
      })
      .get();
    
    console.log('从staff_performance集合查询到的记录数量:', performanceResult.data.length);
    
    // 创建业绩记录的映射，以appointmentId为键
    const performanceMap = {};
    performanceResult.data.forEach(record => {
      if (record.appointmentId) {
        performanceMap[record.appointmentId] = record;
      }
    });
    
    // 合并预约记录和业绩记录
    const mergedRecords = appointmentsResult.data.map(appointment => {
      // 查找对应的业绩记录
      const performance = performanceMap[appointment._id] || {};
      
      // 确定使用的提成比例
      const actualCommissionRate = appointment.paymentMethod === 'balance' 
        ? (performance.commissionRate || balanceCommissionRate)
        : (performance.commissionRate || staffCommissionRate);
      
      // 合并数据，优先使用业绩记录中的数据
      return {
        ...appointment,
        commission: performance.commission || (appointment.servicePrice * actualCommissionRate),
        commissionRate: performance.commissionRate || actualCommissionRate,
        // 确保包含所有必要的时间信息
        date: appointment.date || '',
        time: appointment.time || '',
        createTime: appointment.createTime || '',
        verifyTime: appointment.verifyTime || '',
        completeTime: performance.completeTime || appointment.verifyTime || '',
        paymentMethod: appointment.paymentMethod || 'cash' // 默认为现金支付
      };
    });
    
    // 确保按核销时间排序
    const sortedRecords = mergedRecords.sort((a, b) => {
      // 如果verifyTime是字符串格式，先转换为时间戳再比较
      const timeA = a.verifyTime ? (typeof a.verifyTime === 'string' ? new Date(a.verifyTime).getTime() : a.verifyTime) : 0;
      const timeB = b.verifyTime ? (typeof b.verifyTime === 'string' ? new Date(b.verifyTime).getTime() : b.verifyTime) : 0;
      // 降序排序，最新的在前面
      return timeB - timeA;
    });
    
    // 计算总收入和提成，按支付方式分类
    let totalIncome = 0;
    let totalCommission = 0;
    let cashIncome = 0;
    let cashCommission = 0;
    let balanceIncome = 0;
    let balanceCommission = 0;
    let cashCount = 0;
    let balanceCount = 0;
    
    sortedRecords.forEach(record => {
      totalIncome += record.servicePrice || 0;
      totalCommission += record.commission || 0;
      
      if (record.paymentMethod === 'balance') {
        balanceIncome += record.servicePrice || 0;
        balanceCommission += record.commission || 0;
        balanceCount++;
      } else {
        cashIncome += record.servicePrice || 0;
        cashCommission += record.commission || 0;
        cashCount++;
      }
    });
    
    return {
      code: 0,
      data: sortedRecords,
      stats: {
        totalIncome,
        totalCommission,
        count: sortedRecords.length,
        cash: {
          income: cashIncome,
          commission: cashCommission,
          count: cashCount
        },
        balance: {
          income: balanceIncome,
          commission: balanceCommission,
          count: balanceCount
        }
      },
      message: '获取核销记录成功'
    };
  } catch (err) {
    console.error('获取核销记录失败', err);
    return {
      code: -1,
      message: '获取核销记录失败: ' + err.message
    };
  }
}

// 按日期范围获取员工业绩数据
async function getPerformanceByDateRange(data) {
  const { staffId, startDate, endDate } = data;
  
  if (!staffId) {
    return {
      code: 400,
      message: '缺少员工ID'
    };
  }
  
  if (!startDate || !endDate) {
    return {
      code: 400,
      message: '缺少日期范围'
    };
  }
  
  try {
    // 检查员工是否存在
    try {
      const staffResult = await staffCollection.doc(staffId).get();
      if (!staffResult.data) {
        return {
          code: 404,
          message: '员工不存在'
        };
      }
    } catch (err) {
      return {
        code: 404,
        message: '员工不存在'
      };
    }
    
    // 解析日期
    const startDateTime = new Date(startDate);
    const endDateTime = new Date(endDate);
    
    // 确保日期有效
    if (isNaN(startDateTime.getTime()) || isNaN(endDateTime.getTime())) {
      return {
        code: 400,
        message: '日期格式无效'
      };
    }
    
    // 设置结束日期为当天的23:59:59
    endDateTime.setHours(23, 59, 59, 999);
    
    console.log('查询日期范围:', {
      startDate: formatDate(startDateTime),
      endDate: formatDate(endDateTime)
    });
    
    // 从 staff_performance 集合中获取业绩数据
    const performanceRecords = await staffPerformanceCollection
      .where({
        staffId: staffId
      })
      .get();
    
    console.log('获取到的业绩记录数量:', performanceRecords.data.length);
    
    // 初始化业绩统计
    let totalIncome = 0;
    let totalCommission = 0;
    let filteredRecords = [];
    
    // 遍历业绩记录，计算指定日期范围内的业绩
    performanceRecords.data.forEach(record => {
      // 获取记录的日期
      let recordDate;
      if (record.completeTime) {
        if (record.completeTime instanceof Date) {
          recordDate = record.completeTime;
        } else if (typeof record.completeTime === 'object' && record.completeTime._seconds) {
          // 处理 Firestore 时间戳
          recordDate = new Date(record.completeTime._seconds * 1000);
        } else {
          // 尝试从字符串解析日期
          recordDate = new Date(record.completeTime);
        }
      }
      
      if (!recordDate || isNaN(recordDate.getTime())) {
        console.log('无法解析日期:', record.completeTime);
        return; // 跳过无效日期的记录
      }
      
      // 检查记录是否在指定日期范围内
      if (recordDate >= startDateTime && recordDate <= endDateTime) {
        totalIncome += record.servicePrice || 0;
        totalCommission += record.commission || 0;
        
        // 添加到过滤后的记录列表
        filteredRecords.push({
          ...record,
          dateStr: formatDate(recordDate)
        });
      }
    });
    
    // 按日期降序排序
    filteredRecords.sort((a, b) => {
      const dateA = new Date(a.completeTime);
      const dateB = new Date(b.completeTime);
      return dateB - dateA;
    });
    
    // 返回业绩统计
    return {
      code: 0,
      data: {
        records: filteredRecords,
        totalIncome,
        totalCommission,
        count: filteredRecords.length,
        dateRange: {
          startDate: formatDate(startDateTime),
          endDate: formatDate(endDateTime)
        }
      },
      message: '获取业绩成功'
    };
  } catch (err) {
    console.error('获取业绩失败', err);
    return {
      code: -1,
      message: '获取业绩失败: ' + err.message
    };
  }
}

// 获取店铺业绩统计数据
async function getBusinessStats(data) {
  const { startDate, endDate, staffId } = data;
  
  if (!startDate || !endDate) {
    return {
      code: 400,
      message: '缺少日期范围参数'
    };
  }
  
  try {
    // 解析日期 - 使用更严格的日期处理
    let startDateTime, endDateTime;
    
    // 确保开始日期是当天的开始 (00:00:00)
    startDateTime = new Date(startDate);
    startDateTime.setHours(0, 0, 0, 0);
    
    // 确保结束日期是当天的结束 (23:59:59.999)
    endDateTime = new Date(endDate);
    endDateTime.setHours(23, 59, 59, 999);
    
    // 打印日期范围信息，便于调试
    console.log('查询业绩日期范围(优化后):', {
      startDateTime: startDateTime.toISOString(),
      endDateTime: endDateTime.toISOString(),
      startDate: formatDate(startDateTime),
      endDate: formatDate(endDateTime),
      startDateTimeStr: startDate,
      endDateTimeStr: endDate
    });
    
    // 从 staff_performance 集合中获取所有业绩数据，然后在内存中过滤
    // 这样可以处理不同格式的日期字段
    let query = {};
    
    // 如果指定了员工ID，则添加筛选条件
    if (staffId) {
      query.staffId = staffId;
    }
    
    // 获取业绩记录
    const performanceRecords = await staffPerformanceCollection
      .where(query)
      .get();
    
    console.log('获取到的业绩记录总数量:', performanceRecords.data.length);



    // 初始化统计数据
    const businessData = {
      totalOrders: 0,
      totalCashIncome: 0,     // 现金收入
      totalBalanceIncome: 0,  // 余额收入（不计入总收入）
      totalIncome: 0,         // 总收入（现金收入+充值收入）
      totalCashCommission: 0, // 现金支付的提成
      totalBalanceCommission: 0, // 余额支付的提成
      totalCommission: 0,     // 总提成（现金+余额）
      totalExpense: 0,        // 其他支出
      totalPointsExpense: 0,  // 积分支出
      totalRefundExpense: 0,  // 退款支出
      totalPromotionCommission: 0, // 推广佣金支出
      totalProfit: 0,         // 总利润
      rechargeIncome: 0,      // 充值收入
      staffStats: [],
      dateStats: [],
      serviceStats: []
    };

    // 初始化映射变量（在所有分支中都需要）
    const dateMap = {}; // 日期业绩映射
    const staffMap = {}; // 员工业绩映射
    const serviceMap = {}; // 服务项目业绩映射
    
    // 如果没有数据，直接返回空统计
    if (performanceRecords.data.length === 0) {
      // 即使没有业绩记录，仍然需要获取充值收入
      await getRechargeIncome();

      // 获取支出数据
      try {
        // 构建支出查询条件
        let expenseQuery = {
          createTime: db.command.gte(startDateTime).and(db.command.lte(endDateTime))
        };

        // 如果指定了员工ID，只获取该员工的支出记录
        if (staffId) {
          expenseQuery.staffId = staffId;
        }

        // 先获取所有支出记录，然后在内存中过滤
        let baseExpenseQuery = {};
        if (staffId) {
          baseExpenseQuery.staffId = staffId;
        }

        const allExpenseResult = await db.collection('expenses').where(baseExpenseQuery).get();

        console.log('获取到的所有支出记录数量:', allExpenseResult.data.length);

        // 在内存中过滤日期范围
        const filteredExpenses = allExpenseResult.data.filter(expense => {
          if (!expense.createTime) {
            return false;
          }

          let expenseDate;

          // 处理不同的时间格式
          if (typeof expense.createTime === 'string') {
            // 字符串格式，直接解析
            expenseDate = new Date(expense.createTime);
          } else if (expense.createTime instanceof Date) {
            // Date对象格式，转换为北京时间
            expenseDate = new Date(expense.createTime.getTime() + 8 * 60 * 60 * 1000);
          } else if (expense.createTime && expense.createTime._seconds) {
            // Firestore时间戳格式，转换为北京时间
            expenseDate = new Date((expense.createTime._seconds * 1000) + 8 * 60 * 60 * 1000);
          } else {
            console.log('无法解析支出时间格式:', expense.createTime);
            return false;
          }

          // 检查日期是否在范围内
          const isInRange = expenseDate >= startDateTime && expenseDate <= endDateTime;

          console.log('支出记录时间比较(无业绩记录分支):', {
            expenseId: expense._id,
            createTime: expense.createTime,
            expenseDate: expenseDate.toISOString(),
            isInRange: isInRange
          });

          return isInRange;
        });

        console.log('过滤后的支出记录数量:', filteredExpenses.length);

        filteredExpenses.forEach(expense => {
          const expenseAmount = parseFloat(expense.amount || 0);
          const isRefund = expense.category === 'refund' || expense.type === 'refund';
          const isPointsWithdrawal = expense.type === 'withdrawal';

          if (isRefund) {
            // 退款支出处理逻辑（简化版）- 现在 amount 字段直接存储管理员填写的协商金额
            let refundCashAmount = Number(expense.amount || 0);
            businessData.totalRefundExpense += refundCashAmount;
          } else if (isPointsWithdrawal) {
            // 积分支出
            businessData.totalPointsExpense += expenseAmount;
          } else {
            // 其他支出
            businessData.totalExpense += expenseAmount;
          }
        });
      } catch (err) {
        console.error('获取支出数据失败:', err);
      }

      // 获取积分提现记录并计算积分支出（无业绩记录分支）
      try {
        // 获取积分设置
        const settingsResult = await db.collection('system_settings').where({ type: 'points' }).get();
        const pointsSettings = settingsResult.data[0] || {};
        const exchangeRatio = (pointsSettings.settings && pointsSettings.settings.exchangeRatio) || 100;

        const withdrawalsCollection = db.collection('withdrawals');
        const withdrawalQuery = {
          status: 'completed' // 只获取已完成的提现
        };

        const withdrawalResult = await withdrawalsCollection.where(withdrawalQuery).get();
        const withdrawalList = withdrawalResult.data || [];

        // 过滤日期范围内的提现记录
        const filteredWithdrawals = withdrawalList.filter(withdrawal => {
          if (!withdrawal.processTime) return false;

          let processDate;
          if (withdrawal.processTime instanceof Date) {
            processDate = withdrawal.processTime;
          } else if (typeof withdrawal.processTime === 'object' && withdrawal.processTime._seconds) {
            processDate = new Date(withdrawal.processTime._seconds * 1000);
          } else if (typeof withdrawal.processTime === 'string') {
            processDate = new Date(withdrawal.processTime);
          } else {
            return false;
          }

          return processDate >= startDateTime && processDate <= endDateTime;
        });

        // 计算积分支出
        filteredWithdrawals.forEach(withdrawal => {
          const points = parseFloat(withdrawal.points || 0);
          const amount = points / exchangeRatio; // 转换为人民币
          businessData.totalPointsExpense += amount;
        });

        console.log('无业绩记录分支 - 总积分支出:', businessData.totalPointsExpense.toFixed(2));

      } catch (err) {
        console.error('获取积分提现数据失败:', err);
      }

      // 获取推广佣金支出（无业绩记录分支）
      try {
        console.log('无业绩记录分支 - 开始计算推广佣金支出...');

        // 查询所有已核销的充值记录，统计推广佣金
        const rechargeQuery = {
          status: 'verified'
        };

        const rechargeRecords = await db.collection('recharge_records')
          .where(rechargeQuery)
          .get();

        console.log('无业绩记录分支 - 查询到的充值记录总数量:', rechargeRecords.data.length);

        // 在内存中过滤日期范围
        const filteredRechargeRecords = rechargeRecords.data.filter(record => {
          // 解析核销时间
          let verifyDate = null;

          if (record.verifyTime) {
            if (record.verifyTime instanceof Date) {
              verifyDate = record.verifyTime;
            } else if (typeof record.verifyTime === 'object' && record.verifyTime._seconds) {
              verifyDate = new Date(record.verifyTime._seconds * 1000);
            } else if (typeof record.verifyTime === 'string') {
              verifyDate = new Date(record.verifyTime);
            }
          }

          // 如果没有核销时间，跳过
          if (!verifyDate || isNaN(verifyDate.getTime())) {
            return false;
          }

          // 检查是否在日期范围内
          return verifyDate >= startDateTime && verifyDate <= endDateTime;
        });

        console.log('无业绩记录分支 - 日期范围内的充值记录数量:', filteredRechargeRecords.length);

        // 统计推广佣金
        filteredRechargeRecords.forEach(record => {
          const promotionCommission = parseFloat(record.promoterCommission || 0);
          if (promotionCommission > 0) {
            businessData.totalPromotionCommission += promotionCommission;
          }
        });

        console.log('无业绩记录分支 - 总推广佣金支出:', businessData.totalPromotionCommission.toFixed(2));

      } catch (err) {
        console.error('无业绩记录分支 - 获取推广佣金数据失败:', err);
      }

      // 计算总收入（现金收入+充值收入）
      businessData.totalIncome = businessData.totalCashIncome + businessData.rechargeIncome;

      // 计算总利润（现金收入 + 充值收入 - 总提成 - 其他支出 - 积分支出 - 退款支出 - 推广佣金支出）
      businessData.totalProfit = businessData.totalCashIncome + businessData.rechargeIncome - businessData.totalCommission - businessData.totalExpense - businessData.totalPointsExpense - businessData.totalRefundExpense - businessData.totalPromotionCommission;

      // 安全格式化数字为两位小数的函数
      const safeToFixed = (value, decimals = 2) => {
        const num = parseFloat(value || 0);
        return isNaN(num) ? 0 : parseFloat(num.toFixed(decimals));
      };

      // 格式化数字为两位小数（无业绩记录分支）
      businessData.totalCashIncome = safeToFixed(businessData.totalCashIncome);
      businessData.totalBalanceIncome = safeToFixed(businessData.totalBalanceIncome);
      businessData.totalIncome = safeToFixed(businessData.totalIncome);
      businessData.totalCashCommission = safeToFixed(businessData.totalCashCommission);
      businessData.totalBalanceCommission = safeToFixed(businessData.totalBalanceCommission);
      businessData.totalCommission = safeToFixed(businessData.totalCommission);
      businessData.totalExpense = safeToFixed(businessData.totalExpense);
      businessData.totalPointsExpense = safeToFixed(businessData.totalPointsExpense);
      businessData.totalRefundExpense = safeToFixed(businessData.totalRefundExpense);
      businessData.totalPromotionCommission = safeToFixed(businessData.totalPromotionCommission);
      businessData.totalProfit = safeToFixed(businessData.totalProfit);
      businessData.rechargeIncome = safeToFixed(businessData.rechargeIncome);

      return {
        code: 0,
        data: businessData,
        message: '获取业绩数据成功'
      };
    }
    
    // 处理业绩记录
    
    // 过滤日期范围内的记录 - 优化日期比较逻辑
    const filteredRecords = performanceRecords.data.filter(record => {
      // 解析记录的日期
      let recordDate = null;
      
      try {
        if (record.completeTime) {
          if (record.completeTime instanceof Date) {
            recordDate = record.completeTime;
          } else if (typeof record.completeTime === 'object' && record.completeTime._seconds) {
            // 处理 Firestore 时间戳
            recordDate = new Date(record.completeTime._seconds * 1000);
          } else if (typeof record.completeTime === 'string') {
            // 处理字符串格式的日期时间
            recordDate = new Date(record.completeTime);
          }
        }
        
        // 如果无法解析日期，尝试使用日期字符串部分
        if (!recordDate || isNaN(recordDate.getTime())) {
          if (typeof record.completeTime === 'string') {
            // 尝试仅使用日期部分
            const datePart = record.completeTime.split(' ')[0];
            if (datePart) {
              // 创建当天开始时间的日期对象
              recordDate = new Date(datePart);
              recordDate.setHours(0, 0, 0, 0);
            }
          }
        }
        
        // 如果仍然无法解析日期，记录日志并跳过
        if (!recordDate || isNaN(recordDate.getTime())) {
          console.log('无法解析日期:', record.completeTime);
          return false; // 跳过无效日期的记录
        }
        
        // 检查记录是否在指定日期范围内 - 使用日期比较
        const recordDateOnly = new Date(recordDate);
        recordDateOnly.setHours(0, 0, 0, 0); // 设置为当天开始时间
        
        // 检查记录日期是否在查询范围内 (包括开始和结束日期)
        return recordDateOnly >= startDateTime && recordDateOnly <= endDateTime;
      } catch (err) {
        console.error('日期处理错误:', err, record);
        return false;
      }
    });
    
    console.log('日期范围内的业绩记录数量:', filteredRecords.length);
    
    filteredRecords.forEach(record => {
      // 累计总订单数
      businessData.totalOrders++;
      
      // 区分现金支付和余额支付
      const isBalancePayment = record.paymentMethod === 'balance';
      const servicePrice = record.servicePrice || 0;
      const commission = record.commission || 0;
      
      // 根据支付方式累计收入和提成
      if (isBalancePayment) {
        // 余额支付 - 不计入总收入，但计入余额收入统计
        businessData.totalBalanceIncome += servicePrice;
        businessData.totalBalanceCommission += commission;
      } else {
        // 现金支付 - 计入总收入
        businessData.totalCashIncome += servicePrice;
        businessData.totalCashCommission += commission;
      }
      
      // 累计总提成（现金+余额）
      businessData.totalCommission += commission;
      
      // 按员工统计
      if (!staffMap[record.staffId]) {
        staffMap[record.staffId] = {
          staffId: record.staffId,
          staffName: record.staffName,
          orderCount: 0,
          cashIncome: 0,
          balanceIncome: 0,
          totalIncome: 0,
          cashCommission: 0,
          balanceCommission: 0,
          totalCommission: 0
        };
      }
      
      staffMap[record.staffId].orderCount++;
      
      if (isBalancePayment) {
        staffMap[record.staffId].balanceIncome += servicePrice;
        staffMap[record.staffId].balanceCommission += commission;
      } else {
        staffMap[record.staffId].cashIncome += servicePrice;
        staffMap[record.staffId].cashCommission += commission;
      }
      
      staffMap[record.staffId].totalIncome += servicePrice;
      staffMap[record.staffId].totalCommission += commission;
      
      // 提取日期部分
      let dateStr;
      if (typeof record.completeTime === 'string') {
        dateStr = record.completeTime.split(' ')[0];
      } else {
        const recordDate = new Date(record.completeTime);
        dateStr = formatDate(recordDate);
      }
      
      // 按日期统计
      if (!dateMap[dateStr]) {
        dateMap[dateStr] = {
          date: dateStr,
          orderCount: 0,
          cashIncome: 0,
          balanceIncome: 0,
          cashCommission: 0,
          balanceCommission: 0,
          totalCommission: 0,
          expense: 0,  // 添加支出字段
          profit: 0,
          rechargeIncome: 0, // 添加充值收入字段
          refundExpense: 0 // 添加退款支出字段
        };
      }
      
      dateMap[dateStr].orderCount++;
      
      if (isBalancePayment) {
        dateMap[dateStr].balanceIncome += servicePrice;
        dateMap[dateStr].balanceCommission += commission;
      } else {
        dateMap[dateStr].cashIncome += servicePrice;
        dateMap[dateStr].cashCommission += commission;
      }
      
      dateMap[dateStr].totalCommission += commission;
      
      // 按服务项目统计
      if (!serviceMap[record.serviceId]) {
        serviceMap[record.serviceId] = {
          serviceId: record.serviceId,
          serviceName: record.serviceName,
          count: 0,
          cashIncome: 0,
          balanceIncome: 0,
          totalIncome: 0
        };
      }
      
      serviceMap[record.serviceId].count++;
      
      if (isBalancePayment) {
        serviceMap[record.serviceId].balanceIncome += servicePrice;
      } else {
        serviceMap[record.serviceId].cashIncome += servicePrice;
      }
      
      serviceMap[record.serviceId].totalIncome += servicePrice;
    });
    
    // 获取充值收入
    await getRechargeIncome();
    
    // 获取支出数据
    let expenseQuery = {};
    if (staffId) {
      expenseQuery.staffId = staffId;
    }

    // 添加日期范围
    expenseQuery.createTime = db.command.gte(startDateTime).and(db.command.lte(endDateTime));

    try {
      // 获取支出记录 - 先获取所有记录，然后在内存中过滤
      const expensesCollection = db.collection('expenses');

      // 获取积分设置，用于积分支出转换
      const settingsResult = await db.collection('system_settings').where({ type: 'points' }).get();
      const pointsSettings = settingsResult.data[0] || {};
      const exchangeRatio = (pointsSettings.settings && pointsSettings.settings.exchangeRatio) || 100;
      console.log('积分兑换比例:', exchangeRatio);

      // 构建基础查询条件（只包含员工筛选）
      let baseQuery = {};
      if (staffId) {
        baseQuery.staffId = staffId;
      }

      const allExpenseRecords = await expensesCollection.where(baseQuery).get();

      console.log('获取到的所有支出记录数量:', allExpenseRecords.data.length);

      // 在内存中过滤日期范围
      const filteredExpenseRecords = allExpenseRecords.data.filter(expense => {
        if (!expense.createTime) {
          return false;
        }

        let expenseDate;

        // 处理不同的时间格式
        if (typeof expense.createTime === 'string') {
          // 字符串格式，直接解析
          expenseDate = new Date(expense.createTime);
        } else if (expense.createTime instanceof Date) {
          // Date对象格式，转换为北京时间
          expenseDate = new Date(expense.createTime.getTime() + 8 * 60 * 60 * 1000);
        } else if (expense.createTime && expense.createTime._seconds) {
          // Firestore时间戳格式，转换为北京时间
          expenseDate = new Date((expense.createTime._seconds * 1000) + 8 * 60 * 60 * 1000);
        } else {
          console.log('无法解析支出时间格式:', expense.createTime);
          return false;
        }

        // 检查日期是否在范围内
        const isInRange = expenseDate >= startDateTime && expenseDate <= endDateTime;

        console.log('支出记录时间比较:', {
          expenseId: expense._id,
          createTime: expense.createTime,
          expenseDate: expenseDate.toISOString(),
          startDateTime: startDateTime.toISOString(),
          endDateTime: endDateTime.toISOString(),
          isInRange: isInRange
        });

        return isInRange;
      });

      console.log('过滤后的支出记录数量:', filteredExpenseRecords.length);

      const expenseRecords = { data: filteredExpenseRecords };

      // 打印所有退款记录的详细信息
      const refundRecords = expenseRecords.data.filter(expense =>
        expense.category === 'refund' || expense.type === 'refund'
      );
      console.log('找到的退款记录数量:', refundRecords.length);
      refundRecords.forEach((record, index) => {
        console.log(`退款记录${index + 1}详情:`, {
          _id: record._id,
          amount: record.amount,
          originalAmount: record.originalAmount,
          inputAmount: record.inputAmount,
          refundBalance: record.refundBalance,
          refundBonus: record.refundBonus,
          clearBalance: record.clearBalance,
          remark: record.remark,
          createTime: record.createTime,
          allFields: Object.keys(record)
        });
      });

      // 处理支出记录
      expenseRecords.data.forEach(expense => {
        // 累计总支出
        const expenseAmount = expense.amount || 0;
        
        // 判断支出类型
        const isRefund = expense.category === 'refund' || expense.type === 'refund';
        const isPointsWithdrawal = expense.type === 'withdrawal';

        if (isRefund) {
          // 退款支出 - 现在 amount 字段直接存储管理员填写的协商金额
          let refundCashAmount = Number(expense.amount || 0);

          // 记录详细信息，便于调试
          console.log('退款支出记录详情:', {
            id: expense._id,
            amount: expense.amount, // 管理员填写的协商退款金额
            actualDeductAmount: expense.actualDeductAmount, // 实际从用户余额扣除的金额
            originalAmount: expense.originalAmount, // 管理员输入的原始金额（备用）
            refundBalance: expense.refundBalance, // 现金部分
            refundBonus: expense.refundBonus, // 赠送金额部分
            actualRefund: refundCashAmount, // 实际计入退款支出的金额
            clearBalance: expense.clearBalance, // 是否清零操作
            remark: expense.remark,
            createTime: expense.createTime
          });

          // 确保金额是数字
          if (isNaN(refundCashAmount)) {
            console.error('退款金额不是有效数字:', refundCashAmount);
            refundCashAmount = 0;
          }

          businessData.totalRefundExpense += refundCashAmount;
        } else if (isPointsWithdrawal) {
          // 积分支出 - amount字段已经是转换后的人民币金额
          console.log('积分支出记录:', {
            id: expense._id,
            amount: expenseAmount, // 人民币金额
            points: expense.points, // 积分数量
            description: expense.description
          });

          businessData.totalPointsExpense += expenseAmount;
        } else {
          // 其他支出
          businessData.totalExpense += expenseAmount;
        }
        
        // 提取日期部分
        let expenseDate;
        if (expense.createTime instanceof Date) {
          expenseDate = expense.createTime;
        } else if (typeof expense.createTime === 'object' && expense.createTime._seconds) {
          expenseDate = new Date(expense.createTime._seconds * 1000);
        } else if (typeof expense.createTime === 'string') {
          expenseDate = new Date(expense.createTime);
        } else {
          console.log('无法解析支出日期:', expense.createTime);
          return; // 跳过无效日期的记录
        }
        
        const dateStr = formatDate(expenseDate);
        console.log('支出日期:', dateStr, '金额:', expenseAmount, '类型:', isRefund ? '退款' : '普通支出');
        
        // 按日期统计支出
        if (!dateMap[dateStr]) {
          dateMap[dateStr] = {
            date: dateStr,
            orderCount: 0,
            cashIncome: 0,
            balanceIncome: 0,
            cashCommission: 0,
            balanceCommission: 0,
            totalCommission: 0,
            expense: 0,
            pointsExpense: 0, // 添加积分支出字段
            refundExpense: 0, // 添加退款支出字段
            profit: 0,
            rechargeIncome: 0
          };
        }
        
        if (isRefund) {
          // 退款支出 - 现在 amount 字段直接存储管理员填写的协商金额
          let refundCashAmount = Number(expense.amount || 0);

          // 确保金额是数字
          if (isNaN(refundCashAmount)) {
            refundCashAmount = 0;
          }

          dateMap[dateStr].refundExpense += refundCashAmount;
        } else if (isPointsWithdrawal) {
          // 积分支出 - amount字段已经是转换后的人民币金额
          dateMap[dateStr].pointsExpense += expenseAmount;
        } else {
          dateMap[dateStr].expense += expenseAmount;
        }
      });
    } catch (err) {
      console.error('获取支出数据失败，但继续处理业绩数据:', err);
    }

    // 获取积分提现记录并计算积分支出
    try {
      const withdrawalsCollection = db.collection('withdrawals');
      const withdrawalQuery = {
        status: 'completed' // 只获取已完成的提现
      };

      const withdrawalResult = await withdrawalsCollection.where(withdrawalQuery).get();
      const withdrawalList = withdrawalResult.data || [];

      console.log('获取到的积分提现记录数量:', withdrawalList.length);

      // 过滤日期范围内的提现记录
      const filteredWithdrawals = withdrawalList.filter(withdrawal => {
        if (!withdrawal.processTime) return false;

        let processDate;
        if (withdrawal.processTime instanceof Date) {
          processDate = withdrawal.processTime;
        } else if (typeof withdrawal.processTime === 'object' && withdrawal.processTime._seconds) {
          processDate = new Date(withdrawal.processTime._seconds * 1000);
        } else if (typeof withdrawal.processTime === 'string') {
          processDate = new Date(withdrawal.processTime);
        } else {
          return false;
        }

        return processDate >= startDateTime && processDate <= endDateTime;
      });

      console.log('日期范围内的积分提现记录数量:', filteredWithdrawals.length);

      // 计算积分支出
      filteredWithdrawals.forEach(withdrawal => {
        const points = parseFloat(withdrawal.points || 0);
        const amount = points / exchangeRatio; // 转换为人民币

        console.log('积分提现记录:', {
          points,
          amount: amount.toFixed(2),
          exchangeRatio
        });

        businessData.totalPointsExpense += amount;

        // 按日期统计积分支出
        let processDate;
        if (withdrawal.processTime instanceof Date) {
          processDate = withdrawal.processTime;
        } else if (typeof withdrawal.processTime === 'object' && withdrawal.processTime._seconds) {
          processDate = new Date(withdrawal.processTime._seconds * 1000);
        } else if (typeof withdrawal.processTime === 'string') {
          processDate = new Date(withdrawal.processTime);
        }

        const dateStr = formatDate(processDate);

        if (!dateMap[dateStr]) {
          dateMap[dateStr] = {
            date: dateStr,
            orderCount: 0,
            cashIncome: 0,
            balanceIncome: 0,
            cashCommission: 0,
            balanceCommission: 0,
            totalCommission: 0,
            expense: 0,
            pointsExpense: 0,
            refundExpense: 0,
            profit: 0,
            rechargeIncome: 0
          };
        }

        dateMap[dateStr].pointsExpense += amount;
      });

      console.log('总积分支出:', businessData.totalPointsExpense.toFixed(2));

    } catch (err) {
      console.error('获取积分提现数据失败:', err);
    }

    // 获取推广佣金支出
    try {
      console.log('开始计算推广佣金支出...');
      console.log('查询日期范围:', {
        startDateTime: startDateTime.toISOString(),
        endDateTime: endDateTime.toISOString(),
        staffId: staffId
      });

      // 查询指定日期范围内的充值记录，统计推广佣金
      // 注意：这里查询所有已核销的充值记录，不限制员工ID，因为推广佣金是支出项
      const rechargeQuery = {
        status: 'verified'
      };

      // 添加日期范围筛选（如果verifyTime字段存在）
      // 先查询所有记录，然后在内存中过滤日期
      console.log('查询所有已核销的充值记录...');

      console.log('充值记录查询条件:', JSON.stringify(rechargeQuery, null, 2));

      const rechargeRecords = await db.collection('recharge_records')
        .where(rechargeQuery)
        .get();

      console.log('查询到的充值记录总数量:', rechargeRecords.data.length);

      // 在内存中过滤日期范围
      const filteredRechargeRecords = rechargeRecords.data.filter(record => {
        // 解析核销时间
        let verifyDate = null;

        if (record.verifyTime) {
          if (record.verifyTime instanceof Date) {
            verifyDate = record.verifyTime;
          } else if (typeof record.verifyTime === 'object' && record.verifyTime._seconds) {
            verifyDate = new Date(record.verifyTime._seconds * 1000);
          } else if (typeof record.verifyTime === 'string') {
            verifyDate = new Date(record.verifyTime);
          }
        }

        // 如果没有核销时间，跳过
        if (!verifyDate || isNaN(verifyDate.getTime())) {
          console.log('跳过无效核销时间的记录:', record._id, record.verifyTime);
          return false;
        }

        // 检查是否在日期范围内
        const isInRange = verifyDate >= startDateTime && verifyDate <= endDateTime;

        return isInRange;
      });

      console.log('日期范围内的充值记录数量:', filteredRechargeRecords.length);

      // 打印前几条记录的详细信息
      if (filteredRechargeRecords.length > 0) {
        console.log('前3条符合条件的充值记录详情:');
        filteredRechargeRecords.slice(0, 3).forEach((record, index) => {
          console.log(`记录${index + 1}:`, {
            _id: record._id,
            status: record.status,
            verifyTime: record.verifyTime,
            operatorId: record.operatorId,
            promoterCommission: record.promoterCommission,
            amount: record.amount
          });
        });
      }

      // 统计推广佣金
      let promotionCommissionCount = 0;
      filteredRechargeRecords.forEach((record, index) => {
        const promotionCommission = parseFloat(record.promoterCommission || 0);
        console.log(`充值记录${index + 1} 推广佣金:`, {
          recordId: record._id,
          promoterCommission: record.promoterCommission,
          parsedAmount: promotionCommission,
          verifyTime: record.verifyTime
        });

        if (promotionCommission > 0) {
          businessData.totalPromotionCommission += promotionCommission;
          promotionCommissionCount++;

          // 按日期统计推广佣金支出
          let verifyDate;
          if (record.verifyTime instanceof Date) {
            verifyDate = record.verifyTime;
          } else if (typeof record.verifyTime === 'object' && record.verifyTime._seconds) {
            verifyDate = new Date(record.verifyTime._seconds * 1000);
          } else if (typeof record.verifyTime === 'string') {
            verifyDate = new Date(record.verifyTime);
          }

          if (verifyDate) {
            const dateStr = formatDate(verifyDate);

            if (!dateMap[dateStr]) {
              dateMap[dateStr] = {
                date: dateStr,
                orderCount: 0,
                cashIncome: 0,
                balanceIncome: 0,
                cashCommission: 0,
                balanceCommission: 0,
                totalCommission: 0,
                expense: 0,
                pointsExpense: 0,
                refundExpense: 0,
                promotionCommission: 0,
                profit: 0,
                rechargeIncome: 0
              };
            }

            dateMap[dateStr].promotionCommission += promotionCommission;
          }
        }
      });

      console.log('推广佣金统计结果:', {
        总充值记录数: rechargeRecords.data.length,
        日期范围内记录数: filteredRechargeRecords.length,
        有佣金记录数: promotionCommissionCount,
        总推广佣金支出: businessData.totalPromotionCommission.toFixed(2)
      });

    } catch (err) {
      console.error('获取推广佣金数据失败:', err);
    }

    // 计算总收入（现金收入+充值收入）
    businessData.totalIncome = businessData.totalCashIncome + businessData.rechargeIncome;
    
    // 计算每天的利润（现金收入 + 充值收入 - 总提成 - 其他支出 - 积分支出 - 退款支出 - 推广佣金支出）
    Object.values(dateMap).forEach(date => {
      date.profit = date.cashIncome + date.rechargeIncome - date.totalCommission - date.expense - date.pointsExpense - date.refundExpense - (date.promotionCommission || 0);
    });

    // 计算总利润（现金收入 + 充值收入 - 总提成 - 其他支出 - 积分支出 - 退款支出 - 推广佣金支出）
    businessData.totalProfit = businessData.totalCashIncome + businessData.rechargeIncome - businessData.totalCommission - businessData.totalExpense - businessData.totalPointsExpense - businessData.totalRefundExpense - businessData.totalPromotionCommission;
    
    // 转换映射为数组
    businessData.staffStats = Object.values(staffMap);
    businessData.dateStats = Object.values(dateMap);
    businessData.serviceStats = Object.values(serviceMap);
    
    // 排序
    businessData.staffStats.sort((a, b) => b.totalIncome - a.totalIncome);
    businessData.dateStats.sort((a, b) => a.date.localeCompare(b.date));
    businessData.serviceStats.sort((a, b) => b.totalIncome - a.totalIncome);
    
    // 安全格式化数字为两位小数的函数
    const safeToFixed = (value, decimals = 2) => {
      const num = parseFloat(value || 0);
      return isNaN(num) ? 0 : parseFloat(num.toFixed(decimals));
    };

    // 格式化数字为两位小数
    businessData.totalCashIncome = safeToFixed(businessData.totalCashIncome);
    businessData.totalBalanceIncome = safeToFixed(businessData.totalBalanceIncome);
    businessData.totalIncome = safeToFixed(businessData.totalIncome);
    businessData.totalCashCommission = safeToFixed(businessData.totalCashCommission);
    businessData.totalBalanceCommission = safeToFixed(businessData.totalBalanceCommission);
    businessData.totalCommission = safeToFixed(businessData.totalCommission);
    businessData.totalExpense = safeToFixed(businessData.totalExpense);
    businessData.totalPointsExpense = safeToFixed(businessData.totalPointsExpense);
    businessData.totalRefundExpense = safeToFixed(businessData.totalRefundExpense);
    businessData.totalPromotionCommission = safeToFixed(businessData.totalPromotionCommission);
    businessData.totalProfit = safeToFixed(businessData.totalProfit);
    businessData.rechargeIncome = safeToFixed(businessData.rechargeIncome);
    
    businessData.staffStats.forEach(staff => {
      staff.cashIncome = safeToFixed(staff.cashIncome);
      staff.balanceIncome = safeToFixed(staff.balanceIncome);
      staff.totalIncome = safeToFixed(staff.totalIncome);
      staff.cashCommission = safeToFixed(staff.cashCommission);
      staff.balanceCommission = safeToFixed(staff.balanceCommission);
      staff.totalCommission = safeToFixed(staff.totalCommission);
    });

    businessData.dateStats.forEach(date => {
      date.cashIncome = safeToFixed(date.cashIncome);
      date.balanceIncome = safeToFixed(date.balanceIncome);
      date.cashCommission = safeToFixed(date.cashCommission);
      date.balanceCommission = safeToFixed(date.balanceCommission);
      date.totalCommission = safeToFixed(date.totalCommission);
      date.expense = safeToFixed(date.expense);
      date.pointsExpense = safeToFixed(date.pointsExpense);
      date.refundExpense = safeToFixed(date.refundExpense);
      date.profit = safeToFixed(date.profit);
      date.rechargeIncome = safeToFixed(date.rechargeIncome);
    });
    
    businessData.serviceStats.forEach(service => {
      service.cashIncome = safeToFixed(service.cashIncome);
      service.balanceIncome = safeToFixed(service.balanceIncome);
      service.totalIncome = safeToFixed(service.totalIncome);
    });
    
    return {
      code: 0,
      data: businessData,
      message: '获取业绩数据成功'
    };
    
    // 内部函数：获取充值收入
    async function getRechargeIncome() {
      try {
        console.log('开始获取充值收入，日期范围:', {
          startDateTime: startDateTime.toISOString(),
          endDateTime: endDateTime.toISOString()
        });
        
        // 获取所有已核销的充值记录，然后在内存中过滤日期范围
        let rechargeRecords = [];

        try {
          // 构建充值记录查询条件
          let rechargeQuery = { status: 'verified' };

          // 如果指定了员工ID，只获取该员工核销的充值记录
          if (staffId) {
            rechargeQuery.operatorId = staffId;
          }

          // 获取充值记录
          const allRechargeResult = await db.collection('recharge_records')
            .where(rechargeQuery)
            .get();

          console.log('获取到的所有充值记录数量:', allRechargeResult.data.length);

          // 打印所有充值记录的详细信息
          allRechargeResult.data.forEach((record, index) => {
            console.log(`充值记录 ${index + 1}:`, {
              id: record._id,
              amount: record.amount,
              status: record.status,
              verifyTime: record.verifyTime,
              verifyTimeType: typeof record.verifyTime
            });
          });

          // 在内存中过滤日期范围
          const filteredRecords = allRechargeResult.data.filter(record => {
            if (!record.verifyTime) {
              return false;
            }

            let recordDate;

            // 处理不同的时间格式
            if (typeof record.verifyTime === 'string') {
              // 新格式：字符串格式 "2025-07-25 16:30"
              recordDate = new Date(record.verifyTime);
            } else if (record.verifyTime instanceof Date) {
              // Date对象格式
              recordDate = record.verifyTime;
            } else if (record.verifyTime && record.verifyTime._seconds) {
              // Firestore时间戳格式
              recordDate = new Date(record.verifyTime._seconds * 1000);
            } else {
              console.log('无法解析充值时间格式:', record.verifyTime);
              return false;
            }

            // 检查日期是否在范围内
            const isInRange = recordDate >= startDateTime && recordDate <= endDateTime;

            // 添加调试信息
            console.log('充值记录时间比较:', {
              recordId: record._id,
              verifyTime: record.verifyTime,
              recordDate: recordDate.toISOString(),
              startDateTime: startDateTime.toISOString(),
              endDateTime: endDateTime.toISOString(),
              isInRange: isInRange
            });

            return isInRange;
          });

          console.log('过滤后的充值记录数量:', filteredRecords.length);
          rechargeRecords = filteredRecords;

        } catch (err) {
          console.error('获取充值记录失败:', err);
        }
        
        console.log('最终获取到的充值记录数量:', rechargeRecords.length);
        
        // 处理充值记录
        rechargeRecords.forEach(recharge => {
          // 累计充值收入（只计算实际充值金额，不包括赠送金额）
          const rechargeAmount = recharge.amount || 0;
          businessData.rechargeIncome += rechargeAmount;
          
          // 提取日期部分
          let rechargeDate;
          
          if (recharge.verifyTime instanceof Date) {
            rechargeDate = recharge.verifyTime;
          } else if (typeof recharge.verifyTime === 'object' && recharge.verifyTime._seconds) {
            // 处理Firestore时间戳格式
            rechargeDate = new Date(recharge.verifyTime._seconds * 1000);
          } else if (typeof recharge.verifyTime === 'string') {
            rechargeDate = new Date(recharge.verifyTime);
          } else {
            console.log('无法解析充值日期格式:', recharge.verifyTime);
            // 使用当前日期作为默认值
            rechargeDate = new Date();
          }
          
          const dateStr = formatDate(rechargeDate);
          console.log('充值记录:', {
            id: recharge._id,
            date: dateStr,
            amount: rechargeAmount,
            verifyTime: recharge.verifyTime,
            parsedDate: rechargeDate
          });
          
          // 按日期统计充值收入
          if (!dateMap[dateStr]) {
            dateMap[dateStr] = {
              date: dateStr,
              orderCount: 0,
              cashIncome: 0,
              balanceIncome: 0,
              cashCommission: 0,
              balanceCommission: 0,
              totalCommission: 0,
              expense: 0,
              profit: 0,
              rechargeIncome: 0
            };
          }
          dateMap[dateStr].rechargeIncome += rechargeAmount;
        });
        
        // 调试输出
        console.log('充值收入总计:', businessData.rechargeIncome);
        Object.keys(dateMap).forEach(date => {
          if (dateMap[date].rechargeIncome > 0) {
            console.log(`${date} 充值收入: ${dateMap[date].rechargeIncome}`);
          }
        });
      } catch (err) {
        console.error('获取充值数据失败，但继续处理业绩数据:', err);
      }
    }
  } catch (err) {
    console.error('获取业绩数据失败', err);
    return {
      code: -1,
      message: '获取业绩数据失败: ' + err.message
    };
  }
}

/**
 * 获取员工充值记录统计
 * 这个函数用于获取员工核销的充值记录统计数据
 */
async function getStaffRechargeStats(data) {
  const { staffId, startDate, endDate } = data;
  
  if (!staffId) {
    return {
      code: 400,
      message: '缺少员工ID参数'
    };
  }
  
  try {
    // 解析日期
    let startDateTime, endDateTime;
    
    if (startDate && endDate) {
      // 确保开始日期是当天的开始 (00:00:00)
      startDateTime = new Date(startDate);
      startDateTime.setHours(0, 0, 0, 0);
      
      // 确保结束日期是当天的结束 (23:59:59.999)
      endDateTime = new Date(endDate);
      endDateTime.setHours(23, 59, 59, 999);
    } else {
      // 默认查询最近30天
      endDateTime = new Date();
      endDateTime.setHours(23, 59, 59, 999);
      
      startDateTime = new Date();
      startDateTime.setDate(startDateTime.getDate() - 30);
      startDateTime.setHours(0, 0, 0, 0);
    }
    
    console.log('查询员工充值记录统计，日期范围:', {
      startDate: formatDate(startDateTime),
      endDate: formatDate(endDateTime),
      staffId: staffId
    });
    
    // 初始化统计数据
    const rechargeStats = {
      totalRechargeCount: 0,
      totalRechargeAmount: 0,
      totalBonusAmount: 0,
      dailyStats: [],
      dateRange: {
        startDate: formatDate(startDateTime),
        endDate: formatDate(endDateTime)
      }
    };
    
    // 查询充值记录 - 尝试多种可能的字段名
    let rechargeRecords = [];
    
    // 获取充值记录集合引用
    const rechargeCollection = db.collection('recharge_records');
    
    // 记录集合是否存在
    try {
      const collectionInfo = await rechargeCollection.count();
      console.log('充值记录集合信息:', collectionInfo);
    } catch (err) {
      console.error('获取充值记录集合信息失败:', err);
    }
    
    // 方法1：使用operatorId字段查询
    try {
      const query1 = {
        status: 'verified',
        operatorId: staffId
      };
      
      console.log('方法1查询条件:', query1);
      
      const result1 = await rechargeCollection.where(query1).get();
      console.log('方法1查询结果数量:', result1.data.length);
      
      if (result1.data.length > 0) {
        console.log('方法1查询示例记录:', result1.data[0]);
        rechargeRecords = rechargeRecords.concat(result1.data);
      }
    } catch (err) {
      console.error('方法1查询失败:', err);
    }
    
    // 方法2：使用staffId字段查询
    if (rechargeRecords.length === 0) {
      try {
        const query2 = {
          status: 'verified',
          staffId: staffId
        };
        
        console.log('方法2查询条件:', query2);
        
        const result2 = await rechargeCollection.where(query2).get();
        console.log('方法2查询结果数量:', result2.data.length);
        
        if (result2.data.length > 0) {
          console.log('方法2查询示例记录:', result2.data[0]);
          rechargeRecords = rechargeRecords.concat(result2.data);
        }
      } catch (err) {
        console.error('方法2查询失败:', err);
      }
    }
    
    // 方法3：使用verifyStaffId字段查询
    if (rechargeRecords.length === 0) {
      try {
        const query3 = {
          status: 'verified',
          verifyStaffId: staffId
        };
        
        console.log('方法3查询条件:', query3);
        
        const result3 = await rechargeCollection.where(query3).get();
        console.log('方法3查询结果数量:', result3.data.length);
        
        if (result3.data.length > 0) {
          console.log('方法3查询示例记录:', result3.data[0]);
          rechargeRecords = rechargeRecords.concat(result3.data);
        }
      } catch (err) {
        console.error('方法3查询失败:', err);
      }
    }
    
    // 方法4：获取所有已核销记录，然后在内存中筛选
    if (rechargeRecords.length === 0) {
      try {
        const query4 = {
          status: 'verified'
        };
        
        console.log('方法4查询条件:', query4);
        
        const result4 = await rechargeCollection.where(query4).limit(100).get();
        console.log('方法4查询结果数量:', result4.data.length);
        
        if (result4.data.length > 0) {
          console.log('方法4查询示例记录:', result4.data[0]);
          console.log('方法4查询记录字段:', Object.keys(result4.data[0]));
          
          // 在内存中筛选与员工相关的记录
          const filteredRecords = result4.data.filter(record => {
            return (
              (record.operatorId && record.operatorId === staffId) ||
              (record.staffId && record.staffId === staffId) ||
              (record.verifyStaffId && record.verifyStaffId === staffId) ||
              (record.operatorName && record.operatorName === staffId) ||
              (record.staffName && record.staffName === staffId)
            );
          });
          
          console.log('方法4筛选后结果数量:', filteredRecords.length);
          rechargeRecords = filteredRecords;
        }
      } catch (err) {
        console.error('方法4查询失败:', err);
      }
    }
    
    console.log('最终获取到的充值记录数量:', rechargeRecords.length);
    
    // 如果没有找到记录，尝试直接获取一些样本记录以检查结构
    if (rechargeRecords.length === 0) {
      try {
        const sampleRecords = await rechargeCollection.limit(5).get();
        console.log('样本充值记录数量:', sampleRecords.data.length);
        
        if (sampleRecords.data.length > 0) {
          console.log('样本充值记录结构:', {
            fields: Object.keys(sampleRecords.data[0]),
            sample: sampleRecords.data[0]
          });
        }
      } catch (err) {
        console.error('获取样本记录失败:', err);
      }
    }
    
    // 按日期统计充值记录
    const dateMap = {};
    
    // 处理充值记录
    rechargeRecords.forEach(record => {
      console.log('处理充值记录:', {
        id: record._id,
        amount: record.amount || record.price,
        bonus: record.bonusAmount || record.bonus,
        verifyTime: record.verifyTime || record.verifiedTime
      });
      
      // 确保金额字段正确
      const rechargeAmount = record.amount || record.price || 0;
      const bonusAmount = record.bonusAmount || record.bonus || 0;
      
      // 累计总数据
      rechargeStats.totalRechargeCount++;
      rechargeStats.totalRechargeAmount += rechargeAmount;
      rechargeStats.totalBonusAmount += bonusAmount;
      
      // 提取日期部分
      let rechargeDate;
      
      if (record.verifyTime instanceof Date) {
        rechargeDate = record.verifyTime;
      } else if (record.verifiedTime instanceof Date) {
        rechargeDate = record.verifiedTime;
      } else if (typeof record.verifyTime === 'object' && record.verifyTime._seconds) {
        // 处理Firestore时间戳格式
        rechargeDate = new Date(record.verifyTime._seconds * 1000);
      } else if (typeof record.verifiedTime === 'object' && record.verifiedTime._seconds) {
        rechargeDate = new Date(record.verifiedTime._seconds * 1000);
      } else if (typeof record.verifyTime === 'string') {
        rechargeDate = new Date(record.verifyTime);
      } else if (typeof record.verifiedTime === 'string') {
        rechargeDate = new Date(record.verifiedTime);
      } else if (record.createTime) {
        // 如果没有核销时间，尝试使用创建时间
        if (record.createTime instanceof Date) {
          rechargeDate = record.createTime;
        } else if (typeof record.createTime === 'object' && record.createTime._seconds) {
          rechargeDate = new Date(record.createTime._seconds * 1000);
        } else if (typeof record.createTime === 'string') {
          rechargeDate = new Date(record.createTime);
        } else {
          console.log('无法解析创建日期格式:', record.createTime);
          rechargeDate = new Date(); // 使用当前日期作为默认值
        }
      } else {
        console.log('无法找到日期字段，记录结构:', Object.keys(record));
        // 使用当前日期作为默认值
        rechargeDate = new Date();
      }
      
      const dateStr = formatDate(rechargeDate);
      console.log('解析的日期:', dateStr);
      
      // 按日期统计
      if (!dateMap[dateStr]) {
        dateMap[dateStr] = {
          date: dateStr,
          rechargeCount: 0,
          rechargeAmount: 0,
          bonusAmount: 0
        };
      }
      
      dateMap[dateStr].rechargeCount++;
      dateMap[dateStr].rechargeAmount += rechargeAmount;
      dateMap[dateStr].bonusAmount += bonusAmount;
    });
    
    // 转换日期统计为数组并排序
    rechargeStats.dailyStats = Object.values(dateMap).sort((a, b) => a.date.localeCompare(b.date));
    
    // 格式化数字为两位小数
    rechargeStats.totalRechargeAmount = parseFloat(rechargeStats.totalRechargeAmount.toFixed(2));
    rechargeStats.totalBonusAmount = parseFloat(rechargeStats.totalBonusAmount.toFixed(2));
    
    rechargeStats.dailyStats.forEach(day => {
      day.rechargeAmount = parseFloat(day.rechargeAmount.toFixed(2));
      day.bonusAmount = parseFloat(day.bonusAmount.toFixed(2));
    });
    
    console.log('最终统计结果:', {
      totalCount: rechargeStats.totalRechargeCount,
      totalAmount: rechargeStats.totalRechargeAmount,
      dailyStatsCount: rechargeStats.dailyStats.length
    });
    
    return {
      code: 0,
      data: rechargeStats,
      message: '获取员工充值记录统计成功'
    };
  } catch (err) {
    console.error('获取员工充值记录统计失败', err);
    return {
      code: -1,
      message: '获取员工充值记录统计失败: ' + err.message
    };
  }
}

// 获取员工服务状态
async function getStaffStatus(data) {
  const { staffId } = data;
  
  if (!staffId) {
    return {
      code: 400,
      message: '缺少员工ID'
    };
  }
  
  try {
    const staff = await staffCollection.doc(staffId).get();
    
    return {
      code: 0,
      data: {
        serviceStatus: staff.data.serviceStatus || 'available'
      },
      message: '获取状态成功'
    };
  } catch (err) {
    console.error('获取员工状态失败', err);
    return {
      code: 500,
      message: '获取员工状态失败: ' + err.message
    };
  }
}

// 更新员工服务状态
async function updateServiceStatus(data) {
  const { staffId, status } = data;
  
  if (!staffId || !status) {
    return {
      code: 400,
      message: '缺少必要参数'
    };
  }
  
  if (!['available', 'busy', 'rest'].includes(status)) {
    return {
      code: 400,
      message: '无效的状态值'
    };
  }
  
  try {
    await staffCollection.doc(staffId).update({
      data: {
        serviceStatus: status,
        updateTime: db.serverDate()
      }
    });
    
    return {
      code: 0,
      message: '状态更新成功'
    };
  } catch (err) {
    console.error('更新服务状态失败', err);
    return {
      code: 500,
      message: '更新服务状态失败: ' + err.message
    };
  }
}

// 获取员工推广佣金统计 - 直接从充值记录中统计
async function getStaffCommissions(data) {
  const { staffId } = data;

  if (!staffId) {
    return {
      code: 400,
      message: '缺少员工ID'
    };
  }

  try {
    // 直接从充值记录中查询该员工核销的订单
    const rechargeQuery = {
      operatorId: staffId,
      status: 'verified' // 只查询已核销的订单
    };
    const rechargeRecords = await db.collection('recharge_records')
      .where(rechargeQuery)
      .get();

    // 统计推广佣金
    let totalAmount = 0;
    const commissionList = [];

    rechargeRecords.data.forEach(record => {
      const commission = parseFloat(record.promoterCommission || 0);
      if (commission > 0) {
        totalAmount += commission;
        commissionList.push({
          _id: record._id,
          rechargeRecordId: record._id,
          amount: commission,
          customerOpenid: record.openid,
          promoterStaffId: staffId,
          status: 'pending',
          createTime: record.verifyTime || record.updateTime,
          planTitle: record.planTitle,
          verifyCode: record.verifyCode
        });
      }
    });

    return {
      code: 0,
      data: {
        list: commissionList,
        total: commissionList.length,
        totalAmount: totalAmount,
        page: 1,
        pageSize: 20
      },
      message: '获取推广佣金记录成功'
    };
  } catch (err) {
    console.error('获取员工推广佣金记录失败:', err);
    return {
      code: -1,
      message: '获取推广佣金记录失败: ' + err.message
    };
  }
}