<!--pages/admin/points/points.wxml-->
<view class="points-admin-container">
  <!-- 顶部标题栏 -->
  <view class="header">
    <view class="back-icon" bindtap="goBack">
      <text class="back-icon">←</text>
    </view>
    <view class="title">积分管理</view>
    <view class="refresh-btn" bindtap="refreshData">
      <image class="refresh-icon" src="/static/刷新图标.png" mode="aspectFit"></image>
    </view>
  </view>

  <!-- 标签栏 -->
  <view class="tabs">
    <view class="tab {{activeTab === 'settings' ? 'active' : ''}}" bindtap="switchTab" data-tab="settings">
      <text>积分设置</text>
    </view>
    <view class="tab {{activeTab === 'withdrawals' ? 'active' : ''}}" bindtap="switchTab" data-tab="withdrawals">
      <text>提现申请</text>
    </view>
    <view class="tab {{activeTab === 'records' ? 'active' : ''}}" bindtap="switchTab" data-tab="records">
      <text>积分记录</text>
    </view>
  </view>

  <!-- 积分设置页面 -->
  <view class="tab-content {{activeTab === 'settings' ? 'active' : ''}}">
    <view class="loading-container" wx:if="{{loadingSettings}}">
      <view class="loading-spinner"></view>
      <text class="loading-text">加载中...</text>
    </view>
    <view class="settings-form" wx:else>
      <view class="form-item">
        <text class="form-label">分享灵感文章积分：</text>
        <input class="form-input" type="number" value="{{pointsSettings.articlePoints}}" bindinput="onInputChange" data-field="articlePoints"></input>
      </view>
      
      <view class="form-item">
        <text class="form-label">分享视频内容积分：</text>
        <input class="form-input" type="number" value="{{pointsSettings.videoPoints}}" bindinput="onInputChange" data-field="videoPoints"></input>
      </view>
      
      <view class="form-item">
        <text class="form-label">分享服务项目积分：</text>
        <input class="form-input" type="number" value="{{pointsSettings.servicePoints}}" bindinput="onInputChange" data-field="servicePoints"></input>
      </view>
      
      <view class="form-item">
        <text class="form-label">积分兑换比例：</text>
        <view class="ratio-container">
          <input class="form-input ratio-input" type="number" value="{{pointsSettings.exchangeRatio}}" bindinput="onInputChange" data-field="exchangeRatio"></input>
          <text class="ratio-text">积分 = 1元</text>
        </view>
      </view>
      
      <view class="action-button" bindtap="saveSettings">
        <text>保存设置</text>
      </view>
    </view>
  </view>

  <!-- 提现申请页面 -->
  <view class="tab-content {{activeTab === 'withdrawals' ? 'active' : ''}}">
    <view class="loading-container" wx:if="{{loadingWithdrawals}}">
      <view class="loading-spinner"></view>
      <text class="loading-text">加载中...</text>
    </view>
    <view class="empty-container" wx:elif="{{withdrawals.length === 0}}">
      <text class="empty-text">暂无提现申请</text>
    </view>
    <view class="withdrawals-list" wx:else>
      <view class="list-header">
        <text class="header-cell">用户</text>
        <text class="header-cell">积分</text>
        <text class="header-cell">时间</text>
        <text class="header-cell">状态</text>
        <text class="header-cell">操作</text>
      </view>
      
      <view class="withdrawal-item" wx:for="{{withdrawals}}" wx:key="_id">
        <text class="item-cell">{{item.userName || '用户'}}</text>
        <text class="item-cell">{{item.points}}</text>
        <text class="item-cell">{{item.dateStr}}</text>
        <text class="item-cell status-{{item.status}}">{{item.statusText}}</text>
        <view class="item-cell actions">
          <view class="action-btn view-btn" bindtap="viewWithdrawal" data-id="{{item._id}}">查看</view>
          <view class="action-btn approve-btn" wx:if="{{item.status === 'pending'}}" bindtap="approveWithdrawal" data-id="{{item._id}}">通过</view>
          <view class="action-btn reject-btn" wx:if="{{item.status === 'pending'}}" bindtap="rejectWithdrawal" data-id="{{item._id}}">拒绝</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 积分记录页面 -->
  <view class="tab-content {{activeTab === 'records' ? 'active' : ''}}">
    <view class="loading-container" wx:if="{{loadingRecords}}">
      <view class="loading-spinner"></view>
      <text class="loading-text">加载中...</text>
    </view>
    <view class="empty-container" wx:elif="{{pointsRecords.length === 0}}">
      <text class="empty-text">暂无积分记录</text>
    </view>
    <view wx:else>
      <!-- 积分统计容器 -->
      <view class="stats-container">
        <view class="stats-title">积分提现统计</view>
        <view class="stats-content">
          <view class="stats-item">
            <text class="stats-label">总记录数</text>
            <text class="stats-value">{{pointsStats.totalRecords}}</text>
          </view>
          <view class="stats-item">
            <text class="stats-label">总积分数</text>
            <text class="stats-value">{{pointsStats.totalPoints}}</text>
          </view>
          <view class="stats-item">
            <text class="stats-label">总金额</text>
            <text class="stats-value">¥{{pointsStats.totalAmount}}</text>
          </view>
          <view class="stats-item">
            <text class="stats-label">参与用户数</text>
            <text class="stats-value">{{pointsStats.uniqueUsers}}</text>
          </view>
        </view>
      </view>
      
      <!-- 积分记录列表 -->
      <view class="records-list">
        <view class="list-header">
          <text class="header-cell">用户</text>
          <text class="header-cell">积分</text>
          <text class="header-cell">金额</text>
          <text class="header-cell">类型</text>
          <text class="header-cell">时间</text>
        </view>
        
        <view class="record-item" wx:for="{{pointsRecords}}" wx:key="_id">
          <text class="item-cell">{{item.userName || '用户'}}</text>
          <text class="item-cell">{{item.points}}</text>
          <text class="item-cell">¥{{item.amount}}</text>
          <text class="item-cell">{{item.typeText}}</text>
          <text class="item-cell">{{item.dateStr}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 提现详情弹窗 -->
  <view class="modal" wx:if="{{showWithdrawalDetail}}">
    <view class="modal-mask" bindtap="closeWithdrawalDetail"></view>
    <view class="modal-content">
      <view class="modal-header">
        <text class="modal-title">提现详情</text>
        <view class="modal-close" bindtap="closeWithdrawalDetail">×</view>
      </view>
      
      <view class="modal-body">
        <view class="detail-item">
          <text class="detail-label">用户：</text>
          <text class="detail-value">{{currentWithdrawal.userName || '用户'}}</text>
        </view>
        
        <view class="detail-item">
          <text class="detail-label">提现积分：</text>
          <text class="detail-value">{{currentWithdrawal.points}}</text>
        </view>
        
        <view class="detail-item">
          <text class="detail-label">提现金额：</text>
          <text class="detail-value">¥{{currentWithdrawal.amount}}</text>
        </view>
        
        <view class="detail-item">
          <text class="detail-label">申请时间：</text>
          <text class="detail-value">{{currentWithdrawal.dateTimeStr}}</text>
        </view>
        
        <view class="detail-item">
          <text class="detail-label">状态：</text>
          <text class="detail-value status-{{currentWithdrawal.status}}">{{currentWithdrawal.statusText}}</text>
        </view>
        
        <view class="detail-item" wx:if="{{currentWithdrawal.description}}">
          <text class="detail-label">备注：</text>
          <text class="detail-value">{{currentWithdrawal.description}}</text>
        </view>
        
        <view class="detail-item">
          <text class="detail-label">收款码：</text>
          <view class="qrcode-container">
            <image class="qrcode-image" src="{{currentWithdrawal.paymentQrCode}}" mode="aspectFit" bindtap="previewQrCode" bindlongpress="scanQrCode"></image>
            <text class="qrcode-tip">点击预览，长按扫码转账</text>
          </view>
        </view>
        
        <!-- 添加管理员备注输入框 -->
        <view class="detail-item" wx:if="{{currentWithdrawal.status === 'pending'}}">
          <text class="detail-label">处理备注：</text>
          <textarea class="admin-remark" placeholder="请输入处理备注，用户将看到此信息" bindinput="onRemarkInput" value="{{adminRemark}}"></textarea>
        </view>
      </view>
      
      <view class="modal-footer" wx:if="{{currentWithdrawal.status === 'pending'}}">
        <view class="modal-btn reject-btn" bindtap="rejectCurrentWithdrawal">
          <view class="btn-text">拒绝提现</view>
        </view>
        <view class="modal-btn approve-btn" bindtap="approveCurrentWithdrawal">
          <view class="btn-text">通过提现</view>
        </view>
      </view>
    </view>
  </view>
</view> 