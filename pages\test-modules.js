/**
 * 模块测试页面
 * 在微信开发者工具中测试基础架构组件
 */

// 引入测试模块
const { runModuleTests, getModuleStatus } = require('./index/test-modules.js');

Page({
  data: {
    testResults: [],
    testStatus: '准备测试',
    showResults: false
  },

  onLoad: function() {
    console.log('模块测试页面加载');
    this.setData({
      testStatus: '页面已加载，点击开始测试'
    });
  },

  // 开始测试
  startTest: function() {
    console.log('开始基础架构测试...');
    
    this.setData({
      testStatus: '正在测试基础架构组件...',
      testResults: [],
      showResults: false
    });

    try {
      // 运行测试
      this.runBasicTests();
      
    } catch (error) {
      console.error('测试执行失败:', error);
      this.setData({
        testStatus: '测试执行失败: ' + error.message,
        showResults: true
      });
    }
  },

  // 运行基础测试
  runBasicTests: function() {
    const results = [];
    
    try {
      // 1. 测试基础模块类
      console.log('测试基础模块类...');
      const BaseModule = require('./index/modules/base-module.js');
      
      // 创建测试模块
      class SimpleTestModule extends BaseModule {
        constructor(pageContext) {
          super(pageContext);
          this.moduleName = 'SimpleTestModule';
        }
        
        async init() {
          this.initialized = true;
          return true;
        }
        
        testMethod() {
          return 'success';
        }
      }
      
      const testModule = new SimpleTestModule(this);
      testModule.init();
      const testResult = testModule.testMethod();
      
      results.push({
        name: '基础模块类',
        status: testResult === 'success' ? 'pass' : 'fail',
        message: testResult === 'success' ? '模块创建和方法调用正常' : '模块测试失败'
      });
      
      // 2. 测试模块通信器
      console.log('测试模块通信器...');
      const ModuleCommunicator = require('./index/modules/module-communicator.js');
      const communicator = new ModuleCommunicator();
      
      // 注册模块
      communicator.registerModule('test', testModule);
      const hasModule = communicator.hasModule('test');
      
      // 测试事件
      let eventWorked = false;
      communicator.on('test-event', () => {
        eventWorked = true;
      });
      communicator.emit('test-event');
      
      results.push({
        name: '模块通信器',
        status: (hasModule && eventWorked) ? 'pass' : 'fail',
        message: hasModule ? '模块注册和事件通信正常' : '通信器测试失败'
      });
      
      // 3. 测试错误处理器
      console.log('测试错误处理器...');
      const { globalErrorHandler } = require('./index/utils/error-handler.js');
      
      // 触发一个测试错误
      globalErrorHandler.handleModuleError('TestModule', new Error('测试错误'), 'test');
      const errorStats = globalErrorHandler.getErrorStats();
      
      results.push({
        name: '错误处理器',
        status: errorStats.total > 0 ? 'pass' : 'fail',
        message: `错误处理正常，已记录 ${errorStats.total} 个错误`
      });
      
      // 4. 测试回滚管理器
      console.log('测试回滚管理器...');
      const { globalRollbackManager } = require('./index/utils/rollback-manager.js');
      
      // 创建测试回滚点
      globalRollbackManager.createRollbackPoint('测试回滚点', [])
        .then(rollbackId => {
          const rollbackPoint = globalRollbackManager.getRollbackPoint(rollbackId);
          
          results.push({
            name: '回滚管理器',
            status: rollbackPoint ? 'pass' : 'fail',
            message: rollbackPoint ? '回滚点创建和管理正常' : '回滚管理器测试失败'
          });
          
          // 继续测试工具函数模块
          this.testUtilityModules(results);
        })
        .catch(error => {
          results.push({
            name: '回滚管理器',
            status: 'fail',
            message: '回滚管理器测试失败: ' + error.message
          });
          
          this.testUtilityModules(results);
        });
      
    } catch (error) {
      console.error('基础测试失败:', error);
      results.push({
        name: '基础测试',
        status: 'fail',
        message: '测试执行异常: ' + error.message
      });
      
      this.updateTestResults(results);
    }
  },

  // 测试工具函数模块
  testUtilityModules: function(results) {
    console.log('开始测试工具函数模块...');
    
    try {
      // 5. 测试视频工具模块
      console.log('测试视频工具模块...');
      const videoUtils = require('./index/utils/video-utils.js');
      
      // 测试格式化播放量
      const formattedCount = videoUtils.formatCount(12345);
      const formatSuccess = formattedCount === '12.3k';
      
      // 测试视频数据清理
      const testVideoData = {
        id: 'test123',
        mainTitle: '测试视频',
        playCount: 1000
      };
      const sanitizedData = videoUtils.sanitizeVideoData(testVideoData);
      const sanitizeSuccess = sanitizedData && sanitizedData.id === 'test123';
      
      // 测试视频列表去重
      const testVideoList = [
        { id: '1', title: '视频1' },
        { id: '2', title: '视频2' },
        { id: '1', title: '重复视频' }
      ];
      const deduplicatedList = videoUtils.deduplicateVideoList(testVideoList);
      const deduplicateSuccess = deduplicatedList.length === 2;
      
      results.push({
        name: '视频工具模块',
        status: (formatSuccess && sanitizeSuccess && deduplicateSuccess) ? 'pass' : 'fail',
        message: `格式化、清理、去重功能${(formatSuccess && sanitizeSuccess && deduplicateSuccess) ? '正常' : '异常'}`
      });
      
      // 6. 测试UI工具模块
      console.log('测试UI工具模块...');
      const uiUtils = require('./index/utils/ui-utils.js');
      
      // 测试节流函数
      let throttleCallCount = 0;
      const throttledFn = uiUtils.throttle(() => {
        throttleCallCount++;
      }, 100);
      
      // 快速调用多次
      throttledFn();
      throttledFn();
      throttledFn();
      
      // 测试滚动管理器
      const scrollManager = uiUtils.createScrollManager();
      const scrollInfo = scrollManager.updateScrollPosition(100);
      const scrollSuccess = scrollInfo && scrollInfo.direction;
      
      // 测试触摸处理器
      const touchHandler = uiUtils.createTouchHandler();
      const touchSuccess = touchHandler && typeof touchHandler.handleTouchStart === 'function';
      
      results.push({
        name: 'UI工具模块',
        status: (scrollSuccess && touchSuccess) ? 'pass' : 'fail',
        message: `节流、滚动、触摸处理功能${(scrollSuccess && touchSuccess) ? '正常' : '异常'}`
      });
      
      // 7. 测试常量模块
      console.log('测试常量模块...');
      const constants = require('./index/constants/index-constants.js');
      
      // 测试各种常量
      const paginationSuccess = constants.PAGINATION && constants.PAGINATION.PAGE_SIZE === 4;
      const errorTypesSuccess = constants.ERROR_TYPES && constants.ERROR_TYPES.NETWORK_ERROR;
      const defaultDataSuccess = constants.DEFAULT_PAGE_DATA && constants.DEFAULT_PAGE_DATA.loading === false;
      
      results.push({
        name: '常量模块',
        status: (paginationSuccess && errorTypesSuccess && defaultDataSuccess) ? 'pass' : 'fail',
        message: `分页、错误类型、默认数据常量${(paginationSuccess && errorTypesSuccess && defaultDataSuccess) ? '正常' : '异常'}`
      });
      
      // 更新最终测试结果
      this.updateTestResults(results);
      
    } catch (error) {
      console.error('工具函数测试失败:', error);
      results.push({
        name: '工具函数测试',
        status: 'fail',
        message: '工具函数测试异常: ' + error.message
      });
      
      this.updateTestResults(results);
    }
  },

  // 更新测试结果
  updateTestResults: function(results) {
    const passedCount = results.filter(r => r.status === 'pass').length;
    const totalCount = results.length;
    
    this.setData({
      testResults: results,
      testStatus: `测试完成: ${passedCount}/${totalCount} 项通过`,
      showResults: true
    });
    
    // 显示结果提示
    if (passedCount === totalCount) {
      wx.showToast({
        title: '所有测试通过！',
        icon: 'success',
        duration: 2000
      });
    } else {
      wx.showToast({
        title: `${passedCount}/${totalCount} 项测试通过`,
        icon: 'none',
        duration: 2000
      });
    }
  },

  // 查看详细状态
  viewStatus: function() {
    try {
      const { globalErrorHandler } = require('./index/utils/error-handler.js');
      const { globalRollbackManager } = require('./index/utils/rollback-manager.js');
      
      const errorStats = globalErrorHandler.getErrorStats();
      const rollbackStatus = globalRollbackManager.getStatus();
      
      const statusInfo = `
错误处理器状态:
- 总错误数: ${errorStats.total}
- 日志条数: ${errorStats.logCount}

回滚管理器状态:
- 回滚点数量: ${rollbackStatus.rollbackPointCount}
- 当前回滚点: ${rollbackStatus.currentRollbackPoint || '无'}
      `.trim();
      
      wx.showModal({
        title: '模块状态详情',
        content: statusInfo,
        showCancel: false
      });
      
    } catch (error) {
      wx.showToast({
        title: '获取状态失败',
        icon: 'none'
      });
    }
  },

  // 清理测试数据
  clearTest: function() {
    this.setData({
      testResults: [],
      testStatus: '测试数据已清理',
      showResults: false
    });
    
    wx.showToast({
      title: '已清理测试数据',
      icon: 'success'
    });
  }
});