.content-admin-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
}

.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  background-color: #ffffff;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  position: sticky;
  top: 0;
  z-index: 100;
}

.back-icon {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  color: #333333;
}

.header-title {
  font-size: 34rpx;
  font-weight: bold;
  color: #333333;
}

.add-btn {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: linear-gradient(to right, #ff9a9e, #fad0c4);
  color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  font-weight: bold;
}

.tab-container {
  display: flex;
  background-color: #ffffff;
  padding: 0 20rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.tab-item {
  flex: 1;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #666666;
  position: relative;
}

.tab-item.active {
  color: #ff9a9e;
  font-weight: 500;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 6rpx;
  background: linear-gradient(to right, #ff9a9e, #fad0c4);
  border-radius: 3rpx;
}

.content-list {
  flex: 1;
  padding: 20rpx;
}

.loading-container {
  padding: 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.loading-spinner {
  width: 80rpx;
  height: 80rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #ff9a9e;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 20rpx;
  color: #999999;
  font-size: 28rpx;
}

.empty-container {
  padding: 100rpx 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  color: #999999;
  font-size: 28rpx;
}

.content-item {
  margin-bottom: 30rpx;
  border-radius: 16rpx;
  background-color: #ffffff;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.content-cover {
  height: 300rpx;
  overflow: hidden;
}

.content-cover image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.banner-cover {
  height: 200rpx;
}

.content-info {
  padding: 20rpx 30rpx;
}

.content-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 10rpx;
}

.notice-content {
  font-size: 28rpx;
  color: #666666;
  margin: 10rpx 0;
  line-height: 1.5;
}

.content-meta {
  font-size: 24rpx;
  color: #999999;
}

.content-actions {
  display: flex;
  border-top: 2rpx solid #f0f0f0;
}

.action-btn {
  flex: 1;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
}

.action-btn.edit {
  color: #666666;
  border-right: 2rpx solid #f0f0f0;
}

.action-btn.delete {
  color: #ff5252;
}

.notice-item {
  padding-top: 20rpx;
}

.notice-info {
  padding-top: 0;
} 