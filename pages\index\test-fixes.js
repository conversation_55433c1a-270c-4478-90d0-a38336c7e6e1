/**
 * 测试修复效果的脚本
 * 在开发者工具控制台中运行
 */

// 获取当前页面实例
const currentPages = getCurrentPages();
const currentPage = currentPages[currentPages.length - 1];

console.log('=== 开始测试修复效果 ===');

// 1. 测试 catchHorizontalMove 方法
console.log('1. 测试 catchHorizontalMove 方法');
try {
  if (typeof currentPage.catchHorizontalMove === 'function') {
    console.log('✅ catchHorizontalMove 方法存在');
    
    // 模拟调用
    const mockEvent = { touches: [{ clientX: 100, clientY: 100 }] };
    currentPage.catchHorizontalMove(mockEvent);
    console.log('✅ catchHorizontalMove 方法调用成功');
  } else {
    console.log('❌ catchHorizontalMove 方法不存在');
  }
} catch (error) {
  console.log('❌ catchHorizontalMove 测试失败:', error.message);
}

// 2. 测试页面数据状态
console.log('2. 测试页面数据状态');
const data = currentPage.data;
console.log('页面数据状态:', {
  videoListLength: data.videoList ? data.videoList.length : 0,
  loading: data.loading,
  firstLoading: data.firstLoading,
  showContent: data.showContent,
  isRefreshing: data.isRefreshing
});

if (data.showContent) {
  console.log('✅ showContent 状态正确');
} else {
  console.log('❌ showContent 状态异常');
}

// 3. 测试模块状态
console.log('3. 测试模块状态');
if (currentPage.mainController) {
  console.log('✅ 主控制器存在');
  
  try {
    const moduleStatus = currentPage.mainController.getModulesStatus();
    console.log('模块状态:', moduleStatus);
    
    if (moduleStatus.initializedModules > 0) {
      console.log('✅ 模块初始化正常');
    } else {
      console.log('❌ 模块初始化异常');
    }
  } catch (error) {
    console.log('❌ 获取模块状态失败:', error.message);
  }
} else {
  console.log('❌ 主控制器不存在');
}

// 4. 测试API兼容性
console.log('4. 测试API兼容性');
if (currentPage.apiCompatibility) {
  console.log('✅ API兼容层存在');
  
  try {
    const compatReport = currentPage.apiCompatibility.getCompatibilityReport();
    console.log('兼容性报告:', compatReport);
    
    if (compatReport.mappedMethods > 0) {
      console.log('✅ API映射正常');
    } else {
      console.log('❌ API映射异常');
    }
  } catch (error) {
    console.log('❌ 获取兼容性报告失败:', error.message);
  }
} else {
  console.log('❌ API兼容层不存在');
}

// 5. 测试视频数据
console.log('5. 测试视频数据');
if (data.videoList && data.videoList.length > 0) {
  console.log('✅ 视频数据存在，数量:', data.videoList.length);
  console.log('视频数据示例:', data.videoList[0]);
} else {
  console.log('❌ 视频数据为空或不存在');
  
  // 尝试强制刷新数据
  console.log('尝试强制刷新数据...');
  if (currentPage.apiCompatibility) {
    try {
      currentPage.apiCompatibility.handleMethod('loadVideoList', [true]);
      console.log('✅ 强制刷新请求已发送');
    } catch (error) {
      console.log('❌ 强制刷新失败:', error.message);
    }
  }
}

// 6. 测试刷新状态
console.log('6. 测试刷新状态');
if (data.isRefreshing) {
  console.log('⚠️ 页面正在刷新中');
  
  // 检查是否卡在刷新状态
  setTimeout(() => {
    const newData = getCurrentPages()[getCurrentPages().length - 1].data;
    if (newData.isRefreshing) {
      console.log('❌ 检测到可能的循环刷新问题');
      
      // 尝试修复
      currentPage.setData({ isRefreshing: false });
      wx.stopPullDownRefresh();
      console.log('✅ 已尝试修复循环刷新');
    }
  }, 3000);
} else {
  console.log('✅ 刷新状态正常');
}

console.log('=== 测试完成 ===');

// 返回测试结果摘要
const testResult = {
  catchHorizontalMove: typeof currentPage.catchHorizontalMove === 'function',
  showContent: data.showContent,
  hasVideoData: data.videoList && data.videoList.length > 0,
  mainController: !!currentPage.mainController,
  apiCompatibility: !!currentPage.apiCompatibility,
  isRefreshing: data.isRefreshing
};

console.log('测试结果摘要:', testResult);
return testResult;