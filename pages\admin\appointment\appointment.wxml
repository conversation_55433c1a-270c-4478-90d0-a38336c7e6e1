<!--pages/admin/appointment/appointment.wxml-->
<view class="appointment-admin-container">
  <!-- 顶部标题栏 -->
  <view class="header">
    <view class="back-icon" bindtap="goBack">
      <text>←</text>
    </view>
    <view class="title">预约管理</view>
    <view class="refresh-btn" bindtap="refreshList">
      <image class="refresh-icon" src="/static/icons/refresh.png" mode="aspectFit"></image>
    </view>
  </view>
  
  <!-- 筛选工具栏 -->
  <view class="filter-bar">
    <view class="filter-section">
      <view class="filter-label">状态筛选：</view>
      <picker mode="selector" range="{{statusOptions}}" range-key="label" bindchange="onStatusChange">
        <view class="filter-picker">
          <text>{{currentStatusLabel}}</text>
          <text class="picker-arrow">▼</text>
        </view>
      </picker>
    </view>
    
    <view class="filter-section">
      <view class="filter-label">日期筛选：</view>
      <view class="date-filter-options">
        <picker mode="date" bindchange="onDateChange" value="{{selectedDate}}">
          <view class="filter-picker">
            <text>{{selectedDate || '选择日期'}}</text>
            <text class="picker-arrow">▼</text>
          </view>
        </picker>
        <view class="quick-date-filters">
          <view class="quick-date-btn {{dateFilterType === 'today' ? 'active' : ''}}" 
                catchtap="selectToday">今日</view>
          <view class="quick-date-btn {{dateFilterType === 'week' ? 'active' : ''}}" 
                catchtap="selectThisWeek">本周</view>
          <view class="quick-date-btn {{dateFilterType === 'month' ? 'active' : ''}}" 
                catchtap="selectThisMonth">本月</view>
        </view>
      </view>
      <view class="clear-filter" wx:if="{{selectedDate || dateFilterType}}" bindtap="clearDateFilter">
        <text>×</text>
      </view>
    </view>
    
    <!-- 员工选择部分 -->
    <view class="filter-section">
      <view class="filter-label">核销员工：</view>
      <picker mode="selector" range="{{staffList}}" range-key="name" bindchange="onStaffChange">
        <view class="filter-picker">
          <text>{{selectedStaffName || '选择核销员工'}}</text>
          <text class="picker-arrow">▼</text>
        </view>
      </picker>
      <view class="clear-filter" wx:if="{{selectedStaff}}" bindtap="clearStaffFilter">
        <text>×</text>
      </view>
    </view>
  </view>
  
  <!-- 移除统计按钮区域，界面保持简洁 -->
  
  <!-- 预约列表 -->
  <view class="appointment-list">
    <!-- 加载中 -->
    <view class="loading-container" wx:if="{{loading}}">
      <view class="loading-spinner"></view>
      <text class="loading-text">加载中...</text>
    </view>
    
    <!-- 空状态 -->
    <view class="empty-container" wx:elif="{{appointmentList.length === 0}}">
      <text class="empty-text">暂无预约记录</text>
    </view>
    
    <!-- 列表内容 -->
    <block wx:else>
      <view class="appointment-item" wx:for="{{appointmentList}}" wx:key="_id">
        <!-- 移除原来的header和状态显示 -->
        
        <view class="appointment-content">
          <view class="service-info">
            <text class="service-name">{{item.serviceName}}</text>
            <text class="service-price">¥{{item.servicePrice}}</text>
          </view>
          
          <view class="user-info">
            <image class="user-avatar" src="{{item.userInfo.avatarUrl || '/static/default-avatar.png'}}" mode="aspectFill"></image>
            <view class="user-detail">
              <text class="user-nickname">{{item.userInfo.nickName || '匿名用户'}}</text>
              <view class="phone-container" wx:if="{{item.phoneNumber}}">
                <text class="user-phone">联系电话: {{item.phoneNumber}}</text>
                <view class="call-btn" bindtap="callPhone" data-phone="{{item.phoneNumber}}">拨打</view>
              </view>
              <text class="user-phone" wx:else>未提供联系电话</text>
            </view>
          </view>
          
          <!-- 预约详情信息 -->
          <view class="appointment-detail-info">
            <!-- 时间信息：预约时间和下单时间上下排列 -->
            <view class="time-info-column">
              <view class="time-info-item">
                <text class="detail-label">预约时间:</text>
                <text class="detail-value">{{item.date}} {{item.time}}</text>
              </view>
              <view class="time-info-item" wx:if="{{item.createTime}}">
                <text class="detail-label">下单时间:</text>
                <text class="detail-value">{{item.createTimeFormatted || item.createTime}}</text>
              </view>
            </view>
            
            <!-- 指定员工 -->
            <view class="detail-row" wx:if="{{item.preferredStaffName}}">
              <text class="detail-label">指定员工:</text>
              <text class="detail-value">{{item.preferredStaffName}}</text>
            </view>
            
            <!-- 核销信息 -->
            <block wx:if="{{item.verified}}">
              <view class="detail-row">
                <text class="detail-label">核销码:</text>
                <text class="detail-value">{{item.verifyCode}}</text>
              </view>
              <view class="detail-row" wx:if="{{item.verifyTime}}">
                <text class="detail-label">核销时间:</text>
                <text class="detail-value">{{item.verifyTime}}</text>
              </view>
              <view class="detail-row" wx:if="{{item.verifyStaffName}}">
                <text class="detail-label">核销员工:</text>
                <text class="detail-value">{{item.verifyStaffName}}</text>
              </view>
            </block>
          </view>
          
          <!-- 拒绝理由（如果有） -->
          <view class="reject-reason" wx:if="{{item.status === 'rejected' && item.rejectReason}}">
            <text class="reason-label">拒绝原因：</text>
            <text class="reason-content">{{item.rejectReason}}</text>
          </view>
          
          <!-- 参考图片（如果有） -->
          <view class="reference-images" wx:if="{{item.imageUrls && item.imageUrls.length > 0}}">
            <text class="images-title">参考图片：</text>
            <view class="image-gallery">
              <image 
                wx:for="{{item.imageUrls}}" 
                wx:for-item="imgUrl" 
                wx:key="index" 
                src="{{imgUrl}}" 
                mode="aspectFill" 
                class="reference-image"
                bindtap="previewImage"
                data-urls="{{item.imageUrls}}"
                data-current="{{imgUrl}}"
              ></image>
            </view>
          </view>
        </view>
        
        <!-- 操作按钮 -->
        <view class="appointment-actions">
          <!-- 状态标签，移到操作区域左侧 -->
          <view class="appointment-status {{item.status}}">
            <text class="status-text">
              {{item.status === 'pending' ? '待确认' : 
                item.status === 'confirmed' ? '已确认' : 
                item.status === 'completed' ? '已完成' : 
                item.status === 'cancelled' ? '已取消' : 
                item.status === 'rejected' ? '已拒绝' : '未知状态'}}
            </text>
          </view>
          
          <view class="action-buttons">
          <!-- 待确认状态的操作 -->
          <block wx:if="{{item.status === 'pending'}}">
            <view class="action-btn confirm" bindtap="confirmAppointment" data-id="{{item._id}}">确认</view>
            <view class="action-btn reject" bindtap="rejectAppointment" data-id="{{item._id}}">拒绝</view>
          </block>
          
          <!-- 已确认状态的操作 -->
          <block wx:if="{{item.status === 'confirmed'}}">
            <view class="action-btn complete" bindtap="completeAppointment" data-id="{{item._id}}">完成</view>
          </block>
          </view>
        </view>
      </view>
      
      <!-- 加载更多 -->
      <view class="load-more" wx:if="{{hasMoreData && !loading}}">
        <text>上拉加载更多</text>
      </view>
      <view class="load-more" wx:if="{{!hasMoreData && appointmentList.length > 0}}">
        <text>已显示全部数据</text>
      </view>
    </block>
  </view>
  
  <!-- 预约详情弹窗 -->
  <view class="detail-panel {{showAppointmentDetail ? 'show' : ''}}" wx:if="{{showAppointmentDetail}}">
    <view class="detail-mask" bindtap="closeAppointmentDetail"></view>
    <view class="detail-content">
      <view class="detail-header">
        <text class="detail-title">预约详情</text>
        <view class="detail-close" bindtap="closeAppointmentDetail">×</view>
      </view>
      
      <scroll-view scroll-y="true" class="detail-body">
        <!-- 加载中 -->
        <view class="detail-loading" wx:if="{{loadingDetail}}">
          <view class="loading-spinner"></view>
          <text>加载详情中...</text>
        </view>
        
        <!-- 详情内容 -->
        <block wx:elif="{{appointmentDetail}}">
          <!-- 预约状态 -->
          <view class="detail-section">
            <view class="detail-status {{appointmentDetail.status}}">
              <text class="status-text">
                {{appointmentDetail.status === 'pending' ? '待确认' : 
                  appointmentDetail.status === 'confirmed' ? '已确认' : 
                  appointmentDetail.status === 'completed' ? '已完成' : 
                  appointmentDetail.status === 'cancelled' ? '已取消' : 
                  appointmentDetail.status === 'rejected' ? '已拒绝' : '未知状态'}}
              </text>
            </view>
          </view>
          
          <!-- 服务信息 -->
          <view class="detail-section">
            <view class="section-title">服务信息</view>
            <view class="detail-item">
              <text class="detail-label">服务项目</text>
              <text class="detail-value">{{appointmentDetail.serviceName}}</text>
            </view>
            <view class="detail-item">
              <text class="detail-label">服务价格</text>
              <text class="detail-value price">¥{{appointmentDetail.servicePrice}}</text>
            </view>
            <view class="detail-item" wx:if="{{appointmentDetail.originalPrice}}">
              <text class="detail-label">原价</text>
              <text class="detail-value">¥{{appointmentDetail.originalPrice}}</text>
            </view>
          </view>
          
          <!-- 预约信息 -->
          <view class="detail-section">
            <view class="section-title">预约信息</view>
            <view class="detail-item">
              <text class="detail-label">预约日期</text>
              <text class="detail-value">{{appointmentDetail.date}}</text>
            </view>
            <view class="detail-item">
              <text class="detail-label">预约时间</text>
              <text class="detail-value">{{appointmentDetail.time}}</text>
            </view>
            <view class="detail-item">
              <text class="detail-label">联系电话</text>
              <text class="detail-value">{{appointmentDetail.phoneNumber || '未提供'}}</text>
            </view>
            <view class="detail-item">
              <text class="detail-label">创建时间</text>
              <text class="detail-value">{{appointmentDetail.createTimeFormatted || '未知'}}</text>
            </view>
            <view class="detail-item" wx:if="{{appointmentDetail.preferredStaffName}}">
              <text class="detail-label">指定员工</text>
              <text class="detail-value">{{appointmentDetail.preferredStaffName}}</text>
            </view>
          </view>
          
          <!-- 核销信息 -->
          <view class="detail-section" wx:if="{{appointmentDetail.verified}}">
            <view class="section-title">核销信息</view>
            <view class="detail-item">
              <text class="detail-label">核销码</text>
              <text class="detail-value">{{appointmentDetail.verifyCode}}</text>
            </view>
            <view class="detail-item">
              <text class="detail-label">核销时间</text>
              <text class="detail-value">{{appointmentDetail.verifyTime || '未知'}}</text>
            </view>
            <view class="detail-item" wx:if="{{appointmentDetail.verifyStaffName}}">
              <text class="detail-label">核销员工</text>
              <text class="detail-value">{{appointmentDetail.verifyStaffName}}</text>
            </view>
          </view>
          
          <!-- 拒绝原因 -->
          <view class="detail-section" wx:if="{{appointmentDetail.status === 'rejected' && appointmentDetail.rejectReason}}">
            <view class="section-title">拒绝原因</view>
            <view class="reject-reason-detail">
              <text>{{appointmentDetail.rejectReason}}</text>
            </view>
          </view>
          
          <!-- 参考图片 -->
          <view class="detail-section" wx:if="{{appointmentDetail.imageUrls && appointmentDetail.imageUrls.length > 0}}">
            <view class="section-title">参考图片</view>
            <view class="image-gallery">
              <image 
                wx:for="{{appointmentDetail.imageUrls}}" 
                wx:key="index" 
                src="{{item}}" 
                mode="aspectFill" 
                class="reference-image"
                bindtap="previewImage"
                data-index="{{index}}"
              ></image>
            </view>
          </view>
        </block>
      </scroll-view>
    </view>
  </view>
  
  <!-- 统计面板 -->
  <view class="stats-panel {{showStats ? 'show' : ''}}" wx:if="{{showStats}}">
    <view class="stats-mask" bindtap="closeStats"></view>
    <view class="stats-content">
      <view class="stats-header">
        <text class="stats-title">预约业绩统计</text>
        <view class="stats-close" bindtap="closeStats">×</view>
      </view>
      
      <!-- 日期选择 -->
      <view class="stats-date-filter">
        <view class="date-range-picker">
          <view class="date-picker-item">
            <text class="date-label">开始日期：</text>
            <picker mode="date" bindchange="onStartDateChange" value="{{startDate}}">
              <view class="date-value">{{startDate || '请选择'}}</view>
            </picker>
          </view>
          
          <view class="date-picker-item">
            <text class="date-label">结束日期：</text>
            <picker mode="date" bindchange="onEndDateChange" value="{{endDate}}">
              <view class="date-value">{{endDate || '请选择'}}</view>
            </picker>
          </view>
          
          <view class="stats-search-btn" bindtap="viewStats">查询</view>
        </view>
      </view>
      
      <!-- 统计数据展示 -->
      <scroll-view scroll-y="true" class="stats-body">
        <!-- 加载中 -->
        <view class="stats-loading" wx:if="{{!stats}}">
          <view class="loading-spinner"></view>
          <text>加载统计数据中...</text>
        </view>
        
        <!-- 统计数据 -->
        <block wx:else>
          <!-- 基础数据 -->
          <view class="stats-card">
            <view class="stats-card-title">基础数据</view>
            <view class="stats-basic">
              <view class="stats-basic-item">
                <text class="stats-basic-value">{{stats.total || 0}}</text>
                <text class="stats-basic-label">总预约数</text>
              </view>
              <view class="stats-basic-item">
                <text class="stats-basic-value">¥{{stats.income.total || 0}}</text>
                <text class="stats-basic-label">总营业额</text>
              </view>
              <view class="stats-basic-item">
                <text class="stats-basic-value">¥{{stats.income.completed || 0}}</text>
                <text class="stats-basic-label">已完成营业额</text>
              </view>
            </view>
          </view>
          
          <!-- 状态统计 -->
          <view class="stats-card">
            <view class="stats-card-title">预约状态分布</view>
            <view class="stats-status">
              <view class="stats-status-item">
                <text class="stats-status-label">待确认</text>
                <text class="stats-status-value">{{stats.statusStats.pending || 0}}</text>
              </view>
              <view class="stats-status-item">
                <text class="stats-status-label">已确认</text>
                <text class="stats-status-value">{{stats.statusStats.confirmed || 0}}</text>
              </view>
              <view class="stats-status-item">
                <text class="stats-status-label">已完成</text>
                <text class="stats-status-value">{{stats.statusStats.completed || 0}}</text>
              </view>
              <view class="stats-status-item">
                <text class="stats-status-label">已取消</text>
                <text class="stats-status-value">{{stats.statusStats.cancelled || 0}}</text>
              </view>
              <view class="stats-status-item">
                <text class="stats-status-label">已拒绝</text>
                <text class="stats-status-value">{{stats.statusStats.rejected || 0}}</text>
              </view>
            </view>
          </view>
          
          <!-- 服务统计 -->
          <view class="stats-card">
            <view class="stats-card-title">服务预约分布</view>
            <view class="stats-service">
              <view class="stats-service-item" wx:for="{{stats.serviceStats}}" wx:key="serviceId">
                <view class="stats-service-name">{{item.name}}</view>
                <view class="stats-service-data">
                  <text class="stats-service-count">预约次数: {{item.count}}</text>
                  <text class="stats-service-income">收入: ¥{{item.income}}</text>
                </view>
              </view>
              <view class="stats-empty" wx:if="{{stats.serviceStats.length === 0}}">
                <text>暂无服务统计数据</text>
              </view>
            </view>
          </view>
          
          <!-- 日期统计 -->
          <view class="stats-card">
            <view class="stats-card-title">日期预约分布</view>
            <view class="stats-date">
              <view class="stats-date-item" wx:for="{{stats.dateStats}}" wx:key="date">
                <view class="stats-date-label">{{item.date}}</view>
                <view class="stats-date-data">
                  <text class="stats-date-count">预约次数: {{item.count}}</text>
                  <text class="stats-date-income">收入: ¥{{item.income}}</text>
                </view>
              </view>
              <view class="stats-empty" wx:if="{{stats.dateStats.length === 0}}">
                <text>暂无日期统计数据</text>
              </view>
            </view>
          </view>
        </block>
      </scroll-view>
    </view>
  </view>
  
  <!-- 员工业绩面板 -->
  <view class="stats-panel {{showStaffPerformance ? 'show' : ''}}" wx:if="{{showStaffPerformance}}">
    <view class="stats-mask" bindtap="closeStaffPerformance"></view>
    <view class="stats-content">
      <view class="stats-header">
        <text class="stats-title">员工业绩详情 - {{selectedStaffName}}</text>
        <view class="stats-close" bindtap="closeStaffPerformance">×</view>
      </view>
      
      <!-- 日期选择 -->
      <view class="stats-date-filter">
        <view class="date-range-picker">
          <view class="date-picker-item">
            <text class="date-label">开始日期：</text>
            <picker mode="date" bindchange="onStartDateChange" value="{{startDate}}">
              <view class="date-value">{{startDate || '请选择'}}</view>
            </picker>
          </view>
          
          <view class="date-picker-item">
            <text class="date-label">结束日期：</text>
            <picker mode="date" bindchange="onEndDateChange" value="{{endDate}}">
              <view class="date-value">{{endDate || '请选择'}}</view>
            </picker>
          </view>
          
          <view class="stats-search-btn" bindtap="viewStaffPerformance">查询</view>
        </view>
      </view>
      
      <!-- 员工业绩数据展示 -->
      <scroll-view scroll-y="true" class="stats-body">
        <!-- 加载中 -->
        <view class="stats-loading" wx:if="{{!staffPerformance}}">
          <view class="loading-spinner"></view>
          <text class="loading-message">加载员工业绩数据中...</text>
        </view>
        
        <!-- 业绩数据 -->
        <block wx:else>
          <!-- 基础数据 -->
          <view class="stats-card">
            <view class="stats-card-title">业绩概览</view>
            <view class="stats-basic">
              <view class="stats-basic-item">
                <text class="stats-basic-value">{{staffPerformance.totalOrders || 0}}</text>
                <text class="stats-basic-label">总订单数</text>
              </view>
              <view class="stats-basic-item">
                <text class="stats-basic-value">¥{{staffPerformance.totalIncome || 0}}</text>
                <text class="stats-basic-label">总收入</text>
              </view>
              <view class="stats-basic-item">
                <text class="stats-basic-value">¥{{staffPerformance.totalCommission || 0}}</text>
                <text class="stats-basic-label">总提成</text>
              </view>
            </view>
          </view>
          
          <!-- 订单列表 -->
          <view class="stats-card">
            <view class="stats-card-title">订单明细</view>
            <view class="staff-order-list">
              <block wx:if="{{staffPerformance.orders && staffPerformance.orders.length > 0}}">
                <view class="staff-order-item" wx:for="{{staffPerformance.orders}}" wx:key="appointmentId">
                  <view class="staff-order-header">
                    <text class="staff-order-date">{{item.date}} {{item.time}}</text>
                    <text class="staff-order-status">{{item.status === 'completed' ? '已完成' : '待完成'}}</text>
                  </view>
                  <view class="staff-order-content">
                    <text class="staff-order-service">{{item.serviceName}}</text>
                    <view class="staff-order-price">
                      <text class="price-text">收入: ¥{{item.servicePrice}}</text>
                      <text class="price-text">提成: ¥{{item.commission}}</text>
                    </view>
                  </view>
                </view>
              </block>
              <view class="staff-order-empty" wx:else>
                <text class="empty-notice">所选时间段内无订单记录</text>
              </view>
            </view>
          </view>
          
          <!-- 日期统计 -->
          <view class="stats-card">
            <view class="stats-card-title">日期业绩分布</view>
            <view class="staff-date-stats">
              <block wx:if="{{staffPerformance.dateStats && staffPerformance.dateStats.length > 0}}">
                <view class="staff-date-item" wx:for="{{staffPerformance.dateStats}}" wx:key="date">
                  <view class="staff-date-header">
                    <text class="staff-date-label">{{item.date}}</text>
                    <text class="staff-date-count">{{item.count}}单</text>
                  </view>
                  <view class="staff-date-data">
                    <text class="staff-date-income">收入: ¥{{item.income}}</text>
                    <text class="staff-date-commission">提成: ¥{{item.commission}}</text>
                  </view>
                </view>
              </block>
              <view class="staff-date-empty" wx:else>
                <text class="empty-notice">所选时间段内无日期统计数据</text>
              </view>
            </view>
          </view>
        </block>
      </scroll-view>
    </view>
  </view>
</view>