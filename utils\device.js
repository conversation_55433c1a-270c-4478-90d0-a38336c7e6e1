/**
 * 设备工具函数
 * 用于获取设备唯一标识和其他设备相关信息
 */

// 获取设备唯一标识
function getDeviceIdentifier() {
  try {
    // 尝试从本地存储获取设备ID
    let deviceId = wx.getStorageSync('device_identifier');
    
    // 如果本地没有存储设备ID，则生成一个新的
    if (!deviceId) {
      // 获取系统信息
      const systemInfo = wx.getSystemInfoSync();
      
      // 组合设备信息生成唯一标识
      const brand = systemInfo.brand || '';
      const model = systemInfo.model || '';
      const system = systemInfo.system || '';
      const platform = systemInfo.platform || '';
      const SDKVersion = systemInfo.SDKVersion || '';
      
      // 生成随机字符串
      const randomStr = Math.random().toString(36).substring(2, 15);
      const timestamp = Date.now().toString(36);
      
      // 组合设备信息和随机字符串生成唯一标识
      deviceId = `${brand}_${model}_${platform}_${randomStr}_${timestamp}`;
      
      // 存储到本地
      wx.setStorageSync('device_identifier', deviceId);
    }
    
    return deviceId;
  } catch (err) {
    console.error('获取设备标识失败:', err);
    
    // 如果获取失败，返回一个基于时间戳的临时ID
    const tempId = `temp_${Date.now()}_${Math.random().toString(36).substring(2, 10)}`;
    return tempId;
  }
}

module.exports = {
  getDeviceIdentifier
}; 