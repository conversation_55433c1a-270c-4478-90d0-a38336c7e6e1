<!--系统设置管理页面-->
<view class="settings-container">
  <!-- 顶部标题栏 -->
  <view class="header">
    <view class="back-icon" bindtap="goBack">
      <text>←</text>
    </view>
    <view class="title">系统设置</view>
    <view class="refresh-btn" bindtap="refreshSettings">
      <image class="refresh-icon" src="/static/刷新图标.png" mode="aspectFit"></image>
    </view>
  </view>

  <!-- 加载中状态 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 设置内容 -->
  <scroll-view scroll-y class="settings-content" wx:else>
    <!-- 管理员账号设置 -->
    <view class="settings-section">
      <view class="section-header" bindtap="toggleSection" data-section="adminSettings">
        <text class="section-title">管理员账号设置</text>
        <text class="section-toggle">{{showAdminSettings ? '收起' : '展开'}}</text>
      </view>
      
      <view class="section-content" wx:if="{{showAdminSettings}}">
        <!-- 当前账号信息 -->
        <view class="setting-item">
          <text class="setting-label">当前账号：</text>
          <text class="setting-value">{{adminInfo.username || '管理员'}}</text>
        </view>
        
        <!-- 修改密码 -->
        <view class="setting-item">
          <text class="setting-label">原密码：</text>
          <input class="setting-input" type="password" password placeholder="" value="{{passwordForm.oldPassword}}" bindinput="onInputChange" data-field="oldPassword"></input>
        </view>
        
        <view class="setting-item">
          <text class="setting-label">新密码：</text>
          <input class="setting-input" type="password" password placeholder="" value="{{passwordForm.newPassword}}" bindinput="onInputChange" data-field="newPassword"></input>
        </view>
        
        <view class="setting-item">
          <text class="setting-label">确认密码：</text>
          <input class="setting-input" type="password" password placeholder="" value="{{passwordForm.confirmPassword}}" bindinput="onInputChange" data-field="confirmPassword"></input>
        </view>
        
        <view class="setting-note">
          <text>注：密码长度至少为6位，包含字母和数字</text>
        </view>
        
        <view class="action-button" bindtap="changePassword">
          <text>修改密码</text>
        </view>
      </view>
    </view>
    
    <!-- 关于系统 -->
    <view class="settings-section">
      <view class="section-header" bindtap="toggleSection" data-section="aboutSystem">
        <text class="section-title">关于系统</text>
        <text class="section-toggle">{{showAboutSystem ? '收起' : '展开'}}</text>
      </view>
      
      <view class="section-content" wx:if="{{showAboutSystem}}">
        <view class="setting-item">
          <text class="setting-label">系统版本：</text>
          <text class="setting-value">1.0.0</text>
        </view>
        
        <view class="setting-item">
          <text class="setting-label">技术支持：</text>
          <text class="setting-value">指家科技（广州）有限公司</text>
        </view>
        
        <view class="setting-item">
          <text class="setting-label">联系方式：</text>
          <text class="setting-value">微信号：naildidi</text>
        </view>
      </view>
    </view>
  </scroll-view>
</view> 