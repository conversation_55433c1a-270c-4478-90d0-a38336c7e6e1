<view class="orders-container">
  <!-- 顶部状态栏 - 只保留一个标题 -->
  <view class="page-header">
    <view class="back-icon" bindtap="goBack">
      <text class="iconfont icon-back">←</text>
    </view>
    <view class="header-title">{{pageTitle}}</view>
    <view class="placeholder"></view>
  </view>

  <!-- 内容区域 - 使用scroll-view使其可滚动 -->
  <scroll-view scroll-y="true" class="content-scroll">
    <view class="content">
      <!-- 加载中 -->
      <view class="loading" wx:if="{{isLoading}}">
        <view class="loading-icon"></view>
        <view class="loading-text">加载中...</view>
      </view>

      <!-- 空状态 -->
      <view class="empty-state" wx:if="{{!isLoading && isEmpty}}">
        <view class="empty-icon">📋</view>
        <view class="empty-text">暂无{{paymentMethod === 'cash' ? '现金' : '余额'}}核销记录</view>
        <view class="empty-tips">核销的预约将在这里显示</view>
      </view>

      <!-- 订单列表 -->
      <view class="order-list" wx:if="{{!isLoading && !isEmpty}}">
        <block wx:for="{{orderList}}" wx:key="_id">
          <view class="order-item" bindtap="viewOrderDetail" data-id="{{item._id}}">
            <view class="order-header">
              <view class="order-status {{item.status}}">{{item.statusText}}</view>
              <view class="payment-method {{item.paymentMethod}}">{{item.paymentMethodText}}</view>
            </view>
            
            <view class="order-info">
              <view class="info-row">
                <view class="info-label">预约时间:</view>
                <view class="info-value">{{item.formattedDate}} {{item.appointmentTime}}</view>
              </view>
              
              <view class="info-row">
                <view class="info-label">服务项目:</view>
                <view class="info-value">{{item.serviceName}}</view>
              </view>
              
              <view class="info-row" wx:if="{{item.verifyTime}}">
                <view class="info-label">核销时间:</view>
                <view class="info-value">{{item.verifyTime}}</view>
              </view>
              
              <view class="info-row">
                <view class="info-label">联系电话:</view>
                <view class="info-value">{{item.userPhone}}</view>
              </view>

              <view class="info-row">
                <view class="info-label">消费金额:</view>
                <view class="info-value total-price">¥{{item.servicePrice}}</view>
              </view>

              <view class="info-row">
                <view class="info-label">提成金额:</view>
                <view class="info-value commission">¥{{item.commission}}</view>
              </view>
            </view>
          </view>
        </block>
      </view>
    </view>
  </scroll-view>
</view>

<!-- 如果将来需要使用price-summary，请使用以下格式 -->
<!-- 
<view class="price-summary">
  <text class="price-text">价格文本</text>
</view>
--> 