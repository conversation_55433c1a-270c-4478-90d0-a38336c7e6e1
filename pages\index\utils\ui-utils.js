/**
 * UI相关工具函数
 * 从主页面提取的UI处理、防抖节流、滚动处理等工具函数
 */

/**
 * 节流函数 - 限制函数调用频率
 * @param {Function} fn - 要节流的函数
 * @param {number} delay - 延迟时间（毫秒）
 * @returns {Function} 节流后的函数
 */
function throttle(fn, delay) {
  let lastCall = 0;
  return function () {
    const now = Date.now();
    const args = arguments;
    const context = this;
    
    if (now - lastCall >= delay) {
      fn.apply(context, args);
      lastCall = now;
    }
  };
}

/**
 * 防抖函数 - 延迟执行函数，如果在延迟期间再次调用则重新计时
 * @param {Function} fn - 要防抖的函数
 * @param {number} delay - 延迟时间（毫秒）
 * @returns {Function} 防抖后的函数
 */
function debounce(fn, delay) {
  let timeoutId = null;
  return function () {
    const args = arguments;
    const context = this;
    
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => {
      fn.apply(context, args);
    }, delay);
  };
}

/**
 * 滚动位置管理器
 */
class ScrollManager {
  constructor() {
    this.lastScrollTop = 0;
    this.scrollDirection = 'none'; // 'up', 'down', 'none'
    this.isScrolling = false;
    this.scrollEndTimer = null;
  }
  
  /**
   * 更新滚动位置
   * @param {number} scrollTop - 当前滚动位置
   * @returns {Object} 滚动信息
   */
  updateScrollPosition(scrollTop) {
    const scrollDiff = scrollTop - this.lastScrollTop;
    const previousDirection = this.scrollDirection;
    
    // 确定滚动方向
    if (scrollDiff > 0) {
      this.scrollDirection = 'down';
    } else if (scrollDiff < 0) {
      this.scrollDirection = 'up';
    } else {
      this.scrollDirection = 'none';
    }
    
    // 标记正在滚动
    this.isScrolling = true;
    
    // 清除之前的定时器
    if (this.scrollEndTimer) {
      clearTimeout(this.scrollEndTimer);
    }
    
    // 设置滚动结束检测
    this.scrollEndTimer = setTimeout(() => {
      this.isScrolling = false;
      this.scrollDirection = 'none';
    }, 150);
    
    const scrollInfo = {
      currentScrollTop: scrollTop,
      lastScrollTop: this.lastScrollTop,
      scrollDiff: scrollDiff,
      direction: this.scrollDirection,
      directionChanged: this.scrollDirection !== previousDirection,
      isScrolling: this.isScrolling
    };
    
    this.lastScrollTop = scrollTop;
    return scrollInfo;
  }
  
  /**
   * 重置滚动状态
   */
  reset() {
    this.lastScrollTop = 0;
    this.scrollDirection = 'none';
    this.isScrolling = false;
    if (this.scrollEndTimer) {
      clearTimeout(this.scrollEndTimer);
      this.scrollEndTimer = null;
    }
  }
}

/**
 * 创建滚动管理器实例
 * @returns {ScrollManager}
 */
function createScrollManager() {
  return new ScrollManager();
}

/**
 * 安全的页面滚动到顶部
 * @param {Object} options - 滚动选项
 * @returns {Promise<boolean>} 滚动是否成功
 */
function safeScrollToTop(options = {}) {
  const {
    duration = 0,
    selector = '.main-scroll',
    fallbackPageScroll = true
  } = options;
  
  return new Promise((resolve) => {
    try {
      // 方法1: 尝试通过选择器滚动
      wx.createSelectorQuery()
        .select(selector)
        .node()
        .exec((res) => {
          if (res && res[0] && res[0].node) {
            res[0].node.scrollTop = 0;
            console.log('[UIUtils] 通过选择器滚动到顶部成功');
            resolve(true);
          } else if (fallbackPageScroll) {
            // 方法2: 备用页面滚动
            wx.pageScrollTo({
              scrollTop: 0,
              duration: duration,
              success: () => {
                console.log('[UIUtils] 通过页面滚动到顶部成功');
                resolve(true);
              },
              fail: (error) => {
                console.warn('[UIUtils] 页面滚动失败:', error);
                resolve(false);
              }
            });
          } else {
            resolve(false);
          }
        });
    } catch (error) {
      console.error('[UIUtils] 滚动到顶部失败:', error);
      resolve(false);
    }
  });
}

/**
 * 获取元素尺寸信息
 * @param {string} selector - 选择器
 * @returns {Promise<Object>} 尺寸信息
 */
function getElementSize(selector) {
  return new Promise((resolve, reject) => {
    wx.createSelectorQuery()
      .select(selector)
      .boundingClientRect()
      .exec((res) => {
        if (res && res[0]) {
          resolve(res[0]);
        } else {
          reject(new Error(`元素不存在: ${selector}`));
        }
      });
  });
}

/**
 * 获取多个元素的尺寸信息
 * @param {Array<string>} selectors - 选择器数组
 * @returns {Promise<Array>} 尺寸信息数组
 */
function getMultipleElementSizes(selectors) {
  const promises = selectors.map(selector => 
    getElementSize(selector).catch(error => ({ error, selector }))
  );
  
  return Promise.all(promises);
}

/**
 * 检查元素是否在可视区域内
 * @param {string} selector - 选择器
 * @param {Object} options - 检查选项
 * @returns {Promise<boolean>} 是否在可视区域
 */
function isElementInViewport(selector, options = {}) {
  const { threshold = 0 } = options;
  
  return new Promise((resolve) => {
    wx.createSelectorQuery()
      .select(selector)
      .boundingClientRect()
      .selectViewport()
      .scrollOffset()
      .exec((res) => {
        if (!res || !res[0] || !res[1]) {
          resolve(false);
          return;
        }
        
        const rect = res[0];
        const viewport = res[1];
        
        const isVisible = (
          rect.top >= -threshold &&
          rect.left >= -threshold &&
          rect.bottom <= viewport.height + threshold &&
          rect.right <= viewport.width + threshold
        );
        
        resolve(isVisible);
      });
  });
}

/**
 * 动画帧请求包装器
 * @param {Function} callback - 回调函数
 * @returns {number} 动画帧ID
 */
function requestAnimationFrame(callback) {
  // 微信小程序没有原生的requestAnimationFrame，使用setTimeout模拟
  return setTimeout(callback, 16); // 约60fps
}

/**
 * 取消动画帧
 * @param {number} id - 动画帧ID
 */
function cancelAnimationFrame(id) {
  clearTimeout(id);
}

/**
 * 平滑动画函数
 * @param {Object} options - 动画选项
 * @returns {Promise<void>} 动画完成Promise
 */
function smoothAnimation(options) {
  const {
    duration = 300,
    easing = 'easeOut',
    onUpdate,
    onComplete
  } = options;
  
  return new Promise((resolve) => {
    const startTime = Date.now();
    let animationId;
    
    const easingFunctions = {
      linear: t => t,
      easeIn: t => t * t,
      easeOut: t => t * (2 - t),
      easeInOut: t => t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t
    };
    
    const easingFn = easingFunctions[easing] || easingFunctions.easeOut;
    
    function animate() {
      const elapsed = Date.now() - startTime;
      const progress = Math.min(elapsed / duration, 1);
      const easedProgress = easingFn(progress);
      
      if (onUpdate) {
        onUpdate(easedProgress, progress);
      }
      
      if (progress < 1) {
        animationId = requestAnimationFrame(animate);
      } else {
        if (onComplete) {
          onComplete();
        }
        resolve();
      }
    }
    
    animationId = requestAnimationFrame(animate);
  });
}

/**
 * 触摸事件处理器
 */
class TouchHandler {
  constructor() {
    this.startX = 0;
    this.startY = 0;
    this.endX = 0;
    this.endY = 0;
    this.startTime = 0;
    this.endTime = 0;
  }
  
  /**
   * 处理触摸开始
   * @param {Object} e - 触摸事件
   */
  handleTouchStart(e) {
    const touch = e.touches[0];
    this.startX = touch.clientX;
    this.startY = touch.clientY;
    this.startTime = Date.now();
  }
  
  /**
   * 处理触摸结束
   * @param {Object} e - 触摸事件
   * @returns {Object} 触摸信息
   */
  handleTouchEnd(e) {
    const touch = e.changedTouches[0];
    this.endX = touch.clientX;
    this.endY = touch.clientY;
    this.endTime = Date.now();
    
    const deltaX = this.endX - this.startX;
    const deltaY = this.endY - this.startY;
    const deltaTime = this.endTime - this.startTime;
    const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
    
    return {
      deltaX,
      deltaY,
      deltaTime,
      distance,
      direction: this.getSwipeDirection(deltaX, deltaY),
      isSwipe: distance > 30 && deltaTime < 300,
      isTap: distance < 10 && deltaTime < 300
    };
  }
  
  /**
   * 获取滑动方向
   * @param {number} deltaX - X轴偏移
   * @param {number} deltaY - Y轴偏移
   * @returns {string} 方向
   */
  getSwipeDirection(deltaX, deltaY) {
    if (Math.abs(deltaX) > Math.abs(deltaY)) {
      return deltaX > 0 ? 'right' : 'left';
    } else {
      return deltaY > 0 ? 'down' : 'up';
    }
  }
}

/**
 * 创建触摸处理器
 * @returns {TouchHandler}
 */
function createTouchHandler() {
  return new TouchHandler();
}

/**
 * 显示加载提示
 * @param {string} title - 提示文字
 * @param {boolean} mask - 是否显示透明蒙层
 */
function showLoading(title = '加载中...', mask = true) {
  wx.showLoading({
    title,
    mask
  });
}

/**
 * 隐藏加载提示
 */
function hideLoading() {
  wx.hideLoading();
}

/**
 * 显示成功提示
 * @param {string} title - 提示文字
 * @param {number} duration - 显示时长
 */
function showSuccess(title, duration = 1500) {
  wx.showToast({
    title,
    icon: 'success',
    duration
  });
}

/**
 * 显示错误提示
 * @param {string} title - 提示文字
 * @param {number} duration - 显示时长
 */
function showError(title, duration = 2000) {
  wx.showToast({
    title,
    icon: 'none',
    duration
  });
}

/**
 * 显示警告提示
 * @param {string} title - 提示文字
 * @param {number} duration - 显示时长
 */
function showWarning(title, duration = 2000) {
  wx.showToast({
    title,
    icon: 'none',
    duration
  });
}

/**
 * 安全的setData调用
 * @param {Object} pageContext - 页面上下文
 * @param {Object} data - 要设置的数据
 * @param {Function} callback - 回调函数
 */
function safeSetData(pageContext, data, callback) {
  if (!pageContext || typeof pageContext.setData !== 'function') {
    console.warn('[UIUtils] safeSetData: 无效的页面上下文');
    return;
  }
  
  try {
    pageContext.setData(data, callback);
  } catch (error) {
    console.error('[UIUtils] safeSetData 失败:', error);
  }
}

/**
 * 批量设置数据（防止频繁setData）
 * @param {Object} pageContext - 页面上下文
 * @param {number} delay - 延迟时间
 * @returns {Function} 批量设置函数
 */
function createBatchSetData(pageContext, delay = 16) {
  let pendingData = {};
  let timeoutId = null;
  
  return function batchSetData(data, callback) {
    // 合并数据
    Object.assign(pendingData, data);
    
    // 清除之前的定时器
    if (timeoutId) {
      clearTimeout(timeoutId);
    }
    
    // 设置新的定时器
    timeoutId = setTimeout(() => {
      safeSetData(pageContext, pendingData, callback);
      pendingData = {};
      timeoutId = null;
    }, delay);
  };
}

// 导出所有工具函数
module.exports = {
  // 基础工具函数
  throttle,
  debounce,
  
  // 滚动管理
  ScrollManager,
  createScrollManager,
  safeScrollToTop,
  
  // 元素操作
  getElementSize,
  getMultipleElementSizes,
  isElementInViewport,
  
  // 动画相关
  requestAnimationFrame,
  cancelAnimationFrame,
  smoothAnimation,
  
  // 触摸处理
  TouchHandler,
  createTouchHandler,
  
  // 用户提示
  showLoading,
  hideLoading,
  showSuccess,
  showError,
  showWarning,
  
  // 数据设置
  safeSetData,
  createBatchSetData
};