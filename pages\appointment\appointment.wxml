<view class="appointment-container">
  <!-- 固定的渐变背景层，始终显示 -->
  <view class="fixed-background"></view>
  
  <!-- 页面内容区域，使用scroll-view包装整个内容使其可滚动，并添加下拉刷新功能 -->
  <scroll-view 
    scroll-y="true" 
    class="page-scroll-view {{showContent ? 'show-content' : ''}}" 
    refresher-enabled="{{true}}" 
    refresher-threshold="{{80}}" 
    refresher-default-style="black" 
    refresher-background="#1A1A1A"
    refresher-triggered="{{isRefreshing}}"
    bindrefresherrefresh="onScrollViewRefresh">
    <!-- 轮播图区域 -->
    <view class="carousel-container {{isRefreshing ? 'loading-content-placeholder' : ''}}">
      <!-- 添加调试信息 -->
      <view wx:if="{{!carousels || carousels.length === 0}}" style="padding: 10rpx; color: #fff; text-align: center; font-size: 24rpx;">
        {{loadingCarousels ? '正在加载轮播图...' : '暂无轮播图数据'}}
      </view>

      <swiper wx:if="{{carousels && carousels.length > 0}}" class="carousel-swiper" indicator-dots="{{true}}" autoplay="{{true}}" interval="{{3000}}" duration="{{1000}}" circular="{{true}}" easing-function="easeInOutCubic" bindchange="onCarouselChange">
        <swiper-item wx:for="{{carousels}}" wx:key="id">
          <image wx:if="{{item.imageUrl}}" src="{{item.imageUrl}}" class="carousel-image" mode="aspectFill" binderror="onCarouselImageError" data-index="{{index}}"></image>
          <view wx:else class="carousel-placeholder"></view>
        </swiper-item>
      </swiper>

      <!-- 底部渐变遮罩 -->
      <view wx:if="{{carousels && carousels.length > 0}}" class="carousel-bottom-gradient"></view>
    </view>
    
    <!-- 固定的标题容器 (放在轮播图和服务列表之间) -->
    <view class="carousel-title-container {{isRefreshing ? 'loading-content-placeholder' : ''}}" wx:if="{{carousels && carousels.length > 0 && carousels[currentCarouselIndex]}}" bindtap="openLocation">
      <!-- 主标题区域 -->
      <view class="carousel-title-main" data-text="{{carousels[currentCarouselIndex].title}}" catchtap="">
        {{carousels[currentCarouselIndex].title}}
      </view>
      
      <!-- 添加独立的装饰线 -->
      <view class="title-decorator" catchtap=""></view>
      
      <!-- 底部信息栏 -->
      <view class="carousel-info-bar">
        <!-- 左侧信息区域：Logo和店名 -->
        <view class="carousel-left-info" catchtap="">
          <image class="carousel-logo-icon" src="/static/logo.png" mode="aspectFit"></image>
          <text class="carousel-subtitle">指家科技</text>
        </view>
        
        <!-- 中间信息区域：地址 -->
        <view class="carousel-location" catchtap="openLocation">
          <image class="carousel-location-icon" src="/static/定位图标.png" mode="aspectFit"></image>
          <text class="carousel-location-text">店铺地址</text>
        </view>
        
        <!-- 右侧信息区域：营业时间 -->
        <view class="carousel-business-hours" catchtap="">
          <image class="carousel-business-icon" src="/static/营业图标.png" mode="aspectFit"></image>
          <text class="carousel-business-text">09:30至22:00</text>
        </view>
      </view>
    </view>

    <!-- 主内容区域 -->
    <view class="appointment-content">
      <!-- 步骤指示器 -->
      <view class="step-indicator" wx:if="{{currentStep === 2}}">
        <view class="back-button" bindtap="goToPrevStep">
          <text class="icon-back">←</text>
          <text>返回选择服务</text>
        </view>
      </view>

      <!-- 第一步：选择服务项目 -->
      <view class="step-content" wx:if="{{currentStep === 1}}">
        <!-- 服务项目列表 -->
        <view class="service-selector">
          <!-- 加载中状态 -->
          <view class="loading-container" wx:if="{{loadingServices}}">
            <view class="loading-spinner"></view>
            <text class="loading-text">正在加载服务项目...</text>
          </view>
          
          <!-- 空状态 -->
          <view class="empty-container" wx:elif="{{services.length === 0}}">
            <text class="empty-text">暂无可预约的服务项目</text>
          </view>
          
          <!-- 服务列表 - 不再使用内部的scroll-view，因为外层已有scroll-view -->
          <view class="service-list" wx:else>
            <view 
              wx:for="{{services}}" 
              wx:key="id"
              class="service-item {{selectedService === item.id ? 'selected' : ''}}"
              bindtap="selectService"
              data-id="{{item.id}}"
            >
              <view class="service-card {{isRefreshing ? 'loading-content-placeholder' : ''}}">
                <image class="service-image" src="{{item.image || '/static/default-service.png'}}" mode="aspectFill" bindtap="showServiceDetail" data-detail-image="{{item.detailImage}}" catchtap="showServiceDetail"></image>
                <view class="select-indicator" wx:if="{{selectedService === item.id}}">
                  <text class="check-icon">✓</text>
                </view>
                <view class="new-tag" wx:if="{{item.isNew}}">最新</view>
                <view class="service-content">
                  <!-- 服务名称和预约按钮在同一行 -->
                  <view class="service-header">
                    <text class="service-name">{{item.name}}</text>
                    <!-- 分享按钮和预约按钮 -->
                    <view class="header-actions">
                      <!-- 分享按钮 -->
                      <view class="share-button-wrapper">
                        <button 
                          class="share-button" 
                          open-type="share" 
                          data-service="{{item}}"
                          catchtap="onShareService">
                          <image class="share-icon" src="/static/分享图标.png" mode="aspectFit"></image>
                        </button>
                      </view>
                      <!-- 预约按钮 -->
                      <view class="appointment-btn-wrapper">
                        <view class="appointment-btn" catchtap="showAppointmentModal" data-service="{{item}}">预约</view>
                      </view>
                    </view>
                  </view>
                  <!-- 服务描述和价格信息水平排列 -->
                  <view class="desc-price-container">
                    <!-- 服务描述 -->
                    <text class="service-desc">{{item.description}}</text>
                    <!-- 价格信息放在右侧 -->
                    <view class="price-info">
                      <view class="price-group">
                        <view class="price-row" wx:if="{{item.originalPrice && item.originalPrice > item.price}}">
                          <text class="original-price-label">原价</text>
                          <text class="service-original-price">¥{{item.originalPrice}}</text>
                          <text class="discount-price-label">现优惠价</text>
                          <text class="service-price">¥{{item.price}}</text>
                        </view>
                        <view class="price-row" wx:else>
                          <text class="discount-price-label">现优惠价</text>
                          <text class="service-price">¥{{item.price}}</text>
                        </view>
                      </view>
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 第二步：选择日期和时间 -->
      <view class="step-content" wx:if="{{currentStep === 2}}">
        <!-- 已选服务提示 -->
        <view class="selected-service-info">
          <view class="section-title section-title-centered no-decoration">已选服务</view>
          <view class="selected-service" wx:for="{{services}}" wx:key="id" wx:if="{{selectedService === item.id}}">
            <view class="service-info-step2">
              <text class="selected-service-name">{{item.name}}</text>
              <view class="price-info-step2">
                <view class="price-group">
                  <view class="price-row" wx:if="{{item.originalPrice && item.originalPrice > item.price}}">
                    <text class="original-price-label">原价：</text>
                    <text class="service-original-price">¥{{item.originalPrice}}</text>
                  </view>
                  <view class="price-row discount-price-row">
                    <text class="discount-price-label">现优惠价：</text>
                    <text class="selected-service-price">¥{{item.price}}</text>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 店铺地址信息 -->
        <view class="shop-location" bindtap="openLocation">
          <image src="/static/定位图标.png" class="location-icon" mode="aspectFit"></image>
          <text class="location-text">店铺地址</text>
        </view>

        <!-- 日期选择区域 -->
        <view class="date-selector">
          <picker mode="multiSelector" bindchange="bindMultiPickerChange" bindcolumnchange="bindMultiPickerColumnChange" value="{{multiIndex}}" range="{{multiArray}}">
            <view class="date-picker">
              <text>{{selectedDate ? selectedDate + ' ' + selectedTime : '选择预约时间'}}</text>
              <text class="arrow-down">▼</text>
            </view>
          </picker>
        </view>

        <!-- 手机号输入区域 -->
        <view class="phone-input-section">
          <view class="small-tip-left">手机号用于预约确认及到店提醒</view>
          <view class="phone-input-container">
            <input 
              class="phone-input {{highlightPhoneInput ? 'phone-input-highlight' : ''}}" 
              type="number" 
              maxlength="11" 
              placeholder="输入手机号" 
              placeholder-style="color: #666666; text-align: left;"
              value="{{phoneNumber}}"
              bindinput="inputPhoneNumber"
            />
          </view>
        </view>

        <!-- 时间选择列表 -->
        <scroll-view class="time-list" scroll-y wx:if="{{currentStep === 2}}">
          <view class="small-tip-left">请选择您方便的预约时间</view>
          <view class="time-scroll">
            <block wx:for="{{timeList}}" wx:key="time">
              <view 
                class="time-item {{item.time === selectedTime ? 'selected' : ''}} {{!item.available ? 'disabled' : ''}}"
                data-time="{{item.time}}"
                data-available="{{item.available}}"
                bindtap="selectTime"
              >
                {{item.time}}
                <view class="time-status" wx:if="{{!item.available}}">已过时</view>
              </view>
            </block>
          </view>
        </scroll-view>

        <!-- 提交按钮 -->
        <view class="action-section">
          <view class="action-btn submit-btn {{!canSubmit || submitting ? 'disabled' : ''}}" bindtap="submitAppointment">
            {{submitting ? '提交中...' : '确定提交'}}
          </view>
        </view>
      </view>
    </view>
  </scroll-view>
  
  <!-- 预约弹窗 -->
  <view class="appointment-modal {{isClosing ? 'closing' : ''}}" wx:if="{{showModal}}">
    <view class="modal-mask" bindtap="hideAppointmentModal"></view>
    <view class="modal-content {{showAppointmentAnimation ? 'show' : ''}}">
      <view class="modal-header">
        <text class="modal-title">预约服务</text>
        <view class="modal-close" bindtap="hideAppointmentModal">×</view>
      </view>
      
      <view class="modal-body">
        <!-- 已选服务信息 - 添加点击事件用于切换服务 -->
        <view class="selected-service-info {{dropdownOpen ? 'dropdown-open' : ''}}" bindtap="toggleServiceDropdown">
          <view class="service-header-modal">
            <view class="service-title-container">
              <view class="service-name">{{currentService.name}}</view>
              <view class="service-subtitle">{{currentService.description || '暂无描述'}}</view>
            </view>
            <view class="price-info-modal">
              <view class="price-group">
                <view class="price-row" wx:if="{{currentService.originalPrice && currentService.originalPrice > currentService.price}}">
                  <text class="original-price-label">原价：</text>
                  <text class="service-original-price">¥{{currentService.originalPrice}}</text>
                </view>
                <view class="price-row discount-price-row">
                  <text class="discount-price-label">现优惠价：</text>
                  <text class="service-price">¥{{currentService.price}}</text>
                </view>
              </view>
            </view>
          </view>
          
          <!-- 服务切换下拉菜单 -->
          <view class="service-dropdown {{showServiceDropdown ? 'show' : ''}}">
            <view 
              class="dropdown-item {{currentService.id === item.id ? 'active' : ''}}" 
              wx:for="{{services}}" 
              wx:key="id"
              catchtap="switchService"
              data-service="{{item}}"
            >
              <view class="service-title-container">
                <view class="service-name">{{item.name}}</view>
                <view class="service-subtitle">{{item.description || '暂无描述'}}</view>
              </view>
              <view class="price-info">
                <view class="price-group">
                  <view class="price-row" wx:if="{{item.originalPrice && item.originalPrice > item.price}}">
                    <text class="original-price-label">原价：</text>
                    <text class="service-original-price">¥{{item.originalPrice}}</text>
                  </view>
                  <view class="price-row discount-price-row">
                    <text class="discount-price-label">现优惠价：</text>
                    <text class="service-price">¥{{item.price}}</text>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
        
        <!-- 店铺地址信息 -->
        <view class="shop-location" bindtap="openLocation">
          <image src="/static/定位图标.png" class="location-icon" mode="aspectFit"></image>
          <text class="location-text">店铺地址</text>
        </view>
        
        <!-- 日期选择 -->
        <view class="date-selector">
          <picker mode="multiSelector" bindchange="bindMultiPickerChange" bindcolumnchange="bindMultiPickerColumnChange" value="{{multiIndex}}" range="{{multiArray}}" bindtap="onDatePickerTap">
            <view class="date-picker {{datePickerActive ? 'date-picker-active' : ''}}">
              <text>{{selectedDate ? selectedDate + ' ' + selectedTime : '选择预约时间'}}</text>
              <text class="arrow-down">▼</text>
            </view>
          </picker>
        </view>
        
        <!-- 手机号输入 -->
        <view class="phone-input-section">
          <view class="phone-input-container">
            <input 
              class="phone-input {{highlightPhoneInput ? 'phone-input-highlight' : ''}}" 
              type="number" 
              maxlength="11" 
              placeholder="输入手机号" 
              placeholder-style="color: #666666; text-align: left;"
              value="{{phoneNumber}}"
              bindinput="inputPhoneNumber"
            />
          </view>
        </view>
        
        <!-- 图片上传区域（可折叠） -->
        <view class="image-upload-section">
          <view class="image-header-container {{showImageUpload ? 'expanded' : ''}}" bindtap="toggleImageUpload">
            <view class="image-header-text {{uploadedImages.length > 0 ? 'images-selected' : ''}}">
              可添加参考图片
              <text class="image-optional">(可选，最多3张)</text>
              <view class="toggle-icon {{showImageUpload ? 'expanded' : ''}}">▼</view>
            </view>
          </view>
          
          <!-- 图片上传容器（可折叠） -->
          <view class="image-container" wx:if="{{showImageUpload}}">
            <!-- 删除提示文字 -->
            
            <!-- 图片预览区域 -->
            <view class="image-preview-area">
              <view class="uploaded-images">
                <!-- 第一个位置：显示已上传图片或添加按钮 -->
                <view class="image-item" wx:if="{{uploadedImages.length > 0}}">
                  <image class="preview-image" src="{{uploadedImages[0]}}" mode="aspectFill" bindtap="previewImage" data-index="0"></image>
                  <view class="delete-image" catchtap="deleteImage" data-index="0">×</view>
                </view>
                <view class="add-image-btn" bindtap="{{uploadedImages.length < 3 ? 'chooseImage' : ''}}" wx:else>
                  <view class="add-icon">+</view>
                  <view class="add-text">添加图片</view>
                </view>
                
                <!-- 第二个位置：显示已上传图片或添加按钮 -->
                <view class="image-item" wx:if="{{uploadedImages.length > 1}}">
                  <image class="preview-image" src="{{uploadedImages[1]}}" mode="aspectFill" bindtap="previewImage" data-index="1"></image>
                  <view class="delete-image" catchtap="deleteImage" data-index="1">×</view>
                </view>
                <view class="add-image-btn {{uploadedImages.length < 3 ? '' : 'disabled'}}" bindtap="{{uploadedImages.length < 3 ? 'chooseImage' : ''}}" wx:else>
                  <view class="add-icon">+</view>
                  <view class="add-text">添加图片</view>
                </view>
                
                <!-- 第三个位置：显示已上传图片或添加按钮 -->
                <view class="image-item" wx:if="{{uploadedImages.length > 2}}">
                  <image class="preview-image" src="{{uploadedImages[2]}}" mode="aspectFill" bindtap="previewImage" data-index="2"></image>
                  <view class="delete-image" catchtap="deleteImage" data-index="2">×</view>
                </view>
                <view class="add-image-btn {{uploadedImages.length < 3 ? '' : 'disabled'}}" bindtap="{{uploadedImages.length < 3 ? 'chooseImage' : ''}}" wx:else>
                  <view class="add-icon">+</view>
                  <view class="add-text">添加图片</view>
                </view>
              </view>
            </view>
          </view>
        </view>
        
        <!-- 员工选择区域（可折叠） -->
        <view class="staff-selection-section">
          <view class="staff-header-container {{showStaffSelection ? 'expanded' : ''}}" bindtap="toggleStaffSelection">
            <view class="staff-header-text {{selectedStaffId ? 'staff-selected' : ''}}">
              选择服务人员
              <text class="staff-optional">(可选或到店安排)</text>
              <view class="toggle-icon {{showStaffSelection ? 'expanded' : ''}}">▼</view>
            </view>
          </view>
          
          <!-- 员工选择容器（可折叠） -->
          <view class="staff-container" wx:if="{{showStaffSelection}}">
            <!-- 加载中状态 -->
            <view class="staff-loading" wx:if="{{loadingStaff}}">
              <view class="loading-spinner"></view>
              <text class="loading-text">加载中...</text>
            </view>
            
            <!-- 空状态 -->
            <view class="staff-empty" wx:elif="{{staffList.length === 0}}">
              <text class="empty-text">暂无可选服务人员</text>
            </view>
            
            <!-- 员工列表 -->
            <scroll-view scroll-x class="staff-list" wx:else>
              <view 
                class="staff-item {{selectedStaffId === item._id ? 'selected' : ''}} {{item.serviceStatus !== 'available' ? 'disabled' : ''}}" 
                wx:for="{{staffList}}" 
                wx:key="_id"
                bindtap="{{item.serviceStatus === 'available' ? 'selectStaff' : ''}}"
                data-id="{{item._id}}"
                data-name="{{item.name}}"
              >
                <image class="staff-avatar" src="{{item.avatar || '/static/微信图标.png'}}" mode="aspectFill"></image>
                <view class="staff-name">{{item.name}}</view>
                <view class="staff-status {{item.serviceStatus}}">
                  {{item.serviceStatus === 'available' ? '空闲' : item.serviceStatus === 'busy' ? '服务中' : '休息中'}}
                </view>
                <view class="staff-selected-icon" wx:if="{{selectedStaffId === item._id}}">✓</view>
              </view>
            </scroll-view>
          </view>
        </view>
      </view>
      
      <view class="modal-footer">
        <view class="cancel-btn" bindtap="hideAppointmentModal">取消预约</view>
        <view class="confirm-btn {{canSubmit ? '' : 'disabled'}}" bindtap="submitAppointment">
          {{submitting ? '提交中...' : '提交预约'}}
        </view>
      </view>
    </view>
  </view>

  <!-- 添加登录提示弹窗 -->
  <view class="login-modal" wx:if="{{showLoginModal}}">
    <view class="modal-mask" bindtap="closeLoginModal"></view>
    <view class="modal-content">
      <view class="modal-header">
        <text class="modal-title">登录提示</text>
        <view class="modal-close" bindtap="closeLoginModal">×</view>
      </view>
      
      <view class="modal-body">
        <view class="login-tip">
          <text class="login-tip-text">您需要登录后才能预约</text>
        </view>
        
        <button 
          class="login-btn" 
          open-type="getUserInfo" 
          bindgetuserinfo="onGetUserInfo"
        >微信登录</button>
      </view>
    </view>
  </view>

  <!-- 悬浮客服按钮 -->
  <floating-customer-service
    iconUrl="/images/客服图标.png"
    title="欢迎咨询"
    path="pages/appointment/appointment"
    imageUrl="/images/客服图标.png"
  />

  <!-- 服务详情图片弹窗 -->
  <view class="service-detail-modal {{showDetailModal ? 'show' : ''}}" wx:if="{{showDetailModal}}" bindtap="closeDetailModal">
    <view class="detail-modal-overlay fade-in" bindtap="closeDetailModal">
      <view class="detail-modal-content breathe-in" catchtap="preventClose">
        <!-- 添加可滚动的图片容器层 -->
        <scroll-view class="detail-image-scroll-container" scroll-y="{{true}}" enhanced="{{true}}" show-scrollbar="{{false}}">
          <image
            class="detail-image"
            src="{{detailImageUrl}}"
            mode="widthFix"
            show-menu-by-longpress="{{true}}"
            bindload="onDetailImageLoad"
            binderror="onDetailImageError"
          />
        </scroll-view>
        <!-- 关闭按钮固定在弹窗底部 -->
        <view class="detail-close-button" catchtap="closeDetailModal">
          <view class="close-icon">✕</view>
        </view>
      </view>
    </view>
  </view>
</view>

<!-- 指向广告弹窗 -->
<target-ad-modal
  show="{{showTargetAd}}"
  imageUrl="{{targetAdData.imageUrl}}"
  jumpUrl="{{targetAdData.jumpUrl}}"
  bind:close="onTargetAdClose"
/>