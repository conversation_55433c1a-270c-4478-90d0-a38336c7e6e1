/* pages/admin/gallery/gallery.wxss */
.page-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f7; /* 标准浅灰色背景 */
}

.page-header {
  position: sticky;
  top: 0;
  z-index: 100;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  padding-top: 90rpx; /* 增加顶部内边距，确保在胶囊按钮下方 */
  margin-bottom: 20rpx;
  background-color: #ffffff; /* 白色背景 */
  border-bottom: 1rpx solid #e0e0e0; /* 标准边框颜色 */
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.header-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #4a4a4a; /* 标准深灰色 */
}

.header-actions {
  display: flex;
  align-items: center;
}

/* 添加按钮 */
.add-button {
  position: fixed;
  left: 50%;
  bottom: 60rpx; /* 与底部保持安全距离 */
  transform: translateX(-50%); /* 水平居中 */
  width: 110rpx;
  height: 110rpx;
  border-radius: 50%;
  background-color: #0070c9; /* 标准蓝色 */
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 6rpx 15rpx rgba(0, 112, 201, 0.3); /* 蓝色阴影 */
  z-index: 99;
  transition: all 0.2s ease; /* 统一过渡时间 */
}

.add-button:active {
  transform: translateX(-50%) scale(0.95); /* 保持水平居中的同时缩小 */
  box-shadow: 0 2rpx 8rpx rgba(0, 112, 201, 0.2);
}

.add-icon {
  color: #ffffff;
  font-size: 70rpx;
  font-weight: bold;
  margin-bottom: 6rpx; /* 轻微下移，视觉上更居中 */
}

/* 安全区域，确保内容不被底部遮挡 */
.safe-bottom-area {
  height: 160rpx; /* 增加高度，为添加按钮留出足够空间 */
  width: 100%;
}

/* 内容区域 */
.content-admin-container {
  padding: 0 30rpx 30rpx;
  flex: 1;
}

/* 画廊项样式 */
.gallery-item {
  display: flex;
  background-color: #ffffff;
  border-radius: 6rpx; /* 标准卡片圆角 */
  padding: 20rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05); /* 轻微阴影 */
  transition: all 0.2s ease; /* 统一过渡时间 */
  position: relative;
  align-items: flex-start; /* 顶部对齐 */
}

.gallery-item:active {
  transform: scale(0.98);
  box-shadow: 0 1rpx 5rpx rgba(0, 0, 0, 0.03);
}

/* 画廊封面 - 左侧 */
.gallery-cover {
  width: 180rpx;
  height: 180rpx;
  border-radius: 4rpx; /* 标准按钮圆角 */
  object-fit: cover;
  background-color: #f5f5f7; /* 标准浅灰色背景 */
  flex-shrink: 0;
  margin-right: 20rpx; /* 与右侧内容的间距 */
  align-self: flex-start; /* 顶部对齐 */
}

/* 右侧内容区域 */
.right-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;
  height: 180rpx; /* 与图片高度一致 */
  justify-content: flex-start; /* 顶部对齐，手动控制底部元素位置 */
  position: relative; /* 添加相对定位 */
}

/* 标题行 */
.title-row {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
  position: relative;
}

/* 画廊标题 */
.gallery-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #4a4a4a; /* 标准深灰色 */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
}

/* 副标题样式 */
.gallery-subtitle {
  font-size: 26rpx;
  color: #666666; /* 标准次要文字色 */
  margin-top: 10rpx;
  margin-bottom: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 65%;
  line-height: 1.2;
  font-weight: 400;
}

/* 状态容器样式 */
.status-container {
  position: absolute;
  top: 0;
  right: 0;
  height: 44rpx;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

/* 画廊可见性状态样式 */
.gallery-visibility {
  padding: 0;
  display: flex;
  align-items: center;
  width: fit-content;
  position: relative;
}

/* 状态指示器样式 */
.status-indicator {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  margin-right: 6rpx;
}

/* 状态文本样式 */
.status-text {
  font-size: 24rpx;
  font-weight: 500;
  color: #666666;
}

/* 可见状态（绿色）- 添加呼吸动画 */
.gallery-visibility.visible .status-indicator {
  background-color: #34c759; /* 标准绿色 */
  box-shadow: 0 0 6rpx rgba(52, 199, 89, 0.5); /* 发光效果 */
  animation: breathe 3s ease-in-out infinite; /* 添加呼吸动画，3秒一个周期 */
}

/* 隐藏状态（深红色） */
.gallery-visibility.hidden .status-indicator {
  background-color: #ff3b30; /* 标准红色 */
  box-shadow: 0 0 6rpx rgba(255, 59, 48, 0.5); /* 发光效果 */
}

/* 呼吸动画关键帧 - 增强效果 */
@keyframes breathe {
  0% {
    transform: scale(1);
    opacity: 1;
    background-color: #2eb352; /* 深一点的绿色 */
    box-shadow: 0 0 6rpx rgba(52, 199, 89, 0.5);
  }
  50% {
    transform: scale(1.4); /* 放大更明显 */
    opacity: 0.7; /* 更透明 */
    background-color: #34c759; /* 标准绿色 */
    box-shadow: 0 0 12rpx rgba(52, 199, 89, 0.8); /* 发光效果增强 */
  }
  100% {
    transform: scale(1);
    opacity: 1;
    background-color: #2eb352; /* 深一点的绿色 */
    box-shadow: 0 0 6rpx rgba(52, 199, 89, 0.5);
  }
}

/* 操作按钮区域 */
.gallery-actions {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between; /* 均匀分布 */
  width: 100%; /* 确保占满整行 */
  position: absolute; /* 绝对定位 */
  bottom: 2rpx; /* 轻微调整底部位置 */
  left: 0;
  min-height: 48rpx; /* 确保按钮有足够的高度 */
}

/* 按钮样式 */
.action-button {
  padding: 0; /* 减小内边距 */
  border-radius: 4rpx; /* 标准按钮圆角 */
  font-size: 26rpx; /* 稍微增大字体 */
  text-align: center;
  transition: all 0.2s ease; /* 统一过渡时间 */
  height: 46rpx; /* 轻微减小高度 */
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
  flex: 1; /* 平均分配空间 */
  margin: 0 8rpx; /* 按钮之间的间距 */
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.05); /* 轻微阴影 */
}

/* 第一个按钮去除左边距 */
.action-button:first-child {
  margin-left: 0;
}

/* 最后一个按钮去除右边距 */
.action-button:last-child {
  margin-right: 0;
}

.action-button.edit {
  color: #ffffff; /* 白色文字 */
  background-color: #0070c9; /* 标准蓝色 */
  border: none;
}

.action-button.edit:active {
  background-color: #005ba3; /* 深蓝色，用于点击效果 */
  transform: scale(0.98);
}

.action-button.visibility {
  color: #ffffff; /* 白色文字 */
  background-color: #34c759; /* 标准绿色 */
  border: none;
}

.action-button.visibility:active {
  background-color: #2baa4a; /* 深绿色，用于点击效果 */
  transform: scale(0.98);
}

.action-button.delete {
  color: #ffffff; /* 白色文字 */
  background-color: #ff3b30; /* 标准红色 */
  border: none;
}

.action-button.delete:active {
  background-color: #d70015; /* 深红色，用于点击效果 */
  transform: scale(0.98);
}

/* 加载中样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid rgba(0, 112, 201, 0.1); /* 使用标准蓝色 */
  border-top-color: #0070c9; /* 标准蓝色 */
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #666666; /* 标准次要文字色 */
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-icon {
  width: 160rpx;
  height: 160rpx;
  margin-bottom: 30rpx;
  opacity: 0.6;
  font-size: 120rpx; /* Emoji大小 */
  text-align: center;
  line-height: 160rpx;
  color: #666666; /* 标准次要文字色 */
}

.empty-text {
  font-size: 30rpx;
  color: #666666; /* 标准次要文字色 */
  text-align: center;
  line-height: 1.5;
}

.back-icon {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  color: #333333;
}

.gallery-list {
  margin-bottom: 180rpx; /* 增加底部边距，为添加按钮留出足够空间 */
}

.empty-subtext {
  font-size: 28rpx;
  color: #999999; /* 标准浅色文字 */
  text-align: center;
}

/* 时间信息样式 */
.time-info {
  display: flex;
  align-items: center;
  margin-top: 10rpx;
  font-size: 24rpx;
  color: #999999; /* 标准浅色文字 */
  position: relative;
  z-index: 5;
}

.time-label {
  color: #999999; /* 标准浅色文字 */
  margin-right: 8rpx;
  font-weight: 500;
}

.time-value {
  color: #666666; /* 标准次要文字色 */
}

/* 调整状态指示器的样式 */
.gallery-visibility {
  display: flex;
  align-items: center;
}

/* 调整副标题的位置，腾出空间给时间信息 */
.gallery-subtitle {
  max-width: 65%; /* 减小宽度，避免与状态信息冲突 */
} 