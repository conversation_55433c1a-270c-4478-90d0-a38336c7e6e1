/* global Component, wx, console, setTimeout */

// 小程序组件定义
Component({
  properties: {
    visible: {
      type: Boolean,
      value: false,
      observer(newVal, oldVal) {
        // 只有当值真正变化时才触发
        if (newVal !== oldVal) {
          if (newVal) {
            this._showModal();
          } else {
            // 修复递归更新：不在observer中调用_hideModal()
            // 因为_hideModal()会再次触发setData，导致递归更新
            // 改为直接设置关闭状态，避免重复调用
            if (!this.data.isClosing) {
              this.setData({
                isClosing: true
              });
            }
          }
        }
      }
    },
    articleInfo: {
      type: Object,
      value: {},
      observer(newVal) {
        if (newVal) {
          this._updateArticleInfo(newVal);
        }
      }
    }
  },

  data: {
    coverUrl: '',
    firstImageUrl: '', // 用于存储第一张图片的URL
    showNavbar: true,
    capsuleHeight: 88, // 默认胶囊高度
    scrollTop: 0,  // 滚动位置
    scrollWithAnimation: true, // 控制滚动是否使用动画
    isClosing: false,
    showGalleryAnimation: false, // 控制弹窗动画状态
    loadingContent: false, // 是否正在加载富文本内容
    debugInfo: '', // 用于调试的信息
    showImagePreview: false, // 图片预览状态
    previewImageSrc: '', // 预览图片的src
    previewImageIndex: 0, // 预览图片索引
    imageList: [], // 文章中的所有图片列表
    // 添加缓存管理
    contentCache: {}, // 缓存的富文本内容
    lastRequestTime: {}, // 最后请求时间记录
    isViewingSource: false, // 是否在查看HTML源码
    touchStartY: null, // 触摸起始位置
    singleLayerExit: false, // 添加特殊标记，确保单层退出效果
    cosBaseUrl: '', // COS基础URL
    detailImages: [], // 存储画廊详情图片数组
    // 添加防重复调用标记
    isProcessingClose: false, // 防止重复关闭
  },

  lifetimes: {
    attached() {
      // 确保初始状态为不可见
      this.setData({
        visible: false
      });
      
      // 获取胶囊按钮信息
      this._getCapsuleInfo();
      
      // 初始化性能相关变量
      this.scrollTimer = null;
      this.moveTimer = null;
      this.animationTimer = null;
      this.imageLoadTimer = null;
      this.scrollBottomTimer = null;
      this.loadedImages = new Set();
      
      // 缓存滚动位置，避免频繁setData
      this.scrollTop = 0;
      this.touchStartY = null;
      this.touchStartX = null;
      this.touchStartTime = null;
    },
    
    detached() {
      // 清理所有计时器，避免内存泄漏
      if (this.scrollTimer) clearTimeout(this.scrollTimer);
      if (this.moveTimer) clearTimeout(this.moveTimer);
      if (this.animationTimer) clearTimeout(this.animationTimer);
      if (this.imageLoadTimer) clearTimeout(this.imageLoadTimer);
      if (this.scrollBottomTimer) clearTimeout(this.scrollBottomTimer);
      
      // 清理缓存的数据
      this.loadedImages = null;
    }
  },

  methods: {
    // 获取胶囊按钮位置信息
    _getCapsuleInfo() {
      try {
        // 使用新的API获取系统信息
        const windowInfo = wx.getWindowInfo();
        const menuButtonInfo = wx.getMenuButtonBoundingClientRect();
        
        // 计算胶囊按钮底部到屏幕顶部的距离
        const capsuleBottom = menuButtonInfo.bottom;
        
        // 计算合适的弹窗高度，使其顶部与胶囊底部平行
        const screenHeight = windowInfo.windowHeight;
        const modalHeight = screenHeight - capsuleBottom;
        const modalHeightPercentage = (modalHeight / screenHeight) * 100;
        
        // 更新弹窗样式
        const modalElement = wx.createSelectorQuery().select('.fullscreen-modal-content');
        if (modalElement) {
          this.updateModalHeight(modalHeightPercentage);
        } else {
          console.log('弹窗元素未找到，将使用默认高度');
        }
        
        console.log(`胶囊信息: 底部距离=${capsuleBottom}px, 屏幕高度=${screenHeight}px, 弹窗高度=${modalHeightPercentage}vh`);
      } catch (error) {
        console.error('获取胶囊信息失败:', error);
      }
    },
    
    // 动态更新弹窗高度
    updateModalHeight(heightVh) {
      // 约束高度，确保至少有60vh的显示空间
      const finalHeight = Math.max(heightVh, 60);
      
      // 获取窗口信息（使用新API）
      try {
        const windowInfo = wx.getWindowInfo();
        const modalHeightPx = (finalHeight / 100) * windowInfo.windowHeight;
        
        // 通过自定义样式方式修改弹窗高度
        wx.createSelectorQuery()
          .in(this)
          .select('.fullscreen-modal-content')
          .fields({ node: true, size: true }, () => {
            // 通过动态设置样式变量来修改弹窗高度
            // 注意：这里只能通过setData来修改样式类
            // 由于小程序限制，我们将高度值保存到data中
            this.setData({
              modalHeightStyle: `height: ${finalHeight}vh !important;`
            });
            console.log(`已设置弹窗高度: ${finalHeight}vh`);
          })
          .exec();
      } catch (error) {
        console.error('获取窗口信息失败:', error);
        // 使用默认高度
        this.setData({
          modalHeightStyle: `height: ${finalHeight}vh !important;`
        });
      }
    },

    // 接收文章信息并显示弹窗
    showModal(articleInfo) {
      if (!articleInfo) return;
      
      console.log('接收到文章信息:', articleInfo);
      
      // 再次获取胶囊按钮信息（确保正确高度）
      this._getCapsuleInfo();
      
      // 确保有ID
      if (!articleInfo.id && !articleInfo._id) {
        console.error('文章信息中缺少ID字段 无法获取详情内容');
        this.setData({ 
          debugInfo: '错误 文章信息中缺少ID字段'
        });
      }
      
      // 获取文章ID
      const articleId = articleInfo.id || articleInfo._id || '';
      

      
      // 处理detailImages数组
      const detailImages = articleInfo.detailImages || [];
      console.log('画廊详情图片数组:', detailImages);
      
      // 获取封面图
      const coverUrl = articleInfo.coverUrl || articleInfo.coverImage || '';
      
      this.setData({
        visible: true,
        articleInfo: articleInfo,
        // 移除标题相关字段，只保留必要的图片数据
        coverUrl: coverUrl,
        firstImageUrl: coverUrl, // 设置第一张图片为封面图
        // 设置详情图片数组
        detailImages: detailImages,
        // 设置图片列表用于预览
        imageList: detailImages.length > 0 ? detailImages : [coverUrl],
        // 显示调试信息
        debugInfo: `接收到文章：ID=${articleId || '无ID'}, 详情图片数量=${detailImages.length}`
      });
      
      // 简化：只触发一次导航栏隐藏事件
      console.log('[GalleryDetailModal] 触发导航栏隐藏事件');
      this.triggerEvent('navbarControl', { action: 'hide' });
    },
    
    // 更新文章信息
    _updateArticleInfo(articleInfo) {
      if (!articleInfo) return;
      
      // 获取封面图，作为默认的首图，并且设置为唯一的首图
      const coverUrl = articleInfo.coverUrl || articleInfo.coverImage || '';
      
      // 处理detailImages数组
      const detailImages = articleInfo.detailImages || [];
      
      this.setData({
        coverUrl: coverUrl,
        firstImageUrl: coverUrl,
        // 设置详情图片数组
        detailImages: detailImages,
        // 设置图片列表用于预览
        imageList: detailImages.length > 0 ? detailImages : [coverUrl]
      });
    },
    
    // 获取COS基础URL
    _getCosBaseUrl() {
      wx.cloud.callFunction({
        name: 'resourceManager',
        data: {
          action: 'getBaseUrl'
        },
        success: res => {
          if (res.result && res.result.code === 0) {
            this.setData({
              cosBaseUrl: res.result.data.baseUrl || ''
            });
            console.log('获取到COS基础URL:', this.data.cosBaseUrl);
          }
        },
        fail: err => {
          console.error('获取COS基础URL失败:', err);
        }
      });
    },

    // 格式化图片URL，确保使用HTTPS
    _formatImageUrl(imgSrc) {
      if (!imgSrc) return '';
      
      // 如果已经是HTTPS，直接返回
      if (imgSrc.startsWith('https://')) return imgSrc;
      
      // 如果是HTTP，转换为HTTPS
      if (imgSrc.startsWith('http://')) {
        return imgSrc.replace('http://', 'https://');
      }
      
      // 如果是相对路径，添加基础URL
      if (this.data.cosBaseUrl && !imgSrc.startsWith('/')) {
        return this.data.cosBaseUrl + imgSrc;
      }
      
      return imgSrc;
    },

    // 显示弹窗
    _showModal() {
      // 先显示弹窗（但保持在底部隐藏状态）
      this.setData({
        isClosing: false,
        visible: true
      });

      // 延时触发向上滑入动画
      setTimeout(() => {
        this.setData({
          showGalleryAnimation: true // 触发向上滑入动画
        });
      }, 50); // 短暂延时确保DOM已渲染
    },
    
    // 隐藏弹窗
    _hideModal() {
      // 设置关闭中状态
      this.setData({
        isClosing: true
      });
      
      // 等待动画完成后一次性重置所有状态
      setTimeout(() => {
        this.setData({
          visible: false,
          isClosing: false,
          showGalleryAnimation: false, // 重置动画状态
          scrollTop: 0,
          articleInfo: {},
          refreshing: false,
          imgList: []
        });
      }, 300); // 统一动画时间为300ms
    },
    
    // 关闭弹窗
    onClose() {
      // 防止重复关闭
      if (this.data.isProcessingClose) {
        console.log('[GalleryDetailModal] 正在处理关闭，忽略重复调用');
        return;
      }
      
      // 设置处理中标记
      this.setData({
        isProcessingClose: true
      });
      
      console.log('[GalleryDetailModal] 开始关闭弹窗');
      

      
      // 修复递归调用：直接设置visible为false，让observer处理关闭逻辑
      // 不要手动调用_hideModal()，避免重复调用
      this.setData({
        visible: false
      });
      
      // 简化：直接触发导航栏显示事件，不使用setTimeout
      console.log('[GalleryDetailModal] 触发导航栏显示事件');
      this.triggerEvent('navbarControl', { action: 'show' });
      
      // 触发关闭事件
      this.triggerEvent('close');
      
      // 延迟重置处理标记，防止过快的重复调用
      setTimeout(() => {
        this.setData({
          isProcessingClose: false
        });
      }, 1000);
    },
    
    // 滚动到顶部
    scrollToTop() {
      this.setData({
        scrollTop: 0,
        scrollWithAnimation: true
      });
    },

    // 控制导航栏显示/隐藏
    onNavbarControl(e) {
      const action = e.detail.action;
      if (action === 'show' || action === 'hide') {
        // 简化：直接更新状态，不使用setTimeout
        this.setData({ showNavbar: action === 'show' });
      }
    },

    // 处理滚动事件
    onScrollEvent(e) {
      // 获取滚动位置
      const scrollTop = e.detail.scrollTop;
      
      // 使用节流技术，避免频繁更新数据
      if (this.scrollTimer) {
        clearTimeout(this.scrollTimer);
      }
      
      // 立即更新滚动位置，但其他操作延迟执行
      this.scrollTop = scrollTop; // 直接存储在实例属性中，避免频繁setData
      
      this.scrollTimer = setTimeout(() => {
        // 只在必要时更新data中的scrollTop
        if (Math.abs(this.data.scrollTop - scrollTop) > 10) {
          this.setData({
            scrollTop: scrollTop
          });
        }
        
        // 可以在这里添加其他滚动事件处理逻辑
        // 比如根据滚动位置控制导航栏显示/隐藏等
      }, 100); // 100ms的节流延迟
    },
    
    // 添加触摸开始事件处理
    onTouchStart(e) {
      if (e.touches.length === 1) {
        // 记录触摸起始位置，直接存储在实例属性中，避免频繁setData
        this.touchStartY = e.touches[0].clientY;
        this.touchStartX = e.touches[0].clientX;
        this.touchStartTime = Date.now();
        
        // 只在必要时更新到data
        if (this.data.touchStartY === null) {
          this.setData({
            touchStartY: this.touchStartY
          });
        }
      }
    },

    // 优化触摸移动事件
    onTouchMove(e) {
      // 已有代码逻辑保持不变
      if (!e || !e.touches || e.touches.length === 0) return;
      
      const touch = e.touches[0];
      const currentY = touch.clientY;
      const scrollTop = this.scrollTop || 0; // 使用实例属性，避免频繁读取data
      
      // 如果没有记录起始位置，则记录
      if (!this.touchStartY) {
        this.touchStartY = currentY;
        return;
      }
      
      // 计算滑动距离
      const deltaY = currentY - this.touchStartY;
      
      // 处理向下拉动关闭弹窗逻辑
      if (scrollTop <= 0 && deltaY > 80) {
        // 添加阻尼效果，使下拉更流畅
        const dampenedDelta = Math.sqrt(deltaY) * 10;
        
        // 使用节流技术，避免频繁更新数据
        if (this.moveTimer) {
          clearTimeout(this.moveTimer);
        }
        
        this.moveTimer = setTimeout(() => {
          // 禁用scroll-with-animation以避免冲突
          if (this.data.scrollWithAnimation) {
            this.setData({ 
              scrollWithAnimation: false 
            });
          }
        }, 50);
        
        // 如果下拉超过阈值，关闭弹窗
        if (dampenedDelta > 100) {
          // 防止重复调用：检查是否已经在处理关闭
          if (!this.data.isProcessingClose) {
            this.onClose();
          }
        }
      } else {
        // 正常滚动时启用动画，使用节流
        if (!this.data.scrollWithAnimation && !this.animationTimer) {
          this.animationTimer = setTimeout(() => {
            this.setData({ scrollWithAnimation: true });
            this.animationTimer = null;
          }, 100);
        }
      }
    },
    
    // 优化触摸结束事件
    onTouchEnd() {
      // 清除所有计时器
      if (this.moveTimer) {
        clearTimeout(this.moveTimer);
        this.moveTimer = null;
      }
      
      if (this.animationTimer) {
        clearTimeout(this.animationTimer);
        this.animationTimer = null;
      }
      
      // 恢复默认滚动行为
      this.touchStartY = null;
      this.touchStartX = null;
      this.touchStartTime = null;
      
      // 只在必要时更新data
      if (this.data.touchStartY !== null || !this.data.scrollWithAnimation) {
        this.setData({
          touchStartY: null,
          scrollWithAnimation: true
        });
      }
    },
    
    // 图片加载错误处理
    onImageError(e) {
      console.error('图片加载失败:', e);
      // 如果是顶部图片加载失败，清除图片URL
      const src = e.currentTarget.dataset.src || '';
      if (src === this.data.firstImageUrl) {
        this.setData({
          firstImageUrl: ''
        });
      }
    },
    
    // 图片加载完成处理
    onImageLoad(e) {
      // 获取图片索引
      const index = e.currentTarget.dataset.index;
      
      // 如果有需要，可以在这里添加图片加载完成后的处理逻辑
      // 例如：更新加载状态、优化渲染等
      
      // 使用节流，避免频繁触发渲染
      if (this.imageLoadTimer) {
        clearTimeout(this.imageLoadTimer);
      }
      
      this.imageLoadTimer = setTimeout(() => {
        // 这里可以添加图片加载完成后的逻辑
        // 例如：更新加载状态、触发渲染优化等
        
        // 记录已加载的图片数量，可用于显示加载进度
        if (!this.loadedImages) {
          this.loadedImages = new Set();
        }
        
        if (index !== undefined) {
          this.loadedImages.add(index);
          
          // 如果所有图片都已加载完成，可以执行一些操作
          if (this.loadedImages.size === this.data.detailImages.length) {
            console.log('所有图片加载完成');
            // 这里可以添加所有图片加载完成后的逻辑
          }
        }
      }, 100);
    },
    
    // 处理滚动到底部事件
    onScrollToBottom() {
      // 滚动到底部时，可以在这里添加逻辑
      console.log('滚动到底部');
      
      // 如果需要，可以禁用下拉手势，防止用户在底部继续下拉时触发关闭
      if (this.data.touchStartY !== null) {
        this.setData({
          touchStartY: null
        });
      }
      
      // 防止频繁触发，添加节流处理
      if (this.scrollBottomTimer) {
        clearTimeout(this.scrollBottomTimer);
      }
      
      this.scrollBottomTimer = setTimeout(() => {
        // 移除振动反馈，避免影响滚动性能
        // wx.vibrateShort({
        //   type: 'light' // 轻微振动
        // }).catch(err => {
        //   console.log('振动API不可用', err);
        // });
        
        // 这里可以添加加载更多内容的逻辑
      }, 300);
    },
    
    // 刷新内容
    refreshContent() {
      // 触发短振动
      wx.vibrateShort({
        type: 'medium' // 适中的振动强度
      });
      
      // 重新加载文章详情数据
      if (this.data.id) {
        this.setData({ loadingContent: true });
        
        // 必须先滚动到顶部，确保用户看到加载状态
        this.setData({ scrollTop: 0 });
        
        // 然后再加载内容
        setTimeout(() => {
          this.loadArticleDetail(this.data.id, this.data.originalId);
        }, 100);
      }
    },
    
    // 切换源码/正常视图
    toggleViewSource() {
      this.setData({
        isViewingSource: !this.data.isViewingSource
      });
    },
    
    // 图片点击处理 - 支持预览detailImages
    onImageTap(e) {
      const src = e.currentTarget.dataset.src;
      if (!src) return;
      
      console.log('点击图片:', src);
      
      // 获取所有可预览的图片
      let imageList = this.data.imageList;
      
      // 如果有detailImages，优先使用它们
      if (this.data.detailImages && this.data.detailImages.length > 0) {
        imageList = this.data.detailImages;
      }
      
      // 如果图片列表为空，至少包含当前点击的图片
      if (!imageList || imageList.length === 0) {
        imageList = [src];
      }
      
      // 查找当前图片在列表中的索引
      let currentIndex = 0;
      for (let i = 0; i < imageList.length; i++) {
        if (imageList[i] === src) {
          currentIndex = i;
          break;
        }
      }
      
      // 使用微信预览图片API
      wx.previewImage({
        current: src,
        urls: imageList,
        showmenu: true,
        success: () => {
          console.log('图片预览成功');
        },
        fail: (error) => {
          console.error('图片预览失败:', error);
          wx.showToast({
            title: '图片预览失败',
            icon: 'none'
          });
        }
      });
    },




  }
}); 