/**
 * video-card.wxss - 视频卡片组件样式
 * 
 * 本文件定义了视频卡片的所有样式，使用CSS变量确保可以通过app.wxss进行全局配置
 * 修改样式时请尽量使用变量，而不是直接修改属性值
 */

/* =============================================
 * 1. 组件根元素与变量定义
 * ============================================= */
.video-card-root:first-child {
  margin: 0; /* 确保第一个卡片也没有边距 */
}

.video-card-root {
  /* 容器背景颜色 - 从全局变量映射，格式: CSS变量名: (引用全局变量, 默认值) */
  --container-main-title-bg: var(--videocard-main-title-bg, rgba(0, 0, 0, 0.6));     /* 标题容器背景色 */
  --container-main-title-gradient: var(--videocard-main-title-gradient, linear-gradient(to top, rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0))); /* 标题容器渐变 */
  --container-subtitle-bg: var(--videocard-subtitle-bg, rgba(0, 0, 0, 0.6));         /* 副标题容器背景色 */
  --container-subtitle-gradient: var(--videocard-subtitle-gradient, linear-gradient(to top, rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0))); /* 副标题容器渐变 */
  --container-avatar-bg: var(--videocard-avatar-bg, rgba(100, 100, 100, 0.5));       /* 头像容器背景色 */
  --container-tag-bg: var(--videocard-tag-bg, rgba(255, 255, 255, 0.36));            /* 标签容器背景色 */
  --container-play-count-bg: var(--videocard-play-count-bg, rgba(255, 255, 255, 0.36)); /* 播放量容器背景色 */
  --container-video-bg: var(--videocard-video-bg, #000000);                          /* 视频容器背景色 */
  --container-cover-bg: var(--videocard-cover-bg, #000000);                          /* 封面图背景色 */
  --container-decoration-bg: var(--videocard-decoration-bg, rgba(200, 50, 50, 0.7)); /* 装饰图标容器背景色 */
  --container-gradient: var(--videocard-gradient, linear-gradient(to top, rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.3))); /* 卡片渐变背景 */
  
  /* 文字颜色 */
  --text-play-count: var(--videocard-play-count-text, rgba(0, 0, 0, 0.85));  /* 播放量文字颜色 */
  --text-time: var(--videocard-time-text, rgba(0, 0, 0, 0.7));               /* 时间文字颜色 */
  --text-tag: var(--videocard-tag-text, rgba(0, 0, 0, 0.85));                /* 标签文字颜色 */
  --text-title: var(--videocard-title, #ffffff);                             /* 标题文字颜色 */
  --text-subtitle: var(--videocard-subtitle, rgba(255, 255, 255, 0.7));      /* 副标题文字颜色 */
  
  /* 边框和圆角 */
  --video-container-radius: 0; /* 视频容器无圆角，完全直角 */
  --play-count-radius: var(--videocard-play-count-radius, 12rpx);                   /* 播放量容器圆角 */
  --tag-radius: var(--videocard-tag-radius, 12rpx);                                 /* 标签容器圆角 */
  
  /* 动画效果 */
  --decoration-animation-duration: var(--videocard-decoration-animation-duration, 1.2s);   /* 装饰图标动画持续时间 */
  --decoration-animation-timing: var(--videocard-decoration-animation-timing, infinite ease-in); /* 装饰图标动画函数 */
  --decoration-size: var(--videocard-decoration-size, 69rpx);                       /* 装饰图标容器大小 */
  --decoration-icon-size: var(--videocard-decoration-icon-size, 41rpx);             /* 装饰图标内部大小 */
  --logo-size: var(--videocard-logo-size, 66rpx);                                   /* 头像Logo大小 */
  
  /* 卡片尺寸与边距 */
  --card-width: 678rpx;       /* 卡片宽度 */
  --card-height: auto;        /* 卡片高度自适应 */
  --card-margin: 0;           /* 卡片外边距 */
  --video-height: 678rpx;     /* 视频高度与宽度相同，保持1:1比例 */
  --content-height: 160rpx;   /* 底部内容区域高度 */
  --icon-size: 60rpx;         /* 图标大小 */
  --avatar-size: 86rpx;       /* 头像大小 */
  --icon-margin: 15rpx;       /* 图标边距 */
  
  /* 基础样式 */
  margin: 0;                  /* 确保所有方向没有间距 */
  padding: 0;                 /* 确保没有内边距 */
  transition: transform 0.2s ease-out; /* 添加滑动过渡效果 */
}

/* =============================================
 * 2. 交互状态样式
 * ============================================= */

/* 触摸缩放效果 */
.video-card-root.touched {
  transform: scale(var(--videocard-scale-amount)); /* 使用变量控制缩放大小 */
}

/* 向上滑动效果 */
.video-card-root.moving-up {
  transform: translateY(-4rpx) scale(var(--videocard-scale-amount)); /* 向上移动并缩放 */
}

/* 向下滑动效果 */
.video-card-root.moving-down {
  transform: translateY(4rpx) scale(var(--videocard-scale-amount)); /* 向下移动并缩放 */
}

/* 释放后恢复效果 */
.video-card-root.released {
  transform: scale(1);
  transition: transform 0.3s ease-out; /* 释放时稍慢的恢复效果 */
}

/* =============================================
 * 3. 卡片基础布局
 * ============================================= */

/* 卡片主容器 */
.video-card {
  position: relative;
  width: var(--card-width);
  height: auto; /* 高度自适应 */
  margin: 0;
  padding: 0;
  border-radius: 0 0 40rpx 40rpx; /* 只保留底部圆角，顶部为直角 */
  background: var(--videocard-bg);
  background-image: var(--container-gradient); /* 使用渐变背景 */
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  border: none;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx var(--videocard-shadow); /* 使用变量控制阴影 */
  z-index: 1;
  display: flex;
  flex-direction: column;
}

/* 点击状态效果 */
.video-card:active {
  box-shadow: 0 6rpx 16rpx var(--videocard-shadow); /* 点击状态阴影效果 */
  transform: none;
}

/* =============================================
 * 4. 视频及封面相关样式
 * ============================================= */

/* 视频容器 */
.video-container {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 100%;  /* 1:1宽高比 (正方形) */
  overflow: hidden;
  background-color: var(--container-video-bg);
  border-radius: 0; /* 确保直角 */
}

/* 绝对定位填充样式，用于视频和封面图 */
.absolute-fill {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center; /* 确保截取中心部分 */
  backface-visibility: hidden;
  border-radius: 0; /* 确保直角 */
}

/* 视频元素样式 */
.video {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover; /* cover会保持宽高比并填充整个容器，裁剪超出部分 */
  object-position: center; /* 确保截取中心部分 */
  opacity: 1;
  z-index: 1;
  transform: translateZ(0);
  will-change: transform;
  background-color: var(--container-video-bg);
  border-radius: 0; /* 确保直角 */
}

/* 封面图片样式 */
.cover-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover; /* 保持宽高比并填充整个容器 */
  object-position: center; /* 确保截取中心部分 */
  background-color: var(--container-cover-bg);
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 1;
  z-index: 3;
  transform: translateZ(0);
  will-change: opacity;
  border-radius: 0; /* 确保直角 */
}

/* 隐藏元素样式 */
.video.hidden, 
.cover-image.hidden, 
.play-icon-container.hidden, 
.loading-indicator.hidden {
  display: none !important;
}

/* 视频控制层样式 */
.video-control-layer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2; /* 确保在视频之上，但在其他控制按钮之下 */
  border-radius: 0; /* 确保直角 */
}

/* 防止内置控件显示的覆盖层 */
.video-container::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
  pointer-events: none;
}

/* 封面层样式 */
.cover-layer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 3;
  background-color: transparent;
  border-radius: 0; /* 确保直角 */
}

/* =============================================
 * 5. 播放图标样式
 * ============================================= */

/* 播放图标容器 */
.play-icon-container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 5;
  background-color: transparent;
}

/* 播放图标圆圈 */
.play-icon-circle {
  width: 80rpx;
  height: 80rpx;
  background-color: var(--videocard-play-icon-bg);
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 播放图标三角形 */
.play-icon-triangle {
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 15rpx 0 15rpx 30rpx;
  border-color: transparent transparent transparent var(--videocard-play-icon);
  margin-left: 5rpx;
}

/* =============================================
 * 6. 控制按钮样式
 * ============================================= */

/* 控制按钮区域 */
.control-buttons {
  position: absolute;
  top: 16rpx;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-between;
  padding: 0 20rpx;
}

/* 静音按钮样式 */
.mute-button {
  position: absolute;
  top: calc(var(--icon-margin));
  right: calc(var(--icon-margin));
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: transparent;
  border-radius: 50%;
  z-index: 20;
}

/* 静音图标样式 */
.mute-icon {
  width: calc(var(--icon-size) * 0.9);
  height: calc(var(--icon-size) * 0.9);
  opacity: 0.6;
  filter: brightness(0) invert(1);
}

/* 静音状态图标样式 */
.muted .mute-icon {
  opacity: 0.6;
  filter: brightness(0) invert(1);
}

/* 非静音状态图标样式 */
.unmuted .mute-icon {
  opacity: 0.3;
  filter: brightness(0) invert(1);
}

/* 分享按钮样式 - 已隐藏 */
.share-btn {
  display: none !important;
  visibility: hidden !important;
  padding: 0;
  margin: 0;
  background: none;
  border: none;
  line-height: 1;
  position: absolute;
  right: -80rpx;
  bottom: 20rpx;
  width: 13.2rpx;
  height: 66rpx;
  background-color: transparent;
  border-radius: 24rpx;
  z-index: 20;
}

/* 分享图标样式 - 已隐藏 */
.share-icon {
  display: none !important;
  visibility: hidden !important;
  width: 50rpx;
  height: 50rpx;
  opacity: 0;
}

/* 分享按钮伪元素清除 */
.share-btn::after {
  display: none;
}

/* 分享按钮点击状态 */
.share-btn:active {
  background: none;
}

/* =============================================
 * 7. 标题容器样式
 * ============================================= */

/* 内容区域容器 */
.content-container {
  position: relative;
  width: 100%;
  height: var(--content-height);
  background-color: #ffffff;
  padding: 15rpx 20rpx 25rpx 20rpx; /* 增加底部内边距，确保有足够空间显示所有元素 */
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}

/* 主标题容器 */
.main-title-container {
  position: relative;
  left: 0;
  top: 0;
  margin-left: calc(var(--logo-size) + 20rpx);
  margin-top: 15rpx;
  width: 60%; /* 增加宽度 */
  height: 32rpx;
  z-index: 10;
  background-color: transparent;
  border-radius: 0;
  padding: 0;
}

/* 视频标题文本 */
.video-title {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  color: #000000 !important; /* 修改为黑色，符合Instagram风格 */
  font-weight: bold;
  font-size: 26rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  background-color: transparent;
}

/* =============================================
 * 8. 副标题容器样式
 * ============================================= */

/* 副标题容器 */
.subtitle-container {
  position: relative;
  width: 70%;
  height: 32rpx;
  margin-left: calc(var(--logo-size) + 20rpx);
  margin-top: 10rpx;
  z-index: 10;
  display: flex;
  align-items: center;
  background-color: transparent;
  border-radius: 0;
  padding: 0;
}

/* 视频副标题文本 */
.video-subtitle {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  color: #666666 !important; /* 修改为灰色，符合Instagram风格 */
  font-size: 22rpx !important;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  background-color: transparent;
}

/* =============================================
 * 9. 头像容器样式
 * ============================================= */

/* 头像容器 */
.avatar-container {
  position: absolute;
  width: var(--logo-size);
  height: var(--logo-size);
  left: 20rpx;
  top: 28rpx; /* 从43rpx减少到28rpx，向上移动15rpx */
  z-index: 10;
  background-color: var(--container-avatar-bg);
  border-radius: 50%;
  overflow: visible;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rpx;
}

/* 头像图片 */
.avatar {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background-color: #f0f0f0;
}

/* =============================================
 * 10. 标签容器样式
 * ============================================= */

/* 标签容器 */
.tag-container {
  position: absolute;
  left: calc(var(--logo-size) + 20rpx); /* 与副标题左对齐 */
  top: 120rpx; /* 从110rpx增加到120rpx，向下移动10rpx */
  height: calc(32rpx * 0.8);
  width: auto;
  background-color: transparent;
  border-radius: 0;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding: 0;
}

/* 标签图标 - 已隐藏 */
.tag-icon {
  width: 24rpx;
  height: 24rpx;
  flex-shrink: 0;
  opacity: 0.9;
  display: none;
}

/* 分类标签文本 */
.category-tag {
  font-size: 20rpx !important;
  color: #999999 !important; /* 修改为浅灰色，符合Instagram风格 */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: left; /* 左对齐文本 */
  display: block;
  margin-left: 20rpx; /* 向右移动20rpx */
  line-height: 1; /* 重置行高 */
  vertical-align: middle; /* 垂直居中 */
}

/* =============================================
 * 11. 播放量容器样式
 * ============================================= */

/* 播放量容器 */
.play-count-container {
  position: absolute;
  right: 355rpx; /* 从370rpx减少到355rpx，将容器右移15rpx */
  top: 120rpx; /* 从110rpx增加到120rpx，向下移动10rpx */
  height: calc(32rpx * 0.8);
  width: auto;
  background-color: transparent;
  border-radius: 0;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding: 0;
}

/* 播放量图标 - 已隐藏 */
.view-count-icon {
  width: 24rpx;
  height: 24rpx;
  flex-shrink: 0;
  opacity: 0.9;
  margin-right: 4rpx;
  display: none;
}

/* 播放量文本 */
.play-count-text {
  font-size: 20rpx !important;
  color: #999999 !important; /* 修改为浅灰色，符合Instagram风格 */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: right;
  line-height: 1; /* 重置行高 */
  vertical-align: middle; /* 垂直居中 */
}

/* 时间分隔符 */
.time-separator {
  font-size: 20rpx !important;
  color: var(--text-time) !important;
  margin: 0 2rpx;
  opacity: 0.8;
}

/* 时间文本 */
.time-text {
  font-size: 20rpx !important;
  color: var(--text-time) !important;
  white-space: nowrap;
  opacity: 0.8;
}

/* =============================================
 * 12. 装饰图标样式 - 已移除
 * ============================================= */

/* 装饰图标脉动动画 - 已移除 */
/* @keyframes pulseScale {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
} */

/* 装饰图标容器 - 已移除 */
/* .decoration-1 {
  position: absolute;
  width: calc(var(--decoration-size) * 1.35);
  height: calc(var(--decoration-size) * 1.35);
  right: 5rpx;
  top: calc(78% + 13rpx + (var(--logo-size) - var(--decoration-size) * 1.35) / 2 + 5rpx);
  z-index: 10;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: var(--container-decoration-bg);
  border-radius: 4rpx;
  animation: pulseScale var(--decoration-animation-duration) var(--decoration-animation-timing);
  will-change: transform;
  transform-origin: center;
} */

/* 图标图片 - 已移除 */
/* .icon-image {
  width: calc(var(--decoration-icon-size) * 1.35);
  height: calc(var(--decoration-icon-size) * 1.35);
  filter: brightness(0) invert(1);
} */

/* 装饰图标2 - 已隐藏 */
.decoration-2 {
  display: none;
}

/* =============================================
 * 13. 加载指示器样式
 * ============================================= */

/* 加载指示器容器 */
.loading-indicator {
  position: absolute;
  right: 10rpx;
  bottom: 10rpx;
  z-index: 5;
  opacity: 0.5;
}

/* 加载指示器点 */
.loading-dot {
  width: 10rpx;
  height: 10rpx;
  background-color: #fff;
  border-radius: 50%;
}

/* 加载动画器 */
.loading-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  border-top-color: #fff;
  border-radius: 50%;
}

/* 加载动画 */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* =============================================
 * 14. 视频加载动画样式
 * ============================================= */

/* 视频加载动画容器 */
.video-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.6);
  border-radius: 0; /* 改为直角 */
  padding: 20rpx 30rpx;
  z-index: 10;
}

/* 加载旋转圆圈 */
.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.2);
  border-top: 4rpx solid #ffffff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

/* 加载文字 */
.loading-text {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
}

/* 旋转动画 */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* =============================================
 * 15. 瀑布流容器样式
 * ============================================= */

/* 瀑布流容器 */
.waterfall-container {
  column-count: 2;
  column-gap: 0;
  padding: 0;
}

/* 响应式布局 - 大屏幕 */
@media screen and (min-width: 768px) {
  .waterfall-container {
    column-count: 3;
  }
}

/* 响应式布局 - 小屏幕 */
@media screen and (max-width: 480px) {
  .waterfall-container {
    column-count: 1;
  }
}

/* =============================================
 * 16. 通用辅助类
 * ============================================= */

/* 通用卡片视频 */
.card-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center; /* 确保截取中心部分 */
}

/* 通用卡片图片 */
.card-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center; /* 确保截取中心部分 */
}

/* 视频卡片容器 */
.video-card-container {
  position: relative;
  width: 100%;
  border-radius: 0; /* 确保直角 */
  overflow: hidden;
  margin: 0;
}

/* 视频卡片媒体 */
.video-card-media {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center; /* 确保截取中心部分 */
  background-color: #000;
  border-radius: 0; /* 确保直角 */
}

/* 视频卡片播放图标 */
.video-card-play-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80rpx;
  height: 80rpx;
}

/* 视频元素 */
.video-element {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center; /* 确保截取中心部分 */
  border-radius: 0; /* 确保直角 */
}

/* Z-index 统一管理 */
.avatar-container, 
.main-title-container, 
.subtitle-container, 
.play-count-container,
.tag-container,
.share-btn,
.detail-button-container,
.mute-button {
  z-index: 20; /* 确保这些元素高于视频控制层 */
}

/* 详情按钮容器 */
.detail-button-container {
  position: absolute;
  right: 20rpx; /* 修改为分享按钮原来的位置 */
  /* 计算头像容器的中心位置: 头像top值(28rpx) + 半个头像高度(var(--logo-size)/2) - 半个详情按钮高度(60rpx/2) - 20rpx向上偏移 */
  top: calc(28rpx + (var(--logo-size) / 2) - 30rpx - 20rpx); /* 向上移动20rpx (原来的15rpx + 新增的5rpx) */
  height: 60rpx; /* 与分享按钮大小一致 */
  width: 60rpx; /* 与分享按钮大小一致 */
  background-color: transparent; 
  z-index: 30; /* 提高z-index，确保高于分享按钮 */
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 0;
  pointer-events: auto; /* 确保可以接收点击事件 */
}

/* 详情图标样式 */
.detail-icon {
  width: 100%;
  height: 100%;
  object-fit: contain;
  pointer-events: auto; /* 确保图标可以接收点击事件 */
}

/* 分享按钮容器 */
.share-button-container {
  position: absolute;
  right: 100rpx; /* 修改为详情按钮原来的位置 */
  /* 计算头像容器的中心位置: 头像top值(28rpx) + 半个头像高度(var(--logo-size)/2) - 半个分享按钮高度(60rpx/2) - 20rpx向上偏移 */
  top: calc(28rpx + (var(--logo-size) / 2) - 30rpx - 20rpx); /* 向上移动20rpx (原来的15rpx + 新增的5rpx) */
  height: 60rpx; /* 从50rpx增加到60rpx，增大20% */
  width: 60rpx; /* 从50rpx增加到60rpx，增大20% */
  background-color: transparent; 
  z-index: 25; /* 低于详情按钮，但高于其他元素 */
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 0;
} 
/* =
============================================
 * 分享图标放大样式
 * ============================================= */

/* 分享按钮放大32% */
.share-button-container.enlarged-share {
  transform: scale(1.32); /* 在15%基础上再加大15%（约132%） */
  transform-origin: center; /* 从中心点缩放 */
}