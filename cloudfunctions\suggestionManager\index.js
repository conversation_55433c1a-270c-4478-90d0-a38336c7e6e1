// 投诉建议管理云函数
const cloud = require("wx-server-sdk");
cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV });
const db = cloud.database();
const _ = db.command;

// 生成唯一ID
function generateId() {
  return Date.now().toString(36) + Math.random().toString(36).substring(2);
}

// 确保必要的集合存在 - 参考pointsManager的方法
async function ensureCollectionsExist() {
  console.log("检查并创建必要的集合");

  try {
    // 获取所有集合
    const collections = await db.listCollections().get();
    const collectionNames = collections.data.map(
      (collection) => collection.name
    );
    console.log("现有集合:", collectionNames);

    // 需要确保存在的集合
    const requiredCollections = ["suggestions"];

    // 创建不存在的集合
    for (const collectionName of requiredCollections) {
      if (!collectionNames.includes(collectionName)) {
        try {
          console.log(`创建集合: ${collectionName}`);
          await db.createCollection(collectionName);
          console.log(`成功创建集合: ${collectionName}`);
        } catch (createErr) {
          console.error(`创建集合 ${collectionName} 失败:`, createErr);
        }
      } else {
        console.log(`集合 ${collectionName} 已存在`);
      }
    }
  } catch (listErr) {
    console.error("获取集合列表失败:", listErr);

    // 如果获取集合列表失败，直接尝试创建suggestions集合
    try {
      await db.createCollection("suggestions");
      console.log("成功创建suggestions集合");
    } catch (err) {
      console.error("创建suggestions集合失败:", err);
    }
  }
}

// 云函数入口函数
exports.main = async (event, context) => {
  const { action, data } = event;
  const wxContext = cloud.getWXContext();

  console.log("投诉建议云函数调用:", { action, openid: wxContext.OPENID });

  // 确保必要的集合存在
  await ensureCollectionsExist();

  try {
    switch (action) {
      case "submitSuggestion":
        return await submitSuggestion(data, wxContext);
      case "getConversations":
        return await getConversations(wxContext);
      case "sendReply":
        return await sendReply(data, wxContext);
      case "getAllSuggestions":
        return await getAllSuggestions(data);
      case "adminReply":
        return await adminReply(data);
      case "updateSuggestionStatus":
        return await updateSuggestionStatus(data);
      case "getUserConversations":
        return await getUserConversations(data);
      case "markUserMessagesRead":
        return await markUserMessagesRead(data);
      default:
        return {
          code: 400,
          message: "未知操作类型",
        };
    }
  } catch (err) {
    console.error("云函数执行失败:", err);
    return {
      code: 500,
      message: "服务器错误: " + err.message,
    };
  }
};

// 提交建议 - 参考appointmentManager的简洁方法
async function submitSuggestion(data, wxContext) {
  const { content, contactPhone, isAnonymous, images, videos, userInfo } = data;
  const openid = wxContext.OPENID;

  console.log("云函数收到提交建议请求:", {
    content,
    contactPhone,
    isAnonymous,
    openid,
  });

  if (!content || !content.trim()) {
    return {
      code: 400,
      message: "建议内容不能为空",
    };
  }

  if (!openid) {
    return {
      code: 401,
      message: "用户未登录",
    };
  }

  try {
    // 创建建议记录
    const suggestionId = generateId();
    const suggestionData = {
      suggestionId: suggestionId,
      userId: openid,
      userInfo: isAnonymous
        ? null
        : {
            nickName: userInfo?.nickName || "用户",
            avatarUrl: userInfo?.avatarUrl || "",
          },
      content: content.trim(),
      contactPhone: isAnonymous ? "" : contactPhone || "",
      isAnonymous,
      images: images || [],
      videos: videos || [],
      status: "pending", // pending: 待处理, replied: 已回复, closed: 已关闭
      createTime: db.serverDate(),
      updateTime: db.serverDate(),

      // 对话记录
      conversations: [
        {
          id: generateId(),
          sender: "user",
          senderName: isAnonymous ? "匿名用户" : userInfo?.nickName || "用户",
          content: content.trim(),
          images: images || [],
          videos: videos || [],
          createTime: db.serverDate(),
        },
      ],
    };

    console.log("准备保存建议数据:", suggestionData);

    // 保存到数据库 - 参考appointmentManager的方法
    const result = await db.collection("suggestions").add({
      data: suggestionData,
    });

    console.log("建议提交成功:", suggestionId, result);

    return {
      code: 0,
      message: "建议提交成功",
      data: {
        suggestionId,
        _id: result._id,
      },
    };
  } catch (err) {
    console.error("提交建议失败:", err);
    return {
      code: 500,
      message: "提交失败: " + err.message,
    };
  }
}

// 获取用户的对话记录
async function getConversations(wxContext) {
  const openid = wxContext.OPENID;

  try {
    // 获取用户的所有建议记录
    const result = await db
      .collection("suggestions")
      .where({
        userId: openid,
      })
      .orderBy("updateTime", "desc")
      .get();

    // 合并所有对话记录
    let allConversations = [];

    result.data.forEach((suggestion) => {
      if (suggestion.conversations && suggestion.conversations.length > 0) {
        // 为每个对话添加建议ID，便于后续回复
        const conversations = suggestion.conversations.map((conv) => ({
          ...conv,
          suggestionId: suggestion._id,
          suggestionTitle: suggestion.content.substring(0, 20) + "...",
        }));
        allConversations = allConversations.concat(conversations);
      }
    });

    // 按时间排序
    allConversations.sort((a, b) => {
      const timeA = new Date(a.createTime).getTime();
      const timeB = new Date(b.createTime).getTime();
      return timeA - timeB;
    });

    return {
      code: 0,
      message: "获取对话记录成功",
      data: allConversations,
    };
  } catch (err) {
    console.error("获取对话记录失败:", err);
    return {
      code: 500,
      message: "获取失败: " + err.message,
    };
  }
}

// 用户发送回复
async function sendReply(data, wxContext) {
  const { content, userInfo, suggestionId } = data;
  const openid = wxContext.OPENID;

  if (!content || !content.trim()) {
    return {
      code: 400,
      message: "回复内容不能为空",
    };
  }

  try {
    let suggestion;

    if (suggestionId) {
      // 如果指定了建议ID，直接查找该建议
      const result = await db.collection("suggestions").doc(suggestionId).get();
      if (result.data && result.data.userId === openid) {
        suggestion = result.data;
        suggestion._id = suggestionId;
      }
    } else {
      // 获取用户最新的建议记录
      const result = await db
        .collection("suggestions")
        .where({
          userId: openid,
        })
        .orderBy("updateTime", "desc")
        .limit(1)
        .get();

      if (result.data.length > 0) {
        suggestion = result.data[0];
      }
    }

    if (!suggestion) {
      return {
        code: 404,
        message: "未找到相关建议记录",
      };
    }

    // 创建新的对话记录
    const newConversation = {
      id: generateId(),
      sender: "user",
      senderName: userInfo?.nickName || "用户",
      content: content.trim(),
      images: [],
      videos: [],
      createTime: db.serverDate(),
    };

    // 更新建议记录
    await db
      .collection("suggestions")
      .doc(suggestion._id)
      .update({
        data: {
          conversations: _.push(newConversation),
          status: "user_replied",
          updateTime: db.serverDate(),
        },
      });

    return {
      code: 0,
      message: "回复发送成功",
    };
  } catch (err) {
    console.error("发送回复失败:", err);
    return {
      code: 500,
      message: "发送失败: " + err.message,
    };
  }
}

// 管理员获取所有建议
async function getAllSuggestions(data) {
  const { status, page = 1, pageSize = 20, groupByUser = false } = data || {};

  try {
    let query = db.collection("suggestions");

    // 根据状态筛选
    if (status) {
      query = query.where({
        status: status,
      });
    }

    // 分页查询
    const result = await query
      .orderBy("updateTime", "desc")
      .skip((page - 1) * pageSize)
      .limit(pageSize)
      .get();

    // 获取总数
    const countResult = await query.count();

    return {
      code: 0,
      message: "获取建议列表成功",
      data: result.data,
      total: countResult.total,
      page,
      pageSize,
    };
  } catch (err) {
    console.error("获取建议列表失败:", err);
    return {
      code: 500,
      message: "获取失败: " + err.message,
    };
  }
}

// 管理员回复
async function adminReply(data) {
  console.log("管理员回复接收到的数据:", data);
  
  const { suggestionId, userId, content, images, adminInfo } = data;
  
  // 参数检查 - 允许发送图片时内容可以为空，但至少要有suggestionId或userId
  if ((!suggestionId && !userId) || (!content && (!images || !images.length))) {
    return {
      code: 400,
      message: "缺少必要参数",
    };
  }
  
  try {
    let targetSuggestion;
    let targetSuggestionId = suggestionId;
    
    // 情况1: 有suggestionId，查找现有记录
    if (suggestionId) {
      const suggestionResult = await db.collection("suggestions").doc(suggestionId).get();
      if (suggestionResult.data) {
        targetSuggestion = suggestionResult.data;
      } else if (userId) {
        // 记录不存在但有userId，创建新记录
        console.log("建议记录不存在，但有userId，创建新记录");
        
        // 创建新记录
        const newSuggestion = {
          userId: userId,
          status: "replied",
          createTime: new Date(),
          updateTime: new Date(),
          conversations: []
        };
        
        await db.collection("suggestions").doc(suggestionId).set({
          data: newSuggestion
        });
        
        targetSuggestion = newSuggestion;
      } else {
        return {
          code: 404,
          message: "建议记录不存在",
        };
      }
    }
    // 情况2: 没有suggestionId但有userId，创建新记录
    else if (userId) {
      console.log("没有suggestionId，创建新建议记录");
      
      const newSuggestion = {
        userId: userId,
        status: "replied",
        createTime: new Date(),
        updateTime: new Date(),
        conversations: []
      };
      
      const result = await db.collection("suggestions").add({
        data: newSuggestion
      });
      
      targetSuggestion = newSuggestion;
      targetSuggestionId = result._id;
      
      console.log("创建新建议成功，ID:", targetSuggestionId);
    }
    
    // 创建管理员回复记录
    const adminReply = {
      id: generateId(),
      sender: "admin",
      senderName: adminInfo?.name || "客服",
      content: content || "", // 允许内容为空
      createTime: new Date(),
    };
    
    // 添加图片（如果有）
    if (images && images.length > 0) {
      adminReply.images = images;
      console.log("添加图片到回复:", images);
    }
    
    // 更新或创建建议记录
    if (targetSuggestionId) {
      console.log("更新建议记录:", targetSuggestionId);
      
      // 如果是新创建的记录，直接设置conversations
      if (!targetSuggestion.conversations || targetSuggestion.conversations.length === 0) {
        await db.collection("suggestions").doc(targetSuggestionId).update({
          data: {
            conversations: [adminReply],
            status: "replied",
            updateTime: new Date(),
            lastReplyTime: new Date(),
            lastReplySender: "admin",
          },
        });
      } else {
        // 已有记录，追加对话
        await db.collection("suggestions").doc(targetSuggestionId).update({
          data: {
            conversations: _.push(adminReply),
            status: "replied",
            updateTime: new Date(),
            lastReplyTime: new Date(),
            lastReplySender: "admin",
          },
        });
      }
    }
    
    console.log("管理员回复成功");
    
    return {
      code: 0,
      message: "回复成功",
      suggestionId: targetSuggestionId
    };
  } catch (err) {
    console.error("管理员回复失败:", err);
    return {
      code: 500,
      message: "回复失败: " + err.message,
    };
  }
}

// 更新建议状态
async function updateSuggestionStatus(data) {
  const { suggestionId, status } = data;

  if (!suggestionId || !status) {
    return {
      code: 400,
      message: "缺少必要参数",
    };
  }

  try {
    await db
      .collection("suggestions")
      .doc(suggestionId)
      .update({
        data: {
          status,
          updateTime: db.serverDate(),
        },
      });

    return {
      code: 0,
      message: "状态更新成功",
    };
  } catch (err) {
    console.error("更新状态失败:", err);
    return {
      code: 500,
      message: "更新失败: " + err.message,
    };
  }
}

/**
 * 获取特定用户的所有对话记录
 */
async function getUserConversations(data) {
  const { userId, suggestionIds } = data;

  if (!userId) {
    return {
      code: 400,
      message: "缺少用户ID参数",
    };
  }

  try {
    let query = db.collection("suggestions").where({
      userId: userId
    });
    
    // 如果提供了具体的建议ID列表，则限制查询范围
    if (suggestionIds && suggestionIds.length > 0) {
      query = db.collection("suggestions").where({
        _id: _.in(suggestionIds)
      });
    }

    // 获取该用户的所有建议
    const result = await query.orderBy("createTime", "asc").get();
    
    return {
      code: 0,
      message: "获取用户对话记录成功",
      data: result.data || [],
    };
  } catch (err) {
    console.error("获取用户对话记录失败:", err);
    return {
      code: 500,
      message: "获取失败: " + err.message,
    };
  }
}

/**
 * 标记用户消息为已读
 */
async function markUserMessagesRead(data) {
  const { userId, suggestionIds } = data;

  if (!userId || !suggestionIds || !suggestionIds.length) {
    return {
      code: 400,
      message: "缺少必要参数",
    };
  }

  try {
    // 批量更新多个文档
    const tasks = suggestionIds.map(async (suggestionId) => {
      try {
        const suggestion = await db.collection("suggestions").doc(suggestionId).get();
        if (!suggestion.data) return null;

        // 找到所有用户消息并标记为已读
        const updatedConversations = suggestion.data.conversations.map(conv => {
          if (conv.sender === 'user') {
            return {...conv, isRead: true};
          }
          return conv;
        });

        // 更新文档
        await db.collection("suggestions").doc(suggestionId).update({
          data: {
            conversations: updatedConversations
          }
        });

        return suggestionId;
      } catch (err) {
        console.error(`标记建议 ${suggestionId} 失败:`, err);
        return null;
      }
    });

    // 等待所有更新完成
    const results = await Promise.all(tasks);
    const successCount = results.filter(id => id !== null).length;

    return {
      code: 0,
      message: `成功标记 ${successCount}/${suggestionIds.length} 条建议`,
      updatedIds: results.filter(id => id !== null)
    };
  } catch (err) {
    console.error("标记消息已读失败:", err);
    return {
      code: 500,
      message: "操作失败: " + err.message,
    };
  }
}
