/* 基础加载容器 */
.loading-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  opacity: 1;
  transition: opacity 0.5s ease;
}

/* 默认类型 */
.loading-container.default {
  background: rgba(0, 0, 0, 0.75);
}

/* 透明类型 */
.loading-container.transparent {
  background: transparent;
}

/* 显示/隐藏状态 */
.loading-container.hide {
  opacity: 0;
  pointer-events: none;
  transform: scale(0.95);
  transition: opacity 0.5s ease, transform 0.5s ease;
}

.loading-container.show {
  opacity: 1;
  transform: scale(1);
  transition: opacity 0.5s ease, transform 0.5s ease;
}

/* 加载动画容器 */
.loading-dots {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
}

/* 点样式 */
.dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  background-color: #ffffff;
  opacity: 0.6;
  animation: dot-pulse 1.4s infinite ease-in-out both;
}

/* 尺寸变体 */
.loading-container.small .dot {
  width: 8rpx;
  height: 8rpx;
}

.loading-container.medium .dot {
  width: 12rpx;
  height: 12rpx;
}

.loading-container.large .dot {
  width: 16rpx;
  height: 16rpx;
}

/* 点的动画延迟 */
.dot:nth-child(1) {
  animation-delay: -0.32s;
}

.dot:nth-child(2) {
  animation-delay: -0.16s;
}

/* 脉冲动画 */
@keyframes dot-pulse {
  0%, 80%, 100% { 
    transform: scale(0.6);
    opacity: 0.4;
  } 
  40% { 
    transform: scale(1);
    opacity: 0.8;
  }
} 