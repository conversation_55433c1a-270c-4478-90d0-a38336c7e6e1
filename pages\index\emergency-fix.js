/**
 * 紧急修复脚本 - 解决视频列表不显示问题
 * 直接在页面初始化时调用，确保数据正常加载
 */

/**
 * 紧急修复函数
 * @param {Object} pageContext - 页面上下文
 */
function emergencyFix(pageContext) {
  // 减少日志输出
  // console.log('[EmergencyFix] 开始紧急修复');
  
  // 1. 立即重置页面状态，但不显示内容，保留动画效果
  pageContext.setData({
    loading: false,
    firstLoading: false,
    showContent: false, // 改为false，让onShow方法控制动画
    isRefreshing: false
  });
  
  // 2. 直接调用云函数获取数据
  wx.cloud.callFunction({
    name: 'videoManager',
    data: {
      action: 'getVisibleVideos',
      data: {
        page: 1,
        pageSize: 10
      }
    }
  }).then(res => {
    // 减少日志输出
    // console.log('[EmergencyFix] 云函数调用成功:', res);
    
    if (res.result && res.result.code === 200 && res.result.data && res.result.data.videos) {
      const videos = res.result.data.videos;
      // console.log('[EmergencyFix] 获取到视频数据，数量:', videos.length);
      
      if (videos.length > 0) {
        // 处理视频数据格式
        const processedVideos = videos.map(video => ({
          id: video._id || video.id,
          mainTitle: video.mainTitle || video.title || '',
          subTitle: video.subTitle || video.subtitle || '',
          coverUrl: video.coverUrl || '',
          videoUrl: video.videoUrl || '',
          playCount: video.playCount || 0,
          author: video.author || '',
          authorAvatar: video.authorAvatar || '/static/logo.png',
          isPlaying: false,
          description: video.description || ''
        }));
        
        // 更新页面数据，但不立即显示内容，保留动画效果
        pageContext.setData({
          videoList: processedVideos,
          loading: false,
          firstLoading: false,
          hasMore: videos.length >= 10,
          emergencyFixCompleted: true  // 标记紧急修复已完成
        });
        
        // 延迟显示内容，让onShow方法控制动画效果
        setTimeout(() => {
          if (!pageContext.data.showContent) {
            console.log('[EmergencyFix] 延迟显示内容，确保动画效果');
            pageContext.setData({
              showContent: true
            });
          }
        }, 500); // 给onShow方法足够的时间来控制动画
        
        // console.log('[EmergencyFix] ✅ 紧急修复成功');
        return true;
      }
    }
    
    console.log('[EmergencyFix] ❌ 云函数返回数据异常');
    return false;
  }).catch(err => {
    console.error('[EmergencyFix] ❌ 云函数调用失败:', err);
    return false;
  });
}

/**
 * 检查并执行紧急修复
 * @param {Object} pageContext - 页面上下文
 */
function checkAndFix(pageContext) {
  // 延迟检查，给正常流程一些时间
  setTimeout(() => {
    const data = pageContext.data;
    
    // 如果页面仍然在加载状态或没有视频数据，执行紧急修复
    if (data.loading || data.firstLoading || !data.videoList || data.videoList.length === 0) {
      console.log('[EmergencyFix] 检测到需要紧急修复');
      emergencyFix(pageContext);
    } else {
      // 减少日志输出
      // console.log('[EmergencyFix] 页面状态正常，无需修复');
    }
  }, 3000); // 3秒后检查
}

module.exports = {
  emergencyFix,
  checkAndFix
};