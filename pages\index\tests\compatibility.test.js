/**
 * 兼容性测试
 * 测试重构后系统与原有API、数据结构、行为的兼容性
 */

const MainController = require('../modules/main-controller');
const { createMockPageContext, createMockVideoList, waitFor } = require('./test-utils');

// Mock wx API - 模拟不同版本的微信API
const createWxApiMock = (version = 'latest') => {
  const baseApi = {
    showToast: jest.fn(),
    hideToast: jest.fn(),
    showLoading: jest.fn(),
    hideLoading: jest.fn(),
    stopPullDownRefresh: jest.fn(),
    pageScrollTo: jest.fn(),
    getSystemInfoSync: jest.fn(() => ({
      platform: 'devtools',
      version: version,
      statusBarHeight: 20,
      screenHeight: 800,
      screenWidth: 375,
      SDKVersion: version
    })),
    getStorageSync: jest.fn(),
    setStorageSync: jest.fn(),
    onNetworkStatusChange: jest.fn(),
    getNetworkType: jest.fn((options) => {
      options.success({ networkType: 'wifi' });
    })
  };

  // 根据版本添加不同的API
  if (version >= '2.0.0') {
    baseApi.onUnhandledRejection = jest.fn();
    baseApi.onError = jest.fn();
  }

  if (version >= '2.10.0') {
    baseApi.getPerformance = jest.fn(() => ({
      memory: {
        usedJSHeapSize: 10 * 1024 * 1024,
        totalJSHeapSize: 50 * 1024 * 1024
      }
    }));
  }

  return baseApi;
};

// Mock getCurrentPages
global.getCurrentPages = jest.fn(() => [
  { route: 'pages/index/index' }
]);

// Mock getApp
global.getApp = jest.fn(() => ({
  globalData: { 
    version: '1.0.0',
    currentTabIndex: 1
  }
}));

describe('Compatibility Tests 兼容性测试', () => {
  let mainController;
  let mockPageContext;

  beforeEach(() => {
    jest.clearAllMocks();
    mockPageContext = createMockPageContext({
      videoList: createMockVideoList(3)
    });
  });

  afterEach(() => {
    if (mainController) {
      mainController.destroy();
    }
  });

  describe('API兼容性测试', () => {
    beforeEach(async () => {
      global.wx = createWxApiMock('2.10.0');
      mainController = new MainController(mockPageContext);
      await mainController.init();
    });

    test('所有原有API方法都应该存在', () => {
      // 生命周期方法
      expect(typeof mockPageContext.onLoad).toBe('function');
      expect(typeof mockPageContext.onShow).toBe('function');
      expect(typeof mockPageContext.onHide).toBe('function');
      expect(typeof mockPageContext.onUnload).toBe('function');

      // 业务方法
      expect(typeof mockPageContext.loadVideoList).toBe('function');
      expect(typeof mockPageContext.refreshVideoList).toBe('function');
      expect(typeof mockPageContext.onPullDownRefresh).toBe('function');
      expect(typeof mockPageContext.onReachBottom).toBe('function');

      // 视频相关方法
      expect(typeof mockPageContext.handleVideoTap).toBe('function');
      expect(typeof mockPageContext.onVideoPlay).toBe('function');
      expect(typeof mockPageContext.onVideoPause).toBe('function');
      expect(typeof mockPageContext.onVideoEnd).toBe('function');
      expect(typeof mockPageContext.onVideoError).toBe('function');
      expect(typeof mockPageContext.onVideoFullscreenChange).toBe('function');
      expect(typeof mockPageContext.pauseAllVideos).toBe('function');

      // 搜索相关方法
      expect(typeof mockPageContext.initSearchComponent).toBe('function');
      expect(typeof mockPageContext.onSearchTap).toBe('function');
      expect(typeof mockPageContext.onSearchInput).toBe('function');
      expect(typeof mockPageContext.onSearchConfirm).toBe('function');
      expect(typeof mockPageContext.onSearchFocus).toBe('function');
      expect(typeof mockPageContext.searchLocalVideos).toBe('function');
      expect(typeof mockPageContext.clearSearch).toBe('function');

      // 分享相关方法
      expect(typeof mockPageContext.onShareAppMessage).toBe('function');
      expect(typeof mockPageContext.openSharedVideo).toBe('function');

      // 工具方法
      expect(typeof mockPageContext.formatCount).toBe('function');
      expect(typeof mockPageContext.sanitizeVideoData).toBe('function');
      expect(typeof mockPageContext.getVideoUrl).toBe('function');

      // 导航相关方法
      expect(typeof mockPageContext.handleScroll).toBe('function');
      expect(typeof mockPageContext.onNavbarControl).toBe('function');
    });

    test('API方法调用应该保持原有行为', () => {
      // 测试生命周期方法
      expect(() => {
        mockPageContext.onLoad({ videoId: 'test' });
        mockPageContext.onShow();
        mockPageContext.onHide();
        mockPageContext.onUnload();
      }).not.toThrow();

      // 测试业务方法
      expect(() => {
        mockPageContext.loadVideoList(true);
        mockPageContext.refreshVideoList();
      }).not.toThrow();

      // 测试事件方法
      expect(() => {
        mockPageContext.onPullDownRefresh();
        mockPageContext.onReachBottom();
        mockPageContext.handleScroll({ detail: { scrollTop: 100 } });
      }).not.toThrow();

      // 测试视频方法
      expect(() => {
        const videoEvent = {
          currentTarget: { 
            dataset: { video: { id: 'test' } },
            id: 'video-test'
          }
        };
        mockPageContext.handleVideoTap(videoEvent);
        mockPageContext.onVideoPlay(videoEvent);
        mockPageContext.onVideoPause(videoEvent);
        mockPageContext.onVideoEnd(videoEvent);
        mockPageContext.onVideoError(videoEvent);
        mockPageContext.pauseAllVideos();
      }).not.toThrow();

      // 测试搜索方法
      expect(() => {
        const searchEvent = { detail: { value: '测试', results: [] } };
        mockPageContext.onSearchTap(searchEvent);
        mockPageContext.onSearchInput(searchEvent);
        mockPageContext.onSearchConfirm(searchEvent);
        mockPageContext.clearSearch();
      }).not.toThrow();
    });

    test('工具方法应该返回预期格式的数据', () => {
      // 测试formatCount
      expect(mockPageContext.formatCount(1500)).toBe('1.5k');
      expect(mockPageContext.formatCount(15000)).toBe('1.5万');
      expect(mockPageContext.formatCount(500)).toBe('500');

      // 测试sanitizeVideoData
      const rawVideo = {
        id: 'test_id',
        title: '测试标题',
        playCount: 1000
      };
      const cleanedVideo = mockPageContext.sanitizeVideoData(rawVideo);
      
      expect(cleanedVideo).toHaveProperty('id', 'test_id');
      expect(cleanedVideo).toHaveProperty('mainTitle');
      expect(cleanedVideo).toHaveProperty('isPlaying', false);
      expect(cleanedVideo).toHaveProperty('urlError', false);

      // 测试searchLocalVideos
      const searchResults = mockPageContext.searchLocalVideos('测试');
      expect(Array.isArray(searchResults)).toBe(true);
    });

    test('分享方法应该返回正确的分享配置', () => {
      const shareEvent = {
        from: 'button',
        target: {
          dataset: {
            video: {
              id: 'test_video',
              mainTitle: '测试视频',
              coverUrl: 'http://test.com/cover.jpg'
            }
          }
        }
      };

      const shareConfig = mockPageContext.onShareAppMessage(shareEvent);
      
      expect(shareConfig).toHaveProperty('title');
      expect(shareConfig).toHaveProperty('path');
      expect(shareConfig.path).toContain('videoId=test_video');
    });
  });

  describe('数据结构兼容性测试', () => {
    beforeEach(async () => {
      global.wx = createWxApiMock('2.10.0');
      mainController = new MainController(mockPageContext);
      await mainController.init();
    });

    test('页面数据结构应该保持兼容', () => {
      // 验证关键数据字段存在
      expect(mockPageContext.data).toHaveProperty('videoList');
      expect(mockPageContext.data).toHaveProperty('loading');
      expect(mockPageContext.data).toHaveProperty('firstLoading');
      expect(mockPageContext.data).toHaveProperty('hasMore');
      expect(mockPageContext.data).toHaveProperty('page');
      expect(mockPageContext.data).toHaveProperty('pageSize');
      expect(mockPageContext.data).toHaveProperty('searchKeyword');
      expect(mockPageContext.data).toHaveProperty('isSearching');
      expect(mockPageContext.data).toHaveProperty('currentPlayingVideo');

      // 验证数据类型
      expect(Array.isArray(mockPageContext.data.videoList)).toBe(true);
      expect(typeof mockPageContext.data.loading).toBe('boolean');
      expect(typeof mockPageContext.data.firstLoading).toBe('boolean');
      expect(typeof mockPageContext.data.hasMore).toBe('boolean');
      expect(typeof mockPageContext.data.page).toBe('number');
      expect(typeof mockPageContext.data.pageSize).toBe('number');
    });

    test('视频数据结构应该保持兼容', () => {
      const testVideo = {
        id: 'test_id',
        baseId: 'base_id',
        mainTitle: '测试标题',
        subTitle: '副标题',
        coverUrl: 'http://test.com/cover.jpg',
        playCount: 1000,
        author: '作者',
        description: '描述'
      };

      const sanitizedVideo = mockPageContext.sanitizeVideoData(testVideo);

      // 验证必需字段
      expect(sanitizedVideo).toHaveProperty('id');
      expect(sanitizedVideo).toHaveProperty('baseId');
      expect(sanitizedVideo).toHaveProperty('mainTitle');
      expect(sanitizedVideo).toHaveProperty('subTitle');
      expect(sanitizedVideo).toHaveProperty('coverUrl');
      expect(sanitizedVideo).toHaveProperty('videoUrl');
      expect(sanitizedVideo).toHaveProperty('playCount');
      expect(sanitizedVideo).toHaveProperty('author');
      expect(sanitizedVideo).toHaveProperty('authorAvatar');
      expect(sanitizedVideo).toHaveProperty('isPlaying');
      expect(sanitizedVideo).toHaveProperty('description');
      expect(sanitizedVideo).toHaveProperty('urlError');

      // 验证默认值
      expect(sanitizedVideo.isPlaying).toBe(false);
      expect(sanitizedVideo.urlError).toBe(false);
    });

    test('搜索状态数据结构应该保持兼容', () => {
      // 模拟搜索状态变化
      mockPageContext.onSearchInput({ detail: { value: '测试搜索' } });

      // 验证搜索相关数据字段
      expect(mockPageContext.data).toHaveProperty('searchKeyword');
      expect(mockPageContext.data).toHaveProperty('isSearching');
      expect(mockPageContext.data).toHaveProperty('searchFocused');
      expect(mockPageContext.data).toHaveProperty('originalVideoList');
    });
  });

  describe('微信版本兼容性测试', () => {
    test('应该兼容旧版本微信API', async () => {
      // 测试旧版本微信API
      global.wx = createWxApiMock('1.9.0');
      
      mainController = new MainController(mockPageContext);
      
      // 初始化应该成功，即使某些API不存在
      await expect(mainController.init()).resolves.not.toThrow();
      
      // 基本功能应该正常工作
      expect(() => {
        mockPageContext.loadVideoList();
        mockPageContext.formatCount(1000);
        mockPageContext.handleScroll({ detail: { scrollTop: 100 } });
      }).not.toThrow();
    });

    test('应该兼容新版本微信API', async () => {
      // 测试新版本微信API
      global.wx = createWxApiMock('2.15.0');
      
      mainController = new MainController(mockPageContext);
      await mainController.init();
      
      // 所有功能应该正常工作
      expect(() => {
        mockPageContext.loadVideoList();
        mockPageContext.onVideoPlay({ currentTarget: { id: 'video-test' } });
        mockPageContext.onSearchTap({ detail: { value: '测试' } });
        mockPageContext.onShareAppMessage({ from: 'button' });
      }).not.toThrow();
    });

    test('应该处理API不存在的情况', async () => {
      // 创建缺少某些API的mock
      global.wx = {
        ...createWxApiMock('1.0.0'),
        // 故意删除某些API
        onUnhandledRejection: undefined,
        onError: undefined,
        getPerformance: undefined
      };
      
      mainController = new MainController(mockPageContext);
      
      // 初始化应该成功，有降级处理
      await expect(mainController.init()).resolves.not.toThrow();
      
      // 基本功能应该仍然工作
      expect(mainController.initialized).toBe(true);
    });
  });

  describe('设备兼容性测试', () => {
    test('应该兼容不同的设备平台', async () => {
      const platforms = ['ios', 'android', 'devtools'];
      
      for (const platform of platforms) {
        global.wx = {
          ...createWxApiMock('2.10.0'),
          getSystemInfoSync: jest.fn(() => ({
            platform: platform,
            version: '2.10.0',
            statusBarHeight: platform === 'ios' ? 44 : 20,
            screenHeight: 800,
            screenWidth: 375
          }))
        };
        
        const controller = new MainController(createMockPageContext());
        await controller.init();
        
        // 验证在不同平台上都能正常工作
        expect(controller.initialized).toBe(true);
        
        controller.destroy();
      }
    });

    test('应该兼容不同的屏幕尺寸', async () => {
      const screenSizes = [
        { width: 320, height: 568 }, // iPhone 5
        { width: 375, height: 667 }, // iPhone 6/7/8
        { width: 414, height: 896 }, // iPhone XR
        { width: 360, height: 640 }  // Android
      ];
      
      for (const size of screenSizes) {
        global.wx = {
          ...createWxApiMock('2.10.0'),
          getSystemInfoSync: jest.fn(() => ({
            platform: 'ios',
            version: '2.10.0',
            statusBarHeight: 20,
            screenHeight: size.height,
            screenWidth: size.width,
            windowHeight: size.height - 20,
            windowWidth: size.width
          }))
        };
        
        const controller = new MainController(createMockPageContext());
        await controller.init();
        
        // 验证在不同屏幕尺寸上都能正常工作
        expect(controller.initialized).toBe(true);
        
        controller.destroy();
      }
    });
  });

  describe('数据格式兼容性测试', () => {
    beforeEach(async () => {
      global.wx = createWxApiMock('2.10.0');
      mainController = new MainController(mockPageContext);
      await mainController.init();
    });

    test('应该兼容旧版本的视频数据格式', () => {
      // 旧版本数据格式
      const oldFormatVideo = {
        id: 'old_video',
        title: '旧格式标题', // 使用title而不是mainTitle
        cover: 'http://old.com/cover.jpg', // 使用cover而不是coverUrl
        count: 5000, // 使用count而不是playCount
        desc: '旧格式描述' // 使用desc而不是description
      };

      const sanitizedVideo = mockPageContext.sanitizeVideoData(oldFormatVideo);

      // 验证数据转换正确
      expect(sanitizedVideo.id).toBe('old_video');
      expect(sanitizedVideo.mainTitle).toBeDefined();
      expect(sanitizedVideo.coverUrl).toBeDefined();
      expect(sanitizedVideo.playCount).toBeDefined();
      expect(sanitizedVideo.description).toBeDefined();
    });

    test('应该兼容不完整的视频数据', () => {
      // 不完整的数据
      const incompleteVideo = {
        id: 'incomplete_video'
        // 缺少其他字段
      };

      const sanitizedVideo = mockPageContext.sanitizeVideoData(incompleteVideo);

      // 验证默认值填充
      expect(sanitizedVideo.id).toBe('incomplete_video');
      expect(sanitizedVideo.mainTitle).toBe('');
      expect(sanitizedVideo.coverUrl).toBe('/static/logo.png');
      expect(sanitizedVideo.playCount).toBe('0');
      expect(sanitizedVideo.isPlaying).toBe(false);
      expect(sanitizedVideo.urlError).toBe(false);
    });

    test('应该兼容不同的数字格式', () => {
      // 测试不同格式的数字
      const testCases = [
        { input: 1500, expected: '1.5k' },
        { input: '1500', expected: '1.5k' },
        { input: 15000, expected: '1.5万' },
        { input: '15000', expected: '1.5万' },
        { input: 500, expected: '500' },
        { input: '500', expected: '500' },
        { input: 0, expected: '0' },
        { input: '0', expected: '0' },
        { input: null, expected: '0' },
        { input: undefined, expected: '0' },
        { input: 'invalid', expected: '0' }
      ];

      testCases.forEach(({ input, expected }) => {
        const result = mockPageContext.formatCount(input);
        expect(result).toBe(expected);
      });
    });
  });

  describe('事件处理兼容性测试', () => {
    beforeEach(async () => {
      global.wx = createWxApiMock('2.10.0');
      mainController = new MainController(mockPageContext);
      await mainController.init();
    });

    test('应该兼容不同格式的事件对象', () => {
      // 测试不同格式的滚动事件
      const scrollEvents = [
        { detail: { scrollTop: 100 } },
        { detail: { scrollTop: '100' } },
        { scrollTop: 100 }, // 缺少detail包装
        {} // 空事件对象
      ];

      scrollEvents.forEach(event => {
        expect(() => {
          mockPageContext.handleScroll(event);
        }).not.toThrow();
      });
    });

    test('应该兼容不同格式的视频事件', () => {
      // 测试不同格式的视频事件
      const videoEvents = [
        {
          currentTarget: {
            id: 'video-test',
            dataset: { video: { id: 'test' } }
          }
        },
        {
          currentTarget: {
            id: 'video-test'
            // 缺少dataset
          }
        },
        {
          target: { // 使用target而不是currentTarget
            id: 'video-test',
            dataset: { video: { id: 'test' } }
          }
        }
      ];

      videoEvents.forEach(event => {
        expect(() => {
          mockPageContext.onVideoPlay(event);
          mockPageContext.onVideoPause(event);
          mockPageContext.onVideoEnd(event);
          mockPageContext.onVideoError(event);
        }).not.toThrow();
      });
    });

    test('应该兼容不同格式的搜索事件', () => {
      // 测试不同格式的搜索事件
      const searchEvents = [
        { detail: { value: '测试搜索', results: [] } },
        { detail: { value: '测试搜索' } }, // 缺少results
        { detail: {} }, // 空detail
        { value: '测试搜索' } // 直接在根级别
      ];

      searchEvents.forEach(event => {
        expect(() => {
          mockPageContext.onSearchInput(event);
          mockPageContext.onSearchTap(event);
          mockPageContext.onSearchConfirm(event);
        }).not.toThrow();
      });
    });
  });

  describe('向后兼容性测试', () => {
    test('重构后的系统应该与原有系统行为一致', async () => {
      global.wx = createWxApiMock('2.10.0');
      mainController = new MainController(mockPageContext);
      await mainController.init();

      // 模拟原有系统的典型使用流程
      
      // 1. 页面加载
      mockPageContext.onLoad({ videoId: 'shared_video' });
      expect(mockPageContext.setData).toHaveBeenCalled();

      // 2. 页面显示
      mockPageContext.onShow();
      expect(getApp().globalData.currentTabIndex).toBe(1);

      // 3. 加载视频列表
      mockPageContext.loadVideoList();
      expect(mockPageContext.setData).toHaveBeenCalled();

      // 4. 下拉刷新
      mockPageContext.onPullDownRefresh();
      expect(wx.stopPullDownRefresh).toHaveBeenCalled();

      // 5. 上拉加载更多
      mockPageContext.onReachBottom();
      // 应该不抛出错误

      // 6. 视频播放
      const videoEvent = {
        currentTarget: {
          dataset: { video: { id: 'test_video' } }
        }
      };
      mockPageContext.handleVideoTap(videoEvent);
      // 应该不抛出错误

      // 7. 搜索功能
      mockPageContext.onSearchInput({ detail: { value: '测试' } });
      mockPageContext.onSearchConfirm({ detail: { value: '测试' } });
      mockPageContext.clearSearch();
      // 应该不抛出错误

      // 8. 分享功能
      const shareResult = mockPageContext.onShareAppMessage({
        from: 'button',
        target: { dataset: { video: { id: 'test', mainTitle: '测试' } } }
      });
      expect(shareResult).toHaveProperty('title');
      expect(shareResult).toHaveProperty('path');

      // 9. 页面隐藏和卸载
      mockPageContext.onHide();
      mockPageContext.onUnload();
      // 应该不抛出错误
    });

    test('错误处理应该保持向后兼容', async () => {
      global.wx = createWxApiMock('2.10.0');
      mainController = new MainController(mockPageContext);
      await mainController.init();

      // 模拟各种错误情况
      const errorScenarios = [
        () => mockPageContext.onVideoError({ currentTarget: { id: 'video-test' } }),
        () => mockPageContext.handleScroll(null), // 传入null
        () => mockPageContext.formatCount('invalid'), // 无效数字
        () => mockPageContext.sanitizeVideoData(null), // 传入null
        () => mockPageContext.searchLocalVideos('') // 空搜索
      ];

      // 所有错误场景都应该被优雅处理，不抛出未捕获的异常
      errorScenarios.forEach(scenario => {
        expect(scenario).not.toThrow();
      });

      // 验证错误处理统计
      const errorStats = mainController.getErrorStats();
      expect(errorStats).toBeDefined();
    });
  });

  describe('性能兼容性测试', () => {
    test('重构后性能应该不低于原有系统', async () => {
      global.wx = createWxApiMock('2.10.0');
      
      // 测试系统初始化性能
      const startTime = Date.now();
      mainController = new MainController(mockPageContext);
      await mainController.init();
      const initTime = Date.now() - startTime;

      // 初始化时间应该在合理范围内
      expect(initTime).toBeLessThan(2000);

      // 测试API调用性能
      const apiStartTime = Date.now();
      for (let i = 0; i < 100; i++) {
        mockPageContext.formatCount(i * 100);
        mockPageContext.sanitizeVideoData({ id: `test_${i}` });
      }
      const apiTime = Date.now() - apiStartTime;

      // API调用性能应该保持高效
      expect(apiTime).toBeLessThan(50);

      console.log(`兼容性测试 - 初始化时间: ${initTime}ms, API调用时间: ${apiTime}ms`);
    });
  });
});