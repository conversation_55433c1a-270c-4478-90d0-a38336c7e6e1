const app = getApp();

Page({
  data: {
    userInfo: null,
    hasUserInfo: false,
    canIUseGetUserProfile: false,
    isLoading: false,
    isAdmin: false, // 添加管理员状态
    showCacheTip: false, // 是否显示缓存清理提示
    showUserEditor: false, // 是否显示用户信息编辑器
    editUserInfo: {}, // 编辑中的用户信息
    keyboardHeight: 0, // 键盘高度
    currentField: "", // 当前正在编辑的字段
    statusBarHeight: 0, // 状态栏高度
    navigationBarHeight: 0, // 导航栏高度
    closingAnimation: false, // 添加淡出动画类
    // 添加预约相关数据
    appointments: [], // 用户的预约记录
    loadingAppointments: false, // 是否正在加载预约记录
    showAppointments: false, // 是否显示预约记录面板
    // 添加隐形入口相关数据
    adminTapCount: 0,
    staffTapCount: 0,
    lastTapTime: 0,
    // 余额管理相关数据
    activeBalanceTab: "balance", // 当前激活的标签：balance, recharge, record
    showBalancePanel: false, // 默认折叠面板，不显示内容
    userBalance: "0.00", // 用户余额
    rechargePlans: [], // 充值方案列表
    loadingRechargePlans: false, // 是否加载充值方案中
    rechargeRecords: [], // 充值记录列表
    loadingRechargeRecords: false, // 是否加载充值记录中

    // 充值计划详情相关
    showPlanDetailModal: false,
    currentPlan: null,

    // 充值成功相关
    showRechargeSuccessModal: false,
    rechargeVerifyCode: "",
    rechargeModalClosing: false, // 充值弹窗关闭动画状态
    currentRechargeOrderId: "", // 当前充值订单ID，用于取消操作

    // 底部区域显示的最近预约
    latestAppointment: null,

    // 底部区域显示的广告内容
    adItems: [],
    adFadeIndex: 0, // 当前广告fade索引
    adFadeOpacity: 1, // 当前广告透明度
    adFadeTimer: null, // 广告轮播定时器

    // 投诉建议相关数据
    showSuggestionsModal: false, // 是否显示投诉建议弹窗
    showSuggestionsAnimation: false, // 弹窗动画状态
    isClosingSuggestions: false, // 是否正在关闭弹窗
    activeReplyTab: "view", // 当前激活的回复标签：view(查看回复) 或 send(回复)
    keyboardActive: false, // 键盘是否弹出
    suggestionText: "", // 建议内容
    contactPhone: "", // 联系电话
    isAnonymous: false, // 是否匿名
    uploadedImages: [], // 已上传的图片
    uploadedVideos: [], // 已上传的视频
    conversations: [], // 对话记录
    replyText: "", // 回复内容
    unreadCount: 0, // 未读消息数量
    scrollToView: "", // 滚动到指定消息
    hasAdContent: false, // 是否有广告内容

    // 聊天相关数据
    messageText: "", // 当前输入的消息文本
    sendBtnActive: false, // 发送按钮是否激活
    inputFocus: false, // 输入框焦点状态
    // 移除了showMoreActions，不再需要

    // 下拉刷新相关
    refreshing: false, // 是否正在刷新

    // 登录弹窗相关
    showLoginModal: false, // 是否显示登录弹窗
    pendingAction: null, // 记录用户点击的功能，登录后执行
    tempRechargeplan: null, // 临时保存的充值方案

    // 指向广告相关
    showTargetAd: false,
    targetAdData: {
      imageUrl: '',
      jumpUrl: ''
    }
  },

  onLoad() {
    // 判断是否可以使用 wx.getUserProfile
    if (wx.getUserProfile) {
      this.setData({
        canIUseGetUserProfile: true,
      });
    }

    // 获取系统信息
    this.getSystemInfo();

    // 获取胶囊按钮位置信息
    const menuButtonInfo = wx.getMenuButtonBoundingClientRect();
    const navigationBarHeight = menuButtonInfo.bottom + 10; // 胶囊按钮底部位置加一点间距

    this.setData({
      navigationBarHeight: navigationBarHeight,
    });

    // 检查是否已有用户信息
    if (app.globalData.userInfo) {
      this.setData({
        userInfo: app.globalData.userInfo,
        hasUserInfo: true,
      });

      // 如果用户已登录且有openid，立即获取余额信息
      if (app.globalData.openid) {
        this.fetchUserBalance();
      }
    }

    // 检查是否是管理员
    if (app.globalData.isAdmin) {
      this.setData({
        isAdmin: true,
      });
    }

    // 检查是否需要显示缓存提示
    this.checkNeedCacheTip();

    // 检查版本是否变化
    this.checkVersionChanged();

    // 获取存储的用户信息
    this.getUserInfoFromStorage();

    // 在获取到用户信息后，立即获取余额信息
    if (this.data.hasUserInfo && app.globalData.openid) {
      this.fetchUserBalance();

      // 获取最新的预约信息
      this.fetchLatestAppointment();
    }

    // 初始化广告内容
    this.initAdItems();

    // 监听指向广告显示事件
    this.setupTargetAdListener();
  },

  // 获取系统信息，包括状态栏和导航栏高度
  getSystemInfo() {
    try {
      const systemInfo = wx.getSystemInfoSync();
      const menuButtonInfo = wx.getMenuButtonBoundingClientRect();

      // 状态栏高度
      const statusBarHeight = systemInfo.statusBarHeight;

      // 胶囊按钮与顶部的距离，即为导航栏高度
      // 使用胶囊按钮的bottom位置作为导航栏的底部位置
      const navigationBarHeight = menuButtonInfo.bottom;

      this.setData({
        statusBarHeight,
        navigationBarHeight,
      });

      // console.log("状态栏高度:", statusBarHeight, "导航栏高度:", navigationBarHeight);
    } catch (e) {
      console.error("获取系统信息失败", e);
      // 设置默认值
      this.setData({
        statusBarHeight: 20,
        navigationBarHeight: 80,
      });
    }
  },

  // 打开地图显示店铺位置
  openLocation() {
    wx.openLocation({
      latitude: 23.144894, // 广州越秀区广园西路121号美博城的纬度
      longitude: 113.270213, // 广州越秀区广园西路121号美博城的经度
      name: "指家科技",
      address: "广州市越秀区广园西路121号美博城",
      scale: 18,
    });
  },

  // 从本地存储获取用户信息
  getUserInfoFromStorage() {
    try {
      const storedUserInfo = wx.getStorageSync("userInfo");
      if (storedUserInfo) {
        console.log("从本地存储获取到用户信息:", storedUserInfo);

        // 确保用户信息完整
        if (!storedUserInfo.nickName) {
          storedUserInfo.nickName = "昵称";
        }

        if (!storedUserInfo.signature) {
          storedUserInfo.signature = "填写个性签名，彰显个性人生";
        }

        // 头像逻辑：优先使用自定义头像，否则使用默认头像
        // 但是要保护刚上传的头像，不被重置
        const currentUserInfo = this.data.userInfo || {};
        const hasRecentlyUploadedAvatar =
          currentUserInfo.avatarUrl &&
          currentUserInfo.avatarUrl.startsWith("cloud://") &&
          currentUserInfo.customAvatar === true;

        if (hasRecentlyUploadedAvatar) {
          // 保护刚上传的头像
          console.log(
            "🔒 保护刚上传的头像，不被本地存储重置:",
            currentUserInfo.avatarUrl
          );
          storedUserInfo.avatarUrl = currentUserInfo.avatarUrl;
          storedUserInfo.customAvatar = true;
        } else if (
          !storedUserInfo.avatarUrl ||
          (!storedUserInfo.customAvatar &&
            storedUserInfo.avatarUrl === "/static/微信图标.png")
        ) {
          storedUserInfo.avatarUrl = "/static/微信图标.png";
          storedUserInfo.customAvatar = false;
        }

        console.log("处理后的用户信息:", storedUserInfo);

        this.setData({
          userInfo: storedUserInfo,
          hasUserInfo: true,
        });
        app.globalData.userInfo = storedUserInfo;
      }
    } catch (e) {
      console.error("获取本地存储的用户信息失败", e);
    }
  },

  onShow() {
    if (typeof this.getTabBar === "function" && this.getTabBar()) {
      this.getTabBar().setData({
        selected: 3, // 更新选中的标签，3代表"我的"是第四个标签
      });
    }

    // 切换页面时关闭折叠面板
    this.closeBalancePanel();

    // 获取最新的用户信息
    this.getUserInfoFromStorage();

    // 检查是否需要展开预约记录
    const app = getApp();
    if (app.globalData.showAppointments) {
      // 重置标记
      app.globalData.showAppointments = false;

      // 展开预约记录面板
      this.setData({
        showAppointments: true,
      });

      // 如果用户已登录，获取预约记录
      if (this.data.hasUserInfo && app.globalData.openid) {
        this.fetchUserAppointments();
      }
    }

    // 如果用户已登录且有openid，则尝试从云端刷新用户信息
    if (this.data.hasUserInfo && app.globalData.openid) {
      console.log("尝试从云端获取最新用户信息");

      // 获取用户的预约记录
      this.fetchUserAppointments();

      // 获取用户的余额信息
      this.fetchUserBalance();

      // 获取充值方案列表
      this.fetchRechargePlans();

      // 获取充值记录
      this.fetchRechargeRecords();

      // 获取最新的预约信息
      this.fetchLatestAppointment();

      // 完全跳过用户详细信息的获取，避免覆盖刚上传的头像
      // this.fetchUserDetail(); // 暂时注释掉，防止覆盖头像
    } else if (app.globalData.needRefreshUserInfo) {
      console.log("检测到需要刷新用户信息标记");

      // 重置标记
      app.globalData.needRefreshUserInfo = false;

      // 如果用户已登录且有openid，刷新云端信息
      if (this.data.hasUserInfo && app.globalData.openid) {
        // 检查是否刚刚上传了头像，如果是则跳过fetchUserDetail避免覆盖
        const currentUserInfo = this.data.userInfo || {};
        const hasRecentlyUploadedAvatar =
          currentUserInfo.avatarUrl &&
          currentUserInfo.avatarUrl.startsWith("cloud://") &&
          currentUserInfo.customAvatar === true;

        if (!hasRecentlyUploadedAvatar) {
          this.fetchUserDetail();
        } else {
          console.log("检测到刚上传的头像，跳过fetchUserDetail避免覆盖");
        }

        // 获取用户的预约记录
        this.fetchUserAppointments();

        // 获取最新的预约信息
        this.fetchLatestAppointment();
      }
    }

    // 检查管理员状态
    if (app.globalData.isAdmin) {
      this.setData({
        isAdmin: true,
      });
    }

    // 处理从其他页面跳转过来的登录请求
    if (app.globalData.redirectToLogin && !this.data.hasUserInfo) {
      // 延迟一点时间执行，确保页面已完全加载
      setTimeout(() => {
        this.getUserProfile();
        // 清除重定向标记
        app.globalData.redirectToLogin = false;
      }, 500);
    }

    // 检查是否需要显示用户信息编辑器
    if (app.globalData.showUserEditor && this.data.hasUserInfo) {
      // 延迟一点时间执行，确保页面已完全加载
      setTimeout(() => {
        this.openUserEditor();
        // 清除显示编辑器标记
        app.globalData.showUserEditor = false;
      }, 500);
    }

    this.startAdFade();
  },

  onHide() {
    this.stopAdFade();
  },

  onUnload() {
    this.stopAdFade();
  },

  /**
   * 页面滚动时触发
   */
  onPageScroll() {
    // 滚动时关闭折叠面板
    this.closeBalancePanel();
  },

  /**
   * 关闭余额折叠面板
   */
  closeBalancePanel() {
    if (this.data.showBalancePanel) {
      this.setData({
        showBalancePanel: false,
      });
    }
  },

  /**
   * 阻止事件冒泡
   */
  preventBubble() {
    // 这个方法仅用于阻止事件冒泡，不需要执行任何操作
    console.log("阻止事件冒泡");
  },

  // 从云端获取用户详细信息
  fetchUserDetail() {
    // 如果没有openid，不进行请求
    if (!app.globalData.openid) {
      console.log("无法获取用户详细信息：openid不存在");
      return;
    }

    console.log("从云端获取用户详细信息...");

    wx.cloud.callFunction({
      name: "userManager",
      data: {
        action: "getUserDetail",
      },
      success: (res) => {
        if (res.result && res.result.code === 0 && res.result.data) {
          const cloudUserInfo = res.result.data;
          console.log("获取到云端用户信息:", cloudUserInfo);

          // 确保用户信息完整
          if (!cloudUserInfo.nickName) {
            cloudUserInfo.nickName = "昵称";
          }

          if (!cloudUserInfo.signature) {
            cloudUserInfo.signature = "填写个性签名，彰显个性人生";
          }

          // 检查本地是否有更新的头像（防止云端旧数据覆盖新上传的头像）
          const currentUserInfo = this.data.userInfo || {};
          const localAvatarUrl = currentUserInfo.avatarUrl;

          // 如果本地头像是云存储文件ID（以cloud://开头），且与云端不同，则保留本地头像
          if (
            localAvatarUrl &&
            localAvatarUrl.startsWith("cloud://") &&
            localAvatarUrl !== cloudUserInfo.avatarUrl
          ) {
            console.log(
              "检测到本地有更新的头像，保留本地头像:",
              localAvatarUrl
            );
            cloudUserInfo.avatarUrl = localAvatarUrl;
            cloudUserInfo.customAvatar = true;
          }

          // 同时检查本地存储中的头像
          const storedUserInfo = wx.getStorageSync("userInfo");
          if (
            storedUserInfo &&
            storedUserInfo.avatarUrl &&
            storedUserInfo.avatarUrl.startsWith("cloud://") &&
            storedUserInfo.avatarUrl !== cloudUserInfo.avatarUrl
          ) {
            console.log(
              "检测到本地存储有更新的头像，保留本地存储头像:",
              storedUserInfo.avatarUrl
            );
            cloudUserInfo.avatarUrl = storedUserInfo.avatarUrl;
            cloudUserInfo.customAvatar = true;
          }

          // 更新全局和页面数据
          app.globalData.userInfo = cloudUserInfo;
          this.setData({
            userInfo: cloudUserInfo,
            hasUserInfo: true,
          });

          // 保存到本地存储
          wx.setStorageSync("userInfo", cloudUserInfo);
          console.log("用户信息已从云端更新");
        } else {
          console.log("云端未找到用户信息");
        }
      },
      fail: (err) => {
        console.error("获取用户信息失败", err);
      },
    });
  },

  // 获取用户信息
  getUserProfile() {
    this.setData({ isLoading: true });

    // 推荐使用wx.getUserProfile获取用户信息，开发者每次通过该接口获取用户个人信息均需用户确认
    wx.getUserProfile({
      desc: "用于完善会员资料", // 缩短描述，符合微信要求
      success: (res) => {
        // 先设置基本信息，显示登录成功状态
        const basicUserInfo = res.userInfo;

        // 设置默认值
        if (!basicUserInfo.nickName) {
          basicUserInfo.nickName = "昵称";
        }

        if (!basicUserInfo.signature) {
          basicUserInfo.signature = "填写个性签名，彰显个性人生";
        }

        // 使用微信图标作为默认头像
        if (!basicUserInfo.customAvatar) {
          basicUserInfo.avatarUrl = "/static/微信图标.png";
        }

        // 临时保存到全局，后面会用云端数据覆盖
        app.globalData.userInfo = basicUserInfo;

        this.setData({
          userInfo: basicUserInfo,
          hasUserInfo: true,
          isLoading: false,
        });

        // 调用云函数先保存基本信息，同时获取openid
        wx.cloud.callFunction({
          name: "userManager",
          data: {
            action: "saveUserInfo",
            userInfo: basicUserInfo,
          },
          success: (res) => {
            console.log("保存用户基本信息成功", res);
            if (res.result && res.result.code === 0) {
              // 保存openid到全局变量
              if (res.result.data && res.result.data.openid) {
                app.globalData.openid = res.result.data.openid;
              }

              // 立即查询云端是否有该用户的完整信息
              this.fetchUserDetailAfterLogin();
            } else {
              console.log("保存用户信息成功，但没有获取到完整结果");
              this.setData({ isLoading: false });
            }
          },
          fail: (err) => {
            console.error("保存用户信息失败", err);
            this.setData({ isLoading: false });
          },
        });
      },
      fail: (err) => {
        console.error("获取用户信息失败", err);
        this.setData({ isLoading: false });
      },
    });
  },

  // 登录后获取用户完整信息
  fetchUserDetailAfterLogin() {
    wx.cloud.callFunction({
      name: "userManager",
      data: {
        action: "getUserDetail",
      },
      success: (res) => {
        console.log("获取用户完整信息", res);

        if (res.result && res.result.code === 0 && res.result.data) {
          // 获取到了用户的完整信息（包括自定义头像、昵称和签名）
          const fullUserInfo = res.result.data;
          console.log("云端用户完整信息:", fullUserInfo);

          // 确保用户信息完整
          if (!fullUserInfo.nickName) {
            fullUserInfo.nickName = "昵称";
          }

          if (!fullUserInfo.signature) {
            fullUserInfo.signature = "填写个性签名，彰显个性人生";
          }

          // 如果没有自定义头像，使用微信图标作为默认头像
          if (!fullUserInfo.customAvatar) {
            fullUserInfo.avatarUrl = "/static/微信图标.png";
          }

          // 检查本地是否有刚上传的头像，如果有则保留本地头像
          const currentUserInfo = this.data.userInfo || {};
          if (
            currentUserInfo.avatarUrl &&
            currentUserInfo.avatarUrl.startsWith("cloud://") &&
            currentUserInfo.customAvatar === true &&
            currentUserInfo.avatarUrl !== fullUserInfo.avatarUrl
          ) {
            console.log(
              "🔒 保护刚上传的头像，不被云端数据覆盖:",
              currentUserInfo.avatarUrl
            );
            fullUserInfo.avatarUrl = currentUserInfo.avatarUrl;
            fullUserInfo.customAvatar = true;
          }

          // 更新全局数据和页面数据
          app.globalData.userInfo = fullUserInfo;
          this.setData({
            userInfo: fullUserInfo,
          });

          // 保存到本地存储
          wx.setStorageSync("userInfo", fullUserInfo);

          // 登录成功后立即获取余额信息
          this.fetchUserBalance();
        } else {
          // 如果没有获取到完整信息，使用基本信息
          // 保存到本地存储
          const basicUserInfo = this.data.userInfo || {};

          // 设置默认值
          if (!basicUserInfo.nickName) {
            basicUserInfo.nickName = "昵称";
          }

          if (!basicUserInfo.signature) {
            basicUserInfo.signature = "填写个性签名，彰显个性人生";
          }

          // 使用微信图标作为默认头像
          if (!basicUserInfo.customAvatar) {
            basicUserInfo.avatarUrl = "/static/微信图标.png";
          }

          console.log("未找到云端信息，使用基本信息:", basicUserInfo);
          wx.setStorageSync("userInfo", basicUserInfo);

          // 更新页面数据
          this.setData({
            userInfo: basicUserInfo,
          });

          // 即使使用基本信息，也尝试获取余额信息
          this.fetchUserBalance();

          // 登录成功后立即获取预约信息，用于显示底部预约卡片
          this.fetchLatestAppointment();
        }

        // 隐藏加载提示
        wx.hideLoading();

        // 登录成功提示
        wx.showToast({
          title: '登录成功',
          icon: 'success',
          duration: 1500,
          success: () => {
            // 登录成功后立即获取预约信息，用于显示底部预约卡片
            this.fetchLatestAppointment();

            // 如果当前正在显示余额或充值记录页面，需要刷新数据
            if (this.data.showBalancePanel) {
              if (this.data.activeBalanceTab === 'balance') {
                // 刷新余额数据
                this.fetchUserBalance();
              } else if (this.data.activeBalanceTab === 'record') {
                // 刷新充值记录
                this.fetchRechargeRecords();
              }
            }

            // 延迟执行待处理的操作
            setTimeout(() => {
              this.executePendingAction();
            }, 100);
          }
        });
      },
      fail: (err) => {
        console.error("获取用户信息失败", err);

        // 保存基本信息到本地存储
        const basicUserInfo = this.data.userInfo || {};

        // 设置默认值
        if (!basicUserInfo.nickName) {
          basicUserInfo.nickName = "昵称";
        }

        if (!basicUserInfo.signature) {
          basicUserInfo.signature = "填写个性签名，彰显个性人生";
        }

        // 使用微信图标作为默认头像
        if (!basicUserInfo.customAvatar) {
          basicUserInfo.avatarUrl = "/static/微信图标.png";
        }

        console.log("获取云端信息失败，使用基本信息:", basicUserInfo);
        wx.setStorageSync("userInfo", basicUserInfo);

        // 更新页面数据
        this.setData({
          userInfo: basicUserInfo,
        });

        // 尽管获取用户信息失败，仍然尝试获取余额信息
        this.fetchUserBalance();

        // 登录成功后立即获取预约信息，用于显示底部预约卡片
        this.fetchLatestAppointment();
      },
    });
  },

  // 退出登录
  logout() {
    // 先确认是否要退出
    wx.showModal({
      title: "提示",
      content: "确定要退出登录吗？",
      confirmText: "确定退出",
      confirmColor: "#007AFF",
      cancelText: "取消",
      cancelColor: "#999999",
      success: (res) => {
        if (res.confirm) {
          // 清除用户信息
          app.globalData.userInfo = null;
          app.globalData.isAdmin = false; // 同时清除管理员状态
          this.setData({
            userInfo: null,
            hasUserInfo: false,
            isAdmin: false,
            // 清除预约相关数据
            latestAppointment: null,
            appointments: [],
            showAppointments: false,
            // 清除余额相关数据
            userBalance: "0.00",
            actualBalance: "0.00",
            bonusBalance: "0.00",
            rechargeRecords: [],
            // 关闭所有折叠面板
            showBalancePanel: false,
          });
        }
      },
    });
  },

  // 打开用户信息编辑器
  openUserEditor() {
    console.log("尝试打开用户编辑弹窗");

    if (!this.data.hasUserInfo) {
      console.log("用户未登录，跳转到登录流程");
      this.getUserProfile();
      return;
    }

    console.log("用户已登录，准备显示编辑弹窗");

    // 创建编辑中的用户信息副本，确保包含最新的头像信息
    const currentUserInfo = this.data.userInfo || {};
    const editUserInfo = { ...currentUserInfo };

    // 确保头像信息是最新的（防止被旧数据覆盖）
    if (
      currentUserInfo.avatarUrl &&
      currentUserInfo.avatarUrl.startsWith("cloud://")
    ) {
      editUserInfo.avatarUrl = currentUserInfo.avatarUrl;
      editUserInfo.customAvatar = true;
      console.log("🔄 编辑器使用最新头像:", editUserInfo.avatarUrl);
    }

    // 使用nextTick确保UI更新在同一帧内完成
    wx.nextTick(() => {
      this.setData({
        showUserEditor: true,
        editUserInfo,
        keyboardHeight: 0,
        currentField: "",
      });
    });

    console.log("编辑弹窗已显示，初始键盘高度设为0");
  },

  // 关闭用户信息编辑弹窗
  closeUserEditor() {
    // 添加淡出动画类
    this.setData({
      closingAnimation: true,
    });

    // 等待动画完成后再隐藏弹窗
    setTimeout(() => {
      this.setData({
        showUserEditor: false,
        closingAnimation: false,
      });
    }, 400); // 与CSS中的动画时长一致
  },

  // 处理输入框获取焦点
  onInputFocus(e) {
    // 记录当前正在编辑的字段
    const field = e.currentTarget.dataset.field || "";

    this.setData({
      currentField: field,
    });

    // 手动检测键盘高度
    this.checkKeyboardHeight();
  },

  // 手动检测键盘高度
  checkKeyboardHeight() {
    // 清除之前的定时器
    if (this.keyboardCheckTimer) {
      clearTimeout(this.keyboardCheckTimer);
    }

    // 使用定时器延迟检测，确保键盘已完全弹出
    this.keyboardCheckTimer = setTimeout(() => {
      // 如果键盘高度已经大于0，不需要手动检测
      if (this.data.keyboardHeight > 0) {
        return;
      }

      // 获取系统信息
      wx.getSystemInfo({
        success: (res) => {
          // 通过wx.createSelectorQuery获取当前输入框位置
          wx.createSelectorQuery()
            .select(".info-input-inline")
            .boundingClientRect((rect) => {
              if (rect && this.data.keyboardHeight === 0) {
                // 估算键盘高度
                const estimatedKeyboardHeight =
                  res.windowHeight - rect.bottom - 20;

                if (estimatedKeyboardHeight > 100) {
                  this.setData({
                    keyboardHeight: estimatedKeyboardHeight,
                  });
                }
              }
            })
            .exec();
        },
      });
    }, 300);
  },

  // 处理输入框失去焦点
  onInputBlur(e) {
    // 失焦时记录当前文本，以便在再次聚焦时重新设置
    this.setData({
      inputFocus: false
    });
  },

  /**
   * 点击输入框区域，确保光标位于文本末尾
   */
  focusInputAtEnd() {
    // 保存当前文本
    const currentText = this.data.messageText;
    
    // 如果没有文本，直接聚焦
    if (!currentText || currentText.length === 0) {
      this.setData({
        inputFocus: true
      });
      return;
    }
    
    // 先清空文本，然后设置焦点
    this.setData({
      messageText: '',
      inputFocus: true
    });
    
    // 延时后恢复文本，确保光标在末尾
    setTimeout(() => {
      this.setData({
        messageText: currentText
      });
    }, 200); // 增加延时确保操作完成
  },

  // 处理昵称输入
  onNicknameInput(e) {
    this.setData({
      "editUserInfo.nickName": e.detail.value,
    });
  },

  // 处理签名输入
  onSignatureInput(e) {
    const value = e.detail.value;
    const maxLength = 20; // 最大字符数

    if (value.length > maxLength) {
      // 超出字符限制，截取前20个字符
      const trimmedValue = value.substring(0, maxLength);
      this.setData({
        "editUserInfo.signature": trimmedValue,
      });

      // 显示提示
      wx.showToast({
        title: `签名最多${maxLength}个字符`,
        icon: "none",
        duration: 1500,
      });
    } else {
      // 未超出限制，正常更新
      this.setData({
        "editUserInfo.signature": value,
      });
    }
  },

  // 选择头像
  chooseAvatar() {
    console.log("🎯 用户点击选择头像");
    wx.showActionSheet({
      itemList: ["从相册选择", "拍照"],
      success: (res) => {
        console.log("📋 用户选择了选项:", res.tapIndex);
        if (res.tapIndex === 0) {
          // 从相册选择
          console.log("📷 从相册选择头像");
          this.chooseAvatarImage("album");
        } else if (res.tapIndex === 1) {
          // 拍照
          console.log("📸 拍照选择头像");
          this.chooseAvatarImage("camera");
        }
      },
      fail: (err) => {
        console.error("❌ 显示选择菜单失败:", err);
      },
    });
  },

  // 选择头像图片（重命名避免与建议反馈的chooseImage方法冲突）
  chooseAvatarImage(sourceType) {
    console.log("📱 开始选择图片，来源:", sourceType);
    console.log("🔧 准备调用wx.chooseImage API");

    try {
      // 使用更兼容的wx.chooseImage替代wx.chooseMedia
      wx.chooseImage({
        count: 1,
        sizeType: ["compressed", "original"],
        sourceType: [sourceType],
        success: (res) => {
          console.log("✅ 图片选择成功:", res);
          if (res.tempFilePaths && res.tempFilePaths.length > 0) {
            const tempFilePath = res.tempFilePaths[0];
            console.log("📁 临时文件路径:", tempFilePath);
            this.uploadImage(tempFilePath);
          } else {
            console.error("❌ 没有获取到临时文件路径");
            wx.showToast({
              title: "获取图片失败",
              icon: "none",
            });
          }
        },
        fail: (err) => {
          console.error("❌ 选择图片失败:", err);
          if (err.errMsg && err.errMsg.indexOf("cancel") === -1) {
            wx.showToast({
              title: "选择图片失败: " + err.errMsg,
              icon: "none",
            });
          } else {
            console.log("👤 用户取消了图片选择");
          }
        },
        complete: () => {
          console.log("🏁 图片选择操作完成");
        },
      });
      console.log("🚀 wx.chooseImage API调用完成");
    } catch (error) {
      console.error("💥 调用wx.chooseImage时发生异常:", error);
      wx.showToast({
        title: "调用图片选择API失败",
        icon: "none",
      });
    }
  },

  // 头像上传 - 最简单直接的版本
  uploadImage(filePath) {
    console.log("🚀 开始上传头像，文件路径:", filePath);

    wx.showLoading({
      title: "上传中...",
      mask: true,
    });

    const openid = app.globalData.openid || "guest";
    const timestamp = new Date().getTime();
    const fileExt = filePath.substring(filePath.lastIndexOf("."));
    const cloudPath = `avatars/${openid}_${timestamp}${fileExt}`;

    wx.cloud.uploadFile({
      cloudPath,
      filePath,
      success: (res) => {
        console.log("✅ 头像上传成功！", res);

        const newAvatarUrl = res.fileID;
        console.log("🖼️ 新头像URL:", newAvatarUrl);

        // 🔥 关键步骤：立即强制更新所有头像显示
        this.forceUpdateAvatar(newAvatarUrl);

        wx.hideLoading();
        wx.showToast({
          title: "头像更换成功！",
          icon: "success",
          duration: 2000,
        });
      },
      fail: (err) => {
        console.error("❌ 头像上传失败:", err);
        wx.hideLoading();
        wx.showToast({
          title: "上传失败，请重试",
          icon: "none",
        });
      },
    });
  },

  // 🔥 强制更新头像 - 最简单直接的方法
  forceUpdateAvatar(newAvatarUrl) {
    console.log("🔥 强制更新头像:", newAvatarUrl);

    // 1. 创建新的用户信息对象
    const newUserInfo = {
      ...this.data.userInfo,
      avatarUrl: newAvatarUrl,
      customAvatar: true,
    };

    const newEditUserInfo = {
      ...this.data.editUserInfo,
      avatarUrl: newAvatarUrl,
      customAvatar: true,
    };

    // 2. 强制更新页面数据
    this.setData({
      userInfo: newUserInfo,
      editUserInfo: newEditUserInfo,
    });

    // 3. 更新全局数据
    app.globalData.userInfo = newUserInfo;

    // 4. 立即保存到本地存储
    wx.setStorageSync("userInfo", newUserInfo);

    // 5. 异步保存到云端（不影响显示）
    setTimeout(() => {
      this.saveToCloud(newAvatarUrl);
    }, 100);

    console.log("✅ 头像强制更新完成");
  },

  // 简化的云端保存
  saveToCloud(avatarUrl) {
    const userInfo = this.data.userInfo || {};
    wx.cloud.callFunction({
      name: "userManager",
      data: {
        action: "updateUserInfo",
        userInfo: {
          nickName: userInfo.nickName || "昵称",
          avatarUrl: avatarUrl,
          signature: userInfo.signature || "欢迎使用",
          customAvatar: true,
        },
      },
      success: (res) => {
        console.log("云端保存成功", res);
      },
      fail: (err) => {
        console.error("云端保存失败", err);
      },
    });
  },

  // 立即更新头像显示
  updateAvatarDisplay(newAvatarUrl) {
    console.log("🔄 立即更新头像显示:", newAvatarUrl);

    // 1. 立即更新页面数据
    this.setData({
      "userInfo.avatarUrl": newAvatarUrl,
      "userInfo.customAvatar": true,
      "editUserInfo.avatarUrl": newAvatarUrl,
      "editUserInfo.customAvatar": true,
    });

    // 2. 立即更新全局数据
    if (app.globalData.userInfo) {
      app.globalData.userInfo.avatarUrl = newAvatarUrl;
      app.globalData.userInfo.customAvatar = true;
    }

    console.log("✅ 头像显示已更新");
  },

  // 保存头像数据到本地和云端
  saveAvatarData(newAvatarUrl) {
    console.log("💾 开始保存头像数据:", newAvatarUrl);

    // 保存到本地存储
    try {
      const currentUserInfo = this.data.userInfo || {};
      const updatedUserInfo = {
        ...currentUserInfo,
        avatarUrl: newAvatarUrl,
        customAvatar: true,
      };

      wx.setStorageSync("userInfo", updatedUserInfo);
      console.log("✅ 头像已保存到本地存储");

      // 异步保存到云端
      this.saveAvatarToCloud(newAvatarUrl);
    } catch (e) {
      console.error("❌ 保存到本地存储失败:", e);
    }
  },

  // 立即保存头像到云端 - 简化版本
  saveAvatarToCloud(avatarUrl) {
    console.log("开始保存头像到云端:", avatarUrl);

    // 获取当前用户信息
    const currentUserInfo = this.data.userInfo || {};

    // 准备要更新的用户信息
    const userInfoToUpdate = {
      nickName: currentUserInfo.nickName || "昵称",
      avatarUrl: avatarUrl,
      signature: currentUserInfo.signature || "欢迎使用",
      customAvatar: true, // 强制设置为true
    };

    // 调用云函数更新
    wx.cloud.callFunction({
      name: "userManager",
      data: {
        action: "updateUserInfo",
        userInfo: userInfoToUpdate,
      },
      success: (res) => {
        console.log("头像保存到云端成功", res);
        if (res.result && res.result.code === 0) {
          console.log("头像云端同步完成");
        } else {
          console.error("头像云端保存失败:", res.result);
        }
      },
      fail: (err) => {
        console.error("头像云端保存失败", err);
      },
    });
  },

  // 立即更新头像到云端
  updateAvatarToCloud(avatarUrl) {
    // 获取当前用户信息
    const currentUserInfo = this.data.userInfo || {};

    // 准备要更新的用户信息
    const userInfoToUpdate = {
      nickName: currentUserInfo.nickName || "昵称",
      avatarUrl: avatarUrl,
      signature: currentUserInfo.signature || "欢迎使用",
      customAvatar: true, // 设置标记，表示这是用户自定义的头像
    };

    console.log("立即更新头像到云端:", userInfoToUpdate);

    // 调用云函数更新
    wx.cloud.callFunction({
      name: "userManager",
      data: {
        action: "updateUserInfo",
        userInfo: userInfoToUpdate,
      },
      success: (res) => {
        console.log("头像云端更新成功", res);

        if (res.result && res.result.code === 0) {
          // 云端更新成功，确保本地数据与云端一致
          const finalUserInfo = {
            ...currentUserInfo,
            avatarUrl: avatarUrl,
            customAvatar: true,
          };

          // 更新全局和页面数据
          app.globalData.userInfo = finalUserInfo;
          this.setData({
            userInfo: finalUserInfo,
            "editUserInfo.avatarUrl": avatarUrl,
          });

          // 保存到本地存储
          wx.setStorageSync("userInfo", finalUserInfo);

          console.log("头像更新完成，本地和云端已同步");
        } else {
          console.error("云端头像更新失败:", res.result);
        }
      },
      fail: (err) => {
        console.error("头像更新失败", err);
      },
    });
  },

  // 保存用户信息
  saveUserInfo() {
    const { editUserInfo } = this.data;

    // 检查昵称是否为空
    if (!editUserInfo.nickName || !editUserInfo.nickName.trim()) {
      wx.showToast({
        title: "昵称不能为空",
        icon: "none",
        duration: 2000,
      });
      return;
    }

    // 准备要更新的用户信息 - 确保使用最新的头像信息
    const currentUserInfo = this.data.userInfo || {};

    // 优先使用当前用户信息中的头像，如果editUserInfo中的头像更新则使用editUserInfo的
    let finalAvatarUrl = editUserInfo.avatarUrl;
    let finalCustomAvatar = editUserInfo.customAvatar;

    // 如果当前用户信息中有云存储头像，且editUserInfo中没有或者是默认头像，则使用当前用户信息的头像
    if (
      currentUserInfo.avatarUrl &&
      currentUserInfo.avatarUrl.startsWith("cloud://") &&
      currentUserInfo.customAvatar === true &&
      (!editUserInfo.avatarUrl ||
        editUserInfo.avatarUrl === "/static/微信图标.png")
    ) {
      finalAvatarUrl = currentUserInfo.avatarUrl;
      finalCustomAvatar = true;
      console.log("🔄 使用当前用户信息中的最新头像:", finalAvatarUrl);
    }

    const userInfoToUpdate = {
      nickName: editUserInfo.nickName.trim(),
      avatarUrl: finalAvatarUrl,
      signature: editUserInfo.signature || "欢迎使用",
      customNickname: true, // 标记此昵称是用户自定义的
      customAvatar:
        finalCustomAvatar === true ||
        (finalAvatarUrl && finalAvatarUrl.startsWith("cloud://")), // 智能判断头像自定义标记
    };

    console.log("保存用户信息到云端:", userInfoToUpdate);

    // 显示加载提示
    wx.showLoading({
      title: "保存中...",
      mask: true,
    });

    // 调用云函数更新用户信息
    wx.cloud.callFunction({
      name: "userManager",
      data: {
        action: "updateUserInfo",
        userInfo: userInfoToUpdate,
      },
      success: (res) => {
        console.log("更新用户信息成功", res);
        wx.hideLoading();

        if (res.result && res.result.code === 0) {
          // 关闭编辑弹窗
          this.closeUserEditor();

          // 更新全局数据
          app.globalData.userInfo = userInfoToUpdate;

          // 更新本地数据
          this.setData({
            userInfo: userInfoToUpdate,
          });

          // 保存到本地存储
          wx.setStorageSync("userInfo", userInfoToUpdate);

          // 显示成功提示
          wx.showToast({
            title: "保存成功",
            icon: "success",
            duration: 2000,
          });
        } else {
          wx.showToast({
            title: "保存失败，请重试",
            icon: "none",
            duration: 2000,
          });
        }
      },
      fail: (err) => {
        console.error("更新用户信息失败", err);
        wx.hideLoading();
        wx.showToast({
          title: "网络错误，请重试",
          icon: "none",
          duration: 2000,
        });
      },
    });
  },

  // 获取用户预约记录
  fetchUserAppointments() {
    // 如果用户未登录，不获取预约记录
    if (!this.data.hasUserInfo) {
      return;
    }

    this.setData({
      loadingAppointments: true,
    });

    // 调用云函数获取预约记录，这里使用用户的openid确保只返回该用户自己的预约
    // 云函数会自动获取当前用户的openid，并只返回该用户的预约记录
    // 这样保证了预约记录的隐私安全，只有预约者本人可以查看自己的预约
    wx.cloud.callFunction({
      name: "appointmentManager",
      data: {
        type: "frontend",
        action: "getUserAppointments",
      },
      success: (res) => {
        console.log("获取预约记录结果：", res);

        if (res.result && res.result.code === 0) {
          // 获取成功
          const appointments = res.result.data || [];

          // 按创建时间排序，最新创建的排在前面
          appointments.sort((a, b) => {
            // 获取创建时间
            const createTimeA = a.createTime
              ? new Date(a.createTime).getTime()
              : 0;
            const createTimeB = b.createTime
              ? new Date(b.createTime).getTime()
              : 0;

            // 降序排列（最新的在前面）
            return createTimeB - createTimeA;
          });

          this.setData({
            appointments: appointments,
            loadingAppointments: false,
          });

          // 更新最新预约信息
          this.updateLatestAppointment(appointments);
        } else {
          // 获取失败
          this.setData({
            appointments: [],
            loadingAppointments: false,
            latestAppointment: null, // 清空最新预约
          });

          wx.showToast({
            title: "获取预约记录失败",
            icon: "none",
          });
        }
      },
      fail: (err) => {
        console.error("获取预约记录失败：", err);

        this.setData({
          appointments: [],
          loadingAppointments: false,
          latestAppointment: null, // 清空最新预约
        });

        wx.showToast({
          title: "网络错误，请重试",
          icon: "none",
        });
      },
    });
  },

  // 显示预约二维码
  showAppointmentQRCode(e) {
    const appointment = e.currentTarget.dataset.appointment;

    if (!appointment || !appointment.verifyCode) {
      wx.showToast({
        title: "核销码不存在",
        icon: "none",
      });
      return;
    }

    // 显示核销码
    wx.showModal({
      title: "预约核销码",
      content: `您的核销码为：${appointment.verifyCode}`,
      showCancel: false,
      confirmText: "确定",
    });
  },

  // 跳转到编辑预约页面
  goToModifyAppointment(e) {
    const appointmentId = e.currentTarget.dataset.id;
    console.log("开始修改预约，ID:", appointmentId);

    // 查找对应的预约数据
    const appointmentToModify = this.data.appointments.find(
      (item) => item._id === appointmentId
    );
    if (appointmentToModify) {
      // 设置全局变量，标记修改模式
      const app = getApp();
      app.globalData.isModifyingAppointment = true;
      app.globalData.appointmentToModify = appointmentToModify;

      // 跳转到预约页面
      wx.switchTab({
        url: "/pages/appointment/appointment",
      });
    } else {
      wx.showToast({
        title: "找不到预约信息",
        icon: "none",
      });
    }
  },

  // 取消预约
  cancelAppointment(e) {
    const appointmentId = e.currentTarget.dataset.id;

    if (!appointmentId) {
      wx.showToast({
        title: "预约ID不存在",
        icon: "none",
      });
      return;
    }

    wx.showModal({
      title: "取消预约",
      content: "确定要取消此次预约吗？",
      confirmText: "确定",
      confirmColor: "#FF4D4F",
      cancelText: "取消",
      success: (res) => {
        if (res.confirm) {
          // 调用云函数取消预约
          wx.cloud.callFunction({
            name: "appointmentManager",
            data: {
              type: "frontend",
              action: "cancelAppointment",
              appointmentId: appointmentId,
            },
            success: (res) => {
              console.log("取消预约结果：", res);

              if (res.result && res.result.code === 0) {
                // 取消成功
                wx.showToast({
                  title: "预约已取消",
                  icon: "success",
                });

                // 重新获取预约列表
                this.fetchUserAppointments();
              } else {
                // 取消失败
                wx.showToast({
                  title:
                    res.result && res.result.message
                      ? res.result.message
                      : "取消预约失败",
                  icon: "none",
                });
              }
            },
            fail: (err) => {
              console.error("取消预约失败：", err);

              wx.showToast({
                title: "网络错误，请重试",
                icon: "none",
              });
            },
          });
        }
      },
    });
  },

  // 查看我的预约
  navigateToMyAppointments() {
    // 检查登录状态 - 使用多重检查确保准确性
    const hasUserInfo = this.data.hasUserInfo;
    const globalUserInfo = app.globalData.userInfo;
    const globalOpenid = app.globalData.openid;

    console.log('[My] 预约页面登录状态检查:', {
      hasUserInfo,
      globalUserInfo: !!globalUserInfo,
      globalOpenid: !!globalOpenid
    });

    // 如果用户未登录，显示登录弹窗
    if (!hasUserInfo && !globalUserInfo && !globalOpenid) {
      console.log('[My] 用户未登录，显示登录弹窗');
      this.setData({
        showLoginModal: true,
        pendingAction: 'navigateToMyAppointments'
      });
      return;
    }

    // 如果全局有用户信息但本地状态未同步，先同步状态
    if (!hasUserInfo && globalUserInfo) {
      console.log('[My] 同步用户登录状态');
      this.setData({
        userInfo: globalUserInfo,
        hasUserInfo: true
      });
    }

    // 如果已经显示预约面板，则隐藏
    if (this.data.showAppointments) {
      this.setData({
        showAppointments: false,
      });

      // 更新全局状态
      app.globalData.showAppointments = false;
      return;
    }

    // 显示加载状态
    this.setData({
      loadingAppointments: true,
      showAppointments: true,
    });

    // 更新全局状态
    app.globalData.showAppointments = true;

    // 获取预约记录
    this.fetchUserAppointments();
  },

  // 导航到积分页面
  navigateToPoints() {
    // 检查登录状态 - 使用多重检查确保准确性
    const hasUserInfo = this.data.hasUserInfo;
    const globalUserInfo = app.globalData.userInfo;
    const globalOpenid = app.globalData.openid;

    console.log('[My] 积分页面登录状态检查:', {
      hasUserInfo,
      globalUserInfo: !!globalUserInfo,
      globalOpenid: !!globalOpenid
    });

    // 如果用户未登录，显示登录弹窗
    if (!hasUserInfo && !globalUserInfo && !globalOpenid) {
      console.log('[My] 用户未登录，显示登录弹窗');
      this.setData({
        showLoginModal: true,
        pendingAction: 'navigateToPoints'
      });
      return;
    }

    // 如果全局有用户信息但本地状态未同步，先同步状态
    if (!hasUserInfo && globalUserInfo) {
      console.log('[My] 同步用户登录状态');
      this.setData({
        userInfo: globalUserInfo,
        hasUserInfo: true
      });
    }

    // 导航到积分页面
    console.log("准备跳转到积分页面");
    wx.navigateTo({
      url: "/pages/my/points/points",
      fail: (err) => {
        console.error("跳转到积分页面失败:", err);
        wx.showToast({
          title: "跳转失败，请重试",
          icon: "none",
        });
      },
      success: () => {
        console.log("成功跳转到积分页面");
      },
    });
  },

  // 导航到管理员页面
  navigateToAdmin() {
    // 移除登录验证，直接进入管理员登录页面
    const app = getApp();
    
    if (app.globalData.isAdmin) {
      // 已是管理员状态，直接进入管理页面
      wx.navigateTo({
        url: "/pages/admin/admin",
      });
    } else {
      // 跳转到管理员登录页，无需验证登录状态
      wx.navigateTo({
        url: "/pages/admin/login",
      });
    }
  },

  // 导航到员工登录页面
  navigateToStaffLogin() {
    // 检查是否已经登录为员工
    const staffInfo = wx.getStorageSync("staffInfo");
    const isStaff = wx.getStorageSync("isStaff");

    if (staffInfo && isStaff) {
      // 已登录，直接跳转到员工首页，不经过登录页
      wx.navigateTo({
        url: "/pages/staff/index/index",
      });
    } else {
      // 未登录，跳转到登录页
      wx.navigateTo({
        url: "/pages/staff/login/login",
      });
    }
  },

  // 检查是否需要显示缓存提示
  checkNeedCacheTip() {
    // 直接设置为不显示
    this.setData({
      showCacheTip: false,
    });

    // 记录用户不想再看到提示
    try {
      wx.setStorageSync("cache_tip_never_show", true);
    } catch (err) {
      console.error("设置永不显示失败", err);
    }
  },

  // 关闭登录提示弹窗
  closeLoginModal() {
    this.setData({
      showLoginModal: false,
      pendingAction: null
    });
  },

  // 微信一键登录
  onGetUserInfo(e) {
    if (e.detail.userInfo) {
      // 显示加载中提示
      wx.showLoading({
        title: '登录中...',
        mask: true
      });

      // 获取到用户基本信息
      const basicUserInfo = e.detail.userInfo;

      // 设置默认值
      if (!basicUserInfo.nickName) {
        basicUserInfo.nickName = '昵称';
      }

      // 使用微信图标作为默认头像
      if (!basicUserInfo.customAvatar) {
        basicUserInfo.avatarUrl = '/static/微信图标.png';
      }

      // 临时保存基本信息到全局（后面会用云端完整数据替换）
      app.globalData.userInfo = basicUserInfo;

      // 显示已登录状态并关闭登录弹窗
      this.setData({
        userInfo: basicUserInfo,
        hasUserInfo: true,
        showLoginModal: false
      });

      // 调用云函数保存基本用户信息，同时获取用户openid
      wx.cloud.callFunction({
        name: 'userManager',
        data: {
          action: 'saveUserInfo',
          userInfo: basicUserInfo
        },
        success: res => {
          console.log('保存用户基本信息成功', res);
          if (res.result && res.result.code === 0) {
            // 保存openid到全局变量
            if (res.result.data && res.result.data.openid) {
              app.globalData.openid = res.result.data.openid;
            }

            // 获取用户完整信息（包括自定义头像等）
            this.fetchUserDetailAfterLogin();
          } else {
            // 隐藏加载提示
            wx.hideLoading();

            // 显示成功提示并执行待处理的操作
            wx.showToast({
              title: '登录成功',
              icon: 'success',
              duration: 1500,
              success: () => {
                // 延迟执行待处理的操作
                setTimeout(() => {
                  this.executePendingAction();
                }, 100);
              }
            });
          }
        },
        fail: err => {
          console.error('保存用户信息失败', err);
          // 隐藏加载提示
          wx.hideLoading();
          wx.showToast({
            title: '登录失败，请重试',
            icon: 'none'
          });
        }
      });
    } else {
      console.log('用户取消授权');
    }
  },

  // 执行待处理的操作
  executePendingAction() {
    const action = this.data.pendingAction;
    if (action) {
      console.log('[My] 执行待处理的操作:', action);

      // 清除待处理的操作
      this.setData({
        pendingAction: null
      });

      // 根据操作类型执行相应的方法
      switch (action) {
        case 'navigateToMyAppointments':
          this.navigateToMyAppointments();
          break;
        case 'navigateToPoints':
          this.navigateToPoints();
          break;
        case 'openSuggestionsModal':
          this.openSuggestionsModal();
          break;
        case 'confirmRecharge':
          // 登录后继续充值操作
          if (this.data.tempRechargeplan) {
            console.log('[My] 登录成功，继续充值操作');
            // 重新调用充值方法，此时用户已登录
            this.continueRechargeAfterLogin(this.data.tempRechargeplan);
            // 清除临时保存的充值方案
            this.setData({
              tempRechargeplan: null
            });
          }
          break;
        default:
          console.log('未知的待处理操作:', action);
      }
    }
  },

  /**
   * 登录后继续充值操作
   */
  continueRechargeAfterLogin(plan) {
    if (!plan) {
      console.error('[My] 充值方案数据为空');
      return;
    }

    // 将充值金额和赠送金额转为数字，以便计算总金额
    const price = Number(plan.price || 0);
    const bonus = Number(plan.bonus || 0);

    // 设置当前计划
    this.setData({
      currentPlan: {
        ...plan,
        price: price,
        bonus: bonus,
        total: (price + bonus).toFixed(2),
      },
    });

    // 直接调用创建充值订单，简化流程
    this.processRecharge();
  },

  // 检查版本是否变化
  checkVersionChanged() {
    // 获取当前版本
    const accountInfo = wx.getAccountInfoSync();
    const currentVersion =
      accountInfo.miniProgram.version || accountInfo.miniProgram.envVersion;

    // 获取存储的上一次版本
    const lastVersion = wx.getStorageSync("last_app_version");

    // 存储当前版本
    wx.setStorageSync("last_app_version", currentVersion);

    // 如果没有上一次版本记录，或者版本变化了，返回true
    return !lastVersion || lastVersion !== currentVersion;
  },

  // 关闭缓存提示
  closeCacheTip(e) {
    // 阻止事件冒泡，避免触发clearCache
    if (e && typeof e.stopPropagation === "function") {
      e.stopPropagation();
    }

    this.setData({
      showCacheTip: false,
    });

    // 记录用户已看到提示，短期内不再显示
    wx.setStorageSync("cache_tip_dismissed", new Date().getTime());

    // 无需显示任何提示

    return false; // 确保事件不会穿透
  },

  // 永不再显示缓存提示
  neverShowCacheTip(e) {
    // 阻止事件冒泡
    try {
      if (e && typeof e.stopPropagation === "function") {
        e.stopPropagation();
      }
    } catch (err) {
      console.log("阻止事件冒泡失败，但不影响功能", err);
    }

    // 隐藏提示
    this.setData({
      showCacheTip: false,
    });

    // 记录用户不想再看到提示
    try {
      wx.setStorageSync("cache_tip_never_show", true);
      console.log("已设置永不显示缓存提示");

      // 移除Toast提示
      // wx.showToast({
      //   title: '提示已关闭',
      //   icon: 'success',
      //   duration: 1500
      // });
    } catch (err) {
      console.error("设置永不显示失败", err);
    }

    // 返回false阻止事件冒泡
    return false;
  },

  // 清除缓存
  clearCache() {
    // 显示确认对话框
    wx.showModal({
      title: "确认重启",
      content: "确定要重启小程序吗？这将刷新所有数据。",
      confirmText: "确定",
      cancelText: "取消",
      success: (res) => {
        if (res.confirm) {
          // 用户点击确定，执行清除缓存操作
          this.performCacheClear();
        }
      },
    });
  },

  // 联系客服
  contactCustomerService() {
    // 这个方法实际上不需要做任何事情
    // 因为点击整个区域会触发button的contact事件
    // 这里只是为了保持代码结构的完整性
    console.log("联系客服按钮被点击");
  },

  // 执行缓存清理
  performCacheClear() {
    // 显示加载提示
    wx.showLoading({
      title: "重启中...",
      mask: true,
    });

    try {
      // 1. 同步清除本地存储 - 更可靠
      wx.clearStorageSync();
      console.log("本地存储清除成功");

      // 记录清除缓存的时间
      wx.setStorageSync("last_cache_clear_time", new Date().getTime());

      // 隐藏提示
      this.setData({
        showCacheTip: false,
      });

      // 2. 如果支持新版缓存管理器，尝试使用它清除所有缓存
      if (
        wx.CacheManager &&
        typeof wx.CacheManager.clearCaches === "function"
      ) {
        try {
          wx.CacheManager.clearCaches();
          console.log("CacheManager清除成功");
        } catch (e) {
          console.error("CacheManager清除失败", e);
        }
      }

      // 3. 尝试通过设置Header方式刷新网络缓存
      wx.request({
        url: "https://fake.url.to.trigger.cache.clear",
        header: {
          "Cache-Control": "no-cache, no-store",
          Pragma: "no-cache",
          Expires: "0",
        },
        complete: () => {
          console.log("网络缓存清除请求已发送");
        },
      });

      // 4. 特别处理图片缓存问题，强制刷新
      setTimeout(() => {
        // 隐藏加载提示
        wx.hideLoading();

        // 保存用户信息
        const userInfo = app.globalData.userInfo;
        const isAdmin = app.globalData.isAdmin;

        // 重新初始化app
        if (typeof app.initApp === "function") {
          app.initApp();

          // 恢复用户登录状态
          app.globalData.userInfo = userInfo;
          app.globalData.isAdmin = isAdmin;
        }

        // 显示成功提示
        wx.showToast({
          title: "重启成功",
          icon: "success",
          duration: 2000,
        });

        // 延迟后重启小程序
        setTimeout(() => {
          // 尝试使用 restartMiniProgram API (仅鸿蒙系统支持)
          if (typeof wx.restartMiniProgram === "function") {
            try {
              wx.restartMiniProgram({
                path: "/pages/launch/launch",
                success: function () {
                  console.log("小程序重启成功");
                },
                fail: function (err) {
                  console.error("小程序重启失败", err);
                  // 失败时回退到 reLaunch
                  wx.reLaunch({ url: "/pages/launch/launch" });
                },
              });
            } catch (e) {
              console.error("重启小程序出错，回退到页面重载", e);
              wx.reLaunch({ url: "/pages/launch/launch" });
            }
          } else {
            // 当前系统不支持 restartMiniProgram，使用 reLaunch 代替
            console.log("当前系统不支持重启小程序API，使用页面重载代替");
            wx.reLaunch({ url: "/pages/launch/launch" });
          }
        }, 1500);
      }, 1500);
    } catch (err) {
      console.error("重启过程中发生错误", err);
      wx.hideLoading();
      wx.showToast({
        title: "重启失败，请重试",
        icon: "none",
        duration: 2000,
      });
    }
  },

  // 处理头像点击
  onAvatarTap() {
    if (!this.data.hasUserInfo) return;

    wx.showActionSheet({
      itemList: ["从相册选择", "拍照"],
      success: (res) => {
        if (res.tapIndex === 0) {
          // 从相册选择
          this.chooseImage("album");
        } else if (res.tapIndex === 1) {
          // 拍照
          this.chooseImage("camera");
        }
      },
    });
  },

  // 官方推荐的键盘高度变化监听函数
  onKeyboardHeightChange(e) {
    const height = e.detail.height;

    // 如果高度没有变化，不做任何处理
    if (this.data.keyboardHeight === height) {
      return;
    }

    // 设置键盘高度
    this.setData({
      keyboardHeight: height,
    });

    // 如果键盘收起，延迟重置currentField，确保动画完成
    if (height === 0) {
      setTimeout(() => {
        this.setData({
          currentField: "",
        });
      }, 500); // 与CSS过渡时间一致
    }
  },

  // 以下是移除的侧滑功能相关代码
  // 保留函数接口但不执行实际功能，以兼容现有调用
  touchStart(e) {
    // 不再需要侧滑功能，函数保留但不执行操作
  },

  touchMove(e) {
    // 不再需要侧滑功能，函数保留但不执行操作
  },

  touchEnd(e) {
    // 不再需要侧滑功能，函数保留但不执行操作
  },

  // 删除预约记录
  deleteAppointment(e) {
    const id = e.currentTarget.dataset.id;

    wx.showModal({
      title: "确认删除",
      content: "确定要删除这条预约记录吗？删除后将不再显示。",
      confirmColor: "#FF4D4F",
      success: (res) => {
        if (res.confirm) {
          wx.showLoading({ title: "删除中..." });

          // 调用云函数删除预约记录
          wx.cloud.callFunction({
            name: "appointmentManager",
            data: {
              action: "deleteAppointment",
              appointmentId: id,
            },
            success: (res) => {
              wx.hideLoading();
              console.log("删除预约结果：", res);

              if (res.result && res.result.code === 0) {
                // 从本地数据中移除
                const appointments = this.data.appointments.filter(
                  (item) => item._id !== id
                );
                this.setData({ appointments });

                wx.showToast({
                  title: "删除成功",
                  icon: "success",
                });
              } else {
                wx.showToast({
                  title: res.result?.message || "删除失败",
                  icon: "none",
                });
              }
            },
            fail: (err) => {
              console.error("删除预约失败：", err);
              wx.hideLoading();
              wx.showToast({
                title: "网络错误，请重试",
                icon: "none",
              });
            },
          });
        }
      },
    });
  },

  // 显示预约记录调试信息
  showAppointmentDebugInfo(e) {
    const appointment = e.currentTarget.dataset.appointment;
    if (!appointment) {
      wx.showToast({
        title: "无法获取预约数据",
        icon: "none",
      });
      return;
    }

    // 格式化createTime为可读格式
    let formattedAppointment = { ...appointment };
    if (formattedAppointment.createTime) {
      try {
        // 尝试将createTime转换为可读日期
        const createDate = new Date(formattedAppointment.createTime);
        formattedAppointment.createTimeFormatted = createDate.toLocaleString();
      } catch (e) {
        formattedAppointment.createTimeFormatted = "无法解析的时间格式";
      }
    }

    // 将对象转换为格式化的JSON字符串
    const debugInfo = JSON.stringify(formattedAppointment, null, 2);

    console.log("预约详情：", debugInfo);

    // 显示弹窗
    wx.showModal({
      title: "预约记录详情",
      content:
        debugInfo.length > 1000
          ? debugInfo.substr(0, 1000) + "...(查看控制台获取完整信息)"
          : debugInfo,
      showCancel: false,
      confirmText: "关闭",
    });
  },

  // 管理员入口长按事件处理
  onAdminLongPress: function () {
    console.log("管理员入口长按触发");
    let that = this;

    // 显示加载提示
    wx.showLoading({
      title: "请保持长按",
      mask: true,
    });

    // 延迟8秒后显示密码输入框
    setTimeout(function () {
      // 隐藏加载提示
      wx.hideLoading();

      // 显示密码输入框
      wx.showModal({
        title: "管理员验证",
        placeholderText: "请输入管理员密码",
        editable: true,
        success: (res) => {
          if (res.confirm && res.content) {
            // 验证密码
            if (res.content === "123456") {
              // 管理员密码，实际应用中应该使用更复杂的密码
              wx.navigateTo({
                url: "/pages/admin/index/index",
              });
            } else {
              wx.showToast({
                title: "密码错误",
                icon: "none",
              });
            }
          }
        },
      });
    }, 8000); // 8秒后触发
  },

  // 员工入口长按事件处理
  onStaffLongPress: function () {
    console.log("员工入口长按触发");
    let that = this;

    // 显示加载提示
    wx.showLoading({
      title: "请保持长按",
      mask: true,
    });

    // 延迟8秒后显示密码输入框
    setTimeout(function () {
      // 隐藏加载提示
      wx.hideLoading();

      // 显示密码输入框
      wx.showModal({
        title: "员工验证",
        placeholderText: "请输入员工密码",
        editable: true,
        success: (res) => {
          if (res.confirm && res.content) {
            // 验证密码
            if (res.content === "654321") {
              // 员工密码，实际应用中应该使用更复杂的密码
              wx.navigateTo({
                url: "/pages/staff/login/login",
              });
            } else {
              wx.showToast({
                title: "密码错误",
                icon: "none",
              });
            }
          }
        },
      });
    }, 8000); // 8秒后触发
  },

  // 添加管理员入口点击处理方法
  onAdminTap() {
    const now = Date.now();
    const { adminTapCount, lastTapTime } = this.data;

    // 如果距离上次点击超过1秒，重置计数
    if (now - lastTapTime > 1000) {
      this.setData({
        adminTapCount: 1,
        lastTapTime: now,
      });
      return;
    }

    // 增加点击计数
    const newCount = adminTapCount + 1;
    this.setData({
      adminTapCount: newCount,
      lastTapTime: now,
    });

    // 达到5次连续点击，进入管理员页面
    if (newCount >= 5) {
      this.setData({ adminTapCount: 0 });
      this.navigateToAdmin();
    }
  },

  // 添加员工入口点击处理方法
  onStaffTap() {
    const now = Date.now();
    const { staffTapCount, lastTapTime } = this.data;

    // 如果距离上次点击超过1秒，重置计数
    if (now - lastTapTime > 1000) {
      this.setData({
        staffTapCount: 1,
        lastTapTime: now,
      });
      return;
    }

    // 增加点击计数
    const newCount = staffTapCount + 1;
    this.setData({
      staffTapCount: newCount,
      lastTapTime: now,
    });

    // 达到5次连续点击，进入员工页面
    if (newCount >= 5) {
      this.setData({ staffTapCount: 0 });
      this.navigateToStaffLogin();
    }
  },

  /**
   * 切换余额标签
   */
  switchBalanceTab(e) {
    const tab = e.currentTarget.dataset.tab;

    // 安全地阻止事件冒泡，先检查是否存在该方法
    if (e && typeof e.stopPropagation === "function") {
      e.stopPropagation();
    }

    // 如果点击的是当前已激活的标签，则切换折叠状态
    if (this.data.activeBalanceTab === tab) {
      this.setData({
        showBalancePanel: !this.data.showBalancePanel,
      });
    } else {
      // 如果点击的是不同的标签，则激活该标签并展开面板
      this.setData({
        activeBalanceTab: tab,
        showBalancePanel: true,
      });

      // 根据标签加载对应数据
      if (tab === "balance") {
        // 刷新余额数据
        this.fetchUserBalance();
      } else if (tab === "recharge") {
        this.fetchRechargePlans();
      } else if (tab === "record") {
        this.fetchRechargeRecords();
      }
    }
  },

  /**
   * 获取用户余额
   */
  fetchUserBalance() {
    if (!app.globalData.openid) return;

    wx.cloud
      .callFunction({
        name: "rechargeManager",
        data: {
          type: "frontend",
          action: "getUserBalance",
          openid: app.globalData.openid,
        },
      })
      .then((res) => {
        console.log("获取余额接口返回:", res.result);

        let balance = 0;
        let bonusBalance = 0;
        let totalBalance = 0;

        if (res.result && res.result.success) {
          // 新格式
          balance = parseFloat(res.result.balance || 0);
          bonusBalance = parseFloat(res.result.bonusBalance || 0);
          // 如果返回了totalBalance就使用，否则自己计算
          totalBalance =
            res.result.totalBalance !== undefined
              ? parseFloat(res.result.totalBalance)
              : balance + bonusBalance;

          console.log("解析后的余额数据:", {
            balance: balance,
            bonusBalance: bonusBalance,
            totalBalance: totalBalance,
          });
        } else if (res.result && res.result.code === 0) {
          // 旧格式
          balance = parseFloat(
            (res.result.data && res.result.data.balance) || 0
          );
          bonusBalance = parseFloat(
            (res.result.data && res.result.data.bonusBalance) || 0
          );
          totalBalance = balance + bonusBalance;
        } else {
          console.error("获取余额失败:", res);
          return;
        }

        this.setData({
          userBalance: totalBalance.toFixed(2),
          actualBalance: balance.toFixed(2),
          bonusBalance: bonusBalance.toFixed(2),
        });
      })
      .catch((err) => {
        console.error("获取余额出错:", err);
      });
  },

  /**
   * 获取充值方案列表
   */
  fetchRechargePlans() {
    this.setData({
      loadingRechargePlans: true,
    });

    wx.cloud
      .callFunction({
        name: "rechargeManager",
        data: {
          type: "frontend",
          action: "getPlans",
        },
      })
      .then((res) => {
        console.log("充值方案接口返回:", res.result);

        // 检查不同的返回格式
        let plansData = [];

        if (res.result && res.result.success) {
          // 新格式
          plansData = res.result.plans || [];
        } else if (res.result && res.result.code === 0) {
          // 旧格式
          plansData = res.result.data || [];
        } else {
          this.setData({
            rechargePlans: [],
            loadingRechargePlans: false,
          });
          console.error("获取充值方案失败:", res);
          return;
        }

        // 计算每个方案的总金额
        const plans = plansData.map((plan) => {
          // 兼容后端不同的字段命名
          const price = Number(plan.price || plan.amount || 0);
          const bonus = Number(plan.bonus || 0);

          return {
            ...plan,
            price: price,
            bonus: bonus,
            total: (price + bonus).toFixed(2),
          };
        });

        console.log("处理后的充值方案:", plans);

        this.setData({
          rechargePlans: plans,
          loadingRechargePlans: false,
        });
      })
      .catch((err) => {
        this.setData({
          rechargePlans: [],
          loadingRechargePlans: false,
        });
        console.error("获取充值方案出错:", err);
      });
  },

  /**
   * 获取充值记录
   */
  fetchRechargeRecords() {
    if (!app.globalData.openid) return;

    this.setData({
      loadingRechargeRecords: true,
    });

    wx.cloud
      .callFunction({
        name: "rechargeManager",
        data: {
          type: "frontend",
          action: "getRechargeRecords",
          openid: app.globalData.openid,
          pageSize: 50, // 增加页面大小，获取更多记录
          page: 1
        },
      })
      .then((res) => {
        console.log("充值记录接口返回:", res.result);

        let recordsData = [];

        if (res.result && res.result.success) {
          // 新格式
          recordsData = res.result.records || [];
          console.log("获取到的原始充值记录数据:", recordsData);
          console.log("记录数量:", recordsData.length);
          // 打印前3条记录的详细信息
          recordsData.slice(0, 3).forEach((record, index) => {
            console.log(`记录${index + 1}:`, {
              _id: record._id,
              orderId: record.orderId,
              status: record.status,
              verifyCode: record.verifyCode,
              amount: record.amount,
              bonus: record.bonus,
              planTitle: record.planTitle,
              createTime: record.createTime
            });
          });
        } else if (res.result && res.result.code === 0) {
          // 旧格式
          recordsData = res.result.data || [];
        } else {
          this.setData({
            rechargeRecords: [],
            loadingRechargeRecords: false,
          });
          console.error("获取充值记录失败:", res);
          return;
        }

        // 处理日期时间显示和字段映射
        const records = recordsData.map((record) => {
          // 处理日期时间
          if (record.createTime) {
            const date = new Date(record.createTime);
            record.dateStr = `${date.getFullYear()}-${(date.getMonth() + 1)
              .toString()
              .padStart(2, "0")}-${date.getDate().toString().padStart(2, "0")}`;
            record.timeStr = `${date
              .getHours()
              .toString()
              .padStart(2, "0")}:${date
              .getMinutes()
              .toString()
              .padStart(2, "0")}`;
            // 保存时间戳，用于排序
            record.timestamp = date.getTime();
          } else {
            // 如果没有创建时间，给一个很小的时间戳，确保排在最后
            record.timestamp = 0;
          }

          // 字段映射：将 amount 映射到 price 字段
          if (record.amount !== undefined && record.price === undefined) {
            record.price = record.amount;
          }

          // 确保 bonus 字段存在
          if (record.bonus === undefined) {
            record.bonus = 0;
          }

          // 计算总金额（如果需要）
          if (record.totalAmount === undefined) {
            record.totalAmount =
              parseFloat(record.price || 0) + parseFloat(record.bonus || 0);
          }

          // 处理标题显示
          if (!record.title && record.planTitle) {
            record.title = record.planTitle;
          }

          return record;
        });

        // 按时间戳排序，最新的在最上面
        records.sort((a, b) => b.timestamp - a.timestamp);

        console.log("处理后的充值记录:", records);

        this.setData({
          rechargeRecords: records,
          loadingRechargeRecords: false,
        });
      })
      .catch((err) => {
        this.setData({
          rechargeRecords: [],
          loadingRechargeRecords: false,
        });
        console.error("获取充值记录出错:", err);
      });
  },

  /**
   * 选择充值方案
   */
  selectRechargePlan(e) {
    const planId = e.currentTarget.dataset.id;
    const plan = this.data.rechargePlans.find((p) => p._id === planId);

    if (!plan) return;

    if (!app.globalData.openid) {
      wx.showToast({
        title: "请先登录",
        icon: "none",
      });
      return;
    }

    wx.showModal({
      title: "确认购买",
      content: `确定购买"${plan.title}"充值方案吗？`,
      success: (res) => {
        if (res.confirm) {
          this.createRechargeOrder(plan);
        }
      },
    });
  },

  /**
   * 创建充值订单 - 旧方法，移除加载弹窗
   */
  createRechargeOrder(plan) {
    // 移除加载弹窗，保持功能不变
    // wx.showLoading({
    //   title: "创建订单中...",
    // });

    wx.cloud
      .callFunction({
        name: "rechargeManager",
        data: {
          type: "frontend",
          action: "createRechargeOrder",
          planId: plan._id,
          openid: app.globalData.openid,
        },
      })
      .then((res) => {
        // 移除加载弹窗隐藏调用
        // wx.hideLoading();
        console.log("创建充值订单返回:", res.result);

        let verifyCode = "";
        let success = false;

        if (res.result && res.result.success) {
          // 新格式
          success = true;
          verifyCode = res.result.verifyCode || "";
        } else if (res.result && res.result.code === 0) {
          // 旧格式
          success = true;
          verifyCode = (res.result.data && res.result.data.verifyCode) || "";
        }

        if (success) {
          // 显示支付成功和充值码
          wx.showModal({
            title: "充值成功",
            content: `您已成功购买${plan.title}，充值核销码为: ${verifyCode}。请到店出示此码进行余额充值。`,
            showCancel: false,
            success: () => {
              // 刷新充值记录
              this.fetchRechargeRecords();
              // 切换到充值记录标签
              this.setData({
                activeBalanceTab: "record",
              });
            },
          });
        } else {
          wx.showToast({
            title: res.result.message || "充值失败",
            icon: "none",
          });
        }
      })
      .catch((err) => {
        // 移除加载弹窗隐藏调用
        // wx.hideLoading();
        wx.showToast({
          title: "创建充值订单失败",
          icon: "none",
        });
        console.error("创建充值订单出错:", err);
      });
  },

  /**
   * 显示充值计划详情
   */
  showRechargePlanDetail(e) {
    const planId = e.currentTarget.dataset.id;
    const plan = this.data.rechargePlans.find((p) => p._id === planId);

    if (!plan) return;

    // 将充值金额和赠送金额转为数字，以便计算总金额
    const price = Number(plan.price || 0);
    const bonus = Number(plan.bonus || 0);

    this.setData({
      currentPlan: {
        ...plan,
        price: price,
        bonus: bonus,
        total: (price + bonus).toFixed(2),
      },
      showPlanDetailModal: true,
    });
  },

  /**
   * 隐藏充值计划详情
   */
  hidePlanDetailModal() {
    this.setData({
      showPlanDetailModal: false,
    });
  },

  /**
   * 确认充值 - 简化流程，直接显示核销码弹窗
   */
  confirmRecharge(e) {
    // 安全地阻止事件冒泡，先检查是否存在该方法
    if (e && typeof e.stopPropagation === "function") {
      e.stopPropagation();
    }

    // 获取plan数据，可能来自点击事件或者详情页确认
    let plan = null;

    if (e && e.currentTarget && e.currentTarget.dataset) {
      // 从点击事件获取plan数据
      const planId = e.currentTarget.dataset.id;
      const planData = e.currentTarget.dataset.plan;

      if (planData) {
        plan = planData;
      } else if (planId) {
        plan = this.data.rechargePlans.find((p) => p._id === planId);
      }
    } else if (this.data.currentPlan) {
      // 从详情页获取plan数据
      plan = this.data.currentPlan;
    }

    if (!plan) {
      wx.showToast({
        title: "充值方案数据错误",
        icon: "none",
      });
      return;
    }

    // 检查用户是否已登录
    if (!this.data.hasUserInfo) {
      console.log('[My] 用户未登录，显示登录弹窗');
      // 保存当前充值方案，登录后继续充值
      this.setData({
        showLoginModal: true,
        pendingAction: 'confirmRecharge',
        tempRechargeplan: plan
      });
      return;
    }

    // 将充值金额和赠送金额转为数字，以便计算总金额
    const price = Number(plan.price || 0);
    const bonus = Number(plan.bonus || 0);

    // 设置当前计划并直接显示核销码弹窗
    this.setData({
      currentPlan: {
        ...plan,
        price: price,
        bonus: bonus,
        total: (price + bonus).toFixed(2),
      },
    });

    // 直接调用创建充值订单，简化流程
    this.processRecharge();
  },

  /**
   * 处理充值请求 - 直接创建订单并显示弹窗
   */
  processRecharge() {
    // 直接调用云函数创建充值订单
    wx.cloud
      .callFunction({
        name: "rechargeManager",
        data: {
          type: "frontend",
          action: "createRechargeOrder",
          planId: this.data.currentPlan._id,
          openid: app.globalData.openid,
        },
      })
      .then((res) => {
        if (res.result && res.result.success) {
          // 保存订单ID和核销码，显示弹窗
          this.setData({
            showRechargeSuccessModal: true,
            rechargeVerifyCode: res.result.verifyCode || "",
            currentRechargeOrderId: res.result.recordId || "", // 保存订单ID用于取消
          });
        } else if (res.result && res.result.code === 0) {
          // 旧格式兼容
          const verifyCode = (res.result.data && res.result.data.verifyCode) || "";

          this.setData({
            showRechargeSuccessModal: true,
            rechargeVerifyCode: verifyCode,
            currentRechargeOrderId: res.result.recordId || "",
          });
        } else {
          wx.showToast({
            title: res.result && res.result.message ? res.result.message : "充值失败",
            icon: "none",
          });
        }
      })
      .catch((err) => {
        console.error("创建充值订单失败:", err);
        wx.showToast({
          title: "网络错误，请重试",
          icon: "none",
        });
      });
  },

  /**
   * 确认充值订单 - 保留订单并关闭弹窗
   */
  confirmRechargeOrder() {
    console.log("确认充值订单，当前订单ID:", this.data.currentRechargeOrderId);

    // 关闭弹窗
    this.setData({
      showRechargeSuccessModal: false,
      rechargeVerifyCode: "",
      currentRechargeOrderId: "",
    });

    // 延迟一下再刷新充值记录，确保数据库操作完成
    setTimeout(() => {
      console.log("开始刷新充值记录...");
      this.fetchRechargeRecords();
    }, 500);

    // 显示成功提示
    wx.showToast({
      title: "充值订单已创建",
      icon: "success",
    });
  },

  /**
   * 关闭充值弹窗 - 删除临时订单，不保留任何记录
   */
  closeRechargeSuccessModal() {
    // 如果有当前订单ID，需要删除这个临时订单（用户选择关闭，不进行充值）
    if (this.data.currentRechargeOrderId) {
      wx.cloud
        .callFunction({
          name: "rechargeManager",
          data: {
            type: "frontend",
            action: "cancelRechargeOrder",
            recordId: this.data.currentRechargeOrderId,
            openid: app.globalData.openid,
          },
        })
        .then((res) => {
          console.log("删除临时充值订单结果:", res.result);
        })
        .catch((err) => {
          console.error("删除临时充值订单失败:", err);
        });
    }

    // 添加关闭动画类
    const modalElement = wx.createSelectorQuery().select('.eco-success-modal');
    if (modalElement) {
      // 添加关闭动画类
      this.setData({
        rechargeModalClosing: true
      });

      // 等待动画完成后隐藏弹窗
      setTimeout(() => {
        this.setData({
          showRechargeSuccessModal: false,
          rechargeModalClosing: false,
          rechargeVerifyCode: "",
          currentRechargeOrderId: "",
        });
      }, 400); // 与CSS动画时间保持一致
    } else {
      // 如果没有找到元素，直接关闭
      this.setData({
        showRechargeSuccessModal: false,
        rechargeVerifyCode: "",
        currentRechargeOrderId: "",
      });
    }
  },

  /**
   * 取消充值记录
   */
  cancelRechargeRecord(e) {
    const recordId = e.currentTarget.dataset.id;
    const recordTitle = e.currentTarget.dataset.title;

    if (!recordId) {
      wx.showToast({
        title: '记录ID不存在',
        icon: 'none'
      });
      return;
    }

    wx.showModal({
      title: '取消充值',
      content: `确定要取消"${recordTitle}"的充值记录吗？取消后将无法恢复。`,
      confirmText: '确定取消',
      confirmColor: '#FF4D4F',
      cancelText: '我再想想',
      success: (res) => {
        if (res.confirm) {
          // 调用云函数取消充值记录
          wx.showLoading({
            title: '取消中...'
          });

          wx.cloud.callFunction({
            name: 'rechargeManager',
            data: {
              type: 'frontend',
              action: 'cancelRechargeOrder',
              recordId: recordId,
              openid: app.globalData.openid
            }
          }).then((res) => {
            wx.hideLoading();
            console.log('取消充值记录结果:', res.result);

            if (res.result && res.result.success) {
              // 取消成功
              wx.showToast({
                title: '充值已取消',
                icon: 'success'
              });

              // 重新获取充值记录列表
              this.fetchRechargeRecords();
            } else {
              // 取消失败
              wx.showToast({
                title: res.result && res.result.message ? res.result.message : '取消失败',
                icon: 'none'
              });
            }
          }).catch((err) => {
            wx.hideLoading();
            console.error('取消充值记录失败:', err);

            wx.showToast({
              title: '网络错误，请重试',
              icon: 'none'
            });
          });
        }
      }
    });
  },

  /**
   * 隐藏充值记录（仅前端不显示，后台依然保留）
   */
  hideRechargeRecord(e) {
    const recordId = e.currentTarget.dataset.id;
    const recordTitle = e.currentTarget.dataset.title;

    if (!recordId) {
      wx.showToast({
        title: '记录ID不存在',
        icon: 'none'
      });
      return;
    }

    wx.showModal({
      title: '删除记录',
      content: `确定要删除"${recordTitle}"的充值记录吗？删除后在您的记录中将不再显示，但不影响您的账户余额。`,
      confirmText: '确定删除',
      confirmColor: '#FF4D4F',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          // 调用云函数隐藏充值记录
          wx.showLoading({
            title: '删除中...'
          });

          wx.cloud.callFunction({
            name: 'rechargeManager',
            data: {
              type: 'frontend',
              action: 'hideRechargeRecord',
              recordId: recordId,
              openid: app.globalData.openid
            }
          }).then((res) => {
            wx.hideLoading();
            console.log('隐藏充值记录结果:', res.result);

            if (res.result && res.result.success) {
              // 隐藏成功
              wx.showToast({
                title: '记录已删除',
                icon: 'success'
              });

              // 重新获取充值记录列表
              this.fetchRechargeRecords();
            } else {
              // 隐藏失败
              wx.showToast({
                title: res.result && res.result.message ? res.result.message : '删除失败',
                icon: 'none'
              });
            }
          }).catch((err) => {
            wx.hideLoading();
            console.error('隐藏充值记录失败:', err);

            wx.showToast({
              title: '网络错误，请重试',
              icon: 'none'
            });
          });
        }
      }
    });
  },

  // 获取最新的预约信息
  fetchLatestAppointment() {
    // 如果用户未登录，不获取预约记录
    if (!this.data.hasUserInfo) {
      return;
    }

    // 调用云函数获取预约记录
    wx.cloud.callFunction({
      name: "appointmentManager",
      data: {
        type: "frontend",
        action: "getUserAppointments",
      },
      success: (res) => {
        console.log("获取最新预约结果：", res);

        if (res.result && res.result.code === 0) {
          // 获取成功
          const appointments = res.result.data || [];
          this.updateLatestAppointment(appointments);
        } else {
          // 获取失败，清空最新预约
          this.setData({
            latestAppointment: null,
          });
        }
      },
      fail: (err) => {
        console.error("获取最新预约失败：", err);
        // 获取失败，清空最新预约
        this.setData({
          latestAppointment: null,
        });
      },
    });
  },

  // 解析日期字符串为兼容iOS的格式
  parseDate: function(dateStr, timeStr) {
    // 确保日期使用"/"而不是"-"，并且使用标准的ISO格式
    const datePart = dateStr.replace(/-/g, "/");
    const timePart = timeStr || "00:00:00";
    
    // 返回格式化后的日期对象
    return new Date(`${datePart} ${timePart}`);
  },

  // 更新最新预约信息
  updateLatestAppointment(appointments) {
    // 查找状态为pending或confirmed的预约
    const pendingOrConfirmed = appointments.filter(
      (item) => item.status === "pending" || item.status === "confirmed"
    );

    if (pendingOrConfirmed.length > 0) {
      // 按预约日期排序，最近的预约排在前面
      pendingOrConfirmed.sort((a, b) => {
        // 将日期和时间转换为时间戳，使用iOS兼容的格式
        const dateTimeA = this.parseDate(a.date, a.time).getTime();
        const dateTimeB = this.parseDate(b.date, b.time).getTime();

        // 升序排列（最近的在前面）
        return dateTimeA - dateTimeB;
      });

      // 设置最新预约
      this.setData({
        latestAppointment: pendingOrConfirmed[0],
      });
    } else {
      // 没有未完成的预约
      this.setData({
        latestAppointment: null,
      });
    }
  },

  // 初始化广告内容
  initAdItems() {
    // console.log("初始化广告内容");

    // 首先检查是否有预加载的广告数据
    const app = getApp();
    if (app.globalData.adLoaded) {
      // console.log("使用预加载的广告数据");
      this.setData({
        adItems: app.globalData.adItems,
        hasAdContent:
          app.globalData.hasAdContent ||
          (app.globalData.adItems && app.globalData.adItems.length > 0),
      });

      // 确保至少有两张图片才开始淡入淡出效果
      if (app.globalData.hasAdContent && app.globalData.adItems.length >= 2) {
        this.startAdFade();
      } else {
        // console.log("广告图片数量不足，无法启动淡入淡出效果");
      }
      return;
    }

    // 如果没有预加载数据，尝试使用launchManager云函数获取广告图片
    wx.cloud.callFunction({
      name: "launchManager",
      data: {
        action: "getLaunchImages",
        timestamp: Date.now(), // 添加时间戳确保不使用缓存
      },
      success: (res) => {
        console.log("获取广告图片结果：", res);

        if (res.result && res.result.code === 200 && res.result.data) {
          const launchData = res.result.data;

          // 确保first和second字段都存在
          if (launchData.first && launchData.second) {
            // 使用广告图片
            const adItems = [
              {
                imageUrl: launchData.first,
                linkUrl: "/pages/appointment/appointment",
              },
              {
                imageUrl: launchData.second,
                linkUrl: "/pages/gallery/gallery",
              },
            ];

            this.setData({
              adItems: adItems,
              hasAdContent: true,
            });
            console.log("成功设置广告图片，数量：", adItems.length);
            this.startAdFade();
          } else {
            console.log("广告图片数据不完整，尝试从contentManager获取");
            this.getAdItemsFromContentManager();
          }
        } else {
          console.log("广告图片获取失败，尝试从contentManager获取");
          // 如果广告图片获取失败，尝试从contentManager获取
          this.getAdItemsFromContentManager();
        }
      },
      fail: (err) => {
        console.error("获取广告图片失败：", err);
        // 如果广告图片获取失败，尝试从contentManager获取
        this.getAdItemsFromContentManager();
      },
    });
  },

  // 从contentManager获取广告内容
  getAdItemsFromContentManager() {
    console.log("尝试从contentManager获取广告内容");
    wx.cloud.callFunction({
      name: "contentManager",
      data: {
        action: "getPromoItems", // 暂时保留原有action名称，等云函数修改后再更新
      },
      success: (res) => {
        console.log("获取广告内容结果");

        if (
          res.result &&
          res.result.code === 0 &&
          res.result.data &&
          res.result.data.length > 0
        ) {
          // 获取成功
          const adItems = res.result.data;

          // 确保广告数据格式正确
          const validAdItems = adItems.filter((item) => item && item.imageUrl);

          if (validAdItems.length >= 1) {
            // 如果只有一张图片，复制一份确保有两张以便轮播
            if (validAdItems.length === 1) {
              console.log("只有一张广告图片，复制一份以实现轮播");
              validAdItems.push({ ...validAdItems[0] });
            }

            this.setData({
              adItems: validAdItems,
              hasAdContent: true,
            });
            console.log("成功设置广告内容");
            this.startAdFade();
          } else {
            console.log("获取的广告数据无效，使用空状态");
            this.setEmptyAdState();
          }
        } else {
          // 使用空状态
          console.log("未获取到有效广告内容，使用空状态");
          this.setEmptyAdState();
        }
      },
      fail: (err) => {
        console.error("获取广告内容失败：", err);
        // 使用空状态
        this.setEmptyAdState();
      },
    });
  },

  // 设置广告空状态
  setEmptyAdState() {
    console.log("设置广告空状态");

    this.setData({
      adItems: [],
      hasAdContent: false,
    });
    console.log("成功设置广告空状态");
  },

  // 取消最新预约
  cancelLatestAppointment() {
    if (!this.data.latestAppointment || !this.data.latestAppointment._id) {
      wx.showToast({
        title: "预约信息不存在",
        icon: "none",
      });
      return;
    }

    const appointmentId = this.data.latestAppointment._id;

    wx.showModal({
      title: "取消预约",
      content: "确定要取消此次预约吗？",
      confirmText: "确定",
      confirmColor: "#FF4D4F",
      cancelText: "取消",
      success: (res) => {
        if (res.confirm) {
          // 调用云函数取消预约
          wx.cloud.callFunction({
            name: "appointmentManager",
            data: {
              type: "frontend",
              action: "cancelAppointment",
              appointmentId: appointmentId,
            },
            success: (res) => {
              console.log("取消预约结果：", res);

              if (res.result && res.result.code === 0) {
                // 取消成功
                wx.showToast({
                  title: "预约已取消",
                  icon: "success",
                });
                
                // 清除本地存储的预约标记
                wx.removeStorageSync('hasActiveAppointment');
                // 清除全局状态
                if (getApp().globalData) {
                  getApp().globalData.hasActiveAppointment = false;
                }

                // 重新获取预约列表和最新预约
                this.fetchUserAppointments();
                this.fetchLatestAppointment();
              } else {
                // 取消失败
                wx.showToast({
                  title:
                    res.result && res.result.message
                      ? res.result.message
                      : "取消失败",
                  icon: "none",
                });
              }
            },
            fail: (err) => {
              console.error("取消预约失败：", err);

              wx.showToast({
                title: "网络错误，请重试",
                icon: "none",
              });
            },
          });
        }
      },
    });
  },

  // 修改最新预约
  modifyLatestAppointment() {
    if (!this.data.latestAppointment) {
      wx.showToast({
        title: "预约信息不存在",
        icon: "none",
      });
      return;
    }

    // 设置全局变量，标记修改模式
    const app = getApp();
    app.globalData.isModifyingAppointment = true;
    app.globalData.appointmentToModify = this.data.latestAppointment;

    // 跳转到预约页面
    wx.switchTab({
      url: "/pages/appointment/appointment",
    });
  },

  // 处理广告点击
  handleAdTap(e) {
    const url = e.currentTarget.dataset.url;

    if (!url) {
      return;
    }

    // 判断链接类型
    if (url.startsWith("/pages/")) {
      // 内部页面链接
      if (
        url.includes("pages/index/") ||
        url.includes("pages/gallery/") ||
        url.includes("pages/appointment/") ||
        url.includes("pages/my/")
      ) {
        // TabBar页面使用switchTab
        wx.switchTab({
          url: url,
        });
      } else {
        // 非TabBar页面使用navigateTo
        wx.navigateTo({
          url: url,
        });
      }
    } else if (url.startsWith("http")) {
      // 外部网页链接，使用web-view
      wx.navigateTo({
        url: `/pages/webview/webview?url=${encodeURIComponent(url)}`,
      });
    }
  },

  startAdFade() {
    if (this.data.adItems.length <= 1) {
      console.log("广告项数量不足，无法轮播");
      return;
    }

    // 简化日志，只在启动时输出一次
    // console.log("启动广告轮播效果");
    this.stopAdFade(); // 先停止现有的定时器，避免重复

    const fadeInTime = 1000; // ms，淡入时间
    const showTime = 3000; // ms，显示时间
    const fadeOutTime = 1000; // ms，淡出时间

    const that = this;

    function fade() {
      // 移除每次切换时的日志输出

      // 淡出
      that.setData({ adFadeOpacity: 0 });

      // 等待淡出完成后切换图片并淡入
      setTimeout(() => {
        // 切换到下一张图片
        const nextIndex =
          (that.data.adFadeIndex + 1) % that.data.adItems.length;

        that.setData({
          adFadeIndex: nextIndex,
          adFadeOpacity: 0, // 确保先设为透明
        });

        // 延迟一点点时间后开始淡入，确保新图片已经加载
        setTimeout(() => {
          // 淡入
          that.setData({ adFadeOpacity: 1 });

          // 在显示时间结束后继续下一次循环
          that.data.adFadeTimer = setTimeout(fade, showTime + fadeInTime);
        }, 100);
      }, fadeOutTime);
    }

    // 初始设置为不透明
    that.setData({ adFadeOpacity: 1 });

    // 等待显示时间后开始第一次淡入淡出
    that.data.adFadeTimer = setTimeout(fade, showTime);
  },

  stopAdFade() {
    if (this.data.adFadeTimer) {
      clearTimeout(this.data.adFadeTimer);
      this.setData({ adFadeTimer: null });
    }
  },

  /**
   * 处理局部区域下拉刷新事件
   */
  onAreaRefresh() {
    console.log("局部区域下拉刷新触发，刷新广告内容");

    // 设置刷新状态
    this.setData({
      refreshing: true,
    });

    // 停止当前广告轮播
    this.stopAdFade();

    // 刷新广告内容
    wx.cloud.callFunction({
      name: "launchManager",
      data: {
        action: "getLaunchImages",
        timestamp: Date.now(), // 添加时间戳确保不使用缓存
      },
      success: (res) => {
        console.log("刷新广告内容结果:", res);

        if (res.result && res.result.code === 200 && res.result.data) {
          const launchData = res.result.data;

          // 确保图片数据存在
          if (launchData.first && launchData.second) {
            // 添加时间戳防止缓存
            const timestamp = Date.now();
            const adItems = [
              {
                imageUrl: launchData.first + "?t=" + timestamp,
                linkUrl: "/pages/appointment/appointment",
              },
              {
                imageUrl: launchData.second + "?t=" + timestamp,
                linkUrl: "/pages/gallery/gallery",
              },
            ];

            // 更新页面数据
            this.setData({
              adItems: adItems,
              hasAdContent: true,
              adFadeIndex: 0,
              adFadeOpacity: 1,
            });

            // 更新全局数据
            const app = getApp();
            app.globalData.adItems = adItems;
            app.globalData.hasAdContent = true;

            console.log("广告内容刷新成功");

            // 重新启动广告轮播
            this.startAdFade();
          } else {
            // 尝试从另一个接口获取
            this.getAdFromContentManager();
          }
        } else {
          // 尝试从另一个接口获取
          this.getAdFromContentManager();
        }
      },
      fail: (err) => {
        console.error("广告刷新失败:", err);
        // 尝试从另一个接口获取
        this.getAdFromContentManager();
      },
      complete: () => {
        // 延迟结束刷新状态，给用户一个明显的反馈
        setTimeout(() => {
          this.setData({
            refreshing: false,
          });
        }, 800);
      },
    });
  },

  /**
   * 从ContentManager获取广告数据
   */
  getAdFromContentManager() {
    wx.cloud.callFunction({
      name: "contentManager",
      data: {
        action: "getPromoItems",
        timestamp: Date.now(),
      },
      success: (res) => {
        if (
          res.result &&
          res.result.code === 0 &&
          res.result.data &&
          res.result.data.length > 0
        ) {
          // 获取成功
          const adItems = res.result.data.map((item) => {
            // 为图片添加时间戳
            if (item.imageUrl) {
              const separator = item.imageUrl.includes("?") ? "&" : "?";
              item.imageUrl = item.imageUrl + separator + "t=" + Date.now();
            }
            return item;
          });

          // 确保至少有两个广告项
          if (adItems.length === 1) {
            adItems.push({ ...adItems[0] });
          }

          // 更新数据
          this.setData({
            adItems: adItems,
            hasAdContent: true,
            adFadeIndex: 0,
            adFadeOpacity: 1,
          });

          // 更新全局数据
          const app = getApp();
          app.globalData.adItems = adItems;
          app.globalData.hasAdContent = true;

          // 重新启动广告轮播
          this.startAdFade();
        } else {
          // 设置为空状态
          this.setData({
            adItems: [],
            hasAdContent: false,
          });

          // 更新全局数据
          const app = getApp();
          app.globalData.adItems = [];
          app.globalData.hasAdContent = false;
        }
      },
      fail: (err) => {
        console.error("从ContentManager获取广告失败:", err);
        // 设置为空状态
        this.setData({
          adItems: [],
          hasAdContent: false,
        });

        // 更新全局数据
        const app = getApp();
        app.globalData.adItems = [];
        app.globalData.hasAdContent = false;
      },
    });
  },

  // ==================== 投诉建议功能 ====================

  /**
   * 切换投诉建议面板显示状态
   */
  toggleSuggestions() {
    const newState = !this.data.showSuggestions;
    this.setData({
      showSuggestions: newState,
    });

    // 如果展开面板，加载对话记录
    if (newState) {
      this.loadConversations();
    }
  },

  /**
   * 拨打客服电话
   */
  makePhoneCall() {
    // 先从云端获取配置的电话号码
    wx.cloud.callFunction({
      name: "configManager",
      data: {
        action: "getConfig",
        key: "customerServicePhone",
      },
      success: (res) => {
        let phoneNumber = "************"; // 默认电话号码

        if (res.result && res.result.code === 0 && res.result.data) {
          phoneNumber = res.result.data.value || phoneNumber;
        }

        wx.makePhoneCall({
          phoneNumber: phoneNumber,
          success: () => {
            console.log("拨打电话成功");
          },
          fail: (err) => {
            console.error("拨打电话失败:", err);
            wx.showToast({
              title: "拨打失败，请重试",
              icon: "none",
            });
          },
        });
      },
      fail: (err) => {
        console.error("获取客服电话失败:", err);
        // 使用默认电话号码
        wx.makePhoneCall({
          phoneNumber: "************",
          success: () => {
            console.log("拨打电话成功");
          },
          fail: (err) => {
            console.error("拨打电话失败:", err);
            wx.showToast({
              title: "拨打失败，请重试",
              icon: "none",
            });
          },
        });
      },
    });
  },

  /**
   * 复制微信号
   */
  copyWechat() {
    // 先从云端获取配置的微信号
    wx.cloud.callFunction({
      name: "configManager",
      data: {
        action: "getConfig",
        key: "customerServiceWechat",
      },
      success: (res) => {
        let wechatId = "your_wechat_id"; // 默认微信号

        if (res.result && res.result.code === 0 && res.result.data) {
          wechatId = res.result.data.value || wechatId;
        }

        wx.setClipboardData({
          data: wechatId,
          success: () => {
            wx.showToast({
              title: "微信号已复制",
              icon: "success",
            });
          },
          fail: (err) => {
            console.error("复制失败:", err);
            wx.showToast({
              title: "复制失败，请重试",
              icon: "none",
            });
          },
        });
      },
      fail: (err) => {
        console.error("获取客服微信号失败:", err);
        // 使用默认微信号
        wx.setClipboardData({
          data: "your_wechat_id",
          success: () => {
            wx.showToast({
              title: "微信号已复制",
              icon: "success",
            });
          },
          fail: (err) => {
            console.error("复制失败:", err);
            wx.showToast({
              title: "复制失败，请重试",
              icon: "none",
            });
          },
        });
      },
    });
  },

  /**
   * 建议内容输入
   */
  onSuggestionInput(e) {
    this.setData({
      suggestionText: e.detail.value,
    });
  },

  /**
   * 联系电话输入
   */
  onPhoneInput(e) {
    let value = e.detail.value;

    // 只允许输入数字
    value = value.replace(/[^\d]/g, "");

    // 限制最多11位
    if (value.length > 11) {
      value = value.substring(0, 11);
    }

    this.setData({
      contactPhone: value,
    });
  },

  /**
   * 验证手机号码格式
   */
  validatePhoneNumber(phone) {
    if (!phone) return true; // 手机号是可选的，空值也是有效的

    // 中国手机号码正则表达式
    const phoneRegex = /^1[3-9]\d{9}$/;
    return phoneRegex.test(phone);
  },

  /**
   * 切换匿名状态
   */
  toggleAnonymous(e) {
    this.setData({
      isAnonymous: e.detail.value,
    });
  },

  /**
   * 选择图片 - 直接从相册或相机选择
   */
  chooseImage() {
    wx.chooseMedia({
      count: 9 - (this.data.uploadedImages ? this.data.uploadedImages.length : 0), // 最多9张图片
      mediaType: ["image"],
      sourceType: ["album", "camera"],
      success: (res) => {
        this.uploadFiles(res.tempFiles, "image");
      },
      fail: (err) => {
        if (err.errMsg !== "chooseMedia:fail cancel") {
          wx.showToast({
            title: "选择图片失败",
            icon: "none",
          });
        }
      },
    });
  },

  // 移除了chooseVideo函数，不再支持视频上传

  /**
   * 上传文件
   */
  uploadFiles(files, type) {
    // 不显示上传提示，静默上传
    console.log(`准备上传${files.length}个文件`);

    // 逐个上传图片，每张图片作为单独的消息发送
    const processNextFile = (index) => {
      // 所有文件处理完成
      if (index >= files.length) {
        console.log("所有文件上传完成");
        return;
      }

      const file = files[index];
      const timestamp = new Date().getTime();
      const fileExt = file.tempFilePath.substring(
        file.tempFilePath.lastIndexOf(".")
      );
      
      // 不显示上传进度提示
      console.log(`开始上传第${index + 1}个文件，路径:`, file.tempFilePath);
      
      // 生成云存储路径
      const cloudPath = `suggestions/${type}s/${timestamp}_${index}${fileExt}`;
      
      // 上传单个文件
      wx.cloud.uploadFile({
        cloudPath,
        filePath: file.tempFilePath,
        success: (res) => {
          console.log(`第${index + 1}个文件上传成功:`, res.fileID);
          
          // 上传成功，发送这个文件作为单独一条消息
          this.sendSingleMediaMessage(res.fileID, type);
          
          // 继续处理下一个文件
          setTimeout(() => {
            processNextFile(index + 1);
          }, 800); // 增加延迟，确保消息有足够时间发送
        },
        fail: (err) => {
          console.error(`第${index + 1}个文件上传失败:`, err);
          
          // 继续处理下一个文件
          setTimeout(() => {
            processNextFile(index + 1);
          }, 500);
        }
      });
    };
    
    // 开始处理第一个文件
    processNextFile(0);
  },

  /**
   * 发送单个媒体文件消息（图片或视频）
   */
  sendSingleMediaMessage(fileID, type) {
    // 准备发送数据
    const messageData = {
      content: "[图片]", // 为媒体消息添加默认文本内容，避免内容为空的错误
      userInfo: this.data.userInfo
    };
    
    // 设置媒体类型
    if (type === "image") {
      messageData.images = [fileID];
      messageData.videos = [];
    } else if (type === "video") {
      messageData.content = "[视频]"; // 为视频添加特定的默认文本
      messageData.images = [];
      messageData.videos = [fileID];
    }
    
    console.log("发送媒体消息:", messageData);
    
    // 调用云函数发送消息
    wx.cloud.callFunction({
      name: "suggestionManager",
      data: {
        action: "submitSuggestion",
        data: messageData
      },
      success: (res) => {
        if (res.result && res.result.code === 0) {
          console.log("媒体消息发送成功");
          
          // 强制立即重新加载对话记录以显示新消息
          setTimeout(() => {
            this.loadConversations();
          }, 500);
        } else {
          console.error("发送媒体消息失败:", res.result?.message);
        }
      },
      fail: (err) => {
        console.error("发送媒体消息失败:", err);
      }
    });
  },

  /**
   * 移除图片
   */
  removeImage(e) {
    const index = e.currentTarget.dataset.index;
    const images = [...this.data.uploadedImages];
    images.splice(index, 1);
    this.setData({
      uploadedImages: images,
    });
  },

  /**
   * 移除视频
   */
  removeVideo(e) {
    const index = e.currentTarget.dataset.index;
    const videos = [...this.data.uploadedVideos];
    videos.splice(index, 1);
    this.setData({
      uploadedVideos: videos,
    });
  },

  /**
   * 提交建议
   */
  submitSuggestion() {
    console.log("=== 提交建议按钮被点击 ===");

    // 先显示一个提示，确认方法被调用
    wx.showToast({
      title: "按钮点击成功",
      icon: "success",
      duration: 1000,
    });

    console.log("开始提交建议...");

    const {
      suggestionText,
      contactPhone,
      isAnonymous,
      uploadedImages,
      uploadedVideos,
    } = this.data;

    console.log("表单数据:", {
      suggestionText,
      contactPhone,
      isAnonymous,
      uploadedImages,
      uploadedVideos,
      hasUserInfo: this.data.hasUserInfo,
      userInfo: this.data.userInfo,
    });

    if (!suggestionText.trim()) {
      wx.showToast({
        title: "请输入建议内容",
        icon: "none",
      });
      return;
    }

    // 检查用户登录状态
    if (!this.data.hasUserInfo) {
      wx.showToast({
        title: "请先登录",
        icon: "none",
      });
      return;
    }

    // 移除加载提示，保持功能但不显示弹窗
    // wx.showLoading({
    //   title: "提交中...",
    //   mask: true,
    // });

    // 准备提交数据
    const suggestionData = {
      content: suggestionText.trim(),
      contactPhone: isAnonymous ? "" : contactPhone,
      isAnonymous,
      images: uploadedImages,
      videos: uploadedVideos,
      userInfo: isAnonymous ? null : this.data.userInfo,
    };

    console.log("准备提交的数据:", suggestionData);

    // 调用云函数提交建议
    wx.cloud.callFunction({
      name: "suggestionManager",
      data: {
        action: "submitSuggestion",
        data: suggestionData,
      },
      success: (res) => {
        // 移除加载提示隐藏
        // wx.hideLoading();
        console.log("云函数调用成功:", res);

        if (res.result && res.result.code === 0) {
          wx.showToast({
            title: "提交成功",
            icon: "success",
          });

          // 清空表单
          this.setData({
            suggestionText: "",
            contactPhone: "",
            isAnonymous: false,
            uploadedImages: [],
            uploadedVideos: [],
          });

          // 重新加载对话记录
          this.loadConversations();
        } else {
          console.error("提交失败:", res.result);
          wx.showToast({
            title: res.result?.message || "提交失败",
            icon: "none",
          });
        }
      },
      fail: (err) => {
        // 移除加载提示隐藏
        // wx.hideLoading();
        console.error("提交建议失败:", err);
        wx.showToast({
          title: "网络错误，请重试",
          icon: "none",
        });
      },
    });
  },

  /**
   * 加载对话记录
   */
  loadConversations() {
    if (!this.data.hasUserInfo) {
      console.log("未登录状态，不加载对话记录");
      return;
    }
    console.log("开始加载对话记录...");

    // 不显示加载提示，静默加载
    wx.cloud.callFunction({
      name: "suggestionManager",
      data: {
        action: "getConversations",
      },
      success: (res) => {
        if (res.result && res.result.code === 0) {
          const conversations = res.result.data || [];
          
          console.log(`成功获取${conversations.length}条对话记录`);

          // 格式化时间并设置默认媒体折叠状态
          conversations.forEach((item) => {
            if (item.createTime) {
              const date = new Date(item.createTime);
              item.createTime = `${date.getMonth() + 1}-${date.getDate()} ${date
                .getHours()
                .toString()
                .padStart(2, "0")}:${date
                .getMinutes()
                .toString()
                .padStart(2, "0")}`;
            }
            // 默认所有消息的媒体内容都是折叠状态
            item.showMedia = false;
          });

          // 计算未读消息数量（管理员回复的消息）
          let unreadCount = 0;
          for (let i = conversations.length - 1; i >= 0; i--) {
            if (conversations[i].sender === "admin") {
              unreadCount++;
            } else {
              break; // 遇到用户消息就停止计算
            }
          }

          this.setData({
            conversations,
            unreadCount,
          });

          console.log("对话记录已更新到界面");

          // 滚动到最新消息
          if (conversations.length > 0) {
            const lastMsgId = `msg-${
              conversations[conversations.length - 1].id
            }`;
            this.setData({
              scrollToView: lastMsgId,
            });
            console.log("滚动到最新消息:", lastMsgId);
          }
        } else {
          console.error("获取对话记录失败:", res.result?.message);
          wx.showToast({
            title: res.result?.message || "获取对话失败",
            icon: "none",
          });
        }
      },
      fail: (err) => {
        console.error("调用云函数获取对话记录失败:", err);
        wx.showToast({
          title: "加载失败，请重试",
          icon: "none",
        });
      },
    });
  },

  /**
   * 回复内容输入
   */
  onReplyInput(e) {
    this.setData({
      replyText: e.detail.value,
    });
  },

  /**
   * 发送回复
   */
  sendReply() {
    const { replyText } = this.data;

    if (!replyText.trim()) {
      wx.showToast({
        title: "请输入回复内容",
        icon: "none",
      });
      return;
    }

    wx.showLoading({
      title: "发送中...",
      mask: true,
    });

    wx.cloud.callFunction({
      name: "suggestionManager",
      data: {
        action: "sendReply",
        data: {
          content: replyText.trim(),
          userInfo: this.data.userInfo,
        },
      },
      success: (res) => {
        wx.hideLoading();

        if (res.result && res.result.code === 0) {
          // 清空输入框
          this.setData({
            replyText: "",
          });

          wx.showToast({
            title: "发送成功",
            icon: "success",
          });

          // 重新加载对话记录
          this.loadConversations();
        } else {
          wx.showToast({
            title: res.result?.message || "发送失败",
            icon: "none",
          });
        }
      },
      fail: (err) => {
        console.error("发送回复失败:", err);
        wx.showToast({
          title: "网络错误，请重试",
          icon: "none",
        });
      },
    });
  },

  /**
   * 预览图片
   */
  previewImage(e) {
    const { url, urls } = e.currentTarget.dataset;
    wx.previewImage({
      current: url,
      urls: urls || [url],
    });
  },

  /**
   * 打开投诉建议弹窗
   */
  openSuggestionsModal() {
    // 如果用户未登录，显示登录弹窗
    if (!this.data.hasUserInfo) {
      console.log('[My] 用户未登录，显示登录弹窗');
      this.setData({
        showLoginModal: true,
        pendingAction: 'openSuggestionsModal'
      });
      return;
    }

    // 先显示弹窗（但保持在底部隐藏状态）
    this.setData({
      showSuggestionsModal: true,
      messageText: '', // 重置消息文本
      inputFocus: false // 初始不聚焦
    });

    // 隐藏TabBar导航栏
    console.log('[My] 投诉建议弹窗打开，隐藏导航栏');
    this.hideTabBar();

    // 加载对话记录
    this.loadConversations();

    // 延时触发向上滑入动画
    setTimeout(() => {
      this.setData({
        showSuggestionsAnimation: true // 触发向上滑入动画
      });
    }, 50); // 短暂延时确保DOM已渲染
  },

  /**
   * 关闭投诉建议弹窗
   */
  closeSuggestionsModal() {
    // 添加关闭动画，让弹窗向下滑出
    this.setData({
      isClosingSuggestions: true // 设置关闭状态，触发向下滑出动画
    });

    // 等待动画完成后再隐藏弹窗
    setTimeout(() => {
      this.setData({
        showSuggestionsModal: false,
        isClosingSuggestions: false,
        showSuggestionsAnimation: false // 重置动画状态
      });

      // 显示TabBar导航栏
      console.log('[My] 投诉建议弹窗关闭，显示导航栏');
      this.showTabBar();
    }, 300); // 等待300ms动画完成
  },

  // 隐藏TabBar导航栏
  hideTabBar() {
    try {
      if (typeof this.getTabBar === 'function' && this.getTabBar()) {
        this.getTabBar().hide();
        console.log('[My] TabBar已隐藏');
      }
    } catch (error) {
      console.error('[My] 隐藏TabBar失败:', error);
    }
  },

  // 显示TabBar导航栏
  showTabBar() {
    try {
      if (typeof this.getTabBar === 'function' && this.getTabBar()) {
        this.getTabBar().show();
        console.log('[My] TabBar已显示');
      }
    } catch (error) {
      console.error('[My] 显示TabBar失败:', error);
    }
  },

  // ==================== 聊天相关方法 ====================

  /**
   * 聚焦输入框
   */
  focusInput() {
    // 获取输入框组件并聚焦
    const query = wx.createSelectorQuery();
    query.select('#chatInput').boundingClientRect();
    query.selectViewport().scrollOffset();
    query.exec(function(res) {
      if (res && res[0]) {
        wx.nextTick(() => {
          // 使用延迟确保DOM已更新
          wx.createSelectorQuery()
            .select('#chatInput')
            .context(function(ctx) {
              if (ctx && ctx.context) {
                ctx.context.focus();
              }
            })
            .exec();
        });
      }
    });
  },

  /**
   * 消息输入处理
   */
  onMessageInput(e) {
    const value = e.detail.value;
    const isSendBtnActive = value && value.trim().length > 0;
    
    this.setData({
      messageText: value,
      sendBtnActive: isSendBtnActive // 发送按钮状态变量
    });
  },

  /**
   * 输入框聚焦
   */
  onInputFocus(e) {
    // 聚焦时滚动到最新消息
    if (this.data.conversations.length > 0) {
      const lastMessageId = `msg-${this.data.conversations[this.data.conversations.length - 1].id}`;
      this.setData({
        scrollToView: lastMessageId
      });
    }
    
    // 检查发送按钮状态
    const value = this.data.messageText || '';
    if (value.trim().length > 0 && !this.data.sendBtnActive) {
      this.setData({
        sendBtnActive: true
      });
    }
  },

  /**
   * 输入框失焦
   */
  onInputBlur(e) {
    // 失焦时记录当前文本，以便在再次聚焦时重新设置
    this.setData({
      inputFocus: false
    });
  },

  // 移除了toggleMoreActions和toggleEmoji函数，不再需要

  /**
   * 发送消息
   */
  sendMessage() {
    const { messageText } = this.data;

    if (!messageText.trim()) {
      wx.showToast({
        title: '请输入消息内容',
        icon: 'none'
      });
      return;
    }

    // 检查用户登录状态
    if (!this.data.hasUserInfo) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({
      title: '发送中...',
      mask: true
    });

    // 准备发送数据
    const messageData = {
      content: messageText.trim(),
      userInfo: this.data.userInfo,
      images: [], // 暂时为空，后续可扩展
      videos: []  // 暂时为空，后续可扩展
    };

    // 调用云函数发送消息
    wx.cloud.callFunction({
      name: 'suggestionManager',
      data: {
        action: 'submitSuggestion',
        data: messageData
      },
      success: (res) => {
        wx.hideLoading();

        if (res.result && res.result.code === 0) {
          // 清空输入框
          this.setData({
            messageText: '',
            sendBtnActive: false // 重置按钮状态
          });

          wx.showToast({
            title: '发送成功',
            icon: 'success'
          });

          // 重新加载对话记录
          this.loadConversations();
        } else {
          wx.showToast({
            title: res.result?.message || '发送失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        wx.hideLoading();
        console.error('发送消息失败:', err);
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      }
    });
  },
  /**
   * 切换回复标签页
   */
  switchReplyTab(e) {
    const tab = e.currentTarget.dataset.tab;
    this.setData({
      activeReplyTab: tab,
    });
  },

  /**
   * 切换消息媒体文件显示状态
   */
  toggleMessageMedia(e) {
    const messageId = e.currentTarget.dataset.id;
    const conversations = [...this.data.conversations];

    // 找到对应的消息并切换显示状态
    const messageIndex = conversations.findIndex(
      (item) => item.id === messageId
    );
    if (messageIndex !== -1) {
      conversations[messageIndex].showMedia =
        !conversations[messageIndex].showMedia;
      this.setData({
        conversations: conversations,
      });
    }
  },

  // 设置指向广告事件监听
  setupTargetAdListener() {
    const app = getApp();
    if (app.globalData.eventCenter) {
      app.globalData.eventCenter.on('showTargetAd', (data) => {
        console.log('[My] 收到显示指向广告事件:', data);

        // 只在当前页面是目标页面时显示广告
        const currentRoute = 'pages/my/my';
        if (!data.targetPage || data.targetPage === currentRoute) {
          this.setData({
            showTargetAd: true,
            targetAdData: {
              imageUrl: data.imageUrl || '',
              jumpUrl: data.jumpUrl || ''
            }
          });
        }
      });

      // 监听全局关闭广告事件
      app.globalData.eventCenter.on('closeTargetAdGlobally', () => {
        console.log('[My] 收到全局关闭广告事件');
        this.setData({
          showTargetAd: false,
          targetAdData: {
            imageUrl: '',
            jumpUrl: ''
          }
        });
      });

      // 监听显示充值详情事件（来自指向广告跳转）
      app.globalData.eventCenter.on('showRechargeDetail', (data) => {
        console.log('[My] 收到显示充值详情事件，直接显示核销码弹窗:', data);
        if (data.position && data.amount) {
          // 新的位置+金额方式
          this.showRechargeDetailByPosition(data.position, data.amount);
        } else if (data.planId) {
          // 兼容旧的ID方式
          this.showRechargeDetailById(data.planId);
        }
      });
    }
  },

  // 指向广告关闭事件
  onTargetAdClose() {
    console.log('[My] 指向广告关闭');
    this.setData({
      showTargetAd: false,
      targetAdData: {
        imageUrl: '',
        jumpUrl: ''
      }
    });
  },

  // 根据位置显示充值详情（新的灵活方式）
  showRechargeDetailByPosition(position, amount) {
    console.log(`[My] 尝试显示第${position}个充值方案，金额: ${amount}元`);

    const { rechargePlans } = this.data;

    // 确保充值方案已加载
    if (!rechargePlans || rechargePlans.length === 0) {
      console.log('[My] 充值方案未加载，先加载数据');
      this.fetchRechargePlans();
      // 延迟重试
      setTimeout(() => {
        this.showRechargeDetailByPosition(position, amount);
      }, 1000);
      return;
    }

    // 尝试按位置查找
    let plan = null;
    if (rechargePlans.length >= position) {
      plan = rechargePlans[position - 1]; // 数组索引从0开始，位置从1开始
      console.log('[My] 按位置找到充值方案:', plan.title);
    }

    // 如果没找到，尝试按金额匹配
    if (!plan) {
      plan = rechargePlans.find(item =>
        Number(item.price || item.amount || 0) === Number(amount)
      );
      if (plan) {
        console.log('[My] 通过金额匹配找到充值方案:', plan.title);
      }
    }

    if (plan) {
      console.log('[My] 找到充值方案，显示详情弹窗:', {
        _id: plan._id,
        title: plan.title,
        price: plan.price,
        position: position
      });

      // 切换到充值标签页
      this.setData({
        activeBalanceTab: 'recharge',
        showBalancePanel: true
      });

      // 延迟一下再直接显示充值核销码弹窗，跳过详情弹窗
      setTimeout(() => {
        this.directShowRechargeModal(plan);
      }, 300);
    } else {
      console.warn(`[My] 未找到第${position}个充值方案或金额为${amount}元的方案`);
      console.log('[My] 当前充值方案列表长度:', rechargePlans.length);

      wx.showToast({
        title: '充值方案不存在',
        icon: 'none'
      });
    }
  },

  // 根据ID显示充值详情（兼容旧方式）
  showRechargeDetailById(planId) {
    console.log(`[My] 尝试显示充值方案ID: ${planId}`);

    const { rechargePlans } = this.data;

    // 确保充值方案已加载
    if (!rechargePlans || rechargePlans.length === 0) {
      console.log('[My] 充值方案未加载，先加载数据');
      this.fetchRechargePlans();
      // 延迟重试
      setTimeout(() => {
        this.showRechargeDetailById(planId);
      }, 1000);
      return;
    }

    const plan = rechargePlans.find(item => item._id === planId);

    if (plan) {
      console.log('[My] 找到充值方案，显示详情弹窗:', plan.title);

      // 切换到充值标签页
      this.setData({
        activeBalanceTab: 'recharge',
        showBalancePanel: true
      });

      // 延迟一下再直接显示充值核销码弹窗，跳过详情弹窗
      setTimeout(() => {
        this.directShowRechargeModal(plan);
      }, 300);
    } else {
      console.warn(`[My] 未找到充值方案ID: ${planId}`);

      wx.showToast({
        title: '充值方案不存在',
        icon: 'none'
      });
    }
  },

  // 直接显示充值核销码弹窗（用于指向广告跳转）
  directShowRechargeModal(plan) {
    console.log('[My] 直接显示充值核销码弹窗:', plan.title);

    const app = getApp();

    // 检查用户是否已登录
    if (!app.globalData.openid) {
      wx.showModal({
        title: "请先登录",
        content: "您需要登录后才能进行充值操作",
        cancelText: "取消",
        confirmText: "立即登录",
        confirmColor: "#07C160",
        success: (res) => {
          if (res.confirm) {
            this.getUserProfile();
          }
        },
      });
      return;
    }

    // 将充值金额和赠送金额转为数字
    const price = Number(plan.price || 0);
    const bonus = Number(plan.bonus || 0);

    // 设置当前计划
    this.setData({
      currentPlan: {
        ...plan,
        price: price,
        bonus: bonus,
        total: (price + bonus).toFixed(2),
      },
    });

    // 直接调用创建充值订单
    wx.cloud
      .callFunction({
        name: "rechargeManager",
        data: {
          type: "frontend",
          action: "createRechargeOrder",
          planId: plan._id,
          openid: app.globalData.openid,
        },
      })
      .then((res) => {
        if (res.result && res.result.success) {
          // 直接显示核销码弹窗
          this.setData({
            showRechargeSuccessModal: true,
            rechargeVerifyCode: res.result.verifyCode || "",
            currentRechargeOrderId: res.result.recordId || "",
          });
        } else if (res.result && res.result.code === 0) {
          // 旧格式兼容
          const verifyCode = (res.result.data && res.result.data.verifyCode) || "";

          this.setData({
            showRechargeSuccessModal: true,
            rechargeVerifyCode: verifyCode,
            currentRechargeOrderId: res.result.recordId || "",
          });
        } else {
          wx.showToast({
            title: res.result && res.result.message ? res.result.message : "充值失败",
            icon: "none",
          });
        }
      })
      .catch((err) => {
        console.error("创建充值订单失败:", err);
        wx.showToast({
          title: "网络错误，请重试",
          icon: "none",
        });
      });
  }
});

