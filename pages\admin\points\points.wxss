/* pages/admin/points/points.wxss */

.points-admin-container {
  min-height: 100vh;
  background-color: #f8f8f8;
  padding-bottom: 30rpx;
}

/* 顶部标题栏 */
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  padding-top: 90rpx; /* 增加顶部内边距，确保在胶囊按钮下方 */
  background-color: #fff;
  border-bottom: 1rpx solid #eee;
  position: sticky;
  top: 0;
  z-index: 100;
}

.back-icon {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  color: #333333;
}

.title {
  flex: 1;
  text-align: center;
  font-size: 34rpx;
  font-weight: bold;
  color: #333333;
}

.refresh-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.refresh-icon {
  width: 40rpx;
  height: 40rpx;
}

/* 标签栏 */
.tabs {
  display: flex;
  background-color: #fff;
  border-bottom: 1rpx solid #eee;
}

.tab {
  flex: 1;
  text-align: center;
  padding: 30rpx 0;
  font-size: 28rpx;
  color: #666;
  position: relative;
}

.tab.active {
  color: #333;
  font-weight: bold;
}

.tab.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 6rpx;
  background-color: #333;
  border-radius: 3rpx;
}

/* 标签内容 */
.tab-content {
  display: none;
  padding: 30rpx;
}

.tab-content.active {
  display: block;
}

/* 加载中 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #333;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #999;
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 表单样式 */
.settings-form {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.form-item {
  margin-bottom: 30rpx;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.form-input {
  width: 100%;
  height: 80rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
  color: #333333; /* 添加文字颜色 */
}

.ratio-container {
  display: flex;
  align-items: center;
}

.ratio-input {
  width: 200rpx;
  margin-right: 20rpx;
}

.ratio-text {
  font-size: 28rpx;
  color: #333;
}

.action-button {
  background-color: #333;
  color: #fff;
  font-size: 32rpx;
  padding: 20rpx 0;
  border-radius: 8rpx;
  text-align: center;
  margin-top: 30rpx;
}

/* 列表样式 */
.withdrawals-list, .records-list {
  background-color: #fff;
  border-radius: 12rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.list-header {
  display: flex;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #eee;
  background-color: #f8f8f8;
  font-weight: bold;
}

.header-cell {
  flex: 1;
  text-align: center;
  font-size: 26rpx;
  color: #333;
}

/* 调整金额列的样式 */
.header-cell:nth-child(3), .item-cell:nth-child(3) {
  color: #07c160;
  font-weight: bold;
}

.withdrawal-item, .record-item {
  display: flex;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #eee;
}

.item-cell {
  flex: 1;
  text-align: center;
  font-size: 26rpx;
  color: #333;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 状态样式 */
.status-pending {
  color: #ff9500;
}

.status-completed {
  color: #07c160;
}

.status-rejected {
  color: #ff3b30;
}

/* 操作按钮 */
.actions {
  display: flex;
  justify-content: center;
  gap: 10rpx;
}

.action-btn {
  padding: 10rpx 20rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
  color: #fff;
  margin: 0 6rpx;
  font-weight: bold;
  box-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.action-btn:active {
  transform: scale(0.95);
  box-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.2);
}

.view-btn {
  background-color: #007aff;
}

.approve-btn {
  background-color: #07c160;
}

.reject-btn {
  background-color: #ff3b30;
}

/* 弹窗样式 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
  position: relative;
  width: 80%;
  max-width: 600rpx;
  background-color: #fff;
  border-radius: 12rpx;
  overflow: hidden;
  z-index: 1001;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.modal-close {
  font-size: 40rpx;
  color: #999;
}

.modal-body {
  padding: 30rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.detail-item {
  margin-bottom: 20rpx;
}

.detail-label {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 10rpx;
  display: block;
}

.detail-value {
  font-size: 28rpx;
  color: #333;
}

/* 管理员备注输入框样式 */
.admin-remark {
  width: 100%;
  height: 120rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  padding: 16rpx;
  font-size: 28rpx;
  color: #333333; /* 添加文字颜色 */
  background-color: #ffffff; /* 确保背景为白色 */
  box-sizing: border-box;
}

/* 添加placeholder样式 */
.admin-remark::placeholder {
  color: #999999; /* placeholder文字颜色 */
}

.qrcode-image {
  width: 300rpx;
  height: 300rpx;
  margin-top: 10rpx;
}

/* 收款码容器 */
.qrcode-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 10rpx;
}

/* 收款码提示文字 */
.qrcode-tip {
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
  text-align: center;
  background-color: #f5f5f5;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  border: 1rpx solid #e0e0e0;
}

/* 改进弹窗中的按钮样式 */
.modal-footer {
  display: flex;
  border-top: 1rpx solid #eee;
}

.modal-btn {
  flex: 1;
  text-align: center;
  padding: 30rpx 0;
  font-size: 30rpx;
  position: relative;
}

/* 改进按钮样式 */
.btn-text {
  font-weight: bold;
  font-size: 32rpx;
}

.approve-btn {
  background-color: #07c160;
  color: #fff;
}

.reject-btn {
  background-color: #ff3b30;
  color: #fff;
}

/* 积分统计容器 */
.stats-container {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.stats-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  padding-bottom: 15rpx;
  border-bottom: 1rpx solid #eee;
}

.stats-content {
  display: flex;
  justify-content: space-around;
}

.stats-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stats-label {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.stats-value {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

/* 总金额颜色特殊处理 */
.stats-item:nth-child(3) .stats-value {
  color: #07c160;
} 