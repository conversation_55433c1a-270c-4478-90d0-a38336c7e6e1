.launch-admin-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
  position: relative;
  height: 100vh;
  overflow-y: scroll;
  -webkit-overflow-scrolling: touch;
}

.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  padding-top: 90rpx;
  background-color: #ffffff;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  position: sticky;
  top: 0;
  z-index: 100;
}

.back-icon {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  color: #333333;
}

.header-title {
  font-size: 34rpx;
  font-weight: bold;
  color: #333333;
  flex: 1;
  text-align: center;
}

.loading-container {
  padding: 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.loading-spinner {
  width: 80rpx;
  height: 80rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #ff9a9e;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 20rpx;
  color: #999999;
  font-size: 28rpx;
}

.launch-content {
  flex: 1;
  padding: 30rpx;
}

.upload-tip {
  font-size: 28rpx;
  color: #666666;
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 20rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  line-height: 1.5;
}

/* 数据来源信息样式 */
.data-source-info {
  display: flex;
  align-items: center;
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 20rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.data-source-label {
  font-size: 28rpx;
  color: #666666;
  margin-right: 20rpx;
}

.data-source-value {
  font-size: 28rpx;
  font-weight: bold;
  color: #333333;
}

.image-section {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 20rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 20rpx;
}

.image-uploader {
  width: 100%;
  height: 400rpx;
  background-color: #f5f5f5;
  border-radius: 12rpx;
  overflow: hidden;
  position: relative;
}

.preview-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.upload-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.upload-icon {
  font-size: 50rpx;
  color: #999999;
  margin-bottom: 10rpx;
}

.upload-text {
  font-size: 26rpx;
  color: #999999;
}

.upload-progress {
  margin-top: 15rpx;
}

.progress-bar {
  width: 100%;
  height: 6rpx;
  background-color: #f0f0f0;
  border-radius: 3rpx;
  overflow: hidden;
}

.progress-inner {
  height: 100%;
  background: linear-gradient(to right, #ff9a9e, #fad0c4);
  transition: width 0.3s;
}

.progress-text {
  font-size: 24rpx;
  color: #999999;
  text-align: right;
  margin-top: 5rpx;
}

.upload-status {
  display: flex;
  align-items: center;
  margin-top: 15rpx;
}

.status-icon {
  color: #4CAF50;
  font-size: 30rpx;
  margin-right: 10rpx;
}

.status-text {
  font-size: 26rpx;
  color: #4CAF50;
}

/* 帮助部分样式 */
.help-section {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 20rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.help-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 15rpx;
}

.help-content {
  font-size: 26rpx;
  color: #666666;
}

.help-item {
  margin-bottom: 10rpx;
  line-height: 1.5;
}

/* 空状态样式 */
.empty-state {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 80%;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 34rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 10rpx;
}

.empty-subtext {
  font-size: 28rpx;
  color: #999999;
  text-align: center;
}

/* 底部安全区域，确保内容不被遮挡 */
.safe-bottom-area {
  height: 40rpx;
  width: 100%;
} 