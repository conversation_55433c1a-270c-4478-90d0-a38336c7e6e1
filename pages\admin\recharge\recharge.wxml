<view class="recharge-admin-container">
  <!-- 状态栏安全区域 -->
  <view class="status-bar"></view>
  
  <!-- 导航栏 -->
  <view class="nav-header">
    <view class="back-btn" bindtap="navigateBack">
      <view class="arrow-left"></view>
    </view>
    <view class="header-title">充值管理</view>
    <view class="placeholder"></view>
  </view>
  
  <!-- 主内容区域 -->
  <view class="admin-content">
    <view class="menu-container">
      <block wx:for="{{menuList}}" wx:key="id">
        <view class="menu-item {{item.id}}" bindtap="navigateToFunction" data-url="{{item.url}}">
          <image class="menu-icon" src="{{item.icon}}" mode="aspectFit"></image>
          <view class="menu-name">{{item.name}}</view>
        </view>
      </block>
    </view>
  </view>
</view> 