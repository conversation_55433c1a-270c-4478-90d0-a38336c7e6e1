// 系统设置管理页面
Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 加载状态
    loading: true,
    
    // 管理员信息
    adminInfo: {
      username: '',
      role: 'admin'
    },
    
    // 密码修改表单
    passwordForm: {
      oldPassword: '',
      newPassword: '',
      confirmPassword: ''
    },
    
    // 显示控制
    showAdminSettings: false,
    showAboutSystem: false,
    
    // 管理员设置解锁相关
    adminSettingsLocked: true,
    adminSettingsTapCount: 0
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function(options) {
    console.log('系统设置页面加载');
    
    // 初始化云函数环境
    if (!wx.cloud) {
      console.error('请使用 2.2.3 或以上的基础库以使用云能力');
      wx.showToast({
        title: '请升级微信版本',
        icon: 'none'
      });
      return;
    }
    
    // 初始化管理员账户
    this.initAdminAccount();
    
    // 获取系统设置
    this.fetchSettings();
    
    // 获取管理员信息
    this.fetchAdminInfo();
  },
  
  /**
   * 初始化管理员账户
   */
  initAdminAccount: function() {
    console.log('开始初始化管理员账户');
    
    wx.cloud.callFunction({
      name: 'adminManager',
      data: {
        action: 'initAdmin',
        username: 'admin',
        password: 'admin123'
      },
      success: res => {
        console.log('初始化管理员账户结果：', res);
        if (res && res.result && res.result.code === 0) {
          console.log('初始化管理员账户成功');
        } else {
          console.error('初始化管理员账户失败：', res);
        }
      },
      fail: err => {
        console.error('初始化管理员账户失败：', err);
      }
    });
  },
  
  /**
   * 获取系统设置
   */
  fetchSettings: function() {
    console.log('开始获取系统设置');
    this.setData({ loading: true });
    
    // 简化为直接设置loading为false，不再获取复杂的设置
    setTimeout(() => {
      this.setData({ loading: false });
    }, 500);
  },
  
  /**
   * 获取管理员信息
   */
  fetchAdminInfo: function() {
    wx.cloud.callFunction({
      name: 'adminManager',
      data: {
        action: 'getAdminInfo'
      },
      success: res => {
        if (res && res.result && res.result.code === 0 && res.result.data) {
          this.setData({
            adminInfo: res.result.data
          });
        }
      },
      fail: err => {
        console.error('获取管理员信息失败：', err);
      }
    });
  },

  /**
   * 刷新系统设置
   */
  refreshSettings: function() {
    this.fetchSettings();
    this.fetchAdminInfo();
    
    wx.showToast({
      title: '刷新成功',
      icon: 'success'
    });
  },

  /**
   * 切换部分展示/隐藏
   */
  toggleSection: function(e) {
    const section = e.currentTarget.dataset.section;
    const key = 'show' + section.charAt(0).toUpperCase() + section.slice(1);
    
    if (section === 'adminSettings') {
      if (!this.data.adminSettingsLocked) {
        this.setData({
          [key]: !this.data[key]
        });
        return;
      }
      
      let newCount = this.data.adminSettingsTapCount + 1;
      
      if (newCount >= 8) {
        this.setData({
          adminSettingsLocked: false,
          adminSettingsTapCount: 0,
          showAdminSettings: true
        });
      } else {
        this.setData({
          adminSettingsTapCount: newCount
        });
      }
    } else {
      this.setData({
        [key]: !this.data[key]
      });
    }
  },

  /**
   * 返回上一页
   */
  goBack: function() {
    wx.navigateBack();
  },
  
  /**
   * 处理输入变化 - 密码表单
   */
  onInputChange: function(e) {
    const field = e.currentTarget.dataset.field;
    const value = e.detail.value;
    
    this.setData({
      [`passwordForm.${field}`]: value
    });
  },
  
  /**
   * 修改密码
   */
  changePassword: function() {
    const { oldPassword, newPassword, confirmPassword } = this.data.passwordForm;
    
    // 验证表单
    if (!oldPassword || !newPassword || !confirmPassword) {
      wx.showToast({
        title: '请填写完整表单',
        icon: 'none'
      });
      return;
    }
    
    if (newPassword !== confirmPassword) {
      wx.showToast({
        title: '两次输入的新密码不一致',
        icon: 'none'
      });
      return;
    }
    
    if (newPassword.length < 6) {
      wx.showToast({
        title: '密码长度至少为6位',
        icon: 'none'
      });
      return;
    }
    
    // 检查密码是否包含字母和数字
    const hasLetter = /[a-zA-Z]/.test(newPassword);
    const hasNumber = /[0-9]/.test(newPassword);
    
    if (!hasLetter || !hasNumber) {
      wx.showToast({
        title: '密码必须包含字母和数字',
        icon: 'none'
      });
      return;
    }
    
    // 显示加载提示
    wx.showLoading({
      title: '修改中...',
      mask: true
    });
    
    // 调用云函数修改密码
    wx.cloud.callFunction({
      name: 'adminManager',
      data: {
        action: 'changePassword',
        oldPassword: oldPassword,
        newPassword: newPassword
      },
      success: res => {
        wx.hideLoading();
        console.log('修改密码返回结果：', res);
        
        if (res.result && res.result.code === 0) {
          wx.showToast({
            title: '密码修改成功',
            icon: 'success'
          });
          
          // 清空表单
          this.setData({
            passwordForm: {
              oldPassword: '',
              newPassword: '',
              confirmPassword: ''
            }
          });
        } else {
          // 显示详细错误信息
          const errorMsg = res.result?.message || '密码修改失败';
          
          wx.showModal({
            title: '修改失败',
            content: errorMsg,
            showCancel: false
          });
        }
      },
      fail: err => {
        wx.hideLoading();
        console.error('修改密码失败：', err);
        
        // 检查是否是集合不存在错误
        if (err.errMsg && err.errMsg.includes('collection not exists')) {
          // 尝试初始化管理员账户
          this.initAdminAccount();
          
          wx.showModal({
            title: '系统提示',
            content: '首次使用需要初始化管理员账户，请稍后再试。默认账户：admin，默认密码：admin123',
            showCancel: false
          });
        } else {
          wx.showModal({
            title: '网络错误',
            content: '请检查网络连接并重试',
            showCancel: false
          });
        }
      }
    });
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function() {
    this.resetAdminSettingsLock();
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function() {
    this.resetAdminSettingsLock();
  },
  
  /**
   * 重置管理员设置锁定状态
   */
  resetAdminSettingsLock: function() {
    this.setData({
      adminSettingsLocked: true,
      adminSettingsTapCount: 0,
      showAdminSettings: false
    });
  }
}); 