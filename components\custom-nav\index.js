Component({
  properties: {
    title: {
      type: String,
      value: ''
    },
    forceHidden: {
      type: Boolean,
      value: false
    }
  },

  data: {
    visible: false,
    lastScrollTop: 0,
    isForceHidden: false
  },

  observers: {
    'forceHidden': function(hidden) {
      this.setData({ isForceHidden: hidden });
    }
  },

  methods: {
    // 处理页面滚动
    handleScroll(e) {
      if (this.data.isForceHidden) return;
      
      const scrollTop = e.detail.scrollTop;
      const lastScrollTop = this.data.lastScrollTop;
      
      // 向上滚动且超过一定距离时显示导航栏
      if (scrollTop > lastScrollTop && scrollTop > 100) {
        this.setData({ visible: true });
      }
      // 向下滚动时隐藏导航栏
      else if (scrollTop < lastScrollTop) {
        this.setData({ visible: false });
      }
      
      this.setData({ lastScrollTop: scrollTop });
    },

    // 强制显示导航栏
    show() {
      if (!this.data.isForceHidden) {
        this.setData({ visible: true });
      }
    },

    // 强制隐藏导航栏
    hide() {
      this.setData({ visible: false });
    }
  }
}); 