/**
 * 视频列表模块
 * 负责视频列表数据加载、分页、刷新逻辑和视频数据处理
 * @version 1.0.0
 */

const BaseModule = require("./base-module");
const videoUtils = require("../utils/video-utils");
const {
  PAGINATION,
  LOADING_CONFIG,
  VIDEO_CONFIG,
  ERROR_TYPES,
  PAGE_EVENTS,
  VIDEO_EVENTS,
} = require("../constants/index-constants");

class VideoListModule extends BaseModule {
  constructor(pageContext) {
    super(pageContext);
    this.moduleName = "VideoList";

    // 引入视频工具函数
    this.videoUtils = require("../../../utils/video/index");
    this.getVideoList = this.videoUtils.getVideoList;
    this.getVideoUrl = this.videoUtils.getVideoUrl;
    this.cleanExpiredCache = this.videoUtils.cleanExpiredCache;
  }

  /**
   * 初始化视频列表模块
   */
  init() {
    try {
      // 减少视频列表模块初始化日志
      // console.log("[VideoList] 初始化视频列表模块");

      // 初始化列表状态
      this.initListState();

      this.initialized = true;
      // 减少视频列表模块初始化完成日志
      // console.log("[VideoList] 视频列表模块初始化完成");
    } catch (error) {
      this.handleError(error, "init");
    }
  }

  /**
   * 初始化列表状态
   */
  initListState() {
    try {
      const currentData = this.data;

      // 确保必要的状态字段存在
      const requiredFields = {
        videoList: currentData.videoList || [],
        loading: currentData.loading || false,
        firstLoading:
          currentData.firstLoading !== undefined
            ? currentData.firstLoading
            : true,
        hasMore: currentData.hasMore !== undefined ? currentData.hasMore : true,
        page: currentData.page || PAGINATION.DEFAULT_PAGE,
        pageSize: currentData.pageSize || PAGINATION.PAGE_SIZE,
        lastRefreshTime: currentData.lastRefreshTime || 0,
      };

      // 只更新缺失的字段
      const updateData = {};
      Object.keys(requiredFields).forEach((key) => {
        if (currentData[key] === undefined) {
          updateData[key] = requiredFields[key];
        }
      });

      if (Object.keys(updateData).length > 0) {
        this.safeSetData(updateData);
      }

      // 减少列表状态初始化完成日志
      // console.log("[VideoList] 列表状态初始化完成");
    } catch (error) {
      this.handleError(error, "initListState");
    }
  }

  /**
   * 加载视频列表
   * @param {boolean} refresh - 是否为刷新操作
   * @returns {Promise} 加载结果
   */
  loadVideoList(refresh = false) {
    const that = this;

    if (this.data.loading) {
      // 减少重复请求日志
      // console.log("[VideoList] 正在加载中，忽略重复请求");
      return Promise.resolve();
    }

    // 只在刷新时输出日志
    if (refresh) {
      console.log("[VideoList] 开始刷新视频列表");
    }

    return new Promise((resolve, reject) => {
      that.safeSetData({ loading: true });

      if (refresh) {
        that.safeSetData({
          page: PAGINATION.DEFAULT_PAGE,
          lastRefreshTime: Date.now(),
        });
      }

      // 使用优化后的视频加载函数
      that
        .getVideoList(refresh)
        .then((serverVideoList) => {
          // 减少获取成功日志
          // console.log(
          //   "[VideoList] 获取视频列表成功, 数量:",
          //   serverVideoList ? serverVideoList.length : 0
          // );

          if (
            serverVideoList &&
            Array.isArray(serverVideoList) &&
            serverVideoList.length > 0
          ) {
            // 确保服务器返回的视频列表没有重复项
            serverVideoList = that.deduplicateVideoList(serverVideoList);

            // 只处理当前页需要的视频
            const startIndex = that.data.page * that.data.pageSize;
            const endIndex = startIndex + that.data.pageSize;
            const currentPageVideos = serverVideoList.slice(
              startIndex,
              endIndex
            );

            // 减少当前页视频数量日志
            // console.log(
            //   "[VideoList] 当前页视频数量:",
            //   currentPageVideos.length
            // );

            // 处理视频列表数据
            that
              .processVideoList(currentPageVideos, serverVideoList, refresh)
              .then((newVideoList) => {
                resolve(newVideoList);
              })
              .catch((error) => {
                that.handleLoadError(error);
                reject(error);
              });
          } else {
            // 没有数据时的处理
            that.handleEmptyData();
            resolve([]);
          }
        })
        .catch((error) => {
          console.error("[VideoList] 加载视频列表失败:", error);
          that.handleLoadError(error);
          reject(error);
        });
    });
  }

  /**
   * 处理视频列表数据
   * @param {Array} currentPageVideos - 当前页视频数据
   * @param {Array} serverVideoList - 服务器完整视频列表
   * @param {boolean} refresh - 是否为刷新操作
   * @returns {Promise} 处理结果
   */
  processVideoList(currentPageVideos, serverVideoList, refresh) {
    const that = this;

    return new Promise((resolve, reject) => {
      // 批量处理视频数据，优化性能
      that
        .batchProcessVideos(currentPageVideos)
        .then((processedVideos) => {
          // 更新状态
          const hasMore =
            that.data.page * that.data.pageSize + processedVideos.length <
            serverVideoList.length;

          let newVideoList = refresh
            ? processedVideos // 如果是刷新，直接替换整个列表
            : that.data.videoList.concat(processedVideos); // 否则添加到现有列表后

          // 确保合并后的列表没有重复项
          if (!refresh && that.data.videoList.length > 0) {
            newVideoList = that.deduplicateVideoList(newVideoList);
          }

          // 只在刷新时输出更新日志
          if (refresh) {
            console.log("[VideoList] 刷新完成, 视频数量:", newVideoList.length);
          }

          // 确保数据更新 - 关键修复：强制重置所有加载状态
          const updateData = {
            videoList: newVideoList,
            loading: false,           // 强制停止加载状态
            firstLoading: false,      // 强制停止首次加载状态
            hasMore: hasMore,
            page: refresh ? 0 : that.data.page + 1,
            isRefreshing: false,      // 强制停止刷新状态
            showContent: true,        // 强制显示内容
          };

          // 如果是刷新，保存原始列表以供搜索使用
          if (refresh) {
            updateData.originalVideoList = serverVideoList;

            // 同时更新全局数据，确保其他页面可以使用
            const app = getApp();
            if (app && app.globalData) {
              app.globalData.videoList = serverVideoList;
            }
          }

          // 减少页面数据更新日志
          // console.log("[VideoList] 准备更新页面数据:", {
          //   videoCount: newVideoList.length,
          //   showContent: updateData.showContent,
          //   loading: updateData.loading,
          //   firstLoading: updateData.firstLoading
          // });

          // 立即更新数据，不等待回调
          that.safeSetData(updateData);
          
          // 减少页面数据更新完成日志
          // console.log("[VideoList] 页面数据更新完成");
          
          // 缓存处理后的视频数据
          that.cacheProcessedVideos(newVideoList);

          // 触发视频列表更新事件
          that.emit(VIDEO_EVENTS.VIDEO_LIST_UPDATE, {
            videoList: newVideoList,
            refresh: refresh,
          });

          // 立即resolve，不等待nextTick
          resolve(newVideoList);
        })
        .catch((error) => {
          console.error("[VideoList] 处理视频数据失败:", error);
          
          // 即使处理失败，也要确保页面状态正确
          that.safeSetData({
            loading: false,
            firstLoading: false,
            showContent: true,
            isRefreshing: false
          });
          
          reject(error);
        });
    });
  }

  /**
   * 批量处理视频数据，优化性能
   * @param {Array} videoList - 视频列表
   * @returns {Promise} 处理结果
   */
  batchProcessVideos(videoList) {
    const that = this;

    return new Promise((resolve, reject) => {
      if (!Array.isArray(videoList) || videoList.length === 0) {
        resolve([]);
        return;
      }

      // 先检查缓存中是否有URL
      const urlCache = that.data.urlCache || {};
      const urlFetchingIds = that.data.urlFetchingIds || [];
      const needFetchUrls = [];
      const processedVideos = [];

      // 分类处理：有缓存的直接处理，没有缓存的批量获取
      videoList.forEach((item) => {
        const cachedUrl = urlCache[item.id];

        if (cachedUrl && cachedUrl.url && !that.isUrlCacheExpired(cachedUrl)) {
          // 使用缓存的URL
          processedVideos.push(
            that.sanitizeVideoData(
              Object.assign({}, item, {
                videoUrl: cachedUrl.url,
              })
            )
          );
        } else if (!urlFetchingIds.includes(item.id)) {
          // 需要获取URL的视频
          needFetchUrls.push(item);
        } else {
          // 正在获取中，使用空URL
          processedVideos.push(
            that.sanitizeVideoData(
              Object.assign({}, item, {
                videoUrl: "",
              })
            )
          );
        }
      });

      // 如果没有需要获取URL的视频，直接返回
      if (needFetchUrls.length === 0) {
        resolve(processedVideos);
        return;
      }

      // 批量获取视频URL
      that
        .batchFetchVideoUrls(needFetchUrls)
        .then((urlResults) => {
          // 处理获取到URL的视频
          needFetchUrls.forEach((item, index) => {
            const videoUrl = urlResults[index] || "";
            processedVideos.push(
              that.sanitizeVideoData(
                Object.assign({}, item, {
                  videoUrl: videoUrl,
                })
              )
            );
          });

          resolve(processedVideos);
        })
        .catch((error) => {
          console.error("[VideoList] 批量获取视频URL失败:", error);

          // 即使获取失败，也要返回处理后的数据
          needFetchUrls.forEach((item) => {
            processedVideos.push(
              that.sanitizeVideoData(
                Object.assign({}, item, {
                  videoUrl: "",
                })
              )
            );
          });

          resolve(processedVideos);
        });
    });
  }

  /**
   * 批量获取视频URL
   * @param {Array} videoList - 需要获取URL的视频列表
   * @returns {Promise} URL数组
   */
  batchFetchVideoUrls(videoList) {
    const that = this;

    return new Promise((resolve) => {
      if (!Array.isArray(videoList) || videoList.length === 0) {
        resolve([]);
        return;
      }

      // 更新正在获取的ID列表
      const fetchingIds = videoList.map((item) => item.id);
      const currentFetchingIds = that.data.urlFetchingIds || [];
      that.safeSetData({
        urlFetchingIds: [...currentFetchingIds, ...fetchingIds],
      });

      // 并发获取所有视频URL，但限制并发数量
      const concurrencyLimit = 5; // 限制并发数量，避免请求过多
      const urlPromises = [];

      for (let i = 0; i < videoList.length; i += concurrencyLimit) {
        const batch = videoList.slice(i, i + concurrencyLimit);
        const batchPromises = batch.map((item) => {
          return that
            .fetchSingleVideoUrl(item)
            .then((url) => ({ success: true, url: url }))
            .catch((error) => {
              console.warn(
                `[VideoList] 获取视频 ${item.id} URL失败:`,
                error.message || error
              );
              return { success: false, url: "" };
            });
        });

        urlPromises.push(Promise.all(batchPromises));
      }

      Promise.all(urlPromises).then((batchResults) => {
        // 合并所有批次的结果
        const allResults = batchResults.flat();
        const urls = allResults.map((result) => result.url);

        // 更新URL缓存
        that.updateUrlCache(videoList, urls);

        // 清理正在获取的ID列表
        const updatedFetchingIds = that.data.urlFetchingIds.filter(
          (id) => !fetchingIds.includes(id)
        );
        that.safeSetData({
          urlFetchingIds: updatedFetchingIds,
        });

        resolve(urls);
      });
    });
  }

  /**
   * 获取单个视频URL
   * @param {object} videoItem - 视频项
   * @returns {Promise} 视频URL
   */
  fetchSingleVideoUrl(videoItem) {
    const that = this;

    return new Promise((resolve, reject) => {
      if (getApp() && getApp().globalData && getApp().globalData.debugMode) {
        console.log(`[VideoList] 开始获取视频 ${videoItem.id} 的URL`);
      }

      // 优先使用完整的fileKey获取视频URL
      that
        .getVideoUrl(videoItem.id, videoItem.baseId, videoItem.fileKey)
        .then((videoUrl) => {
          if (
            getApp() &&
            getApp().globalData &&
            getApp().globalData.debugMode
          ) {
            console.log(`[VideoList] 成功获取视频 ${videoItem.id} 的URL`);
          }
          resolve(videoUrl);
        })
        .catch((error) => {
          reject(error);
        });
    });
  }

  /**
   * 更新URL缓存
   * @param {Array} videoList - 视频列表
   * @param {Array} urls - 对应的URL列表
   */
  updateUrlCache(videoList, urls) {
    try {
      const urlCache = this.data.urlCache || {};
      const timestamp = Date.now();

      videoList.forEach((item, index) => {
        if (urls[index]) {
          urlCache[item.id] = {
            url: urls[index],
            timestamp: timestamp,
            baseId: item.baseId,
            fileKey: item.fileKey,
          };
        }
      });

      this.safeSetData({ urlCache: urlCache });

      // 同时使用数据管理器缓存到本地存储
      if (this.page.dataManager) {
        const urlMap = {};
        videoList.forEach((item, index) => {
          if (urls[index]) {
            urlMap[item.id] = urls[index];
          }
        });
        this.page.dataManager.batchCacheVideoUrls(urlMap);
      }

      console.log(`[VideoList] 更新URL缓存，数量: ${videoList.length}`);
    } catch (error) {
      this.handleError(error, "updateUrlCache");
    }
  }

  /**
   * 检查URL缓存是否过期
   * @param {object} cacheItem - 缓存项
   * @returns {boolean} 是否过期
   */
  isUrlCacheExpired(cacheItem) {
    if (!cacheItem || !cacheItem.timestamp) {
      return true;
    }

    const now = Date.now();
    const age = now - cacheItem.timestamp;
    const expireTime = VIDEO_CONFIG.URL_FETCH_TIMEOUT || 60 * 60 * 1000; // 1小时

    return age > expireTime;
  }

  /**
   * 缓存处理后的视频数据
   * @param {Array} videoList - 视频列表
   */
  cacheProcessedVideos(videoList) {
    try {
      if (this.page.dataManager) {
        this.page.dataManager.cacheVideoList(videoList, {
          expireTime: LOADING_CONFIG.CACHE_EXPIRE_TIME,
        });
      }

      console.log(
        `[VideoList] 缓存处理后的视频数据，数量: ${videoList.length}`
      );
    } catch (error) {
      this.handleError(error, "cacheProcessedVideos");
    }
  }

  /**
   * 预加载视频URL
   * @param {Array} videoList - 视频列表
   * @param {number} preloadCount - 预加载数量
   */
  preloadVideoUrls(videoList, preloadCount = VIDEO_CONFIG.PRELOAD_COUNT) {
    try {
      if (!Array.isArray(videoList) || videoList.length === 0) {
        return;
      }

      const needPreload = videoList.slice(0, preloadCount).filter((item) => {
        const urlCache = this.data.urlCache || {};
        const cachedUrl = urlCache[item.id];
        return !cachedUrl || this.isUrlCacheExpired(cachedUrl);
      });

      if (needPreload.length > 0) {
        console.log(`[VideoList] 预加载视频URL，数量: ${needPreload.length}`);
        this.batchFetchVideoUrls(needPreload)
          .then(() => {
            console.log("[VideoList] 预加载完成");
          })
          .catch((error) => {
            console.warn("[VideoList] 预加载失败:", error);
          });
      }
    } catch (error) {
      this.handleError(error, "preloadVideoUrls");
    }
  }

  /**
   * 清理过期的URL缓存
   */
  cleanExpiredUrlCache() {
    try {
      const urlCache = this.data.urlCache || {};
      const cleanedCache = {};
      let cleanedCount = 0;

      Object.keys(urlCache).forEach((videoId) => {
        const cacheItem = urlCache[videoId];
        if (!this.isUrlCacheExpired(cacheItem)) {
          cleanedCache[videoId] = cacheItem;
        } else {
          cleanedCount++;
        }
      });

      if (cleanedCount > 0) {
        this.safeSetData({ urlCache: cleanedCache });
        console.log(`[VideoList] 清理过期URL缓存，数量: ${cleanedCount}`);
      }
    } catch (error) {
      this.handleError(error, "cleanExpiredUrlCache");
    }
  }

  /**
   * 处理加载错误
   * @param {Error} error - 错误对象
   */
  handleLoadError(error) {
    this.safeSetData({
      loading: false,
      firstLoading: false,
      isRefreshing: false,
      showContent: true, // 即使失败也显示内容区域
    });

    this.handleError(error, "loadVideoList");
  }

  /**
   * 处理空数据情况
   */
  handleEmptyData() {
    console.log("[VideoList] 未获取到视频数据或数据为空");
    this.safeSetData({
      videoList: [],
      loading: false,
      firstLoading: false,
      hasMore: false,
      isRefreshing: false,
      showContent: true, // 确保内容显示
    });
  }

  /**
   * 处理下拉刷新
   * @returns {Promise} 刷新结果
   */
  handleRefresh() {
    console.log("[VideoList] 下拉刷新");

    // 如果已经在刷新中，不要重复操作
    if (this.data.isRefreshing || this.data.loading) {
      console.log("[VideoList] 已经在刷新中，忽略重复请求");
      wx.stopPullDownRefresh();
      return Promise.resolve();
    }

    this.safeSetData({ isRefreshing: true });

    // 强制清除视频列表缓存，确保获取最新数据
    wx.removeStorageSync("video_list_cache");

    // 强制重新加载视频列表
    return this.loadVideoList(true)
      .then(() => {
        console.log("[VideoList] 下拉刷新完成");

        // 设置isRefreshing为false，允许滚动视图回弹
        this.safeSetData({ isRefreshing: false });

        // 使用延时确保动画顺畅
        setTimeout(() => {
          wx.stopPullDownRefresh();
        }, 300);
      })
      .catch((error) => {
        console.error("[VideoList] 刷新失败:", error);

        // 设置isRefreshing为false，允许滚动视图回弹
        this.safeSetData({ isRefreshing: false });

        // 使用延时确保动画顺畅
        setTimeout(() => {
          wx.stopPullDownRefresh();
        }, 300);
      });
  }

  /**
   * 处理页面的下拉刷新事件（API兼容性方法）
   * @returns {Promise} 刷新结果
   */
  onPullDownRefresh() {
    return this.handleRefresh();
  }

  /**
   * 处理页面的上拉加载事件（API兼容性方法）
   * @returns {Promise} 加载结果
   */
  onReachBottom() {
    return this.handleLoadMore();
  }

  /**
   * 处理上拉加载更多
   * @returns {Promise} 加载结果
   */
  handleLoadMore() {
    // 只有在有更多数据可加载且当前不在加载状态时才加载更多
    if (this.data.hasMore && !this.data.loading) {
      console.log("[VideoList] 上拉加载更多");
      return this.loadVideoList();
    }
    return Promise.resolve();
  }

  /**
   * 根据ID去重视频列表
   * @param {Array} videoList - 视频列表
   * @returns {Array} 去重后的视频列表
   */
  deduplicateVideoList(videoList) {
    return videoUtils.deduplicateVideoList(videoList);
  }

  /**
   * 清理视频数据
   * @param {object} item - 原始视频数据
   * @returns {object} 清理后的视频数据
   */
  sanitizeVideoData(item) {
    return videoUtils.sanitizeVideoData(item);
  }

  /**
   * 格式化播放量
   * @param {number|string} count - 播放量
   * @returns {string} 格式化后的播放量
   */
  formatCount(count) {
    return videoUtils.formatCount(count);
  }

  /**
   * 更新视频列表
   * @param {Array} videos - 新的视频列表
   */
  updateVideoList(videos) {
    try {
      if (!Array.isArray(videos)) {
        throw new Error("videos must be an array");
      }

      this.safeSetData({ videoList: videos });

      // 触发更新事件
      this.emit(VIDEO_EVENTS.VIDEO_LIST_UPDATE, { videoList: videos });

      console.log("[VideoList] 视频列表已更新，数量:", videos.length);
    } catch (error) {
      this.handleError(error, "updateVideoList");
    }
  }

  /**
   * 获取当前视频列表
   * @returns {Array} 当前视频列表
   */
  getVideoList() {
    return this.data.videoList || [];
  }

  /**
   * 重置列表状态
   */
  resetList() {
    try {
      this.safeSetData({
        videoList: [],
        loading: false,
        firstLoading: true,
        hasMore: true,
        page: PAGINATION.DEFAULT_PAGE,
        lastRefreshTime: 0,
        isRefreshing: false,
        originalVideoList: null,
      });

      console.log("[VideoList] 列表状态已重置");
    } catch (error) {
      this.handleError(error, "resetList");
    }
  }

  /**
   * 获取列表状态信息
   * @returns {object} 状态信息
   */
  getListStatus() {
    try {
      return {
        videoCount: this.data.videoList ? this.data.videoList.length : 0,
        loading: this.data.loading,
        firstLoading: this.data.firstLoading,
        hasMore: this.data.hasMore,
        currentPage: this.data.page,
        isRefreshing: this.data.isRefreshing,
        lastRefreshTime: this.data.lastRefreshTime,
      };
    } catch (error) {
      this.handleError(error, "getListStatus");
      return {};
    }
  }

  /**
   * 销毁模块，清理资源
   */
  destroy() {
    try {
      console.log("[VideoList] 销毁视频列表模块");
      super.destroy();
    } catch (error) {
      this.handleError(error, "destroy");
    }
  }
}

module.exports = VideoListModule;
