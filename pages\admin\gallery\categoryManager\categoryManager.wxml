<!-- 分类管理组件 -->
<view class="category-manager {{visible ? 'visible' : ''}}">
  <view class="category-manager-mask" catchtap="onClose"></view>
  <view class="category-manager-content" catchtap="onContentTap">
    <view class="category-manager-header">
      <text class="category-manager-title">分类管理</text>
      <view class="category-manager-close" catchtap="onClose">×</view>
    </view>
    
    <scroll-view class="category-manager-body" scroll-y>
      <block wx:if="{{loading && categories.length === 0}}">
        <view class="loading-container">
          <view class="loading"></view>
          <text class="loading-text">加载中...</text>
        </view>
      </block>
      <block wx:else>
        <!-- 分类列表 -->
        <view class="category-list">
          <block wx:if="{{categories.length === 0}}">
            <view class="empty-tip">暂无分类，点击下方按钮添加</view>
          </block>
          <block wx:else>
            <view 
              class="category-item" 
              wx:for="{{categories}}" 
              wx:key="_id"
            >
              <view class="category-info">
                <text class="category-name">{{item.name}}</text>
              </view>
              <view class="category-actions">
                <view class="action-btn edit-btn" catchtap="showEditForm" data-id="{{item._id}}">编辑</view>
                <view class="action-btn delete-btn" catchtap="deleteCategory" data-id="{{item._id}}">删除</view>
              </view>
            </view>
          </block>
        </view>

        <!-- 添加分类按钮 -->
        <block wx:if="{{!isAdding && !isEditing}}">
          <view class="add-category-btn" catchtap="showAddForm">
            <text class="add-icon">+</text>
            <text>添加分类</text>
          </view>
        </block>
        
        <!-- 添加分类表单 -->
        <block wx:if="{{isAdding}}">
          <view class="category-form">
            <view class="form-title">添加分类</view>
            <view class="form-item">
              <input 
                class="form-input" 
                placeholder="请输入分类名称" 
                value="{{newCategoryName}}" 
                bindinput="onInputNewCategory" 
                focus="{{true}}"
              />
            </view>
            <view class="form-actions">
              <button class="form-btn cancel-btn" catchtap="hideAddForm" disabled="{{loading}}">取消</button>
              <button class="form-btn confirm-btn" catchtap="addCategory" disabled="{{loading}}">
                <block wx:if="{{loading}}">
                  <view class="btn-loading"></view>
                  <text>添加中...</text>
                </block>
                <block wx:else>添加</block>
              </button>
            </view>
          </view>
        </block>
        
        <!-- 编辑分类表单 -->
        <block wx:if="{{isEditing}}">
          <view class="category-form">
            <view class="form-title">编辑分类</view>
            <view class="form-item">
              <input 
                class="form-input" 
                placeholder="请输入分类名称" 
                value="{{newCategoryName}}" 
                bindinput="onInputNewCategory" 
                focus="{{true}}"
              />
            </view>
            <view class="form-actions">
              <button class="form-btn cancel-btn" catchtap="hideEditForm" disabled="{{loading}}">取消</button>
              <button class="form-btn confirm-btn" catchtap="updateCategory" disabled="{{loading}}">
                <block wx:if="{{loading}}">
                  <view class="btn-loading"></view>
                  <text>更新中...</text>
                </block>
                <block wx:else>更新</block>
              </button>
            </view>
          </view>
        </block>
      </block>
    </scroll-view>
  </view>
</view> 