/* 员工验单页面样式 */
.verify-container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 顶部状态栏样式 */
.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  padding-top: 90rpx;
  background-color: #ffffff;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.back-icon {
  font-size: 40rpx;
  color: #333333;
  width: 60rpx;
}

.header-title {
  font-size: 34rpx;
  font-weight: bold;
  color: #333333;
  flex: 1;
  text-align: center;
}

.placeholder {
  width: 60rpx;
}

/* 核销码输入区域样式 */
.verify-code-section {
  background-color: #ffffff;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.verify-code-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 20rpx;
}

.verify-code-input-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.verify-code-input {
  flex: 1;
  height: 80rpx;
  border: 1rpx solid #dddddd;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  margin-right: 20rpx;
}

.verify-button {
  width: 160rpx;
  height: 80rpx;
  background-color: #ff6b81;
  color: #ffffff;
  font-size: 28rpx;
  border-radius: 8rpx;
  padding: 0;
  line-height: 80rpx;
}

/* 内容区域样式 */
.content {
  padding: 30rpx;
}

/* 待验证订单标题 */
.section-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 20rpx;
}

/* 加载中样式 */
.loading {
  padding: 100rpx 0;
  text-align: center;
}

.loading-icon {
  display: inline-block;
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #ff6b81;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #999999;
  margin-top: 20rpx;
}

/* 空状态样式 */
.empty-state {
  padding: 100rpx 0;
  text-align: center;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 32rpx;
  color: #333333;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.empty-tips {
  font-size: 26rpx;
  color: #999999;
}

/* 订单列表样式 */
.order-list {
  padding-bottom: 40rpx;
}

.order-item {
  background-color: #ffffff;
  border-radius: 16rpx;
  margin-bottom: 30rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.order-number {
  font-size: 28rpx;
  color: #333333;
  font-weight: bold;
}

.order-status {
  font-size: 26rpx;
  color: #ff6b81;
  font-weight: bold;
}

.order-info {
  padding: 20rpx 0;
}

.info-row {
  display: flex;
  margin-bottom: 16rpx;
}

.info-label {
  width: 160rpx;
  font-size: 26rpx;
  color: #666666;
}

.info-value {
  flex: 1;
  font-size: 26rpx;
  color: #333333;
}

.order-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}

.order-time {
  font-size: 24rpx;
  color: #999999;
}

.verify-btn {
  background-color: #ff6b81;
  color: #ffffff;
  font-size: 26rpx;
  padding: 10rpx 30rpx;
  border-radius: 30rpx;
} 