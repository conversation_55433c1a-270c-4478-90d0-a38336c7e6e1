.container {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative;
}

.header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 88rpx;
  padding-top: 44rpx;
  background-color: #ffffff;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.back-btn {
  width: 80rpx;
  height: 88rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.back-icon {
  width: 40rpx;
  height: 40rpx;
}

.title {
  flex: 1;
  text-align: center;
  font-size: 34rpx;
  font-weight: bold;
  color: #333333;
  margin-right: 80rpx;
}

.map {
  width: 100%;
  height: calc(100vh - 280rpx);
  margin-top: 132rpx;
}

.shop-info {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  background-color: #ffffff;
  padding: 30rpx;
  border-radius: 24rpx 24rpx 0 0;
  box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.shop-name {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 16rpx;
}

.shop-address {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 30rpx;
}

.location-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 10rpx;
}

.actions {
  display: flex;
  justify-content: space-between;
}

.action-btn {
  width: 48%;
  height: 80rpx;
  border-radius: 40rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 30rpx;
}

.navigate-btn {
  background-color: #07c160;
  color: #ffffff;
}

.call-btn {
  background-color: #f8f8f8;
  color: #333333;
  border: 1rpx solid #dddddd;
} 