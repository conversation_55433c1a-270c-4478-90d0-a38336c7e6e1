<view class="appointment-detail-container">
  <!-- 顶部标题栏 -->
  <view class="header">
    <view class="back-btn" bindtap="goBack">
      <text class="back-icon">←</text>
    </view>
    <view class="title">预约详情</view>
    <view class="placeholder"></view>
  </view>
  
  <!-- 预约详情卡片 -->
  <view class="detail-card" wx:if="{{appointment}}">
    <!-- 预约状态 -->
    <view class="status-bar {{appointment.status}}">
      <text>{{statusMap[appointment.status] || '未知状态'}}</text>
    </view>
    
    <!-- 服务信息 -->
    <view class="service-info">
      <view class="service-header">
        <text class="service-name">{{appointment.serviceName}}</text>
        <text class="service-price">¥{{appointment.servicePrice}}</text>
      </view>
      
      <!-- 服务描述，如果有 -->
      <view class="service-desc" wx:if="{{appointment.serviceDesc}}">
        <text>{{appointment.serviceDesc}}</text>
      </view>
    </view>
    
    <!-- 预约信息 -->
    <view class="appointment-info">
      <view class="info-item">
        <text class="info-label">预约日期</text>
        <text class="info-value">{{appointment.date}}</text>
      </view>
      <view class="info-item">
        <text class="info-label">预约时间</text>
        <text class="info-value">{{appointment.time}}</text>
      </view>
      <view class="info-item">
        <text class="info-label">联系电话</text>
        <text class="info-value">{{appointment.phoneNumber || '未提供'}}</text>
      </view>
      <view class="info-item">
        <text class="info-label">服务员工</text>
        <text class="info-value" wx:if="{{appointment.preferredStaffName}}">{{appointment.preferredStaffName}}</text>
        <text class="info-value" wx:elif="{{appointment.assignment_type === 'random'}}">随机分配</text>
        <text class="info-value" wx:else>未指定</text>
      </view>
      <view class="info-item">
        <text class="info-label">预约编号</text>
        <text class="info-value">{{appointment._id}}</text>
      </view>
      <view class="info-item">
        <text class="info-label">创建时间</text>
        <text class="info-value">{{appointment.createTime ? appointment.createTime.toLocaleString() : '未知'}}</text>
      </view>
    </view>
    
    <!-- 参考图片 -->
    <view class="reference-images" wx:if="{{appointment.imageUrls && appointment.imageUrls.length > 0}}">
      <view class="images-title">参考图片</view>
      <view class="images-container">
        <image 
          wx:for="{{appointment.imageUrls}}" 
          wx:key="index" 
          src="{{item}}" 
          mode="aspectFill" 
          class="reference-image"
          bindtap="previewImage"
          data-index="{{index}}"
        ></image>
      </view>
    </view>
    
    <!-- 拒绝原因（如果有） -->
    <view class="reject-reason" wx:if="{{appointment.status === 'rejected' && appointment.rejectReason}}">
      <text class="reason-label">拒绝原因：</text>
      <text class="reason-content">{{appointment.rejectReason}}</text>
    </view>
    
    <!-- 提示信息 -->
    <view class="status-tip" wx:if="{{appointment.status === 'pending'}}">
      <text>您的预约正在等待确认，我们会尽快处理</text>
    </view>
    <view class="status-tip" wx:elif="{{appointment.status === 'confirmed'}}">
      <text>您的预约已确认，请按预约时间前来</text>
    </view>
  </view>
  
  <!-- 操作按钮 -->
  <view class="action-buttons" wx:if="{{appointment}}">
    <!-- 取消预约按钮 -->
    <view class="action-btn cancel" wx:if="{{appointment.status === 'pending' || appointment.status === 'confirmed'}}" bindtap="cancelAppointment">
      取消预约
    </view>
    
    <!-- 再次预约按钮 -->
    <view class="action-btn reappointment" wx:if="{{appointment.status === 'cancelled' || appointment.status === 'rejected'}}" bindtap="reAppointment">
      再次预约
    </view>
  </view>
</view> 