<view class="content-admin-container">
  <view class="page-header">
    <view class="back-icon" bindtap="navigateBack">
      <text class="iconfont icon-back">←</text>
    </view>
    <view class="header-title">内容管理</view>
    <view class="add-btn" bindtap="addNewContent">+</view>
  </view>
  
  <view class="tab-container">
    <view class="tab-item {{currentTab === 'gallery' ? 'active' : ''}}" 
          bindtap="switchTab" 
          data-tab="gallery">
      画廊内容
    </view>
    <view class="tab-item {{currentTab === 'banner' ? 'active' : ''}}" 
          bindtap="switchTab" 
          data-tab="banner">
      轮播图
    </view>
    <view class="tab-item {{currentTab === 'notice' ? 'active' : ''}}" 
          bindtap="switchTab" 
          data-tab="notice">
      公告
    </view>
  </view>
  
  <view class="content-list">
    <!-- 加载状态 -->
    <view class="loading-container" wx:if="{{isLoading}}">
      <view class="loading-spinner"></view>
      <view class="loading-text">加载中...</view>
    </view>
    
    <!-- 空状态 -->
    <view class="empty-container" wx:if="{{!isLoading && contentList.length === 0}}">
      <view class="empty-icon">🔍</view>
      <view class="empty-text">暂无内容，点击右上角添加</view>
    </view>
    
    <!-- 内容列表 -->
    <block wx:if="{{!isLoading && contentList.length > 0}}">
      <!-- 画廊内容 -->
      <block wx:if="{{currentTab === 'gallery'}}">
        <view class="content-item" wx:for="{{contentList}}" wx:key="_id">
          <view class="content-cover">
            <image src="{{item.coverUrl || '/static/images/default.png'}}" mode="aspectFill"></image>
          </view>
          <view class="content-info">
            <view class="content-title">{{item.title}}</view>
            <view class="content-meta">
              <text>更新: {{item.updateTime}}</text>
            </view>
          </view>
          <view class="content-actions">
            <view class="action-btn edit" bindtap="editContent" data-id="{{item._id}}">编辑</view>
            <view class="action-btn delete" bindtap="deleteContent" data-id="{{item._id}}" data-title="{{item.title}}">删除</view>
          </view>
        </view>
      </block>
      
      <!-- Banner内容 -->
      <block wx:if="{{currentTab === 'banner'}}">
        <view class="content-item" wx:for="{{contentList}}" wx:key="_id">
          <view class="content-cover banner-cover">
            <image src="{{item.imageUrl || '/static/images/default.png'}}" mode="aspectFill"></image>
          </view>
          <view class="content-info">
            <view class="content-title">{{item.title}}</view>
            <view class="content-meta">
              <text>链接: {{item.linkUrl}}</text>
            </view>
          </view>
          <view class="content-actions">
            <view class="action-btn edit" bindtap="editContent" data-id="{{item._id}}">编辑</view>
            <view class="action-btn delete" bindtap="deleteContent" data-id="{{item._id}}" data-title="{{item.title}}">删除</view>
          </view>
        </view>
      </block>
      
      <!-- 公告内容 -->
      <block wx:if="{{currentTab === 'notice'}}">
        <view class="content-item notice-item" wx:for="{{contentList}}" wx:key="_id">
          <view class="content-info notice-info">
            <view class="content-title">{{item.title}}</view>
            <view class="notice-content">{{item.content}}</view>
            <view class="content-meta">
              <text>发布: {{item.createTime}}</text>
            </view>
          </view>
          <view class="content-actions">
            <view class="action-btn edit" bindtap="editContent" data-id="{{item._id}}">编辑</view>
            <view class="action-btn delete" bindtap="deleteContent" data-id="{{item._id}}" data-title="{{item.title}}">删除</view>
          </view>
        </view>
      </block>
    </block>
  </view>
</view> 