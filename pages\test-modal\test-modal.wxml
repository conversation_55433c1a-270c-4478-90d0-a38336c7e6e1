<!--pages/test-modal/test-modal.wxml-->
<view class="container">
  <view class="header">
    <text class="title">Modal组件演示</text>
    <text class="subtitle">展示不同类型的弹窗效果</text>
  </view>

  <view class="demo-section">
    <view class="demo-item">
      <button class="demo-btn" bindtap="showBasic">基础Modal</button>
      <text class="demo-desc">带标题和关闭按钮的基础弹窗</text>
    </view>

    <view class="demo-item">
      <button class="demo-btn" bindtap="showFull">全屏Modal</button>
      <text class="demo-desc">全屏显示的弹窗</text>
    </view>

    <view class="demo-item">
      <button class="demo-btn" bindtap="showNoHeader">无头部Modal</button>
      <text class="demo-desc">没有标题栏的弹窗</text>
    </view>

    <view class="demo-item">
      <button class="demo-btn" bindtap="showCustom">自定义Modal</button>
      <text class="demo-desc">自定义样式的弹窗</text>
    </view>
  </view>

  <!-- 基础Modal演示 -->
  <modal 
    visible="{{showBasicModal}}" 
    title="基础弹窗演示"
    showHeader="{{true}}"
    maskClosable="{{true}}"
    bindclose="closeBasic">
    <view class="modal-demo-content">
      <text class="content-title">这是一个基础Modal</text>
      <text class="content-text">• 有标题栏</text>
      <text class="content-text">• 有关闭按钮</text>
      <text class="content-text">• 可以点击遮罩关闭</text>
      <text class="content-text">• 自动隐藏导航栏</text>
      <button class="close-btn" bindtap="closeBasic">关闭弹窗</button>
    </view>
  </modal>

  <!-- 全屏Modal演示 -->
  <modal 
    visible="{{showFullModal}}" 
    title="全屏弹窗演示"
    showHeader="{{true}}"
    fullWidth="{{true}}"
    maskClosable="{{false}}"
    bindclose="closeFull">
    <view class="modal-demo-content full">
      <text class="content-title">全屏Modal演示</text>
      <text class="content-text">• 占满整个屏幕</text>
      <text class="content-text">• 不能点击遮罩关闭</text>
      <text class="content-text">• 适合展示详细内容</text>
      <view class="demo-images">
        <view class="demo-image">图片1</view>
        <view class="demo-image">图片2</view>
        <view class="demo-image">图片3</view>
      </view>
      <button class="close-btn" bindtap="closeFull">关闭弹窗</button>
    </view>
  </modal>

  <!-- 无头部Modal演示 -->
  <modal 
    visible="{{showNoHeaderModal}}" 
    showHeader="{{false}}"
    maskClosable="{{true}}"
    bindclose="closeNoHeader">
    <view class="modal-demo-content no-header">
      <text class="content-title">无头部Modal</text>
      <text class="content-text">• 没有标题栏</text>
      <text class="content-text">• 更简洁的界面</text>
      <text class="content-text">• 适合自定义头部</text>
      <button class="close-btn" bindtap="closeNoHeader">关闭弹窗</button>
    </view>
  </modal>

  <!-- 自定义Modal演示 -->
  <modal 
    visible="{{showCustomModal}}" 
    title="自定义样式"
    showHeader="{{true}}"
    contentClass="custom-modal"
    maskClosable="{{true}}"
    bindclose="closeCustom">
    <view class="modal-demo-content custom">
      <text class="content-title">自定义样式Modal</text>
      <text class="content-text">• 可以自定义样式类</text>
      <text class="content-text">• 灵活的内容布局</text>
      <text class="content-text">• 支持各种交互效果</text>
      <view class="custom-content">
        <view class="feature-card">
          <text class="feature-title">动画效果</text>
          <text class="feature-desc">淡入淡出动画</text>
        </view>
        <view class="feature-card">
          <text class="feature-title">响应式</text>
          <text class="feature-desc">适配各种屏幕</text>
        </view>
      </view>
      <button class="close-btn" bindtap="closeCustom">关闭弹窗</button>
    </view>
  </modal>
</view>
