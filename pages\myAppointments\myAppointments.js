const app = getApp()

Page({
  data: {
    // 预约列表
    appointmentList: [],
    loading: true,
    
    // 分页
    page: 1,
    pageSize: 10,
    hasMoreData: false,
    
    // 状态筛选
    statusOptions: [
      { value: '', label: '全部' },
      { value: 'pending', label: '待确认' },
      { value: 'confirmed', label: '已确认' },
      { value: 'completed', label: '已完成' },
      { value: 'cancelled', label: '已取消' },
      { value: 'rejected', label: '已拒绝' }
    ],
    currentStatus: ''
  },

  onLoad() {
    // 检查登录状态
    if (!app.globalData.userInfo) {
      wx.showModal({
        title: '提示',
        content: '请先登录，即可查看预约记录',
        confirmText: '去登录',
        confirmColor: '#007AFF',
        cancelText: '返回',
        cancelColor: '#999999',
        success: (res) => {
          if (res.confirm) {
            wx.switchTab({
              url: '/pages/my/my'
            });
          } else {
            wx.navigateBack();
          }
        }
      });
      return;
    }
    
    // 获取预约列表
    this.fetchAppointmentList();
  },
  
  // 获取预约列表
  fetchAppointmentList(refresh = true) {
    const { currentStatus, page, pageSize } = this.data;
    
    if (refresh) {
      this.setData({
        loading: true,
        page: 1
      });
    } else {
      this.setData({
        loading: true
      });
    }
    
    wx.cloud.callFunction({
      name: 'appointmentManager',
      data: {
        action: 'getUserAppointments',
        type: 'frontend',
        status: currentStatus,
        page: refresh ? 1 : page,
        pageSize: pageSize
      },
      success: res => {
        console.log('获取预约列表成功：', res);
        
        if (res.result && res.result.code === 0) {
          const data = res.result.data || [];
          
          // 格式化预约状态
          const formattedData = data.map(item => {
            item.statusText = this.formatStatus(item.status);
            return item;
          });
          
          if (refresh) {
            this.setData({
              appointmentList: formattedData,
              hasMoreData: data.length >= pageSize
            });
          } else {
            this.setData({
              appointmentList: [...this.data.appointmentList, ...formattedData],
              hasMoreData: data.length >= pageSize
            });
          }
        } else {
          wx.showToast({
            title: '获取预约列表失败',
            icon: 'none'
          });
        }
      },
      fail: err => {
        console.error('获取预约列表失败：', err);
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      },
      complete: () => {
        this.setData({
          loading: false
        });
        
        // 停止下拉刷新
        wx.stopPullDownRefresh();
      }
    });
  },
  
  // 格式化预约状态
  formatStatus(status) {
    switch (status) {
      case 'pending':
        return '待确认';
      case 'confirmed':
        return '已确认';
      case 'completed':
        return '已完成';
      case 'cancelled':
        return '已取消';
      case 'rejected':
        return '已拒绝';
      default:
        return '未知状态';
    }
  },
  
  // 取消预约
  cancelAppointment(e) {
    const appointmentId = e.currentTarget.dataset.id;
    
    wx.showModal({
      title: '取消预约',
      content: '确定要取消此预约吗？',
      confirmText: '确定',
      confirmColor: '#E02020',
      cancelText: '取消',
      cancelColor: '#999999',
      success: (res) => {
        if (res.confirm) {
          wx.showLoading({
            title: '处理中...',
            mask: true
          });
          
          wx.cloud.callFunction({
            name: 'appointmentManager',
            data: {
              action: 'cancelAppointment',
              type: 'frontend',
              appointmentId: appointmentId
            },
            success: res => {
              if (res.result && res.result.code === 0) {
                wx.showToast({
                  title: '预约已取消',
                  icon: 'success'
                });
                
                // 清除本地存储的预约标记
                wx.removeStorageSync('hasActiveAppointment');
                // 清除全局状态
                if (getApp().globalData) {
                  getApp().globalData.hasActiveAppointment = false;
                }
                
                // 刷新列表
                this.fetchAppointmentList();
              } else {
                wx.showToast({
                  title: res.result.message || '操作失败',
                  icon: 'none'
                });
              }
            },
            fail: err => {
              console.error('取消预约失败：', err);
              wx.showToast({
                title: '网络错误，请重试',
                icon: 'none'
              });
            },
            complete: () => {
              wx.hideLoading();
            }
          });
        }
      }
    });
  },
  
  // 状态筛选变化
  onStatusChange(e) {
    const status = e.detail.value;
    
    this.setData({
      currentStatus: status
    });
    
    // 刷新列表
    this.fetchAppointmentList();
  },
  
  // 查看预约详情
  viewAppointmentDetail(e) {
    const appointment = e.currentTarget.dataset.appointment;
    
    if (!appointment) {
      return;
    }
    
    // 将预约信息存储到本地，以便在详情页获取
    wx.setStorageSync('appointment_detail', appointment);
    
    wx.navigateTo({
      url: '/pages/myAppointments/detail'
    });
  },
  
  // 重新预约
  reAppointment(e) {
    const service = e.currentTarget.dataset.service;
    
    if (!service) {
      return;
    }
    
    // 跳转到预约页面
    wx.switchTab({
      url: '/pages/appointment/appointment'
    });
  },
  
  // 下拉刷新
  onPullDownRefresh() {
    this.fetchAppointmentList();
  },
  
  // 上拉加载更多
  onReachBottom() {
    if (this.data.hasMoreData && !this.data.loading) {
      this.setData({
        page: this.data.page + 1
      });
      
      this.fetchAppointmentList(false);
    }
  }
}) 