Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 是否显示弹窗
    show: {
      type: Boolean,
      value: false
    },
    // 广告图片地址
    imageUrl: {
      type: String,
      value: ''
    },
    // 跳转地址
    jumpUrl: {
      type: String,
      value: ''
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 动画显示状态
    showAnimation: false
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 图片点击事件处理
     */
    onImageTap() {
      const { jumpUrl } = this.properties

      if (!jumpUrl) {
        console.warn('跳转地址为空')
        return
      }

      // 关闭弹窗
      this.onClose()

      // 延迟跳转，确保弹窗关闭动画完成
      setTimeout(() => {
        // 通过app.js处理跳转逻辑
        const app = getApp()
        if (app && app.handleTargetAdJump) {
          app.handleTargetAdJump(jumpUrl)
        }
      }, 300)
    },

    /**
     * 遮罩点击事件处理
     */
    onMaskTap() {
      // 取消点击遮罩层关闭广告的功能，让广告更具强制性
      // 用户只能通过点击右上角关闭按钮来关闭广告
      // this.onClose()
    },

    /**
     * 关闭弹窗
     */
    onClose() {
      this.setData({
        showAnimation: false
      })

      // 全局关闭广告，防止在其他页面再次显示
      const app = getApp()
      if (app && app.closeTargetAdGlobally) {
        app.closeTargetAdGlobally()
      }

      // 延迟隐藏弹窗，等待动画完成
      setTimeout(() => {
        this.triggerEvent('close')
      }, 300)
    },

    /**
     * 图片加载成功
     */
    onImageLoad() {
      console.log('[TargetAdModal] 广告图片加载成功')
    },

    /**
     * 图片加载失败
     */
    onImageError() {
      console.warn('[TargetAdModal] 广告图片加载失败')
      // 可以在这里设置默认图片或显示错误提示
    }

  },

  /**
   * 组件生命周期
   */
  observers: {
    'show': function(show) {
      if (show) {
        // 显示弹窗时启动动画
        setTimeout(() => {
          this.setData({
            showAnimation: true
          })
        }, 50)
      }
    }
  }
})
