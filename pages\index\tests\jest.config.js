/**
 * Jest 测试配置文件
 * 为视频列表模块测试提供配置
 */

module.exports = {
  // 测试环境
  testEnvironment: 'node',
  
  // 测试文件匹配模式
  testMatch: [
    '**/tests/**/*.test.js'
  ],
  
  // 覆盖率收集
  collectCoverage: true,
  collectCoverageFrom: [
    '../modules/**/*.js',
    '../utils/**/*.js',
    '!../tests/**/*.js'
  ],
  
  // 覆盖率报告格式
  coverageReporters: [
    'text',
    'html',
    'lcov'
  ],
  
  // 覆盖率阈值
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80
    }
  },
  
  // 模块路径映射
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/../$1'
  },
  
  // 设置文件
  setupFilesAfterEnv: [
    '<rootDir>/setup.js'
  ],
  
  // 清除模拟
  clearMocks: true,
  
  // 详细输出
  verbose: true,
  
  // 超时设置
  testTimeout: 10000
};