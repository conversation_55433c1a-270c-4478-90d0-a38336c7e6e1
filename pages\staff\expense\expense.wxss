/* pages/staff/expense/expense.wxss */

/* 容器样式 */
.expense-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100vh;
  background-color: #f8f8f8; /* 与员工中心页面保持一致的背景色 */
  position: relative;
  overflow: hidden;
}

/* 顶部标题栏 */
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  padding-top: 90rpx;
  background-color: #ffffff;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
}

.back-icon {
  font-size: 40rpx;
  font-weight: bold;
  color: #333333;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  flex: 1;
  text-align: center;
  margin-right: 60rpx; /* 为了保持标题居中 */
}

/* 内容区域容器 */
.page-content {
  flex: 1;
  padding-top: 160rpx;  /* 为固定头部留出空间 */
  height: calc(100vh - 160rpx);
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

/* 表单样式 */
.expense-form {
  background-color: #fff;
  margin: 20rpx 30rpx;
  padding: 30rpx;
  border-radius: 15rpx; /* 增大圆角与员工中心一致 */
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08); /* 增强阴影效果 */
}

.form-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #666666; /* 淡化标题 */
  margin-bottom: 25rpx;
  border-left: 4rpx solid #07C160; /* 微信绿色 */
  padding-left: 15rpx;
}

.form-item {
  margin-bottom: 30rpx;
}

.form-label {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 15rpx;
}

.form-input-container {
  display: flex;
  align-items: center;
  border-bottom: 1rpx solid #eee;
  padding-bottom: 10rpx;
}

.currency-symbol {
  font-size: 36rpx;
  color: #333;
  margin-right: 10rpx;
}

.form-input {
  flex: 1;
  font-size: 32rpx;
  color: #333;
}

.form-textarea {
  width: 100%;
  height: 150rpx;
  font-size: 28rpx;
  color: #333;
  padding: 20rpx;
  box-sizing: border-box;
  background-color: #f9f9f9;
  border: none;
  border-radius: 8rpx;
}

/* 图片上传 */
.image-uploader {
  width: 100%;
}

.image-list {
  display: flex;
  flex-wrap: wrap;
}

.image-item {
  width: 180rpx;
  height: 180rpx;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
  position: relative;
}

.image-item image {
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
}

.delete-btn {
  position: absolute;
  top: -15rpx;
  right: -15rpx;
  width: 40rpx;
  height: 40rpx;
  background-color: rgba(0, 0, 0, 0.5);
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
}

.upload-btn {
  width: 180rpx;
  height: 180rpx;
  background-color: #f9f9f9;
  border: 1rpx dashed #ddd;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.upload-icon {
  font-size: 60rpx;
  color: #999;
}

/* 提交按钮 */
.submit-btn {
  width: 100%;
  height: 90rpx;
  background-color: #07C160; /* 微信绿色 */
  color: #ffffff;
  font-size: 32rpx;
  font-weight: 500;
  border-radius: 45rpx;
  margin-top: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(7, 193, 96, 0.2); /* 绿色阴影 */
}

.submit-btn[disabled] {
  opacity: 0.6;
  box-shadow: none;
}

/* 历史记录 */
.history-section {
  background-color: #fff;
  margin: 20rpx 30rpx;
  padding: 30rpx;
  border-radius: 15rpx; /* 增大圆角与员工中心一致 */
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08); /* 增强阴影效果 */
}

.section-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #666666; /* 淡化标题 */
  margin-bottom: 25rpx;
  border-left: 4rpx solid #07C160; /* 微信绿色 */
  padding-left: 15rpx;
}

/* 加载中 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 50rpx 0;
}

.loading-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #07C160; /* 微信绿色 */
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 24rpx;
  color: #999;
}

/* 支出记录列表 */
.expense-list {
  width: 100%;
}

.expense-item {
  padding: 20rpx;
  margin-bottom: 20rpx;
  background-color: #f9f9f9;
  border-radius: 12rpx;
  border-left: 4rpx solid #07C160; /* 微信绿色 */
}

.expense-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15rpx;
}

.expense-time {
  color: #999;
  font-size: 24rpx;
}

.expense-body {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
}

.expense-amount {
  font-size: 32rpx;
  color: #07C160; /* 微信绿色 */
  font-weight: bold;
  margin-right: 20rpx;
}

.expense-remark {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.expense-images {
  display: flex;
  align-items: center;
}

.expense-image-thumb {
  width: 100rpx;
  height: 100rpx;
  border-radius: 8rpx;
  margin-right: 10rpx;
}

.image-count {
  font-size: 24rpx;
  color: #999;
}

/* 分页 */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 30rpx;
  margin-bottom: 30rpx;
}

.page-btn {
  padding: 10rpx 30rpx;
  background-color: #f9f9f9;
  color: #07C160; /* 微信绿色 */
  border-radius: 30rpx;
  font-size: 26rpx;
  margin: 0 10rpx;
}

.page-btn.disabled {
  background-color: #f0f0f0;
  color: #ccc;
}

.page-info {
  font-size: 26rpx;
  color: #666;
}

/* 空状态 */
.empty-state {
  padding: 50rpx 0;
  text-align: center;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 支出详情弹窗 */
.expense-detail-panel {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  visibility: hidden;
  opacity: 0;
  transition: all 0.3s ease;
}

.expense-detail-panel.show {
  visibility: visible;
  opacity: 1;
}

.panel-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
}

.panel-content {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 70%;
  background-color: #fff;
  border-radius: 30rpx 30rpx 0 0;
  overflow: hidden;
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

.expense-detail-panel.show .panel-content {
  transform: translateY(0);
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.panel-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.panel-close {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  color: #999;
}

.panel-body {
  height: calc(100% - 120rpx);
  overflow-y: auto;
  padding: 30rpx;
}

.detail-item {
  margin-bottom: 30rpx;
}

.detail-label {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 10rpx;
  display: block;
}

.detail-value {
  font-size: 32rpx;
  color: #333;
}

.detail-images {
  display: flex;
  flex-wrap: wrap;
  margin-top: 10rpx;
}

.detail-image {
  width: 200rpx;
  height: 200rpx;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
  border-radius: 8rpx;
} 