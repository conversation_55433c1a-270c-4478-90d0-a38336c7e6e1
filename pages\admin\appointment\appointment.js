// pages/admin/appointment/appointment.js
const app = getApp()

Page({

  /**
   * 页面的初始数据
   */
  data: {
    // 预约列表数据
    appointmentList: [],
    loading: true,
    
    // 分页相关
    page: 1,
    pageSize: 10,
    total: 0,
    hasMoreData: false,
    
    // 筛选相关
    statusOptions: [
      { value: '', label: '全部' },
      { value: 'pending', label: '待确认' },
      { value: 'completed', label: '已完成' },
      { value: 'cancelled', label: '已取消' },
      { value: 'rejected', label: '已拒绝' }
    ],
    currentStatus: '',
    currentStatusLabel: '全部',
    
    // 日期筛选
    selectedDate: '',
    showDatePicker: false,
    dateFilterType: '', // 日期筛选类型：today, week, month
    
    // 统计数据
    stats: null,
    showStats: false,
    
    // 员工相关
    staffList: [],
    selectedStaff: '',
    selectedStaffName: '选择核销员工',
    
    // 查询日期范围
    startDate: '',
    endDate: '',
    showStartDatePicker: false,
    showEndDatePicker: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 获取今天的日期
    const today = new Date();
    const year = today.getFullYear();
    const month = (today.getMonth() + 1).toString().padStart(2, '0');
    const day = today.getDate().toString().padStart(2, '0');
    const todayStr = `${year}-${month}-${day}`;
    
    this.setData({
      selectedDate: '', // 默认不筛选日期
      startDate: todayStr,
      endDate: todayStr
    });
    
    // 获取预约列表
    this.fetchAppointmentList();
    
    // 获取员工列表
    this.fetchStaffList();
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 检查管理员状态
    if (!app.globalData.isAdmin) {
      wx.showToast({
        title: '请先登录管理员账号',
        icon: 'none',
        duration: 2000
      });
      
      setTimeout(() => {
        wx.navigateBack();
      }, 2000);
      return;
    }
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.fetchAppointmentList();
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    if (this.data.hasMoreData && !this.data.loading) {
      this.setData({
        page: this.data.page + 1
      });
      
      this.fetchAppointmentList(false);
    }
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },

  // 获取员工列表
  fetchStaffList() {
    wx.showLoading({
      title: '加载员工列表',
      mask: true
    });
    
    wx.cloud.callFunction({
      name: 'staffManager',
      data: {
        type: 'admin',
        action: 'getStaffList',
        data: {}
      },
      success: res => {
        console.log('获取员工列表成功：', res);
        
        if (res.result && res.result.code === 0) {
          const staffList = res.result.data.list || [];
          
          // 添加"全部"选项
          const staffOptions = [
            { _id: '', name: '全部核销员工' },
            ...staffList
          ];
          
          this.setData({
            staffList: staffOptions
          });
          
          // 调试输出员工列表
          console.log('处理后的员工列表：', staffOptions);
        } else {
          console.error('获取员工列表失败：', res.result);
          wx.showToast({
            title: '获取员工列表失败',
            icon: 'none'
          });
        }
      },
      fail: err => {
        console.error('获取员工列表失败：', err);
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      },
      complete: () => {
        wx.hideLoading();
      }
    });
  },
  
  // 员工选择变化
  onStaffChange(e) {
    const index = e.detail.value;
    const staff = this.data.staffList[index];
    
    this.setData({
      selectedStaff: staff._id,
      selectedStaffName: staff.name
    });
    
    // 刷新列表
    this.fetchAppointmentList();
  },

  // 获取预约列表
  fetchAppointmentList(refresh = true) {
    const { currentStatus, selectedDate, page, pageSize, selectedStaff, dateFilterType } = this.data;
    
    if (refresh) {
      this.setData({
        loading: true,
        page: 1
      });
    } else {
      this.setData({
        loading: true
      });
    }
    
    wx.cloud.callFunction({
      name: 'appointmentManager',
      data: {
        type: 'admin',
        action: 'getAppointmentList',
        date: selectedDate || '',
        dateFilterType: dateFilterType || '', // 添加日期筛选类型
        status: currentStatus,
        staffId: selectedStaff || '', // 添加员工ID筛选
        page: refresh ? 1 : page,
        pageSize: pageSize,
        includeImageUrls: true // 请求包含图片URL
      },
      success: res => {
        console.log('获取预约列表成功：', res);
        
        if (res.result && res.result.code === 0) {
          const { list, total, page, pageSize, pageCount } = res.result.data;
          
          // 处理每个预约记录，格式化创建时间
          const processedList = list.map(item => {
            // 格式化创建时间
            if (item.createTime) {
              try {
                const createTime = new Date(item.createTime);
                item.createTimeFormatted = `${createTime.getFullYear()}-${String(createTime.getMonth() + 1).padStart(2, '0')}-${String(createTime.getDate()).padStart(2, '0')} ${String(createTime.getHours()).padStart(2, '0')}:${String(createTime.getMinutes()).padStart(2, '0')}`;
              } catch (err) {
                console.error('格式化创建时间失败：', err);
                item.createTimeFormatted = '未知';
              }
            }
            
            // 如果有图片ID但没有URL，获取临时URL
            if (item.imageFileIDs && item.imageFileIDs.length > 0 && (!item.imageUrls || item.imageUrls.length === 0)) {
              this.getImageUrls(item);
            }
            
            return item;
          });
          
          if (refresh) {
            this.setData({
              appointmentList: processedList,
              total: total,
              page: page,
              hasMoreData: page < pageCount
            });
          } else {
            this.setData({
              appointmentList: [...this.data.appointmentList, ...processedList],
              total: total,
              page: page,
              hasMoreData: page < pageCount
            });
          }
        } else {
          wx.showToast({
            title: '获取预约列表失败',
            icon: 'none'
          });
        }
      },
      fail: err => {
        console.error('获取预约列表失败：', err);
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      },
      complete: () => {
        this.setData({
          loading: false
        });
        
        // 停止下拉刷新
        wx.stopPullDownRefresh();
      }
    });
  },
  
  // 获取图片临时URL
  getImageUrls(appointment) {
    if (!appointment.imageFileIDs || appointment.imageFileIDs.length === 0) {
      return;
    }
    
    wx.cloud.getTempFileURL({
      fileList: appointment.imageFileIDs,
      success: res => {
        console.log('获取图片临时URL成功：', res);
        
        if (res.fileList && res.fileList.length > 0) {
          const imageUrls = res.fileList.map(file => file.tempFileURL);
          
          // 更新预约记录中的图片URL
          const updatedList = this.data.appointmentList.map(item => {
            if (item._id === appointment._id) {
              return {
                ...item,
                imageUrls: imageUrls
              };
            }
            return item;
          });
          
          this.setData({
            appointmentList: updatedList
          });
        }
      },
      fail: err => {
        console.error('获取图片临时URL失败：', err);
      }
    });
  },
  
  // 获取预约统计数据
  fetchAppointmentStats() {
    const { startDate, endDate, selectedStaff } = this.data;
    
    wx.showLoading({
      title: '加载统计数据...',
      mask: true
    });
    
    wx.cloud.callFunction({
      name: 'appointmentManager',
      data: {
        type: 'admin',
        action: 'getAppointmentStats',
        startDate: startDate,
        endDate: endDate,
        staffId: selectedStaff || '' // 添加员工ID筛选
      },
      success: res => {
        console.log('获取预约统计数据成功：', res);
        
        if (res.result && res.result.code === 0) {
          this.setData({
            stats: res.result.data,
            showStats: true
          });
        } else {
          wx.showToast({
            title: '获取统计数据失败',
            icon: 'none'
          });
        }
      },
      fail: err => {
        console.error('获取统计数据失败：', err);
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      },
      complete: () => {
        wx.hideLoading();
      }
    });
  },
  
  // 查看员工业绩详情
  viewStaffPerformance() {
    const { selectedStaff, selectedStaffName, startDate, endDate } = this.data;
    
    if (!selectedStaff) {
      wx.showToast({
        title: '请先选择员工',
        icon: 'none'
      });
      return;
    }
    
    wx.showLoading({
      title: '获取员工业绩...',
      mask: true
    });
    
    wx.cloud.callFunction({
      name: 'staffManager',
      data: {
        type: 'admin',
        action: 'getStaffPerformance',
        data: {
          staffId: selectedStaff,
          startDate: startDate,
          endDate: endDate
        }
      },
      success: res => {
        console.log('获取员工业绩详情成功：', res);
        
        if (res.result && res.result.code === 0) {
          const performanceData = res.result.data;
          
          this.setData({
            staffPerformance: performanceData,
            showStaffPerformance: true
          });
        } else {
          wx.showToast({
            title: res.result?.message || '获取员工业绩失败',
            icon: 'none'
          });
        }
      },
      fail: err => {
        console.error('获取员工业绩失败：', err);
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      },
      complete: () => {
        wx.hideLoading();
      }
    });
  },
  
  // 关闭员工业绩面板
  closeStaffPerformance() {
    this.setData({
      showStaffPerformance: false
    });
  },
  
  // 确认预约
  confirmAppointment(e) {
    const appointmentId = e.currentTarget.dataset.id;
    
    wx.showModal({
      title: '确认预约',
      content: '确定要接受此预约请求吗？',
      confirmText: '确定',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          wx.showLoading({
            title: '处理中...',
            mask: true
          });
          
          wx.cloud.callFunction({
            name: 'appointmentManager',
            data: {
              type: 'admin',
              action: 'confirmAppointment',
              appointmentId: appointmentId
            },
            success: res => {
              if (res.result && res.result.code === 0) {
                wx.showToast({
                  title: '预约已确认',
                  icon: 'success'
                });
                
                // 刷新列表
                this.fetchAppointmentList();
              } else {
                wx.showToast({
                  title: res.result.message || '操作失败',
                  icon: 'none'
                });
              }
            },
            fail: err => {
              console.error('确认预约失败：', err);
              wx.showToast({
                title: '网络错误，请重试',
                icon: 'none'
              });
            },
            complete: () => {
              wx.hideLoading();
            }
          });
        }
      }
    });
  },
  
  // 拒绝预约
  rejectAppointment(e) {
    const appointmentId = e.currentTarget.dataset.id;
    
    wx.showModal({
      title: '拒绝预约',
      content: '确定要拒绝此预约请求吗？',
      confirmText: '确定',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          wx.showModal({
            title: '拒绝原因',
            editable: true,
            placeholderText: '请输入拒绝原因（可选）',
            success: (res2) => {
              if (res2.confirm) {
                const reason = res2.content || '';
                
                wx.showLoading({
                  title: '处理中...',
                  mask: true
                });
                
                wx.cloud.callFunction({
                  name: 'appointmentManager',
                  data: {
                    type: 'admin',
                    action: 'rejectAppointment',
                    appointmentId: appointmentId,
                    reason: reason
                  },
                  success: res => {
                    if (res.result && res.result.code === 0) {
                      wx.showToast({
                        title: '预约已拒绝',
                        icon: 'success'
                      });
                      
                      // 刷新列表
                      this.fetchAppointmentList();
                    } else {
                      wx.showToast({
                        title: res.result.message || '操作失败',
                        icon: 'none'
                      });
                    }
                  },
                  fail: err => {
                    console.error('拒绝预约失败：', err);
                    wx.showToast({
                      title: '网络错误，请重试',
                      icon: 'none'
                    });
                  },
                  complete: () => {
                    wx.hideLoading();
                  }
                });
              }
            }
          });
        }
      }
    });
  },
  
  // 完成预约
  completeAppointment(e) {
    const appointmentId = e.currentTarget.dataset.id;
    
    wx.showModal({
      title: '完成预约',
      content: '确定要将此预约标记为已完成吗？',
      confirmText: '确定',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          wx.showLoading({
            title: '处理中...',
            mask: true
          });
          
          wx.cloud.callFunction({
            name: 'appointmentManager',
            data: {
              type: 'admin',
              action: 'completeAppointment',
              appointmentId: appointmentId
            },
            success: res => {
              if (res.result && res.result.code === 0) {
                wx.showToast({
                  title: '预约已完成',
                  icon: 'success'
                });
                
                // 刷新列表
                this.fetchAppointmentList();
              } else {
                wx.showToast({
                  title: res.result.message || '操作失败',
                  icon: 'none'
                });
              }
            },
            fail: err => {
              console.error('完成预约失败：', err);
              wx.showToast({
                title: '网络错误，请重试',
                icon: 'none'
              });
            },
            complete: () => {
              wx.hideLoading();
            }
          });
        }
      }
    });
  },
  
  // 切换状态筛选
  onStatusChange(e) {
    const statusIndex = e.detail.value;
    const status = this.data.statusOptions[statusIndex].value;
    const label = this.data.statusOptions[statusIndex].label;
    
    this.setData({
      currentStatus: status,
      currentStatusLabel: label
    });
    
    // 刷新列表
    this.fetchAppointmentList();
  },
  
  // 获取状态标签文本
  getStatusLabel(statusValue) {
    const option = this.data.statusOptions.find(item => item.value === statusValue);
    return option ? option.label : '全部';
  },
  
  // 显示日期选择器
  showDatePicker() {
    this.setData({
      showDatePicker: true
    });
  },
  
  // 日期选择变化
  onDateChange(e) {
    const date = e.detail.value;
    
    this.setData({
      selectedDate: date,
      showDatePicker: false
    });
    
    // 刷新列表
    this.fetchAppointmentList();
  },
  
  // 显示开始日期选择器
  showStartDatePicker() {
    this.setData({
      showStartDatePicker: true
    });
  },
  
  // 开始日期变化
  onStartDateChange(e) {
    const date = e.detail.value;
    
    this.setData({
      startDate: date,
      showStartDatePicker: false
    });
  },
  
  // 显示结束日期选择器
  showEndDatePicker() {
    this.setData({
      showEndDatePicker: true
    });
  },
  
  // 结束日期变化
  onEndDateChange(e) {
    const date = e.detail.value;
    
    this.setData({
      endDate: date,
      showEndDatePicker: false
    });
  },
  
  // 查看统计数据
  viewStats() {
    this.fetchAppointmentStats();
  },
  
  // 关闭统计面板
  closeStats() {
    this.setData({
      showStats: false
    });
  },
  
  // 清除日期筛选
  clearDateFilter() {
    this.setData({
      selectedDate: '',
      dateFilterType: ''
    });
    
    this.fetchAppointmentList();
  },
  
  // 选择今日
  selectToday() {
    const today = new Date();
    const year = today.getFullYear();
    const month = (today.getMonth() + 1).toString().padStart(2, '0');
    const day = today.getDate().toString().padStart(2, '0');
    const todayStr = `${year}-${month}-${day}`;
    
    this.setData({
      selectedDate: todayStr,
      dateFilterType: 'today'
    });
    
    this.fetchAppointmentList();
  },
  
  // 选择本周
  selectThisWeek() {
    this.setData({
      selectedDate: '',
      dateFilterType: 'week'
    });
    
    this.fetchAppointmentList();
  },
  
  // 选择本月
  selectThisMonth() {
    this.setData({
      selectedDate: '',
      dateFilterType: 'month'
    });
    
    this.fetchAppointmentList();
  },
  
  // 查看用户详情
  viewUserDetail(e) {
    const { openid } = e.currentTarget.dataset;
    
    if (!openid) {
      return;
    }
    
    wx.navigateTo({
      url: `/pages/admin/user/userDetail?openid=${openid}`
    });
  },
  
  // 刷新列表
  refreshList() {
    this.fetchAppointmentList();
  },

  // 返回上一页
  goBack() {
    wx.navigateBack();
  },
  
  // 清除员工筛选
  clearStaffFilter() {
    this.setData({
      selectedStaff: '',
      selectedStaffName: '选择核销员工'
    });
    
    this.fetchAppointmentList();
  },
  
  // 预览图片
  previewImage(e) {
    const urls = e.currentTarget.dataset.urls;
    const current = e.currentTarget.dataset.current;
    
    wx.previewImage({
      current: current,
      urls: urls
    });
  },
  
  // 拨打电话
  callPhone(e) {
    const phone = e.currentTarget.dataset.phone;
    
    if (!phone) {
      return;
    }
    
    wx.makePhoneCall({
      phoneNumber: phone,
      success: () => {
        console.log('拨打电话成功');
      },
      fail: (err) => {
        console.log('用户取消拨打电话');
      }
    });
  }
})