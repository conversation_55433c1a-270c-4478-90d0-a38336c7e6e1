/* 滚动视图样式 */
.recharge-records-scroll {
  height: 100vh;
  width: 100%;
}

/* 充值核销记录页面样式 */
.recharge-records-container {
  background-color: #f5f5f7;
  min-height: 100vh;
  padding-bottom: 50rpx;
  padding-top: 160rpx; /* 为固定头部留出空间 */
}

/* 顶部状态栏样式 */
.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  padding-top: 90rpx;
  background-color: #ffffff;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
}

.header-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  flex: 1;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.back-btn {
  font-size: 40rpx;
  font-weight: bold;
  color: #333333;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.placeholder {
  width: 60rpx;
}

/* 充值统计卡片样式 */
.recharge-stats-section {
  margin: 20rpx 30rpx;
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 25rpx 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.stats-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 20rpx;
}

.stats-cards {
  width: 100%;
}

/* 主要统计卡片 */
.main-stats {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.main-stats-card {
  width: 48%;
  background-color: #f8f8f8;
  border-radius: 8rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.03);
}

.main-card-title {
  font-size: 26rpx;
  color: #666666;
  margin-bottom: 10rpx;
}

.main-card-value {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
}

/* 次要统计卡片 */
.secondary-stats {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.stats-card {
  width: 31%;
  background-color: #f8f8f8;
  border-radius: 8rpx;
  padding: 15rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.03);
}

.card-title {
  font-size: 24rpx;
  color: #666666;
  margin-bottom: 8rpx;
}

.card-value {
  font-size: 28rpx;
  font-weight: bold;
  color: #333333;
}

/* 充值记录统计信息 */
.recharge-count-info {
  font-size: 26rpx;
  color: #666666;
  padding: 10rpx 0;
  text-align: right;
}

/* 日期筛选样式 */
.filter-section {
  margin: 20rpx 30rpx;
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.date-filter {
  width: 100%;
}

.date-picker-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.date-picker-item {
  width: 48%;
}

.date-label {
  font-size: 26rpx;
  color: #666666;
  margin-bottom: 10rpx;
  display: block;
}

.date-picker-value {
  background-color: #f8f8f8;
  padding: 15rpx;
  border-radius: 6rpx;
  font-size: 28rpx;
  color: #333333;
}

.filter-buttons {
  display: flex;
  justify-content: space-between;
}

.filter-btn {
  width: 48%;
  font-size: 28rpx;
  padding: 15rpx 0;
  border-radius: 6rpx;
  text-align: center;
}

.apply {
  background-color: #07c160;
  color: #ffffff;
}

.clear {
  background-color: #f0f0f0;
  color: #666666;
}

/* 记录总数样式 */
.total-count {
  margin: 20rpx 30rpx;
  font-size: 26rpx;
  color: #666666;
}

/* 记录列表样式 */
.records-list {
  margin: 0 30rpx;
}

.record-item {
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.record-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
  padding-bottom: 15rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.record-id {
  font-size: 24rpx;
  color: #999999;
}

.record-status {
  font-size: 24rpx;
  color: #ff9800;
  background-color: rgba(255, 152, 0, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 30rpx;
}

.record-status.verified {
  color: #07c160;
  background-color: rgba(7, 193, 96, 0.1);
}

.record-details {
  font-size: 28rpx;
}

.record-info-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10rpx;
}

.info-label {
  color: #666666;
}

.info-value {
  color: #333333;
}

.info-value.highlight {
  color: #ff6b00;
  font-weight: bold;
}

/* 无记录提示 */
.no-records {
  text-align: center;
  padding: 50rpx 0;
  color: #999999;
  font-size: 28rpx;
}

/* 加载中样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx 0;
}

.loading-icon {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #07c160;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  margin-top: 10rpx;
  font-size: 24rpx;
  color: #999999;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 底部提示 */
.bottom-tip {
  text-align: center;
  padding: 20rpx 0;
  color: #999999;
  font-size: 24rpx;
} 