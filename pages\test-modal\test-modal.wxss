/* pages/test-modal/test-modal.wxss */
.container {
  padding: 40rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 60rpx;
  padding: 40rpx 0;
}

.title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 20rpx;
}

.subtitle {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

.demo-section {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.demo-item {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  padding: 30rpx;
  backdrop-filter: blur(10rpx);
}

.demo-btn {
  width: 100%;
  background: linear-gradient(45deg, #ff6b6b, #ee5a24);
  color: white;
  border: none;
  border-radius: 15rpx;
  padding: 25rpx;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 15rpx;
}

.demo-desc {
  display: block;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
  text-align: center;
}

/* Modal内容样式 */
.modal-demo-content {
  padding: 40rpx;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.modal-demo-content.full {
  height: 100vh;
  justify-content: center;
}

.modal-demo-content.no-header {
  padding-top: 60rpx;
}

.modal-demo-content.custom {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
}

.content-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  text-align: center;
  margin-bottom: 20rpx;
}

.custom .content-title {
  color: white;
}

.content-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 10rpx;
}

.custom .content-text {
  color: rgba(255, 255, 255, 0.9);
}

.close-btn {
  background: #007aff;
  color: white;
  border: none;
  border-radius: 12rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
  margin-top: 30rpx;
}

.demo-images {
  display: flex;
  gap: 20rpx;
  margin: 30rpx 0;
}

.demo-image {
  flex: 1;
  height: 200rpx;
  background: #f0f0f0;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 24rpx;
}

.custom-content {
  display: flex;
  gap: 20rpx;
  margin: 30rpx 0;
}

.feature-card {
  flex: 1;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 12rpx;
  padding: 20rpx;
  text-align: center;
}

.feature-title {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.feature-desc {
  display: block;
  font-size: 24rpx;
  opacity: 0.8;
}
