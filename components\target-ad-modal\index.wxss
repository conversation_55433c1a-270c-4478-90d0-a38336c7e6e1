/* 指向广告弹窗组件样式 */
.target-ad-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 9999;
}

/* 遮罩层 - 渐变背景 */
.mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(0, 0, 0, 0.4) 0%, rgba(0, 0, 0, 0.7) 100%);
  opacity: 0;
  transition: opacity 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  backdrop-filter: blur(2rpx); /* 添加背景模糊效果 */
}

.mask-show {
  opacity: 1;
}

/* 弹窗内容 - 4:3 宽高比优化 */
.modal-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) scale(0.8);
  width: 660rpx; /* 增加1/10：600 + 60 = 660 */
  max-width: 85%; /* 适当增加最大宽度 */
  background-color: transparent;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 12rpx 48rpx rgba(0, 0, 0, 0.25);
  opacity: 0;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.content-show {
  transform: translate(-50%, -50%) scale(1);
  opacity: 1;
}

/* 广告图片 - 4:3 宽高比优化 */
.ad-image {
  width: 100%;
  height: 880rpx; /* 增加1/10：800 + 80 = 880 */
  border-radius: 16rpx;
  display: block;
  object-fit: cover; /* 确保图片填充整个容器 */
  background-color: transparent; /* 改为透明背景 */
  transition: transform 0.3s ease;
  cursor: pointer;
}

.ad-image:active {
  transform: scale(0.98);
}

/* 关闭按钮 - 现代化设计 */
.close-btn {
  position: absolute;
  top: -15rpx;
  right: -15rpx;
  width: 56rpx;
  height: 56rpx;
  background: rgba(0, 0, 0, 0.6);
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
  transition: all 0.2s ease;
}

.close-btn:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}

.close-icon {
  color: #ffffff;
  font-size: 32rpx;
  font-weight: 600;
  line-height: 1;
}

/* 响应式设计 - 保持4:3宽高比 */
@media screen and (max-width: 400px) {
  .modal-content {
    width: 594rpx; /* 增加1/10：540 + 54 = 594 */
    max-width: 85%;
  }

  .ad-image {
    height: 792rpx; /* 增加1/10：720 + 72 = 792 */
  }
}

@media screen and (min-width: 800px) {
  .modal-content {
    width: 726rpx; /* 增加1/10：660 + 66 = 726 */
  }

  .ad-image {
    height: 968rpx; /* 增加1/10：880 + 88 = 968 */
  }
}
