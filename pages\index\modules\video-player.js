/**
 * 视频播放模块
 * 负责视频播放控制、播放状态管理、全屏状态处理和视频事件处理
 * @version 1.0.0
 */

const BaseModule = require("./base-module");
const {
  VIDEO_CONFIG,
  VIDEO_EVENTS,
  ERROR_TYPES,
} = require("../constants/index-constants");

class VideoPlayerModule extends BaseModule {
  constructor(pageContext) {
    super(pageContext);
    this.moduleName = "VideoPlayer";

    // 引入视频工具函数
    this.videoUtils = require("../../../utils/video/index");
    this.getVideoUrl = this.videoUtils.getVideoUrl;
  }

  /**
   * 初始化视频播放模块
   */
  init() {
    try {
      // 减少视频播放模块初始化日志
      // console.log('[VideoPlayer] 初始化视频播放模块');

      // 初始化播放状态
      this.initPlayerState();

      this.initialized = true;
      // 减少视频播放模块初始化完成日志
      // console.log('[VideoPlayer] 视频播放模块初始化完成');
    } catch (error) {
      this.handleError(error, "init");
    }
  }

  /**
   * 初始化播放状态
   */
  initPlayerState() {
    try {
      const currentData = this.data;

      // 确保必要的播放状态字段存在
      const requiredFields = {
        currentPlayingVideo: currentData.currentPlayingVideo || null,
      };

      // 只更新缺失的字段
      const updateData = {};
      Object.keys(requiredFields).forEach((key) => {
        if (currentData[key] === undefined) {
          updateData[key] = requiredFields[key];
        }
      });

      if (Object.keys(updateData).length > 0) {
        this.safeSetData(updateData);
      }

      // 减少播放状态初始化完成日志
      // console.log('[VideoPlayer] 播放状态初始化完成');
    } catch (error) {
      this.handleError(error, "initPlayerState");
    }
  }

  /**
   * 处理视频点击事件
   * @param {object} e - 事件对象
   */
  handleVideoTap(e) {
    try {
      const that = this;
      const { video } = e.currentTarget.dataset;

      if (!video) {
        console.warn("[VideoPlayer] 视频数据为空");
        return;
      }

      console.log("[VideoPlayer] 处理视频点击:", video.id);

      // 只在用户点击时获取视频URL
      if (!video.videoUrl) {
        this.fetchVideoUrlAndPlay(video);
      } else {
        this.playVideo(video);
      }
    } catch (error) {
      this.handleError(error, "handleVideoTap");
    }
  }

  /**
   * 获取视频URL并播放
   * @param {object} video - 视频对象
   */
  fetchVideoUrlAndPlay(video) {
    const that = this;

    try {
      console.log("[VideoPlayer] 获取视频URL:", video.id);

      this.getVideoUrl(video.id, video.baseId)
        .then((videoUrl) => {
          console.log("[VideoPlayer] 成功获取视频URL:", video.id);

          // 更新视频URL
          const updatedList = that.data.videoList.map((item) => {
            if (item.id === video.id) {
              return Object.assign({}, item, {
                videoUrl: videoUrl,
                isPlaying: true,
              });
            }
            return Object.assign({}, item, { isPlaying: false });
          });

          // 更新当前播放视频
          const currentPlayingVideo = updatedList.find((item) => {
            return item.id === video.id;
          });

          that.safeSetData({
            videoList: updatedList,
            currentPlayingVideo: currentPlayingVideo,
          });

          // 触发视频播放事件
          that.emit(VIDEO_EVENTS.VIDEO_PLAY, {
            video: currentPlayingVideo,
          });
        })
        .catch((error) => {
          console.error("[VideoPlayer] 获取视频URL失败:", error);
          wx.showToast({
            title: "视频加载失败",
            icon: "none",
          });

          that.handleError(error, "fetchVideoUrlAndPlay");
        });
    } catch (error) {
      this.handleError(error, "fetchVideoUrlAndPlay");
    }
  }

  /**
   * 播放视频
   * @param {object} video - 视频对象
   */
  playVideo(video) {
    try {
      const that = this;

      // 暂停所有其他视频
      this.pauseAllVideos();

      // 更新当前视频状态
      const updatedList = that.data.videoList.map((item) => {
        if (item.id === video.id) {
          return Object.assign({}, item, { isPlaying: !item.isPlaying });
        }
        return Object.assign({}, item, { isPlaying: false });
      });

      // 更新当前播放视频
      const targetVideo = updatedList.find((item) => item.id === video.id);
      const currentPlayingVideo =
        targetVideo && targetVideo.isPlaying ? targetVideo : null;

      that.safeSetData({
        videoList: updatedList,
        currentPlayingVideo: currentPlayingVideo,
      });

      // 触发相应的播放事件
      if (currentPlayingVideo) {
        that.emit(VIDEO_EVENTS.VIDEO_PLAY, { video: currentPlayingVideo });
      } else {
        that.emit(VIDEO_EVENTS.VIDEO_PAUSE, { video: targetVideo });
      }

      console.log(
        "[VideoPlayer] 视频播放状态更新:",
        video.id,
        targetVideo ? targetVideo.isPlaying : false
      );
    } catch (error) {
      this.handleError(error, "playVideo");
    }
  }

  /**
   * 视频播放事件处理
   * @param {object} e - 事件对象
   */
  onVideoPlay(e) {
    try {
      console.log("[VideoPlayer] 视频开始播放");

      // 视频开始播放
      const videoId = e.currentTarget.id.replace("video-", "");
      if (videoId) {
        // 找到正在播放的视频
        const playingVideo = this.data.videoList.find((v) => {
          return v.id === videoId;
        });

        if (playingVideo) {
          this.safeSetData({
            currentPlayingVideo: playingVideo,
          });

          // 触发播放事件
          this.emit(VIDEO_EVENTS.VIDEO_PLAY, {
            video: playingVideo,
            source: "native",
          });

          console.log("[VideoPlayer] 当前播放视频已更新:", videoId);
        }
      }
    } catch (error) {
      this.handleError(error, "onVideoPlay");
    }
  }

  /**
   * 视频暂停事件处理
   * @param {object} e - 事件对象
   */
  onVideoPause(e) {
    try {
      console.log("[VideoPlayer] 视频暂停");

      // 视频暂停
      const videoId = e.currentTarget.id.replace("video-", "");
      if (
        videoId &&
        this.data.currentPlayingVideo &&
        this.data.currentPlayingVideo.id === videoId
      ) {
        const pausedVideo = this.data.currentPlayingVideo;

        // 清除当前播放视频的引用
        this.safeSetData({
          currentPlayingVideo: null,
        });

        // 触发暂停事件
        this.emit(VIDEO_EVENTS.VIDEO_PAUSE, {
          video: pausedVideo,
          source: "native",
        });

        console.log("[VideoPlayer] 当前播放视频已清除:", videoId);
      }
    } catch (error) {
      this.handleError(error, "onVideoPause");
    }
  }

  /**
   * 视频结束事件处理
   * @param {object} e - 事件对象
   */
  onVideoEnd(e) {
    try {
      console.log("[VideoPlayer] 视频播放结束");

      // 视频播放结束
      const videoId = e.currentTarget.id.replace("video-", "");
      if (
        videoId &&
        this.data.currentPlayingVideo &&
        this.data.currentPlayingVideo.id === videoId
      ) {
        const endedVideo = this.data.currentPlayingVideo;

        // 清除当前播放视频的引用
        this.safeSetData({
          currentPlayingVideo: null,
        });

        // 触发结束事件
        this.emit(VIDEO_EVENTS.VIDEO_END, {
          video: endedVideo,
          source: "native",
        });

        console.log("[VideoPlayer] 视频播放结束，已清除当前播放视频:", videoId);
      }
    } catch (error) {
      this.handleError(error, "onVideoEnd");
    }
  }

  /**
   * 视频错误事件处理
   * @param {object} e - 事件对象
   */
  onVideoError(e) {
    try {
      console.error("[VideoPlayer] 视频播放错误:", e);

      const videoId = e.currentTarget
        ? e.currentTarget.id.replace("video-", "")
        : null;

      // 显示错误提示
      wx.showToast({
        title: "视频加载失败",
        icon: "none",
      });

      // 如果是当前播放的视频出错，清除播放状态
      if (
        videoId &&
        this.data.currentPlayingVideo &&
        this.data.currentPlayingVideo.id === videoId
      ) {
        const errorVideo = this.data.currentPlayingVideo;

        this.safeSetData({
          currentPlayingVideo: null,
        });

        // 触发错误事件
        this.emit(VIDEO_EVENTS.VIDEO_ERROR, {
          video: errorVideo,
          error: e.detail || e,
          source: "native",
        });
      }

      // 记录错误
      this.handleError(new Error("视频播放错误"), "onVideoError");
    } catch (error) {
      this.handleError(error, "onVideoError");
    }
  }

  /**
   * 处理视频全屏状态变化
   * @param {object} e - 事件对象
   */
  onVideoFullscreenChange(e) {
    try {
      const isFullscreen = e.detail.isFullscreen;
      console.log("[VideoPlayer] 视频全屏状态变化:", isFullscreen);

      // 更新全局状态，供搜索框等组件使用
      const app = getApp();
      if (app && app.globalData) {
        app.globalData.isVideoFullscreen = isFullscreen;
      }

      // 创建一个简单的事件总线如果不存在
      if (!app.globalData.eventBus) {
        app.globalData.eventBus = {
          _events: {},
          on: function (name, callback) {
            this._events[name] = this._events[name] || [];
            this._events[name].push(callback);
          },
          emit: function (name, data) {
            if (this._events[name]) {
              this._events[name].forEach((callback) => {
                callback(data);
              });
            }
          },
        };
      }

      // 通过事件总线广播全屏状态变化
      app.globalData.eventBus.emit("videoFullscreenChange", { isFullscreen });

      // 触发全屏变化事件
      this.emit(VIDEO_EVENTS.VIDEO_FULLSCREEN_CHANGE, {
        isFullscreen: isFullscreen,
        video: this.data.currentPlayingVideo,
      });
    } catch (error) {
      this.handleError(error, "onVideoFullscreenChange");
    }
  }

  /**
   * 暂停所有视频
   */
  pauseAllVideos() {
    try {
      console.log("[VideoPlayer] 暂停所有视频");

      // 遍历所有视频并尝试暂停
      this.data.videoList.forEach((item) => {
        // 方法1: 使用wx API直接暂停
        try {
          const videoCtx = wx.createVideoContext("video-" + item.id, this.page);
          if (videoCtx) {
            videoCtx.pause();
          }
        } catch (e) {
          console.error(
            "[VideoPlayer] 无法通过API暂停视频 " + item.id + ":",
            e
          );
        }

        // 方法2: 尝试通过组件实例暂停
        try {
          // 尝试找到主列表中的视频卡片
          const videoCard = this.page.selectComponent("#video-card-" + item.id);
          if (videoCard && typeof videoCard.pause === "function") {
            videoCard.pause();
          }

          // 尝试找到第二个列表中的视频卡片
          const videoCardList2 = this.page.selectComponent(
            "#video-card-list2-" + item.id
          );
          if (videoCardList2 && typeof videoCardList2.pause === "function") {
            videoCardList2.pause();
          }
        } catch (e) {
          console.error(
            "[VideoPlayer] 无法通过组件暂停视频 " + item.id + ":",
            e
          );
        }

        // 强制更新播放状态
        item.isPlaying = false;
      });

      // 更新状态并清除当前播放视频引用
      this.safeSetData({
        videoList: this.data.videoList,
        currentPlayingVideo: null,
      });

      // 触发暂停所有视频事件
      this.emit(VIDEO_EVENTS.VIDEO_PAUSE, {
        action: "pauseAll",
        source: "manual",
      });

      console.log("[VideoPlayer] 所有视频已暂停");
    } catch (error) {
      this.handleError(error, "pauseAllVideos");
    }
  }

  /**
   * 获取当前播放的视频
   * @returns {object|null} 当前播放的视频对象
   */
  getCurrentPlayingVideo() {
    try {
      return this.data.currentPlayingVideo;
    } catch (error) {
      this.handleError(error, "getCurrentPlayingVideo");
      return null;
    }
  }

  /**
   * 设置当前播放的视频
   * @param {object|null} video - 视频对象
   */
  setCurrentPlayingVideo(video) {
    try {
      // 验证视频对象
      if (video && !video.id) {
        console.warn("[VideoPlayer] 无效的视频对象");
        return;
      }

      const oldVideo = this.data.currentPlayingVideo;

      this.safeSetData({
        currentPlayingVideo: video,
      });

      // 触发当前播放视频变化事件
      this.emit("currentVideoChange", {
        oldVideo: oldVideo,
        newVideo: video,
      });

      console.log(
        "[VideoPlayer] 当前播放视频已更新:",
        video ? video.id : "null"
      );
    } catch (error) {
      this.handleError(error, "setCurrentPlayingVideo");
    }
  }

  /**
   * 检查视频是否正在播放
   * @param {string} videoId - 视频ID
   * @returns {boolean} 是否正在播放
   */
  isVideoPlaying(videoId) {
    try {
      if (!videoId) return false;

      const currentVideo = this.data.currentPlayingVideo;
      return (
        currentVideo && currentVideo.id === videoId && currentVideo.isPlaying
      );
    } catch (error) {
      this.handleError(error, "isVideoPlaying");
      return false;
    }
  }

  /**
   * 切换视频播放状态
   * @param {string} videoId - 视频ID
   */
  toggleVideoPlayState(videoId) {
    try {
      if (!videoId) return;

      const video = this.data.videoList.find((item) => item.id === videoId);
      if (!video) {
        console.warn("[VideoPlayer] 未找到视频:", videoId);
        return;
      }

      // 模拟点击事件
      const mockEvent = {
        currentTarget: {
          dataset: { video: video },
        },
      };

      this.handleVideoTap(mockEvent);
    } catch (error) {
      this.handleError(error, "toggleVideoPlayState");
    }
  }

  /**
   * 停止指定视频
   * @param {string} videoId - 视频ID
   */
  stopVideo(videoId) {
    try {
      if (!videoId) return;

      // 使用视频上下文停止视频
      const videoCtx = wx.createVideoContext("video-" + videoId, this.page);
      if (videoCtx) {
        videoCtx.stop();
      }

      // 更新视频状态
      const updatedList = this.data.videoList.map((item) => {
        if (item.id === videoId) {
          return { ...item, isPlaying: false };
        }
        return item;
      });

      // 如果停止的是当前播放视频，清除引用
      let currentPlayingVideo = this.data.currentPlayingVideo;
      if (currentPlayingVideo && currentPlayingVideo.id === videoId) {
        currentPlayingVideo = null;
      }

      this.safeSetData({
        videoList: updatedList,
        currentPlayingVideo: currentPlayingVideo,
      });

      console.log("[VideoPlayer] 视频已停止:", videoId);
    } catch (error) {
      this.handleError(error, "stopVideo");
    }
  }

  /**
   * 批量更新视频播放状态
   * @param {Array} videoUpdates - 视频状态更新数组 [{id, isPlaying}, ...]
   */
  batchUpdateVideoStates(videoUpdates) {
    try {
      if (!Array.isArray(videoUpdates)) {
        console.warn("[VideoPlayer] videoUpdates必须是数组");
        return;
      }

      const updatedList = this.data.videoList.map((video) => {
        const update = videoUpdates.find((u) => u.id === video.id);
        if (update) {
          return { ...video, isPlaying: update.isPlaying };
        }
        return video;
      });

      // 找到当前播放的视频
      const currentPlayingVideo =
        updatedList.find((video) => video.isPlaying) || null;

      this.safeSetData({
        videoList: updatedList,
        currentPlayingVideo: currentPlayingVideo,
      });

      // 触发批量状态更新事件
      this.emit("batchStateUpdate", {
        updates: videoUpdates,
        currentVideo: currentPlayingVideo,
      });

      console.log(
        "[VideoPlayer] 批量更新视频状态完成，数量:",
        videoUpdates.length
      );
    } catch (error) {
      this.handleError(error, "batchUpdateVideoStates");
    }
  }

  /**
   * 重置所有视频播放状态
   */
  resetAllVideoStates() {
    try {
      const updatedList = this.data.videoList.map((video) => ({
        ...video,
        isPlaying: false,
      }));

      this.safeSetData({
        videoList: updatedList,
        currentPlayingVideo: null,
      });

      // 触发状态重置事件
      this.emit("stateReset", {
        videoCount: updatedList.length,
      });

      console.log("[VideoPlayer] 所有视频播放状态已重置");
    } catch (error) {
      this.handleError(error, "resetAllVideoStates");
    }
  }

  /**
   * 获取所有播放中的视频
   * @returns {Array} 播放中的视频列表
   */
  getPlayingVideos() {
    try {
      return this.data.videoList.filter((video) => video.isPlaying);
    } catch (error) {
      this.handleError(error, "getPlayingVideos");
      return [];
    }
  }

  /**
   * 获取视频播放历史
   * @returns {Array} 播放历史列表
   */
  getPlayHistory() {
    try {
      // 从本地存储获取播放历史
      const history = wx.getStorageSync("video_play_history") || [];
      return history;
    } catch (error) {
      this.handleError(error, "getPlayHistory");
      return [];
    }
  }

  /**
   * 添加视频到播放历史
   * @param {object} video - 视频对象
   */
  addToPlayHistory(video) {
    try {
      if (!video || !video.id) return;

      const history = this.getPlayHistory();
      const maxHistorySize = 50; // 最多保存50条历史记录

      // 移除已存在的记录
      const filteredHistory = history.filter((item) => item.id !== video.id);

      // 添加新记录到开头
      const newHistory = [
        {
          id: video.id,
          mainTitle: video.mainTitle,
          subTitle: video.subTitle,
          coverUrl: video.coverUrl,
          playTime: Date.now(),
        },
        ...filteredHistory,
      ].slice(0, maxHistorySize);

      // 保存到本地存储
      wx.setStorageSync("video_play_history", newHistory);

      console.log("[VideoPlayer] 视频已添加到播放历史:", video.id);
    } catch (error) {
      this.handleError(error, "addToPlayHistory");
    }
  }

  /**
   * 清除播放历史
   */
  clearPlayHistory() {
    try {
      wx.removeStorageSync("video_play_history");
      console.log("[VideoPlayer] 播放历史已清除");
    } catch (error) {
      this.handleError(error, "clearPlayHistory");
    }
  }

  /**
   * 监听视频状态变化
   * @param {string} videoId - 视频ID
   * @param {function} callback - 回调函数
   */
  watchVideoState(videoId, callback) {
    try {
      if (!videoId || typeof callback !== "function") return;

      const eventName = `videoState:${videoId}`;
      this.on(eventName, callback);

      console.log("[VideoPlayer] 开始监听视频状态:", videoId);
    } catch (error) {
      this.handleError(error, "watchVideoState");
    }
  }

  /**
   * 取消监听视频状态变化
   * @param {string} videoId - 视频ID
   * @param {function} callback - 回调函数
   */
  unwatchVideoState(videoId, callback) {
    try {
      if (!videoId) return;

      const eventName = `videoState:${videoId}`;
      this.off(eventName, callback);

      console.log("[VideoPlayer] 停止监听视频状态:", videoId);
    } catch (error) {
      this.handleError(error, "unwatchVideoState");
    }
  }

  /**
   * 触发视频状态变化事件
   * @param {string} videoId - 视频ID
   * @param {object} state - 状态对象
   */
  notifyVideoStateChange(videoId, state) {
    try {
      if (!videoId) return;

      const eventName = `videoState:${videoId}`;
      this.emit(eventName, { videoId, state });

      console.log("[VideoPlayer] 视频状态变化通知:", videoId, state);
    } catch (error) {
      this.handleError(error, "notifyVideoStateChange");
    }
  }

  /**
   * 获取视频播放统计
   * @returns {object} 播放统计信息
   */
  getPlayStatistics() {
    try {
      const history = this.getPlayHistory();
      const currentVideo = this.data.currentPlayingVideo;
      const playingVideos = this.getPlayingVideos();

      // 统计今天的播放次数
      const today = new Date().toDateString();
      const todayPlays = history.filter((item) => {
        const playDate = new Date(item.playTime).toDateString();
        return playDate === today;
      });

      // 统计最常播放的视频
      const playCount = {};
      history.forEach((item) => {
        playCount[item.id] = (playCount[item.id] || 0) + 1;
      });

      const mostPlayedVideos = Object.entries(playCount)
        .sort(([, a], [, b]) => b - a)
        .slice(0, 5)
        .map(([id, count]) => ({ id, count }));

      return {
        totalPlays: history.length,
        todayPlays: todayPlays.length,
        currentlyPlaying: playingVideos.length,
        hasCurrentVideo: !!currentVideo,
        currentVideoId: currentVideo ? currentVideo.id : null,
        mostPlayedVideos: mostPlayedVideos,
        lastPlayTime: history.length > 0 ? history[0].playTime : null,
      };
    } catch (error) {
      this.handleError(error, "getPlayStatistics");
      return {};
    }
  }

  /**
   * 设置视频播放模式
   * @param {string} mode - 播放模式 ('single', 'loop', 'random')
   */
  setPlayMode(mode) {
    try {
      const validModes = ["single", "loop", "random"];
      if (!validModes.includes(mode)) {
        console.warn("[VideoPlayer] 无效的播放模式:", mode);
        return;
      }

      this.playMode = mode;
      wx.setStorageSync("video_play_mode", mode);

      // 触发播放模式变化事件
      this.emit("playModeChange", { mode });

      console.log("[VideoPlayer] 播放模式已设置:", mode);
    } catch (error) {
      this.handleError(error, "setPlayMode");
    }
  }

  /**
   * 获取当前播放模式
   * @returns {string} 播放模式
   */
  getPlayMode() {
    try {
      return this.playMode || wx.getStorageSync("video_play_mode") || "single";
    } catch (error) {
      this.handleError(error, "getPlayMode");
      return "single";
    }
  }

  /**
   * 获取下一个要播放的视频
   * @returns {object|null} 下一个视频对象
   */
  getNextVideo() {
    try {
      const currentVideo = this.data.currentPlayingVideo;
      const videoList = this.data.videoList;
      const playMode = this.getPlayMode();

      if (!videoList || videoList.length === 0) return null;
      if (!currentVideo) return videoList[0];

      const currentIndex = videoList.findIndex(
        (video) => video.id === currentVideo.id
      );
      if (currentIndex === -1) return videoList[0];

      switch (playMode) {
        case "loop":
          // 循环播放：到最后一个后回到第一个
          return videoList[(currentIndex + 1) % videoList.length];

        case "random":
          // 随机播放：随机选择一个不同的视频
          const availableVideos = videoList.filter(
            (_, index) => index !== currentIndex
          );
          if (availableVideos.length === 0) return currentVideo;
          const randomIndex = Math.floor(
            Math.random() * availableVideos.length
          );
          return availableVideos[randomIndex];

        case "single":
        default:
          // 单曲播放：播放下一个，到最后一个就停止
          return currentIndex < videoList.length - 1
            ? videoList[currentIndex + 1]
            : null;
      }
    } catch (error) {
      this.handleError(error, "getNextVideo");
      return null;
    }
  }

  /**
   * 播放下一个视频
   */
  playNext() {
    try {
      const nextVideo = this.getNextVideo();
      if (nextVideo) {
        const mockEvent = {
          currentTarget: {
            dataset: { video: nextVideo },
          },
        };
        this.handleVideoTap(mockEvent);
        console.log("[VideoPlayer] 播放下一个视频:", nextVideo.id);
      } else {
        console.log("[VideoPlayer] 没有下一个视频");
      }
    } catch (error) {
      this.handleError(error, "playNext");
    }
  }

  /**
   * 获取播放器状态摘要
   * @returns {object} 状态摘要
   */
  getPlayerStatus() {
    try {
      const currentVideo = this.data.currentPlayingVideo;
      const playingVideos = this.getPlayingVideos();
      const statistics = this.getPlayStatistics();

      return {
        hasCurrentVideo: !!currentVideo,
        currentVideoId: currentVideo ? currentVideo.id : null,
        currentVideoTitle: currentVideo ? currentVideo.mainTitle : null,
        playingCount: playingVideos.length,
        totalVideos: this.data.videoList.length,
        playMode: this.getPlayMode(),
        statistics: statistics,
      };
    } catch (error) {
      this.handleError(error, "getPlayerStatus");
      return {};
    }
  }

  /**
   * 处理视频点击事件（用于API兼容性）
   * @param {object} e - 事件对象
   */
  onVideoTap(e) {
    try {
      console.log("[VideoPlayer] 处理视频点击事件");

      // 如果事件对象包含视频信息，直接使用
      let video = null;
      if (e && e.detail && e.detail.video) {
        video = e.detail.video;
      } else if (
        e &&
        e.currentTarget &&
        e.currentTarget.dataset &&
        e.currentTarget.dataset.video
      ) {
        video = e.currentTarget.dataset.video;
      } else if (e && e.detail && e.detail.videoInfo) {
        video = e.detail.videoInfo;
      }

      if (video) {
        // 调用现有的视频点击处理方法
        this.handleVideoTap(e);
      } else {
        console.warn("[VideoPlayer] 视频点击事件中没有找到视频信息");
      }
    } catch (error) {
      console.error("[VideoPlayer] 处理视频点击事件失败:", error);
      this.handleError(error, "onVideoTap");
    }
  }

  /**
   * 处理视频详情事件
   * @param {object} e - 事件对象
   */
  onVideoDetail(e) {
    try {
      console.log("[VideoPlayer] 打开视频详情");

      const videoInfo = e.detail.videoInfo;
      if (!videoInfo) {
        console.warn("[VideoPlayer] 视频信息为空");
        return;
      }

      console.log("[VideoPlayer] 视频详情信息:", videoInfo);

      // 先暂停所有正在播放的视频
      this.pauseAllVideos();

      // 设置详情模态框的数据
      this.safeSetData({
        selectedVideoDetail: videoInfo,
        showVideoDetail: true,
      });

      // 触发视频详情打开事件
      this.emit(VIDEO_EVENTS.VIDEO_DETAIL_OPEN, {
        video: videoInfo,
      });
    } catch (error) {
      console.error("[VideoPlayer] 打开视频详情失败:", error);
      this.handleError(error, "onVideoDetail");
    }
  }

  /**
   * 关闭视频详情
   */
  onVideoDetailClose() {
    try {
      console.log("[VideoPlayer] 关闭视频详情");

      this.safeSetData({
        showVideoDetail: false,
        selectedVideoDetail: null,
      });

      // 触发视频详情关闭事件
      this.emit(VIDEO_EVENTS.VIDEO_DETAIL_CLOSE);
    } catch (error) {
      console.error("[VideoPlayer] 关闭视频详情失败:", error);
      this.handleError(error, "onVideoDetailClose");
    }
  }

  /**
   * 打开视频详情（供外部调用）
   * @param {object} videoInfo - 视频信息
   */
  openVideoDetail(videoInfo) {
    try {
      if (!videoInfo) {
        console.warn("[VideoPlayer] 视频信息为空");
        return;
      }

      // 先暂停所有正在播放的视频
      this.pauseAllVideos();

      // 模拟详情事件
      const mockEvent = {
        detail: {
          videoInfo: videoInfo,
        },
      };

      this.onVideoDetail(mockEvent);
    } catch (error) {
      this.handleError(error, "openVideoDetail");
    }
  }

  /**
   * 销毁模块，清理资源
   */
  destroy() {
    try {
      console.log("[VideoPlayer] 销毁视频播放模块");

      // 暂停所有视频
      this.pauseAllVideos();

      super.destroy();
    } catch (error) {
      this.handleError(error, "destroy");
    }
  }
}

module.exports = VideoPlayerModule;
