// 获取全局app实例
const app = getApp();

Component({
  properties: {
    placeholder: {
      type: String,
      value: '搜索'
    },
    // 添加视频列表作为属性，这样组件内部可以直接搜索
    videoList: {
      type: Array,
      value: []
    }
  },

  data: {
    statusBarHeight: 20,
    navigationBarHeight: 44,
    menuButtonInfo: {},
    searchBarWidth: '100%',
    isFocused: false,
    searchValue: '',
    searchTimer: null,
    isSearching: false,
    searchHistory: [], // 搜索历史记录
    hotKeywords: ['美甲喷绘', '模版', '单色渐变', '多功能美甲桌', '指家科技'], // 热门搜索词
    isFullscreenMode: false, // 添加全屏模式状态
    _globalStateCheckTimer: null,
    _historyStorageKey: 'SEARCH_HISTORY_STORAGE_KEY', // 使用唯一的键名，避免冲突
    _historyInitialized: false // 添加初始化标志，避免重复初始化
  },

  lifetimes: {
    attached() {
      // 使用新的API获取系统信息
      const windowInfo = wx.getWindowInfo();
      const menuButtonInfo = wx.getMenuButtonBoundingClientRect();
      
      this.setData({
        statusBarHeight: windowInfo.statusBarHeight,
        menuButtonInfo,
        // 搜索框宽度 = 屏幕宽度 - 左边距 - (屏幕右边缘到胶囊按钮右边缘的距离 + 胶囊按钮宽度 + 搜索框右边距)
        searchBarWidth: `${windowInfo.windowWidth - 16 - (windowInfo.windowWidth - menuButtonInfo.left + 8)}px`
      });
      
      // 初始化和加载搜索历史（只在未初始化时执行）
      if (!this.data._historyInitialized) {
        this._initSearchHistory();
      }

      // 监听全屏事件
      wx.onWindowResize(this.handleFullscreenChange.bind(this));
      
      // 监听全局视频全屏事件
      if (getApp() && getApp().globalData && getApp().globalData.eventBus) {
        getApp().globalData.eventBus.on('videoFullscreenChange', (data) => {
          this.setData({
            isFullscreenMode: data.isFullscreen
          });
        });
      } else {
        // 如果全局事件总线不存在，定期检查全局状态
        this._setupGlobalStateCheck();
      }
    },

    detached() {
      // 在组件卸载时移除监听
      wx.offWindowResize(this.handleFullscreenChange);
      // 清理定时器
      if (this._globalStateCheckTimer) {
        clearInterval(this._globalStateCheckTimer);
      }
    },
    
    ready() {
      // 只在未初始化时执行初始化
      if (!this.data._historyInitialized) {
        this._initSearchHistory();
      }
      this._setupGlobalStateCheck();
      
      // 设置CSS变量
      this._setCssVariables();
    }
  },
  
  pageLifetimes: {
    show() {
      // 页面显示时重新加载历史记录（只在未初始化时执行）
      if (!this.data._historyInitialized) {
        this._initSearchHistory();
      }
    },
    
    hide() {
      // 页面隐藏时确保历史记录已保存
      this._saveSearchHistoryToStorage();
    }
  },

  methods: {
    // 设置全局状态检查
    _setupGlobalStateCheck() {
      // 每秒检查一次全局状态
      this._globalStateCheckTimer = setInterval(() => {
        if (getApp() && getApp().globalData && getApp().globalData.isVideoFullscreen !== undefined) {
          const isFullscreen = getApp().globalData.isVideoFullscreen;
          if (this.data.isFullscreenMode !== isFullscreen) {
            this.setData({
              isFullscreenMode: isFullscreen
            });
          }
        }
      }, 1000);
    },
    
    // 监听窗口大小变化以检测全屏状态
    handleFullscreenChange(res) {
      // 检测是否是全屏模式
      const isFullscreen = res.size.windowWidth > res.size.windowHeight * 1.2;
      
      // 只有在全局状态未设置的情况下才依赖窗口大小变化
      if (!(getApp() && getApp().globalData && getApp().globalData.isVideoFullscreen !== undefined)) {
        this.setData({ isFullscreenMode: isFullscreen });
      }
    },

    // 搜索框获得焦点
    onSearchFocus() {
      // 在设置焦点前确保历史记录已加载
      this._initSearchHistory();
      
      this.setData({ isFocused: true });
      
      // 通知父组件搜索框获得焦点
      this.triggerEvent('searchfocus', { focused: true });
    },

    // 搜索框失去焦点
    onSearchBlur() {
      // 延迟设置，避免点击遮罩层时太快关闭导致无法触发点击事件
      setTimeout(() => {
        this.setData({ isFocused: false });
        // 通知父组件搜索框失去焦点
        this.triggerEvent('searchfocus', { focused: false });
      }, 200);
    },

    // 点击遮罩层
    onMaskTap() {
      // 收起键盘
      wx.hideKeyboard();
    },

    // 点击搜索区域外的空白区域
    onBackdropTap() {
      // 收起键盘
      wx.hideKeyboard();
      
      // 通知父组件退出焦点状态，但保留搜索结果
      this.triggerEvent('searchfocus', { focused: false });
      
      // 延迟移除焦点状态
      setTimeout(() => {
        this.setData({ isFocused: false });
      }, 200);
    },

    // 搜索框输入
    onSearchInput(e) {
      const value = e.detail.value;
      this.setData({ 
        searchValue: value,
        isSearching: value.trim() !== '' // 只有当有内容时才设置为搜索状态
      });

      // 清除之前的定时器
      if (this.data.searchTimer) {
        clearTimeout(this.data.searchTimer);
      }

      // 设置较短的防抖时间(200毫秒)，提供更即时的搜索体验
      const searchTimer = setTimeout(() => {
        // 关键改动: 无论是否在焦点状态，只要有内容变化就执行搜索
        if (value.trim()) {
          this.performSearch(value);
        } else {
          // 如果输入为空，触发清空事件
          this.triggerEvent('clear');
          this.setData({ isSearching: false });
        }
      }, 200);

      this.setData({ searchTimer });
      
      // 触发input事件，传递给父组件
      this.triggerEvent('input', { value });
    },

    // 搜索框确认搜索
    onSearchConfirm(e) {
      const value = e.detail.value;
      if (value.trim()) {
        this.performSearch(value);
        // 将关键词加入历史记录
        this._addSearchKeyword(value);
        // 搜索确认后，通知父组件焦点状态更改，但保持搜索结果
        this.triggerEvent('searchfocus', { focused: false, confirmed: true });
      } else {
        // 清空搜索，恢复显示所有视频
        this.triggerEvent('clear');
        this.triggerEvent('searchfocus', { focused: false });
      }
      // 触发confirm事件
      this.triggerEvent('confirm', { value });
      // 收起键盘
      wx.hideKeyboard();
      
      // 延迟设置失去焦点状态，确保界面状态正确
      setTimeout(() => {
        this.setData({ isFocused: false });
      }, 100);
    },

    // 执行搜索
    performSearch(value) {
      if (!value.trim()) {
        this.triggerEvent('clear');
        this.setData({ isSearching: false });
        return;
      }
      
      // 从属性中获取视频列表进行本地搜索
      const results = this.localSearchVideos(value);
      
      this.setData({ isSearching: false });
      
      // 触发search事件，传递结果给父组件
      this.triggerEvent('search', { 
        value,
        results,
        isConfirm: true
      });
    },

    // 本地搜索视频
    localSearchVideos(keyword) {
      if (!this.properties.videoList || !this.properties.videoList.length) {
        return [];
      }

      const searchTerms = keyword.toLowerCase().split(' ').filter(term => term.trim() !== '');
      
      return this.properties.videoList.filter(video => {
        if (!video) return false;
        
        const mainTitle = (video.mainTitle || video.title || '').toLowerCase();
        const subTitle = (video.subTitle || video.subtitle || '').toLowerCase();
        const description = (video.description || '').toLowerCase();
        
        // 检查所有搜索词是否都至少匹配一个字段
        return searchTerms.every(term => 
          mainTitle.includes(term) || 
          subTitle.includes(term) || 
          description.includes(term)
        );
      });
    },

    // 清空搜索内容
    clearSearch() {
      this.setData({
        searchValue: '',
        isSearching: false
      });
      
      // 触发清空事件，恢复显示所有视频
      this.triggerEvent('clear');
      
      // 如果当前是聚焦状态，通知父组件已退出搜索
      if (this.data.isFocused) {
        this.triggerEvent('searchfocus', { focused: false });
        
        // 延迟移除焦点状态
        setTimeout(() => {
          this.setData({ isFocused: false });
        }, 200);
      }
    },
    
    // ===== 全新的历史记录处理逻辑 =====
    
    // 初始化搜索历史
    _initSearchHistory() {
      // 避免重复初始化
      if (this.data._historyInitialized) {
        if (app && app.globalData && app.globalData.debugMode) {
          console.log('[搜索组件] 搜索历史已初始化，跳过');
        }
        return;
      }
      
      if (app && app.globalData && app.globalData.debugMode) {
        console.log('[搜索组件] 初始化搜索历史');
      }
      this._loadSearchHistoryFromStorage();
      
      // 标记为已初始化
      this.setData({
        _historyInitialized: true
      });
    },
    
    // 从存储中加载搜索历史
    _loadSearchHistoryFromStorage() {
      try {
        const searchHistory = wx.getStorageSync(this.data._historyStorageKey);
        if (searchHistory) {
          this.setData({
            searchHistory: JSON.parse(searchHistory)
          });
          if (app && app.globalData && app.globalData.debugMode) {
            console.log('[搜索组件] 搜索历史加载成功，数量:', this.data.searchHistory.length);
          }
        } else {
          if (app && app.globalData && app.globalData.debugMode) {
            console.log('[搜索组件] 未找到搜索历史记录');
          }
          this.setData({
            searchHistory: []
          });
        }
      } catch (e) {
        console.error('[搜索组件] 加载搜索历史失败:', e);
        this.setData({
          searchHistory: []
        });
      }
    },
    
    // 将搜索历史保存到存储
    _saveSearchHistoryToStorage() {
      const history = this.data.searchHistory;
      if (app && app.globalData && app.globalData.debugMode) {
        console.log('[搜索组件] 保存历史记录到存储', history);
      }
      
      try {
        // 同步保存
        wx.setStorageSync(this.data._historyStorageKey, JSON.stringify(history));
        
        // 同时保存到全局变量
        if (getApp() && getApp().globalData) {
          getApp().globalData.searchHistory = history;
        }
      } catch (error) {
        console.error('[搜索组件] 同步保存历史记录失败', error);
        
        // 异步保存作为备用
        wx.setStorage({
          key: this.data._historyStorageKey,
          data: JSON.stringify(history),
          success: () => {
            if (app && app.globalData && app.globalData.debugMode) {
              console.log('[搜索组件] 异步保存历史记录成功');
            }
            
            // 更新全局变量
            if (getApp() && getApp().globalData) {
              getApp().globalData.searchHistory = history;
            }
          },
          fail: (error) => {
            console.error('[搜索组件] 异步保存历史记录也失败', error);
          }
        });
      }
    },
    
    // 添加搜索关键词到历史
    _addSearchKeyword(keyword) {
      if (!keyword || keyword.trim() === '') return;
      
      // 获取当前历史（确保是数组）
      let history = Array.isArray(this.data.searchHistory) ? [...this.data.searchHistory] : [];
      
      // 如果关键词已存在，先移除它
      history = history.filter(item => item !== keyword);
      
      // 添加新关键词到最前面
      history.unshift(keyword);
      
      // 限制最多保存10个关键词
      if (history.length > 10) {
        history = history.slice(0, 10);
      }
      
      // 更新组件状态
      this.setData({ searchHistory: history });
      
      // 保存到存储和全局变量
      this._saveSearchHistoryToStorage();
      
      if (app && app.globalData && app.globalData.debugMode) {
        console.log('[搜索组件] 添加关键词到历史', keyword, history);
      }
    },
    
    // 清除搜索历史
    clearSearchHistory() {
      try {
        // 清除组件状态
        this.setData({ searchHistory: [] });
        
        // 清除存储
        wx.removeStorageSync(this.data._historyStorageKey);
        
        // 清除全局变量
        if (getApp() && getApp().globalData) {
          getApp().globalData.searchHistory = [];
        }
        
        if (app && app.globalData && app.globalData.debugMode) {
          console.log('[搜索组件] 清除搜索历史成功');
        }
      } catch (error) {
        console.error('[搜索组件] 清除搜索历史失败', error);
        
        // 备用方案：使用异步API
        wx.removeStorage({
          key: this.data._historyStorageKey,
          complete: () => {
            if (app && app.globalData && app.globalData.debugMode) {
              console.log('[搜索组件] 异步清除搜索历史完成');
            }
          }
        });
      }
    },
    
    // 点击历史记录
    onHistoryItemTap(e) {
      const keyword = e.currentTarget.dataset.keyword;
      if (!keyword) return;
      
      // 设置搜索关键词
      this.setData({ searchValue: keyword });
      
      // 执行搜索
      this.performSearch(keyword);
    },
    
    // 点击热门搜索词
    onHotKeywordTap(e) {
      const keyword = e.currentTarget.dataset.keyword;
      if (!keyword) return;
      
      // 设置搜索关键词
      this.setData({ searchValue: keyword });
      
      // 执行搜索
      this.performSearch(keyword);
      
      // 保存到历史记录
      this._addSearchKeyword(keyword);
    },
    
    // 设置CSS变量，用于样式计算
    _setCssVariables() {
      // 获取根元素
      const query = wx.createSelectorQuery().in(this);
      query.select('.search-nav').fields({ computedStyle: ['--status-bar-height', '--nav-bar-height'] }).exec(res => {
        if (res && res[0]) {
          // 将变量设置到样式中
          wx.nextTick(() => {
            this.createSelectorQuery()
              .select('.search-nav')
              .node(function(node) {
                if (node && node.node) {
                  const style = node.node.style;
                  style.setProperty('--status-bar-height', `${this.data.statusBarHeight}px`);
                  style.setProperty('--nav-bar-height', `${this.data.navigationBarHeight}px`);
                }
              }.bind(this))
              .exec();
          });
        }
      });
    }
  }
}); 