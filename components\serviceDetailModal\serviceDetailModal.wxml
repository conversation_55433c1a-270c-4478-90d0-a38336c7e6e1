<!-- components/serviceDetailModal/serviceDetailModal.wxml -->
<view class="service-detail-modal {{show ? 'show' : ''}}" wx:if="{{show}}" bindtap="closeModal">
  <view class="modal-overlay {{showAnimation ? 'fade-in' : 'fade-out'}}">
    <view class="modal-content {{showAnimation ? 'breathe-in' : 'breathe-out'}}" catchtap="preventClose">
      <image 
        class="detail-image" 
        src="{{detailImageUrl}}" 
        mode="aspectFit"
        show-menu-by-longpress="{{true}}"
        bindload="onImageLoad"
        binderror="onImageError"
      />
      <view class="close-hint">点击空白区域关闭</view>
    </view>
  </view>
</view>
