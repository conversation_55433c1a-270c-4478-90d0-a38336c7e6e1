// 云函数入口文件
const cloud = require('wx-server-sdk');

// 添加日志控制函数
function log(message, data) {
  // 设置为false可以禁用所有日志，生产环境建议设置为false
  const enableLogging = true;
  if (enableLogging) {
    if (data !== undefined) {
      // 安全地处理数据，避免Symbol转换问题
      let safeData;
      if (typeof data === 'symbol') {
        safeData = data.description || 'Symbol';
      } else if (typeof data === 'object') {
        try {
          // 尝试安全地转换对象
          safeData = JSON.stringify(data, (key, value) => {
            if (typeof value === 'symbol') {
              return value.description || 'Symbol';
            }
            return value;
          }).substring(0, 500);
        } catch (e) {
          safeData = '[无法序列化的对象]';
        }
      } else {
        safeData = data;
      }
      console.log(message, safeData);
    } else {
      console.log(message);
    }
  }
}

// 获取云环境ID - 从环境变量获取，避免硬编码
const envId = process.env.CLOUD_ENV || cloud.DYNAMIC_CURRENT_ENV;

// 初始化云环境
cloud.init({
  env: envId
});

const db = cloud.database();
const _ = db.command;

// 定义常量
const COLLECTION_NAME = 'video_list'; // 视频集合名称
const VIDEO_FOLDER = 'videos'; // 视频存储文件夹
const COVER_FOLDER = 'covers'; // 封面图存储文件夹
const DETAIL_FOLDER = 'details'; // 详情图存储文件夹
const URL_EXPIRATION = 3600; // 临时链接有效期（秒）

// 初始化集合函数
async function initCollections() {
  try {
    // 检查并创建video_list集合
    try {
      await db.createCollection(COLLECTION_NAME);
      log('创建video_list集合成功');
      
      // 创建索引
      try {
        await db.collection(COLLECTION_NAME).createIndex({
          isVisible: 1,
          sortOrder: 1,
          createTime: -1
        });
        log('创建索引成功');
      } catch (indexErr) {
        // 索引可能已存在，这是正常的
        log('索引可能已存在:', indexErr.message);
      }
      
      // 设置集合权限为所有用户可读
      try {
        await cloud.callFunction({
          name: 'configManager',
          data: {
            action: 'setCollectionPermission',
            data: {
              collectionName: COLLECTION_NAME,
              readAccess: 'EVERYONE',
              writeAccess: 'OWNER'
            }
          }
        });
        log('已设置video_list集合权限为所有用户可读');
      } catch (permErr) {
        log('设置集合权限失败:', permErr);
      }
    } catch (err) {
      // 如果集合已存在，会抛出错误，这是正常的
      log('video_list集合已存在或创建失败:', err.message);
    }
    
    return true;
  } catch (err) {
    log('初始化集合失败:', err);
    return false;
  }
}

/**
 * 视频管理云函数
 * 用于管理视频资源，包括视频列表、详情、添加、更新、删除等功能
 * 前端/后端API在同一云函数中，但逻辑分离
 */
exports.main = async (event, context) => {
  // 初始化集合
  await initCollections();
  
  const wxContext = cloud.getWXContext();
  
  try {
    // 记录请求信息
    log('收到请求', {
      type: event.type,
      action: event.action,
      openid: wxContext.OPENID
    });
    
    // 检查是否为管理员API
    if (event.type === 'admin') {
      // 检查管理员权限
      const isAdmin = await checkAdminPermission(wxContext.OPENID);
      
      if (!isAdmin) {
        return {
          code: 403,
          message: '无管理员权限',
          data: null
        };
      }
      
      // 管理员API
      switch (event.action) {
        case 'getAllVideos':
          return await getAllVideos(event.data);
        case 'addVideo':
          return await addVideo(event.data);
        case 'updateVideo':
          return await updateVideo(event.data);
        case 'deleteVideo':
          return await deleteVideo(event.data);
        case 'toggleVisibility':
          return await toggleVisibility(event.data);
        case 'toggleVideoVisibility':
          return await toggleVideoVisibility(event.data);
        case 'updateOrder':
          return await updateOrder(event.data);
        case 'uploadVideo':
          return await uploadVideo2(event.data); // 使用新函数
        case 'uploadCover':
          return await uploadCover(event.data);
        case 'uploadDetail':
          return await uploadDetail(event.data);
        case 'initChunkUpload':
          return await initChunkUpload(event.data);
        case 'uploadChunk':
          return await uploadChunk(event.data);
        case 'completeChunkUpload':
          return await completeChunkUpload(event.data);
        case 'mergeChunks':
          return await mergeChunks(event.data);
        case 'alternativeMerge':
          return await alternativeMerge(event.data);
        case 'directUploadVideo':
          return await directUploadVideo2(event.data); // 使用新函数
        case 'uploadCoverViaCloudStorage':
          return await uploadCoverViaCloudStorage2(event.data); // 使用新函数
        case 'uploadDetailViaCloudStorage':
          return await uploadDetailViaCloudStorage2(event.data); // 使用新函数
        case 'compressAndUploadVideo':
          return await compressAndUploadVideo(event.data);
        case 'migrateVideoData':
          return await migrateVideoData();
        default:
          return {
            code: 404,
            message: '未知的管理员操作',
            data: null
          };
      }
    } else {
      // 用户API
      switch (event.action) {
        case 'getVisibleVideos':
          return await getVisibleVideos(event.data);
        case 'getVideoDetail':
          return await getVideoDetail(event.data);
        case 'increasePlayCount':
          return await increasePlayCount(event.data);
        default:
          return {
            code: 404,
            message: '未知的用户操作',
            data: null
          };
      }
    }
  } catch (error) {
    log('处理请求失败', error);
    return {
      code: 500,
      message: '处理请求失败: ' + error.message,
      data: null
    };
  }
};

/**
 * 检查是否有管理员权限
 * @param {string} openid 用户openid
 * @returns {boolean} 是否有管理员权限
 */
async function checkAdminPermission(openid) {
  try {
    // 获取WXContext
    const wxContext = cloud.getWXContext();
    log('当前用户OPENID:', openid);
    log('WXContext:', wxContext);
    
    // 简化权限检查：如果用户能够调用admin类型的接口，说明已经通过了前端的管理员验证
    // 这是基于前端已经做了管理员登录验证的假设
    if (wxContext.SOURCE === 'wx_devtools' || wxContext.SOURCE.includes('wx')) {
      log('用户通过小程序或开发工具访问，已通过管理员登录验证');
      return true;
    }
    
    // 如果需要更严格的验证，可以保留以下代码
    // 从配置中获取管理员列表
    const configResult = await cloud.callFunction({
      name: 'configManager',
      data: {
        action: 'getConfig',
        data: { configName: 'ADMIN_CONFIG' }
      }
    });
    
    if (configResult.result.code !== 200) {
      log('获取管理员配置失败');
      return false;
    }
    
    const adminConfig = configResult.result.data;
    log('管理员配置:', adminConfig);
    
    // 检查是否在管理员列表中
    const isInAdminList = adminConfig && 
                         adminConfig.adminOpenIds && 
                         adminConfig.adminOpenIds.includes(openid);
    
    // 检查是否为云函数创建者
    const isCreator = wxContext.FROM_OPENID === wxContext.OPENID;
    const isDefaultAdminEnabled = adminConfig && adminConfig.defaultAdmin === true;
    
    // 判断权限：在管理员列表中 或 (是创建者且允许默认管理员)
    const hasPermission = isInAdminList || (isCreator && isDefaultAdminEnabled);
    
    log('权限检查详情:', {
      isInAdminList,
      isCreator,
      isDefaultAdminEnabled,
      hasPermission
    });
    
    return hasPermission;
  } catch (error) {
    log('检查管理员权限失败', error);
    return false;
  }
}

/**
 * 获取配置信息
 * @returns {Object} 配置信息
 */
async function getConfig() {
  try {
    // 获取视频配置
    const videoConfigResult = await cloud.callFunction({
      name: 'configManager',
      data: {
        action: 'getVideoConfig'
      }
    });
    
    if (videoConfigResult.result.code !== 200) {
      throw new Error('获取视频配置失败');
    }
    
    const videoConfig = videoConfigResult.result.data;
    
    return {
      videoConfig,
      videoCollection: COLLECTION_NAME
    };
  } catch (error) {
    log('获取配置失败', error);
    throw error;
  }
}

/**
 * 生成临时访问链接
 * @param {string} fileID 云存储文件ID
 * @returns {string} 临时访问链接
 */
async function generateTempUrl(fileID) {
  if (!fileID) return '';
  
  try {
    // 检查fileID是否已经是云存储格式
    if (!fileID.startsWith('cloud://')) {
      log('文件ID不是云存储格式，无法生成临时链接:', fileID);
      return '';
    }
    
    // 获取临时链接
    const result = await cloud.getTempFileURL({
      fileList: [fileID],
      maxAge: URL_EXPIRATION
    });
    
    if (result && result.fileList && result.fileList.length > 0) {
      return result.fileList[0].tempFileURL || '';
    }
    
    return '';
  } catch (error) {
    log('生成临时链接失败', error);
    return '';
  }
}

/**
 * 为视频添加临时访问链接
 * @param {Array|Object} videos 视频列表或单个视频
 * @returns {Array|Object} 添加临时链接后的视频
 */
async function addTempUrlsToVideos(videos) {
  // 处理null或undefined情况
  if (!videos) {
    log('视频数据为空，无法添加临时链接');
    return videos;
  }
  
  // 处理单个视频
  if (!Array.isArray(videos)) {
    const video = videos;
    
    // 确保video对象存在
    if (!video) {
      log('单个视频对象为空，无法添加临时链接');
      return video;
    }
    
    // 向后兼容：处理老数据结构中使用key而非fileID的情况
    if (video.videoKey && !video.videoFileID) {
      video.videoFileID = video.videoKey;
    }
    
    if (video.coverKey && !video.coverFileID) {
      video.coverFileID = video.coverKey;
    }
    
    if (video.detailKey && !video.detailFileID) {
      video.detailFileID = video.detailKey;
    }
    
    // 生成视频URL
    if (video.videoFileID) {
      try {
        video.videoUrl = await generateTempUrl(video.videoFileID);
      } catch (error) {
        log('生成视频临时URL失败', error);
        video.videoUrl = '';
      }
      
      // 为确保前端兼容，同时设置videoKey
      if (!video.videoKey) {
        video.videoKey = video.videoFileID;
      }
    }
    
    // 生成封面URL
    if (video.coverFileID) {
      try {
        video.coverUrl = await generateTempUrl(video.coverFileID);
      } catch (error) {
        log('生成封面临时URL失败', error);
        video.coverUrl = '';
      }
      
      // 为确保前端兼容，同时设置coverKey
      if (!video.coverKey) {
        video.coverKey = video.coverFileID;
      }
    }
    
    // 生成详情图URL
    if (video.detailFileID) {
      try {
        video.detailUrl = await generateTempUrl(video.detailFileID);
      } catch (error) {
        log('生成详情图临时URL失败', error);
        video.detailUrl = '';
      }
      
      // 为确保前端兼容，同时设置detailKey
      if (!video.detailKey) {
        video.detailKey = video.detailFileID;
      }
    }
    
    // 处理多张详情图
    if (video.detailFileIDs && Array.isArray(video.detailFileIDs) && video.detailFileIDs.length > 0) {
      try {
        // 批量获取临时URL
        const result = await cloud.getTempFileURL({
          fileList: video.detailFileIDs,
          maxAge: URL_EXPIRATION
        });
        
        if (result && result.fileList && result.fileList.length > 0) {
          video.detailUrls = result.fileList.map(file => file.tempFileURL || '');
          // 为确保前端兼容，设置detailKeys
          if (!video.detailKeys) {
            video.detailKeys = video.detailFileIDs;
          }
        } else {
          video.detailUrls = [];
        }
      } catch (error) {
        log('批量获取详情图临时URL失败', error);
        video.detailUrls = [];
      }
    } else if (video.detailFileID) {
      // 如果只有单张详情图，也创建detailUrls数组
      video.detailUrls = [video.detailUrl || ''];
    }
    
    return video;
  }
  
  // 确保视频数组中没有null或undefined
  const validVideos = videos.filter(video => video);
  
  // 处理视频列表 - 先兼容旧数据结构
  validVideos.forEach(video => {
    // 向后兼容：处理老数据结构中使用key而非fileID的情况
    if (video.videoKey && !video.videoFileID) {
      video.videoFileID = video.videoKey;
    }
    
    if (video.coverKey && !video.coverFileID) {
      video.coverFileID = video.coverKey;
    }
    
    if (video.detailKey && !video.detailFileID) {
      video.detailFileID = video.detailKey;
    }
    
    // 确保初始化属性避免访问未定义
    video.videoUrl = video.videoUrl || '';
    video.coverUrl = video.coverUrl || '';
    video.detailUrl = video.detailUrl || '';
    video.detailUrls = video.detailUrls || [];
  });
  
  // 收集所有需要转换的fileID
  const fileIDsToConvert = [];
  const fileIDMap = new Map(); // 用于映射fileID到视频对象和属性
  
  validVideos.forEach((video, index) => {
    if (video.videoFileID) {
      fileIDsToConvert.push(video.videoFileID);
      fileIDMap.set(video.videoFileID, { index, prop: 'videoUrl' });
      // 为确保前端兼容，同时设置videoKey
      if (!video.videoKey) {
        video.videoKey = video.videoFileID;
      }
    }
    
    if (video.coverFileID) {
      fileIDsToConvert.push(video.coverFileID);
      fileIDMap.set(video.coverFileID, { index, prop: 'coverUrl' });
      // 为确保前端兼容，同时设置coverKey
      if (!video.coverKey) {
        video.coverKey = video.coverFileID;
      }
    }
    
    if (video.detailFileID) {
      fileIDsToConvert.push(video.detailFileID);
      fileIDMap.set(video.detailFileID, { index, prop: 'detailUrl' });
      // 为确保前端兼容，同时设置detailKey
      if (!video.detailKey) {
        video.detailKey = video.detailFileID;
      }
    }
    
    // 处理多张详情图
    if (video.detailFileIDs && Array.isArray(video.detailFileIDs)) {
      video.detailFileIDs.forEach((fileID, detailIndex) => {
        if (fileID) {
          fileIDsToConvert.push(fileID);
          fileIDMap.set(fileID, { index, prop: 'detailUrls', detailIndex });
        }
      });
      
      // 为确保前端兼容，设置detailKeys
      if (!video.detailKeys) {
        video.detailKeys = video.detailFileIDs;
      }
    }
  });
  
  // 如果没有需要转换的fileID，直接返回
  if (fileIDsToConvert.length === 0) {
    return validVideos;
  }
  
  try {
    // 批量获取临时URL
    const result = await cloud.getTempFileURL({
      fileList: fileIDsToConvert,
      maxAge: URL_EXPIRATION
    });
    
    if (result && result.fileList) {
      // 将临时URL应用到视频对象
      result.fileList.forEach(file => {
        const mapping = fileIDMap.get(file.fileID);
        if (mapping) {
          const { index, prop, detailIndex } = mapping;
          
          if (prop === 'detailUrls' && detailIndex !== undefined) {
            // 处理详情图数组
            validVideos[index].detailUrls[detailIndex] = file.tempFileURL || '';
          } else {
            // 处理普通属性
            validVideos[index][prop] = file.tempFileURL || '';
          }
        }
      });
    }
  } catch (error) {
    log('批量获取临时URL失败', error);
  }
  
  return validVideos;
}

/**
 * 获取可见的视频列表（前端接口）
 * @param {Object} data 请求参数
 * @returns {Object} 响应结果
 */
async function getVisibleVideos(data) {
  try {
    const config = await getConfig();
    const { videoConfig } = config;
    
    // 分页参数
    const pageSize = data?.pageSize || videoConfig.pagination.frontendPageSize;
    const page = data?.page || 1;
    const skip = (page - 1) * pageSize;
    
    // 排序参数
    const sortField = data?.sortField || videoConfig.db.sortField;
    const sortDirection = data?.sortDirection || videoConfig.db.sortDirection;
    
    // 是否使用displayTime排序（下拉刷新时使用）
    const useDisplayTime = data?.useDisplayTime === true;
    
    // 查询可见视频
    const countResult = await db.collection(COLLECTION_NAME)
      .where({ isVisible: true })
      .count();
    
    const total = countResult.total;
    
    // 准备查询
    let query = db.collection(COLLECTION_NAME)
      .where({ isVisible: true });
    
    // 如果使用displayTime排序，则按照displayTime降序排序（新的在前）
    // 否则按照配置的sortField排序
    if (useDisplayTime) {
      // 先查询所有符合条件的记录
      const allVideos = await query.get();
      
      // 手动排序：优先按displayTime（新的在前），其次按sortOrder
      const sortedVideos = allVideos.data.sort((a, b) => {
        // 如果两者都有displayTime，按时间降序排序（新的在前）
        if (a.displayTime && b.displayTime) {
          return new Date(b.displayTime) - new Date(a.displayTime);
        }
        
        // 如果只有一个有displayTime，有的排在前面
        if (a.displayTime && !b.displayTime) return -1;
        if (!a.displayTime && b.displayTime) return 1;
        
        // 如果都没有displayTime，按sortOrder升序排序
        const aOrder = typeof a.sortOrder === 'number' ? a.sortOrder : Number.MAX_SAFE_INTEGER;
        const bOrder = typeof b.sortOrder === 'number' ? b.sortOrder : Number.MAX_SAFE_INTEGER;
        return aOrder - bOrder;
      });
      
      // 分页处理
      const paginatedVideos = sortedVideos.slice(skip, skip + pageSize);
      
      // 添加临时链接 - 移除cos和config参数
      const videosWithUrls = await addTempUrlsToVideos(paginatedVideos);
      
      return {
        code: 200,
        message: '获取视频列表成功（显示时间排序）',
        data: {
          videos: videosWithUrls,
          total,
          page,
          pageSize,
          totalPages: Math.ceil(total / pageSize)
        }
      };
    } else {
      // 使用默认排序
      const result = await query
        .orderBy(sortField, sortDirection)
        .skip(skip)
        .limit(pageSize)
        .get();
      
      // 添加临时链接 - 移除cos和config参数
      const videosWithUrls = await addTempUrlsToVideos(result.data);
      
      return {
        code: 200,
        message: '获取视频列表成功',
        data: {
          videos: videosWithUrls,
          total,
          page,
          pageSize,
          totalPages: Math.ceil(total / pageSize)
        }
      };
    }
  } catch (error) {
    log('获取视频列表失败', error);
    return {
      code: 500,
      message: '获取视频列表失败: ' + error.message,
      data: null
    };
  }
}

/**
 * 获取所有视频列表（后台接口）
 * @param {Object} data 请求参数
 * @returns {Object} 响应结果
 */
async function getAllVideos(data) {
  try {
    const config = await getConfig();
    const { videoConfig } = config;
    
    // 分页参数
    const pageSize = data?.pageSize || videoConfig.pagination.adminPageSize;
    const page = data?.page || 1;
    const skip = (page - 1) * pageSize;
    
    // 排序参数
    const sortField = data?.sortField || videoConfig.db.sortField;
    const sortDirection = data?.sortDirection || videoConfig.db.sortDirection;
    
    // 查询条件
    const query = {};
    
    // 添加可见性筛选
    if (data?.visibilityFilter === 'visible') {
      query.isVisible = true;
    } else if (data?.visibilityFilter === 'hidden') {
      query.isVisible = false;
    }
    
    // 查询总数
    const countResult = await db.collection(COLLECTION_NAME)
      .where(query)
      .count();
    
    const total = countResult.total;
    
    // 查询数据
    const result = await db.collection(COLLECTION_NAME)
      .where(query)
      .orderBy(sortField, sortDirection)
      .skip(skip)
      .limit(pageSize)
      .get();
    
    // 添加临时链接 - 移除cos和config参数
    const videosWithUrls = await addTempUrlsToVideos(result.data);
    
    return {
      code: 200,
      message: '获取视频列表成功',
      data: {
        videos: videosWithUrls,
        total,
        page,
        pageSize,
        totalPages: Math.ceil(total / pageSize)
      }
    };
  } catch (error) {
    log('获取视频列表失败', error);
    return {
      code: 500,
      message: '获取视频列表失败: ' + error.message,
      data: null
    };
  }
}

/**
 * 获取视频详情
 * @param {Object} data 请求参数
 * @returns {Object} 响应结果
 */
async function getVideoDetail(data) {
  try {
    // 兼容不同的参数名称：videoId或id
    const videoId = data?.videoId || data?.id;
    
    if (!videoId) {
      return {
        code: 400,
        message: '缺少视频ID',
        data: null
      };
    }
    
    const config = await getConfig();
    
    // 查询视频详情
    const result = await db.collection(COLLECTION_NAME)
      .doc(videoId)
      .get();
    
    if (!result.data) {
      return {
        code: 404,
        message: '视频不存在',
        data: null
      };
    }
    
    // 如果是前端请求，检查视频是否可见
    if (!data.isAdmin && !result.data.isVisible) {
      return {
        code: 403,
        message: '该视频不可见',
        data: null
      };
    }
    
    // 添加临时链接
    const videoWithUrls = await addTempUrlsToVideos(result.data);
    
    return {
      code: 200,
      message: '获取视频详情成功',
      data: videoWithUrls
    };
  } catch (error) {
    log('获取视频详情失败', error);
    return {
      code: 500,
      message: '获取视频详情失败: ' + error.message,
      data: null
    };
  }
}

/**
 * 增加播放量
 * @param {Object} data 请求参数
 * @returns {Object} 响应结果
 */
async function increasePlayCount(data) {
  try {
    if (!data?.id) {
      return {
        code: 400,
        message: '缺少视频ID',
        data: null
      };
    }
    
    const config = await getConfig();
    const { videoConfig } = config;
    
    // 更新播放量
    await db.collection(videoConfig.db.collection)
      .doc(data.id)
      .update({
        data: {
          playCount: _.inc(1),
          updateTime: db.serverDate()
        }
      });
    
    return {
      code: 200,
      message: '增加播放量成功',
      data: null
    };
  } catch (error) {
    log('增加播放量失败', error);
    return {
      code: 500,
      message: '增加播放量失败: ' + error.message,
      data: null
    };
  }
}

/**
 * 添加视频
 * @param {Object} data 请求参数
 * @returns {Object} 响应结果
 */
async function addVideo(data) {
  try {
    if (!data?.mainTitle || !data?.videoKey || !data?.coverKey) {
      return {
        code: 400,
        message: '缺少必要参数',
        data: null
      };
    }
    
    const config = await getConfig();
    const { videoConfig } = config;
    
    // 获取最小排序值
    let minOrder = 1;
    const orderResult = await db.collection(videoConfig.db.collection)
      .orderBy(videoConfig.db.sortField, 'asc')
      .limit(1)
      .get();
    
    if (orderResult.data.length > 0) {
      minOrder = orderResult.data[0][videoConfig.db.sortField] - 1;
    }
    
    // 创建视频记录
    const result = await db.collection(videoConfig.db.collection)
      .add({
        data: {
          mainTitle: data.mainTitle,
          subTitle: data.subTitle || '',
          playCount: data.playCount || 0,
          [videoConfig.db.sortField]: data.sortOrder || minOrder,
          videoKey: data.videoKey,
          coverKey: data.coverKey,
          detailKey: data.detailKey || '',
          isVisible: data.isVisible !== undefined ? data.isVisible : true,
          createTime: db.serverDate(),
          updateTime: db.serverDate()
        }
      });
    
    return {
      code: 200,
      message: '添加视频成功',
      data: { id: result._id }
    };
  } catch (error) {
    log('添加视频失败', error);
    return {
      code: 500,
      message: '添加视频失败: ' + error.message,
      data: null
    };
  }
}

/**
 * 更新视频
 * @param {Object} data 请求参数
 * @returns {Object} 响应结果
 */
async function updateVideo(data) {
  try {
    if (!data?.id) {
      return {
        code: 400,
        message: '缺少视频ID',
        data: null
      };
    }
    
    const config = await getConfig();
    const { videoConfig } = config;
    
    // 过滤掉id字段和不允许直接修改的字段
    const { id, createTime, _id, _openid, ...updateData } = data;
    
    // 添加更新时间
    updateData.updateTime = db.serverDate();
    
    // 更新视频记录
    await db.collection(videoConfig.db.collection)
      .doc(id)
      .update({
        data: updateData
      });
    
    return {
      code: 200,
      message: '更新视频成功',
      data: null
    };
  } catch (error) {
    log('更新视频失败', error);
    return {
      code: 500,
      message: '更新视频失败: ' + error.message,
      data: null
    };
  }
}

/**
 * 删除视频
 * @param {Object} data 请求参数
 * @returns {Object} 响应结果
 */
async function deleteVideo(data) {
  try {
    if (!data?.id) {
      return {
        code: 400,
        message: '缺少视频ID',
        data: null
      };
    }
    
    const config = await getConfig();
    const { cos, videoConfig, storageConfig } = config;
    
    // 获取视频信息
    const video = await db.collection(videoConfig.db.collection)
      .doc(data.id)
      .get();
    
    if (!video.data) {
      return {
        code: 404,
        message: '视频不存在',
        data: null
      };
    }
    
    // 删除相关文件
    const keysToDelete = [];
    if (video.data.videoKey) keysToDelete.push({ Key: video.data.videoKey });
    if (video.data.coverKey) keysToDelete.push({ Key: video.data.coverKey });
    if (video.data.detailKey) keysToDelete.push({ Key: video.data.detailKey });
    
    if (keysToDelete.length > 0) {
      await new Promise((resolve, reject) => {
        cos.deleteMultipleObject({
          Bucket: storageConfig.bucket,
          Region: storageConfig.region,
          Objects: keysToDelete
        }, (err, data) => {
          if (err) {
            log('删除COS文件失败', err);
            // 继续执行，不中断流程
          }
          resolve();
        });
      });
    }
    
    // 删除数据库记录
    await db.collection(videoConfig.db.collection)
      .doc(data.id)
      .remove();
    
    return {
      code: 200,
      message: '删除视频成功',
      data: null
    };
  } catch (error) {
    log('删除视频失败', error);
    return {
      code: 500,
      message: '删除视频失败: ' + error.message,
      data: null
    };
  }
}

/**
 * 切换视频可见性
 * @param {Object} data 请求参数
 * @returns {Object} 响应结果
 */
async function toggleVisibility(data) {
  try {
    if (!data?.id) {
      return {
        code: 400,
        message: '缺少视频ID',
        data: null
      };
    }
    
    const config = await getConfig();
    const { videoConfig } = config;
    
    // 获取视频信息
    const video = await db.collection(videoConfig.db.collection)
      .doc(data.id)
      .get();
    
    if (!video.data) {
      return {
        code: 404,
        message: '视频不存在',
        data: null
      };
    }
    
    // 切换可见性
    const newVisibility = !video.data.isVisible;
    
    await db.collection(videoConfig.db.collection)
      .doc(data.id)
      .update({
        data: {
          isVisible: newVisibility,
          updateTime: db.serverDate()
        }
      });
    
    return {
      code: 200,
      message: `视频已${newVisibility ? '显示' : '隐藏'}`,
      data: { isVisible: newVisibility }
    };
  } catch (error) {
    log('切换视频可见性失败', error);
    return {
      code: 500,
      message: '切换视频可见性失败: ' + error.message,
      data: null
    };
  }
}

/**
 * 更新排序
 * @param {Object} data 请求参数
 * @returns {Object} 响应结果
 */
async function updateOrder(data) {
  try {
    if (!data?.id || data?.sortOrder === undefined) {
      return {
        code: 400,
        message: '缺少必要参数',
        data: null
      };
    }
    
    const config = await getConfig();
    const { videoConfig } = config;
    
    // 更新排序
    await db.collection(videoConfig.db.collection)
      .doc(data.id)
      .update({
        data: {
          [videoConfig.db.sortField]: data.sortOrder,
          updateTime: db.serverDate()
        }
      });
    
    return {
      code: 200,
      message: '更新排序成功',
      data: null
    };
  } catch (error) {
    log('更新排序失败', error);
    return {
      code: 500,
      message: '更新排序失败: ' + error.message,
      data: null
    };
  }
}

/**
 * 上传视频到云开发存储（替代COS版本）
 * @param {Object} data 请求参数
 * @returns {Object} 响应结果
 */
async function uploadVideo2(data) {
  try {
    // 检查参数
    if ((!data?.fileContent && !data?.fileID) || !data?.fileName) {
      return {
        code: 400,
        message: '缺少必要参数',
        data: null
      };
    }
    
    // 安全处理参数
    let fileID = '';
    let fileName = '';
    
    try {
      if (data.fileID) {
        fileID = String(data.fileID);
      }
      
      if (data.fileName) {
        fileName = String(data.fileName);
      }
    } catch (paramError) {
      log('参数处理错误', paramError);
      return {
        code: 400,
        message: '参数格式错误',
        data: null
      };
    }
    
    // 记录原始文件名，用于调试
    log('上传视频，原始文件名:', fileName);
    
    // 处理文件名，移除可能导致问题的特殊字符
    let safeFileName = fileName.replace(/[^\w\d.-]/g, '_');
    
    // 生成唯一文件名
    const timestamp = Date.now();
    const uniqueSuffix = timestamp.toString(36) + Math.random().toString(36).substr(2, 5);
    
    // 获取文件扩展名，如果没有或无法识别，默认使用mp4
    let fileExt = 'mp4';
    const extMatch = safeFileName.match(/\.([^.]+)$/);
    if (extMatch && extMatch[1]) {
      fileExt = extMatch[1].toLowerCase();
    }
    
    // 安全获取常量
    const videoFolder = String(VIDEO_FOLDER || 'videos');
    const cloudPath = `${videoFolder}/${uniqueSuffix}_${safeFileName}`;
    
    log('上传视频，处理后的文件名:', cloudPath);
    
    // 使用云存储API
    if (data.useCloudStorage && fileID) {
      log('从云存储获取视频文件:', fileID);
      
      try {
        // 下载临时文件
        log('视频上传 - 开始下载临时文件', fileID);
        const downloadResult = await cloud.downloadFile({
          fileID: fileID
        });
        
        if (!downloadResult || !downloadResult.fileContent) {
          log('视频上传 - 下载临时文件失败', downloadResult || '无返回结果');
          return {
            code: 500,
            message: '下载临时视频文件失败',
            data: null
          };
        }
        
        log('视频上传 - 临时文件下载成功，文件大小:', downloadResult.fileContent.length);
        
        // 上传到永久路径
        log('视频上传 - 开始上传到永久存储', cloudPath);
        const uploadResult = await cloud.uploadFile({
          cloudPath: cloudPath,
          fileContent: downloadResult.fileContent
        });
        
        if (!uploadResult || !uploadResult.fileID) {
          log('视频上传 - 上传到永久存储失败', uploadResult || '无返回结果');
          return {
            code: 500,
            message: '上传到永久存储失败',
            data: null
          };
        }
        
        // 安全处理结果
        const resultFileID = String(uploadResult.fileID || '');
        log('视频上传 - 上传成功', resultFileID);
        
        // 删除临时云存储文件
        try {
          await cloud.deleteFile({
            fileList: [fileID]
          });
          log('已删除临时云存储文件:', fileID);
        } catch (deleteErr) {
          log('删除临时云存储文件失败，但不影响主流程:', deleteErr);
        }
        
        // 生成临时访问链接
        let tempUrl = '';
        try {
          tempUrl = await generateTempUrl(resultFileID);
          log('视频上传 - 生成临时链接成功');
        } catch (urlError) {
          log('视频上传 - 生成临时URL失败', urlError);
        }
        
        return {
          code: 200,
          message: '上传视频成功',
          data: {
            fileID: resultFileID,
            key: resultFileID,
            tempUrl: tempUrl,
            fileType: fileExt
          }
        };
      } catch (processError) {
        log('视频上传处理过程出错', processError);
        throw new Error('视频处理失败: ' + String(processError.message || '未知错误'));
      }
    } else if (data.fileContent) {
      // 处理base64内容上传
      try {
        // 将base64内容转换为Buffer
        const fileContent = Buffer.from(data.fileContent, 'base64');
        
        // 上传到云存储
        log('视频上传 - 开始上传base64内容', cloudPath);
        const uploadResult = await cloud.uploadFile({
          cloudPath: cloudPath,
          fileContent: fileContent
        });
        
        if (!uploadResult || !uploadResult.fileID) {
          log('视频上传 - 上传到云存储失败', uploadResult || '无返回结果');
          return {
            code: 500,
            message: '上传到云存储失败',
            data: null
          };
        }
        
        // 安全处理结果
        const resultFileID = String(uploadResult.fileID || '');
        log('视频上传 - 上传成功', resultFileID);
        
        // 生成临时访问链接
        let tempUrl = '';
        try {
          tempUrl = await generateTempUrl(resultFileID);
          log('视频上传 - 生成临时链接成功');
        } catch (urlError) {
          log('视频上传 - 生成临时URL失败', urlError);
        }
        
        return {
          code: 200,
          message: '上传视频成功',
          data: {
            fileID: resultFileID,
            key: resultFileID,
            tempUrl: tempUrl,
            fileType: fileExt
          }
        };
      } catch (processError) {
        log('视频上传处理过程出错', processError);
        throw new Error('视频处理失败: ' + String(processError.message || '未知错误'));
      }
    } else {
      throw new Error('缺少视频内容或云存储ID');
    }
  } catch (error) {
    log('上传视频失败', error);
    // 提供更详细的错误信息
    let errorMessage = '上传视频失败';
    if (error.message) {
      if (error.message.includes('exceed')) {
        errorMessage = '视频文件过大，请压缩后再上传';
      } else if (error.message.includes('format')) {
        errorMessage = '视频格式不支持，请转换为MP4格式';
      } else {
        errorMessage = '上传视频失败: ' + error.message;
      }
    }
    return {
      code: 500,
      message: errorMessage,
      data: null
    };
  }
}

/**
 * 上传封面图
 * @param {Object} data 请求参数
 * @returns {Object} 响应结果
 */
async function uploadCover(data) {
  try {
    if (!data?.fileContent || !data?.fileName) {
      return {
        code: 400,
        message: '缺少必要参数',
        data: null
      };
    }
    
    const config = await getConfig();
    const { cos, videoConfig, storageConfig } = config;
    
    // 记录原始文件名，用于调试
    log('上传封面图，原始文件名:', data.fileName);
    
    // 处理文件名，移除可能导致问题的特殊字符
    let safeFileName = data.fileName.replace(/[^\w\d.-]/g, '_');
    
    // 生成唯一文件名
    const timestamp = Date.now();
    const uniqueSuffix = timestamp.toString(36) + Math.random().toString(36).substr(2, 5);
    
    // 获取文件扩展名，如果没有或无法识别，默认使用jpg
    let fileExt = 'jpg';
    const extMatch = safeFileName.match(/\.([^.]+)$/);
    if (extMatch && extMatch[1]) {
      fileExt = extMatch[1].toLowerCase();
    }
    
    // 构建存储路径
    const key = `${videoConfig.storage.paths.covers}${uniqueSuffix}_${safeFileName}`;
    
    log('上传封面图，处理后的文件名:', key);
    log('上传封面图，文件类型:', fileExt);
    
    // 上传文件
    const uploadResult = await new Promise((resolve, reject) => {
      try {
        cos.putObject({
          Bucket: storageConfig.bucket,
          Region: storageConfig.region,
          Key: key,
          Body: Buffer.from(data.fileContent, 'base64'),
          ContentType: getContentType(fileExt)
        }, (err, result) => {
          if (err) {
            log('COS上传封面图失败:', err);
            reject(err);
          } else {
            resolve(result);
          }
        });
      } catch (err) {
        log('COS上传封面图异常:', err);
        reject(err);
      }
    });
    
    // 生成临时访问链接
    const tempUrl = await generateTempUrl(cos, config, key);
    
    log('封面图上传成功，生成临时链接:', tempUrl.substring(0, 100) + '...');
    
    return {
      code: 200,
      message: '上传封面图成功',
      data: {
        key,
        tempUrl,
        etag: uploadResult.ETag,
        fileType: fileExt
      }
    };
  } catch (error) {
    log('上传封面图失败', error);
    // 提供更详细的错误信息
    let errorMessage = '上传封面图失败';
    if (error.message) {
      if (error.message.includes('exceed')) {
        errorMessage = '图片文件过大，请压缩后再上传';
      } else if (error.message.includes('format')) {
        errorMessage = '图片格式不支持，请转换为JPG、PNG或WEBP格式';
      } else {
        errorMessage = '上传封面图失败: ' + error.message;
      }
    }
    return {
      code: 500,
      message: errorMessage,
      data: null
    };
  }
}

/**
 * 上传详情图片
 * @param {Object} data 上传数据
 * @returns {Object} 响应结果
 */
async function uploadDetail(data) {
  try {
    if (!data?.fileContent || !data?.fileName) {
      return {
        code: 400,
        message: '缺少必要参数',
        data: null
      };
    }
    
    const config = await getConfig();
    const { cos, videoConfig, storageConfig } = config;
    
    // 记录原始文件名，用于调试
    log('上传详情图片，原始文件名:', data.fileName);
    
    // 处理文件名，移除可能导致问题的特殊字符
    let safeFileName = data.fileName.replace(/[^\w\d.-]/g, '_');
    
    // 确保文件名格式正确
    // 格式应该是: detail_视频ID_时间戳_随机字符串.扩展名
    let fileName = safeFileName;
    
    // 获取文件扩展名，如果没有或无法识别，默认使用jpg
    let fileExt = 'jpg';
    const extMatch = safeFileName.match(/\.([^.]+)$/);
    if (extMatch && extMatch[1]) {
      fileExt = extMatch[1].toLowerCase();
    } else {
      // 如果没有扩展名，添加默认扩展名
      fileName = `${fileName}.jpg`;
    }
    
    // 确保路径正确
    let key = fileName;
    if (!key.startsWith(videoConfig.storage.paths.details)) {
      key = `${videoConfig.storage.paths.details}${fileName}`;
    }
    
    log('上传详情图片，处理后的文件名:', key);
    log('上传详情图片，文件类型:', fileExt);
    
    // 上传文件
    const uploadResult = await new Promise((resolve, reject) => {
      try {
        cos.putObject({
          Bucket: storageConfig.bucket,
          Region: storageConfig.region,
          Key: key,
          Body: Buffer.from(data.fileContent, 'base64'),
          ContentType: getContentType(fileExt)
        }, (err, result) => {
          if (err) {
            log('COS上传详情图片失败:', err);
            reject(err);
          } else {
            resolve(result);
          }
        });
      } catch (err) {
        log('COS上传详情图片异常:', err);
        reject(err);
      }
    });
    
    // 生成临时访问链接
    const tempUrl = await generateTempUrl(cos, config, key);
    
    log('详情图片上传成功，生成临时链接:', tempUrl.substring(0, 100) + '...');
    
    return {
      code: 200,
      message: '上传详情图成功',
      data: {
        key,
        tempUrl,
        etag: uploadResult.ETag,
        fileType: fileExt
      }
    };
  } catch (error) {
    log('上传详情图失败', error);
    // 提供更详细的错误信息
    let errorMessage = '上传详情图失败';
    if (error.message) {
      if (error.message.includes('exceed')) {
        errorMessage = '图片文件过大，请压缩后再上传';
      } else if (error.message.includes('format')) {
        errorMessage = '图片格式不支持，请转换为JPG、PNG或WEBP格式';
      } else {
        errorMessage = '上传详情图失败: ' + error.message;
      }
    }
    return {
      code: 500,
      message: errorMessage,
      data: null
    };
  }
}

/**
 * 初始化分片上传，创建上传任务
 * @param {Object} data 请求参数
 * @returns {Object} 响应结果
 */
async function initChunkUpload(data) {
  try {
    if (!data?.fileName || !data?.totalChunks || !data?.fileSize) {
      return {
        code: 400,
        message: '缺少必要参数',
        data: null
      };
    }

    const config = await getConfig();
    const { cos, videoConfig, storageConfig } = config;
    
    // 生成唯一上传ID
    const uploadId = Date.now().toString(36) + Math.random().toString(36).substr(2, 10);
    
    // 处理文件名，移除可能导致问题的特殊字符
    let safeFileName = data.fileName.replace(/[^\w\d.-]/g, '_');
    
    // 获取文件扩展名，如果没有或无法识别，默认使用mp4
    let fileExt = 'mp4';
    const extMatch = safeFileName.match(/\.([^.]+)$/);
    if (extMatch && extMatch[1]) {
      fileExt = extMatch[1].toLowerCase();
    }
    
    // 构建最终存储路径
    const timestamp = Date.now();
    const uniqueSuffix = timestamp.toString(36) + Math.random().toString(36).substr(2, 5);
    const key = `${videoConfig.storage.paths.videos}${uniqueSuffix}_${safeFileName}`;
    
    // 创建临时存储文件夹
    const tempDir = 'temp_uploads/' + uploadId + '/';
    
    // 存储上传信息到云数据库，用于跟踪上传进度和状态
    await db.collection('upload_tasks').add({
      data: {
        uploadId,
        fileName: safeFileName,
        fileSize: data.fileSize,
        totalChunks: data.totalChunks,
        uploadedChunks: [],
        targetKey: key,
        fileExt,
        status: 'initialized',
        createdAt: db.serverDate(),
        updatedAt: db.serverDate()
      }
    });
    
    log('初始化分片上传任务', { uploadId, fileName: safeFileName, totalChunks: data.totalChunks });
    
    return {
      code: 0,
      message: '初始化上传任务成功',
      data: {
        uploadId,
        targetKey: key
      }
    };
  } catch (error) {
    log('初始化分片上传失败', error);
    return {
      code: 500,
      message: '初始化上传任务失败: ' + error.message,
      data: null
    };
  }
}

/**
 * 上传单个分片
 * @param {Object} data 请求参数
 * @returns {Object} 响应结果
 */
async function uploadChunk(data) {
  try {
    if (!data?.uploadId || !data?.chunkIndex || !data?.chunkData || data?.chunkIndex < 0) {
      return {
        code: 400,
        message: '缺少必要参数',
        data: null
      };
    }
    
    const config = await getConfig();
    const { cos, storageConfig } = config;
    
    // 从数据库获取上传任务信息
    const taskResult = await db.collection('upload_tasks').where({
      uploadId: data.uploadId
    }).get();
    
    if (!taskResult.data || taskResult.data.length === 0) {
      return {
        code: 404,
        message: '上传任务不存在',
        data: null
      };
    }
    
    const uploadTask = taskResult.data[0];
    const chunkIndex = parseInt(data.chunkIndex);
    
    // 确保chunkIndex有效
    if (chunkIndex < 0 || chunkIndex >= uploadTask.totalChunks) {
      return {
        code: 400,
        message: '无效的分片索引',
        data: null
      };
    }
    
    // 检查此分片是否已上传
    if (uploadTask.uploadedChunks && uploadTask.uploadedChunks.includes(chunkIndex)) {
      return {
        code: 0,
        message: '分片已上传',
        data: {
          chunkIndex,
          uploadedChunks: uploadTask.uploadedChunks
        }
      };
    }
    
    // 构建临时分片存储路径
    const tempDir = 'temp_uploads/' + data.uploadId + '/';
    const chunkKey = `${tempDir}chunk_${chunkIndex}`;
    
    // 上传分片到COS
    await new Promise((resolve, reject) => {
      try {
        cos.putObject({
          Bucket: storageConfig.bucket,
          Region: storageConfig.region,
          Key: chunkKey,
          Body: Buffer.from(data.chunkData, 'base64')
        }, (err, result) => {
          if (err) {
            log('上传分片失败:', err);
            reject(err);
          } else {
            resolve(result);
          }
        });
      } catch (err) {
        log('上传分片异常:', err);
        reject(err);
      }
    });
    
    // 更新上传任务状态
    const updatedChunks = uploadTask.uploadedChunks || [];
    if (!updatedChunks.includes(chunkIndex)) {
      updatedChunks.push(chunkIndex);
    }
    
    await db.collection('upload_tasks').doc(uploadTask._id).update({
      data: {
        uploadedChunks: updatedChunks,
        updatedAt: db.serverDate()
      }
    });
    
    log('分片上传成功', { uploadId: data.uploadId, chunkIndex, totalUploaded: updatedChunks.length });
    
    return {
      code: 0,
      message: '分片上传成功',
      data: {
        chunkIndex,
        uploadedChunks: updatedChunks,
        progress: Math.floor(updatedChunks.length / uploadTask.totalChunks * 100)
      }
    };
  } catch (error) {
    log('上传分片失败', error);
    return {
      code: 500,
      message: '上传分片失败: ' + error.message,
      data: null
    };
  }
}

/**
 * 完成分片上传，合并所有分片
 * @param {Object} data 请求参数
 * @returns {Object} 响应结果
 */
async function completeChunkUpload(data) {
  try {
    if (!data?.uploadId) {
      return {
        code: 400,
        message: '缺少必要参数',
        data: null
      };
    }
    
    const config = await getConfig();
    const { cos, storageConfig } = config;
    
    // 从数据库获取上传任务信息
    const taskResult = await db.collection('upload_tasks').where({
      uploadId: data.uploadId
    }).get();
    
    if (!taskResult.data || taskResult.data.length === 0) {
      return {
        code: 404,
        message: '上传任务不存在',
        data: null
      };
    }
    
    const uploadTask = taskResult.data[0];
    
    // 检查是否所有分片都已上传
    if (!uploadTask.uploadedChunks || uploadTask.uploadedChunks.length !== uploadTask.totalChunks) {
      return {
        code: 400,
        message: `分片上传不完整，已上传${uploadTask.uploadedChunks ? uploadTask.uploadedChunks.length : 0}/${uploadTask.totalChunks}`,
        data: null
      };
    }
    
    // 更新任务状态为"合并中"
    await db.collection('upload_tasks').doc(uploadTask._id).update({
      data: {
        status: 'merging',
        updatedAt: db.serverDate()
      }
    });
    
    // 准备合并分片的参数
    const tempDir = 'temp_uploads/' + data.uploadId + '/';
    const objects = [];
    
    // 按顺序排列分片
    const sortedChunks = [...uploadTask.uploadedChunks].sort((a, b) => a - b);
    
    // 构建对象数组
    for (const index of sortedChunks) {
      objects.push({
        Key: `${tempDir}chunk_${index}`
      });
    }
    
    // 合并所有分片到最终文件
    await new Promise((resolve, reject) => {
      cos.uploadPartCopy({
        Bucket: storageConfig.bucket,
        Region: storageConfig.region,
        Key: uploadTask.targetKey,
        Objects: objects,
        SliceSize: 20 * 1024 * 1024, // 20MB分片
        ContentType: getContentType(uploadTask.fileExt)
      }, (err, data) => {
        if (err) {
          log('合并分片失败:', err);
          reject(err);
        } else {
          resolve(data);
        }
      });
    });
    
    // 生成临时访问链接
    const tempUrl = await generateTempUrl(cos, config, uploadTask.targetKey);
    
    // 清理临时分片文件
    try {
      for (const index of sortedChunks) {
        await new Promise((resolve) => {
          cos.deleteObject({
            Bucket: storageConfig.bucket,
            Region: storageConfig.region,
            Key: `${tempDir}chunk_${index}`
          }, () => resolve());
        });
      }
    } catch (cleanupError) {
      log('清理临时分片文件失败', cleanupError);
      // 继续执行，不影响主流程
    }
    
    // 更新任务状态为"完成"
    await db.collection('upload_tasks').doc(uploadTask._id).update({
      data: {
        status: 'completed',
        tempUrl,
        updatedAt: db.serverDate()
      }
    });
    
    log('分片上传任务完成', { uploadId: data.uploadId, targetKey: uploadTask.targetKey });
    
    return {
      code: 0,
      message: '上传完成',
      data: {
        key: uploadTask.targetKey,
        fileUrl: tempUrl,
        fileName: uploadTask.fileName,
        fileExt: uploadTask.fileExt
      }
    };
  } catch (error) {
    log('完成分片上传失败', error);
    
    // 尝试更新任务状态为"失败"
    try {
      const taskResult = await db.collection('upload_tasks').where({
        uploadId: data.uploadId
      }).get();
      
      if (taskResult.data && taskResult.data.length > 0) {
        await db.collection('upload_tasks').doc(taskResult.data[0]._id).update({
          data: {
            status: 'failed',
            errorMessage: error.message,
            updatedAt: db.serverDate()
          }
        });
      }
    } catch (updateError) {
      log('更新任务状态失败', updateError);
    }
    
    return {
      code: 500,
      message: '完成上传失败: ' + error.message,
      data: null
    };
  }
}

/**
 * 压缩并上传视频
 * @param {Object} data 请求参数
 * @returns {Object} 响应结果
 */
async function compressAndUploadVideo(data) {
  try {
    if (!data?.fileContent || !data?.fileName || !data?.compressionLevel) {
      return {
        code: 400,
        message: '缺少必要参数',
        data: null
      };
    }
    
    const config = await getConfig();
    const { cos, videoConfig, storageConfig } = config;
    
    // 记录原始文件名，用于调试
    log('压缩并上传视频，原始文件名:', data.fileName);
    
    // 处理文件名，移除可能导致问题的特殊字符
    let safeFileName = data.fileName.replace(/[^\w\d.-]/g, '_');
    
    // 生成唯一文件名
    const timestamp = Date.now();
    const uniqueSuffix = timestamp.toString(36) + Math.random().toString(36).substr(2, 5);
    
    // 获取文件扩展名，如果没有或无法识别，默认使用mp4
    let fileExt = 'mp4';
    const extMatch = safeFileName.match(/\.([^.]+)$/);
    if (extMatch && extMatch[1]) {
      fileExt = extMatch[1].toLowerCase();
    }
    
    // 根据压缩级别设置参数
    let compressionOptions = {};
    switch (data.compressionLevel) {
      case 'high': // 高质量，低压缩
        compressionOptions = { bitrate: 3000, fps: 30, resolution: '720p' };
        break;
      case 'medium': // 中等质量和压缩
        compressionOptions = { bitrate: 1500, fps: 25, resolution: '480p' };
        break;
      case 'low': // 低质量，高压缩
        compressionOptions = { bitrate: 800, fps: 20, resolution: '360p' };
        break;
      default: // 默认使用中等压缩
        compressionOptions = { bitrate: 1500, fps: 25, resolution: '480p' };
    }
    
    // 构建存储路径
    const key = `${videoConfig.storage.paths.videos}${uniqueSuffix}_compressed_${safeFileName}`;
    
    log('压缩并上传视频，处理后的文件名:', key);
    log('压缩并上传视频，文件类型:', fileExt);
    log('压缩参数:', compressionOptions);
    
    // 模拟压缩过程（实际上前端应该使用wx.compressVideo API）
    // 在云函数中我们直接上传，前端负责压缩
    
    // 上传文件
    const uploadResult = await new Promise((resolve, reject) => {
      try {
        cos.putObject({
          Bucket: storageConfig.bucket,
          Region: storageConfig.region,
          Key: key,
          Body: Buffer.from(data.fileContent, 'base64'),
          ContentType: getContentType(fileExt)
        }, (err, result) => {
          if (err) {
            log('COS上传压缩视频失败:', err);
            reject(err);
          } else {
            resolve(result);
          }
        });
      } catch (err) {
        log('COS上传压缩视频异常:', err);
        reject(err);
      }
    });
    
    // 生成临时访问链接
    const tempUrl = await generateTempUrl(cos, config, key);
    
    log('压缩视频上传成功，生成临时链接:', tempUrl.substring(0, 100) + '...');
    
    return {
      code: 200,
      message: '上传压缩视频成功',
      data: {
        key,
        tempUrl,
        etag: uploadResult.ETag,
        fileType: fileExt,
        compressionLevel: data.compressionLevel,
        compressionOptions
      }
    };
  } catch (error) {
    log('上传压缩视频失败', error);
    return {
      code: 500,
      message: '上传压缩视频失败: ' + error.message,
      data: null
    };
  }
}

/**
 * 合并分片文件
 * @param {Object} data 请求参数
 * @returns {Object} 响应结果
 */
async function mergeChunks(data) {
  try {
    if (!data?.fileName || !data?.chunkFolderPath || !data?.totalChunks) {
      return {
        code: 400,
        message: '缺少必要参数',
        data: null
      };
    }
    
    const config = await getConfig();
    const { cos, videoConfig, storageConfig } = config;
    
    log('开始合并分片文件, 文件夹:', safeToString(data.chunkFolderPath), '总分片数:', data.totalChunks);
    
    // 处理文件名，移除可能导致问题的特殊字符
    let safeFileName = safeToString(data.fileName).replace(/[^\w\d.-]/g, '_');
    
    // 生成唯一文件名
    const timestamp = Date.now();
    const uniqueSuffix = timestamp.toString(36) + Math.random().toString(36).substr(2, 5);
    
    // 获取文件扩展名，如果没有或无法识别，默认使用mp4
    let fileExt = 'mp4';
    const extMatch = safeFileName.match(/\.([^.]+)$/);
    if (extMatch && extMatch[1]) {
      fileExt = extMatch[1].toLowerCase();
    }
    
    // 构建目标文件路径
    const targetKey = `${videoConfig.storage.paths.videos}${uniqueSuffix}_${safeFileName}`;
    
    // 为每个分片创建临时复制任务
    const chunkFileIds = [];
    
    // 使用直接复制方法，不经过下载
    try {
      // 列出所有分片的云存储路径
      log('准备处理分片文件...');
      
      // 获取上传文件的临时存储路径
      const tempObjectKey = `${targetKey}_temp`;
      
      // 使用多段上传初始化
      const initResult = await new Promise((resolve, reject) => {
        cos.multipartInit({
          Bucket: storageConfig.bucket,
          Region: storageConfig.region,
          Key: targetKey,
          ContentType: getContentType(fileExt)
        }, (err, data) => {
          if (err) {
            log('初始化多段上传失败:', err);
            reject(err);
            return;
          }
          resolve(data);
        });
      });
      
      if (!initResult || !initResult.UploadId) {
        throw new Error('初始化多段上传失败');
      }
      
      const uploadId = initResult.UploadId;
      log('初始化多段上传成功, uploadId:', uploadId);
      
      // 批量处理分片，每个批次最多处理5个分片
      const batchSize = 5;
      const totalBatches = Math.ceil(data.totalChunks / batchSize);
      const uploadParts = [];
      
      for (let batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
        const batchStart = batchIndex * batchSize;
        const batchEnd = Math.min(batchStart + batchSize, data.totalChunks);
        
        log(`处理分片批次 ${batchIndex+1}/${totalBatches} (分片 ${batchStart} - ${batchEnd-1})...`);
        
        // 为每个批次的分片创建上传任务
        const batchTasks = [];
        
        for (let i = batchStart; i < batchEnd; i++) {
          const chunkFileName = `chunk_${i.toString().padStart(5, '0')}`;
          const chunkPath = safeToString(data.chunkFolderPath) + chunkFileName;
          const fileID = `cloud://${cloud.DYNAMIC_CURRENT_ENV}.${chunkPath}`;
          
          batchTasks.push(
            (async (partNumber, fileId) => {
              try {
                log(`获取分片 ${partNumber} 的临时URL...`);
                
                // 获取分片的临时URL
                const getUrlResult = await cloud.getTempFileURL({
                  fileList: [fileId]
                });
                
                if (!getUrlResult.fileList || getUrlResult.fileList.length === 0) {
                  throw new Error(`获取分片${partNumber}的URL失败`);
                }
                
                const fileInfo = getUrlResult.fileList[0];
                if (fileInfo.status !== 0) {
                  throw new Error(fileInfo.errMsg || `获取分片${partNumber}的URL失败`);
                }
                
                const fileUrl = fileInfo.tempFileURL;
                
                // 下载分片
                log(`下载分片 ${partNumber}...`);
                const downloadResult = await cloud.downloadFile({
                  fileID: fileId
                });
                
                if (!downloadResult.fileContent) {
                  throw new Error(`下载分片${partNumber}失败`);
                }
                
                // 上传分片到目标位置
                log(`上传分片 ${partNumber} 到目标位置...`);
                const uploadResult = await new Promise((resolve, reject) => {
                  cos.multipartUpload({
                    Bucket: storageConfig.bucket,
                    Region: storageConfig.region,
                    Key: targetKey,
                    UploadId: uploadId,
                    PartNumber: partNumber + 1,
                    Body: downloadResult.fileContent
                  }, (err, data) => {
                    if (err) {
                      log(`上传分片 ${partNumber} 失败:`, err);
                      reject(err);
                      return;
                    }
                    resolve(data);
                  });
                });
                
                log(`分片 ${partNumber} 处理成功`);
                
                // 返回分片信息
                return {
                  PartNumber: partNumber + 1,
                  ETag: uploadResult.ETag
                };
              } catch (error) {
                log(`处理分片 ${partNumber} 失败:`, error);
                throw error;
              }
            })(i, fileID)
          );
        }
        
        // 并行处理批次中的分片
        const batchResults = await Promise.all(batchTasks);
        uploadParts.push(...batchResults);
        
        log(`批次 ${batchIndex+1}/${totalBatches} 处理完成`);
      }
      
      // 按分片号排序
      uploadParts.sort((a, b) => a.PartNumber - b.PartNumber);
      
      // 完成多段上传
      log('完成多段上传...');
      await new Promise((resolve, reject) => {
        cos.multipartComplete({
          Bucket: storageConfig.bucket,
          Region: storageConfig.region,
          Key: targetKey,
          UploadId: uploadId,
          Parts: uploadParts
        }, (err, data) => {
          if (err) {
            log('完成多段上传失败:', err);
            reject(err);
            return;
          }
          resolve(data);
        });
      });
      
      log('多段上传完成，合并成功');
      
    } catch (mergeError) {
      log('使用多段上传合并失败，尝试传统方法:', mergeError);
      
      // 回退到传统方法: 逐个下载分片，并按顺序合并
      // 但使用批处理方式减少内存占用
      const batchSize = 3; // 每批处理3个分片
      const totalBatches = Math.ceil(data.totalChunks / batchSize);
      
      // 创建临时合并文件
      let tempFileKey = `temp_files/${Date.now()}_${uniqueSuffix}_merged.${fileExt}`;
      let mergedFileSize = 0;
      
      // 初始化一个空文件
      await new Promise((resolve, reject) => {
        cos.putObject({
          Bucket: storageConfig.bucket,
          Region: storageConfig.region,
          Key: tempFileKey,
          Body: Buffer.alloc(0),
          ContentType: getContentType(fileExt)
        }, (err, result) => {
          if (err) {
            log('初始化临时文件失败:', err);
            reject(err);
          } else {
            resolve(result);
          }
        });
      });
      
      // 按批次处理分片
      for (let batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
        const batchStart = batchIndex * batchSize;
        const batchEnd = Math.min(batchStart + batchSize, data.totalChunks);
        
        log(`处理分片批次 ${batchIndex+1}/${totalBatches} (分片 ${batchStart} - ${batchEnd-1})...`);
        
        // 为这个批次创建一个合并缓冲区
        let batchBuffer = Buffer.alloc(0);
        
        // 逐个处理这个批次的分片
        for (let i = batchStart; i < batchEnd; i++) {
          const chunkFileName = `chunk_${i.toString().padStart(5, '0')}`;
          const chunkPath = safeToString(data.chunkFolderPath) + chunkFileName;
          const fileID = `cloud://${cloud.DYNAMIC_CURRENT_ENV}.${chunkPath}`;
          
          try {
            log(`下载分片 ${i+1}/${data.totalChunks}...`);
            
            const downloadResult = await cloud.downloadFile({
              fileID: fileID
            });
            
            if (!downloadResult.fileContent) {
              throw new Error(`下载分片${i+1}失败`);
            }
            
            // 将分片添加到批次缓冲区
            batchBuffer = Buffer.concat([batchBuffer, downloadResult.fileContent]);
            
            log(`分片 ${i+1} 下载并合并到批次缓冲区`);
          } catch (err) {
            log(`处理分片 ${i+1} 失败:`, err);
            throw new Error(`合并分片失败: 处理分片 ${i+1} 时出错`);
          }
        }
        
        // 将批次缓冲区追加到临时文件
        await new Promise((resolve, reject) => {
          cos.appendObject({
            Bucket: storageConfig.bucket,
            Region: storageConfig.region,
            Key: tempFileKey,
            Position: mergedFileSize,
            Body: batchBuffer,
            ContentType: getContentType(fileExt)
          }, (err, result) => {
            if (err) {
              log(`追加批次 ${batchIndex+1} 到临时文件失败:`, err);
              reject(err);
            } else {
              mergedFileSize += batchBuffer.length;
              log(`批次 ${batchIndex+1} 追加成功，当前文件大小: ${mergedFileSize} 字节`);
              resolve(result);
            }
          });
        });
        
        // 释放批次缓冲区内存
        batchBuffer = null;
      }
      
      // 将临时合并文件复制到最终目标位置
      await new Promise((resolve, reject) => {
        cos.putObjectCopy({
          Bucket: storageConfig.bucket,
          Region: storageConfig.region,
          Key: targetKey,
          CopySource: `${storageConfig.bucket}.${storageConfig.region}.myqcloud.com/${tempFileKey}`,
          ContentType: getContentType(fileExt)
        }, (err, result) => {
          if (err) {
            log('复制临时文件到最终位置失败:', err);
            reject(err);
          } else {
            log('临时文件复制到最终位置成功');
            resolve(result);
          }
        });
      });
      
      // 清理临时合并文件
      try {
        await new Promise((resolve) => {
          cos.deleteObject({
            Bucket: storageConfig.bucket,
            Region: storageConfig.region,
            Key: tempFileKey
          }, () => resolve());
        });
        log('临时合并文件清理成功');
      } catch (cleanupErr) {
        log('清理临时合并文件失败，但不影响主流程:', cleanupErr);
      }
    }
    
    // 生成临时访问链接
    const tempUrl = await generateTempUrl(cos, config, targetKey);
    
    log('合并文件上传成功，生成临时链接:', tempUrl.substring(0, 100) + '...');
    
    // 清理临时分片文件
    try {
      log('开始清理临时分片文件...');
      
      // 创建要删除的文件ID列表
      const deleteFileIds = [];
      for (let i = 0; i < data.totalChunks; i++) {
        const chunkFileName = `chunk_${i.toString().padStart(5, '0')}`;
        const chunkPath = safeToString(data.chunkFolderPath) + chunkFileName;
        const fileID = `cloud://${cloud.DYNAMIC_CURRENT_ENV}.${chunkPath}`;
        deleteFileIds.push(fileID);
      }
      
      // 分批删除文件，每批最多50个
      const batchSize = 50;
      const totalBatches = Math.ceil(deleteFileIds.length / batchSize);
      
      for (let batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
        const batchStart = batchIndex * batchSize;
        const batchEnd = Math.min(batchStart + batchSize, deleteFileIds.length);
        const batchFileIds = deleteFileIds.slice(batchStart, batchEnd);
        
        try {
          await cloud.deleteFile({
            fileList: batchFileIds
          });
          log(`已清理分片文件批次 ${batchIndex+1}/${totalBatches} (${batchFileIds.length}个文件)`);
        } catch (deleteErr) {
          log(`清理分片文件批次 ${batchIndex+1} 失败，但不影响主流程:`, deleteErr);
        }
      }
      
      log(`清理临时分片文件完成`);
    } catch (deleteErr) {
      log('清理临时分片文件失败，但不影响主流程:', deleteErr);
    }
    
    return {
      code: 200,
      message: '合并分片成功',
      data: {
        key: targetKey,
        tempUrl,
        fileType: fileExt
      }
    };
  } catch (error) {
    log('合并分片文件失败', error);
    return {
      code: 500,
      message: '合并分片文件失败: ' + error.message,
      data: null
    };
  }
}

// 添加一个安全的字符串转换函数，用于处理可能的Symbol值
function safeToString(value) {
  if (value === undefined || value === null) {
    return '';
  }
  
  // 检查是否为Symbol类型
  if (typeof value === 'symbol') {
    try {
      // 尝试使用Symbol的description属性
      return value.description || 'Symbol';
    } catch (e) {
      return 'Symbol';
    }
  }
  
  // 对于对象，使用JSON.stringify并捕获可能的错误
  if (typeof value === 'object') {
    try {
      return JSON.stringify(value);
    } catch (e) {
      return '[Object]';
    }
  }
  
  // 其他类型直接转换为字符串
  return String(value);
}

/**
 * 替代的分片合并方法，使用更简单的策略
 * @param {Object} data 合并参数
 * @returns {Object} 合并结果
 */
async function alternativeMerge(data) {
  try {
    if (!data?.fileName || !data?.chunkFolderPath || !data?.totalChunks) {
      return {
        code: 400,
        message: '缺少必要参数',
        data: null
      };
    }
    
    const config = await getConfig();
    const { cos, videoConfig, storageConfig } = config;
    
    // 单分片特殊处理 - 添加单独的处理流程避免合并步骤
    if (data.totalChunks === 1) {
      log('检测到单分片上传，使用简化处理流程');
      
      try {
        // 安全处理文件名和路径
        let safeFileName = safeToString(data.fileName).replace(/[^\w\d.-]/g, '_');
        const chunkFileName = `chunk_${(0).toString().padStart(5, '0')}`;
        const chunkPath = safeToString(data.chunkFolderPath) + chunkFileName;
        const fileID = `cloud://${cloud.DYNAMIC_CURRENT_ENV}.${chunkPath}`;
        
        log('单分片处理 - 文件ID:', fileID);
        
        // 生成唯一文件名
        const timestamp = Date.now();
        const uniqueSuffix = timestamp.toString(36) + Math.random().toString(36).substr(2, 5);
        
        // 获取文件扩展名
        let fileExt = 'mp4';
        const extMatch = safeFileName.match(/\.([^.]+)$/);
        if (extMatch && extMatch[1]) {
          fileExt = extMatch[1].toLowerCase();
        }
        
        // 构建目标路径
        const targetKey = `${videoConfig.storage.paths.videos}${uniqueSuffix}_${safeFileName}`;
        log('单分片处理 - 目标路径:', targetKey);
        
        // 检查文件是否存在
        try {
          const fileExistsResult = await cloud.getTempFileURL({
            fileList: [fileID]
          });
          
          if (!fileExistsResult.fileList || fileExistsResult.fileList.length === 0 || 
              fileExistsResult.fileList[0].status !== 0) {
            log('单分片处理 - 文件不存在:', fileExistsResult);
            throw new Error('分片文件不存在');
          }
          
          log('单分片处理 - 文件存在，开始下载');
        } catch (checkErr) {
          log('单分片处理 - 检查文件存在性失败:', checkErr);
          throw new Error('检查分片文件失败: ' + checkErr.message);
        }
        
        // 下载分片
        log('下载单个分片...');
        const downloadResult = await cloud.downloadFile({
          fileID: fileID
        });
        
        if (!downloadResult.fileContent) {
          log('单分片处理 - 下载失败，无内容');
          throw new Error('下载分片失败，无内容返回');
        }
        
        log('单分片处理 - 下载成功，内容大小:', downloadResult.fileContent.length);
        
        // 直接上传到目标位置
        log('上传到最终位置...');
        await new Promise((resolve, reject) => {
          cos.putObject({
            Bucket: storageConfig.bucket,
            Region: storageConfig.region,
            Key: targetKey,
            Body: downloadResult.fileContent,
            ContentType: getContentType(fileExt)
          }, (err, result) => {
            if (err) {
              log('单分片处理 - 上传失败:', err);
              reject(err);
            } else {
              log('单分片处理 - 上传成功:', result);
              resolve(result);
            }
          });
        });
        
        // 生成临时访问链接
        const tempUrl = await generateTempUrl(cos, config, targetKey);
        log('单分片处理 - 生成临时链接成功');
        
        // 清理临时分片
        try {
          await cloud.deleteFile({
            fileList: [fileID]
          });
          log('清理临时分片文件完成');
        } catch (err) {
          log('清理临时分片失败，但不影响主流程:', err);
        }
        
        return {
          code: 200,
          message: '单分片处理成功',
          data: {
            key: targetKey,
            tempUrl,
            fileType: fileExt
          }
        };
      } catch (error) {
        log('单分片处理失败', error);
        return {
          code: 500,
          message: '单分片处理失败: ' + error.message,
          data: null
        };
      }
    }
    
    // 原有的多分片处理逻辑
    // 使用安全的字符串转换
    log('开始替代合并方法, 文件夹:', safeToString(data.chunkFolderPath), '总分片数:', data.totalChunks);
    
    // 处理文件名，移除可能导致问题的特殊字符
    let safeFileName = safeToString(data.fileName).replace(/[^\w\d.-]/g, '_');
    
    // 生成唯一文件名
    const timestamp = Date.now();
    const uniqueSuffix = timestamp.toString(36) + Math.random().toString(36).substr(2, 5);
    
    // 获取文件扩展名，如果没有或无法识别，默认使用mp4
    let fileExt = 'mp4';
    const extMatch = safeFileName.match(/\.([^.]+)$/);
    if (extMatch && extMatch[1]) {
      fileExt = extMatch[1].toLowerCase();
    }
    
    // 构建目标文件路径
    const targetKey = `${videoConfig.storage.paths.videos}${uniqueSuffix}_${safeFileName}`;
    
    // 使用直接上传方法，先将所有分片下载到本地内存，然后一次性上传
    // 注意：这种方法适用于中等大小的视频，对于非常大的视频可能会导致内存不足
    
    // 创建一个临时目录，用于存储合并后的文件
    const tempDir = `/tmp/${uniqueSuffix}`;
    const fs = require('fs');
    const path = require('path');
    
    // 确保临时目录存在
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }
    
    const tempFilePath = path.join(tempDir, safeFileName);
    
    // 使用流式写入而不是文件流，以减少内存使用
    let fileDescriptor = null;
    try {
      fileDescriptor = fs.openSync(tempFilePath, 'w');
    } catch (err) {
      log('打开临时文件失败:', err);
      throw new Error('创建临时文件失败: ' + err.message);
    }
    
    // 按顺序处理每个分片
    log('开始按顺序处理分片...');
    
    // 使用较小的批次大小，减少内存压力
    const batchSize = 1; // 每次只处理1个分片，进一步减小内存压力
    const totalBatches = Math.ceil(data.totalChunks / batchSize);
    
    try {
      // 使用更高效的处理方式
      for (let batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
        const batchStart = batchIndex * batchSize;
        const batchEnd = Math.min(batchStart + batchSize, data.totalChunks);
        
        log(`处理分片批次 ${batchIndex+1}/${totalBatches} (分片 ${batchStart} - ${batchEnd-1})...`);
        
        // 逐个处理这个批次的分片
        for (let i = batchStart; i < batchEnd; i++) {
          // 使用安全的字符串格式化
          const chunkFileName = `chunk_${i.toString().padStart(5, '0')}`;
          const chunkPath = safeToString(data.chunkFolderPath) + chunkFileName;
          const fileID = `cloud://${cloud.DYNAMIC_CURRENT_ENV}.${chunkPath}`;
          
          try {
            log(`下载分片 ${i+1}/${data.totalChunks}...`);
            
            const downloadResult = await cloud.downloadFile({
              fileID: fileID
            });
            
            if (!downloadResult.fileContent) {
              throw new Error(`下载分片${i+1}失败`);
            }
            
            // 使用同步写入文件描述符
            fs.writeSync(fileDescriptor, downloadResult.fileContent, 0, downloadResult.fileContent.length);
            
            log(`分片 ${i+1} 已写入临时文件`);
            
            // 手动触发垃圾回收，减轻内存压力
            if (global.gc) {
              global.gc();
            }
          } catch (err) {
            log(`处理分片 ${i+1} 失败:`, err);
            throw new Error(`合并分片失败: 处理分片 ${i+1} 时出错`);
          }
        }
        
        // 每批次处理完后暂停一下，让系统有时间释放资源
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    } finally {
      // 确保文件描述符被关闭
      if (fileDescriptor !== null) {
        fs.closeSync(fileDescriptor);
      }
    }
    
    log('所有分片已合并到临时文件，准备上传到云存储...');
    
    // 读取合并后的文件（使用流式读取）
    const fileStream = fs.createReadStream(tempFilePath);
    
    // 上传合并后的文件到云存储
    await new Promise((resolve, reject) => {
      cos.putObject({
        Bucket: storageConfig.bucket,
        Region: storageConfig.region,
        Key: targetKey,
        Body: fileStream,
        ContentType: getContentType(fileExt)
      }, (err, result) => {
        if (err) {
          log('上传合并文件失败:', err);
          reject(err);
        } else {
          log('上传合并文件成功');
          resolve(result);
        }
      });
    });
    
    // 清理临时文件
    try {
      fs.unlinkSync(tempFilePath);
      log('临时文件清理成功');
    } catch (cleanupErr) {
      log('清理临时文件失败，但不影响主流程:', cleanupErr);
    }
    
    // 生成临时访问链接
    const tempUrl = await generateTempUrl(cos, config, targetKey);
    
    log('合并文件上传成功，生成临时链接:', safeToString(tempUrl).substring(0, 100) + '...');
    
    // 清理临时分片文件
    try {
      log('开始清理临时分片文件...');
      
      // 创建要删除的文件ID列表
      const deleteFileIds = [];
      for (let i = 0; i < data.totalChunks; i++) {
        const chunkFileName = `chunk_${i.toString().padStart(5, '0')}`;
        const chunkPath = safeToString(data.chunkFolderPath) + chunkFileName;
        const fileID = `cloud://${cloud.DYNAMIC_CURRENT_ENV}.${chunkPath}`;
        deleteFileIds.push(fileID);
      }
      
      // 分批删除文件，每批最多50个
      const batchSize = 50;
      const totalBatches = Math.ceil(deleteFileIds.length / batchSize);
      
      for (let batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
        const batchStart = batchIndex * batchSize;
        const batchEnd = Math.min(batchStart + batchSize, deleteFileIds.length);
        const batchFileIds = deleteFileIds.slice(batchStart, batchEnd);
        
        try {
          await cloud.deleteFile({
            fileList: batchFileIds
          });
          log(`已清理分片文件批次 ${batchIndex+1}/${totalBatches} (${batchFileIds.length}个文件)`);
        } catch (deleteErr) {
          log(`清理分片文件批次 ${batchIndex+1} 失败，但不影响主流程:`, deleteErr);
        }
      }
      
      log(`清理临时分片文件完成`);
    } catch (deleteErr) {
      log('清理临时分片文件失败，但不影响主流程:', deleteErr);
    }
    
    return {
      code: 200,
      message: '替代合并方法成功',
      data: {
        key: targetKey,
        tempUrl,
        fileType: fileExt
      }
    };
  } catch (error) {
    log('替代合并方法失败', error);
    return {
      code: 500,
      message: '替代合并方法失败: ' + error.message,
      data: null
    };
  }
}

/**
 * 根据文件扩展名获取内容类型
 * @param {string} ext 文件扩展名
 * @returns {string} 内容类型
 */
function getContentType(ext) {
  const contentTypes = {
    // 视频格式
    'mp4': 'video/mp4',
    'mov': 'video/quicktime',
    'avi': 'video/x-msvideo',
    'wmv': 'video/x-ms-wmv',
    'flv': 'video/x-flv',
    'mkv': 'video/x-matroska',
    'webm': 'video/webm',
    'm4v': 'video/x-m4v',
    '3gp': 'video/3gpp',
    
    // 图片格式 - 扩展支持
    'jpg': 'image/jpeg',
    'jpeg': 'image/jpeg',
    'jpe': 'image/jpeg',
    'jfif': 'image/jpeg',
    'png': 'image/png',
    'gif': 'image/gif',
    'webp': 'image/webp',
    'bmp': 'image/bmp',
    'ico': 'image/x-icon',
    'svg': 'image/svg+xml',
    'tif': 'image/tiff',
    'tiff': 'image/tiff'
  };
  
  return contentTypes[ext.toLowerCase()] || 'application/octet-stream';
}

/**
 * 直接上传视频到云开发存储（简化版，不使用分片）
 * @param {Object} data 请求参数
 * @returns {Object} 响应结果
 */
async function directUploadVideo(data) {
  try {
    // 安全地验证参数
    const fileID = safeToString(data?.fileID);
    const fileName = safeToString(data?.fileName);
    
    if (!fileID || !fileName) {
      return {
        code: 400,
        message: '缺少必要参数',
        data: null
      };
    }
    
    // 记录原始文件名，用于调试
    log('直接上传视频，原始文件名:', fileName);
    
    // 处理文件名，移除可能导致问题的特殊字符
    let safeFileName = fileName.replace(/[^\w\d.-]/g, '_');
    
    // 生成唯一文件名
    const timestamp = Date.now();
    const uniqueSuffix = timestamp.toString(36) + Math.random().toString(36).substr(2, 8);
    
    // 获取文件扩展名，如果没有或无法识别，默认使用mp4
    let fileExt = 'mp4';
    const extMatch = safeFileName.match(/\.([^.]+)$/);
    if (extMatch && extMatch[1]) {
      fileExt = extMatch[1].toLowerCase();
    } else {
      // 如果没有扩展名，添加默认扩展名
      safeFileName = `${safeFileName}.mp4`;
    }
    
    // 构建存储路径 - 确保所有部分都是字符串
    const videoFolder = safeToString(VIDEO_FOLDER);
    const cloudPath = `${videoFolder}/${uniqueSuffix}_${safeFileName}`;
    
    log('直接上传视频，处理后的文件名:', cloudPath);
    
    try {
      // 从临时文件复制到永久存储
      const destFileID = `cloud://${safeToString(envId)}/${cloudPath}`;
      log('目标文件路径:', destFileID);
      
      const result = await cloud.copyFile({
        fileID: fileID,
        destFileID: destFileID
      });
      
      if (!result || !result.fileID) {
        log('复制文件到永久存储失败:', result);
        return {
          code: 500,
          message: '复制文件到永久存储失败',
          data: null
        };
      }
      
      const resultFileID = safeToString(result.fileID);
      log('视频上传成功，fileID:', resultFileID);
      
      // 生成临时访问链接
      const tempUrl = await generateTempUrl(resultFileID);
      
      // 确保返回前端期望的数据结构
      return {
        code: 200,
        message: '直接上传视频成功',
        data: {
          fileID: resultFileID,
          key: resultFileID, // 保持与前端兼容
          tempUrl: safeToString(tempUrl),
          fileType: fileExt
        }
      };
    } catch (cloudError) {
      log('处理云存储视频失败:', cloudError);
      throw new Error('处理云存储视频失败: ' + safeToString(cloudError.message || '未知错误'));
    }
  } catch (error) {
    log('直接上传视频失败', error);
    return {
      code: 500,
      message: '直接上传视频失败: ' + safeToString(error.message || '未知错误'),
      data: null
    };
  }
}

/**
 * 通过云存储上传封面图片（更安全的实现 - 不使用copyFile）
 * @param {Object} data 上传数据
 * @returns {Object} 响应结果
 */
async function uploadCoverViaCloudStorage2(data) {
  try {
    // 避免Symbol错误的参数处理
    if (!data) {
      return {
        code: 400,
        message: '参数为空',
        data: null
      };
    }
    
    // 提取并转换参数
    let fileID = '';
    if (data.fileID) {
      if (typeof data.fileID === 'symbol') {
        fileID = data.fileID.toString();
      } else {
        fileID = String(data.fileID);
      }
    }
    
    let fileName = '';
    if (data.fileName) {
      if (typeof data.fileName === 'symbol') {
        fileName = data.fileName.toString();
      } else {
        fileName = String(data.fileName);
      }
    }
    
    if (!fileID || !fileName) {
      return {
        code: 400,
        message: '缺少必要参数：fileID或fileName',
        data: null
      };
    }
    
    // 记录参数
    log('封面上传 - 接收到参数', {
      fileID: fileID,
      fileName: fileName
    });
    
    // 处理文件名，移除可能导致问题的特殊字符
    let safeFileName = fileName.replace(/[^\w\d.-]/g, '_');
    
    // 生成唯一文件名
    const timestamp = Date.now();
    const uniqueSuffix = timestamp.toString(36) + Math.random().toString(36).substr(2, 5);
    
    // 获取文件扩展名，如果没有或无法识别，默认使用jpg
    let fileExt = 'jpg';
    const extMatch = safeFileName.match(/\.([^.]+)$/);
    if (extMatch && extMatch[1]) {
      fileExt = extMatch[1].toLowerCase();
    }
    
    // 构建存储路径
    let coverFolder = '';
    if (typeof COVER_FOLDER === 'symbol') {
      coverFolder = COVER_FOLDER.toString();
    } else {
      coverFolder = String(COVER_FOLDER || 'covers');
    }
    
    const cloudPath = `${coverFolder}/${uniqueSuffix}_${safeFileName}`;
    log('封面上传 - 目标路径', cloudPath);
    
    // 替代copyFile方法的实现 - 使用下载后再上传的方式
    try {
      // 1. 先下载临时文件
      log('封面上传 - 开始下载临时文件', fileID);
      const downloadResult = await cloud.downloadFile({
        fileID: fileID
      });
      
      if (!downloadResult || !downloadResult.fileContent) {
        log('封面上传 - 下载临时文件失败', downloadResult || '无返回结果');
        return {
          code: 500,
          message: '下载临时文件失败',
          data: null
        };
      }
      
      // 2. 将下载的文件重新上传到永久路径
      log('封面上传 - 开始上传到永久存储', cloudPath);
      const uploadResult = await cloud.uploadFile({
        cloudPath: cloudPath,
        fileContent: downloadResult.fileContent
      });
      
      if (!uploadResult || !uploadResult.fileID) {
        log('封面上传 - 上传到永久存储失败', uploadResult || '无返回结果');
        return {
          code: 500,
          message: '上传到永久存储失败',
          data: null
        };
      }
      
      // 安全处理返回的fileID
      let resultFileID = '';
      if (uploadResult.fileID) {
        if (typeof uploadResult.fileID === 'symbol') {
          resultFileID = uploadResult.fileID.toString();
        } else {
          resultFileID = String(uploadResult.fileID);
        }
      }
      
      log('封面上传 - 上传成功', resultFileID);
      
      // 生成临时访问链接
      let tempUrl = '';
      try {
        tempUrl = await generateTempUrl(resultFileID);
        log('封面上传 - 生成临时链接成功', tempUrl);
      } catch (urlErr) {
        log('封面上传 - 生成临时链接失败', urlErr);
        // 继续执行，允许没有临时链接
      }
      
      // 返回结果
      return {
        code: 200,
        message: '上传封面图成功',
        data: {
          fileID: resultFileID,
          key: resultFileID, // 与前端兼容
          tempUrl: tempUrl || '',
          fileType: fileExt
        }
      };
    } catch (processErr) {
      log('封面上传 - 处理过程出错', processErr);
      
      let errorMsg = '处理云存储封面图失败';
      if (processErr && processErr.message) {
        errorMsg += ': ' + String(processErr.message);
      }
      
      throw new Error(errorMsg);
    }
  } catch (error) {
    log('封面上传 - 整体流程出错', error);
    
    // 提供更详细的错误信息
    let errorMessage = '上传封面图失败';
    if (error && error.message) {
      const errStr = String(error.message);
      if (errStr.includes('exceed')) {
        errorMessage = '图片文件过大，请压缩后再上传';
      } else if (errStr.includes('format')) {
        errorMessage = '图片格式不支持，请转换为JPG、PNG或WEBP格式';
      } else {
        errorMessage = '上传封面图失败: ' + errStr;
      }
    }
    
    return {
      code: 500,
      message: errorMessage,
      data: null
    };
  }
}

/**
 * 切换视频可见性（支持直接设置可见性）
 * @param {Object} data 请求参数
 * @returns {Object} 响应结果
 */
async function toggleVideoVisibility(data) {
  try {
    if (!data?.videoId) {
      return {
        code: 400,
        message: '缺少视频ID',
        data: null
      };
    }
    
    const config = await getConfig();
    const { videoConfig } = config;
    
    // 获取视频信息
    const video = await db.collection(videoConfig.db.collection)
      .doc(data.videoId)
      .get();
    
    if (!video.data) {
      return {
        code: 404,
        message: '视频不存在',
        data: null
      };
    }
    
    // 确定新的可见性
    // 如果直接提供了visible参数，使用该参数值
    // 否则切换当前视频的可见性状态
    const newVisibility = data.visible !== undefined ? !!data.visible : !video.data.isVisible;
    
    log('更新视频可见性', {
      videoId: data.videoId,
      currentVisibility: video.data.isVisible,
      newVisibility: newVisibility
    });
    
    // 准备更新数据
    const updateData = {
      isVisible: newVisibility,
      updateTime: db.serverDate()
    };
    
    // 当视频从隐藏变为可见时，同时更新其创建时间
    // 这将使其被前端识别为"新视频"，并优先显示在列表前面
    if (newVisibility && !video.data.isVisible) {
      // 仅当视频是从隐藏变为可见时，将其视为"新上传"的视频
      log('视频从隐藏变为可见，更新时间戳使其显示为新视频');
      updateData.displayTime = db.serverDate(); // 添加显示时间字段，用于前端排序
    }
    
    await db.collection(videoConfig.db.collection)
      .doc(data.videoId)
      .update({
        data: updateData
      });
    
    return {
      code: 200,
      message: `视频已${newVisibility ? '显示' : '隐藏'}`,
      data: { 
        isVisible: newVisibility,
        updateTime: new Date()  // 返回更新时间
      }
    };
  } catch (error) {
    log('切换视频可见性失败', error);
    return {
      code: 500,
      message: '切换视频可见性失败: ' + error.message,
      data: null
    };
  }
}

/**
 * 迁移视频数据，确保所有视频记录都有排序字段
 * @returns {Object} 响应结果
 */
async function migrateVideoData() {
  try {
    const config = await getConfig();
    const { videoConfig } = config;
    
    log('开始迁移视频数据，确保排序字段存在...');
    
    // 获取所有视频记录
    const result = await db.collection(videoConfig.db.collection)
      .get();
    
    const videos = result.data || [];
    log(`找到 ${videos.length} 条视频记录`);
    
    // 检查哪些记录缺少排序字段
    const videosToUpdate = videos.filter(video => 
      typeof video.sortOrder !== 'number' && 
      (typeof video.order !== 'number' || video.order === undefined)
    );
    
    log(`有 ${videosToUpdate.length} 条记录需要添加排序字段`);
    
    if (videosToUpdate.length === 0) {
      return {
        code: 200,
        message: '所有视频记录已有排序字段，无需迁移',
        data: {
          totalVideos: videos.length,
          updatedVideos: 0
        }
      };
    }
    
    // 为每条记录添加排序字段
    // 默认使用创建时间的时间戳作为排序值，确保旧记录排在前面
    let updatedCount = 0;
    const sortField = videoConfig.db.sortField;
    
    for (let i = 0; i < videosToUpdate.length; i++) {
      const video = videosToUpdate[i];
      const createTimeValue = video.createTime instanceof Date 
        ? video.createTime.getTime() 
        : (video.createTime?.$date ? new Date(video.createTime.$date).getTime() : Date.now());
      
      // 默认排序值：使用创建时间的时间戳作为基础，并添加一个递增的偏移量
      // 较早创建的视频将排在前面（有较小的排序值）
      const sortValue = Math.floor(createTimeValue / 1000) + i;
      
      try {
        await db.collection(videoConfig.db.collection)
          .doc(video._id)
          .update({
            data: {
              [sortField]: sortValue,
              updateTime: db.serverDate()
            }
          });
        
        updatedCount++;
        
        // 每更新10条记录记录一次日志
        if (updatedCount % 10 === 0 || updatedCount === videosToUpdate.length) {
          log(`已更新 ${updatedCount}/${videosToUpdate.length} 条记录`);
        }
      } catch (updateError) {
        log(`更新视频 ${video._id} 失败:`, updateError);
        // 继续处理下一条记录
      }
    }
    
    return {
      code: 200,
      message: `成功为 ${updatedCount} 条视频记录添加排序字段`,
      data: {
        totalVideos: videos.length,
        updatedVideos: updatedCount
      }
    };
  } catch (error) {
    log('迁移视频数据失败', error);
    return {
      code: 500,
      message: '迁移视频数据失败: ' + error.message,
      data: null
    };
  }
}

/**
 * 将COS中的数据迁移到云开发存储
 * @returns {Object} 响应结果
 */
async function migrateToCloudStorage() {
  try {
    log('开始迁移视频数据到云开发存储...');
    
    // 获取所有视频记录
    const result = await db.collection(COLLECTION_NAME).get();
    const videos = result.data || [];
    
    log(`找到 ${videos.length} 条视频记录需要迁移`);
    
    if (videos.length === 0) {
      return {
        code: 200,
        message: '没有视频记录需要迁移',
        data: {
          totalVideos: 0,
          migratedVideos: 0
        }
      };
    }
    
    // 记录迁移结果
    const migrationResults = {
      total: videos.length,
      success: 0,
      failed: 0,
      skipped: 0,
      errors: []
    };
    
    // 遍历所有视频记录
    for (let i = 0; i < videos.length; i++) {
      const video = videos[i];
      log(`处理第 ${i + 1}/${videos.length} 条记录，ID: ${video._id}`);
      
      // 检查是否已经迁移过
      if (video.videoFileID && video.coverFileID) {
        log(`视频 ${video._id} 已经迁移过，跳过`);
        migrationResults.skipped++;
        continue;
      }
      
      try {
        // 构建更新数据
        const updateData = {};
        let needUpdate = false;
        
        // 迁移视频文件
        if (video.videoKey && !video.videoFileID) {
          try {
            // 从外部URL下载视频文件
            // 注意：这里假设视频文件可以通过公开URL访问，实际情况可能需要调整
            const videoUrl = `https://your-cos-domain.com/${video.videoKey}`;
            
            // 由于云函数无法直接从外部URL下载文件，这里需要使用其他方式
            // 建议在前端实现下载和上传，或者使用专门的迁移工具
            
            // 这里我们只记录需要迁移的文件，实际迁移需要单独处理
            log(`视频 ${video._id} 的视频文件需要手动迁移: ${videoUrl}`);
            
            // 记录原始videoKey，方便后续迁移
            updateData.originalVideoKey = video.videoKey;
            needUpdate = true;
          } catch (videoErr) {
            log(`迁移视频 ${video._id} 的视频文件失败:`, videoErr);
            migrationResults.errors.push({
              id: video._id,
              type: 'video',
              error: videoErr.message
            });
          }
        }
        
        // 迁移封面图片
        if (video.coverKey && !video.coverFileID) {
          try {
            // 记录原始coverKey，方便后续迁移
            updateData.originalCoverKey = video.coverKey;
            needUpdate = true;
            
            log(`视频 ${video._id} 的封面图片需要手动迁移: ${video.coverKey}`);
          } catch (coverErr) {
            log(`迁移视频 ${video._id} 的封面图片失败:`, coverErr);
            migrationResults.errors.push({
              id: video._id,
              type: 'cover',
              error: coverErr.message
            });
          }
        }
        
        // 迁移详情图片
        if (video.detailKey && !video.detailFileID) {
          try {
            // 记录原始detailKey，方便后续迁移
            updateData.originalDetailKey = video.detailKey;
            needUpdate = true;
            
            log(`视频 ${video._id} 的详情图片需要手动迁移: ${video.detailKey}`);
          } catch (detailErr) {
            log(`迁移视频 ${video._id} 的详情图片失败:`, detailErr);
            migrationResults.errors.push({
              id: video._id,
              type: 'detail',
              error: detailErr.message
            });
          }
        }
        
        // 更新视频记录
        if (needUpdate) {
          await db.collection(videoConfig.db.collection)
            .doc(video._id)
            .update({
              data: updateData
            });
        }
        
        migrationResults.success++;
      } catch (migrateError) {
        log(`迁移视频 ${video._id} 失败:`, migrateError);
        migrationResults.failed++;
        migrationResults.errors.push({
          id: video._id,
          type: 'migrate',
          error: migrateError.message
        });
      }
    }
    
    return {
      code: 200,
      message: `成功迁移 ${migrationResults.success} 条视频记录，失败 ${migrationResults.failed} 条，跳过 ${migrationResults.skipped} 条`,
      data: {
        totalVideos: videos.length,
        migratedVideos: migrationResults.success,
        failedVideos: migrationResults.failed,
        skippedVideos: migrationResults.skipped,
        errors: migrationResults.errors
      }
    };
  } catch (error) {
    log('迁移视频数据失败', error);
    return {
      code: 500,
      message: '迁移视频数据失败: ' + error.message,
      data: null
    };
  }
}

// 添加一个更安全的详情图上传函数
/**
 * 通过云存储上传详情图片（更安全的实现 - 不使用copyFile）
 * @param {Object} data 上传数据
 * @returns {Object} 响应结果
 */
async function uploadDetailViaCloudStorage2(data) {
  try {
    // 避免Symbol错误的参数处理
    if (!data) {
      return {
        code: 400,
        message: '参数为空',
        data: null
      };
    }
    
    // 提取并转换参数
    let fileID = '';
    if (data.fileID) {
      if (typeof data.fileID === 'symbol') {
        fileID = data.fileID.toString();
      } else {
        fileID = String(data.fileID);
      }
    }
    
    let fileName = '';
    if (data.fileName) {
      if (typeof data.fileName === 'symbol') {
        fileName = data.fileName.toString();
      } else {
        fileName = String(data.fileName);
      }
    }
    
    if (!fileID || !fileName) {
      return {
        code: 400,
        message: '缺少必要参数：fileID或fileName',
        data: null
      };
    }
    
    // 记录参数
    log('详情图上传 - 接收到参数', {
      fileID: fileID,
      fileName: fileName
    });
    
    // 处理文件名，移除可能导致问题的特殊字符
    let safeFileName = fileName.replace(/[^\w\d.-]/g, '_');
    
    // 生成唯一文件名
    const timestamp = Date.now();
    const uniqueSuffix = timestamp.toString(36) + Math.random().toString(36).substr(2, 5);
    
    // 获取文件扩展名，如果没有或无法识别，默认使用jpg
    let fileExt = 'jpg';
    const extMatch = safeFileName.match(/\.([^.]+)$/);
    if (extMatch && extMatch[1]) {
      fileExt = extMatch[1].toLowerCase();
    }
    
    // 构建存储路径
    let detailFolder = '';
    if (typeof DETAIL_FOLDER === 'symbol') {
      detailFolder = DETAIL_FOLDER.toString();
    } else {
      detailFolder = String(DETAIL_FOLDER || 'details');
    }
    
    const cloudPath = `${detailFolder}/${uniqueSuffix}_${safeFileName}`;
    log('详情图上传 - 目标路径', cloudPath);
    
    // 替代copyFile方法的实现 - 使用下载后再上传的方式
    try {
      // 1. 先下载临时文件
      log('详情图上传 - 开始下载临时文件', fileID);
      const downloadResult = await cloud.downloadFile({
        fileID: fileID
      });
      
      if (!downloadResult || !downloadResult.fileContent) {
        log('详情图上传 - 下载临时文件失败', downloadResult || '无返回结果');
        return {
          code: 500,
          message: '下载临时文件失败',
          data: null
        };
      }
      
      // 2. 将下载的文件重新上传到永久路径
      log('详情图上传 - 开始上传到永久存储', cloudPath);
      const uploadResult = await cloud.uploadFile({
        cloudPath: cloudPath,
        fileContent: downloadResult.fileContent
      });
      
      if (!uploadResult || !uploadResult.fileID) {
        log('详情图上传 - 上传到永久存储失败', uploadResult || '无返回结果');
        return {
          code: 500,
          message: '上传到永久存储失败',
          data: null
        };
      }
      
      // 安全处理返回的fileID
      let resultFileID = '';
      if (uploadResult.fileID) {
        if (typeof uploadResult.fileID === 'symbol') {
          resultFileID = uploadResult.fileID.toString();
        } else {
          resultFileID = String(uploadResult.fileID);
        }
      }
      
      log('详情图上传 - 上传成功', resultFileID);
      
      // 生成临时访问链接
      let tempUrl = '';
      try {
        tempUrl = await generateTempUrl(resultFileID);
        log('详情图上传 - 生成临时链接成功', tempUrl);
      } catch (urlErr) {
        log('详情图上传 - 生成临时链接失败', urlErr);
        // 继续执行，允许没有临时链接
      }
      
      // 返回结果
      return {
        code: 200,
        message: '上传详情图成功',
        data: {
          fileID: resultFileID,
          key: resultFileID, // 与前端兼容
          tempUrl: tempUrl || '',
          fileType: fileExt
        }
      };
    } catch (processErr) {
      log('详情图上传 - 处理过程出错', processErr);
      
      let errorMsg = '处理云存储详情图失败';
      if (processErr && processErr.message) {
        errorMsg += ': ' + String(processErr.message);
      }
      
      throw new Error(errorMsg);
    }
  } catch (error) {
    log('详情图上传 - 整体流程出错', error);
    
    // 提供更详细的错误信息
    let errorMessage = '上传详情图失败';
    if (error && error.message) {
      const errStr = String(error.message);
      if (errStr.includes('exceed')) {
        errorMessage = '图片文件过大，请压缩后再上传';
      } else if (errStr.includes('format')) {
        errorMessage = '图片格式不支持，请转换为JPG、PNG或WEBP格式';
      } else {
        errorMessage = '上传详情图失败: ' + errStr;
      }
    }
    
    return {
      code: 500,
      message: errorMessage,
      data: null
    };
  }
}

// 添加一个更安全的视频上传函数
/**
 * 直接上传视频到云开发存储（简化版，不使用分片）（更安全的实现）
 * @param {Object} data 请求参数
 * @returns {Object} 响应结果
 */
async function directUploadVideo2(data) {
  try {
    // 安全获取参数
    let fileID = '';
    let fileName = '';
    
    try {
      if (data && data.fileID) {
        fileID = String(data.fileID);
      }
      
      if (data && data.fileName) {
        fileName = String(data.fileName);
      }
    } catch (paramError) {
      log('参数处理错误', paramError);
      return {
        code: 400,
        message: '参数格式错误',
        data: null
      };
    }
    
    if (!fileID || !fileName) {
      return {
        code: 400,
        message: '缺少必要参数',
        data: null
      };
    }
    
    log('处理视频上传请求', { fileID, fileName });
    
    // 生成云路径
    const timestamp = Date.now();
    const uniqueSuffix = timestamp.toString(36) + Math.random().toString(36).slice(2, 8);
    const safeFileName = String(fileName).replace(/[^\w\d.-]/g, '_');
    
    // 获取文件扩展名
    let fileExt = 'mp4';
    const extMatch = safeFileName.match(/\.([^.]+)$/);
    if (extMatch && extMatch[1]) {
      fileExt = extMatch[1].toLowerCase();
    }
    
    // 安全获取常量
    const videoFolder = String(VIDEO_FOLDER || 'videos');
    const cloudPath = `${videoFolder}/${uniqueSuffix}_${safeFileName}`;
    
    log('准备上传视频', { source: fileID, path: cloudPath });
    
    // 使用下载后再上传的方式替代copyFile
    try {
      // 1. 先下载临时文件
      log('视频上传 - 开始下载临时文件', fileID);
      const downloadResult = await cloud.downloadFile({
        fileID: fileID
      });
      
      if (!downloadResult || !downloadResult.fileContent) {
        log('视频上传 - 下载临时文件失败', downloadResult || '无返回结果');
        return {
          code: 500,
          message: '下载临时视频文件失败',
          data: null
        };
      }
      
      log('视频上传 - 临时文件下载成功，文件大小:', downloadResult.fileContent.length);
      
      // 2. 将下载的文件重新上传到永久路径
      log('视频上传 - 开始上传到永久存储', cloudPath);
      const uploadResult = await cloud.uploadFile({
        cloudPath: cloudPath,
        fileContent: downloadResult.fileContent
      });
      
      if (!uploadResult || !uploadResult.fileID) {
        log('视频上传 - 上传到永久存储失败', uploadResult || '无返回结果');
        return {
          code: 500,
          message: '上传到永久存储失败',
          data: null
        };
      }
      
      // 安全处理结果
      const resultFileID = String(uploadResult.fileID || '');
      log('视频上传 - 上传成功', resultFileID);
      
      // 生成临时访问链接
      let tempUrl = '';
      try {
        tempUrl = await generateTempUrl(resultFileID);
        log('视频上传 - 生成临时链接成功');
      } catch (urlError) {
        log('视频上传 - 生成临时URL失败', urlError);
        // 继续执行，不中断流程
      }
      
      return {
        code: 200,
        message: '视频上传成功',
        data: {
          fileID: resultFileID,
          key: resultFileID,
          tempUrl: tempUrl,
          fileType: fileExt
        }
      };
    } catch (processError) {
      log('视频上传处理过程出错', processError);
      throw new Error('视频处理失败: ' + String(processError.message || '未知错误'));
    }
  } catch (error) {
    log('视频上传失败', error);
    
    // 提供更详细的错误信息
    let errorMessage = '视频上传失败';
    if (error && error.message) {
      const errStr = String(error.message);
      if (errStr.includes('exceed') || errStr.includes('large')) {
        errorMessage = '视频文件过大，请压缩后再上传';
      } else if (errStr.includes('format')) {
        errorMessage = '视频格式不支持，请转换为MP4或其他常见格式';
      } else {
        errorMessage = '视频上传失败: ' + errStr;
      }
    }
    
    return {
      code: 500,
      message: errorMessage,
      data: null
    };
  }
}