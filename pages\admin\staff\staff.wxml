<!--pages/admin/staff/staff.wxml-->
<view class="staff-container">
  <!-- 顶部状态栏 -->
  <view class="page-header">
    <view class="back-icon" bindtap="navigateBack">←</view>
    <view class="header-title">员工管理</view>
    <view style="width: 60rpx;"></view> <!-- 占位，保持标题居中 -->
  </view>
  
  <!-- 员工列表 -->
  <view class="staff-list">
    <view wx:if="{{loading}}" class="loading">
      <view class="loading-icon"></view>
      <view class="loading-text">加载中...</view>
    </view>
    
    <view wx:elif="{{staffList.length === 0}}" class="empty-tip">
      暂无员工，请添加
    </view>
    
    <view wx:else class="list-content">
      <!-- 表头 -->
      <view class="list-header">
        <view class="cell name-cell">姓名</view>
        <view class="cell phone-cell">手机号</view>
        <view class="cell commission-cell">提成</view>
        <view class="cell status-cell">状态</view>
        <view class="cell action-cell">操作</view>
      </view>
      
      <!-- 列表项 -->
      <view class="list-item" wx:for="{{staffList}}" wx:key="_id">
        <view class="cell name-cell">{{item.name}}</view>
        <view class="cell phone-cell">{{item.phoneNumber}}</view>
        <view class="cell commission-cell">
          <view>现金: {{item.commissionRate ? (item.commissionRate * 100) + '%' : '30%'}}</view>
          <view>余额: {{item.balanceCommissionRate ? (item.balanceCommissionRate * 100) + '%' : '20%'}}</view>
        </view>
        <view class="cell status-cell">
          <view class="status-tag {{item.status === 'active' ? 'active' : 'inactive'}}">
            {{item.status === 'active' ? '正常' : '停用'}}
          </view>
        </view>
        <view class="cell action-cell">
          <view class="action-btn edit-btn" bindtap="showEditStaffModal" data-id="{{item._id}}">编</view>
          <view class="action-btn {{item.status === 'active' ? 'disable-btn' : 'enable-btn'}}" bindtap="toggleStaffStatus" data-id="{{item._id}}">
            {{item.status === 'active' ? '停' : '启'}}
          </view>
        </view>
      </view>
    </view>
  </view>
  
  <!-- 悬浮添加按钮 -->
  <view class="add-button" bindtap="showAddStaffModal">
    <text class="add-icon">+</text>
  </view>
  
  <!-- 添加员工弹窗 -->
  <view class="modal" wx:if="{{showAddModal}}">
    <view class="modal-mask" bindtap="closeAddModal"></view>
    <view class="modal-content">
      <view class="modal-header">
        <view class="modal-title">添加员工</view>
        <view class="modal-close" bindtap="closeAddModal">×</view>
      </view>
      
      <view class="modal-body">
        <view class="form-item avatar-upload">
          <view class="form-label">头像</view>
          <view class="avatar-wrapper" bindtap="chooseAvatar" data-type="add">
            <image class="avatar-preview" src="{{formData.avatarUrl || '/static/images/default-avatar.png'}}" mode="aspectFill"></image>
            <view class="avatar-upload-icon">+</view>
          </view>
        </view>
        
        <view class="form-item">
          <view class="form-label">姓名</view>
          <input class="form-input" placeholder="请输入员工姓名" value="{{formData.name}}" bindinput="onInput" data-field="name" />
        </view>
        
        <view class="form-item">
          <view class="form-label">手机号</view>
          <input class="form-input" placeholder="请输入手机号" type="number" maxlength="11" value="{{formData.phoneNumber}}" bindinput="onInput" data-field="phoneNumber" />
        </view>
        
        <view class="form-item">
          <view class="form-label">登录密码</view>
          <input class="form-input" placeholder="请输入登录密码" password value="" bindinput="onInput" data-field="password" />
        </view>
        
        <view class="form-item">
          <view class="form-label">现金支付提成比例(%)</view>
          <view class="commission-slider">
            <slider min="0" max="100" value="{{formData.commissionRate}}" show-value bindchange="onCommissionRateChange" activeColor="#0070c9" block-color="#0070c9"></slider>
          </view>
        </view>
        
        <view class="form-item">
          <view class="form-label">余额支付提成比例(%)</view>
          <view class="commission-slider">
            <slider min="0" max="100" value="{{formData.balanceCommissionRate}}" show-value bindchange="onBalanceCommissionRateChange" activeColor="#4caf50" block-color="#4caf50"></slider>
          </view>
        </view>
        
        <view class="form-item">
          <view class="form-label">状态</view>
          <picker class="form-picker" range="{{statusOptions}}" range-key="label" value="{{formData.status === 'active' ? 0 : 1}}" bindchange="onStatusChange">
            <view class="picker-text">{{formData.status === 'active' ? '正常' : '停用'}}</view>
          </picker>
        </view>
      </view>
      
      <view class="modal-footer">
        <view class="modal-btn cancel-btn" bindtap="closeAddModal">取消</view>
        <view class="modal-btn confirm-btn" bindtap="addStaff">确定</view>
      </view>
    </view>
  </view>
  
  <!-- 编辑员工弹窗 -->
  <view class="modal" wx:if="{{showEditModal}}">
    <view class="modal-mask" bindtap="closeEditModal"></view>
    <view class="modal-content">
      <view class="modal-header">
        <view class="modal-title">编辑员工</view>
        <view class="modal-close" bindtap="closeEditModal">×</view>
      </view>
      
      <view class="modal-body">
        <view class="form-item avatar-upload">
          <view class="form-label">头像</view>
          <view class="avatar-wrapper" bindtap="chooseAvatar" data-type="edit">
            <image class="avatar-preview" src="{{formData.avatarUrl || currentStaff.avatar || '/static/images/default-avatar.png'}}" mode="aspectFill"></image>
            <view class="avatar-upload-icon">+</view>
          </view>
        </view>
        
        <view class="form-item">
          <view class="form-label">姓名</view>
          <input class="form-input" placeholder="请输入员工姓名" value="{{formData.name}}" bindinput="onInput" data-field="name" />
        </view>
        
        <view class="form-item">
          <view class="form-label">手机号</view>
          <input class="form-input" placeholder="请输入手机号" type="number" maxlength="11" value="{{formData.phoneNumber}}" bindinput="onInput" data-field="phoneNumber" />
        </view>
        
        <view class="form-item">
          <view class="form-label">登录密码</view>
          <input class="form-input" placeholder="不修改请留空" password value="{{formData.password}}" bindinput="onInput" data-field="password" />
        </view>
        
        <view class="form-item">
          <view class="form-label">现金支付提成比例(%)</view>
          <view class="commission-slider">
            <slider min="0" max="100" value="{{formData.commissionRate}}" show-value bindchange="onCommissionRateChange" activeColor="#0070c9" block-color="#0070c9"></slider>
          </view>
        </view>
        
        <view class="form-item">
          <view class="form-label">余额支付提成比例(%)</view>
          <view class="commission-slider">
            <slider min="0" max="100" value="{{formData.balanceCommissionRate}}" show-value bindchange="onBalanceCommissionRateChange" activeColor="#4caf50" block-color="#4caf50"></slider>
          </view>
        </view>
        
        <view class="form-item">
          <view class="form-label">状态</view>
          <picker class="form-picker" range="{{statusOptions}}" range-key="label" value="{{formData.status === 'active' ? 0 : 1}}" bindchange="onStatusChange">
            <view class="picker-text">{{formData.status === 'active' ? '正常' : '停用'}}</view>
          </picker>
        </view>
      </view>
      
      <view class="modal-footer">
        <view class="modal-btn cancel-btn" bindtap="closeEditModal">取消</view>
        <view class="modal-btn confirm-btn" bindtap="updateStaff">确定</view>
      </view>
    </view>
  </view>
  
  <!-- 底部安全区域 -->
  <view class="safe-bottom-area"></view>
</view> 