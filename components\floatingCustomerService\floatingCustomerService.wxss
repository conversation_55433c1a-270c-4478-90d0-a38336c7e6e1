.floating-customer-service {
  position: fixed;
  z-index: 9999;
  touch-action: none;
}

.service-wrapper {
  position: relative;
  width: auto;
  height: auto;
  display: inline-block;
}

.close-btn {
  position: absolute;
  top: -10rpx;
  right: -10rpx;
  width: 30rpx;
  height: 30rpx;
  background: rgba(0, 0, 0, 0.6);
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 22rpx;
  z-index: 10000;
  box-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.3);
}

.customer-service-btn {
  width: 70rpx !important;
  height: 70rpx;
  padding: 8rpx;
  margin: 0;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
}

.customer-service-btn::after {
  border: none;
}

.customer-service-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 0;
}