Page({
  data: {
    // 地图中心点坐标（默认值，将在onLoad中更新为店铺位置）
    latitude: 23.144894,
    longitude: 113.270213,
    scale: 16,
    markers: [],
    shopName: '今禧美学',
    shopAddress: '广州市越秀区广园西路121号美博城',
    shopPhone: '020-12345678'
  },

  onLoad: function (options) {
    // 获取店铺位置信息（实际应用中可从后端API获取）
    const shopLocation = {
      latitude: 23.144894,
      longitude: 113.270213,
      name: '今禧美学',
      address: '广州市越秀区广园西路121号美博城',
      phone: '020-12345678'
    };

    // 更新店铺信息
    this.setData({
      latitude: shopLocation.latitude,
      longitude: shopLocation.longitude,
      shopName: shopLocation.name,
      shopAddress: shopLocation.address,
      shopPhone: shopLocation.phone,
      markers: [{
        id: 1,
        latitude: shopLocation.latitude,
        longitude: shopLocation.longitude,
        callout: {
          content: shopLocation.name,
          color: '#000000',
          fontSize: 14,
          borderRadius: 4,
          bgColor: '#ffffff',
          padding: 8,
          display: 'ALWAYS'
        },
        iconPath: '/static/定位图标.png', // 使用已有的定位图标
        width: 30,
        height: 30
      }]
    });

    // 获取用户当前位置
    this.getUserLocation();
  },

  // 获取用户当前位置
  getUserLocation: function () {
    const that = this;
    wx.getLocation({
      type: 'gcj02', // 使用国测局坐标系（火星坐标系）
      success: function (res) {
        // 添加用户位置标记
        const userMarker = {
          id: 2,
          latitude: res.latitude,
          longitude: res.longitude,
          iconPath: '/static/我的图标亮.png', // 使用已有的图标
          width: 30,
          height: 30
        };

        // 更新标记数组，保留店铺标记，添加用户位置标记
        const updatedMarkers = [...that.data.markers];
        const userMarkerIndex = updatedMarkers.findIndex(marker => marker.id === 2);
        if (userMarkerIndex >= 0) {
          updatedMarkers[userMarkerIndex] = userMarker;
        } else {
          updatedMarkers.push(userMarker);
        }

        that.setData({
          markers: updatedMarkers
        });
      },
      fail: function (err) {
        console.error('获取用户位置失败', err);
        wx.showToast({
          title: '获取位置信息失败，请检查是否授权',
          icon: 'none',
          duration: 2000
        });
      }
    });
  },

  // 点击标记时的回调
  markerTap: function (e) {
    const markerId = e.markerId;
    console.log('点击了标记：', markerId);
    // 可以根据markerId做不同的处理
  },

  // 导航到店铺
  navigateToHere: function () {
    const { latitude, longitude, shopName, shopAddress } = this.data;
    wx.openLocation({
      latitude,
      longitude,
      name: shopName,
      address: shopAddress,
      scale: 18
    });
  },

  // 拨打店铺电话
  callShop: function () {
    wx.makePhoneCall({
      phoneNumber: this.data.shopPhone,
      fail: function () {
        wx.showToast({
          title: '拨打电话失败',
          icon: 'none'
        });
      }
    });
  },

  // 返回上一页
  goBack: function () {
    wx.navigateBack({
      delta: 1
    });
  }
}); 