const app = getApp()

Page({
  data: {
    contentType: '', // 内容类型：gallery, banner, notice
    contentId: '', // 编辑时的内容ID
    isEdit: false, // 是否是编辑模式
    isLoading: false, // 加载状态
    isSaving: false, // 保存状态
    
    // 表单数据
    formData: {
      title: '', // 标题
      coverUrl: '', // 封面图片
      imageUrl: '', // Banner图片
      linkUrl: '', // 链接URL
      content: '', // 公告内容
      status: 1 // 状态：1-显示，0-隐藏
    },
    
    // 临时图片
    tempImagePath: '',
    // 是否显示图片上传进度
    showUploadProgress: false,
    uploadProgress: 0
  },

  onLoad(options) {
    // 检查管理员登录状态
    if (!app.globalData.isAdmin) {
      this.redirectToLogin()
      return
    }
    
    const { type, id } = options
    
    // 设置内容类型
    this.setData({
      contentType: type || 'gallery',
      contentId: id || '',
      isEdit: !!id
    })
    
    // 如果是编辑模式，加载内容数据
    if (this.data.isEdit) {
      this.loadContentData()
    }
    
    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: this.data.isEdit ? '编辑内容' : '添加内容'
    })
  },
  
  // 重定向到登录页
  redirectToLogin() {
    wx.showToast({
      title: '请先登录',
      icon: 'none'
    })
    
    setTimeout(() => {
      wx.navigateTo({
        url: '/pages/admin/login'
      })
    }, 1500)
  },
  
  // 加载内容数据
  loadContentData() {
    const { contentType, contentId } = this.data
    
    this.setData({ isLoading: true })
    
    // 根据内容类型获取不同的数据
    if (contentType === 'gallery') {
      // 模拟获取画廊内容
      const mockData = this.getMockGalleryData().find(item => item._id === contentId)
      if (mockData) {
        this.setData({
          'formData.title': mockData.title,
          'formData.coverUrl': mockData.coverUrl,
          'formData.status': mockData.status,
          tempImagePath: mockData.coverUrl,
          isLoading: false
        })
      } else {
        this.setData({ isLoading: false })
        wx.showToast({
          title: '内容不存在',
          icon: 'none'
        })
      }
    } else if (contentType === 'banner') {
      // 模拟获取Banner内容
      const mockData = this.getMockBannerData().find(item => item._id === contentId)
      if (mockData) {
        this.setData({
          'formData.title': mockData.title,
          'formData.imageUrl': mockData.imageUrl,
          'formData.linkUrl': mockData.linkUrl,
          'formData.status': mockData.status,
          tempImagePath: mockData.imageUrl,
          isLoading: false
        })
      } else {
        this.setData({ isLoading: false })
        wx.showToast({
          title: '内容不存在',
          icon: 'none'
        })
      }
    } else if (contentType === 'notice') {
      // 模拟获取公告内容
      const mockData = this.getMockNoticeData().find(item => item._id === contentId)
      if (mockData) {
        this.setData({
          'formData.title': mockData.title,
          'formData.content': mockData.content,
          'formData.status': mockData.status,
          isLoading: false
        })
      } else {
        this.setData({ isLoading: false })
        wx.showToast({
          title: '内容不存在',
          icon: 'none'
        })
      }
    }
  },
  
  // 输入标题
  onTitleInput(e) {
    this.setData({
      'formData.title': e.detail.value
    })
  },
  
  // 输入链接
  onLinkInput(e) {
    this.setData({
      'formData.linkUrl': e.detail.value
    })
  },
  
  // 输入内容
  onContentInput(e) {
    this.setData({
      'formData.content': e.detail.value
    })
  },
  
  // 选择图片
  chooseImage() {
    wx.chooseMedia({
      count: 1,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const tempPath = res.tempFiles[0].tempFilePath
        this.setData({
          tempImagePath: tempPath
        })
        
        // 模拟上传过程
        this.simulateImageUpload(tempPath)
      }
    })
  },
  
  // 模拟图片上传
  simulateImageUpload(filePath) {
    this.setData({
      showUploadProgress: true,
      uploadProgress: 0
    })
    
    // 模拟上传进度
    let progress = 0
    const timer = setInterval(() => {
      progress += 10
      this.setData({
        uploadProgress: progress
      })
      
      if (progress >= 100) {
        clearInterval(timer)
        
        // 模拟上传完成，设置图片URL
        const { contentType } = this.data
        if (contentType === 'gallery') {
          this.setData({
            'formData.coverUrl': filePath,
            showUploadProgress: false
          })
        } else if (contentType === 'banner') {
          this.setData({
            'formData.imageUrl': filePath,
            showUploadProgress: false
          })
        }
        
        wx.showToast({
          title: '上传成功',
          icon: 'success'
        })
      }
    }, 300)
  },
  
  // 切换状态
  toggleStatus() {
    this.setData({
      'formData.status': this.data.formData.status === 1 ? 0 : 1
    })
  },
  
  // 提交表单
  submitForm() {
    const { contentType, formData, isEdit, contentId } = this.data
    
    // 验证表单
    if (!formData.title) {
      wx.showToast({
        title: '请输入标题',
        icon: 'none'
      })
      return
    }
    
    // 针对不同类型的内容进行验证
    if (contentType === 'gallery' && !formData.coverUrl) {
      wx.showToast({
        title: '请上传封面图片',
        icon: 'none'
      })
      return
    }
    
    if (contentType === 'banner') {
      if (!formData.imageUrl) {
        wx.showToast({
          title: '请上传Banner图片',
          icon: 'none'
        })
        return
      }
      
      if (!formData.linkUrl) {
        wx.showToast({
          title: '请输入链接地址',
          icon: 'none'
        })
        return
      }
    }
    
    if (contentType === 'notice' && !formData.content) {
      wx.showToast({
        title: '请输入公告内容',
        icon: 'none'
      })
      return
    }
    
    // 设置保存状态
    this.setData({ isSaving: true })
    
    // 模拟保存数据
    setTimeout(() => {
      this.setData({ isSaving: false })
      
      wx.showToast({
        title: isEdit ? '更新成功' : '添加成功',
        icon: 'success'
      })
      
      // 延迟返回上一页
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
    }, 1000)
  },
  
  // 返回上一页
  navigateBack() {
    wx.navigateBack()
  },
  
  // 模拟画廊数据
  getMockGalleryData() {
    return [
      {
        _id: 'gallery1',
        title: '作品集合一',
        coverUrl: '/static/images/gallery1.jpg',
        type: 'gallery',
        createTime: '2023-12-01',
        updateTime: '2023-12-20',
        status: 1
      },
      {
        _id: 'gallery2',
        title: '作品集合二',
        coverUrl: '/static/images/gallery2.jpg',
        type: 'gallery',
        createTime: '2023-11-15',
        updateTime: '2023-12-18',
        status: 1
      },
      {
        _id: 'gallery3',
        title: '美甲展示专辑',
        coverUrl: '/static/images/gallery3.jpg',
        type: 'gallery',
        createTime: '2023-10-20',
        updateTime: '2023-12-15',
        status: 1
      }
    ]
  },
  
  // 模拟Banner数据
  getMockBannerData() {
    return [
      {
        _id: 'banner1',
        title: '新春优惠活动',
        imageUrl: '/static/images/banner1.jpg',
        linkUrl: '/pages/promotion/detail?id=1',
        type: 'banner',
        createTime: '2023-12-10',
        updateTime: '2023-12-20',
        status: 1
      },
      {
        _id: 'banner2',
        title: '美甲新品上市',
        imageUrl: '/static/images/banner2.jpg',
        linkUrl: '/pages/product/detail?id=2',
        type: 'banner',
        createTime: '2023-11-20',
        updateTime: '2023-12-15',
        status: 1
      }
    ]
  },
  
  // 模拟公告数据
  getMockNoticeData() {
    return [
      {
        _id: 'notice1',
        title: '店铺营业时间调整通知',
        content: '尊敬的顾客，本店从2024年1月1日起，营业时间调整为10:00-22:00，感谢您的支持！',
        type: 'notice',
        createTime: '2023-12-20',
        updateTime: '2023-12-20',
        status: 1
      },
      {
        _id: 'notice2',
        title: '会员积分活动',
        content: '即日起至2024年2月底，会员消费每满100元赠送20积分，积分可兑换多种礼品！',
        type: 'notice',
        createTime: '2023-12-15',
        updateTime: '2023-12-15',
        status: 1
      }
    ]
  }
}) 