/* 测试页面样式 */
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  padding: 20rpx 0;
  margin-bottom: 20rpx;
  border-bottom: 1px solid #ddd;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.subtitle {
  font-size: 30rpx;
  font-weight: bold;
  color: #444;
}

.input-section {
  background-color: #fff;
  padding: 20rpx;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.1);
}

.input-group {
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.label {
  width: 150rpx;
  font-size: 28rpx;
  color: #666;
}

.input {
  flex: 1;
  height: 70rpx;
  border: 1px solid #ddd;
  border-radius: 6rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  background-color: #f9f9f9;
}

.button-group {
  display: flex;
  justify-content: space-between;
  margin: 30rpx 0;
}

.btn {
  flex: 1;
  margin: 0 10rpx;
  background-color: #4285f4;
  color: white;
  font-size: 28rpx;
  height: 80rpx;
  line-height: 80rpx;
}

.btn-small {
  padding: 0 20rpx;
  height: 60rpx;
  line-height: 60rpx;
  font-size: 24rpx;
  margin-left: 10rpx;
  background-color: #4285f4;
  color: white;
}

.result-section {
  background-color: #fff;
  padding: 20rpx;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.1);
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.url-actions {
  display: flex;
}

.url-display {
  background-color: #f0f0f0;
  padding: 20rpx;
  border-radius: 6rpx;
  margin-bottom: 20rpx;
  word-break: break-all;
}

.url-text {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

.video-container {
  width: 100%;
  margin-top: 20rpx;
}

video {
  width: 100%;
}

.log-section {
  background-color: #fff;
  padding: 20rpx;
  border-radius: 10rpx;
  box-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.1);
  margin-bottom: 40rpx;
}

.log-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.log-container {
  height: 600rpx;
  background-color: #f0f0f0;
  border-radius: 6rpx;
  padding: 10rpx;
}

.log-item {
  font-size: 24rpx;
  padding: 10rpx;
  border-bottom: 1px solid #ddd;
  color: #333;
  word-break: break-all;
}

.log-item:nth-child(odd) {
  background-color: #f9f9f9;
} 