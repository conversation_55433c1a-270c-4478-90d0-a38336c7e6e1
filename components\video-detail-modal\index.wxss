/* 导入WxParse样式 */
/* @import "../../wxParse/wxParse.wxss"; */

/* 富文本内容样式 */
.fullscreen-modal-content {
  display: flex;
  flex-direction: column;
  height: 100vh; /* 使用100vh确保占满整个视口高度 */
  padding: 0;
  margin: 0;
  width: 100vw;
  max-width: 100vw;
  box-sizing: border-box;
  position: fixed;
  top: auto; /* 移除顶部固定定位 */
  left: 0;
  right: 0;
  bottom: 0; /* 固定在底部 */
  transform: translateY(100%); /* 初始位置在屏幕底部 */
  overflow: hidden;
  border-top-left-radius: 24rpx; /* 添加顶部圆角 */
  border-top-right-radius: 24rpx; /* 添加顶部圆角 */
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.15); /* 添加顶部阴影 */
  z-index: 9999; /* 确保显示在最上层 */
  /* 使用animation代替transition，提供更好的控制 */
  will-change: transform; /* 优化性能 */
  background-color: var(--videomodal-bg); /* 添加背景色变量 */
  /* 关键：将内容视为整体的一部分 */
  transform-style: flat !important;
  -webkit-transform-style: flat !important;
  /* 消除3D变换可能导致的闪烁 */
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
}

/* 使用动画类来控制可见状态 */
.fullscreen-modal-content.visible {
  animation: videoModalSlideIn 300ms ease-out forwards;
}

/* 关闭状态 */
.fullscreen-modal-content.hidden {
  /* 固定背景色，防止透明效果 */
  background-color: var(--videomodal-bg) !important;
  /* 确保无阴影 */
  box-shadow: none !important;
  /* 使用动画而不是直接设置transform */
  animation: videoModalSlideOut 300ms ease-in forwards !important;
  transition: none !important;
  /* 所有子元素必须共同移动，不能有独立图层 */
  contain: layout style paint;
  -webkit-contain: layout style paint;
}

/* 添加动画关键帧 */
@keyframes videoModalSlideIn {
  0% {
    transform: translateY(100%);
    opacity: 1;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

/* 调整滑出动画，确保平滑过渡 */
@keyframes videoModalSlideOut {
  0% {
    transform: translateY(0);
    opacity: 1;
  }
  100% {
    transform: translateY(100%);
    opacity: 1;
  }
}

/* 自定义modal蒙层，使其与内容一起移动 */
.fullscreen-modal-content + .modal-mask {
  transition: opacity 0.2s;
  will-change: opacity;
}

/* 改进滚动区域样式，确保整体协调 */
.scrollable-content {
  flex: 1;
  /* 确保滚动区域精确计算，使用变量以适应控制条高度变化 */
  height: calc(100vh - var(--videomodal-footer-height, 140rpx));
  position: relative;
  z-index: 1;
  -webkit-overflow-scrolling: touch;
  background-color: transparent; /* 改为透明背景 */
  overflow-y: auto;
  padding: 0;
  width: 100%;
  will-change: scroll-position;
  /* 修改底部padding，让内容可以滚动到底部控制条下方 */
  padding-bottom: 60rpx; /* 增加底部padding，确保内容可以完全滚动 */
}

/* 自定义标题栏 */
.custom-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 24rpx; /* 增加上下内边距 */
  background-color: var(--videomodal-header-bg, #7B8080);
  border-bottom: 1px solid var(--border-color, rgba(255, 255, 255, 0.1));
  position: relative;
  z-index: 10;
  height: 90rpx; /* 设置固定高度 */
  box-sizing: border-box;
}

.header-title {
  font-size: 32rpx;
  font-weight: 500;
  color: var(--videomodal-video-title, #ffffff);
  text-align: center;
  flex: 1;
}

.header-left {
  width: 60rpx;
}

.header-close {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.close-icon {
  font-size: 44rpx;
  color: var(--text-primary, #ffffff);
  line-height: 1;
}

.header-close:active {
  background-color: var(--bg-mask, rgba(0, 0, 0, 0.7));
}

/* 顶部安全区域 - 避免与胶囊按钮重叠 */
.safe-area-top {
  display: none;
}

/* 视频播放区域 - 修改为自适应比例 */
.video-container {
  width: 100%;
  height: 100vw; /* 修改为100vw，保持1:1比例，与主页一致 */
  max-height: calc(100vh - 150rpx); /* 调整最大高度，确保在小屏幕上也能完整显示 */
  background-color: var(--bg-dark);
  position: relative;
  margin: 0;
  padding: 0;
  overflow: hidden;
  border-top-left-radius: 24rpx;
  border-top-right-radius: 24rpx;
  transition: all 0.3s ease;
}

/* 视频容器内的视频元素样式 */
.video-container .video-element {
  width: 100%;
  height: 100%;
  display: block;
  object-fit: contain; /* 保持contain，确保视频不变形 */
  background-color: var(--bg-dark);
  border-radius: 0; /* 确保没有圆角 */
}

/* 竖屏视频适配 - 修改为以宽度为基准 */
.video-container.portrait {
  width: 56.25vw; /* 将视频宽度设置为屏幕宽度的56.25%，保持合适比例 */
  height: 100vw; /* 高度设为屏幕宽度的100%，形成9:16的比例 */
  max-height: calc(100vh - 150rpx); /* 与普通容器保持一致 */
  margin: 0 auto; /* 水平居中 */
}

/* 全屏模式下的容器样式 */
.fullscreen-modal-content.fullscreen {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  z-index: 999999 !important;
  background-color: var(--videomodal-bg); /* 修改为使用videomodal-bg变量 */
  transform: none !important;
  border-radius: 0;
}

/* 全屏模式下的视频容器样式 */
.fullscreen-modal-content.fullscreen .video-container {
  width: 100vw;
  height: 100vh;
  border-radius: 0;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999999;
  background-color: var(--bg-dark);
}

/* 全屏模式下的视频元素样式 */
.fullscreen-modal-content.fullscreen .video-container .video-element {
  width: 100%;
  height: 100%;
  object-fit: contain;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

/* 全屏模式下的视频容器样式 */
.fullscreen-modal-content.fullscreen .video-container.portrait {
  width: 56.25vh; /* 全屏时使用视口高度作为基准 */
  height: 100vh;
  margin: 0 auto;
  position: fixed;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
}

/* 封面图样式 */
.video-container-image {
  width: 100%;
  height: 100%;
  display: block;
  object-fit: cover; /* 封面图使用cover，填满容器 */
  background-color: var(--bg-dark);
}

/* 视频信息区域 */
.video-info {
  padding: 20rpx 30rpx; /* 增加内边距，使内容更加突出 */
  display: flex;
  align-items: flex-start; /* 改为顶部对齐，便于播放量与主标题对齐 */
  background-color: rgba(220, 235, 245, 0.9); /* 修改为淡蓝色背景 */
  position: relative; /* 保持相对定位 */
  min-height: 100rpx; /* 增加最小高度，使区域更大 */
  margin: 10rpx 20rpx 0 20rpx; /* 减小顶部边距，与视频容器更紧凑 */
  border-radius: 16rpx; /* 保持圆角 */
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2); /* 保持阴影效果 */
  border-bottom: none; /* 保持移除底部边框 */
}

.author-avatar {
  width: 80rpx; /* 增加头像尺寸 */
  height: 80rpx; /* 增加头像尺寸 */
  flex-shrink: 0;
  margin-right: 24rpx; /* 与主标题保持合适间距 */
  align-self: flex-start; /* 顶部对齐 */
  margin-top: 4rpx; /* 微调位置 */
}

.author-avatar-image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}

/* 标题和副标题容器 */
.title-container {
  flex: 1; /* 占据剩余空间 */
  display: flex;
  flex-direction: column;
  justify-content: flex-start; /* 改为顶部对齐 */
  margin-right: 24rpx; /* 与播放量保持间距 */
  overflow: hidden; /* 确保内容不溢出 */
  min-width: 0; /* 解决flex子项目溢出问题 */
  padding-top: 4rpx; /* 添加顶部内边距，微调位置 */
}

.video-title {
  font-size: 28rpx;
  font-weight: bold;
  color: var(--videomodal-video-title, #ffffff);
  line-height: 1.4;
  width: 100%; /* 使用容器宽度 */
  word-wrap: break-word; /* 允许长单词换行 */
  margin-bottom: 6rpx; /* 减小与副标题的间距 */
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2; /* 最多显示2行 */
  overflow: hidden;
}

.video-subtitle {
  font-size: 22rpx;
  color: var(--videomodal-video-subtitle, rgba(255, 255, 255, 0.8));
  line-height: 1.3; /* 减小行高 */
  width: 100%; /* 使用容器宽度 */
  word-wrap: break-word; /* 允许长单词换行 */
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2; /* 最多显示2行 */
  overflow: hidden;
  opacity: 0.8; /* 降低不透明度，使其与主标题形成对比 */
}

.play-count {
  font-size: 22rpx;
  color: var(--videomodal-playcount-text, #ffffff);
  line-height: 1.2;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  gap: 4rpx;
  margin-left: auto;
  margin-right: 12rpx;
  background-color: var(--videomodal-playcount-bg, rgba(0, 0, 0, 0.3));
  padding: 8rpx 16rpx;
  border-radius: var(--videomodal-playcount-radius, 12rpx);
  min-width: 120rpx;
  justify-content: center;
  white-space: nowrap;
  align-self: flex-start;
  margin-top: 4rpx; /* 从12rpx减少到4rpx，向上移动8rpx */
}

/* 播放量文字样式 */
.play-count-text {
  font-size: 22rpx; /* 与父容器一致 */
  color: var(--videomodal-playcount-text, #ffffff);
  opacity: 0.9;
}

/* 装饰点样式 */
.decorator-dot {
  font-size: 24rpx; /* 稍微增大点的大小 */
  color: var(--videomodal-dot-color, #AAAAAA); /* 改为灰色 */
  opacity: 0.8; /* 稍微降低不透明度 */
  margin: 0 4rpx; /* 增加左右间距 */
  font-weight: bold; /* 加粗 */
}

/* 移除原来的图标样式 */
.view-icon {
  display: none;
}



.touch-icon {
  width: 59rpx;
  height: 59rpx;
  object-fit: contain;
  backface-visibility: hidden; /* 提高渲染性能 */
  filter: brightness(0) invert(1); /* 简化滤镜以确保图标为白色 */
}

/* 触碰图标的缩放动画 */
@keyframes pulseScale {
  0% {
    transform: scale(var(--videomodal-pulse-scale-max, 1));
  }
  50% {
    transform: scale(var(--videomodal-pulse-scale-min, 0.6));
  }
  100% {
    transform: scale(var(--videomodal-pulse-scale-max, 1));
  }
}

/* 视频详情描述区域 */
.video-detail-section {
  padding: 20rpx 15rpx; /* 进一步减少左右内边距，为图片留出更多空间 */
  background-color: rgba(220, 235, 245, 0.9); /* 修改为淡蓝色背景 */
  margin: 10rpx 20rpx 30rpx 20rpx; /* 保持外边距不变 */
  border-radius: 16rpx;
  box-sizing: border-box;
  overflow-x: hidden;
}

/* 标题和刷新按钮行 */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}

.section-title {
  font-size: 0; /* 将字体大小设为0，隐藏文字 */
  font-weight: 400;
  color: transparent; /* 设置颜色为透明 */
  padding: 4rpx 15rpx;
  background-color: transparent; /* 移除背景色 */
  border-radius: 20rpx;
}

/* 刷新按钮样式 */
.refresh-btn {
  font-size: 0; /* 将字体大小设为0，隐藏文字 */
  color: transparent; /* 设置颜色为透明 */
  padding: 0;
  border: none;
  border-radius: 0;
  background: transparent;
}

.refresh-btn:active {
  opacity: 1;
}

/* 调试信息样式 */
.debug-info {
  font-size: 24rpx;
  color: var(--text-secondary, rgba(255, 255, 255, 0.8));
  background-color: var(--bg-light, #333333);
  padding: 10rpx;
  margin-bottom: 10rpx;
  border-radius: 8rpx;
  word-break: break-all;
  border: 1rpx solid var(--videomodal-border, rgba(211, 94, 94, 0.2));
}

/* 加载动画容器 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx 0;
}

/* 三点加载动画 */
.loading-dots {
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background-color: var(--videomodal-loading-dot, rgba(224, 40, 40, 1));
  margin: 0 8rpx;
  animation: dot-animation 1.4s infinite ease-in-out both;
}

.loading-dot:nth-child(1) {
  animation-delay: -0.32s;
}

.loading-dot:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes dot-animation {
  0%, 80%, 100% { 
    transform: scale(0);
  } 
  40% { 
    transform: scale(1.0);
  }
}

.loading-text {
  margin-top: 20rpx;
  font-size: 26rpx;
  color: var(--videomodal-loading-text, rgba(204, 60, 60, 0.8));
}

.detail-content {
  margin-bottom: 20rpx;
}

.description-text {
  font-size: 28rpx;
  color: var(--videomodal-detail-text, #ffffff);
  line-height: 1.6;
}

.description-placeholder {
  font-size: 28rpx;
  color: var(--videomodal-detail-text, #ffffff);
  line-height: 1.6;
  opacity: 0.6;
}

.rich-content-area {
  min-height: 200rpx;
}

/* 添加拖动指示器样式 */
.drag-indicator {
  width: 80rpx;
  height: 8rpx;
  border-radius: 4rpx;
  background-color: var(--videomodal-drag-indicator, rgba(255, 255, 255, 0.8));
  position: absolute;
  top: 12rpx;
  left: 50%;
  transform: translateX(-50%);
  z-index: 100;
  margin: 0;
}

/* 添加底部留白区域样式 */
.bottom-space {
  height: 75rpx; /* 调整为75rpx的留白高度 */
  width: 100%;
  padding: 0;
  margin: 0;
}

/* 底部操作区 */
.modal-footer {
  height: var(--videomodal-footer-height, 140rpx);
  padding: 0 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  /* 修改为半透明背景，创建磨砂玻璃效果 */
  background-color: var(--videomodal-footer-bg, rgba(216, 71, 71, 0.3)); /* 调整透明度为0.3 */
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-top: 0.5px solid rgba(255, 255, 255, 0.1);
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 10;
  width: 100%;
  box-sizing: border-box;
  gap: 30rpx;
  padding-bottom: 20rpx; /* 为安全区域留出空间 */
}

.action-button {
  padding: 0;
  background-color: transparent;
  font-size: 28rpx;
  line-height: normal;
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
  border: none;
  outline: none;
  box-shadow: none;
  position: relative;
  margin-top: 15rpx; /* 向上移动按钮位置 */
}

/* 移除默认按钮样式和点击效果 */
.action-button::after {
  display: none;
  border: none;
  content: none;
}

.action-button:hover,
.action-button:active,
.action-button.button-hover {
  background-color: transparent;
  opacity: 1;
}

/* 添加底部按钮图标样式 */
.action-button-icon {
  width: var(--videomodal-button-icon-size, 56rpx); /* 使用变量定义尺寸 */
  height: var(--videomodal-button-icon-size, 56rpx);
  filter: var(--videomodal-button-filter, brightness(0) invert(1)); /* 确保图标为白色 */
}



/* 添加自定义控制器样式，用于平滑过渡 */
.custom-video-controls {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.5));
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10rpx;
  opacity: 1;
  transition: opacity 1s ease;
}

.custom-video-controls.hidden {
  opacity: 0;
  pointer-events: none;
}

/* 富文本样式类 - 新增 */
.rich-text-wrapper {
  color: var(--videomodal-detail-text, #ffffff) !important;
  width: 100%;
  box-sizing: border-box;
}

.rich-text-content {
  color: var(--videomodal-detail-text, #ffffff) !important;
}

/* 富文本相关样式 */
.rich-text-node {
  color: var(--videomodal-detail-text, #ffffff) !important;
  width: 100%;
  font-size: 28rpx;
  line-height: 1.6;
  box-sizing: border-box;
}

.rich-text-error {
  color: var(--videomodal-detail-text, #ffffff);
  text-align: center;
  padding: 20rpx;
}

/* 表格样式 */
.rich-text-table {
  width: 100%;
  border-collapse: collapse;
  margin: 10rpx 0;
  border: 1px solid #ddd;
  color: var(--videomodal-detail-text, #ffffff);
  }

.rich-text-th {
  border: 1px solid #ddd;
  padding: 8rpx;
  text-align: left;
  font-weight: bold;
  color: var(--videomodal-detail-text, #ffffff);
  }

.rich-text-td {
  border: 1px solid #ddd;
  padding: 8rpx;
  text-align: left;
  color: var(--videomodal-detail-text, #ffffff);
}

.close-button, .scroll-top-button {
  flex: 1; /* 两侧按钮占相同空间 */
}

.close-button {
  background-color: transparent;
  color: var(--videomodal-button-text, #ffffff);
  padding: 16rpx 40rpx;
  border-radius: 40rpx;
  font-size: 30rpx;
}

/* 移除关闭按钮的点击效果 */
.close-button:active,
.close-button:hover,
.close-button.button-hover {
  background-color: transparent;
  opacity: 1;
}

.scroll-top-button {
  background-color: transparent;
  color: var(--videomodal-button-text, #ffffff);
  padding: 16rpx 40rpx;
  border-radius: 40rpx;
  font-size: 30rpx;
}

/* 移除置顶按钮的点击效果 */
.scroll-top-button:active,
.scroll-top-button:hover,
.scroll-top-button.button-hover {
  background-color: transparent;
  opacity: 1;
}

/* 富文本容器样式 - 已合并到video-detail-section */
.rich-content-container {
  display: none; /* 隐藏冗余容器 */
}

/* 视频控件淡入淡出效果 */
.video-element {
  width: 100%;
  transition: opacity 1s ease;
}

/* 视频容器基础样式 */
.video-container {
  position: relative;
  width: 100%;
  background-color: var(--bg-dark);
  overflow: hidden;
}

/* 对视频控件应用过渡效果 */
.controls-transition {
  opacity: 1;
}

/* 使用单独的类来实现控件淡出背景效果，替代伪元素 */
.controls-hidden-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.1);
  pointer-events: none;
  opacity: 0.8;
  transition: opacity 1s ease;
}

/* 视频控件淡出效果 - 使用类选择器 */
.controls-hidden .video-element {
  opacity: 0.3; /* 不完全透明，确保用户知道视频仍在播放 */
  transition: opacity 1s ease;
}

/* 详情图片区域样式 */
.detail-images-container {
  width: 100%;
  padding: 0; /* 完全移除内边距 */
  box-sizing: border-box;
  margin-top: 10rpx; /* 添加顶部外边距 */
}

.detail-image-item {
  width: 100%;
  margin-bottom: 15rpx; /* 减小图片之间的间距 */
  border-radius: 6rpx; /* 进一步减小圆角 */
  overflow: hidden;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.08); /* 减轻阴影 */
}

.detail-image {
  width: 110%; /* 进一步增加图片宽度到110% */
  max-width: 110%; /* 设置最大宽度 */
  margin: 0 -5%; /* 使用负边距使图片居中 */
  display: block;
}