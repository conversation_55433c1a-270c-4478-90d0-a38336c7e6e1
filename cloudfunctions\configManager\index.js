// 云函数入口文件
const cloud = require('wx-server-sdk');

// 获取云环境ID - 从环境变量或配置中获取，避免硬编码
const envId = process.env.CLOUD_ENV;

// 初始化云环境
cloud.init({
  env: envId
});

// 导入配置
const config = require('./config');

// 初始化集合函数
async function initCollections() {
  try {
    const db = cloud.database();
    
    // 检查并创建config集合
    try {
      await db.createCollection('config')
      console.log('创建config集合成功')
    } catch (err) {
      // 如果集合已存在，会抛出错误，这是正常的
      console.log('config集合已存在或创建失败:', err.message)
    }
    
    // 检查并创建operation_logs集合
    try {
      await db.createCollection('operation_logs')
      console.log('创建operation_logs集合成功')
    } catch (err) {
      // 如果集合已存在，会抛出错误，这是正常的
      console.log('operation_logs集合已存在或创建失败:', err.message)
    }
    
    return true
  } catch (err) {
    console.error('初始化集合失败:', err)
    return false
  }
}

/**
 * 全局配置管理云函数
 * 用于集中管理和提供全局配置信息
 */
exports.main = async (event, context) => {
  // 初始化集合
  await initCollections();
  
  const { action, data = {} } = event;
  
  try {
    switch (action) {
      case 'setCollectionPermission':
        return await setCollectionPermission(data);
      case 'getConfig':
        return await getConfig(data);
      case 'getCloudStorageConfig':
        return getCloudStorageConfig();
      case 'getLaunchConfig':
        return getLaunchConfig();
      case 'getFileNamingRules':
        return getFileNamingRules(data);
      case 'getRequestConfig':
        return getRequestConfig();
      case 'getVideoConfig':
        return getVideoConfig(data);
      case 'getAllConfig':
        return getAllConfig();
      default:
        return {
          code: 404,
          message: '未知的操作',
          data: null
        };
    }
  } catch (error) {
    console.error('处理请求失败', error);
    return {
      code: 500,
      message: '处理请求失败: ' + error.message,
      data: null
    };
  }
};

/**
 * 设置集合权限
 * @param {Object} data 请求参数
 * @returns {Object} 响应结果
 */
async function setCollectionPermission(data) {
  try {
    const { collectionName, readAccess, writeAccess } = data;
    
    if (!collectionName) {
      return {
        code: 400,
        message: '缺少集合名称',
        data: null
      };
    }
    
    console.log(`尝试设置集合 ${collectionName} 的权限，读权限: ${readAccess}, 写权限: ${writeAccess}`);
    
    // 由于微信云开发没有直接的API来设置集合权限
    // 我们需要在微信开发者工具中手动设置集合权限
    // 这里只记录操作日志
    
    // 创建一条日志记录
    try {
      const db = cloud.database();
      await db.collection('operation_logs').add({
        data: {
          operation: 'setCollectionPermission',
          collectionName,
          readAccess,
          writeAccess,
          operator: cloud.getWXContext().OPENID,
          createTime: db.serverDate()
        }
      });
      
      console.log(`已记录设置集合 ${collectionName} 权限的操作`);
    } catch (logError) {
      console.error('记录操作日志失败:', logError);
    }
    
    // 如果是gallery集合，还需要提醒用户设置云存储权限
    let additionalInstructions = '';
    if (collectionName === 'gallery') {
      additionalInstructions = '\n\n同时，请确保云存储中的gallery目录权限设置为"所有用户可读，仅创建者可写"，步骤如下：\n' +
        '1. 在微信开发者工具中，打开云开发控制台\n' +
        '2. 点击左侧菜单"存储管理"\n' +
        '3. 找到"gallery"目录，点击右侧"权限设置"\n' +
        '4. 将权限设置为"所有用户可读，仅创建者可写"\n' +
        '5. 点击确定保存设置';
    }
    
    return {
      code: 200,
      message: '请在微信开发者工具中手动设置集合权限' + additionalInstructions,
      data: {
        collectionName,
        readAccess,
        writeAccess,
        instructions: '请在微信开发者工具的云开发控制台中，找到数据库 > ' + 
                      collectionName + ' 集合 > 权限设置，将权限设置为"所有用户可读，仅创建者可写"'
      }
    };
  } catch (error) {
    console.error('设置集合权限失败', error);
    return {
      code: 500,
      message: '设置集合权限失败: ' + error.message,
      data: null
    };
  }
}

/**
 * 获取指定配置项
 * @param {Object} data - 包含要获取的配置项名称
 * @returns {Object} 配置项数据
 */
async function getConfig(data) {
  try {
    const { configName } = data;
    
    if (!configName) {
      return {
        code: 400,
        message: '缺少配置名称',
        data: null
      };
    }
    
    // 从配置集合中获取配置
    const configResult = await cloud.database().collection('config')
      .where({ name: configName })
      .get();
    
    if (configResult.data.length === 0) {
      return {
        code: 404,
        message: '配置不存在',
        data: null
      };
    }
    
    return {
      code: 200,
      message: '获取配置成功',
      data: configResult.data[0].value
    };
  } catch (error) {
    console.error('获取配置失败', error);
    return {
      code: 500,
      message: '获取配置失败: ' + error.message,
      data: null
    };
  }
}

/**
 * 获取云存储配置
 */
async function getCloudStorageConfig() {
  return {
    code: 200,
    message: '获取云存储配置成功',
    data: config.CLOUD_STORAGE_CONFIG
  };
}

/**
 * 获取启动页配置
 */
async function getLaunchConfig() {
  return {
    code: 200,
    message: '获取启动页配置成功',
    data: config.LAUNCH_CONFIG
  };
}

/**
 * 获取文件命名规则
 */
async function getFileNamingRules(data) {
  const { fileType } = data || {};
  
  if (fileType && !config.FILE_NAMING[fileType]) {
    return {
      code: 404,
      message: '文件类型不存在',
      data: null
    };
  }
  
  const result = fileType ? config.FILE_NAMING[fileType] : config.FILE_NAMING;
  
  return {
    code: 200,
    message: '获取文件命名规则成功',
    data: result
  };
}

/**
 * 获取请求配置
 */
async function getRequestConfig() {
  return {
    code: 200,
    message: '获取请求配置成功',
    data: config.REQUEST_CONFIG
  };
}

/**
 * 获取视频配置
 */
async function getVideoConfig(data) {
  const { section } = data || {};
  
  if (section && !config.VIDEO_CONFIG[section]) {
    return {
      code: 404,
      message: '视频配置部分不存在',
      data: null
    };
  }
  
  const result = section ? config.VIDEO_CONFIG[section] : config.VIDEO_CONFIG;
  
  return {
    code: 200,
    message: '获取视频配置成功',
    data: result
  };
}

/**
 * 获取所有配置
 */
async function getAllConfig() {
  return {
    code: 200,
    message: '获取所有配置成功',
    data: {
      ENV_ID: config.ENV_ID,
      APP_ID: config.APP_ID,
      CLOUD_STORAGE_CONFIG: config.CLOUD_STORAGE_CONFIG,
      LAUNCH_CONFIG: config.LAUNCH_CONFIG,
      REQUEST_CONFIG: config.REQUEST_CONFIG,
      FILE_NAMING: config.FILE_NAMING,
      VIDEO_CONFIG: config.VIDEO_CONFIG
    }
  };
} 