<!--pages/staff/expense/expense.wxml-->
<view class="expense-container">
  <!-- 顶部标题栏 -->
  <view class="header">
    <view class="back-icon" bindtap="goBack">
      <text>←</text>
    </view>
    <view class="title">支出记录</view>
  </view>
  
  <!-- 可滚动的内容区域 -->
  <view class="page-content">
    <!-- 添加支出表单 -->
    <view class="expense-form">
      <view class="form-title">添加支出</view>
      
      <!-- 金额输入 -->
      <view class="form-item">
        <view class="form-label">支出金额</view>
        <view class="form-input-container">
          <text class="currency-symbol">¥</text>
          <input class="form-input" type="digit" value="{{amount}}" bindinput="onAmountInput" placeholder="请输入金额" />
        </view>
      </view>
      
      <!-- 备注输入 -->
      <view class="form-item">
        <view class="form-label">备注说明</view>
        <textarea class="form-textarea" value="{{remark}}" bindinput="onRemarkInput" placeholder="请输入备注信息" maxlength="100"></textarea>
      </view>
      
      <!-- 图片上传 -->
      <view class="form-item">
        <view class="form-label">上传凭证</view>
        <view class="image-uploader">
          <view class="image-list">
            <view class="image-item" wx:for="{{images}}" wx:key="index">
              <image src="{{item}}" mode="aspectFill" bindtap="previewImage" data-url="{{item}}" data-index="{{index}}"></image>
              <view class="delete-btn" bindtap="deleteImage" data-index="{{index}}">×</view>
            </view>
            <view class="upload-btn" bindtap="chooseImage" wx:if="{{images.length < 9}}">
              <text class="upload-icon">+</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 提交按钮 -->
      <button class="submit-btn" bindtap="submitExpense" disabled="{{!amount || amount <= 0}}">提交支出记录</button>
    </view>
    
    <!-- 历史记录 -->
    <view class="history-section">
      <view class="section-title">历史记录</view>
      
      <!-- 加载中 -->
      <view class="loading-container" wx:if="{{loading}}">
        <view class="loading-spinner"></view>
        <text class="loading-text">加载中...</text>
      </view>
      
      <!-- 记录列表 -->
      <view class="expense-list" wx:if="{{!loading && expenseList.length > 0}}">
        <view class="expense-item" wx:for="{{expenseList}}" wx:key="_id" bindtap="viewExpenseDetail" data-expense="{{item}}">
          <view class="expense-header">
            <text class="expense-time">{{item.createTime}}</text>
          </view>
          <view class="expense-body">
            <view class="expense-amount">¥{{item.amount}}</view>
            <view class="expense-remark">{{item.remark || '无备注'}}</view>
          </view>
          <view class="expense-images" wx:if="{{item.images && item.images.length > 0}}">
            <image class="expense-image-thumb" src="{{item.images[0]}}" mode="aspectFill" catchtap="previewImage" data-urls="{{item.images}}" data-current="{{item.images[0]}}"></image>
            <text class="image-count" wx:if="{{item.images.length > 1}}">+{{item.images.length - 1}}</text>
          </view>
        </view>
      </view>
      
      <!-- 分页 -->
      <view class="pagination" wx:if="{{!loading && expenseList.length > 0 && totalPages > 1}}">
        <view class="page-btn {{currentPage <= 1 ? 'disabled' : ''}}" bindtap="prevPage">上一页</view>
        <view class="page-info">{{currentPage}}/{{totalPages}}</view>
        <view class="page-btn {{currentPage >= totalPages ? 'disabled' : ''}}" bindtap="nextPage">下一页</view>
      </view>
      
      <!-- 空状态 -->
      <view class="empty-state" wx:if="{{!loading && expenseList.length === 0}}">
        <text class="empty-text">暂无支出记录</text>
      </view>
    </view>
  </view>
</view>

<!-- 支出详情弹窗 -->
<view class="expense-detail-panel {{showExpenseDetail ? 'show' : ''}}" wx:if="{{showExpenseDetail}}">
  <view class="panel-mask" bindtap="closeExpenseDetail"></view>
  <view class="panel-content">
    <view class="panel-header">
      <text class="panel-title">支出详情</text>
      <view class="panel-close" bindtap="closeExpenseDetail">×</view>
    </view>
    
    <view class="panel-body">
      <view class="detail-item">
        <text class="detail-label">金额：</text>
        <text class="detail-value">¥{{currentExpense.amount}}</text>
      </view>
      
      <view class="detail-item">
        <text class="detail-label">时间：</text>
        <text class="detail-value">{{currentExpense.createTime}}</text>
      </view>
      
      <view class="detail-item">
        <text class="detail-label">备注：</text>
        <text class="detail-value">{{currentExpense.remark || '无备注'}}</text>
      </view>
      
      <view class="detail-item" wx:if="{{currentExpense.images && currentExpense.images.length > 0}}">
        <text class="detail-label">凭证：</text>
        <view class="detail-images">
          <image 
            class="detail-image" 
            wx:for="{{currentExpense.images}}" 
            wx:key="index" 
            src="{{item}}" 
            mode="aspectFill"
            bindtap="previewImage"
            data-urls="{{currentExpense.images}}"
            data-current="{{item}}"
          ></image>
        </view>
      </view>
    </view>
  </view>
</view> 