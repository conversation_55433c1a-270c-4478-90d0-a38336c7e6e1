Component({
  properties: {
    visible: {
      type: Boolean,
      value: false
    },
    videoInfo: {
      type: Object,
      value: null
    }
  },
  
  data: {
    isEdit: false,
    isSubmitting: false,
    formData: {
      mainTitle: '',
      subTitle: '',
      description: '',
      sortOrder: 0,
      playCount: '',
      coverUrl: '',
      videoUrl: '',
      isVisible: true,
      detailImages: []
    },
    // 日志控制
    logConfig: {
      // 是否启用详细日志
      enableDetailedLogs: false,
      // 是否在生产环境中显示日志
      showLogsInProduction: false
    }
  },
  
  observers: {
    'visible': function(visible) {
      if (visible && this.properties.videoInfo) {
        const videoInfo = this.properties.videoInfo;
        this.setData({
          isEdit: true,
          formData: {
            _id: videoInfo._id || videoInfo.id,
            mainTitle: videoInfo.mainTitle || videoInfo.title || '',
            subTitle: videoInfo.subTitle || videoInfo.subtitle || '',
            description: videoInfo.description || '',
            sortOrder: videoInfo.sortOrder || videoInfo.order || 0,
            playCount: videoInfo.playCount || '',
            coverUrl: videoInfo.coverUrl || '',
            videoUrl: videoInfo.videoUrl || '',
            coverKey: videoInfo.coverKey || '',
            videoKey: videoInfo.videoKey || '',
            isVisible: videoInfo.isVisible !== undefined ? videoInfo.isVisible : true,
            detailImages: []
          }
        });
        
        // 检查是否有详情图片数据
        if (videoInfo.detailUrl) {
          this._log('编辑模式：发现详情图片');
          this.setData({
            'formData.detailImages': [{
              url: videoInfo.detailUrl,
              key: videoInfo.detailKey
            }]
          });
        }
        // 多张图片的情况，但只使用第一张
        else if (videoInfo.detailUrls && Array.isArray(videoInfo.detailUrls) && videoInfo.detailUrls.length > 0) {
          this._log('编辑模式：发现多张详情图片，使用第一张');
          
          const url = videoInfo.detailUrls[0];
          const key = (videoInfo.detailKeys && Array.isArray(videoInfo.detailKeys) && videoInfo.detailKeys.length > 0)
            ? videoInfo.detailKeys[0]
            : videoInfo.detailKey;
            
          this.setData({
            'formData.detailImages': [{
              url: url,
              key: key
            }]
          });
        }
        // 如果只有detailKey但没有图片URL，尝试从后端加载
        else if (videoInfo.detailKey) {
          this._log('编辑模式：尝试通过detailKey加载详情图片');
          this.loadDetailImage(videoInfo.detailKey);
        }
      } else if (visible) {
        this.resetForm();
      }
    }
  },
  
  methods: {
    // 日志控制函数
    _log(message, data) {
      // 检查是否在开发环境
      const isDevEnv = wx.getSystemInfoSync().platform === 'devtools';
      
      // 如果是生产环境且不允许显示日志，则直接返回
      if (!isDevEnv && !this.data.logConfig.showLogsInProduction) {
        return;
      }
      
      // 如果启用了详细日志或在开发环境中
      if (this.data.logConfig.enableDetailedLogs || isDevEnv) {
        if (data !== undefined) {
          console.log(message, data);
        } else {
          console.log(message);
        }
      }
    },
    
    // 错误日志函数
    _error(message, error) {
      // 错误日志总是显示在开发环境中
      const isDevEnv = wx.getSystemInfoSync().platform === 'devtools';
      
      if (isDevEnv || this.data.logConfig.showLogsInProduction) {
        if (error !== undefined) {
          console.error(message, error);
        } else {
          console.error(message);
        }
      }
    },
    
    // 重置表单
    resetForm() {
      this.setData({
        isEdit: false,
        formData: {
          mainTitle: '',
          subTitle: '',
          description: '',
          sortOrder: 0,
          playCount: '',
          coverUrl: '',
          videoUrl: '',
          isVisible: true,
          detailImages: []
        }
      });
    },
    
    // 隐藏模态框
    hideModal() {
      this.triggerEvent('close');
    },
    
    // 标题输入
    onTitleInput(e) {
      this.setData({
        'formData.mainTitle': e.detail.value
      });
    },
    
    // 副标题输入
    onSubtitleInput(e) {
      this.setData({
        'formData.subTitle': e.detail.value
      });
    },
    
    // 描述输入
    onDescriptionInput(e) {
      this.setData({
        'formData.description': e.detail.value
      });
    },
    
    // 排序输入
    onOrderInput(e) {
      this.setData({
        'formData.sortOrder': parseInt(e.detail.value) || 0
      });
    },
    
    // 播放量输入
    onPlayCountInput(e) {
      // 允许输入为空，但保存时会转为数字
      const value = e.detail.value === '' ? '' : (parseInt(e.detail.value) || 0);
      this.setData({
        'formData.playCount': value
      });
    },
    
    // 切换可见性
    toggleVisibility() {
      this.setData({
        'formData.isVisible': !this.data.formData.isVisible
      });
    },
    
    // 上传封面
    uploadCover() {
      wx.chooseMedia({
        count: 1,
        mediaType: ['image'],
        sizeType: ['compressed', 'original'], // 同时支持压缩和原图
        sourceType: ['album', 'camera'],
        success: (res) => {
          const tempFilePath = res.tempFiles[0].tempFilePath;
          const fileSize = res.tempFiles[0].size;
          
          // 获取文件名，如果没有则生成一个
          let fileName = res.tempFiles[0].name;
          if (!fileName) {
            // 从路径中提取扩展名
            const extMatch = tempFilePath.match(/\.([^.]+)$/);
            const ext = extMatch ? extMatch[1].toLowerCase() : 'jpg';
            
            // 生成一个唯一的文件名
            const timestamp = Date.now();
            const randomStr = Math.random().toString(36).substring(2, 8);
            fileName = `cover_${timestamp}_${randomStr}.${ext}`;
          }
          
          this._log('准备上传封面图:', {fileName, fileSize: (fileSize/1024/1024).toFixed(2) + 'MB'});
          
          // 显示文件大小提示
          const fileSizeMB = (fileSize / (1024 * 1024)).toFixed(2);
          if (fileSizeMB > 5) {
            wx.showToast({
              title: `文件较大(${fileSizeMB}MB)，上传可能较慢`,
              icon: 'none',
              duration: 2000
            });
          }
          
          // 使用临时云存储路径方式上传
          this._processImageUploadViaCloudStorage(tempFilePath, fileName, 'cover');
        },
        fail: (err) => {
          this._error('选择封面图失败', err);
          wx.showToast({
            title: '选择图片失败',
            icon: 'none'
          });
        }
      });
    },
    
    // 移除封面
    removeCover() {
      this.setData({
        'formData.coverUrl': '',
        'formData.coverKey': ''
      });
    },
    
    // 上传视频
    uploadVideo() {
      wx.chooseMedia({
        count: 1,
        mediaType: ['video'],
        sourceType: ['album', 'camera'],
        camera: 'back',
        success: (res) => {
          const tempFilePath = res.tempFiles[0].tempFilePath;
          const fileName = res.tempFiles[0].name || `video_${Date.now()}.${tempFilePath.match(/\.([^.]+)$/)[1] || 'mp4'}`;
          const fileSize = res.tempFiles[0].size;
          
          // 获取视频信息 - 添加try-catch并优化日志输出
          try {
            wx.getVideoInfo({
              src: tempFilePath,
              success: (videoInfo) => {
                // 开发环境下才输出详细日志
                if (wx.getSystemInfoSync().platform !== 'devtools') {
                  console.log('视频信息:', videoInfo);
                }
                
                // 检查文件大小，自动推荐上传方式
                const sizeInMB = (fileSize/1024/1024).toFixed(2);
                let recommendedMethod = '';
                let message = `视频大小: ${sizeInMB}MB\n时长: ${(videoInfo.duration/60).toFixed(2)}分钟\n分辨率: ${videoInfo.width}x${videoInfo.height}\n`;
                
                // 根据文件大小推荐上传方式
                if (fileSize > 15 * 1024 * 1024) {
                  recommendedMethod = '建议使用直接上传';
                  message += '文件较大，建议使用直接上传或压缩\n请选择上传方式:';
                } else if (fileSize > 5 * 1024 * 1024) {
                  recommendedMethod = '可选普通或直接上传';
                  message += '请选择上传方式:';
                } else {
                  recommendedMethod = '可使用普通上传';
                  message += '请选择上传方式:';
                }
                
                // 显示视频信息和上传选项
                wx.showModal({
                  title: '视频上传选项 ' + recommendedMethod,
                  content: message,
                  cancelText: '普通上传',
                  confirmText: '高级上传',
                  success: (modalRes) => {
                    if (modalRes.confirm) {
                      // 显示高级上传选项
                      wx.showActionSheet({
                        itemList: [
                          '直接上传 ⭐推荐', 
                          '压缩后上传 (高质量)', 
                          '压缩后上传 (中等质量)', 
                          '压缩后上传 (低质量)'
                        ],
                        success: (actionRes) => {
                          switch (actionRes.tapIndex) {
                            case 0: // 直接上传
                              this._processDirectUpload(tempFilePath, fileName);
                              break;
                            case 1: // 高质量压缩
                              this._compressAndUploadVideo(tempFilePath, fileName, 'high');
                              break;
                            case 2: // 中等质量压缩
                              this._compressAndUploadVideo(tempFilePath, fileName, 'medium');
                              break;
                            case 3: // 低质量压缩
                              this._compressAndUploadVideo(tempFilePath, fileName, 'low');
                              break;
                          }
                        }
                      });
                    } else {
                      // 常规上传方式
                      // 检查文件大小，微信云函数单次调用限制约为20MB，考虑Base64编码增加约33%体积
                      const maxSize = 15 * 1024 * 1024; // 15MB
                      if (fileSize > maxSize) {
                        wx.showModal({
                          title: '文件过大',
                          content: '视频文件超过15MB，请使用"高级上传"选项进行直接上传或压缩后上传',
                          cancelText: '取消',
                          confirmText: '使用直接上传',
                          success: (result) => {
                            if (result.confirm) {
                              this._processDirectUpload(tempFilePath, fileName);
                            }
                          }
                        });
                      } else {
                        this._processVideoUpload(tempFilePath, fileName);
                      }
                    }
                  }
                });
              },
              fail: (err) => {
                // 在开发环境中才输出错误日志，减少不必要的日志
                if (wx.getSystemInfoSync().platform === 'devtools') {
                  // 如果是ffmpeg相关错误，不输出详细日志，这只是开发环境的限制
                  if (err.errMsg && err.errMsg.includes('ffmpeg')) {
                    console.log('开发环境中视频预览受限，这不影响实际功能');
                  } else {
                    console.error('获取视频信息失败', err);
                  }
                }
                
                // 回退到原来的上传方式
                const maxSize = 15 * 1024 * 1024; // 15MB
                if (fileSize > maxSize) {
                  wx.showModal({
                    title: '文件较大',
                    content: `视频文件大小为${(fileSize/1024/1024).toFixed(2)}MB，建议使用直接上传，是否继续？`,
                    cancelText: '普通上传',
                    confirmText: '直接上传',
                    success: (modalRes) => {
                      if (modalRes.confirm) {
                        this._processDirectUpload(tempFilePath, fileName);
                      } else {
                        // 用户仍然选择常规上传
                        wx.showModal({
                          title: '确认上传',
                          content: '大文件使用普通上传可能会失败，是否继续？',
                          success: (confirmRes) => {
                            if (confirmRes.confirm) {
                              this._processVideoUpload(tempFilePath, fileName);
                            }
                          }
                        });
                      }
                    }
                  });
                } else {
                  this._processVideoUpload(tempFilePath, fileName);
                }
              }
            });
          } catch (e) {
            // 捕获可能的异常，确保即使getVideoInfo完全失败也能继续上传流程
            console.log('视频信息获取异常，继续上传流程');
            this._processVideoUpload(tempFilePath, fileName);
          }
        },
        fail: (err) => {
          console.error('选择视频失败', err);
          wx.showToast({
            title: '选择视频失败',
            icon: 'none'
          });
        }
      });
    },
    
    // 压缩并上传视频
    _compressAndUploadVideo(tempFilePath, fileName, compressionLevel) {
          wx.showLoading({
        title: '正在压缩视频...',
            mask: true
          });
          
      this._log('开始压缩视频', { fileName, compressionLevel });
      
      // 压缩设置
      const compressionSettings = {
        high: { quality: 'high' },
        medium: { quality: 'medium' },
        low: { quality: 'low' }
      };
      
      // 使用微信API压缩视频
      wx.compressVideo({
        src: tempFilePath,
        quality: compressionSettings[compressionLevel].quality,
        success: (compressRes) => {
          this._log('视频压缩成功', compressRes);
          const compressedTempFilePath = compressRes.tempFilePath;
          
          // 更新UI提示
          wx.showLoading({
            title: '正在上传压缩视频...',
            mask: true
          });
          
          // 上传压缩后的视频到云存储
          const cloudPath = `temp_videos/compressed_${Date.now()}_${fileName}`;
          
          wx.cloud.uploadFile({
            cloudPath: cloudPath,
            filePath: compressedTempFilePath,
            success: res => {
              const fileID = res.fileID;
              this._log('压缩视频上传到云存储成功', { fileID });
              
              // 调用云函数处理云存储中的压缩视频
              wx.cloud.callFunction({
                name: 'videoManager',
                data: {
                  type: 'admin',
                  action: 'uploadVideo',
                  data: {
                    fileID: fileID,
                    fileName: `compressed_${fileName}`,
                    useCloudStorage: true,
                    compressionLevel: compressionLevel
                  }
                },
                success: (res) => {
                  wx.hideLoading();
                  
                  if (res.result && res.result.code === 200) {
                    const videoData = res.result.data || {};
                    
                    this._log('压缩视频上传成功', { key: videoData.key });
                    
                    this.setData({
                      'formData.videoUrl': videoData.tempUrl || '',
                      'formData.videoKey': videoData.key || ''
                    });
                    
                    wx.showToast({
                      title: '压缩视频上传成功',
                      icon: 'success'
                    });
                  } else {
                    this._error('压缩视频上传失败', res.result);
                    
                    wx.showModal({
                      title: '上传失败',
                      content: res.result?.message || '压缩视频上传失败，请检查网络或视频格式',
                      showCancel: false
                    });
                  }
                },
                fail: (err) => {
                  wx.hideLoading();
                  this._error('压缩视频上传失败', err);
                  
                  // 尝试删除已上传的临时文件
                  wx.cloud.deleteFile({
                    fileList: [fileID],
                    success: res => {
                      this._log('清理临时文件成功');
                    }
                  });
                  
                  wx.showModal({
                    title: '上传失败',
                    content: '压缩视频上传失败，请重试',
                    showCancel: false
                  });
                }
              });
            },
            fail: err => {
              wx.hideLoading();
              this._error('上传到云存储失败', err);
              
              wx.showModal({
                title: '上传失败',
                content: '上传到云存储失败，请重试',
                showCancel: false
          });
        }
      });
    },
        fail: (err) => {
          wx.hideLoading();
          this._error('视频压缩失败', err);
          
          wx.showModal({
            title: '压缩失败',
            content: '视频压缩失败，请尝试使用直接上传或减小视频文件大小',
            showCancel: false
          });
        }
      });
    },
    
    // 处理直接上传
    _processDirectUpload(tempFilePath, fileName) {
          wx.showLoading({
            title: '上传中...',
            mask: true
          });
          
      this._log('开始直接上传', { fileName });
      
      // 首先将视频文件上传到云存储
      const cloudPath = `temp_videos/${Date.now()}_${fileName}`;
      
      wx.cloud.uploadFile({
        cloudPath: cloudPath,
            filePath: tempFilePath,
        success: res => {
          const fileID = res.fileID;
          this._log('视频文件上传到云存储成功', { fileID });
          
          // 调用云函数处理云存储中的视频
              wx.cloud.callFunction({
                name: 'videoManager',
                data: {
                  type: 'admin',
              action: 'directUploadVideo',
                  data: {
                fileID: fileID,
                    fileName: fileName
                  }
                },
            timeout: 60000, // 设置60秒超时，给大文件足够的处理时间
                success: (res) => {
                  wx.hideLoading();
                  
                  if (res.result && res.result.code === 200) {
                    const videoData = res.result.data || {};
                
                this._log('直接上传成功', { key: videoData.key });
                    
                    this.setData({
                      'formData.videoUrl': videoData.tempUrl || '',
                      'formData.videoKey': videoData.key || ''
                    });
                    
                    wx.showToast({
                      title: '视频上传成功',
                      icon: 'success'
                    });
                
                // 添加振动反馈
                if (wx.vibrateShort) {
                  wx.vibrateShort({
                    type: 'medium'
                  });
                }
                  } else {
                this._error('直接上传失败', res.result);
                
                wx.showModal({
                  title: '上传失败',
                  content: res.result?.message || '视频上传失败，请检查网络或视频格式',
                  showCancel: false
                    });
                  }
                },
                fail: (err) => {
                  wx.hideLoading();
              this._error('直接上传失败', err);
              
              // 尝试删除已上传的临时文件
              wx.cloud.deleteFile({
                fileList: [fileID],
                success: res => {
                  this._log('清理临时文件成功');
                }
              });
              
              // 分析错误原因
              let errorMsg = '视频上传失败';
              if (err.errMsg) {
                if (err.errMsg.includes('exceed')) {
                  errorMsg = '视频文件过大，请压缩后再上传';
                } else if (err.errMsg.includes('timeout')) {
                  errorMsg = '处理超时，请尝试压缩后再上传';
                } else {
                  errorMsg = '上传失败: ' + err.errMsg;
                }
              }
              
              wx.showModal({
                title: '上传失败',
                content: errorMsg,
                confirmText: '使用压缩上传',
                success: (modalRes) => {
                  if (modalRes.confirm) {
                    // 自动切换到压缩上传
                    this._compressAndUploadVideo(tempFilePath, fileName, 'medium');
                  }
            }
          });
        }
      });
    },
        fail: err => {
          wx.hideLoading();
          this._error('上传到云存储失败', err);
          
          wx.showModal({
            title: '上传失败',
            content: '上传到云存储失败，请重试',
            showCancel: false
          });
        }
      });
    },
    
    // 处理普通视频上传（保留原有功能）
    _processVideoUpload(tempFilePath, fileName) {
          wx.showLoading({
            title: '上传中...',
            mask: true
          });
          
      this._log('开始普通上传', { fileName });
      
      // 首先将视频文件上传到云存储
      const cloudPath = `temp_videos/${Date.now()}_${fileName}`;
      
      wx.cloud.uploadFile({
        cloudPath: cloudPath,
            filePath: tempFilePath,
        success: res => {
          const fileID = res.fileID;
          this._log('视频文件上传到云存储成功', { fileID });
          
          // 调用云函数处理云存储中的视频
              wx.cloud.callFunction({
                name: 'videoManager',
                data: {
                  type: 'admin',
              action: 'uploadVideo',
                  data: {
                fileID: fileID,
                    fileName: fileName,
                useCloudStorage: true // 标记使用了云存储
                  }
                },
            timeout: 30000, // 设置30秒超时
                success: (res) => {
                  wx.hideLoading();
                  
                  if (res.result && res.result.code === 200) {
                const videoData = res.result.data || {};
                    
                this._log('普通上传成功', { key: videoData.key });
                
                    this.setData({
                  'formData.videoUrl': videoData.tempUrl || '',
                  'formData.videoKey': videoData.key || ''
                    });
                    
                    wx.showToast({
                  title: '视频上传成功',
                      icon: 'success'
                    });
                
                // 添加振动反馈
                if (wx.vibrateShort) {
                  wx.vibrateShort({
                    type: 'medium'
                  });
                }
                  } else {
                this._error('普通上传失败', res.result);
                
                wx.showModal({
                  title: '上传失败',
                  content: res.result?.message || '视频上传失败，请检查网络或视频格式',
                  showCancel: false
                    });
                  }
                },
                fail: (err) => {
                  wx.hideLoading();
              this._error('普通上传失败', err);
              
              // 尝试删除已上传的临时文件
              wx.cloud.deleteFile({
                fileList: [fileID],
                success: res => {
                  this._log('清理临时文件成功');
                }
              });
              
              // 分析错误原因
              let errorMsg = '视频上传失败';
              if (err.errMsg) {
                if (err.errMsg.includes('exceed')) {
                  errorMsg = '视频文件过大，请压缩后再上传';
                } else if (err.errMsg.includes('timeout')) {
                  errorMsg = '处理超时，请使用直接上传功能处理大视频';
                } else if (err.errMsg.includes('timed out')) {
                  errorMsg = '云函数处理超时，请使用"直接上传"选项';
                } else {
                  errorMsg = '上传失败: ' + err.errMsg;
                }
              }
              
              wx.showModal({
                title: '上传失败',
                content: errorMsg,
                confirmText: '使用直接上传',
                success: (modalRes) => {
                  if (modalRes.confirm) {
                    // 自动切换到直接上传
                    this._processDirectUpload(tempFilePath, fileName);
                  }
                }
                  });
                }
              });
            },
        fail: err => {
              wx.hideLoading();
          this._error('上传到云存储失败', err);
          
          wx.showModal({
            title: '上传失败',
            content: '上传到云存储失败，请重试',
            showCancel: false
          });
        }
      });
    },
    
    // 移除视频
    removeVideo() {
      this.setData({
        'formData.videoUrl': '',
        'formData.videoKey': ''
      });
    },
    
    // 上传详情图片
    uploadDetailImage() {
      // 修改为只允许上传一张详情图片
      wx.chooseMedia({
        count: 1,
        mediaType: ['image'],
        sizeType: ['compressed', 'original'], // 同时支持压缩和原图
        sourceType: ['album', 'camera'],
        success: (res) => {
          const tempFilePath = res.tempFiles[0].tempFilePath;
          const fileSize = res.tempFiles[0].size;
          
          // 生成更具唯一性的文件名
          const timestamp = Date.now();
          const randomStr = Math.random().toString(36).substring(2, 8);
          const videoId = this.data.formData._id || 'new';
          
          // 从路径中提取扩展名，如果没有则默认为jpg
          const extMatch = tempFilePath.match(/\.([^.]+)$/);
          const fileExt = extMatch ? extMatch[1].toLowerCase() : 'jpg';
          
          // 格式: detail_视频ID_时间戳_随机字符串.扩展名
          const fileName = `detail_${videoId}_${timestamp}_${randomStr}.${fileExt}`;
          
          this._log('生成唯一文件名:', fileName);
          
          // 显示文件大小提示
          const fileSizeMB = (fileSize / (1024 * 1024)).toFixed(2);
          if (fileSizeMB > 5) {
              wx.showToast({
              title: `文件较大(${fileSizeMB}MB)，上传可能较慢`,
              icon: 'none',
              duration: 2000
            });
          }
          
          // 使用临时云存储路径方式上传
          this._processImageUploadViaCloudStorage(tempFilePath, fileName, 'detail');
        },
        fail: (err) => {
          this._error('选择详情图片失败', err);
          wx.showToast({
            title: '选择图片失败',
            icon: 'none'
          });
        }
      });
    },
    
    // 移除详情图片
    removeDetailImage(e) {
      const index = e.currentTarget.dataset.index;
      
      // 清空详情图片数组
      this.setData({
        'formData.detailImages': [],
        'formData.detailKey': ''
      });
      
      wx.showToast({
        title: '已移除详情图片',
        icon: 'success'
      });
    },
    
    // 加载详情图片（编辑模式下使用）
    loadDetailImage(detailKey) {
      if (!detailKey) return;
      
      // 检查是否已有缓存的错误记录，避免重复请求已知不存在的文件
      const failedKeysStr = wx.getStorageSync('failed_detail_keys') || '[]';
      try {
        const failedKeys = JSON.parse(failedKeysStr);
        if (Array.isArray(failedKeys) && failedKeys.includes(detailKey)) {
          this._log('跳过已知不存在的详情图片:', detailKey);
          return;
        }
      } catch (e) {
        // 解析错误，忽略
      }
      
      wx.showLoading({
        title: '加载详情图片...',
        mask: true
      });
      
      // 调用云函数获取详情图片
      wx.cloud.callFunction({
        name: 'videoManager',
        data: {
          type: 'admin',
          action: 'getVideoDetail',
          data: {
            id: this.data.formData._id
          }
        },
        success: (res) => {
          wx.hideLoading();
          
          if (res.result && res.result.code === 200 && res.result.data) {
            const videoDetail = res.result.data;
            let detailImage = null;
            
            // 优先使用detailUrl，兼容旧版本
            if (videoDetail.detailUrl) {
              this._log('找到详情图片');
              
              // 验证URL是否有效
              this._checkImageExists(videoDetail.detailUrl, (exists) => {
                if (exists) {
              detailImage = {
                url: videoDetail.detailUrl,
                key: videoDetail.detailKey
              };
                  
                  this.setData({
                    'formData.detailImages': [detailImage],
                    'formData.detailKey': detailImage.key
                  });
                } else {
                  this._log('详情图片URL无效，记录失败的key');
                  this._recordFailedDetailKey(videoDetail.detailKey);
                }
              });
            } 
            // 处理多张详情图片的情况，但只使用第一张
            else if (videoDetail.detailUrls && Array.isArray(videoDetail.detailUrls) && videoDetail.detailUrls.length > 0) {
              this._log('找到多张详情图片，使用第一张');
              
              const url = videoDetail.detailUrls[0];
              const key = (videoDetail.detailKeys && Array.isArray(videoDetail.detailKeys) && videoDetail.detailKeys.length > 0) 
                ? videoDetail.detailKeys[0] 
                : videoDetail.detailKey;
                
              // 验证URL是否有效
              this._checkImageExists(url, (exists) => {
                if (exists) {
              detailImage = { url, key };
            
              this.setData({
                'formData.detailImages': [detailImage],
                'formData.detailKey': detailImage.key
              });
                } else {
                  this._log('详情图片URL无效，记录失败的key');
                  this._recordFailedDetailKey(key);
                }
              });
            } else {
              this._log('未找到详情图片');
            }
          }
        },
        fail: (err) => {
          this._error('加载详情图片失败', err);
          wx.hideLoading();
          
          // 记录失败的key
          this._recordFailedDetailKey(detailKey);
        }
      });
    },
    
    // 检查图片URL是否存在
    _checkImageExists(url, callback) {
      if (!url) {
        callback(false);
        return;
      }
      
      // 使用HEAD请求检查资源是否存在
      wx.request({
        url: url,
        method: 'HEAD',
        success: (res) => {
          // 状态码2xx表示资源存在
          callback(res.statusCode >= 200 && res.statusCode < 300);
        },
        fail: () => {
          callback(false);
        }
      });
    },
    
    // 记录失败的detailKey，避免重复请求
    _recordFailedDetailKey(key) {
      if (!key) return;
      
      try {
        const failedKeysStr = wx.getStorageSync('failed_detail_keys') || '[]';
        let failedKeys = [];
        
        try {
          failedKeys = JSON.parse(failedKeysStr);
          if (!Array.isArray(failedKeys)) failedKeys = [];
        } catch (e) {
          failedKeys = [];
        }
        
        if (!failedKeys.includes(key)) {
          failedKeys.push(key);
          wx.setStorageSync('failed_detail_keys', JSON.stringify(failedKeys));
        }
      } catch (e) {
        this._error('记录失败的detailKey出错', e);
      }
    },
    
    // 提交表单
    submitForm() {
      const { mainTitle, subTitle, sortOrder, playCount, coverUrl, videoUrl, coverKey, videoKey, isVisible, detailKey } = this.data.formData;
      const detailImages = this.data.formData.detailImages || [];
      
      // 表单验证
      if (!mainTitle) {
        wx.showToast({
          title: '请输入视频标题',
          icon: 'none'
        });
        return;
      }
      
      if (!subTitle) {
        wx.showToast({
          title: '请输入视频副标题',
          icon: 'none'
        });
        return;
      }
      
      if (!coverUrl || !coverKey) {
        wx.showToast({
          title: '请上传视频封面',
          icon: 'none'
        });
        return;
      }
      
      if (!videoUrl || !videoKey) {
        wx.showToast({
          title: '请上传视频文件',
          icon: 'none'
        });
        return;
      }
      
      // 开始提交
      this.setData({ isSubmitting: true });
      
      // 调用云函数保存数据
      const action = this.data.isEdit ? 'updateVideo' : 'addVideo';
      
      // 准备提交的数据
      const submitData = {
        mainTitle: mainTitle,
        subTitle: subTitle,
        playCount: parseInt(playCount) || 0,
        sortOrder: sortOrder,
        coverKey: coverKey,
        videoKey: videoKey,
        isVisible: isVisible
      };
      
      // 处理详情图片
      if (detailImages && detailImages.length > 0) {
        // 只使用第一张图片
        const detailImage = detailImages[0];
        
        // 保存图片的key作为detailKey
        submitData.detailKey = detailImage.key;
        
        // 保存图片的URL作为detailUrl
        submitData.detailUrl = detailImage.url;
        
        // 兼容多图片格式，只添加一张图片
        submitData.detailKeys = [detailImage.key];
        submitData.detailUrls = [detailImage.url];
        
        console.log('提交详情图片数据:', {
          detailUrl: submitData.detailUrl,
          detailKey: submitData.detailKey
        });
      }
      
      // 如果是编辑模式，添加ID
      if (this.data.isEdit && this.data.formData._id) {
        submitData.id = this.data.formData._id;
      }
      
      // 调用云函数
      wx.cloud.callFunction({
        name: 'videoManager',
        data: {
          type: 'admin',
          action: action,
          data: submitData
        },
        success: res => {
          console.log('提交视频数据成功', res);
          
          if (res.result && res.result.code === 200) {
            wx.showToast({
              title: this.data.isEdit ? '更新成功' : '添加成功',
              icon: 'success'
            });
            
            // 触发成功事件
            this.triggerEvent('success', {
              action,
              data: submitData
            });
            
            // 隐藏模态框
            this.hideModal();
          } else {
            wx.showToast({
              title: res.result?.message || '提交失败',
              icon: 'none'
            });
          }
        },
        fail: err => {
          console.error('提交视频数据失败', err);
          wx.showToast({
            title: '提交失败，请重试',
            icon: 'none'
          });
        },
        complete: () => {
          this.setData({ isSubmitting: false });
        }
      });
    },
    
    // 使用临时云存储路径方式上传图片
    _processImageUploadViaCloudStorage(tempFilePath, fileName, type) {
      wx.showLoading({
        title: '准备上传...',
        mask: true
      });
      
      // 先上传到云存储临时路径
      const cloudPath = `temp/${type}/${fileName}`;
      
      this._log('开始上传到临时云存储:', cloudPath);
      
      wx.cloud.uploadFile({
        cloudPath: cloudPath,
        filePath: tempFilePath,
        success: res => {
          this._log('上传到临时云存储成功', res);
          const fileID = res.fileID;
          
          wx.showLoading({
            title: '处理图片...',
            mask: true
          });
          
          // 调用云函数处理临时文件
          wx.cloud.callFunction({
            name: 'videoManager',
            data: {
              type: 'admin',
              action: type === 'cover' ? 'uploadCoverViaCloudStorage' : 'uploadDetailViaCloudStorage',
              data: {
                fileID: fileID,
                fileName: fileName,
                videoId: type === 'detail' ? (this.data.formData._id || '') : '' // 详情图需要传递视频ID
              }
            },
            success: res => {
              wx.hideLoading();
              
              if (res.result && res.result.code === 200) {
                const imageData = res.result.data || {};
                
                if (type === 'cover') {
                  this.setData({
                    'formData.coverUrl': imageData.tempUrl || '',
                    'formData.coverKey': imageData.key || ''
                  });
                } else {
                  // 详情图
                  this.setData({
                    'formData.detailImages': [{
                      url: imageData.tempUrl,
                      key: imageData.key
                    }],
                    'formData.detailKey': imageData.key
                  });
                }
                
                wx.showToast({
                  title: `${type === 'cover' ? '封面' : '详情'}图片上传成功`,
                  icon: 'success'
                });
                
                // 删除临时文件
                wx.cloud.deleteFile({
                  fileList: [fileID],
                  success: res => {
                    this._log('临时文件删除成功', res);
                  },
                  fail: err => {
                    this._error('临时文件删除失败', err);
                  }
                });
              } else {
                wx.showModal({
                  title: '上传失败',
                  content: res.result?.message || `${type === 'cover' ? '封面' : '详情'}图片上传失败，请检查网络或图片格式`,
                  showCancel: false
                });
                
                // 删除临时文件
                wx.cloud.deleteFile({
                  fileList: [fileID],
                  fail: err => {
                    this._error('临时文件删除失败', err);
                  }
                });
              }
            },
            fail: err => {
              wx.hideLoading();
              this._error(`${type}图片处理失败`, err);
              
              // 分析错误原因
              let errorMsg = `${type === 'cover' ? '封面' : '详情'}图片上传失败`;
              if (err.errMsg) {
                if (err.errMsg.includes('exceed')) {
                  errorMsg = '图片文件过大，请压缩后再上传';
                } else if (err.errMsg.includes('timeout')) {
                  errorMsg = '上传超时，请检查网络或压缩图片后重试';
                }
              }
              
              wx.showModal({
                title: '上传失败',
                content: errorMsg,
                showCancel: false
              });
              
              // 删除临时文件
              wx.cloud.deleteFile({
                fileList: [fileID],
                fail: deleteErr => {
                  this._error('临时文件删除失败', deleteErr);
                }
              });
            }
          });
        },
        fail: err => {
          wx.hideLoading();
          this._error('上传到临时云存储失败', err);
          
          wx.showModal({
            title: '上传失败',
            content: '上传图片文件失败，请检查网络或重试',
            showCancel: false
          });
        }
      });
    }
  }
}); 