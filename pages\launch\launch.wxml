<!-- 
  启动页
  显示欢迎动画，并预加载主要资源
-->
<view class="launch-fullscreen-overlay" bindtap="onTapToSkip" catchtouchmove="catchTouchMove" style="background-color: #f5f5f7;">
  <view class="launch-container {{isFadingOut ? 'fade-out' : ''}}" catchtouchmove="catchTouchMove">
    <!-- 第一张图片 -->
    <view class="{{firstShimmerClass}}">
      <image 
        class="launch-image first-image {{isFirstImageLoaded ? 'loaded' : ''}}"
        src="{{firstImageUrl}}"
        mode="aspectFill"
        bindload="onFirstImageLoad"
        binderror="onImageLoadError"
        catchtouchmove="catchTouchMove"
        data-type="第一张"
        data-src="{{firstImageUrl}}"
      />
    </view>
    
    <!-- 第二张图片 -->
    <view class="{{secondShimmerClass}}">
      <image 
        class="launch-image second-image {{showSecondImage ? 'animate-second' : ''}}"
        src="{{secondImageUrl}}"
        mode="aspectFill"
        bindload="onSecondImageLoad"
        binderror="onImageLoadError"
        catchtouchmove="catchTouchMove"
        data-type="第二张"
        data-src="{{secondImageUrl}}"
      />
    </view>
  </view>
</view> 