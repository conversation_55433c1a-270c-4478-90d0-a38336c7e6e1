<!--pages/admin/gallery/gallery.wxml-->
<wxs module="timeFormat">
  function formatDate(dateObj) {
    if (!dateObj) return '';
    
    var date;
    if (typeof dateObj === 'string') {
      date = getDate(dateObj);
    } else if (dateObj['$date']) {
      date = getDate(dateObj['$date']);
    } else {
      date = dateObj;
    }
    
    var year = date.getFullYear();
    var month = date.getMonth() + 1;
    var day = date.getDate();
    var hour = date.getHours();
    var minute = date.getMinutes();
    
    month = month < 10 ? '0' + month : month;
    day = day < 10 ? '0' + day : day;
    hour = hour < 10 ? '0' + hour : hour;
    minute = minute < 10 ? '0' + minute : minute;
    
    return year + '-' + month + '-' + day + ' ' + hour + ':' + minute;
  }
  
  module.exports = {
    formatDate: formatDate
  };
</wxs>

<view class="page-container">
  <view class="page-header">
    <view class="back-icon" bindtap="navigateBack">
      <text class="iconfont icon-back">←</text>
    </view>
    <view class="header-title">画廊管理后台</view>
    <view class="header-actions">
    </view>
  </view>
  
  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{isLoading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>
  
  <!-- 内容区域 -->
  <view class="content-admin-container" wx:else>
    <!-- 画廊列表 -->
    <view class="gallery-list" wx:if="{{galleryList.length > 0}}">
      <view class="gallery-item" wx:for="{{galleryList}}" wx:key="_id">
        <!-- 画廊封面 -->
        <image class="gallery-cover" src="{{item.coverUrl}}" mode="aspectFill" bindtap="viewGalleryDetail" data-id="{{item._id}}"></image>
        
        <!-- 右侧内容区域 -->
        <view class="right-content">
          <!-- 标题行 -->
          <view class="title-row">
            <view class="gallery-title" bindtap="viewGalleryDetail" data-id="{{item._id}}">{{item.mainTitle}}</view>
          </view>
          
          <!-- 副标题 -->
          <view class="gallery-subtitle">{{item.subTitle}}</view>
          
          <!-- 时间信息 - 新增 -->
          <view class="time-info" wx:if="{{item.visibilityChangeTime || item.updateTime || item.createTime}}">
            <block wx:if="{{item.visibilityChangeTime}}">
              <text class="time-label">变更:</text>
              <text class="time-value">{{item.visibilityChangeTimeStr || timeFormat.formatDate(item.visibilityChangeTime)}}</text>
            </block>
            <block wx:elif="{{item.updateTime}}">
              <text class="time-label">更新:</text>
              <text class="time-value">{{item.updateTimeStr || timeFormat.formatDate(item.updateTime)}}</text>
            </block>
            <block wx:elif="{{item.createTime}}">
              <text class="time-label">创建:</text>
              <text class="time-value">{{item.createTimeStr || timeFormat.formatDate(item.createTime)}}</text>
            </block>
          </view>
          
          <!-- 状态容器 -->
          <view class="status-container">
            <view class="gallery-visibility {{item.isVisible ? 'visible' : 'hidden'}}">
              <view class="status-indicator"></view>
              <text class="status-text">{{item.isVisible ? '已显示' : '已隐藏'}}</text>
            </view>
          </view>
          
          <!-- 操作按钮区域 -->
          <view class="gallery-actions">
            <view class="action-button visibility" catchtap="toggleGalleryVisibility" data-id="{{item._id}}" data-visible="{{item.isVisible}}">
              <text>{{item.isVisible ? '隐藏' : '显示'}}</text>
            </view>
            <view class="action-button edit" catchtap="editGallery" data-id="{{item._id}}">
              <text>编辑</text>
            </view>
            <view class="action-button delete" catchtap="deleteGallery" data-id="{{item._id}}">
              <text>删除</text>
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 空状态 -->
    <view class="empty-state" wx:else>
      <text class="empty-text">暂无画廊内容</text>
      <text class="empty-subtext">点击下方按钮添加新画廊</text>
    </view>
  </view>
  
  <!-- 添加按钮 -->
  <view class="add-button" bindtap="addNewGallery">
    <text class="add-icon">+</text>
  </view>
  
  <!-- 画廊详情模态框 -->
  <gallery-detail-modal id="galleryDetailModal"></gallery-detail-modal>
  
  <!-- 画廊上传/编辑模态框 -->
  <gallery-upload 
    visible="{{uploadModalVisible}}" 
    galleryInfo="{{currentGalleryInfo}}" 
    editMode="{{currentGalleryInfo !== null}}"
    bind:close="closeUploadModal"
    bind:success="onUploadSuccess">
  </gallery-upload>

  <view class="safe-bottom-area"></view>
</view> 