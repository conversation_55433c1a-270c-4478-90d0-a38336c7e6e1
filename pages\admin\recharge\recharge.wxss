.recharge-admin-container {
  min-height: 100vh;
  background-color: #f5f5f7;
  display: flex;
  flex-direction: column;
}

.status-bar {
  width: 100%;
  height: 88rpx;
  background-color: #ffffff;
}

.nav-header {
  width: 100%;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #ffffff;
  position: relative;
  padding: 0 30rpx;
  box-sizing: border-box;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.back-btn {
  width: 70rpx;
  height: 70rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.arrow-left {
  width: 20rpx;
  height: 20rpx;
  border-top: 4rpx solid #4a4a4a;
  border-left: 4rpx solid #4a4a4a;
  transform: rotate(-45deg);
}

.header-title {
  flex: 1;
  text-align: center;
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
}

.placeholder {
  width: 60rpx;
}

.admin-content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 30rpx;
}

.menu-container {
  width: 100%;
  max-width: 600rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 30rpx;
}

.menu-item {
  width: 100%;
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.menu-item::after {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 8rpx;
  background: linear-gradient(to bottom, #4a90e2, #6aa5e6);
}

.menu-item:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.menu-icon {
  width: 60rpx;
  height: 60rpx;
  margin-right: 20rpx;
}

.menu-name {
  font-size: 32rpx;
  color: #333333;
  font-weight: 500;
}

/* 为每个菜单项添加不同的颜色 */
.menu-item.plans::after {
  background: linear-gradient(to bottom, #4a90e2, #6aa5e6);
}

.menu-item.records::after {
  background: linear-gradient(to bottom, #f5a623, #f7b955);
}

.menu-item.consumption::after {
  background: linear-gradient(to bottom, #50e3c2, #7aebd0);
} 