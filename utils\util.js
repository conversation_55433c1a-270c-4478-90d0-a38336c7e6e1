/**
 * 工具函数库
 */

/**
 * 格式化日期为YYYY-MM-DD格式
 * @param {Date} date 日期对象
 * @returns {string} 格式化后的日期字符串
 */
const formatDate = date => {
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');
  return `${year}-${month}-${day}`;
};

/**
 * 格式化日期时间为YYYY-MM-DD HH:mm:ss格式
 * @param {Date} date 日期对象
 * @returns {string} 格式化后的日期时间字符串
 */
const formatDateTime = date => {
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');
  const hour = date.getHours().toString().padStart(2, '0');
  const minute = date.getMinutes().toString().padStart(2, '0');
  const second = date.getSeconds().toString().padStart(2, '0');
  return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
};

/**
 * 格式化时间为HH:mm格式
 * @param {Date} date 日期对象
 * @returns {string} 格式化后的时间字符串
 */
const formatTime = date => {
  const hour = date.getHours().toString().padStart(2, '0');
  const minute = date.getMinutes().toString().padStart(2, '0');
  return `${hour}:${minute}`;
};

/**
 * 获取当前日期的开始时间（00:00:00）
 * @param {Date} date 日期对象
 * @returns {Date} 日期开始时间
 */
const getStartOfDay = date => {
  const result = new Date(date);
  result.setHours(0, 0, 0, 0);
  return result;
};

/**
 * 获取当前日期的结束时间（23:59:59）
 * @param {Date} date 日期对象
 * @returns {Date} 日期结束时间
 */
const getEndOfDay = date => {
  const result = new Date(date);
  result.setHours(23, 59, 59, 999);
  return result;
};

/**
 * 获取本周的开始日期（周一）
 * @param {Date} date 日期对象
 * @returns {Date} 本周开始日期
 */
const getStartOfWeek = date => {
  const result = new Date(date);
  const day = result.getDay() || 7; // 将周日的0转换为7
  result.setDate(result.getDate() - day + 1); // 设置为本周一
  result.setHours(0, 0, 0, 0);
  return result;
};

/**
 * 获取本周的结束日期（周日）
 * @param {Date} date 日期对象
 * @returns {Date} 本周结束日期
 */
const getEndOfWeek = date => {
  const result = new Date(date);
  const day = result.getDay() || 7; // 将周日的0转换为7
  result.setDate(result.getDate() - day + 7); // 设置为本周日
  result.setHours(23, 59, 59, 999);
  return result;
};

/**
 * 获取本月的开始日期（1号）
 * @param {Date} date 日期对象
 * @returns {Date} 本月开始日期
 */
const getStartOfMonth = date => {
  const result = new Date(date);
  result.setDate(1);
  result.setHours(0, 0, 0, 0);
  return result;
};

/**
 * 获取本月的结束日期（月末）
 * @param {Date} date 日期对象
 * @returns {Date} 本月结束日期
 */
const getEndOfMonth = date => {
  const result = new Date(date);
  result.setMonth(result.getMonth() + 1);
  result.setDate(0);
  result.setHours(23, 59, 59, 999);
  return result;
};

/**
 * 日期字符串转换为日期对象
 * @param {string} dateStr 日期字符串，格式为YYYY-MM-DD或YYYY/MM/DD
 * @returns {Date} 日期对象
 */
const parseDate = dateStr => {
  if (!dateStr) return null;
  // 替换/为-，确保格式一致
  const normalizedDateStr = dateStr.replace(/\//g, '-');
  return new Date(normalizedDateStr);
};

module.exports = {
  formatDate,
  formatDateTime,
  formatTime,
  getStartOfDay,
  getEndOfDay,
  getStartOfWeek,
  getEndOfWeek,
  getStartOfMonth,
  getEndOfMonth,
  parseDate
}; 