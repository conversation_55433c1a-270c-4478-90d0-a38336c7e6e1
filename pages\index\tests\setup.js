/**
 * Jest 测试环境设置
 * 为测试提供全局配置和模拟
 */

// 模拟微信小程序全局对象
global.wx = {
  // 存储相关API
  setStorageSync: jest.fn(),
  getStorageSync: jest.fn(),
  removeStorageSync: jest.fn(),
  getStorageInfoSync: jest.fn(() => ({ keys: [] })),
  
  // 界面相关API
  showToast: jest.fn(),
  stopPullDownRefresh: jest.fn(),
  pageScrollTo: jest.fn(),
  createSelectorQuery: jest.fn(() => ({
    select: jest.fn(() => ({
      node: jest.fn(() => ({
        exec: jest.fn()
      }))
    }))
  })),
  
  // 异步相关API
  nextTick: jest.fn((callback) => {
    if (callback) {
      setTimeout(callback, 0);
    }
  }),
  
  // 网络相关API
  request: jest.fn(),
  
  // 云函数相关API
  cloud: {
    callFunction: jest.fn()
  }
};

// 模拟全局应用对象
global.getApp = jest.fn(() => ({
  globalData: {
    debugMode: false,
    videoList: [],
    userInfo: null,
    currentTabIndex: 1
  }
}));

// 模拟控制台方法，避免测试输出过多日志
global.console = {
  ...console,
  log: jest.fn(),
  warn: jest.fn(),
  error: jest.fn()
};

// 设置默认超时
jest.setTimeout(10000);

// 在每个测试前重置所有模拟
beforeEach(() => {
  jest.clearAllMocks();
});