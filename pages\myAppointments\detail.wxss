/* 预约详情页样式 */
.appointment-detail-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 40rpx;
}

/* 标题栏样式 */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #ffffff;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 10;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
  flex: 1;
  text-align: center;
}

.back-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.back-icon {
  font-size: 40rpx;
  color: #333333;
}

.placeholder {
  width: 60rpx;
}

/* 详情卡片 */
.detail-card {
  margin: 30rpx;
  background-color: #ffffff;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.status-bar {
  padding: 20rpx 30rpx;
  font-size: 32rpx;
  font-weight: bold;
  color: #ffffff;
  text-align: center;
}

.status-bar.pending {
  background-color: #f5a623;
}

.status-bar.confirmed {
  background-color: #4a90e2;
}

.status-bar.completed {
  background-color: #7ed321;
}

.status-bar.cancelled {
  background-color: #9b9b9b;
}

.status-bar.rejected {
  background-color: #d0021b;
}

/* 服务信息 */
.service-info {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.service-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.service-name {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
}

.service-price {
  font-size: 36rpx;
  color: #e02020;
  font-weight: bold;
}

.service-desc {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.5;
}

/* 预约信息 */
.appointment-info {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.info-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  font-size: 28rpx;
  color: #666666;
}

.info-value {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
  word-break: break-all;
}

/* 拒绝原因 */
.reject-reason {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  background-color: #fff6f6;
}

.reason-label {
  font-size: 28rpx;
  color: #e02020;
  font-weight: bold;
  margin-bottom: 10rpx;
  display: block;
}

.reason-content {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.5;
}

/* 参考图片 */
.reference-images {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.images-title {
  font-size: 28rpx;
  color: #333333;
  font-weight: bold;
  margin-bottom: 20rpx;
}

.images-container {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.reference-image {
  width: 200rpx;
  height: 200rpx;
  border-radius: 8rpx;
  object-fit: cover;
}

/* 状态提示 */
.status-tip {
  padding: 30rpx;
  font-size: 28rpx;
  color: #666666;
  text-align: center;
}

/* 操作按钮 */
.action-buttons {
  padding: 0 30rpx;
  margin-top: 30rpx;
  display: flex;
  justify-content: center;
}

.action-btn {
  padding: 20rpx 80rpx;
  border-radius: 100rpx;
  font-size: 32rpx;
  text-align: center;
  margin: 0 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.action-btn.cancel {
  background-color: #ffffff;
  color: #e02020;
  border: 1rpx solid #e02020;
}

.action-btn.reappointment {
  background-color: #4a90e2;
  color: #ffffff;
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 100rpx 0;
  flex: 1;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 28rpx;
  color: #999999;
  margin-bottom: 30rpx;
}

.back-btn-large {
  padding: 16rpx 40rpx;
  background-color: #4a90e2;
  color: #ffffff;
  font-size: 28rpx;
  border-radius: 100rpx;
  box-shadow: 0 4rpx 12rpx rgba(74, 144, 226, 0.3);
}

/* 核销码区域样式 */
.verify-code-section {
  background-color: #f8f8f8;
  border-radius: 8px;
  padding: 15px;
  margin: 15px 0;
  text-align: center;
}

.verify-code-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.verify-code {
  font-size: 28px;
  font-weight: bold;
  color: #333;
  letter-spacing: 4px;
  margin: 10px 0;
}

.verify-code-tip {
  font-size: 12px;
  color: #999;
  margin-top: 8px;
} 