# 视频列表模块测试文档

本目录包含视频列表模块的完整测试套件，包括单元测试、集成测试和性能测试。

## 📁 文件结构

```
tests/
├── video-list.test.js              # 主要单元测试和集成测试
├── video-list.performance.test.js  # 性能测试
├── test-utils.js                   # 测试工具函数
├── setup.js                        # Jest 测试环境设置
├── jest.config.js                  # Jest 配置文件
├── run-tests.js                    # 测试运行脚本
└── README.md                       # 本文档
```

## 🚀 快速开始

### 安装依赖

```bash
npm install --save-dev jest
```

### 运行测试

```bash
# 运行所有测试
node run-tests.js all

# 只运行单元测试
node run-tests.js unit

# 只运行性能测试
node run-tests.js perf

# 生成覆盖率报告
node run-tests.js coverage

# 监听模式运行测试
node run-tests.js watch
```

## 📊 测试覆盖范围

### 单元测试覆盖

- ✅ **模块初始化** - 测试模块正确初始化和状态设置
- ✅ **视频列表加载** - 测试各种加载场景和错误处理
- ✅ **下拉刷新** - 测试刷新逻辑和状态管理
- ✅ **上拉加载更多** - 测试分页加载和边界条件
- ✅ **批量视频处理** - 测试批量数据处理和优化
- ✅ **URL缓存管理** - 测试缓存机制和过期处理
- ✅ **视频数据处理** - 测试数据清理和格式化
- ✅ **列表状态管理** - 测试状态更新和获取
- ✅ **错误处理** - 测试各种错误场景的处理

### 集成测试覆盖

- ✅ **完整加载流程** - 测试从加载到显示的完整流程
- ✅ **完整刷新流程** - 测试刷新操作的完整流程
- ✅ **模块间交互** - 测试与其他模块的交互

### 性能测试覆盖

- ✅ **大数据量处理** - 测试处理1000+视频的性能
- ✅ **并发请求处理** - 测试并发URL获取的性能
- ✅ **内存使用优化** - 测试内存泄漏和缓存清理
- ✅ **缓存命中率** - 测试缓存效率
- ✅ **边界条件** - 测试极端情况的性能

## 🎯 测试目标

### 覆盖率目标

- **分支覆盖率**: ≥ 80%
- **函数覆盖率**: ≥ 80%
- **行覆盖率**: ≥ 80%
- **语句覆盖率**: ≥ 80%

### 性能目标

- **1000个视频处理**: < 5秒
- **视频去重**: < 100ms (1000个视频)
- **URL缓存更新**: < 500ms (1000个URL)
- **并发URL获取**: < 2秒 (50个URL)
- **缓存命中率**: > 50%

## 🧪 测试用例说明

### 核心功能测试

#### 1. 视频列表加载测试
```javascript
test('应该成功加载视频列表', async () => {
  // 测试正常加载流程
  // 验证数据处理和状态更新
});
```

#### 2. 刷新功能测试
```javascript
test('应该正确处理下拉刷新', async () => {
  // 测试刷新逻辑
  // 验证缓存清理和数据更新
});
```

#### 3. 分页加载测试
```javascript
test('应该在有更多数据时加载', async () => {
  // 测试分页逻辑
  // 验证hasMore状态管理
});
```

### 性能测试

#### 1. 大数据量测试
```javascript
test('应该能够处理大量视频数据', async () => {
  // 测试1000个视频的处理性能
  // 验证处理时间 < 5秒
});
```

#### 2. 并发处理测试
```javascript
test('应该能够高效处理并发URL请求', async () => {
  // 测试并发请求限制
  // 验证并发数量控制
});
```

## 🔧 测试工具

### Mock 对象

- **wx API**: 模拟微信小程序API
- **getApp()**: 模拟全局应用对象
- **页面上下文**: 模拟页面数据和方法
- **视频工具函数**: 模拟视频相关API调用

### 测试辅助函数

- `createMockPageContext()`: 创建模拟页面上下文
- `createMockVideoList()`: 创建模拟视频数据
- `createMockVideoUtils()`: 创建模拟视频工具函数
- `waitFor()`: 等待异步操作
- `expectSetDataToBeCalled()`: 验证setData调用

## 📈 测试报告

测试运行后会生成以下报告：

1. **控制台输出**: 实时测试结果和统计
2. **覆盖率报告**: HTML格式的详细覆盖率报告
3. **性能报告**: 性能测试的时间统计

### 查看覆盖率报告

```bash
# 生成覆盖率报告
node run-tests.js coverage

# 在浏览器中打开报告
open coverage/index.html
```

## 🐛 调试测试

### 调试单个测试

```bash
# 运行特定测试文件
npx jest video-list.test.js

# 运行特定测试用例
npx jest -t "应该成功加载视频列表"

# 详细输出模式
npx jest --verbose
```

### 调试技巧

1. **使用 console.log**: 在测试中添加日志输出
2. **使用 debugger**: 在测试代码中设置断点
3. **检查 mock 调用**: 验证 mock 函数的调用情况
4. **分步验证**: 将复杂测试拆分为多个步骤

## 📝 编写新测试

### 测试命名规范

```javascript
describe('功能模块名称', () => {
  test('应该 + 期望行为', () => {
    // 测试代码
  });
});
```

### 测试结构

```javascript
// 1. 准备 (Arrange)
const mockData = createMockVideoList(5);

// 2. 执行 (Act)
const result = await videoListModule.loadVideoList();

// 3. 验证 (Assert)
expect(result).toHaveLength(5);
```

### 最佳实践

1. **独立性**: 每个测试应该独立运行
2. **可重复**: 测试结果应该一致
3. **清晰性**: 测试意图应该明确
4. **完整性**: 覆盖正常和异常情况
5. **性能**: 测试运行应该快速

## 🔄 持续集成

### 自动化测试

```bash
# 在CI/CD中运行测试
npm test

# 生成测试报告
npm run test:coverage

# 运行性能测试
npm run test:performance
```

### 测试门禁

- 所有测试必须通过
- 覆盖率必须达到80%
- 性能测试必须满足目标
- 无内存泄漏

## 📞 支持

如果在运行测试时遇到问题，请检查：

1. **依赖安装**: 确保 Jest 已正确安装
2. **Node.js 版本**: 建议使用 Node.js 14+
3. **文件路径**: 确保测试文件路径正确
4. **权限问题**: 确保有文件读写权限

更多问题请参考 [Jest 官方文档](https://jestjs.io/docs/getting-started)。