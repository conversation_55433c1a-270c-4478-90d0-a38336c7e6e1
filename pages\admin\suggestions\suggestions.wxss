/* pages/admin/suggestions/suggestions.wxss */
.chat-container {
  height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
}

/* 自定义导航栏 */
.custom-nav-bar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: white;
  border-bottom: 1rpx solid #eee;
}

.nav-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
  padding: 0 30rpx;
  /* 删除固定的padding-top，允许通过内联样式动态设置 */
}

.nav-left {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 添加黑色箭头样式 */
.back-arrow {
  font-size: 40rpx;
  font-weight: 400;
  color: #000000;
  line-height: 1;
  transition: transform 0.2s;
}

.nav-left:active .back-arrow {
  transform: translateX(-4rpx);
}

.nav-title {
  flex: 1;
  text-align: center;
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.nav-right {
  width: 60rpx;
  height: 60rpx;
}

/* 主体内容 */
.main-content {
  display: flex;
  height: calc(100vh - 88rpx - var(--status-bar-height, 44rpx));
  /* 允许js控制与胶囊的距离 */
  transition: margin-top 0.3s;
}

/* 聊天模式下的主体内容占据全高度 */
.main-content.chat-mode {
  margin-top: 0;
  height: 100vh;
}

/* 左侧用户列表 */
.user-list-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f9f9f9;
  border-right: 1rpx solid #eeeeee;
}

/* 在大屏幕上的布局 */
@media (min-width: 768px) {
  .user-list-container {
    width: 380rpx;
    min-width: 380rpx;
  }
}

/* 筛选栏 */
.filter-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 20rpx;
  background-color: #ffffff;
  border-bottom: 1rpx solid #eee;
}

.filter-item {
  flex: 1;
}

.picker-display {
  display: flex;
  align-items: center;
  padding: 12rpx 20rpx;
  background: #f5f5f5;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #333;
  width: fit-content;
}

.picker-arrow {
  width: 24rpx;
  height: 24rpx;
  margin-left: 10rpx;
  transform: rotate(90deg);
}

.total-count {
  font-size: 24rpx;
  color: #999;
}

/* 用户列表 */
.user-list {
  flex: 1;
  background-color: #ffffff;
  position: relative;
}

/* 用户列表项 */
.user-item {
  display: flex;
  padding: 24rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
  position: relative;
  background-color: #ffffff;
  transition: background-color 0.2s;
}

.user-item.active {
  background-color: #ecf5ff;
}

.user-item:active {
  background-color: #f5f5f5;
}

.user-avatar-container {
  position: relative;
  margin-right: 20rpx;
}

.user-avatar {
  width: 96rpx;
  height: 96rpx;
  border-radius: 8rpx;
  background-color: #e0e0e0;
}

.unread-badge {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  min-width: 32rpx;
  height: 32rpx;
  border-radius: 16rpx;
  background-color: #ff3b30;
  color: white;
  font-size: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 6rpx;
}

.user-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  overflow: hidden;
}

.user-name-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
}

.user-name {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  max-width: 60%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.last-time {
  font-size: 24rpx;
  color: #999;
}

.last-message-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.last-message-preview {
  font-size: 26rpx;
  color: #666;
  max-width: 80%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.status-indicator {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background-color: #cccccc;
}

.status-indicator.pending {
  background-color: #ff9500;
}

.status-indicator.user_replied {
  background-color: #ff3b30;
}

.status-indicator.replied {
  background-color: #34c759;
}

.status-indicator.closed {
  background-color: #8e8e93;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
}

.loading-spinner {
  width: 64rpx;
  height: 64rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #007aff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-spinner.small {
  width: 32rpx;
  height: 32rpx;
  border-width: 3rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #999;
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-icon {
  width: 160rpx;
  height: 160rpx;
  margin-bottom: 30rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 30rpx;
  color: #999;
}

/* 空聊天状态 */
.empty-chat-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 40rpx;
}

.empty-chat-container .empty-icon {
  width: 180rpx;
  height: 180rpx;
  margin-bottom: 30rpx;
  opacity: 0.5;
}

.empty-chat-container .empty-text {
  font-size: 32rpx;
  font-weight: 500;
  color: #666;
  margin-bottom: 10rpx;
}

.empty-chat-container .empty-subtext {
  font-size: 26rpx;
  color: #999;
}

/* 加载更多和没有更多 */
.load-more, .no-more {
  text-align: center;
  padding: 20rpx 0;
  color: #999;
  font-size: 26rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.load-more {
  display: flex;
  align-items: center;
  justify-content: center;
}

.load-more .loading-spinner {
  margin-right: 10rpx;
}

/* 右侧聊天区域 */
.chat-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
  position: relative;
  height: 100%;
  padding-bottom: 120rpx; /* 为固定定位的输入框留出空间 */
  transition: padding-bottom 0.3s ease; /* 添加过渡效果 */
}

/* 键盘激活时的聊天区域 */
.chat-area.keyboard-active {
  padding-bottom: 220rpx; /* 增加底部空间，防止内容被覆盖 */
}

/* 聊天头部 - 新样式，简化且保持在胶囊下方 */
.chat-header {
  width: 100%;
  background-color: #ffffff;
  border-bottom: 1rpx solid #eeeeee;
  position: relative;
  z-index: 100;
}

.chat-header-content {
  display: flex;
  align-items: center;
  padding: 0 24rpx;
  height: 88rpx;
  /* 删除固定的margin-top，允许通过内联样式动态设置 */
}

.back-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16rpx;
  position: relative;
}

.back-arrow {
  font-size: 40rpx;
  font-weight: 400;
  color: #000000;
  line-height: 1;
  transition: transform 0.2s;
}

.back-btn:active .back-arrow {
  transform: translateX(-4rpx);
}

.chat-user-name {
  flex: 1;
  font-size: 34rpx;
  font-weight: 400;
  color: #333;
  text-align: center;
  padding-right: 60rpx; /* 为了抵消左侧返回按钮的宽度，保持居中 */
}

/* 聊天消息区域 */
.chat-messages {
  flex: 1;
  padding: 0 24rpx;
  position: relative;
  overflow-y: auto;
  transition: transform 0.3s ease, padding-bottom 0.3s ease; /* 添加transform过渡效果 */
}

/* 键盘显示时的消息区域 */
.chat-messages.keyboard-shown {
  padding-bottom: 120rpx;
  transform: translateY(calc(-1 * min(80px, 20vh))); /* 使用calc和vh单位，更好地适应不同尺寸手机 */
}

/* 底部安全区域 */
.safe-bottom-area {
  width: 100%;
  transition: height 0.3s;
  min-height: env(safe-area-inset-bottom, 34rpx); /* 确保至少有底部安全区域的高度 */
}

/* 键盘激活时的底部安全区域，确保足够空间 */
.keyboard-active .safe-bottom-area {
  min-height: max(env(safe-area-inset-bottom, 34rpx), 60px); /* 确保键盘激活时有足够的底部安全距离 */
}

/* 消息列表 */
.message-list {
  padding: 16rpx 0;
}

/* 时间分割线 */
.time-divider {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 24rpx 0;
}

.time-text {
  padding: 6rpx 16rpx;
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 10rpx;
  font-size: 24rpx;
  color: #999;
}

/* 消息项 */
.message-item {
  display: flex;
  margin-bottom: 24rpx;
  padding: 0 8rpx;
}

.user-message {
  justify-content: flex-start;
}

.admin-message {
  justify-content: flex-start;
  flex-direction: row-reverse;
}

.message-avatar-container {
  margin: 0 16rpx;
}

.message-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 8rpx;
}

.admin-message .message-avatar {
  border-radius: 50%;
}

.message-content {
  max-width: 70%;
  display: flex;
  flex-direction: column;
  position: relative;
}

.message-bubble {
  padding: 20rpx 24rpx;
  border-radius: 12rpx;
  background-color: #ffffff;
  margin-bottom: 8rpx;
  box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.1);
  position: relative;
}

.user-message .message-bubble {
  border-bottom-left-radius: 4rpx;
  background-color: #ffffff;
}

.admin-message .message-bubble {
  border-bottom-right-radius: 4rpx;
  background-color: #007AFF;
}

.message-text {
  font-size: 30rpx;
  line-height: 1.5;
  color: #333;
  word-break: break-word;
}

.message-time {
  font-size: 24rpx;
  color: #999;
  align-self: flex-end;
  margin-top: 4rpx;
}

.message-images {
  display: flex;
  flex-wrap: wrap;
  margin-top: 8rpx;
  gap: 8rpx;
  background-color: transparent;
  border-radius: 8rpx;
  overflow: hidden;
}

.message-image {
  width: 240rpx;
  height: 240rpx;
  border-radius: 8rpx;
  object-fit: cover;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
}

.message-videos {
  margin-top: 8rpx;
}

.message-video {
  width: 100%;
  max-width: 400rpx;
  border-radius: 8rpx;
  margin-top: 8rpx;
}

/* 输入区域 */
.chat-input-area {
  display: flex;
  align-items: center;
  padding: 16rpx 24rpx;
  background-color: #ffffff;
  border-top: 1rpx solid #eeeeee;
  min-height: 88rpx;
  width: 100%;
  box-sizing: border-box;
  position: fixed; /* 固定定位 */
  bottom: 0; /* 基础定位在底部 */
  left: 0;
  right: 0;
  z-index: 100; /* 确保在最上层 */
  transition: bottom 0.2s ease; /* 只对bottom应用过渡，提高性能 */
  /* 删除默认的padding-bottom，让它由内联样式动态控制 */
  will-change: bottom, transform; /* 提示浏览器这些属性会变化，优化渲染性能 */
  -webkit-backface-visibility: hidden; /* 优化性能 */
  backface-visibility: hidden;
}

/* 媒体选择按钮 */
.media-btn {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background-color: transparent; /* 将背景改为透明 */
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16rpx;
  transition: background-color 0.2s;
  position: relative; /* 添加定位以便放置加号 */
}

.media-btn:active {
  background-color: rgba(0, 0, 0, 0.05); /* 点击时轻微显示背景 */
}

.media-icon {
  font-size: 40rpx;
  line-height: 1;
  color: #666;
  font-weight: 300;
  text-align: center; /* 确保文本居中 */
}

/* 键盘激活状态下的输入区域 */
.chat-input-area.keyboard-active {
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  /* 键盘激活时不需要额外的padding-bottom */
}

/* 输入区域底部填充，确保无断层 */
.chat-input-area::after {
  content: "";
  position: absolute;
  left: 0;
  right: 0;
  bottom: -1000px; /* 延伸足够长的距离 */
  height: 1000px;
  background-color: #ffffff;
  z-index: -1;
}

.input-container {
  flex: 1;
  background-color: #f5f5f5;
  border-radius: 36rpx;
  padding: 12rpx 24rpx;
  margin-right: 16rpx;
  overflow: hidden; /* 防止内容溢出 */
}

.message-input {
  width: 100%;
  min-height: 60rpx;
  max-height: 240rpx;
  font-size: 30rpx;
  line-height: 1.5;
  color: #333; /* 文字颜色改为黑色 */
  padding: 0 12rpx; /* 添加左右内边距，使文字不贴边 */
  text-align: left; /* 文字靠左对齐 */
}

/* 输入框的placeholder样式 */
.message-input::placeholder {
  color: #999;
  text-align: left; /* placeholder文字靠左对齐 */
}

.send-btn {
  min-width: 120rpx;
  height: 72rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #cccccc;
  color: #ffffff;
  border-radius: 36rpx;
  font-size: 28rpx;
  padding: 0 30rpx;
  transition: background-color 0.3s;
}

.send-btn.active {
  background-color: #07c160;
}

/* 欢迎页面 */
.welcome-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f9f9f9;
}

.welcome-icon {
  width: 160rpx;
  height: 160rpx;
  margin-bottom: 30rpx;
  opacity: 0.5;
}

.welcome-text {
  font-size: 32rpx;
  color: #999;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-content {
    flex-direction: column;
  }
  
  .user-list-container {
    height: 100%;
  }
  
  .chat-area {
    height: 100%;
    width: 100%;
  }
}

/* 用户消息和管理员消息样式优化 */
.user-message .message-bubble {
  background-color: #ffffff;
}

.admin-message .message-bubble {
  background-color: #007AFF;
}

.admin-message .message-text {
  color: #ffffff;
}

/* 添加气泡箭头 */
.user-message .message-bubble:after {
  content: "";
  position: absolute;
  left: -10rpx;
  top: 20rpx;
  border-width: 10rpx;
  border-style: solid;
  border-color: transparent #ffffff transparent transparent;
}

.admin-message .message-bubble:after {
  content: "";
  position: absolute;
  right: -10rpx;
  top: 20rpx;
  border-width: 10rpx;
  border-style: solid;
  border-color: transparent transparent transparent #007AFF;
}
