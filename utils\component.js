/**
 * 组件工具类
 * 注意：此文件依赖于微信小程序环境中的全局console对象
 */

/* global console, Behavior */

// 创建基础行为
const ComponentBase = Behavior({
  /**
   * 生命周期函数混入
   */
  lifetimes: {
    created() {
      this._eventHandlers = new Map();
    },
    detached() {
      if (this._eventHandlers) {
        this._eventHandlers.clear();
      }
    }
  },

  /**
   * 方法混入
   */
  methods: {
    /**
     * 注册事件处理器
     * @param {string} eventName 事件名称
     * @param {Function} handler 处理函数
     */
    on(eventName, handler) {
      if (!this._eventHandlers) {
        this._eventHandlers = new Map();
      }
      if (!this._eventHandlers.has(eventName)) {
        this._eventHandlers.set(eventName, new Set());
      }
      this._eventHandlers.get(eventName).add(handler);
    },

    /**
     * 移除事件处理器
     * @param {string} eventName 事件名称
     * @param {Function} handler 处理函数
     */
    off(eventName, handler) {
      if (!this._eventHandlers || !this._eventHandlers.has(eventName)) return;
      if (handler) {
        this._eventHandlers.get(eventName).delete(handler);
      } else {
        this._eventHandlers.delete(eventName);
      }
    },

    /**
     * 触发事件
     * @param {string} eventName 事件名称
     * @param {*} data 事件数据
     */
    emit(eventName, data) {
      if (!this._eventHandlers || !this._eventHandlers.has(eventName)) return;
      for (const handler of this._eventHandlers.get(eventName)) {
        try {
          handler.call(this, data);
        } catch (err) {
          console.error(`[Component] 事件处理错误: ${eventName}`, err);
        }
      }
    }
  }
});

module.exports = {
  ComponentBase
}; 