Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 要分享的内容信息（可以是视频、文章或服务）
    videoInfo: {
      type: Object,
      value: null
    },
    // 自定义样式
    customStyle: {
      type: String,
      value: ''
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 分享按钮是否显示
    showShareOptions: false
  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 点击分享按钮
    onTapShare: function(e) {
      // 阻止事件冒泡
      if (e && e.stopPropagation) {
        e.stopPropagation();
      }
      
      // 触发分享事件，传递内容信息
      this.triggerEvent('share', {
        videoInfo: this.properties.videoInfo
      });
      
      console.log('分享按钮被点击，传递数据:', this.properties.videoInfo);
    }
  }
}) 