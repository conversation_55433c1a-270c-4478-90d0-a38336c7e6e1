<!-- 移除WxParse模板导入 -->
<!-- <import src="../../wxParse/wxParse.wxml"/> -->

<modal 
  visible="{{visible}}" 
  showHeader="{{false}}"
  bindclose="onClose"
  bindnavbarControl="onNavbarControl"
  fullWidth="{{true}}"
  maskClosable="{{false}}">
  <view 
    class="fullscreen-modal-content {{visible ? 'visible' : ''}} {{isClosing ? 'hidden' : ''}} {{isFullscreenMode ? 'fullscreen' : ''}}" 
    style="{{modalHeightStyle}}; background: var(--videomodal-bg);"
    bindtouchstart="onTouchStart"
    bindtouchmove="onTouchMove"
    bindtouchend="onTouchEnd"
    bindtouchcancel="onTouchEnd">



    <!-- 添加拖动指示器，使视觉效果更好 -->
    <view class="drag-indicator"></view>

    <scroll-view 
      id="modalScroll"
      scroll-y 
      scroll-top="{{scrollTop}}"
      scroll-with-animation="{{true}}"
      class="scrollable-content" 
      enhanced="{{true}}"
      bounces="{{false}}"
      show-scrollbar="{{false}}"
      bindscroll="onScroll">
      
      <!-- 视频播放区域 -->
      <view class="video-container {{isPortraitVideo ? 'portrait' : ''}}" bindtap="toggleControls">
        <video 
          wx:if="{{videoUrl && !isClosing}}"
          class="video-element controls-transition {{!showControls ? 'controls-hidden' : ''}}"
          id="detailVideo"
          src="{{videoUrl}}"
          controls="{{true}}"
          show-fullscreen-btn="{{true}}"
          play-btn-position="center"
          object-fit="contain"
          show-play-btn="{{true}}"
          show-center-play-btn="{{true}}"
          initial-time="{{currentPlayTime}}"
          bindplay="onVideoPlay"
          bindpause="onVideoPause"
          bindended="onVideoEnd"
          binderror="onVideoError"
          bindtimeupdate="onVideoTimeUpdate"
          bindfullscreenchange="onFullscreenChange"
          bindloadedmetadata="onVideoLoadedMetaData"
          bindready="onVideoReady"
          enable-play-gesture="{{false}}"
          enable-auto-rotation="{{true}}"
          vslide-gesture="{{true}}"
          vslide-gesture-in-fullscreen="{{true}}"
          show-progress="{{true}}"
          show-no-wifi-tip="{{false}}"
          direction="0"
          show-mute-btn="{{true}}"
          picture-in-picture-mode="{{['push', 'pop']}}"
          picture-in-picture-show-progress="{{true}}"
          custom-cache="{{false}}"
          autoplay="{{false}}"
          style="width:100%;"
        ></video>
        <!-- 添加覆盖层，替代之前的伪元素 -->
        <view wx:if="{{!showControls}}" class="controls-hidden-overlay"></view>
        <view 
          wx:if="{{!videoUrl || isClosing}}"
          class="video-placeholder"
          style="width:100%; height:100%; background-color: var(--bg-dark);"
        >
          <!-- 预留封面图位置，确保样式正确应用 -->
          <image wx:if="{{coverUrl}}" class="video-container-image" src="{{coverUrl}}" mode="aspectFill"></image>
        </view>
      </view>
      
      <!-- 视频信息区域 -->
      <view class="video-info">
        <!-- Logo图标 -->
        <view class="author-avatar">
          <image class="author-avatar-image" src="{{authorAvatar}}" mode="aspectFill"></image>
        </view>
        
        <!-- 标题和副标题区域 -->
        <view class="title-container">
          <!-- 主标题 -->
          <view class="video-title">{{mainTitle}}</view>
          
          <!-- 副标题 -->
          <view class="video-subtitle">{{subTitle}}</view>
        </view>
        
        <!-- 播放量 - 移到右侧 -->
        <view class="play-count">
          <text class="play-count-text">播放量</text>
          <text class="decorator-dot">•</text>
          <text>{{playCount}}</text>
        </view>
      </view>
      
      <!-- 视频详情描述区域 -->
      <view class="video-detail-section">
        <view class="section-header">
          <view class="section-title">详情描述</view>
          <view class="refresh-btn" bindtap="refreshContent">刷新</view>
        </view>
        
        <!-- 富文本内容加载中 - 使用三点动画 -->
        <view wx:if="{{loadingContent}}" class="loading-container">
          <view class="loading-dots">
            <view class="loading-dot"></view>
            <view class="loading-dot"></view>
            <view class="loading-dot"></view>
          </view>
          <view class="loading-text">加载中</view>
        </view>
        
        <!-- 详情图片显示区域 -->
        <block wx:elif="{{detailImages && detailImages.length > 0}}">
          <view class="detail-images-container">
            <block wx:for="{{detailImages}}" wx:key="index">
              <view class="detail-image-item" data-index="{{index}}" bindtap="previewDetailImage">
                <image class="detail-image" src="{{item}}" mode="widthFix" lazy-load="true"></image>
              </view>
            </block>
          </view>
        </block>
        
        <!-- 普通文本描述（当没有详情图片时显示） -->
        <block wx:else>
          <view class="detail-content">
            <view wx:if="{{description}}" class="description-text">{{description}}</view>
            <view wx:else class="description-placeholder">暂无详情描述</view>
          </view>
        </block>
      </view>
      
      <!-- 添加底部留白区域，确保内容可以滚动到底部控制条下方 -->
      <view class="bottom-space"></view>
    </scroll-view>
    
    <!-- 底部操作区 -->
    <view class="modal-footer">
      <button class="action-button scroll-top-button" bindtap="scrollToTop">
        <image class="action-button-icon" src="/static/置顶图标.svg" mode="aspectFit"></image>
      </button>
      

      
      <button class="action-button close-button" bindtap="onClose">
        <image class="action-button-icon" src="/static/退出图标.svg" mode="aspectFit"></image>
      </button>
    </view>
  </view>
</modal> 