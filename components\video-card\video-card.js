/* global Component wx */
const app = getApp();

Component({
  properties: {
    videoId: { type: String, value: '' },
    mainTitle: { 
      type: String, 
      value: '', 
      observer: function(newVal) {
        if (newVal) {
          this.extractCategory(newVal);
        }
      }
    },
    subTitle: { type: String, value: '' },
    coverUrl: { type: String, value: '' },
    videoUrl: { type: String, value: '' },
    playCount: { type: String, value: '0' },
    authorAvatar: { type: String, value: '/static/logo.png' },
    createTime: { 
      type: String, 
      value: '', 
      observer: function(newVal) {
        if (newVal) {
          this.formatTime(newVal);
        }
      }
    },
    isMuted: {
      type: Boolean,
      value: true,
      observer: function(newVal) {
        this.setData({
          _isMuted: newVal
        });
      }
    },
    showVideo: { type: Boolean, value: false },
    readyToPlay: { type: Boolean, value: false },
    videoInfo: { type: Object, value: null }
  },

  data: {
    isPlaying: false,
    hasStartedPlaying: false,
    hasEnded: false,
    _videoContext: null,
    _isMuted: true,
    _lastLoadTime: null,
    isVideoLoaded: false,
    isVideoLoading: false,
    formattedTime: '',
    category: '',
    // 触摸状态相关
    isTouched: false,
    touchStartY: 0,
    touchDirection: '',
    isMoving: false,
    _retryCount: 0
  },

  observers: {
    'showVideo': function(show) {
      this.setData({ isPlaying: show });
    },
    'isMuted': function(muted) {
      this.setData({ _isMuted: muted });
    },
    'videoUrl': function(url) {
      if (url) {
        // 重置视频加载状态
        this.setData({ 
          isVideoLoaded: false,
          _lastLoadTime: null,
          isVideoLoading: false
        });
      }
    }
  },

  lifetimes: {
    attached() {
      this._videoContext = wx.createVideoContext(`video-${this.properties.videoId}`, this);
      
      // 获取全局静音状态
      const globalApp = getApp();
      const globalMuted = globalApp.globalData.globalMuted;
      
      this.setData({ 
        isPlaying: this.properties.showVideo,
        _isMuted: globalMuted !== undefined ? globalMuted : this.properties.isMuted
      });
      
      // 监听全局静音状态变化事件
      this.globalMuteChangeHandler = (isMuted) => {
        this.updateMuteState(isMuted);
      };
      
      // 将当前组件添加到全局视频卡片列表中，以便统一管理
      if (!globalApp.videoCardInstances) {
        globalApp.videoCardInstances = [];
      }
      globalApp.videoCardInstances.push(this);
    },
    detached() {
      if (this._videoContext) {
        this.pause();
      }
      
      // 从全局视频卡片列表中移除
      const globalApp = getApp();
      if (globalApp.videoCardInstances) {
        const index = globalApp.videoCardInstances.indexOf(this);
        if (index !== -1) {
          globalApp.videoCardInstances.splice(index, 1);
        }
      }
    }
  },

  methods: {
    // 触摸开始
    handleTouchStart(e) {
      this.setData({
        isTouched: true,
        touchStartY: e.touches[0].clientY,
        touchDirection: '',
        isMoving: false
      });
    },

    // 触摸移动
    handleTouchMove(e) {
      if (!this.data.isTouched) return;
      
      const currentY = e.touches[0].clientY;
      const diffY = currentY - this.data.touchStartY;
      
      // 判断滑动方向
      let direction = '';
      if (diffY < -10) {
        direction = 'up';
      } else if (diffY > 10) {
        direction = 'down';
      }
      
      if (direction && direction !== this.data.touchDirection) {
        this.setData({
          touchDirection: direction,
          isMoving: true
        });
      }
    },

    // 触摸结束
    handleTouchEnd() {
      if (!this.data.isTouched) return;
      
      this.setData({
        isTouched: false,
        isMoving: false,
        touchDirection: ''
      });
      
      // 使用setTimeout确保动画类被先移除后再添加released类
      setTimeout(() => {
        this.setData({
          isReleased: true
        });
        
        // 移除released类
        setTimeout(() => {
          this.setData({
            isReleased: false
          });
        }, 300);
      }, 0);
    },
    
    extractCategory(title) {
      if (title.includes('音乐')) {
        this.setData({ category: '音乐' });
      } else if (title.includes('冥想')) {
        this.setData({ category: '冥想' });
      } else if (title.includes('放松')) {
        this.setData({ category: '放松' });
      } else if (title.includes('睡眠')) {
        this.setData({ category: '助眠' });
      } else {
        this.setData({ category: '视频' });
      }
    },
    formatTime(timestamp) {
      if (!timestamp) return '';
      
      const now = new Date();
      const createDate = new Date(timestamp);
      const diffTime = now - createDate;
      const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
      
      let formattedTime = '';
      if (diffDays === 0) {
        const hours = Math.floor(diffTime / (1000 * 60 * 60));
        if (hours === 0) {
          const minutes = Math.floor(diffTime / (1000 * 60));
          formattedTime = minutes <= 0 ? '刚刚' : `${minutes}分钟前`;
        } else {
          formattedTime = `${hours}小时前`;
        }
      } else if (diffDays === 1) {
        formattedTime = '昨天';
      } else if (diffDays < 7) {
        formattedTime = `${diffDays}天前`;
      } else if (diffDays < 30) {
        formattedTime = `${Math.floor(diffDays / 7)}周前`;
      } else if (diffDays < 365) {
        formattedTime = `${Math.floor(diffDays / 30)}月前`;
      } else {
        formattedTime = `${Math.floor(diffDays / 365)}年前`;
      }
      
      this.setData({ formattedTime });
    },
    onCardTap() {
      this.triggerEvent('videotap', {
        videoInfo: this.properties.videoInfo
      });
    },
    
    // 停止其他所有正在播放的视频
    stopOtherVideos() {
      const globalApp = getApp();
      if (globalApp.videoCardInstances) {
        globalApp.videoCardInstances.forEach(instance => {
          if (instance !== this && instance.data.isPlaying) {
            instance.pause();
          }
        });
      }
    },
    
    onCoverTap() {
      // 停止其他视频
      this.stopOtherVideos();
      
      if (this.data.hasEnded) {
        this._videoContext.seek(0);
        this.setData({ hasEnded: false });
      }
      
      this.setData({
        isPlaying: true,
        hasStartedPlaying: true,
        isVideoLoading: true
      });
      this._videoContext.play();
    },
    
    onVideoTap() {
      if (this.data.hasEnded) {
        // 停止其他视频
        this.stopOtherVideos();
        
        this._videoContext.seek(0);
        this.setData({ 
          hasEnded: false,
          isPlaying: true,
          isVideoLoading: true
        });
        this._videoContext.play();
      } else if (this.data.isPlaying) {
        this._videoContext.pause();
        this.setData({ isPlaying: false });
      } else {
        // 停止其他视频
        this.stopOtherVideos();
        
        this._videoContext.play();
        this.setData({ 
          isPlaying: true,
          isVideoLoading: true
        });
      }
    },

    onVideoPlay() {
      // 确保其他视频已停止
      this.stopOtherVideos();
      
      this.setData({
        hasStartedPlaying: true,
        hasEnded: false,
        isPlaying: true,
        isVideoLoading: false
      });
      
      // 增加播放量
      if (this.properties.videoId) {
        wx.cloud.callFunction({
          name: 'videoManager',
          data: {
            type: 'frontend',
            action: 'increasePlayCount',
            data: {
              id: this.properties.videoId
            }
          }
        }).catch(err => {
          // 保留关键错误日志
          console.error('增加播放量失败', err.message || err);
        });
      }
      
      this.triggerEvent('videoPlay');
    },

    onVideoPause() {
      this.setData({ isPlaying: false });
      this.triggerEvent('videoPause');
    },

    onVideoEnded() {
      this.setData({
        hasEnded: true,
        isPlaying: false,
        isVideoLoading: false
      });
      this.triggerEvent('videoEnd');
    },

    // 更新当前组件的静音状态
    updateMuteState(isMuted) {
      this.setData({ _isMuted: isMuted });
    },

    // 修改后的 toggleMute 方法，更新全局静音状态
    toggleMute() {
      const globalApp = getApp();
      const newMuteState = !this.data._isMuted;
      
      // 更新全局静音状态
      globalApp.globalData.globalMuted = newMuteState;
      
      // 保存到本地存储，以便下次启动应用时恢复
      wx.setStorageSync('globalMuted', newMuteState);
      
      // 更新当前组件状态
      this.setData({ _isMuted: newMuteState });
      
      // 更新所有其他视频卡片的静音状态
      if (globalApp.videoCardInstances) {
        globalApp.videoCardInstances.forEach(instance => {
          if (instance !== this) {
            instance.updateMuteState(newMuteState);
          }
        });
      }
      
      // 触发事件通知父组件
      this.triggerEvent('muteChange', { isMuted: newMuteState });
    },

    onShareButtonTap() {
      this.triggerEvent('share', { videoInfo: this.data.videoInfo });
    },

    // 详情按钮点击处理
    onDetailButtonTap() {
      console.log('详情按钮被点击');
      
      // 先暂停当前视频
      if (this.data.isPlaying && this._videoContext) {
        this._videoContext.pause();
        this.setData({ 
          isPlaying: false 
        });
      }
      
      // 触发详情事件，传递视频信息
      this.triggerEvent('detail', {
        videoInfo: this.properties.videoInfo || {
          id: this.properties.videoId,
          mainTitle: this.properties.mainTitle,
          subTitle: this.properties.subTitle,
          coverUrl: this.properties.coverUrl
        }
      });
    },

    pause() {
      if (this._videoContext) {
        try {
          this._videoContext.pause();
          
          // 更新状态为已暂停，无论之前是什么状态
          this.setData({ 
            isPlaying: false 
          });
          
          // 通知父组件视频已暂停
          this.triggerEvent('videoPause');
        } catch (error) {
          // 保留关键错误日志，但简化错误信息
          console.error('视频暂停失败:', error.message || error);
        }
      }
    },

    onVideoLoaded(e) {
      const now = Date.now();
      if (!this._lastLoadTime) {
        this._lastLoadTime = now;
      } else {
        this._lastLoadTime = now;
      }
      
      // 只在调试模式下输出视频加载成功日志
      if (app && app.globalData && app.globalData.debugMode) {
        console.log(`视频加载成功: ${this.properties.videoId}`);
      }
      
      this.setData({ isVideoLoaded: true, isVideoLoading: false });
    },

    onVideoError(e) {
      // 获取详细的错误信息
      const errorDetail = e && e.detail ? e.detail.errMsg : '未知错误';
      const videoId = this.properties.videoId || '未知ID';
      
      // 保留错误日志，但增加更详细的上下文信息
      console.error(`视频加载失败: ${videoId} ${errorDetail}`);
      
      // 记录重试次数
      if (!this._retryCount) {
        this._retryCount = 0;
      }
      
      // 如果重试次数小于最大重试次数，尝试重新加载视频
      const MAX_RETRY = 2;
      if (this._retryCount < MAX_RETRY) {
        this._retryCount++;
        console.log(`尝试重新加载视频(${this._retryCount}/${MAX_RETRY}): ${videoId}`);
        
        // 设置状态为加载中
        this.setData({ isVideoLoading: true });
        
        // 延迟一段时间后重新获取视频URL并更新
        setTimeout(async () => {
          try {
            // 获取app实例
            const app = getApp();
            const videoUtils = app.videoUtils || require('../../utils/video/index');
            
            // 重新获取视频URL
            const newVideoUrl = await videoUtils.getVideoUrl(videoId, '', '', true); // 传入force参数强制刷新
            
            if (newVideoUrl) {
              console.log(`已获取新的视频URL: ${videoId}`);
              this.setData({ 
                videoUrl: newVideoUrl,
                isVideoLoading: false
              });
            } else {
              this.setData({ 
                isVideoLoaded: false,
                hasStartedPlaying: false, 
                isVideoLoading: false
              });
            }
          } catch (retryError) {
            console.error(`重试加载视频失败: ${videoId}`, retryError.message || retryError);
            this.setData({ 
              isVideoLoaded: false,
              hasStartedPlaying: false, 
              isVideoLoading: false
            });
          }
        }, 1000); // 延迟1秒后重试
      } else {
        // 重试次数达到上限，更新状态
        this.setData({ 
          isVideoLoaded: false,
          hasStartedPlaying: false, 
          isVideoLoading: false
        });
      }
    },

    // 处理视频分享
    onShareVideo(e) {
      // 从分享按钮组件接收视频信息
      const videoInfo = e.detail.videoInfo || this.properties.videoInfo || {
        id: this.properties.videoId,
        mainTitle: this.properties.mainTitle,
        subTitle: this.properties.subTitle,
        coverUrl: this.properties.coverUrl
      };
      
      // 触发父组件的分享事件
      this.triggerEvent('share', {
        videoInfo: videoInfo
      });
    }
  }
});
