<view class="order-detail-container">
  <!-- 顶部状态栏 -->
  <view class="page-header">
    <view class="back-icon" bindtap="goBack">
      <text class="iconfont icon-back">←</text>
    </view>
    <view class="header-title">核销详情</view>
    <view class="placeholder"></view>
  </view>

  <!-- 内容区域 -->
  <view class="content">
    <!-- 加载中 -->
    <view class="loading" wx:if="{{isLoading}}">
      <view class="loading-icon"></view>
      <view class="loading-text">加载中...</view>
    </view>

    <!-- 预约详情 -->
    <block wx:if="{{!isLoading && appointmentDetail}}">
      <!-- 预约状态 -->
      <view class="status-section">
        <view class="status-text">{{appointmentDetail.statusText}}</view>
        <view class="order-number">预约ID: {{appointmentDetail._id}}</view>
      </view>

      <!-- 预约信息 -->
      <view class="detail-card">
        <view class="card-title">预约信息</view>
        <view class="info-item">
          <view class="info-label">服务项目</view>
          <view class="info-value">{{appointmentDetail.serviceName}}</view>
        </view>
        <view class="info-item">
          <view class="info-label">预约日期</view>
          <view class="info-value">{{appointmentDetail.date}}</view>
        </view>
        <view class="info-item">
          <view class="info-label">预约时间</view>
          <view class="info-value">{{appointmentDetail.time}}</view>
        </view>
        <view class="info-item">
          <view class="info-label">服务价格</view>
          <view class="info-value price">¥{{appointmentDetail.servicePrice}}</view>
        </view>
      </view>

      <!-- 核销信息 -->
      <view class="detail-card">
        <view class="card-title">核销信息</view>
        <view class="info-item">
          <view class="info-label">核销码</view>
          <view class="info-value">{{appointmentDetail.verifyCode}}</view>
        </view>
        <view class="info-item">
          <view class="info-label">核销状态</view>
          <view class="info-value">{{appointmentDetail.verified ? '已核销' : '未核销'}}</view>
        </view>
        <view class="info-item" wx:if="{{appointmentDetail.verified}}">
          <view class="info-label">核销时间</view>
          <view class="info-value">{{appointmentDetail.verifyTime || '未知'}}</view>
        </view>
        <view class="info-item" wx:if="{{appointmentDetail.verified}}">
          <view class="info-label">核销员工</view>
          <view class="info-value">{{appointmentDetail.verifyStaffName}}</view>
        </view>
      </view>

      <!-- 客户信息 -->
      <view class="detail-card">
        <view class="card-title">客户信息</view>
        <view class="info-item">
          <view class="info-label">联系电话</view>
          <view class="info-value">
            {{appointmentDetail.maskedPhone || '未提供'}}
          </view>
        </view>
      </view>

      <!-- 参考图片 -->
      <view class="detail-card" wx:if="{{appointmentDetail.imageUrls && appointmentDetail.imageUrls.length > 0 && (isAssignedStaff || isAdmin)}}">
        <view class="card-title">参考图片</view>
        <view class="image-gallery">
          <image 
            wx:for="{{appointmentDetail.imageUrls}}" 
            wx:key="index" 
            src="{{item}}" 
            mode="aspectFill" 
            class="reference-image"
            bindtap="previewImage"
            data-index="{{index}}"
          ></image>
        </view>
      </view>

      <!-- 业绩信息 -->
      <view class="detail-card">
        <view class="card-title">业绩信息</view>
        <view class="info-item">
          <view class="info-label">消费金额</view>
          <view class="info-value total-price">¥{{appointmentDetail.servicePrice}}</view>
        </view>
        <view class="info-item">
          <view class="info-label">提成金额</view>
          <view class="info-value commission">¥{{appointmentDetail.commission || (appointmentDetail.servicePrice * 0.3)}}</view>
        </view>
        <view class="info-item">
          <view class="info-label">提成比例</view>
          <view class="info-value">{{(appointmentDetail.commissionRate || 0.3) * 100}}%</view>
        </view>
        <view class="info-item summary-item">
          <view class="summary-text">消费金额 ¥{{appointmentDetail.servicePrice}} × {{(appointmentDetail.commissionRate || 0.3) * 100}}% = 提成 ¥{{appointmentDetail.commission || (appointmentDetail.servicePrice * 0.3)}}</view>
        </view>
      </view>
    </block>
  </view>
</view> 