const app = getApp()

Page({
  data: {
    carousels: [],
    loading: true,
    showAddForm: false,
    showEditForm: false,
    currentCarousel: null,
    formData: {
      title: '',
      order: '',
      imageUrl: ''
    },
    uploadPath: 'carousel-images/',
    tempImagePath: ''
  },

  onLoad() {
    this.fetchCarousels();
  },

  // 处理下拉刷新
  onPullDownRefresh() {
    console.log('下拉刷新触发');
    this.fetchCarousels().then(() => {
      wx.stopPullDownRefresh();
      wx.showToast({
        title: '刷新成功',
        icon: 'success',
        duration: 1000
      });
    });
  },

  // 获取轮播图列表
  fetchCarousels() {
    this.setData({ loading: true });
    
    return new Promise((resolve, reject) => {
      wx.cloud.callFunction({
        name: 'appointmentManager',
        data: {
          action: 'getCarousel',
          type: 'admin'
        },
        success: res => {
          console.log('获取轮播图列表成功', res);
          
          if (res.result && res.result.success) {
            this.setData({
              carousels: res.result.data || [],
              loading: false
            });
            resolve(res.result.data);
          } else {
            this.setData({
              carousels: [],
              loading: false
            });
            
            wx.showToast({
              title: '获取轮播图列表失败',
              icon: 'none'
            });
            reject(new Error('获取轮播图列表失败'));
          }
        },
        fail: err => {
          console.error('获取轮播图列表失败', err);
          
          this.setData({
            carousels: [],
            loading: false
          });
          
          wx.showToast({
            title: '网络异常，请稍后重试',
            icon: 'none'
          });
          reject(err);
        }
      });
    });
  },

  // 显示添加表单
  showAddForm() {
    // 获取当前轮播图数量，用于默认排序
    const order = this.data.carousels.length + 1;
    
    this.setData({
      showAddForm: true,
      formData: {
        title: '',
        order: order,
        imageUrl: ''
      },
      tempImagePath: ''
    });
  },

  // 显示编辑表单
  showEditForm(e) {
    const { carousel } = e.currentTarget.dataset;

    // 检查是否为默认的空状态轮播图，如果是则清空标题和排序
    let title = carousel.title;
    let order = carousel.order;

    // 如果标题是默认的"轮播图1"、"轮播图2"等格式，且没有图片，则清空标题
    if (title && title.match(/^轮播图\d+$/) && !carousel.imageUrl) {
      title = '';
    }

    // 如果是空状态的轮播图（没有图片），则清空排序显示
    if (!carousel.imageUrl) {
      order = '';
    }

    this.setData({
      showEditForm: true,
      currentCarousel: carousel,
      formData: {
        title: title,
        order: order,
        imageUrl: carousel.imageUrl
      },
      tempImagePath: ''
    });
  },

  // 关闭表单
  closeForm() {
    this.setData({
      showAddForm: false,
      showEditForm: false,
      currentCarousel: null,
      formData: {
        title: '',
        order: '',
        imageUrl: ''
      },
      tempImagePath: ''
    });
  },

  // 表单输入
  inputChange(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;
    
    this.setData({
      [`formData.${field}`]: value
    });
  },

  // 选择图片
  chooseImage() {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: res => {
        const tempFilePath = res.tempFilePaths[0];
        
        this.setData({
          tempImagePath: tempFilePath
        });
      }
    });
  },

  // 上传图片
  uploadImage() {
    if (!this.data.tempImagePath) {
      return Promise.resolve(this.data.formData.imageUrl);
    }
    
    const cloudPath = `${this.data.uploadPath}${new Date().getTime()}_${Math.floor(Math.random() * 1000)}.jpg`;
    
    wx.showLoading({
      title: '上传图片中...',
    });
    
    return new Promise((resolve, reject) => {
      wx.cloud.uploadFile({
        cloudPath: cloudPath,
        filePath: this.data.tempImagePath,
        success: res => {
          const fileID = res.fileID;
          resolve(fileID);
        },
        fail: err => {
          console.error('上传图片失败', err);
          reject(err);
        },
        complete: () => {
          wx.hideLoading();
        }
      });
    });
  },

  // 提交添加表单
  async submitAddForm() {
    const { title, order } = this.data.formData;
    
    if (!title) {
      wx.showToast({
        title: '请填写轮播图标题',
        icon: 'none'
      });
      return;
    }
    
    if (!this.data.tempImagePath) {
      wx.showToast({
        title: '请选择轮播图图片',
        icon: 'none'
      });
      return;
    }
    
    try {
      // 上传图片
      const imageUrl = await this.uploadImage();
      
      // 添加轮播图
      wx.showLoading({
        title: '添加中...',
      });
      
      wx.cloud.callFunction({
        name: 'appointmentManager',
        data: {
          action: 'addCarousel',
          type: 'admin',
          title: title,
          imageUrl: imageUrl,
          order: Number(order) || this.data.carousels.length + 1
        },
        success: res => {
          wx.hideLoading();
          
          console.log('添加轮播图结果:', res);
          
          if (res.result && res.result.success) {
            wx.showToast({
              title: '添加成功',
              icon: 'success'
            });
            
            this.closeForm();
            this.fetchCarousels();
          } else {
            wx.showToast({
              title: res.result?.message || '添加失败',
              icon: 'none',
              duration: 2000
            });
            
            console.error('添加轮播图失败:', res.result);
          }
        },
        fail: err => {
          wx.hideLoading();
          console.error('添加轮播图云函数调用失败', err);
          
          wx.showToast({
            title: '网络异常，请稍后重试',
            icon: 'none',
            duration: 2000
          });
        }
      });
    } catch (err) {
      wx.hideLoading();
      console.error('提交表单失败', err);
      
      wx.showToast({
        title: '上传图片失败，请重试',
        icon: 'none',
        duration: 2000
      });
    }
  },

  // 提交编辑表单
  async submitEditForm() {
    const { title, order } = this.data.formData;
    const { currentCarousel } = this.data;
    
    if (!title || !currentCarousel) {
      wx.showToast({
        title: '请填写轮播图标题',
        icon: 'none'
      });
      return;
    }
    
    try {
      // 上传图片（如果有新图片）
      let imageUrl = this.data.formData.imageUrl;
      if (this.data.tempImagePath) {
        imageUrl = await this.uploadImage();
      }
      
      // 更新轮播图
      wx.showLoading({
        title: '更新中...',
      });
      
      wx.cloud.callFunction({
        name: 'appointmentManager',
        data: {
          action: 'updateCarousel',
          type: 'admin',
          carouselId: currentCarousel.id,
          title: title,
          imageUrl: imageUrl,
          order: Number(order) || currentCarousel.order
        },
        success: res => {
          wx.hideLoading();
          
          if (res.result && res.result.success) {
            wx.showToast({
              title: '更新成功',
              icon: 'success'
            });
            
            this.closeForm();
            this.fetchCarousels();
          } else {
            wx.showToast({
              title: res.result?.message || '更新失败',
              icon: 'none'
            });
          }
        },
        fail: err => {
          wx.hideLoading();
          console.error('更新轮播图失败', err);
          
          wx.showToast({
            title: '网络异常，请稍后重试',
            icon: 'none'
          });
        }
      });
    } catch (err) {
      wx.hideLoading();
      console.error('提交表单失败', err);
      
      wx.showToast({
        title: '上传图片失败，请重试',
        icon: 'none'
      });
    }
  },

  // 删除轮播图
  deleteCarousel(e) {
    const { carousel } = e.currentTarget.dataset;
    
    wx.showModal({
      title: '确认删除',
      content: `确定要删除轮播图"${carousel.title}"吗？`,
      success: res => {
        if (res.confirm) {
          wx.showLoading({
            title: '删除中...',
          });
          
          wx.cloud.callFunction({
            name: 'appointmentManager',
            data: {
              action: 'deleteCarousel',
              type: 'admin',
              carouselId: carousel.id
            },
            success: res => {
              wx.hideLoading();
              
              if (res.result && res.result.success) {
                wx.showToast({
                  title: '删除成功',
                  icon: 'success'
                });
                
                this.fetchCarousels();
              } else {
                wx.showToast({
                  title: res.result?.message || '删除失败',
                  icon: 'none'
                });
              }
            },
            fail: err => {
              wx.hideLoading();
              console.error('删除轮播图失败', err);
              
              wx.showToast({
                title: '网络异常，请稍后重试',
                icon: 'none'
              });
            }
          });
        }
      }
    });
  },

  // 上移轮播图
  moveUp(e) {
    const { carousel } = e.currentTarget.dataset;
    
    if (carousel.order <= 1) {
      return; // 已经是第一个，无法上移
    }
    
    const prevCarousel = this.data.carousels.find(item => item.order === carousel.order - 1);
    
    if (!prevCarousel) {
      return;
    }
    
    wx.showLoading({
      title: '调整中...',
    });
    
    // 更新当前轮播图的顺序
    wx.cloud.callFunction({
      name: 'appointmentManager',
      data: {
        action: 'updateCarousel',
        type: 'admin',
        carouselId: carousel.id,
        order: carousel.order - 1
      },
      success: () => {
        // 更新前一个轮播图的顺序
        wx.cloud.callFunction({
          name: 'appointmentManager',
          data: {
            action: 'updateCarousel',
            type: 'admin',
            carouselId: prevCarousel.id,
            order: prevCarousel.order + 1
          },
          success: () => {
            wx.hideLoading();
            this.fetchCarousels();
          },
          fail: err => {
            wx.hideLoading();
            console.error('调整顺序失败', err);
            wx.showToast({
              title: '调整顺序失败',
              icon: 'none'
            });
          }
        });
      },
      fail: err => {
        wx.hideLoading();
        console.error('调整顺序失败', err);
        wx.showToast({
          title: '调整顺序失败',
          icon: 'none'
        });
      }
    });
  },

  // 下移轮播图
  moveDown(e) {
    const { carousel } = e.currentTarget.dataset;
    
    if (carousel.order >= this.data.carousels.length) {
      return; // 已经是最后一个，无法下移
    }
    
    const nextCarousel = this.data.carousels.find(item => item.order === carousel.order + 1);
    
    if (!nextCarousel) {
      return;
    }
    
    wx.showLoading({
      title: '调整中...',
    });
    
    // 更新当前轮播图的顺序
    wx.cloud.callFunction({
      name: 'appointmentManager',
      data: {
        action: 'updateCarousel',
        type: 'admin',
        carouselId: carousel.id,
        order: carousel.order + 1
      },
      success: () => {
        // 更新下一个轮播图的顺序
        wx.cloud.callFunction({
          name: 'appointmentManager',
          data: {
            action: 'updateCarousel',
            type: 'admin',
            carouselId: nextCarousel.id,
            order: nextCarousel.order - 1
          },
          success: () => {
            wx.hideLoading();
            this.fetchCarousels();
          },
          fail: err => {
            wx.hideLoading();
            console.error('调整顺序失败', err);
            wx.showToast({
              title: '调整顺序失败',
              icon: 'none'
            });
          }
        });
      },
      fail: err => {
        wx.hideLoading();
        console.error('调整顺序失败', err);
        wx.showToast({
          title: '调整顺序失败',
          icon: 'none'
        });
      }
    });
  },

  // 返回上一页
  navigateBack() {
    wx.navigateBack();
  }
}) 