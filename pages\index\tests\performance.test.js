/**
 * 性能测试
 * 测试系统性能指标、内存使用、响应时间等
 */

const MainController = require('../modules/main-controller');
const UnifiedErrorHandler = require('../modules/unified-error-handler');
const APICompatibility = require('../modules/api-compatibility');
const { createMockPageContext, createMockVideoList, waitFor } = require('./test-utils');

// Mock performance API
global.performance = {
  now: jest.fn(() => Date.now()),
  mark: jest.fn(),
  measure: jest.fn(),
  getEntriesByType: jest.fn(() => []),
  memory: {
    usedJSHeapSize: 10 * 1024 * 1024, // 10MB
    totalJSHeapSize: 50 * 1024 * 1024, // 50MB
    jsHeapSizeLimit: 100 * 1024 * 1024 // 100MB
  }
};

// Mock wx API with performance tracking
global.wx = {
  showToast: jest.fn(),
  hideToast: jest.fn(),
  showLoading: jest.fn(),
  hideLoading: jest.fn(),
  stopPullDownRefresh: jest.fn(),
  pageScrollTo: jest.fn(),
  createSelectorQuery: jest.fn(() => ({
    select: jest.fn(() => ({
      node: jest.fn(() => ({
        exec: jest.fn((callback) => {
          setTimeout(() => callback([{ node: { scrollTop: 0 } }]), 1);
        })
      })),
      boundingClientRect: jest.fn(() => ({
        exec: jest.fn((callback) => {
          setTimeout(() => callback([{ top: 0, left: 0, width: 100, height: 100 }]), 1);
        })
      }))
    }))
  })),
  getSystemInfoSync: jest.fn(() => ({
    platform: 'devtools',
    version: '1.0.0',
    statusBarHeight: 20,
    screenHeight: 800,
    screenWidth: 375
  })),
  getStorageSync: jest.fn(),
  setStorageSync: jest.fn(),
  onNetworkStatusChange: jest.fn(),
  onUnhandledRejection: jest.fn(),
  onError: jest.fn(),
  getNetworkType: jest.fn((options) => {
    setTimeout(() => options.success({ networkType: 'wifi' }), 1);
  }),
  getPerformance: jest.fn(() => global.performance)
};

// Mock getCurrentPages
global.getCurrentPages = jest.fn(() => [
  { route: 'pages/index/index' }
]);

// Mock getApp
global.getApp = jest.fn(() => ({
  globalData: { 
    version: '1.0.0',
    currentTabIndex: 1
  }
}));

/**
 * 性能测试工具函数
 */
class PerformanceTestUtils {
  /**
   * 测量函数执行时间
   * @param {Function} fn - 要测量的函数
   * @param {Array} args - 函数参数
   * @returns {Promise<{result: any, duration: number}>}
   */
  static async measureExecutionTime(fn, args = []) {
    const startTime = performance.now();
    const result = await fn.apply(null, args);
    const endTime = performance.now();
    
    return {
      result,
      duration: endTime - startTime
    };
  }

  /**
   * 测量内存使用
   * @returns {Object} 内存使用信息
   */
  static measureMemoryUsage() {
    if (performance.memory) {
      return {
        used: performance.memory.usedJSHeapSize,
        total: performance.memory.totalJSHeapSize,
        limit: performance.memory.jsHeapSizeLimit,
        usagePercent: (performance.memory.usedJSHeapSize / performance.memory.totalJSHeapSize * 100).toFixed(2)
      };
    }
    return { used: 0, total: 0, limit: 0, usagePercent: '0' };
  }

  /**
   * 执行压力测试
   * @param {Function} fn - 要测试的函数
   * @param {number} iterations - 迭代次数
   * @param {number} concurrency - 并发数
   * @returns {Promise<Object>} 测试结果
   */
  static async stressTest(fn, iterations = 100, concurrency = 10) {
    const results = {
      totalIterations: iterations,
      concurrency: concurrency,
      durations: [],
      errors: [],
      startTime: performance.now(),
      endTime: 0,
      averageDuration: 0,
      minDuration: Infinity,
      maxDuration: 0,
      successRate: 0
    };

    const batches = Math.ceil(iterations / concurrency);
    
    for (let batch = 0; batch < batches; batch++) {
      const batchPromises = [];
      const batchSize = Math.min(concurrency, iterations - batch * concurrency);
      
      for (let i = 0; i < batchSize; i++) {
        batchPromises.push(
          this.measureExecutionTime(fn).then(({ duration }) => {
            results.durations.push(duration);
            results.minDuration = Math.min(results.minDuration, duration);
            results.maxDuration = Math.max(results.maxDuration, duration);
          }).catch(error => {
            results.errors.push(error);
          })
        );
      }
      
      await Promise.all(batchPromises);
    }

    results.endTime = performance.now();
    results.totalDuration = results.endTime - results.startTime;
    results.averageDuration = results.durations.reduce((sum, d) => sum + d, 0) / results.durations.length;
    results.successRate = ((iterations - results.errors.length) / iterations * 100).toFixed(2);

    return results;
  }

  /**
   * 创建大量测试数据
   * @param {number} count - 数据数量
   * @returns {Array} 测试数据
   */
  static createLargeDataset(count = 1000) {
    return createMockVideoList(count);
  }
}

describe('Performance Tests 性能测试', () => {
  let mainController;
  let mockPageContext;

  beforeEach(() => {
    jest.clearAllMocks();
    mockPageContext = createMockPageContext();
  });

  afterEach(() => {
    if (mainController) {
      mainController.destroy();
    }
  });

  describe('系统初始化性能测试', () => {
    test('主控制器初始化应该在合理时间内完成', async () => {
      mainController = new MainController(mockPageContext);
      
      const { duration } = await PerformanceTestUtils.measureExecutionTime(
        () => mainController.init()
      );
      
      console.log(`主控制器初始化耗时: ${duration.toFixed(2)}ms`);
      
      // 初始化应该在2秒内完成
      expect(duration).toBeLessThan(2000);
      expect(mainController.initialized).toBe(true);
    });

    test('模块注册应该高效完成', async () => {
      mainController = new MainController(mockPageContext);
      
      const { duration } = await PerformanceTestUtils.measureExecutionTime(
        () => mainController.registerModules()
      );
      
      console.log(`模块注册耗时: ${duration.toFixed(2)}ms`);
      
      // 模块注册应该在100ms内完成
      expect(duration).toBeLessThan(100);
      expect(mainController.modules.size).toBeGreaterThan(0);
    });

    test('API兼容层初始化应该高效完成', async () => {
      const apiCompatibility = new APICompatibility(
        { getModule: jest.fn() }, 
        mockPageContext
      );
      
      const { duration } = await PerformanceTestUtils.measureExecutionTime(
        () => apiCompatibility.init()
      );
      
      console.log(`API兼容层初始化耗时: ${duration.toFixed(2)}ms`);
      
      // API兼容层初始化应该在50ms内完成
      expect(duration).toBeLessThan(50);
      expect(apiCompatibility.apiMappings.size).toBeGreaterThan(0);
      
      apiCompatibility.destroy();
    });

    test('错误处理器初始化应该高效完成', async () => {
      const errorHandler = new UnifiedErrorHandler(mockPageContext);
      
      const { duration } = await PerformanceTestUtils.measureExecutionTime(
        () => errorHandler.init()
      );
      
      console.log(`错误处理器初始化耗时: ${duration.toFixed(2)}ms`);
      
      // 错误处理器初始化应该在100ms内完成
      expect(duration).toBeLessThan(100);
      expect(errorHandler.initialized).toBe(true);
      
      errorHandler.destroy();
    });
  });

  describe('API调用性能测试', () => {
    beforeEach(async () => {
      mainController = new MainController(mockPageContext);
      await mainController.init();
    });

    test('API代理调用应该高效执行', async () => {
      const iterations = 1000;
      
      const { duration } = await PerformanceTestUtils.measureExecutionTime(async () => {
        for (let i = 0; i < iterations; i++) {
          mockPageContext.formatCount(i * 100);
          mockPageContext.sanitizeVideoData({ id: `test_${i}` });
        }
      });
      
      console.log(`${iterations}次API调用耗时: ${duration.toFixed(2)}ms`);
      console.log(`平均每次调用耗时: ${(duration / iterations).toFixed(3)}ms`);
      
      // 1000次API调用应该在100ms内完成
      expect(duration).toBeLessThan(100);
      
      // 平均每次调用应该在0.1ms内完成
      expect(duration / iterations).toBeLessThan(0.1);
    });

    test('并发API调用应该高效处理', async () => {
      const concurrentCalls = 100;
      
      const { duration } = await PerformanceTestUtils.measureExecutionTime(async () => {
        const promises = [];
        
        for (let i = 0; i < concurrentCalls; i++) {
          promises.push(Promise.resolve(mockPageContext.loadVideoList()));
          promises.push(Promise.resolve(mockPageContext.formatCount(i * 1000)));
          promises.push(Promise.resolve(mockPageContext.handleScroll({ detail: { scrollTop: i * 10 } })));
        }
        
        await Promise.all(promises);
      });
      
      console.log(`${concurrentCalls * 3}个并发API调用耗时: ${duration.toFixed(2)}ms`);
      
      // 300个并发API调用应该在200ms内完成
      expect(duration).toBeLessThan(200);
    });
  });

  describe('错误处理性能测试', () => {
    let errorHandler;

    beforeEach(async () => {
      mainController = new MainController(mockPageContext);
      await mainController.init();
      errorHandler = mainController.unifiedErrorHandler;
    });

    test('单个错误处理应该高效完成', async () => {
      const testError = new Error('性能测试错误');
      
      const { duration } = await PerformanceTestUtils.measureExecutionTime(
        () => errorHandler.handleError(testError, { source: 'performance_test' })
      );
      
      console.log(`单个错误处理耗时: ${duration.toFixed(2)}ms`);
      
      // 单个错误处理应该在10ms内完成
      expect(duration).toBeLessThan(10);
    });

    test('大量错误处理应该保持性能', async () => {
      const errorCount = 100;
      
      const { duration } = await PerformanceTestUtils.measureExecutionTime(async () => {
        const promises = [];
        
        for (let i = 0; i < errorCount; i++) {
          const error = new Error(`批量测试错误 ${i}`);
          promises.push(errorHandler.handleError(error, { 
            source: 'batch_test',
            index: i
          }));
        }
        
        await Promise.all(promises);
      });
      
      console.log(`${errorCount}个错误处理耗时: ${duration.toFixed(2)}ms`);
      console.log(`平均每个错误处理耗时: ${(duration / errorCount).toFixed(2)}ms`);
      
      // 100个错误处理应该在1秒内完成
      expect(duration).toBeLessThan(1000);
      
      // 平均每个错误处理应该在10ms内完成
      expect(duration / errorCount).toBeLessThan(10);
      
      // 验证错误统计
      const errorStats = errorHandler.getErrorStats();
      expect(errorStats.totalErrors).toBe(errorCount);
    });

    test('错误分析性能测试', async () => {
      // 先添加一些错误数据
      for (let i = 0; i < 50; i++) {
        errorHandler.errorStats.errorsByType.set(`ERROR_TYPE_${i}`, i + 1);
      }
      
      const { duration } = await PerformanceTestUtils.measureExecutionTime(
        () => errorHandler.performErrorAnalysis()
      );
      
      console.log(`错误分析耗时: ${duration.toFixed(2)}ms`);
      
      // 错误分析应该在50ms内完成
      expect(duration).toBeLessThan(50);
    });
  });

  describe('大数据量处理性能测试', () => {
    beforeEach(async () => {
      mainController = new MainController(mockPageContext);
      await mainController.init();
    });

    test('大量视频数据处理性能', async () => {
      const largeVideoList = PerformanceTestUtils.createLargeDataset(1000);
      
      const { duration } = await PerformanceTestUtils.measureExecutionTime(async () => {
        // 模拟批量数据处理
        for (let i = 0; i < largeVideoList.length; i += 100) {
          const batch = largeVideoList.slice(i, i + 100);
          
          // 处理每个批次
          batch.forEach(video => {
            mockPageContext.sanitizeVideoData(video);
            mockPageContext.formatCount(video.playCount);
          });
          
          // 模拟异步处理间隔
          await waitFor(1);
        }
      });
      
      console.log(`1000个视频数据处理耗时: ${duration.toFixed(2)}ms`);
      
      // 1000个视频数据处理应该在5秒内完成
      expect(duration).toBeLessThan(5000);
    });

    test('大量状态查询性能', async () => {
      const queryCount = 1000;
      
      const { duration } = await PerformanceTestUtils.measureExecutionTime(() => {
        for (let i = 0; i < queryCount; i++) {
          mainController.getModulesStatus();
          mainController.getErrorStats();
        }
      });
      
      console.log(`${queryCount * 2}次状态查询耗时: ${duration.toFixed(2)}ms`);
      
      // 2000次状态查询应该在100ms内完成
      expect(duration).toBeLessThan(100);
    });
  });

  describe('内存使用性能测试', () => {
    test('系统初始化内存使用', async () => {
      const initialMemory = PerformanceTestUtils.measureMemoryUsage();
      
      mainController = new MainController(mockPageContext);
      await mainController.init();
      
      const afterInitMemory = PerformanceTestUtils.measureMemoryUsage();
      
      console.log('初始化前内存使用:', initialMemory);
      console.log('初始化后内存使用:', afterInitMemory);
      
      // 内存使用应该在合理范围内
      expect(parseFloat(afterInitMemory.usagePercent)).toBeLessThan(80);
    });

    test('大量操作后内存使用', async () => {
      mainController = new MainController(mockPageContext);
      await mainController.init();
      
      const initialMemory = PerformanceTestUtils.measureMemoryUsage();
      
      // 执行大量操作
      for (let i = 0; i < 1000; i++) {
        mockPageContext.loadVideoList();
        mockPageContext.handleScroll({ detail: { scrollTop: i } });
        mockPageContext.formatCount(i * 100);
        
        if (i % 100 === 0) {
          await waitFor(1); // 给垃圾回收一些时间
        }
      }
      
      const afterOperationsMemory = PerformanceTestUtils.measureMemoryUsage();
      
      console.log('操作前内存使用:', initialMemory);
      console.log('操作后内存使用:', afterOperationsMemory);
      
      // 内存增长应该在合理范围内
      const memoryGrowth = afterOperationsMemory.used - initialMemory.used;
      console.log('内存增长:', (memoryGrowth / 1024 / 1024).toFixed(2), 'MB');
      
      // 内存增长应该小于10MB
      expect(memoryGrowth).toBeLessThan(10 * 1024 * 1024);
    });

    test('资源清理后内存释放', async () => {
      mainController = new MainController(mockPageContext);
      await mainController.init();
      
      // 执行一些操作
      for (let i = 0; i < 100; i++) {
        mockPageContext.loadVideoList();
        mockPageContext.handleScroll({ detail: { scrollTop: i } });
      }
      
      const beforeCleanupMemory = PerformanceTestUtils.measureMemoryUsage();
      
      // 清理资源
      mainController.destroy();
      
      // 等待垃圾回收
      await waitFor(100);
      
      const afterCleanupMemory = PerformanceTestUtils.measureMemoryUsage();
      
      console.log('清理前内存使用:', beforeCleanupMemory);
      console.log('清理后内存使用:', afterCleanupMemory);
      
      // 内存使用应该有所减少或保持稳定
      expect(afterCleanupMemory.used).toBeLessThanOrEqual(beforeCleanupMemory.used * 1.1);
    });
  });

  describe('压力测试', () => {
    beforeEach(async () => {
      mainController = new MainController(mockPageContext);
      await mainController.init();
    });

    test('API调用压力测试', async () => {
      const testResults = await PerformanceTestUtils.stressTest(
        () => mockPageContext.formatCount(Math.floor(Math.random() * 10000)),
        500, // 500次迭代
        20   // 20个并发
      );
      
      console.log('API调用压力测试结果:');
      console.log(`- 总迭代次数: ${testResults.totalIterations}`);
      console.log(`- 并发数: ${testResults.concurrency}`);
      console.log(`- 总耗时: ${testResults.totalDuration.toFixed(2)}ms`);
      console.log(`- 平均耗时: ${testResults.averageDuration.toFixed(2)}ms`);
      console.log(`- 最小耗时: ${testResults.minDuration.toFixed(2)}ms`);
      console.log(`- 最大耗时: ${testResults.maxDuration.toFixed(2)}ms`);
      console.log(`- 成功率: ${testResults.successRate}%`);
      console.log(`- 错误数: ${testResults.errors.length}`);
      
      // 验证性能指标
      expect(testResults.successRate).toBe('100.00');
      expect(testResults.averageDuration).toBeLessThan(5);
      expect(testResults.maxDuration).toBeLessThan(20);
      expect(testResults.errors.length).toBe(0);
    });

    test('错误处理压力测试', async () => {
      const errorHandler = mainController.unifiedErrorHandler;
      
      const testResults = await PerformanceTestUtils.stressTest(
        () => {
          const error = new Error(`压力测试错误 ${Math.random()}`);
          return errorHandler.handleError(error, { source: 'stress_test' });
        },
        200, // 200次迭代
        10   // 10个并发
      );
      
      console.log('错误处理压力测试结果:');
      console.log(`- 总迭代次数: ${testResults.totalIterations}`);
      console.log(`- 并发数: ${testResults.concurrency}`);
      console.log(`- 总耗时: ${testResults.totalDuration.toFixed(2)}ms`);
      console.log(`- 平均耗时: ${testResults.averageDuration.toFixed(2)}ms`);
      console.log(`- 成功率: ${testResults.successRate}%`);
      console.log(`- 错误数: ${testResults.errors.length}`);
      
      // 验证性能指标
      expect(testResults.successRate).toBe('100.00');
      expect(testResults.averageDuration).toBeLessThan(20);
      expect(testResults.errors.length).toBe(0);
      
      // 验证错误统计
      const errorStats = errorHandler.getErrorStats();
      expect(errorStats.totalErrors).toBe(testResults.totalIterations);
    });

    test('混合操作压力测试', async () => {
      const operations = [
        () => mockPageContext.loadVideoList(),
        () => mockPageContext.handleScroll({ detail: { scrollTop: Math.random() * 1000 } }),
        () => mockPageContext.formatCount(Math.random() * 10000),
        () => mockPageContext.sanitizeVideoData({ id: `test_${Math.random()}` }),
        () => mockPageContext.onSearchInput({ detail: { value: `搜索${Math.random()}` } })
      ];
      
      const testResults = await PerformanceTestUtils.stressTest(
        () => {
          const operation = operations[Math.floor(Math.random() * operations.length)];
          return operation();
        },
        300, // 300次迭代
        15   // 15个并发
      );
      
      console.log('混合操作压力测试结果:');
      console.log(`- 总迭代次数: ${testResults.totalIterations}`);
      console.log(`- 并发数: ${testResults.concurrency}`);
      console.log(`- 总耗时: ${testResults.totalDuration.toFixed(2)}ms`);
      console.log(`- 平均耗时: ${testResults.averageDuration.toFixed(2)}ms`);
      console.log(`- 成功率: ${testResults.successRate}%`);
      
      // 验证性能指标
      expect(testResults.successRate).toBe('100.00');
      expect(testResults.averageDuration).toBeLessThan(10);
      expect(testResults.totalDuration).toBeLessThan(5000);
    });
  });

  describe('响应时间基准测试', () => {
    beforeEach(async () => {
      mainController = new MainController(mockPageContext);
      await mainController.init();
    });

    test('关键操作响应时间基准', async () => {
      const benchmarks = {
        '系统初始化': async () => {
          const controller = new MainController(createMockPageContext());
          await controller.init();
          controller.destroy();
        },
        '视频列表加载': () => mockPageContext.loadVideoList(),
        '视频数据格式化': () => mockPageContext.formatCount(12345),
        '视频数据清理': () => mockPageContext.sanitizeVideoData({ 
          id: 'test', 
          title: '测试视频' 
        }),
        '滚动处理': () => mockPageContext.handleScroll({ 
          detail: { scrollTop: 100 } 
        }),
        '搜索输入': () => mockPageContext.onSearchInput({ 
          detail: { value: '测试搜索' } 
        }),
        '错误处理': () => mainController.unifiedErrorHandler.handleError(
          new Error('基准测试错误'), 
          { source: 'benchmark' }
        )
      };
      
      console.log('\n=== 响应时间基准测试结果 ===');
      
      for (const [name, operation] of Object.entries(benchmarks)) {
        const { duration } = await PerformanceTestUtils.measureExecutionTime(operation);
        console.log(`${name}: ${duration.toFixed(2)}ms`);
        
        // 设置基准时间限制
        const timeLimit = name === '系统初始化' ? 2000 : 50;
        expect(duration).toBeLessThan(timeLimit);
      }
    });
  });

  describe('性能回归测试', () => {
    test('性能指标不应该退化', async () => {
      // 这个测试用于确保重构后的性能不低于重构前
      const performanceTargets = {
        systemInitTime: 2000,      // 系统初始化时间 < 2秒
        apiCallTime: 5,            // API调用时间 < 5ms
        errorHandlingTime: 10,     // 错误处理时间 < 10ms
        memoryUsagePercent: 80,    // 内存使用率 < 80%
        concurrentOperations: 200  // 并发操作响应时间 < 200ms
      };
      
      mainController = new MainController(mockPageContext);
      
      // 测试系统初始化时间
      const { duration: initTime } = await PerformanceTestUtils.measureExecutionTime(
        () => mainController.init()
      );
      expect(initTime).toBeLessThan(performanceTargets.systemInitTime);
      
      // 测试API调用时间
      const { duration: apiTime } = await PerformanceTestUtils.measureExecutionTime(
        () => mockPageContext.formatCount(1000)
      );
      expect(apiTime).toBeLessThan(performanceTargets.apiCallTime);
      
      // 测试错误处理时间
      const { duration: errorTime } = await PerformanceTestUtils.measureExecutionTime(
        () => mainController.unifiedErrorHandler.handleError(
          new Error('回归测试'), 
          { source: 'regression' }
        )
      );
      expect(errorTime).toBeLessThan(performanceTargets.errorHandlingTime);
      
      // 测试内存使用
      const memoryUsage = PerformanceTestUtils.measureMemoryUsage();
      expect(parseFloat(memoryUsage.usagePercent)).toBeLessThan(performanceTargets.memoryUsagePercent);
      
      console.log('\n=== 性能回归测试通过 ===');
      console.log(`系统初始化时间: ${initTime.toFixed(2)}ms (目标: <${performanceTargets.systemInitTime}ms)`);
      console.log(`API调用时间: ${apiTime.toFixed(2)}ms (目标: <${performanceTargets.apiCallTime}ms)`);
      console.log(`错误处理时间: ${errorTime.toFixed(2)}ms (目标: <${performanceTargets.errorHandlingTime}ms)`);
      console.log(`内存使用率: ${memoryUsage.usagePercent}% (目标: <${performanceTargets.memoryUsagePercent}%)`);
    });
  });
});