const app = getApp()

Page({
  /**
   * 页面的初始数据
   */
  data: {
    staffInfo: null,
    records: [],
    loading: false,
    page: 1,
    pageSize: 20,
    hasMore: true,
    totalCount: 0,
    // 筛选条件
    startDate: '',
    endDate: '',
    dateFilter: false,
    // 充值统计数据
    statsLoading: true,
    totalRechargeAmount: 0,
    totalRechargeCount: 0,
    todayRechargeAmount: 0,
    yesterdayRechargeAmount: 0,
    weekRechargeAmount: 0,
    monthRechargeAmount: 0
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    const staffInfo = wx.getStorageSync('staffInfo');
    if (!staffInfo) {
      this.redirectToLogin();
      return;
    }
    
    this.setData({ staffInfo });
    this.loadRechargeRecords();
    this.loadRechargeStats();
  },

  /**
   * 加载充值核销记录
   */
  loadRechargeRecords(refresh = false) {
    const { staffInfo, page, pageSize, startDate, endDate, dateFilter } = this.data;
    
    if (refresh) {
      this.setData({
        page: 1,
        records: [],
        hasMore: true
      });
    }
    
    if (!this.data.hasMore && !refresh) {
      return;
    }
    
    this.setData({ loading: true });
    
    const queryPage = refresh ? 1 : page;
    
    // 构建查询参数
    const queryData = {
      operatorId: staffInfo._id,
      page: queryPage,
      pageSize: pageSize
    };
    
    // 添加日期过滤条件（如果有）
    if (dateFilter && startDate && endDate) {
      queryData.startDate = startDate;
      queryData.endDate = endDate;
    }
    
    wx.cloud.callFunction({
      name: 'rechargeManager',
      data: {
        type: 'staff',
        action: 'getOperatorRechargeRecords',
        data: queryData
      }
    }).then(res => {
      console.log('充值核销记录结果：', res.result);
      
      if (res.result && res.result.code === 0) {
        const newRecords = res.result.data.records || [];
        const total = res.result.data.total || 0;
        
        // 格式化记录中的时间
        const formattedRecords = newRecords.map(record => {
          // 同时检查verifiedTime和verifyTime字段
          if (record.verifiedTime || record.verifyTime) {
            const date = new Date(record.verifiedTime || record.verifyTime);
            record.formattedDate = `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`;
            record.formattedTime = `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
          }
          
          // 确保金额字段正确
          if (record.amount === undefined && record.price !== undefined) {
            record.amount = record.price;
          }
          
          // 确保赠送金额字段正确
          if (record.bonusAmount === undefined && record.bonus !== undefined) {
            record.bonusAmount = record.bonus;
          }
          
          // 确保推广佣金字段正确 - 检查多个可能的字段名
          if (record.promoterCommission !== undefined) {
            record.promoterCommission = parseFloat(record.promoterCommission).toFixed(2);
          } else if (record.promotionCommission !== undefined) {
            // 如果promoterCommission不存在，尝试使用promotionCommission
            record.promoterCommission = parseFloat(record.promotionCommission).toFixed(2);
          } else {
            record.promoterCommission = '0.00';
          }
          
          // 记录充值金额和赠送金额，方便调试
          // console.log('记录金额信息:', record._id, record.promoterCommission);
          
          return record;
        });
        
        // 更新数据
        this.setData({
          records: refresh ? formattedRecords : [...this.data.records, ...formattedRecords],
          totalCount: total,
          hasMore: formattedRecords.length === pageSize,
          page: queryPage + 1,
          loading: false
        });
      } else {
        wx.showToast({
          title: res.result?.message || '获取记录失败',
          icon: 'none'
        });
        this.setData({ loading: false });
      }
    }).catch(err => {
      console.error('获取充值核销记录失败：', err);
      wx.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      });
      this.setData({ loading: false });
    });
  },

  /**
   * 加载充值统计数据
   */
  loadRechargeStats() {
    const { staffInfo } = this.data;
    
    if (!staffInfo || !staffInfo._id) {
      wx.showToast({
        title: '员工信息不完整',
        icon: 'none'
      });
      this.setData({ statsLoading: false });
      return;
    }
    
    this.setData({ statsLoading: true });
    
    console.log('开始获取充值统计数据，员工ID:', staffInfo._id);
    
    wx.cloud.callFunction({
      name: 'staffManager',
      data: {
        type: 'staff',
        action: 'getStaffRechargeStats',
        data: {
          staffId: staffInfo._id
        }
      }
    }).then(res => {
      console.log('充值统计结果：', JSON.stringify(res.result));
      
      if (res.result && res.result.code === 0) {
        const statsData = res.result.data;
        
        console.log('充值统计数据详情:', {
          totalRechargeAmount: statsData.totalRechargeAmount,
          totalRechargeCount: statsData.totalRechargeCount,
          dailyStats: statsData.dailyStats ? statsData.dailyStats.length : 0
        });
        
        // 提取统计数据
        let todayAmount = 0;
        let yesterdayAmount = 0;
        let weekAmount = 0;
        let monthAmount = 0;
        
        // 获取当前日期（北京时间）
        const now = new Date();
        const today = formatDate(now);
        
        // 获取昨天日期
        const yesterday = new Date();
        yesterday.setDate(yesterday.getDate() - 1);
        const yesterdayStr = formatDate(yesterday);
        
        // 获取一周前的日期
        const weekAgo = new Date();
        weekAgo.setDate(weekAgo.getDate() - 7);
        
        // 获取一个月前的日期
        const monthAgo = new Date();
        monthAgo.setMonth(monthAgo.getMonth() - 1);
        
        console.log('日期范围:', {
          today: today,
          yesterday: yesterdayStr,
          weekAgo: formatDate(weekAgo),
          monthAgo: formatDate(monthAgo)
        });
        
        // 处理每日统计数据
        if (statsData.dailyStats && statsData.dailyStats.length > 0) {
          console.log('处理每日统计数据，共', statsData.dailyStats.length, '条');
          
          statsData.dailyStats.forEach(day => {
            console.log('处理日期:', day.date, '金额:', day.rechargeAmount);
            
            const dayDate = new Date(day.date);
            
            // 今日数据
            if (day.date === today) {
              todayAmount = day.rechargeAmount;
              console.log('匹配今日数据:', today, todayAmount);
            }
            
            // 昨日数据
            if (day.date === yesterdayStr) {
              yesterdayAmount = day.rechargeAmount;
              console.log('匹配昨日数据:', yesterdayStr, yesterdayAmount);
            }
            
            // 本周数据（最近7天）
            if (dayDate >= weekAgo) {
              weekAmount += day.rechargeAmount;
              console.log('累加本周数据:', day.date, day.rechargeAmount, '累计:', weekAmount);
            }
            
            // 本月数据（最近30天）
            if (dayDate >= monthAgo) {
              monthAmount += day.rechargeAmount;
              console.log('累加本月数据:', day.date, day.rechargeAmount, '累计:', monthAmount);
            }
          });
        } else {
          console.log('没有每日统计数据');
        }
        
        // 更新统计数据
        const updateData = {
          totalRechargeAmount: statsData.totalRechargeAmount || 0,
          totalRechargeCount: statsData.totalRechargeCount || 0,
          todayRechargeAmount: todayAmount,
          yesterdayRechargeAmount: yesterdayAmount,
          weekRechargeAmount: weekAmount,
          monthRechargeAmount: monthAmount,
          statsLoading: false
        };
        
        console.log('更新统计数据:', updateData);
        
        this.setData(updateData);
      } else {
        console.error('获取充值统计失败:', res.result);
        this.setData({ statsLoading: false });
      }
    }).catch(err => {
      console.error('获取充值统计失败：', err);
      this.setData({ statsLoading: false });
    });
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    this.loadRechargeRecords(true);
    this.loadRechargeStats();
    wx.stopPullDownRefresh();
  },

  /**
   * 加载更多
   */
  onReachBottom() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadRechargeRecords();
    }
  },

  /**
   * 设置开始日期
   */
  bindStartDateChange(e) {
    this.setData({
      startDate: e.detail.value
    });
  },

  /**
   * 设置结束日期
   */
  bindEndDateChange(e) {
    this.setData({
      endDate: e.detail.value
    });
  },

  /**
   * 应用日期筛选
   */
  applyDateFilter() {
    const { startDate, endDate } = this.data;
    
    if (!startDate || !endDate) {
      wx.showToast({
        title: '请选择开始和结束日期',
        icon: 'none'
      });
      return;
    }
    
    const start = new Date(startDate);
    const end = new Date(endDate);
    
    if (end < start) {
      wx.showToast({
        title: '结束日期不能早于开始日期',
        icon: 'none'
      });
      return;
    }
    
    this.setData({
      dateFilter: true
    }, () => {
      this.loadRechargeRecords(true);
    });
  },

  /**
   * 清除日期筛选
   */
  clearDateFilter() {
    this.setData({
      dateFilter: false,
      startDate: '',
      endDate: ''
    }, () => {
      this.loadRechargeRecords(true);
    });
  },

  /**
   * 返回上一页
   */
  goBack() {
    wx.navigateBack();
  },

  /**
   * 跳转到登录页
   */
  redirectToLogin() {
    wx.redirectTo({
      url: '/pages/staff/login/login'
    });
  },

  /**
   * 日期格式化辅助函数
   */
  formatDate(date) {
    const d = new Date(date);
    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const day = String(d.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }
})

// 日期格式化辅助函数
function formatDate(date) {
  const d = new Date(date);
  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const day = String(d.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
} 