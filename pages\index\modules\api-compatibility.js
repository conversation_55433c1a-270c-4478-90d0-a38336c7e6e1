/**
 * API兼容性模块
 * 确保重构后的模块化架构与原有API完全兼容
 * @version 1.0.0
 */

const { ERROR_TYPES } = require('../constants/index-constants');

class APICompatibility {
  constructor(mainController, pageContext) {
    this.mainController = mainController;
    this.page = pageContext;
    
    // API方法映射表
    this.apiMappings = new Map();
    
    // 兼容性检查结果
    this.compatibilityReport = {
      totalMethods: 0,
      mappedMethods: 0,
      unmappedMethods: [],
      errors: []
    };
  }

  /**
   * 初始化API兼容层
   */
  init() {
    try {
      // 减少API兼容层初始化日志
      // console.log('[APICompatibility] 初始化API兼容层');
      
      // 设置API映射
      this.setupAPIMappings();
      
      // 创建代理方法
      this.createProxyMethods();
      
      // 验证兼容性
      this.validateCompatibility();
      
      // 减少API兼容层初始化完成日志
      // console.log('[APICompatibility] API兼容层初始化完成');
    } catch (error) {
      console.error('[APICompatibility] 初始化失败:', error);
      throw error;
    }
  }

  /**
   * 设置API映射关系
   */
  setupAPIMappings() {
    try {
      // 页面生命周期方法映射
      this.apiMappings.set('onLoad', {
        module: 'MainController',
        method: 'handlePageLoad',
        type: 'lifecycle'
      });
      
      this.apiMappings.set('onShow', {
        module: 'MainController', 
        method: 'handlePageShow',
        type: 'lifecycle'
      });
      
      this.apiMappings.set('onHide', {
        module: 'MainController',
        method: 'handlePageHide', 
        type: 'lifecycle'
      });
      
      this.apiMappings.set('onUnload', {
        module: 'MainController',
        method: 'handlePageUnload',
        type: 'lifecycle'
      });

      // 视频列表相关方法映射
      this.apiMappings.set('loadVideoList', {
        module: 'VideoList',
        method: 'loadVideoList',
        type: 'business'
      });
      
      this.apiMappings.set('refreshVideoList', {
        module: 'VideoList', 
        method: 'refreshVideoList',
        type: 'business'
      });
      
      this.apiMappings.set('onPullDownRefresh', {
        module: 'VideoList',
        method: 'onPullDownRefresh',
        type: 'event'
      });
      
      this.apiMappings.set('onReachBottom', {
        module: 'VideoList',
        method: 'onReachBottom', 
        type: 'event'
      });

      // 视频播放相关方法映射
      this.apiMappings.set('handleVideoTap', {
        module: 'VideoPlayer',
        method: 'handleVideoTap',
        type: 'event'
      });
      
      this.apiMappings.set('onVideoPlay', {
        module: 'VideoPlayer',
        method: 'onVideoPlay',
        type: 'event'
      });
      
      this.apiMappings.set('onVideoPause', {
        module: 'VideoPlayer', 
        method: 'onVideoPause',
        type: 'event'
      });
      
      this.apiMappings.set('onVideoEnd', {
        module: 'VideoPlayer',
        method: 'onVideoEnd',
        type: 'event'
      });
      
      this.apiMappings.set('onVideoError', {
        module: 'VideoPlayer',
        method: 'onVideoError',
        type: 'event'
      });
      
      this.apiMappings.set('onVideoFullscreenChange', {
        module: 'VideoPlayer',
        method: 'onVideoFullscreenChange',
        type: 'event'
      });
      
      this.apiMappings.set('pauseAllVideos', {
        module: 'VideoPlayer',
        method: 'pauseAllVideos',
        type: 'business'
      });

      // 搜索相关方法映射
      this.apiMappings.set('initSearchComponent', {
        module: 'Search',
        method: 'initSearchComponent',
        type: 'business'
      });
      
      this.apiMappings.set('onSearchTap', {
        module: 'Search',
        method: 'onSearchTap',
        type: 'event'
      });
      
      this.apiMappings.set('onSearchInput', {
        module: 'Search',
        method: 'onSearchInput',
        type: 'event'
      });
      
      this.apiMappings.set('onSearchConfirm', {
        module: 'Search',
        method: 'onSearchConfirm',
        type: 'event'
      });
      
      this.apiMappings.set('onSearchFocus', {
        module: 'Search',
        method: 'onSearchFocus',
        type: 'event'
      });
      
      this.apiMappings.set('searchLocalVideos', {
        module: 'Search',
        method: 'searchLocalVideos',
        type: 'business'
      });
      
      this.apiMappings.set('clearSearch', {
        module: 'Search',
        method: 'clearSearch',
        type: 'business'
      });
      
      this.apiMappings.set('onSearchResultTap', {
        module: 'Search',
        method: 'onSearchResultTap',
        type: 'event'
      });

      // 分享相关方法映射
      this.apiMappings.set('onShareAppMessage', {
        module: 'Share',
        method: 'onShareAppMessage',
        type: 'event'
      });
      
      this.apiMappings.set('openSharedVideo', {
        module: 'Share',
        method: 'openSharedVideo',
        type: 'business'
      });

      // 导航相关方法映射
      this.apiMappings.set('handleScroll', {
        module: 'Navigation',
        method: 'handleScroll',
        type: 'event'
      });
      
      this.apiMappings.set('onNavbarControl', {
        module: 'Navigation',
        method: 'onNavbarControl',
        type: 'event'
      });

      // 触摸事件映射
      this.apiMappings.set('catchHorizontalMove', {
        module: 'Navigation',
        method: 'catchHorizontalMove',
        type: 'event'
      });

      // 工具方法映射
      this.apiMappings.set('formatCount', {
        module: 'VideoList',
        method: 'formatCount',
        type: 'utility'
      });
      
      this.apiMappings.set('sanitizeVideoData', {
        module: 'VideoList',
        method: 'sanitizeVideoData',
        type: 'utility'
      });
      
      this.apiMappings.set('getVideoUrl', {
        module: 'VideoList',
        method: 'getVideoUrl',
        type: 'utility'
      });

      // 其他事件方法映射
      this.apiMappings.set('onAuthorTap', {
        module: 'VideoList',
        method: 'onAuthorTap',
        type: 'event'
      });
      
      this.apiMappings.set('onVideoTap', {
        module: 'VideoPlayer',
        method: 'onVideoTap',
        type: 'event'
      });
      
      this.apiMappings.set('onVideoDetail', {
        module: 'VideoPlayer',
        method: 'onVideoDetail',
        type: 'event'
      });
      
      this.apiMappings.set('onVideoDetailClose', {
        module: 'VideoPlayer',
        method: 'onVideoDetailClose',
        type: 'event'
      });
      
      this.apiMappings.set('openVideoDetail', {
        module: 'VideoPlayer',
        method: 'openVideoDetail',
        type: 'business'
      });
      
      this.apiMappings.set('onMuteChange', {
        module: 'VideoPlayer',
        method: 'onMuteChange',
        type: 'event'
      });

      // 数据处理方法映射
      this.apiMappings.set('processPreloadedVideoData', {
        module: 'DataManager',
        method: 'processPreloadedVideoData',
        type: 'business'
      });
      
      this.apiMappings.set('loadMoreVideosInBackground', {
        module: 'DataManager',
        method: 'loadMoreVideosInBackground',
        type: 'business'
      });
      
      this.apiMappings.set('preloadNextPageUrls', {
        module: 'DataManager',
        method: 'preloadNextPageUrls',
        type: 'business'
      });

      // 减少API映射设置日志
      // console.log(`[APICompatibility] API映射设置完成，共 ${this.apiMappings.size} 个方法`);
    } catch (error) {
      console.error('[APICompatibility] 设置API映射失败:', error);
      throw error;
    }
  }

  /**
   * 创建代理方法
   */
  createProxyMethods() {
    try {
      // 减少代理方法创建日志
      // console.log('[APICompatibility] 创建代理方法');
      
      this.apiMappings.forEach((mapping, methodName) => {
        // 创建代理方法
        this.page[methodName] = this.createProxyMethod(methodName, mapping);
        this.compatibilityReport.mappedMethods++;
      });
      
      this.compatibilityReport.totalMethods = this.apiMappings.size;
      
      // 减少代理方法创建完成日志
      // console.log(`[APICompatibility] 代理方法创建完成，共 ${this.compatibilityReport.mappedMethods} 个`);
    } catch (error) {
      console.error('[APICompatibility] 创建代理方法失败:', error);
      this.compatibilityReport.errors.push({
        type: 'proxy_creation_error',
        error: error.message
      });
    }
  }

  /**
   * 创建单个代理方法
   * @param {string} methodName - 方法名
   * @param {object} mapping - 映射配置
   * @returns {Function} 代理方法
   */
  createProxyMethod(methodName, mapping) {
    return (...args) => {
      try {
        // 获取目标模块
        const targetModule = this.mainController.getModule(mapping.module);
        
        if (!targetModule) {
          // console.warn(`[APICompatibility] 模块 ${mapping.module} 不存在，方法 ${methodName} 调用失败`);
          return this.handleMissingModule(methodName, mapping, args);
        }
        
        // 检查目标方法是否存在
        if (typeof targetModule[mapping.method] !== 'function') {
          console.warn(`[APICompatibility] 方法 ${mapping.method} 在模块 ${mapping.module} 中不存在`);
          return this.handleMissingMethod(methodName, mapping, args);
        }
        
        // 调用目标方法 - 只记录关键方法的代理调用
        const criticalMethods = ['loadVideoList', 'handlePageLoad', 'onPullDownRefresh'];
        if (criticalMethods.includes(methodName)) {
          console.log(`[APICompatibility] 代理调用: ${methodName} -> ${mapping.module}.${mapping.method}`);
        }
        return targetModule[mapping.method].apply(targetModule, args);
        
      } catch (error) {
        console.error(`[APICompatibility] 代理方法 ${methodName} 调用失败:`, error);
        return this.handleProxyError(methodName, mapping, args, error);
      }
    };
  }

  /**
   * 处理模块缺失的情况
   * @param {string} methodName - 方法名
   * @param {object} mapping - 映射配置
   * @param {Array} args - 参数
   */
  handleMissingModule(methodName, mapping, args) {
    try {
      // console.warn(`[APICompatibility] 模块 ${mapping.module} 缺失，尝试降级处理`);
      
      // 根据方法类型提供降级处理
      switch (mapping.type) {
        case 'lifecycle':
          return this.handleLifecycleFallback(methodName, args);
        case 'event':
          return this.handleEventFallback(methodName, args);
        case 'business':
          return this.handleBusinessFallback(methodName, args);
        case 'utility':
          return this.handleUtilityFallback(methodName, args);
        default:
          console.warn(`[APICompatibility] 未知的方法类型: ${mapping.type}`);
          return null;
      }
    } catch (error) {
      console.error(`[APICompatibility] 处理模块缺失失败:`, error);
      return null;
    }
  }

  /**
   * 处理方法缺失的情况
   * @param {string} methodName - 方法名
   * @param {object} mapping - 映射配置
   * @param {Array} args - 参数
   */
  handleMissingMethod(methodName, mapping, args) {
    try {
      console.warn(`[APICompatibility] 方法 ${mapping.method} 缺失，尝试替代方案`);
      
      // 尝试查找替代方法
      const alternativeMethod = this.findAlternativeMethod(methodName, mapping);
      if (alternativeMethod) {
        return alternativeMethod.apply(this, args);
      }
      
      // 提供默认实现
      return this.provideDefaultImplementation(methodName, mapping, args);
      
    } catch (error) {
      console.error(`[APICompatibility] 处理方法缺失失败:`, error);
      return null;
    }
  }

  /**
   * 处理代理错误
   * @param {string} methodName - 方法名
   * @param {object} mapping - 映射配置
   * @param {Array} args - 参数
   * @param {Error} error - 错误对象
   */
  handleProxyError(methodName, mapping, args, error) {
    try {
      console.error(`[APICompatibility] 代理错误处理: ${methodName}`, error);
      
      // 记录错误
      this.compatibilityReport.errors.push({
        type: 'proxy_error',
        methodName: methodName,
        module: mapping.module,
        error: error.message,
        timestamp: Date.now()
      });
      
      // 尝试错误恢复
      return this.attemptErrorRecovery(methodName, mapping, args, error);
      
    } catch (recoveryError) {
      console.error(`[APICompatibility] 错误恢复失败:`, recoveryError);
      return null;
    }
  }

  /**
   * 生命周期方法降级处理
   * @param {string} methodName - 方法名
   * @param {Array} args - 参数
   */
  handleLifecycleFallback(methodName, args) {
    try {
      // console.log(`[APICompatibility] 生命周期方法降级: ${methodName}`);

      switch (methodName) {
        case 'onLoad':
          // 基本的页面加载处理
          // console.log('[APICompatibility] 执行基本的页面加载逻辑');
          return this.basicPageLoad(args[0]);

        case 'onShow':
          // 基本的页面显示处理
          // console.log('[APICompatibility] 执行基本的页面显示逻辑');
          return this.basicPageShow();

        case 'onHide':
          // 基本的页面隐藏处理
          // console.log('[APICompatibility] 执行基本的页面隐藏逻辑');
          return this.basicPageHide();
          
        case 'onUnload':
          // 基本的页面卸载处理
          console.log('[APICompatibility] 执行基本的页面卸载逻辑');
          return this.basicPageUnload();
          
        default:
          console.warn(`[APICompatibility] 未知的生命周期方法: ${methodName}`);
          return null;
      }
    } catch (error) {
      console.error(`[APICompatibility] 生命周期降级处理失败:`, error);
      return null;
    }
  }

  /**
   * 事件方法降级处理
   * @param {string} methodName - 方法名
   * @param {Array} args - 参数
   */
  handleEventFallback(methodName, args) {
    try {
      console.log(`[APICompatibility] 事件方法降级: ${methodName}`);
      
      // 记录事件调用
      console.log(`[APICompatibility] 事件 ${methodName} 被调用，参数:`, args);
      
      // 提供基本的事件处理
      switch (methodName) {
        case 'onPullDownRefresh':
          wx.stopPullDownRefresh();
          break;
        case 'onVideoError':
          wx.showToast({ title: '视频加载失败', icon: 'none' });
          break;
        default:
          // 其他事件暂不处理
          break;
      }
      
      return null;
    } catch (error) {
      console.error(`[APICompatibility] 事件降级处理失败:`, error);
      return null;
    }
  }

  /**
   * 业务方法降级处理
   * @param {string} methodName - 方法名
   * @param {Array} args - 参数
   */
  handleBusinessFallback(methodName, args) {
    try {
      console.log(`[APICompatibility] 业务方法降级: ${methodName}`);
      
      // 根据具体的业务方法提供降级实现
      switch (methodName) {
        case 'loadVideoList':
          return this.fallbackLoadVideoList(args[0]);
        case 'refreshVideoList':
          return this.fallbackRefreshVideoList();
        case 'searchLocalVideos':
          return this.fallbackSearchLocalVideos(args[0]);
        case 'clearSearch':
          return this.fallbackClearSearch();
        default:
          console.warn(`[APICompatibility] 未实现的业务方法降级: ${methodName}`);
          return null;
      }
    } catch (error) {
      console.error(`[APICompatibility] 业务降级处理失败:`, error);
      return null;
    }
  }

  /**
   * 工具方法降级处理
   * @param {string} methodName - 方法名
   * @param {Array} args - 参数
   */
  handleUtilityFallback(methodName, args) {
    try {
      console.log(`[APICompatibility] 工具方法降级: ${methodName}`);
      
      // 提供基本的工具方法实现
      switch (methodName) {
        case 'formatCount':
          return this.fallbackFormatCount(args[0]);
        case 'sanitizeVideoData':
          return this.fallbackSanitizeVideoData(args[0]);
        case 'getVideoUrl':
          return this.fallbackGetVideoUrl(args[0], args[1]);
        default:
          console.warn(`[APICompatibility] 未实现的工具方法降级: ${methodName}`);
          return null;
      }
    } catch (error) {
      console.error(`[APICompatibility] 工具降级处理失败:`, error);
      return null;
    }
  }

  /**
   * 查找替代方法
   * @param {string} methodName - 方法名
   * @param {object} mapping - 映射配置
   * @returns {Function|null} 替代方法
   */
  findAlternativeMethod(methodName, mapping) {
    try {
      // 定义方法替代关系
      const alternatives = {
        'loadVideoList': 'refreshVideoList',
        'onVideoPlay': 'handleVideoTap',
        'onSearchTap': 'onSearchInput'
      };
      
      const alternativeName = alternatives[methodName];
      if (alternativeName) {
        const alternativeMapping = this.apiMappings.get(alternativeName);
        if (alternativeMapping) {
          const targetModule = this.mainController.getModule(alternativeMapping.module);
          if (targetModule && typeof targetModule[alternativeMapping.method] === 'function') {
            console.log(`[APICompatibility] 找到替代方法: ${methodName} -> ${alternativeName}`);
            return targetModule[alternativeMapping.method].bind(targetModule);
          }
        }
      }
      
      return null;
    } catch (error) {
      console.error(`[APICompatibility] 查找替代方法失败:`, error);
      return null;
    }
  }

  /**
   * 提供默认实现
   * @param {string} methodName - 方法名
   * @param {object} mapping - 映射配置
   * @param {Array} args - 参数
   */
  provideDefaultImplementation(methodName, mapping, args) {
    try {
      console.log(`[APICompatibility] 提供默认实现: ${methodName}`);
      
      // 根据方法类型提供默认实现
      switch (mapping.type) {
        case 'lifecycle':
          return this.handleLifecycleFallback(methodName, args);
        case 'event':
          return this.handleEventFallback(methodName, args);
        case 'business':
          return this.handleBusinessFallback(methodName, args);
        case 'utility':
          return this.handleUtilityFallback(methodName, args);
        default:
          return null;
      }
    } catch (error) {
      console.error(`[APICompatibility] 提供默认实现失败:`, error);
      return null;
    }
  }

  /**
   * 尝试错误恢复
   * @param {string} methodName - 方法名
   * @param {object} mapping - 映射配置
   * @param {Array} args - 参数
   * @param {Error} error - 错误对象
   */
  attemptErrorRecovery(methodName, mapping, args, error) {
    try {
      console.log(`[APICompatibility] 尝试错误恢复: ${methodName}`);
      
      // 延迟重试
      setTimeout(() => {
        try {
          const targetModule = this.mainController.getModule(mapping.module);
          if (targetModule && typeof targetModule[mapping.method] === 'function') {
            console.log(`[APICompatibility] 错误恢复重试: ${methodName}`);
            return targetModule[mapping.method].apply(targetModule, args);
          }
        } catch (retryError) {
          console.error(`[APICompatibility] 错误恢复重试失败:`, retryError);
        }
      }, 1000);
      
      // 返回降级处理结果
      return this.provideDefaultImplementation(methodName, mapping, args);
      
    } catch (error) {
      console.error(`[APICompatibility] 错误恢复失败:`, error);
      return null;
    }
  }

  // ==================== 降级实现方法 ====================

  /**
   * 基本页面加载
   * @param {object} options - 页面参数
   */
  basicPageLoad(options) {
    try {
      console.log('[APICompatibility] 执行基本页面加载');
      
      // 设置基本数据
      this.page.setData({
        loading: false,
        firstLoading: true,
        showContent: true
      });
      
      return Promise.resolve();
    } catch (error) {
      console.error('[APICompatibility] 基本页面加载失败:', error);
      return Promise.reject(error);
    }
  }

  /**
   * 基本页面显示
   */
  basicPageShow() {
    try {
      // console.log('[APICompatibility] 执行基本页面显示');
      
      // 更新全局状态
      const app = getApp();
      if (app && app.globalData) {
        app.globalData.currentTabIndex = 1;
      }
      
      // 关键修复：同时更新tabBar状态，确保图标不会回退
      if (typeof this.page.getTabBar === 'function' && this.page.getTabBar()) {
        this.page.getTabBar().setData({
          selected: 1  // index页面对应的索引为1（展示）
        });
        // console.log('[APICompatibility] 已同步更新tabBar状态');
      }
      
    } catch (error) {
      console.error('[APICompatibility] 基本页面显示失败:', error);
    }
  }

  /**
   * 基本页面隐藏
   */
  basicPageHide() {
    try {
      // console.log('[APICompatibility] 执行基本页面隐藏');
      // 暂停所有视频
      // 这里可以添加基本的暂停逻辑
    } catch (error) {
      console.error('[APICompatibility] 基本页面隐藏失败:', error);
    }
  }

  /**
   * 基本页面卸载
   */
  basicPageUnload() {
    try {
      console.log('[APICompatibility] 执行基本页面卸载');
      // 清理资源
    } catch (error) {
      console.error('[APICompatibility] 基本页面卸载失败:', error);
    }
  }

  /**
   * 降级视频列表加载
   * @param {boolean} refresh - 是否刷新
   */
  fallbackLoadVideoList(refresh) {
    try {
      console.log('[APICompatibility] 降级视频列表加载');
      
      // 显示加载状态
      this.page.setData({ loading: true });
      
      // 模拟加载完成
      setTimeout(() => {
        this.page.setData({
          loading: false,
          firstLoading: false,
          showContent: true
        });
      }, 1000);
      
      return Promise.resolve([]);
    } catch (error) {
      console.error('[APICompatibility] 降级视频列表加载失败:', error);
      return Promise.reject(error);
    }
  }

  /**
   * 降级刷新视频列表
   */
  fallbackRefreshVideoList() {
    try {
      console.log('[APICompatibility] 降级刷新视频列表');
      return this.fallbackLoadVideoList(true);
    } catch (error) {
      console.error('[APICompatibility] 降级刷新视频列表失败:', error);
      return Promise.reject(error);
    }
  }

  /**
   * 降级本地搜索
   * @param {string} keyword - 搜索关键词
   */
  fallbackSearchLocalVideos(keyword) {
    try {
      console.log('[APICompatibility] 降级本地搜索:', keyword);
      
      // 返回空结果
      return [];
    } catch (error) {
      console.error('[APICompatibility] 降级本地搜索失败:', error);
      return [];
    }
  }

  /**
   * 降级清除搜索
   */
  fallbackClearSearch() {
    try {
      console.log('[APICompatibility] 降级清除搜索');
      
      this.page.setData({
        searchKeyword: '',
        isSearching: false,
        searchFocused: false
      });
      
    } catch (error) {
      console.error('[APICompatibility] 降级清除搜索失败:', error);
    }
  }

  /**
   * 降级格式化计数
   * @param {number} count - 计数
   */
  fallbackFormatCount(count) {
    try {
      count = parseInt(count, 10) || 0;
      
      if (count >= 10000) {
        return (count / 10000).toFixed(1) + '万';
      } else if (count >= 1000) {
        return (count / 1000).toFixed(1) + 'k';
      } else {
        return count.toString();
      }
    } catch (error) {
      console.error('[APICompatibility] 降级格式化计数失败:', error);
      return '0';
    }
  }

  /**
   * 降级清理视频数据
   * @param {object} item - 视频数据
   */
  fallbackSanitizeVideoData(item) {
    try {
      return {
        id: item.id || '',
        baseId: item.baseId || item.id || '',
        mainTitle: item.mainTitle || item.title || '',
        subTitle: item.subTitle || '',
        coverUrl: item.coverUrl || '/static/logo.png',
        videoUrl: item.videoUrl || '',
        playCount: item.playCount || '0',
        author: item.author || '',
        authorAvatar: item.authorAvatar || '/static/logo.png',
        isPlaying: false,
        description: item.description || '',
        urlError: false
      };
    } catch (error) {
      console.error('[APICompatibility] 降级清理视频数据失败:', error);
      return {};
    }
  }

  /**
   * 降级获取视频URL
   * @param {string} videoId - 视频ID
   * @param {string} baseId - 基础ID
   */
  fallbackGetVideoUrl(videoId, baseId) {
    try {
      console.log('[APICompatibility] 降级获取视频URL:', videoId, baseId);
      
      // 返回空Promise
      return Promise.resolve('');
    } catch (error) {
      console.error('[APICompatibility] 降级获取视频URL失败:', error);
      return Promise.reject(error);
    }
  }

  /**
   * 验证兼容性
   */
  validateCompatibility() {
    try {
      // 减少API兼容性验证日志
      // console.log('[APICompatibility] 验证API兼容性');
      
      // 检查所有映射的方法是否可用
      this.apiMappings.forEach((mapping, methodName) => {
        try {
          const targetModule = this.mainController.getModule(mapping.module);
          if (!targetModule) {
            this.compatibilityReport.unmappedMethods.push({
              method: methodName,
              reason: 'module_not_found',
              module: mapping.module
            });
          } else if (typeof targetModule[mapping.method] !== 'function') {
            this.compatibilityReport.unmappedMethods.push({
              method: methodName,
              reason: 'method_not_found',
              module: mapping.module,
              targetMethod: mapping.method
            });
          }
        } catch (error) {
          this.compatibilityReport.errors.push({
            type: 'validation_error',
            method: methodName,
            error: error.message
          });
        }
      });
      
      // 输出兼容性报告
      // 只在有错误时输出兼容性验证结果
      if (this.compatibilityReport.errors.length > 0 || this.compatibilityReport.unmappedMethods.length > 0) {
        console.log('[APICompatibility] 兼容性验证完成:', this.compatibilityReport);
      }
      
    } catch (error) {
      console.error('[APICompatibility] 验证兼容性失败:', error);
    }
  }

  /**
   * 获取兼容性报告
   * @returns {object} 兼容性报告
   */
  getCompatibilityReport() {
    return { ...this.compatibilityReport };
  }

  /**
   * 处理方法调用（用于页面直接调用的方法）
   * @param {string} methodName - 方法名
   * @param {Arguments} args - 参数
   * @returns {any} 方法调用结果
   */
  handleMethod(methodName, args) {
    try {
      // 只记录关键方法调用，忽略频繁的滚动事件
      const criticalMethods = ['loadVideoList', 'handlePageLoad', 'onPullDownRefresh'];
      if (criticalMethods.includes(methodName)) {
        console.log(`[APICompatibility] 处理方法调用: ${methodName}`);
      }
      
      // 获取映射配置
      const mapping = this.apiMappings.get(methodName);
      if (!mapping) {
        if (criticalMethods.includes(methodName)) {
          console.warn(`[APICompatibility] 未找到方法映射: ${methodName}`);
        }
        return this.provideDefaultImplementation(methodName, { type: 'event' }, Array.from(args));
      }
      
      // 获取目标模块
      const targetModule = this.mainController.getModule(mapping.module);
      if (!targetModule) {
        console.warn(`[APICompatibility] 模块 ${mapping.module} 不存在，方法 ${methodName} 调用失败`);
        return this.handleMissingModule(methodName, mapping, Array.from(args));
      }
      
      // 检查目标方法是否存在
      if (typeof targetModule[mapping.method] !== 'function') {
        console.warn(`[APICompatibility] 方法 ${mapping.method} 在模块 ${mapping.module} 中不存在`);
        return this.handleMissingMethod(methodName, mapping, Array.from(args));
      }
      
      // 调用目标方法 - 只记录关键方法的代理调用
      if (criticalMethods.includes(methodName)) {
        console.log(`[APICompatibility] 代理调用: ${methodName} -> ${mapping.module}.${mapping.method}`);
      }
      return targetModule[mapping.method].apply(targetModule, args);
      
    } catch (error) {
      console.error(`[APICompatibility] 处理方法调用失败: ${methodName}`, error);
      return this.handleProxyError(methodName, { type: 'event' }, Array.from(args), error);
    }
  }

  /**
   * 销毁兼容层
   */
  destroy() {
    try {
      console.log('[APICompatibility] 销毁API兼容层');
      
      // 清理映射
      this.apiMappings.clear();
      
      // 重置报告
      this.compatibilityReport = {
        totalMethods: 0,
        mappedMethods: 0,
        unmappedMethods: [],
        errors: []
      };
      
    } catch (error) {
      console.error('[APICompatibility] 销毁API兼容层失败:', error);
    }
  }
}

module.exports = APICompatibility;