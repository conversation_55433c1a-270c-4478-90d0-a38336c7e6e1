/**
 * 搜索功能模块
 * 负责搜索功能实现、搜索状态管理、本地搜索逻辑和搜索结果处理
 * @version 1.0.0
 */

const BaseModule = require('./base-module');
const videoUtils = require('../utils/video-utils');
const { 
  SEARCH_CONFIG,
  SEARCH_EVENTS,
  ERROR_TYPES 
} = require('../constants/index-constants');

class SearchModule extends BaseModule {
  constructor(pageContext) {
    super(pageContext);
    this.moduleName = 'Search';
    
    // 搜索历史和缓存
    this.searchHistory = [];
    this.searchCache = new Map();
    this.searchDebounceTimer = null;
  }

  /**
   * 初始化搜索模块
   */
  init() {
    try {
      // console.log('[Search] 初始化搜索模块');
      
      // 初始化搜索状态
      this.initSearchState();
      
      // 加载搜索历史
      this.loadSearchHistory();
      
      this.initialized = true;
      // console.log('[Search] 搜索模块初始化完成');
    } catch (error) {
      this.handleError(error, 'init');
    }
  }

  /**
   * 初始化搜索状态
   */
  initSearchState() {
    try {
      const currentData = this.data;
      
      // 确保必要的搜索状态字段存在
      const requiredFields = {
        searchKeyword: currentData.searchKeyword || '',
        isSearching: currentData.isSearching || false,
        searchFocused: currentData.searchFocused || false,
        originalVideoList: currentData.originalVideoList || null
      };

      // 只更新缺失的字段
      const updateData = {};
      Object.keys(requiredFields).forEach(key => {
        if (currentData[key] === undefined) {
          updateData[key] = requiredFields[key];
        }
      });

      if (Object.keys(updateData).length > 0) {
        this.safeSetData(updateData);
      }

      // console.log('[Search] 搜索状态初始化完成');
    } catch (error) {
      this.handleError(error, 'initSearchState');
    }
  }

  /**
   * 初始化搜索组件
   */
  initSearchComponent() {
    try {
      console.log('[Search] 初始化搜索组件');
      
      // 初始化搜索相关数据
      this.safeSetData({
        searchKeyword: '',
        isSearching: false,
        searchFocused: false,
        originalVideoList: null
      });
      
      // 触发搜索组件初始化事件
      this.emit(SEARCH_EVENTS.SEARCH_START, { 
        action: 'init'
      });
      
      console.log('[Search] 搜索组件初始化完成');
    } catch (error) {
      this.handleError(error, 'initSearchComponent');
    }
  }

  /**
   * 处理搜索框点击事件
   * @param {object} e - 事件对象
   */
  onSearchTap(e) {
    try {
      const { value, results } = e.detail;
      const that = this;
      
      console.log('[Search] 搜索框点击事件:', value);
      
      // 保存原始视频列表，用于恢复
      if (!this.data.originalVideoList && this.data.videoList.length > 0) {
        this.safeSetData({
          originalVideoList: [...this.data.videoList],
          isSearching: true,
          searchKeyword: value
        });
      }
      
      // 先执行滚动到顶部，等待滚动完成后再进行搜索和更新列表
      this.scrollToTopAndSearch(value, results, () => {
        // 根据搜索结果更新视频列表
        if (results && results.length > 0) {
          that.handleSearchResults(results, value);
        } else if (value) {
          // 前端搜索
          const filteredList = that.searchLocalVideos(value);
          that.handleSearchResults(filteredList, value);
        }
      });
    } catch (error) {
      this.handleError(error, 'onSearchTap');
    }
  }

  /**
   * 处理搜索输入事件
   * @param {object} e - 事件对象
   */
  onSearchInput(e) {
    try {
      const { value } = e.detail;
      
      console.log('[Search] 搜索输入事件:', value);
      
      // 更新搜索关键词
      this.safeSetData({ searchKeyword: value });
      
      // 如果有搜索内容，保存原始列表（如果尚未保存）
      if (value.trim() && !this.data.originalVideoList && this.data.videoList.length > 0) {
        this.safeSetData({
          originalVideoList: [...this.data.videoList]
        });
      }
      
      // 如果输入为空且处于搜索状态，恢复原始列表
      if (!value.trim() && this.data.isSearching) {
        this.clearSearch();
        return;
      }
      
      // 实时搜索（带防抖）
      if (value.trim()) {
        this.debouncedSearch(value);
      }
    } catch (error) {
      this.handleError(error, 'onSearchInput');
    }
  }

  /**
   * 处理搜索确认事件
   * @param {object} e - 事件对象
   */
  onSearchConfirm(e) {
    try {
      const { value } = e.detail;
      const that = this;
      
      console.log('[Search] 搜索确认事件:', value);
      
      if (!value) {
        // 如果搜索内容为空，恢复原始列表
        this.clearSearch();
        return;
      }
      
      // 保存原始视频列表，用于恢复
      if (!this.data.originalVideoList && this.data.videoList.length > 0) {
        this.safeSetData({ originalVideoList: [...this.data.videoList] });
      }
      
      // 添加到搜索历史
      this.addToSearchHistory(value);
      
      // 先执行滚动到顶部，等待滚动完成后再进行搜索和更新列表
      this.scrollToTopAndSearch(value, null, () => {
        // 基于关键词搜索
        const filteredList = that.searchLocalVideos(value);
        
        // 搜索完成后，设置搜索状态并显示结果
        that.safeSetData({
          videoList: filteredList,
          isSearching: true,
          searchKeyword: value,
          searchFocused: false,
          showContent: true
        });

        // 触发搜索完成事件
        that.emit(SEARCH_EVENTS.SEARCH_COMPLETE, {
          keyword: value,
          results: filteredList,
          resultCount: filteredList.length
        });
      });
    } catch (error) {
      this.handleError(error, 'onSearchConfirm');
    }
  }

  /**
   * 处理搜索焦点变化
   * @param {object} e - 事件对象
   */
  onSearchFocus(e) {
    try {
      const focused = e.detail.focused;
      
      console.log('[Search] 搜索焦点变化:', focused);
      
      this.safeSetData({
        searchFocused: focused
      });

      // 触发焦点变化事件
      this.emit(SEARCH_EVENTS.SEARCH_FOCUS_CHANGE, {
        focused: focused
      });
    } catch (error) {
      this.handleError(error, 'onSearchFocus');
    }
  }

  /**
   * 滚动到顶部并执行搜索
   * @param {string} value - 搜索值
   * @param {Array} results - 搜索结果
   * @param {function} callback - 回调函数
   */
  scrollToTopAndSearch(value, results, callback) {
    try {
      // 将滚动位置强制设置为0
      wx.createSelectorQuery()
        .select('.main-scroll')
        .node()
        .exec((res) => {
          if (res && res[0] && res[0].node) {
            res[0].node.scrollTop = 0;
            console.log('[Search] 强制滚动到顶部 scrollTop = 0');
            
            // 再强制一次页面滚动到顶部
            wx.pageScrollTo({
              scrollTop: 0,
              duration: 0,
              success: () => {
                console.log('[Search] 页面已滚动到顶部');
                
                if (callback) callback();
                
                // 延迟100ms再进行第二次滚动确认，确保列表已经渲染
                setTimeout(() => {
                  wx.createSelectorQuery()
                    .select('.main-scroll')
                    .node()
                    .exec((res) => {
                      if (res && res[0] && res[0].node) {
                        res[0].node.scrollTop = 0;
                        console.log('[Search] 二次确认滚动位置 scrollTop = 0');
                      }
                    });
                }, 100);
              }
            });
          } else {
            // 备用方案：如果无法获取scroll-view节点，直接执行回调
            if (callback) callback();
          }
        });
    } catch (error) {
      console.error('[Search] 滚动到顶部失败', error);
      // 如果滚动失败，仍然执行回调
      if (callback) callback();
    }
  }

  /**
   * 防抖搜索
   * @param {string} keyword - 搜索关键词
   */
  debouncedSearch(keyword) {
    try {
      // 清除之前的定时器
      if (this.searchDebounceTimer) {
        clearTimeout(this.searchDebounceTimer);
      }
      
      // 设置新的定时器
      this.searchDebounceTimer = setTimeout(() => {
        const filteredList = this.searchLocalVideos(keyword);
        this.safeSetData({
          videoList: filteredList,
          isSearching: true
        });

        // 触发实时搜索事件
        this.emit('realtimeSearch', {
          keyword: keyword,
          results: filteredList,
          resultCount: filteredList.length
        });
      }, SEARCH_CONFIG.SEARCH_DEBOUNCE_DELAY);
    } catch (error) {
      this.handleError(error, 'debouncedSearch');
    }
  }

  /**
   * 本地搜索视频
   * @param {string} keyword - 搜索关键词
   * @returns {Array} 搜索结果
   */
  searchLocalVideos(keyword) {
    try {
      if (!keyword || !this.data.originalVideoList) {
        return this.data.videoList;
      }

      // 检查缓存
      const cacheKey = keyword.toLowerCase();
      if (this.searchCache.has(cacheKey)) {
        console.log('[Search] 使用搜索缓存:', keyword);
        return this.searchCache.get(cacheKey);
      }

      console.log('[Search] 执行本地搜索:', keyword);
      
      // 使用视频工具函数进行搜索
      const results = videoUtils.searchVideos(
        this.data.originalVideoList, 
        keyword, 
        {
          caseSensitive: false,
          searchFields: ['mainTitle', 'subTitle', 'description', 'author']
        }
      );

      // 缓存搜索结果
      this.cacheSearchResult(cacheKey, results);
      
      console.log(`[Search] 搜索完成，关键词: ${keyword}, 结果数量: ${results.length}`);
      
      return results;
    } catch (error) {
      this.handleError(error, 'searchLocalVideos');
      return this.data.videoList;
    }
  }

  /**
   * 处理搜索结果
   * @param {Array} results - 搜索结果
   * @param {string} keyword - 搜索关键词
   */
  handleSearchResults(results, keyword) {
    try {
      this.safeSetData({
        videoList: results,
        isSearching: true,
        searchKeyword: keyword,
        showContent: true
      });

      // 触发搜索结果处理事件
      this.emit('searchResultsHandled', {
        keyword: keyword,
        results: results,
        resultCount: results.length
      });

      console.log(`[Search] 搜索结果已处理，关键词: ${keyword}, 结果数量: ${results.length}`);
    } catch (error) {
      this.handleError(error, 'handleSearchResults');
    }
  }

  /**
   * 清除搜索
   */
  clearSearch() {
    try {
      console.log('[Search] 清除搜索');
      
      // 恢复原始视频列表
      if (this.data.originalVideoList) {
        this.safeSetData({
          videoList: this.data.originalVideoList,
          originalVideoList: null,
          isSearching: false,
          searchKeyword: '',
          searchFocused: false,
          showContent: true
        });
      } else {
        // 如果没有原始列表，只清除搜索状态
        this.safeSetData({
          isSearching: false,
          searchKeyword: '',
          searchFocused: false
        });
      }

      // 触发搜索清除事件
      this.emit(SEARCH_EVENTS.SEARCH_CLEAR, {
        action: 'manual'
      });
      
      console.log('[Search] 搜索已清除');
    } catch (error) {
      this.handleError(error, 'clearSearch');
    }
  }

  /**
   * 缓存搜索结果
   * @param {string} keyword - 搜索关键词
   * @param {Array} results - 搜索结果
   */
  cacheSearchResult(keyword, results) {
    try {
      // 限制缓存大小
      if (this.searchCache.size >= 50) {
        // 删除最旧的缓存项
        const firstKey = this.searchCache.keys().next().value;
        this.searchCache.delete(firstKey);
      }
      
      this.searchCache.set(keyword, results);
      console.log(`[Search] 搜索结果已缓存: ${keyword}`);
    } catch (error) {
      this.handleError(error, 'cacheSearchResult');
    }
  }

  /**
   * 清除搜索缓存
   */
  clearSearchCache() {
    try {
      this.searchCache.clear();
      console.log('[Search] 搜索缓存已清除');
    } catch (error) {
      this.handleError(error, 'clearSearchCache');
    }
  }

  /**
   * 加载搜索历史
   */
  loadSearchHistory() {
    try {
      const history = wx.getStorageSync('search_history') || [];
      this.searchHistory = history;
      // console.log(`[Search] 搜索历史已加载，数量: ${history.length}`);
    } catch (error) {
      this.handleError(error, 'loadSearchHistory');
      this.searchHistory = [];
    }
  }

  /**
   * 添加到搜索历史
   * @param {string} keyword - 搜索关键词
   */
  addToSearchHistory(keyword) {
    try {
      if (!keyword || keyword.length < SEARCH_CONFIG.MIN_SEARCH_LENGTH) {
        return;
      }

      // 移除已存在的相同关键词
      this.searchHistory = this.searchHistory.filter(item => item !== keyword);
      
      // 添加到开头
      this.searchHistory.unshift(keyword);
      
      // 限制历史记录数量
      if (this.searchHistory.length > SEARCH_CONFIG.MAX_SEARCH_HISTORY) {
        this.searchHistory = this.searchHistory.slice(0, SEARCH_CONFIG.MAX_SEARCH_HISTORY);
      }
      
      // 保存到本地存储
      wx.setStorageSync('search_history', this.searchHistory);
      
      console.log(`[Search] 搜索历史已更新: ${keyword}`);
    } catch (error) {
      this.handleError(error, 'addToSearchHistory');
    }
  }

  /**
   * 获取搜索历史
   * @returns {Array} 搜索历史列表
   */
  getSearchHistory() {
    try {
      return this.searchHistory;
    } catch (error) {
      this.handleError(error, 'getSearchHistory');
      return [];
    }
  }

  /**
   * 清除搜索历史
   */
  clearSearchHistory() {
    try {
      this.searchHistory = [];
      wx.removeStorageSync('search_history');
      console.log('[Search] 搜索历史已清除');
    } catch (error) {
      this.handleError(error, 'clearSearchHistory');
    }
  }

  /**
   * 获取搜索建议
   * @param {string} keyword - 搜索关键词
   * @returns {Array} 搜索建议列表
   */
  getSearchSuggestions(keyword) {
    try {
      if (!keyword || keyword.length < SEARCH_CONFIG.MIN_SEARCH_LENGTH) {
        return [];
      }

      const suggestions = [];
      
      // 从搜索历史中匹配
      const historySuggestions = this.searchHistory.filter(item => 
        item.toLowerCase().includes(keyword.toLowerCase())
      ).slice(0, 5);
      
      suggestions.push(...historySuggestions.map(item => ({
        type: 'history',
        text: item
      })));

      // 从视频标题中匹配
      if (this.data.originalVideoList) {
        const titleSuggestions = this.data.originalVideoList
          .filter(video => 
            video.mainTitle && 
            video.mainTitle.toLowerCase().includes(keyword.toLowerCase())
          )
          .map(video => video.mainTitle)
          .slice(0, 3);
        
        suggestions.push(...titleSuggestions.map(item => ({
          type: 'title',
          text: item
        })));
      }

      return suggestions.slice(0, 8); // 最多返回8个建议
    } catch (error) {
      this.handleError(error, 'getSearchSuggestions');
      return [];
    }
  }

  /**
   * 保存搜索状态到本地存储
   */
  saveSearchState() {
    try {
      const searchState = {
        isSearching: this.data.isSearching,
        searchKeyword: this.data.searchKeyword,
        originalVideoList: this.data.originalVideoList,
        timestamp: Date.now()
      };

      wx.setStorageSync('search_state', searchState);
      console.log('[Search] 搜索状态已保存');
    } catch (error) {
      this.handleError(error, 'saveSearchState');
    }
  }

  /**
   * 从本地存储恢复搜索状态
   */
  restoreSearchState() {
    try {
      const searchState = wx.getStorageSync('search_state');
      
      if (!searchState) {
        console.log('[Search] 没有找到保存的搜索状态');
        return false;
      }

      // 检查状态是否过期（30分钟）
      const now = Date.now();
      const stateAge = now - searchState.timestamp;
      const maxAge = 30 * 60 * 1000; // 30分钟

      if (stateAge > maxAge) {
        console.log('[Search] 搜索状态已过期，清除');
        wx.removeStorageSync('search_state');
        return false;
      }

      // 恢复搜索状态
      this.safeSetData({
        isSearching: searchState.isSearching,
        searchKeyword: searchState.searchKeyword,
        originalVideoList: searchState.originalVideoList
      });

      // 如果处于搜索状态，重新执行搜索
      if (searchState.isSearching && searchState.searchKeyword) {
        const results = this.searchLocalVideos(searchState.searchKeyword);
        this.safeSetData({
          videoList: results
        });
      }

      console.log('[Search] 搜索状态已恢复:', searchState.searchKeyword);
      return true;
    } catch (error) {
      this.handleError(error, 'restoreSearchState');
      return false;
    }
  }

  /**
   * 清除保存的搜索状态
   */
  clearSavedSearchState() {
    try {
      wx.removeStorageSync('search_state');
      console.log('[Search] 保存的搜索状态已清除');
    } catch (error) {
      this.handleError(error, 'clearSavedSearchState');
    }
  }

  /**
   * 设置搜索模式
   * @param {string} mode - 搜索模式 ('local', 'remote', 'hybrid')
   */
  setSearchMode(mode) {
    try {
      const validModes = ['local', 'remote', 'hybrid'];
      if (!validModes.includes(mode)) {
        console.warn('[Search] 无效的搜索模式:', mode);
        return;
      }

      this.searchMode = mode;
      wx.setStorageSync('search_mode', mode);

      // 触发搜索模式变化事件
      this.emit('searchModeChange', { mode });

      console.log('[Search] 搜索模式已设置:', mode);
    } catch (error) {
      this.handleError(error, 'setSearchMode');
    }
  }

  /**
   * 获取当前搜索模式
   * @returns {string} 搜索模式
   */
  getSearchMode() {
    try {
      return this.searchMode || wx.getStorageSync('search_mode') || 'local';
    } catch (error) {
      this.handleError(error, 'getSearchMode');
      return 'local';
    }
  }

  /**
   * 批量更新搜索状态
   * @param {object} stateUpdates - 状态更新对象
   */
  batchUpdateSearchState(stateUpdates) {
    try {
      if (!stateUpdates || typeof stateUpdates !== 'object') {
        console.warn('[Search] 无效的状态更新对象');
        return;
      }

      // 验证状态字段
      const validFields = ['isSearching', 'searchKeyword', 'searchFocused', 'originalVideoList'];
      const filteredUpdates = {};

      Object.keys(stateUpdates).forEach(key => {
        if (validFields.includes(key)) {
          filteredUpdates[key] = stateUpdates[key];
        }
      });

      if (Object.keys(filteredUpdates).length > 0) {
        this.safeSetData(filteredUpdates);

        // 触发批量状态更新事件
        this.emit('batchSearchStateUpdate', {
          updates: filteredUpdates
        });

        console.log('[Search] 批量更新搜索状态:', Object.keys(filteredUpdates));
      }
    } catch (error) {
      this.handleError(error, 'batchUpdateSearchState');
    }
  }

  /**
   * 监听搜索状态变化
   * @param {string} field - 状态字段名
   * @param {function} callback - 回调函数
   */
  watchSearchState(field, callback) {
    try {
      if (!field || typeof callback !== 'function') return;

      const eventName = `searchState:${field}`;
      this.on(eventName, callback);

      console.log('[Search] 开始监听搜索状态:', field);
    } catch (error) {
      this.handleError(error, 'watchSearchState');
    }
  }

  /**
   * 取消监听搜索状态变化
   * @param {string} field - 状态字段名
   * @param {function} callback - 回调函数
   */
  unwatchSearchState(field, callback) {
    try {
      if (!field) return;

      const eventName = `searchState:${field}`;
      this.off(eventName, callback);

      console.log('[Search] 停止监听搜索状态:', field);
    } catch (error) {
      this.handleError(error, 'unwatchSearchState');
    }
  }

  /**
   * 触发搜索状态变化事件
   * @param {string} field - 状态字段名
   * @param {*} value - 新值
   * @param {*} oldValue - 旧值
   */
  notifySearchStateChange(field, value, oldValue) {
    try {
      if (!field) return;

      const eventName = `searchState:${field}`;
      this.emit(eventName, { field, value, oldValue });

      console.log('[Search] 搜索状态变化通知:', field, value);
    } catch (error) {
      this.handleError(error, 'notifySearchStateChange');
    }
  }

  /**
   * 重置搜索状态
   */
  resetSearchState() {
    try {
      const oldState = {
        isSearching: this.data.isSearching,
        searchKeyword: this.data.searchKeyword,
        searchFocused: this.data.searchFocused
      };

      this.safeSetData({
        isSearching: false,
        searchKeyword: '',
        searchFocused: false,
        originalVideoList: null
      });

      // 清除保存的状态
      this.clearSavedSearchState();

      // 触发状态重置事件
      this.emit('searchStateReset', {
        oldState: oldState
      });

      console.log('[Search] 搜索状态已重置');
    } catch (error) {
      this.handleError(error, 'resetSearchState');
    }
  }

  /**
   * 获取搜索性能统计
   * @returns {object} 性能统计信息
   */
  getSearchPerformanceStats() {
    try {
      const stats = wx.getStorageSync('search_performance_stats') || {
        totalSearches: 0,
        averageSearchTime: 0,
        cacheHitRate: 0,
        lastSearchTime: null
      };

      return {
        ...stats,
        currentCacheSize: this.searchCache.size,
        historySize: this.searchHistory.length
      };
    } catch (error) {
      this.handleError(error, 'getSearchPerformanceStats');
      return {};
    }
  }

  /**
   * 记录搜索性能
   * @param {string} keyword - 搜索关键词
   * @param {number} searchTime - 搜索耗时（毫秒）
   * @param {boolean} cacheHit - 是否命中缓存
   */
  recordSearchPerformance(keyword, searchTime, cacheHit) {
    try {
      const stats = this.getSearchPerformanceStats();
      
      // 更新统计数据
      stats.totalSearches++;
      stats.averageSearchTime = ((stats.averageSearchTime * (stats.totalSearches - 1)) + searchTime) / stats.totalSearches;
      stats.cacheHitRate = ((stats.cacheHitRate * (stats.totalSearches - 1)) + (cacheHit ? 1 : 0)) / stats.totalSearches;
      stats.lastSearchTime = Date.now();

      // 保存统计数据
      wx.setStorageSync('search_performance_stats', stats);

      console.log(`[Search] 搜索性能已记录: ${keyword}, 耗时: ${searchTime}ms, 缓存命中: ${cacheHit}`);
    } catch (error) {
      this.handleError(error, 'recordSearchPerformance');
    }
  }

  /**
   * 优化搜索缓存
   */
  optimizeSearchCache() {
    try {
      const maxCacheSize = 30;
      
      if (this.searchCache.size <= maxCacheSize) {
        return;
      }

      // 转换为数组并按使用频率排序（这里简化为按添加顺序）
      const cacheEntries = Array.from(this.searchCache.entries());
      
      // 保留最近的缓存项
      const keepEntries = cacheEntries.slice(-maxCacheSize);
      
      // 重建缓存
      this.searchCache.clear();
      keepEntries.forEach(([key, value]) => {
        this.searchCache.set(key, value);
      });

      console.log(`[Search] 搜索缓存已优化，保留 ${keepEntries.length} 项`);
    } catch (error) {
      this.handleError(error, 'optimizeSearchCache');
    }
  }

  /**
   * 获取搜索状态
   * @returns {object} 搜索状态信息
   */
  getSearchStatus() {
    try {
      const performanceStats = this.getSearchPerformanceStats();
      
      return {
        isSearching: this.data.isSearching,
        searchKeyword: this.data.searchKeyword,
        searchFocused: this.data.searchFocused,
        hasOriginalList: !!this.data.originalVideoList,
        currentResultCount: this.data.videoList ? this.data.videoList.length : 0,
        historyCount: this.searchHistory.length,
        cacheSize: this.searchCache.size,
        searchMode: this.getSearchMode(),
        performanceStats: performanceStats
      };
    } catch (error) {
      this.handleError(error, 'getSearchStatus');
      return {};
    }
  }

  /**
   * 销毁模块，清理资源
   */
  destroy() {
    try {
      console.log('[Search] 销毁搜索模块');
      
      // 清除防抖定时器
      if (this.searchDebounceTimer) {
        clearTimeout(this.searchDebounceTimer);
        this.searchDebounceTimer = null;
      }
      
      // 清除搜索缓存
      this.clearSearchCache();
      
      super.destroy();
    } catch (error) {
      this.handleError(error, 'destroy');
    }
  }
}

module.exports = SearchModule;