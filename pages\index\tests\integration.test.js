/**
 * 集成测试
 * 测试模块间协作、完整业务流程和系统集成
 */

const MainController = require('../modules/main-controller');
const { createMockPageContext, createMockVideoList, waitFor, createTestError } = require('./test-utils');

// Mock wx API
global.wx = {
  showToast: jest.fn(),
  hideToast: jest.fn(),
  showLoading: jest.fn(),
  hideLoading: jest.fn(),
  stopPullDownRefresh: jest.fn(),
  pageScrollTo: jest.fn(),
  createSelectorQuery: jest.fn(() => ({
    select: jest.fn(() => ({
      node: jest.fn(() => ({
        exec: jest.fn((callback) => {
          callback([{ node: { scrollTop: 0 } }]);
        })
      })),
      boundingClientRect: jest.fn(() => ({
        exec: jest.fn((callback) => {
          callback([{ top: 0, left: 0, width: 100, height: 100 }]);
        })
      }))
    }))
  })),
  getSystemInfoSync: jest.fn(() => ({
    platform: 'devtools',
    version: '1.0.0',
    statusBarHeight: 20,
    screenHeight: 800,
    screenWidth: 375
  })),
  getStorageSync: jest.fn(),
  setStorageSync: jest.fn(),
  onNetworkStatusChange: jest.fn(),
  onUnhandledRejection: jest.fn(),
  onError: jest.fn(),
  getNetworkType: jest.fn((options) => {
    options.success({ networkType: 'wifi' });
  })
};

// Mock getCurrentPages
global.getCurrentPages = jest.fn(() => [
  { route: 'pages/index/index' }
]);

// Mock getApp
global.getApp = jest.fn(() => ({
  globalData: { 
    version: '1.0.0',
    currentTabIndex: 1
  }
}));

describe('Integration Tests 集成测试', () => {
  let mainController;
  let mockPageContext;

  beforeEach(() => {
    jest.clearAllMocks();
    mockPageContext = createMockPageContext({
      videoList: createMockVideoList(5)
    });
  });

  afterEach(() => {
    if (mainController) {
      mainController.destroy();
    }
  });

  describe('系统初始化集成测试', () => {
    test('应该完整初始化整个系统', async () => {
      mainController = new MainController(mockPageContext);
      
      // 执行完整初始化
      await mainController.init();
      
      // 验证系统状态
      expect(mainController.initialized).toBe(true);
      expect(mainController.pageState).toBe('loaded');
      
      // 验证所有核心模块都已注册
      expect(mainController.modules.size).toBeGreaterThan(0);
      
      // 验证API兼容层已初始化
      expect(mainController.apiCompatibility).toBeDefined();
      
      // 验证统一错误处理器已初始化
      expect(mainController.unifiedErrorHandler).toBeDefined();
      
      // 验证模块状态
      const status = mainController.getModulesStatus();
      expect(status.totalModules).toBeGreaterThan(0);
    });

    test('应该正确处理模块依赖关系', async () => {
      mainController = new MainController(mockPageContext);
      
      // 注册模块和设置依赖
      mainController.registerModules();
      mainController.setupModuleDependencies();
      
      // 获取初始化顺序
      const initOrder = mainController.getModuleInitOrder();
      
      // 验证依赖顺序
      const dataManagerIndex = initOrder.indexOf('DataManager');
      const videoListIndex = initOrder.indexOf('VideoList');
      const videoPlayerIndex = initOrder.indexOf('VideoPlayer');
      
      // DataManager 应该在 VideoList 之前
      expect(dataManagerIndex).toBeLessThan(videoListIndex);
      
      // VideoList 应该在 VideoPlayer 之前
      expect(videoListIndex).toBeLessThan(videoPlayerIndex);
    });

    test('应该建立模块间通信', async () => {
      mainController = new MainController(mockPageContext);
      await mainController.init();
      
      // 验证通信器存在
      expect(mainController.communicator).toBeDefined();
      
      // 验证事件转发设置
      mainController.setupModuleEventForwarding();
      
      // 模拟模块间事件通信
      const testEventData = { test: 'data' };
      mainController.communicator.emit('videoListUpdated', testEventData);
      
      // 验证通信不抛出错误
      expect(mainController.communicator).toBeDefined();
    });
  });

  describe('API兼容性集成测试', () => {
    beforeEach(async () => {
      mainController = new MainController(mockPageContext);
      await mainController.init();
    });

    test('应该保持完整的API兼容性', () => {
      // 验证关键API方法存在
      expect(typeof mockPageContext.loadVideoList).toBe('function');
      expect(typeof mockPageContext.onVideoPlay).toBe('function');
      expect(typeof mockPageContext.onSearchTap).toBe('function');
      expect(typeof mockPageContext.onShareAppMessage).toBe('function');
      expect(typeof mockPageContext.handleScroll).toBe('function');
      
      // 验证生命周期方法存在
      expect(typeof mockPageContext.onLoad).toBe('function');
      expect(typeof mockPageContext.onShow).toBe('function');
      expect(typeof mockPageContext.onHide).toBe('function');
      expect(typeof mockPageContext.onUnload).toBe('function');
    });

    test('应该正确代理API调用到目标模块', () => {
      // 测试生命周期方法调用
      expect(() => {
        mockPageContext.onLoad({ test: 'param' });
        mockPageContext.onShow();
        mockPageContext.onHide();
        mockPageContext.onUnload();
      }).not.toThrow();
      
      // 测试业务方法调用
      expect(() => {
        mockPageContext.loadVideoList(true);
        mockPageContext.refreshVideoList();
        mockPageContext.clearSearch();
      }).not.toThrow();
      
      // 测试事件方法调用
      expect(() => {
        mockPageContext.onPullDownRefresh();
        mockPageContext.onReachBottom();
        mockPageContext.handleScroll({ detail: { scrollTop: 100 } });
      }).not.toThrow();
    });

    test('应该提供降级处理', () => {
      // 模拟模块不可用的情况
      const originalGetModule = mainController.getModule;
      mainController.getModule = jest.fn(() => null);
      
      // 调用API方法应该不抛出错误
      expect(() => {
        mockPageContext.loadVideoList();
        mockPageContext.formatCount(1500);
        mockPageContext.sanitizeVideoData({ id: 'test' });
      }).not.toThrow();
      
      // 恢复原方法
      mainController.getModule = originalGetModule;
    });
  });

  describe('错误处理集成测试', () => {
    beforeEach(async () => {
      mainController = new MainController(mockPageContext);
      await mainController.init();
    });

    test('应该统一处理各种类型的错误', async () => {
      const errorHandler = mainController.unifiedErrorHandler;
      
      // 测试不同类型的错误处理
      const networkError = createTestError('网络连接失败', 'NETWORK_ERROR');
      const moduleError = createTestError('模块错误', 'MODULE_ERROR');
      const videoError = createTestError('视频播放失败', 'VIDEO_PLAY_ERROR');
      
      // 处理错误
      const networkResult = await errorHandler.handleError(networkError, { source: 'network' });
      const moduleResult = await errorHandler.handleError(moduleError, { source: 'module' });
      const videoResult = await errorHandler.handleError(videoError, { source: 'video' });
      
      // 验证错误处理结果
      expect(networkResult).toHaveProperty('errorType', 'NETWORK_ERROR');
      expect(moduleResult).toHaveProperty('errorType', 'MODULE_ERROR');
      expect(videoResult).toHaveProperty('errorType', 'VIDEO_PLAY_ERROR');
      
      // 验证错误统计
      const errorStats = errorHandler.getErrorStats();
      expect(errorStats.totalErrors).toBe(3);
    });

    test('应该触发错误恢复机制', async () => {
      const errorHandler = mainController.unifiedErrorHandler;
      
      // 模拟模块错误
      const moduleError = createTestError('模块初始化失败', 'MODULE_ERROR');
      
      // 处理错误并等待恢复
      await errorHandler.handleError(moduleError, { 
        source: 'module',
        moduleName: 'VideoList'
      });
      
      // 验证恢复机制被触发
      expect(errorHandler.errorStats.totalErrors).toBeGreaterThan(0);
    });

    test('应该处理高频错误', async () => {
      const errorHandler = mainController.unifiedErrorHandler;
      
      // 模拟高频错误
      const testError = createTestError('高频测试错误', 'TEST_ERROR');
      
      // 连续处理多个相同错误
      for (let i = 0; i < 5; i++) {
        await errorHandler.handleError(testError, { source: 'test' });
      }
      
      // 验证错误统计
      const errorStats = errorHandler.getErrorStats();
      expect(errorStats.totalErrors).toBe(5);
    });
  });

  describe('模块协作集成测试', () => {
    beforeEach(async () => {
      mainController = new MainController(mockPageContext);
      await mainController.init();
    });

    test('应该协调模块生命周期', async () => {
      // 验证所有模块都已初始化
      const status = mainController.getModulesStatus();
      expect(status.totalModules).toBeGreaterThan(0);
      
      // 模拟模块状态变化
      mainController.handleModuleStateChange('VideoList', { 
        current: 'active',
        previous: 'idle'
      });
      
      // 验证状态管理
      expect(mainController.moduleStates.get('VideoList')).toBe('active');
    });

    test('应该处理模块间事件传递', () => {
      // 设置事件转发
      mainController.setupModuleEventForwarding();
      
      // 模拟事件传递
      const eventData = { videoId: 'test_video', action: 'play' };
      
      // 触发事件不应该抛出错误
      expect(() => {
        mainController.communicator.emit('videoListUpdated', eventData);
        mainController.communicator.emit('playStateChange', eventData);
        mainController.communicator.emit('searchStateChange', eventData);
      }).not.toThrow();
    });

    test('应该执行健康检查和恢复', () => {
      // 执行健康检查
      mainController.performHealthCheck();
      
      // 模拟不健康的模块
      const unhealthyModules = [
        { name: 'VideoList', reason: 'not_responding' }
      ];
      
      // 处理不健康模块
      expect(() => {
        mainController.handleUnhealthyModules(unhealthyModules);
      }).not.toThrow();
    });
  });

  describe('完整业务流程集成测试', () => {
    beforeEach(async () => {
      mainController = new MainController(mockPageContext);
      await mainController.init();
    });

    test('应该完整执行页面加载流程', async () => {
      // 模拟页面加载
      const loadOptions = { videoId: 'shared_video' };
      
      // 执行页面加载
      await mockPageContext.onLoad(loadOptions);
      
      // 验证页面状态
      expect(mockPageContext.setData).toHaveBeenCalled();
    });

    test('应该完整执行视频播放流程', () => {
      // 模拟视频点击事件
      const videoEvent = {
        currentTarget: {
          dataset: {
            video: {
              id: 'test_video',
              videoUrl: 'http://test.com/video.mp4'
            }
          }
        }
      };
      
      // 执行视频播放流程
      expect(() => {
        mockPageContext.handleVideoTap(videoEvent);
      }).not.toThrow();
    });

    test('应该完整执行搜索流程', () => {
      // 模拟搜索事件
      const searchEvent = {
        detail: {
          value: '测试搜索',
          results: createMockVideoList(3)
        }
      };
      
      // 执行搜索流程
      expect(() => {
        mockPageContext.onSearchTap(searchEvent);
        mockPageContext.onSearchInput(searchEvent);
        mockPageContext.onSearchConfirm(searchEvent);
      }).not.toThrow();
    });

    test('应该完整执行分享流程', () => {
      // 模拟分享事件
      const shareEvent = {
        from: 'button',
        target: {
          dataset: {
            video: {
              id: 'test_video',
              mainTitle: '测试视频'
            }
          }
        }
      };
      
      // 执行分享流程
      const shareResult = mockPageContext.onShareAppMessage(shareEvent);
      expect(shareResult).toBeDefined();
    });

    test('应该完整执行刷新流程', async () => {
      // 执行下拉刷新
      await mockPageContext.onPullDownRefresh();
      
      // 验证刷新逻辑
      expect(wx.stopPullDownRefresh).toHaveBeenCalled();
    });

    test('应该完整执行滚动处理流程', () => {
      // 模拟滚动事件
      const scrollEvent = {
        detail: { scrollTop: 150 }
      };
      
      // 执行滚动处理
      expect(() => {
        mockPageContext.handleScroll(scrollEvent);
      }).not.toThrow();
    });
  });

  describe('系统优化集成测试', () => {
    beforeEach(async () => {
      mainController = new MainController(mockPageContext);
      await mainController.init();
    });

    test('应该根据错误分析执行系统优化', () => {
      // 模拟错误分析结果
      const analysis = {
        totalErrors: 20,
        recoveryRate: '60%',
        recommendations: [
          { type: 'network_optimization', priority: 'high' },
          { type: 'module_stability', priority: 'medium' }
        ]
      };
      
      // 处理错误分析
      expect(() => {
        mainController.handleErrorAnalysis(analysis);
      }).not.toThrow();
    });

    test('应该执行网络状态适应', () => {
      // 模拟网络状态变化
      const networkInfo = {
        networkType: 'wifi',
        isConnected: true
      };
      
      // 处理网络恢复
      expect(() => {
        mainController.handleNetworkRecovery(networkInfo);
      }).not.toThrow();
    });

    test('应该执行离线模式切换', () => {
      // 切换到离线模式
      expect(() => {
        mainController.switchToOfflineMode();
      }).not.toThrow();
    });
  });

  describe('性能集成测试', () => {
    beforeEach(async () => {
      mainController = new MainController(mockPageContext);
      await mainController.init();
    });

    test('应该高效处理大量并发操作', async () => {
      const startTime = Date.now();
      
      // 并发执行多种操作
      const operations = [
        () => mockPageContext.loadVideoList(),
        () => mockPageContext.handleScroll({ detail: { scrollTop: 100 } }),
        () => mockPageContext.onSearchInput({ detail: { value: '测试' } }),
        () => mockPageContext.formatCount(1500),
        () => mockPageContext.sanitizeVideoData({ id: 'test' })
      ];
      
      // 执行100次并发操作
      const promises = [];
      for (let i = 0; i < 100; i++) {
        const operation = operations[i % operations.length];
        promises.push(Promise.resolve(operation()));
      }
      
      await Promise.all(promises);
      
      const endTime = Date.now();
      const executionTime = endTime - startTime;
      
      // 100次并发操作应该在500ms内完成
      expect(executionTime).toBeLessThan(500);
    });

    test('应该高效处理模块状态查询', () => {
      const startTime = Date.now();
      
      // 执行大量状态查询
      for (let i = 0; i < 1000; i++) {
        mainController.getModulesStatus();
        mainController.getErrorStats();
      }
      
      const endTime = Date.now();
      const executionTime = endTime - startTime;
      
      // 1000次状态查询应该在100ms内完成
      expect(executionTime).toBeLessThan(100);
    });
  });

  describe('边界情况集成测试', () => {
    test('应该处理系统初始化失败', async () => {
      mainController = new MainController(mockPageContext);
      
      // 模拟初始化错误
      const originalRegisterModules = mainController.registerModules;
      mainController.registerModules = jest.fn(() => {
        throw new Error('模块注册失败');
      });
      
      // 初始化应该失败但不崩溃
      await expect(mainController.init()).rejects.toThrow();
      
      // 恢复原方法
      mainController.registerModules = originalRegisterModules;
    });

    test('应该处理模块部分失败的情况', async () => {
      mainController = new MainController(mockPageContext);
      
      // 模拟部分模块初始化失败
      mainController.failedModules.add('VideoPlayer');
      mainController.failedModules.add('Search');
      
      await mainController.init();
      
      // 系统应该仍然可以工作
      expect(mainController.initialized).toBe(true);
      expect(mainController.failedModules.size).toBeGreaterThan(0);
    });

    test('应该处理API兼容层失败', async () => {
      mainController = new MainController(mockPageContext);
      
      // 模拟API兼容层初始化失败
      const originalInitAPI = mainController.initAPICompatibility;
      mainController.initAPICompatibility = jest.fn(() => {
        throw new Error('API兼容层初始化失败');
      });
      
      // 系统初始化应该处理这个错误
      await mainController.init();
      
      // 恢复原方法
      mainController.initAPICompatibility = originalInitAPI;
    });

    test('应该处理错误处理器失败', async () => {
      mainController = new MainController(mockPageContext);
      
      // 模拟错误处理器初始化失败
      const originalInitErrorHandler = mainController.initUnifiedErrorHandler;
      mainController.initUnifiedErrorHandler = jest.fn(() => {
        throw new Error('错误处理器初始化失败');
      });
      
      // 系统初始化应该处理这个错误
      await mainController.init();
      
      // 恢复原方法
      mainController.initUnifiedErrorHandler = originalInitErrorHandler;
    });
  });

  describe('资源清理集成测试', () => {
    test('应该完整清理所有系统资源', async () => {
      mainController = new MainController(mockPageContext);
      await mainController.init();
      
      // 验证初始化状态
      expect(mainController.modules.size).toBeGreaterThan(0);
      expect(mainController.apiCompatibility).toBeDefined();
      expect(mainController.unifiedErrorHandler).toBeDefined();
      
      // 执行销毁
      mainController.destroy();
      
      // 验证清理状态
      expect(mainController.modules.size).toBe(0);
      expect(mainController.moduleStates.size).toBe(0);
      expect(mainController.failedModules.size).toBe(0);
    });
  });
});