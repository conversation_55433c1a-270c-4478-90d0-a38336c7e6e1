<!-- 指向广告管理页面 -->
<view class="page-container">
  <!-- 页面头部 -->
  <view class="page-header">
    <view class="back-icon" bindtap="navigateBack">
      <text class="iconfont icon-back">←</text>
    </view>
    <view class="header-title">指向广告管理</view>
    <view class="header-actions">
    </view>
  </view>

  <!-- 内容区域 -->
  <scroll-view class="content-admin-container" scroll-y="true" enable-back-to-top="true">
    <!-- 配置表单 -->
    <view class="form-container">
      <!-- 广告图片上传 -->
      <view class="form-item">
        <view class="form-label">广告图片</view>
        <view class="upload-container">
          <view class="upload-area" bindtap="chooseImage">
            <image 
              wx:if="{{config.imageUrl}}" 
              class="preview-image" 
              src="{{config.imageUrl}}" 
              mode="aspectFit"
            />
            <view wx:else class="upload-placeholder">
              <text class="upload-icon">+</text>
              <text class="upload-text">点击上传图片</text>
            </view>
            
            <!-- 上传中状态 -->
            <view wx:if="{{uploading}}" class="upload-loading">
              <text class="loading-text">上传中...</text>
            </view>
          </view>
          <view class="upload-tips">
            <text>支持jpg、png、gif格式，建议尺寸16:9或4:3，不超过2MB</text>
          </view>
        </view>
      </view>
      
      <!-- 跳转内容选择 -->
      <view class="form-item">
        <view class="form-label">跳转内容</view>
        <view class="content-selector" bindtap="showContentSelector">
          <view class="selector-display">
            <text class="selected-content" wx:if="{{selectedContent.title}}">
              {{selectedContent.title}}
            </text>
            <text class="placeholder-text" wx:else>
              点击选择要跳转的内容
            </text>
            <text class="selector-arrow">></text>
          </view>
        </view>
        <view class="input-tips">
          <text>选择用户点击广告后要跳转到的具体内容</text>
        </view>
      </view>
      
      <!-- 触发时间选择 -->
      <view class="form-item">
        <view class="form-label">触发时间</view>
        <picker 
          class="form-picker"
          mode="selector" 
          range="{{triggerTimeOptions}}"
          value="{{triggerTimeIndex}}"
          bindchange="onTriggerTimeChange"
        >
          <view class="picker-display">
            <text>{{triggerTimeOptions[triggerTimeIndex]}}</text>
            <text class="picker-arrow">></text>
          </view>
        </picker>
        <view class="input-tips">
          <text>用户在小程序停留多长时间后弹出广告</text>
        </view>
      </view>
      
      <!-- 启用状态开关 -->
      <view class="form-item">
        <view class="form-label">启用状态</view>
        <switch 
          class="form-switch"
          checked="{{config.enabled}}"
          bindchange="onEnabledChange"
        />
        <view class="input-tips">
          <text>关闭后广告将不会显示</text>
        </view>
      </view>
    </view>
    
    <!-- 保存按钮 -->
    <view class="save-container">
      <button
        class="save-btn {{saving ? 'saving' : ''}}"
        bindtap="saveConfig"
        disabled="{{saving}}"
      >
        {{saving ? '保存中...' : '保存设置'}}
      </button>
    </view>
  </scroll-view>

  <!-- 内容选择弹窗 -->
  <view class="content-modal" wx:if="{{showContentModal}}">
    <view class="modal-mask" bindtap="hideContentSelector"></view>
    <view class="modal-content">
      <view class="modal-header">
        <text class="modal-title">选择跳转内容</text>
        <view class="modal-close" bindtap="hideContentSelector">×</view>
      </view>

      <!-- 内容分类选择 -->
      <view class="content-tabs">
        <view
          class="tab-item {{contentType === 'none' ? 'active' : ''}}"
          bindtap="switchContentType"
          data-type="none"
        >
          无跳转
        </view>
        <view
          class="tab-item {{contentType === 'service' ? 'active' : ''}}"
          bindtap="switchContentType"
          data-type="service"
        >
          服务内容
        </view>
        <view
          class="tab-item {{contentType === 'video' ? 'active' : ''}}"
          bindtap="switchContentType"
          data-type="video"
        >
          视频内容
        </view>
        <view
          class="tab-item {{contentType === 'gallery' ? 'active' : ''}}"
          bindtap="switchContentType"
          data-type="gallery"
        >
          画廊内容
        </view>
        <view
          class="tab-item {{contentType === 'recharge' ? 'active' : ''}}"
          bindtap="switchContentType"
          data-type="recharge"
        >
          充值优惠
        </view>
      </view>

      <!-- 内容列表 -->
      <scroll-view class="content-list" scroll-y="true">
        <view class="loading-container" wx:if="{{loadingContent}}">
          <text class="loading-text">加载中...</text>
        </view>

        <view class="content-item"
              wx:for="{{contentList}}"
              wx:key="id"
              bindtap="selectContent"
              data-item="{{item}}"
        >
          <!-- 无跳转选项特殊显示 -->
          <view wx:if="{{item.type === 'none'}}" class="no-jump-indicator">
            <text class="no-jump-text">无跳转</text>
          </view>
          <!-- 其他选项正常显示图片 -->
          <image wx:else class="content-cover" src="{{item.coverUrl}}" mode="aspectFill"></image>

          <view class="content-info">
            <text class="content-title">{{item.title}}</text>
            <text class="content-subtitle" wx:if="{{item.subtitle}}">{{item.subtitle}}</text>
          </view>
          <view class="select-icon" wx:if="{{selectedContent.id === item.id}}">✓</view>
        </view>

        <view class="empty-content" wx:if="{{!loadingContent && contentList.length === 0}}">
          <text>暂无内容</text>
        </view>
      </scroll-view>
    </view>
  </view>
</view>
