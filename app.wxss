/**app.wxss**/
/**
 * 全局样式变量配置
 * 
 * 本文件定义了整个应用的全局样式变量，方便统一管理和修改
 * 
 * 使用方法：
 * 1. 在组件中使用 var(--变量名) 引用下面定义的变量
 * 2. 修改此处的变量值即可全局生效，无需修改各组件样式
 */

page {
  /*=============================================
   * 1. 基础颜色系统
   * 修改这些值会影响整个应用外观
   *=============================================*/
  
  /* 主题颜色 */
  --primary-gradient: linear-gradient(to top,rgb(75, 75, 75), #000000);  /* 主渐变：底部灰色到顶部黑色 */
  --primary-color: #000000;       /* 主色：黑色(与渐变顶部一致) */
  --secondary-color: #646464;     /* 辅助色：灰色(与渐变底部一致) */
  --accent-color:rgba(30, 143, 255, 0);        /* 强调色：蓝色 */
  
  /* 背景颜色 */
  --bg-gradient: var(--primary-gradient); /* 全局渐变背景 */
  --bg-mask: rgba(0, 0, 0, 0.5);          /* 全局遮罩层背景 */
  --bg-light: #333333;                    /* 浅色背景(深色主题) */
  --bg-dark: #000000;                     /* 深色背景 */
  --bg-gray: #222222;                     /* 灰色背景 */
  
  /* 文本颜色 */
  --text-primary: #ffffff;                     /* 主要文本：白色 */
  --text-secondary: rgba(255, 255, 255, 0.8);  /* 次要文本：80%白色 */
  --text-disabled: rgba(255, 255, 255, 0.4);   /* 禁用文本：40%白色 */
  --text-dark: #ffffff;                        /* 深色文本：白色 */
  --text-medium: rgba(255, 255, 255, 0.7);     /* 中等文本：70%白色 */
  --text-light: rgba(255, 255, 255, 0.5);      /* 浅色文本：50%白色 */
  
  /* 边框和分割线 */
  --border-color: rgba(255, 255, 255, 0.2);    /* 主要边框：20%白色 */
  --divider-color: rgba(255, 255, 255, 0.1);   /* 分割线：10%白色 */
  --border-light: rgba(255, 255, 255, 0.15);   /* 浅色边框：15%白色 */
  
  /* 圆角大小 */
  --border-radius-sm: 8rpx;   /* 小圆角 */
  --border-radius-md: 16rpx;  /* 中圆角 */
  --border-radius-lg: 24rpx;  /* 大圆角 */
  
  /* 状态颜色 */
  --info-color: #2196f3;      /* 信息色：蓝色 */
  --success-color:rgb(221, 231, 222);   /* 成功色：绿色 */
  --warning-color: #FFC107;   /* 警告色：黄色 */
  --error-color: #F44336;     /* 错误色：红色 */

  /*=============================================
   * 2. 组件通用样式
   * 这些变量可用于多个组件
   *=============================================*/
  
  /* 卡片通用样式 */
  --card-bg: var(--bg-light);              /* 卡片背景色 */
  --card-title: var(--text-dark);          /* 卡片标题色 */
  --card-text: var(--text-medium);         /* 卡片文字色 */
  
  /* 按钮通用样式 */
  --button-primary-bg: var(--primary-color);    /* 主按钮背景色 */
  --button-primary-text: var(--text-primary);   /* 主按钮文字色 */
  --button-secondary-bg: var(--bg-gray);        /* 次按钮背景色 */
  --button-secondary-text: var(--text-medium);  /* 次按钮文字色 */
  
  /* 图标通用样式 */
  --icon-active: var(--accent-color);      /* 激活状态图标颜色 */
  --icon-inactive: var(--text-light);      /* 未激活状态图标颜色 */
  
  /* 导航栏通用样式 */
  --tab-bar-height: 96rpx;                 /* 导航栏高度 */
  --tab-bar-bg: var(--primary-color);      /* 导航栏背景色 */
  --tab-active: var(--accent-color);       /* 导航栏激活项颜色 */
  --tab-inactive: var(--text-light);       /* 导航栏未激活项颜色 */

  /*=============================================
   * 3. 视频卡片组件样式(video-card)
   *=============================================*/
  
  /* 基础样式 */
  --videocard-bg: rgba(223, 193, 193, 0.1);       /* 卡片背景：10%白色 */
  --videocard-gradient: linear-gradient(to top, rgb(19, 18, 18), rgb(66, 77, 67)); /* 卡片渐变：从底部黑色到顶部透明 */
  --videocard-border: rgba(255, 255, 255, 0);     /* 卡片边框：透明 */
  --videocard-shadow: rgba(0, 0, 0, 0);         /* 卡片阴影：30%黑色 */
  --videocard-scale-amount: 0.97;                 /* 缩放效果量：0.97 */
  
  /* 文字颜色 */
  --videocard-title: rgb(252, 252, 249);            /* 标题颜色：白色 */
  --videocard-subtitle: rgba(189, 189, 187); /* 副标题颜色：70%白色 */
  --videocard-tag-text: rgba(189, 189, 187);/* 标签文字：85%白色 */
  --videocard-play-count-text:rgb(189, 189, 187);           /* 播放量文字：白色 */
  --videocard-time-text: #ffffff;                 /* 时间文字：白色 */
  
  /* 图标颜色 */
  --videocard-play-icon-bg: rgba(0, 0, 0, 0.7);   /* 播放图标背景：70%黑色 */
  --videocard-play-icon: #ffffff;                 /* 播放图标三角：白色 */
  --videocard-decoration-1: rgb(255, 255, 255);   /* 装饰图标1：白色 */
  --videocard-decoration-2: rgb(255, 255, 255);   /* 装饰图标2：白色 */
  --videocard-mute-icon: #ffffff;                 /* 静音图标：白色 */
  
  /* 容器背景色 */
  --videocard-main-title-bg: rgba(255, 255, 255, 0);             /* 标题容器背景：透明 */
  --videocard-main-title-gradient: linear-gradient(to top, rgba(0, 0, 0, 0), rgba(0, 0, 0, 0)); /* 标题容器渐变：底黑顶透明 */
  --videocard-subtitle-bg: rgba(0, 0, 0, 0);                     /* 副标题容器背景：透明 */
  --videocard-subtitle-gradient: linear-gradient(to top, rgba(0, 0, 0, 0), rgba(0, 0, 0, 0)); /* 副标题容器渐变：底黑顶透明 */
  --videocard-avatar-bg: rgba(100, 100, 100, 0.5);               /* 头像容器背景：50%灰色 */
  --videocard-video-bg: rgba(0, 0, 0, 0);                        /* 视频容器背景：透明 */
  --videocard-cover-bg:rgba(0, 0, 0, 0);                                 /* 封面背景：黑色 */
  --videocard-play-count-bg: rgba(255, 255, 255, 0.1);          /* 播放量容器背景：27%白色 */
  --videocard-tag-bg: rgba(255, 255, 255, 0);                 /* 标签容器背景：27%白色 */
  --videocard-decoration-bg: rgba(255, 0, 0, 0);                 /* 装饰图标容器背景：透明 */
  
  /* 尺寸和圆角 */
  --videocard-logo-size: 75rpx;                   /* Logo大小：75rpx */
  --videocard-video-radius: 30rpx 30rpx 45rpx 45rpx; /* 视频容器圆角：增大顶部圆角 */
  --videocard-play-count-radius: 10rpx;           /* 播放量容器圆角 */
  --videocard-tag-radius: 10rpx;                  /* 标签容器圆角 */
  
  /* 动画效果 */
  --videocard-decoration-animation-duration: 0.5s;           /* 装饰图标动画时长 */
  --videocard-decoration-animation-timing: infinite ease-in; /* 装饰图标动画函数 */
  --videocard-decoration-size: 76rpx;                        /* 装饰图标容器大小 */
  --videocard-decoration-icon-size: 45rpx;                   /* 装饰图标内部大小 */
  
  /*=============================================
   * 4. 搜索导航组件样式(search-nav)
   *=============================================*/
  
  /* 背景和基础样式 */
  --searchnav-bg: linear-gradient(to top, rgb(7, 7, 7), #000000); /* 背景渐变：底部暗灰到顶部黑色 */
  --searchnav-text: #ffffff;                                       /* 文字颜色：白色 */
  --searchnav-icon: #ffffff;                                       /* 图标颜色：白色 */
  
  /* 输入框样式 */
  --searchnav-input-bg: rgb(20, 20, 20);                     /* 未输入框背景 */
  --searchnav-input-focus-bg: rgb(31, 31, 31);                  /* 输入框背景 */
  --searchnav-placeholder: rgb(37, 37, 37);               /* 占位符颜色：50%白色 */
  
  /* 搜索结果和建议区域 */
  --searchnav-suggestion-bg: rgba(23, 29, 23, 0.95);                   /* 建议区域背景：95%黑色，确保不透底 */
  --searchnav-keyword-bg: rgba(99, 99, 99, 0.2);                  /* 关键词背景：20%红色 */
  --searchnav-keyword-active-bg: rgba(211, 94, 94, 0.4);           /* 激活关键词背景：40%红色 */
  --searchnav-section-title:rgb(128, 128, 128);                              /* 分区标题：红色 */
  --searchnav-keyword-text: #ffffff;                               /* 关键词文字：白色 */
  --searchnav-result-bg: rgba(0, 0, 0, 0.6);                       /* 结果项背景：60%黑色 */
  --searchnav-result-hover-bg: rgba(0, 0, 0, 0.8);                 /* 结果项悬停：80%黑色 */
  --searchnav-result-title: #ffffff;                               /* 结果标题：白色 */
  --searchnav-result-subtitle: rgba(255, 255, 255, 0.7);           /* 结果副标题：70%白色 */
  --searchnav-highlight:rgb(189, 182, 182);                                  /* 高亮颜色：红色 */

  /*=============================================
   * 5. 分类导航栏样式(category-nav)
   *=============================================*/
  
  /* 背景和文本 */
  --categorynav-bg: linear-gradient(to top, rgb(7, 7, 7), #000000); /* 背景渐变：底部暗灰到顶部黑色 */
  --categorynav-text:rgb(124, 124, 124);                                       /* 文本颜色：白色 */
  --categorynav-text-active: rgb(255, 255, 255);                     /* 激活文本：白色 */
  
  /* 分类项样式 */
  --categorynav-item-bg: rgba(117, 117, 117, 0.1);                            /* 分类项背景：深灰 */
  --categorynav-item-active-bg: rgba(255, 255, 255, 0.16);               /* 激活背景：40%深灰 */
  --categorynav-item-height: 60rpx;                                  /* 分类项高度 */
  --categorynav-item-radius: 15rpx;                                  /* 分类项圆角 */
  --categorynav-item-margin: 20rpx;                                  /* 分类项间距 */
  --categorynav-font-size: 30rpx;                                    /* 分类文本大小 */

  /*=============================================
   * 6. 启动页样式(launch)
   *=============================================*/
  
  /* 背景和容器 */
  --launch-container-bg: linear-gradient(to top, rgba(0, 0, 0, 0), rgba(0, 0, 0, 0)); /* 容器背景：透明渐变 */
  --launch-overlay-bg: rgba(0, 0, 0, 0);                                              /* 遮罩层背景：透明 */
  
  /* 过渡和动画 */
  --launch-first-image-transition: 1s;                                                /* 首图过渡时间 */
  --launch-second-image-transition: 2s;                                               /* 次图过渡时间 */
  --launch-fade-out-transition: 0.8s;                                                 /* 淡出过渡时间 */
  
  /* 图片效果 */
  --launch-image-opacity: 1;                                                          /* 图片不透明度：完全不透明 */
  --launch-image-blend-mode: normal;                                                  /* 图片混合模式：正常 */
  
  /* 错误和加载提示 */
  --launch-error-text: #ffffff;                                                       /* 错误文字：白色 */
  --launch-error-bg: rgba(0, 0, 0, 0.7);                                             /* 错误背景：70%黑色 */
  --launch-loading-spinner-border: #333333;                                           /* 加载器边框：深灰 */
  --launch-loading-spinner-top: #d35e5e;                                              /* 加载器顶边：红色 */
  --launch-loading-text: #ffffff;                                                     /* 加载文字：白色 */

  /*=============================================
   * 7. 画廊页面样式(gallery)
   *=============================================*/
  
  /* 基础样式 */
  --gallery-bg: linear-gradient(to top,rgb(75, 75, 75),rgb(0, 0, 0));                           /* 页面背景：白色底部到黑色顶部渐变 */
  --gallery-loading-bg: linear-gradient(to top,rgba(204, 125, 125, 0),rgba(63, 16, 194, 0));                    /* 加载背景：红黑渐变 */
  --gallery-loading-spinner:rgb(255, 255, 255);                                                 /* 加载指示器：红色 */
  
  /* 信息卡片样式 */
  --gallery-card-bg: linear-gradient(to top,rgb(34, 34, 34), rgb(0, 0, 0));  /* 卡片背景：底部深红到顶部浅红渐变 */
  --gallery-card-shadow: rgba(255, 255, 255, 0.1);                                      /* 卡片阴影：30%红色 */
  --gallery-card-scale: 0.97;                                                         /* 卡片缩放比例 */
  --gallery-card-border-radius: 40rpx;                                                /* 卡片圆角 */
  
  /* 图片容器样式 */
  --gallery-image-container-bg: rgba(0, 0, 0, 0.5);                                   /* 图片容器背景：50%黑色 */
  --gallery-image-border-radius: 28rpx;                                               /* 图片容器圆角 */
  
  /* 文字样式 */
  --gallery-title-color: #ffffff;                                                     /* 标题颜色：白色 */
  --gallery-description-color: rgba(255, 255, 255, 0.8);                              /* 描述颜色：80%白色 */
  --gallery-creator-color:rgb(255, 255, 255);                                                   /* 创作者颜色：红色 */
  
  /* 装饰图标样式 */
  --gallery-decoration-size: 70rpx;                                                   /* 装饰图标大小 */
  --gallery-icon-size: 42rpx;                                                         /* 图标大小 */
  
  /* 脉冲动画样式 */
  --gallery-pulse-duration: 0.7s;                                                     /* 脉冲动画持续时间 */
  --gallery-pulse-timing: infinite ease-in-out;                                       /* 脉冲动画函数 */
  --gallery-pulse-scale-min: 0.95;                                                    /* 脉冲缩放最小值 */
  --gallery-pulse-scale-max: 1.05;                                                    /* 脉冲缩放最大值 */
  --gallery-pulse-opacity-min: 0.7;                                                   /* 脉冲透明度最小值 */
  --gallery-pulse-opacity-max: 1;                                                     /* 脉冲透明度最大值 */
  
  /* 提示文本样式 */
  --gallery-hint-text-color: rgba(255, 255, 255, 0.7);                                /* 提示文本颜色：70%白色 */
  --gallery-hint-text-opacity: 0.9;                                                   /* 提示文本透明度 */

  /*=============================================
   * 8. 画廊详情模态框样式(gallery-detail-modal)
   *=============================================*/
  
  /* 基础样式 */
  --gallerymodal-bg: rgba(29, 29, 29, 0.9);                         /* 模态框背景：顶红到底灰渐变，改为半透明 */
  --gallerymodal-text: #ffffff;                                                       /* 模态框文本：白色 */
  --gallerymodal-header-text:rgb(0, 0, 0);                                                /* 头部文本：白色 */
  --gallerymodal-header-active-bg: rgba(235, 0, 0, 0.1);                          /* 头部按钮激活背景色 */
  --gallerymodal-video-bg: #000000;                                                  /* 视频背景色 */
  --gallerymodal-section-bg: rgba(0, 0, 0, 0.1);                                     /* 区块背景色 */
  --gallerymodal-section-title-bg: rgba(0, 0, 0, 0.2);                               /* 区块标题背景色 */
  --gallerymodal-debug-bg: rgba(0, 0, 0, 0.3);                                       /* 调试信息背景色 */
  --gallerymodal-shadow: rgba(0, 0, 0, 0.1);                                         /* 阴影颜色 */
  
  /* 内容区域 */
  --gallerymodal-content-bg: rgb(255, 238, 238);                                      /* 内容背景：80%黑色 */
  --gallerymodal-content-text: rgb(34, 34, 34);                                               /* 内容文本：白色 */
  --gallerymodal-detail-bg: rgb(255, 255, 255);                                  /* 内容卡片背景色：红色 */
  --gallerymodal-detail-text: rgb(34, 34, 34);                                            /* 内容卡片文本色 */
  --gallerymodal-contact-button-bg: rgba(255, 255, 255, 0.9);                         /* 联系按钮背景色 */
  --gallerymodal-contact-button-text: #333333;                                        /* 联系按钮文本色 */
  --gallerymodal-footer-bg: rgba(25, 25, 25, 0.2);                                       /* 底部背景：磨砂玻璃效果 */
  --gallerymodal-footer-blur: blur(15px);                                          /* 底部模糊效果 */
  --gallerymodal-footer-border: 0.5px solid rgba(255, 255, 255, 0.1);              /* 底部边框 */
  --gallerymodal-footer-height: 140rpx;                                              /* 底部控制条高度：与视频详情保持一致 */
  --gallerymodal-button-text:rgb(255, 255, 255);                                                /* 按钮文本：红色 */
  --gallerymodal-decoration-bg: rgba(255, 255, 255, 0);                           /* 装饰按钮背景：5%白色 */
  
  /* 按钮样式 */
  --gallerymodal-button-icon-size: 56rpx;                                            /* 按钮图标尺寸 */
  --gallerymodal-contact-icon-size: 72rpx;                                           /* 联系图标尺寸 */
  --gallerymodal-decoration-size: 110rpx;                                            /* 装饰背景尺寸 */
  --gallerymodal-decoration-blur: blur(5px);                                         /* 装饰背景模糊效果 */
  --gallerymodal-button-filter: brightness(0) invert(1);                             /* 按钮图标滤镜：白色 */
  
  /* 动画效果 */
  --gallerymodal-pulse-duration: 0.7s;                                               /* 脉冲动画持续时间 */
  --gallerymodal-pulse-timing: infinite ease-in;                                     /* 脉冲动画函数 */
  --gallerymodal-pulse-scale-min: 0.8;                                               /* 脉冲缩放最小值 */
  --gallerymodal-pulse-scale-max: 1;                                                 /* 脉冲缩放最大值 */
  
  /* 加载和交互 */
  --gallerymodal-loading-dot: rgb(92, 92, 92);                                 /* 加载点：80%红色 */
  --gallerymodal-loading-text: rgba(24, 24, 24, 0.9);                              /* 加载文本：90%白色 */
  --gallerymodal-drag-indicator: rgba(255, 255, 255, 0.8);                            /* 拖动指示器：80%白色 */
  
  /* 边框和分割线 */
  --gallerymodal-border: rgba(211, 94, 94, 0.2);                                      /* 边框颜色：20%红色 */
  --gallerymodal-divider: rgba(211, 94, 94, 0.1);                                     /* 分割线：10%红色 */

  /*=============================================
   * 9. 视频详情弹窗样式(video-detail-modal)
   *=============================================*/
  
  /* 基础样式 */
  --videomodal-bg: rgba(255, 255, 255, 0.9);                                                  /* 详情页背景色，改为半透明 */
  --videomodal-header-bg:rgba(255, 255, 255, 0);                                                  /* 头部背景：灰色 */
  --videomodal-footer-bg:rgba(0, 0, 0, 0.2);                                     /* 底部控制条背景 */
  --videomodal-footer-height: 140rpx;                                               /* 底部控制栏高度 */
  --videomodal-decoration-bg: rgba(255, 255, 255, 0.18);                            /* 触摸动画背景色 */
  --videomodal-close-color:rgba(255, 255, 255, 0.08);                                                /* 关闭图标颜色 */
  --videomodal-text:rgb(0, 0, 0);                                                       /* 模态框文本 */
  --videomodal-detail-bg: rgba(220, 235, 245, 0.9);                                    /* 详情内容背景色：淡蓝色 */
  --videomodal-video-info-bg: rgba(220, 235, 245, 0.9);                                /* 标题框背景色：淡蓝色 */
  --videomodal-playcount-text:rgb(0, 0, 0);                                             /* 播放量文字颜色 */
  --videomodal-playcount-bg: rgba(255, 255, 255, 0.3);                                    /* 播放量容器背景色 */
  --videomodal-playcount-radius: 11rpx;                                                  /* 播放量容器圆角 */
  
  /* 文字样式 */
  --videomodal-video-title: rgb(33, 33, 33);                                                  /* 视频主标题：白色 */
  --videomodal-video-subtitle: rgb(102, 102, 102);                             /* 视频副标题：80%白色 */
  --videomodal-detail-text:rgb(102, 102, 102);                                                 /* 详情文本：白色 */
  --videomodal-button-text:rgb(185, 185, 185);                                                 /* 按钮文本：红色 */
  
  /* 加载动画 */
  --videomodal-loading-dot: rgba(194, 194, 194, 0.8);                                  /* 加载点：80%红色 */
  --videomodal-loading-text: rgba(185, 185, 185, 0.8);                                 /* 加载文本：80%红色 */
  
  /* 边框和阴影 */
  --videomodal-border: rgba(255, 255, 255, 0.2);                                       /* 边框：20%红色 */
  --videomodal-divider: rgba(211, 94, 94, 0);                                      /* 分割线：10%红色 */

  /* 按钮样式 */
  --videomodal-button-icon-size: 56rpx;                                             /* 按钮图标尺寸 */
  --videomodal-contact-icon-size: 72rpx;                                            /* 联系图标尺寸 - 从52rpx调整为72rpx与gallery-detail-modal保持一致 */
  --videomodal-decoration-size: 80rpx;                                             /* 装饰背景尺寸 */
  --videomodal-button-filter: brightness(0) invert(1);                              /* 按钮图标滤镜：白色 */
  
  /* 动画效果 */
  --videomodal-pulse-duration: 0.7s;                                                /* 脉冲动画持续时间 */
  --videomodal-pulse-timing: infinite ease-in;                                      /* 脉冲动画函数 */
  --videomodal-pulse-scale-min: 0.8;                                                /* 脉冲缩放最小值 */
  --videomodal-pulse-scale-max: 1;                                                  /* 脉冲缩放最大值 */



  /*=============================================
   * 10. 全局页面样式 - 不建议修改
   *=============================================*/
  
  /* 应用全局背景 */
  background: var(--bg-gradient);
  background-attachment: fixed;
  min-height: 100vh;
  color: var(--text-primary);
  position: fixed;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

/* 全局容器样式 */
.container {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
} 

/* 全局遮罩层 */
.mask {
  background-color: var(--bg-mask);
} 

/* 添加页面过渡动画样式 */
.page-container {
  width: 100%;
  min-height: 100vh;
  background-color: #ffffff;
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* 页面切换过渡遮罩 */
.page-transition-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ffffff;
  z-index: 9999;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.page-transition-mask.active {
  opacity: 1;
}

