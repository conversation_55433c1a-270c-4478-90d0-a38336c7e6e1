<!-- pages/my/points/points.wxml -->
<view class="points-container">
  <!-- 顶部标题栏 -->
  <view class="header">
    <view class="header-title">我的积分</view>
  </view>

  <!-- 标签栏 -->
  <view class="tabs">
    <view class="tab {{activeTab === 'points' ? 'active' : ''}}" bindtap="switchTab" data-tab="points">
      <text>我的积分</text>
    </view>
    <view class="tab {{activeTab === 'records' ? 'active' : ''}}" bindtap="switchTab" data-tab="records">
      <text>分享记录</text>
    </view>
    <view class="tab {{activeTab === 'withdrawals' ? 'active' : ''}}" bindtap="switchTab" data-tab="withdrawals">
      <text>提现记录</text>
    </view>
  </view>

  <!-- 内容区域 -->
  <view class="content-area">
    <!-- 积分页面 -->
    <view class="tab-content {{activeTab === 'points' ? 'active' : ''}}">
      <view class="loading-container" wx:if="{{loadingPoints}}">
        <view class="loading"></view>
        <text class="loading-text">加载中...</text>
      </view>
      <view class="points-info" wx:else>
        <view class="points-amount-container">
          <text class="points-label">当前积分</text>
          <view class="points-amount">
            <text class="points-value">{{points}}</text>
          </view>
        </view>

        <view class="points-detail">
          <view class="detail-item">
            <text class="detail-label">累计获得积分</text>
            <text class="detail-value">{{totalPoints}}</text>
          </view>
          <view class="detail-item" wx:if="{{frozenPoints > 0}}">
            <text class="detail-label">提现中积分</text>
            <text class="detail-value">{{frozenPoints}}</text>
          </view>
        </view>

        <view class="withdrawal-button-container">
          <button class="withdrawal-button" bindtap="showWithdrawalForm" disabled="{{points <= 0 || hasPendingWithdrawal}}">
            申请提现
          </button>
          <view class="points-status-container" wx:if="{{!hasPendingWithdrawal && points > 0}}">
            <text class="status-hint">当前积分可提现</text>
          </view>
        </view>

        <view class="points-status" wx:if="{{hasPendingWithdrawal}}">
          <text class="status-text pending">提现申请处理中</text>
          <text class="status-tip">您的提现申请正在处理中，请耐心等待</text>
        </view>

        <view class="points-rules">
          <view class="rules-title">积分规则</view>
          <view class="rules-content">
            <view class="rules-text">分享小程序内容给好友，当好友通过您的链接访问后即可获得积分奖励。不同内容分享均可获得积分，积分可随时申请提现。快去分享赚积分吧！</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 分享记录页面 -->
    <view class="tab-content {{activeTab === 'records' ? 'active' : ''}}">
      <view class="loading-container" wx:if="{{loadingRecords}}">
        <view class="loading"></view>
        <text class="loading-text">加载中...</text>
      </view>
      <view class="empty-container" wx:elif="{{shareRecords.length === 0}}">
        <text class="empty-text">分享已发出，等待好友访问后显示记录</text>
      </view>
      <view class="records-list" wx:else>
        <view class="record-item" wx:for="{{shareRecords}}" wx:key="_id">
          <view class="record-date">
            <text>{{item.dateStr}}</text>
            <text class="record-time">{{item.timeStr}}</text>
          </view>
          <view class="record-content">
            <view class="record-title">分享{{item.contentTypeText}}</view>
            <view class="record-details">
              <view class="record-info">
                <text class="record-label">获得积分</text>
                <text class="record-value">+{{item.points}}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 提现记录页面 -->
    <view class="tab-content {{activeTab === 'withdrawals' ? 'active' : ''}}">
      <view class="loading-container" wx:if="{{loadingWithdrawals}}">
        <view class="loading"></view>
        <text class="loading-text">加载中...</text>
      </view>
      <view class="empty-container" wx:elif="{{withdrawalRecords.length === 0}}">
        <text class="empty-text">暂无提现记录</text>
      </view>
      <view class="records-list" wx:else>
        <view class="record-item" wx:for="{{withdrawalRecords}}" wx:key="_id">
          <view class="record-date">
            <text>{{item.dateStr}}</text>
            <text class="record-time">{{item.timeStr}}</text>
          </view>
          <view class="record-content">
            <view class="record-title">积分提现</view>
            <view class="record-details">
              <view class="record-info">
                <text class="record-label">提现积分</text>
                <text class="record-value">{{item.points}}</text>
              </view>
              <view class="record-status {{item.statusClass}}">
                {{item.statusText}}
              </view>
            </view>
            <!-- 添加备注信息显示 -->
            <view class="record-remark" wx:if="{{item.remark}}">
              <text class="remark-text">{{item.remark}}</text>
            </view>
            <view class="record-qrcode" wx:if="{{item.paymentQrCode}}" bindtap="previewQrCode" data-qrcode="{{item.paymentQrCode}}">
              <text class="qrcode-label">查看收款码</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部返回按钮 -->
  <view class="back-button-container">
    <view class="back-button" bindtap="navigateBack">返回</view>
  </view>

  <!-- 提现表单弹窗 -->
  <view class="withdrawal-form-mask" wx:if="{{showWithdrawalForm}}" bindtap="hideWithdrawalForm">
    <view class="withdrawal-form" catchtap="preventBubble">
      <view class="form-header">
        <view class="header-spacer"></view>
        <text class="form-title">申请提现</text>
        <view class="form-close" bindtap="hideWithdrawalForm">×</view>
      </view>
      <view class="form-content">
        <view class="form-info">
          <view class="form-item">
            <text class="form-label">提现积分</text>
            <text class="form-value">{{points}}</text>
          </view>
          <view class="form-item">
            <text class="form-label">提现金额</text>
            <text class="form-value amount-value">¥{{points}}</text>
          </view>
        </view>
        
        <view class="form-qrcode-section">
          <view class="upload-container">
            <image class="qrcode-preview" wx:if="{{paymentQrCode}}" src="{{paymentQrCode}}" mode="aspectFit" bindtap="uploadQrCode"></image>
            <view class="upload-button" bindtap="uploadQrCode" wx:if="{{!paymentQrCode}}">
              <text>上传收款码</text>
            </view>
          </view>
        </view>
        
        <view class="form-textarea-container">
          <textarea class="form-textarea" placeholder="分享更多内容 获取积分奖励" bindinput="inputDescription" value="{{description}}"></textarea>
          <view class="optional-text">选填</view>
        </view>
      </view>
      <view class="form-footer">
        <button class="submit-button" bindtap="submitWithdrawal" disabled="{{submittingWithdrawal || !paymentQrCode}}">
          {{submittingWithdrawal ? '提交中...' : '提交申请'}}
        </button>
      </view>
    </view>
  </view>

  <!-- 收款码预览 -->
  <view class="qrcode-preview-mask" wx:if="{{showQrCodePreview}}" bindtap="closeQrCodePreview">
    <view class="qrcode-preview-container" catchtap="preventBubble">
      <image class="qrcode-preview-image" src="{{previewQrCode}}" mode="aspectFit"></image>
      <view class="qrcode-preview-close" bindtap="closeQrCodePreview">关闭</view>
    </view>
  </view>
</view> 