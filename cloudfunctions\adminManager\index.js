// 云函数入口文件
const cloud = require('wx-server-sdk')
const crypto = require('crypto')

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV })

// 云函数入口函数
exports.main = async (event, context) => {
  const { action } = event;
  const db = cloud.database();
  const _ = db.command;
  
  try {
    console.log('云函数接收到的action:', action);
    
    switch (action) {
      // 获取管理员信息
      case 'getAdminInfo':
        return await getAdminInfo(db);
      
      // 修改管理员密码
      case 'changePassword':
        return await changePassword(db, event.oldPassword, event.newPassword);
        
      // 初始化管理员账户
      case 'initAdmin':
        return await initAdmin(db, event.username, event.password);
        
      // 验证管理员登录
      case 'login':
        return await verifyLogin(db, event.username, event.password);
        
      default:
        return {
          code: -1,
          message: '未知操作'
        };
    }
  } catch (error) {
    console.error('云函数执行出错:', error);
    return {
      code: -1,
      message: '云函数执行出错: ' + (error.message || JSON.stringify(error)),
      error: error
    };
  }
};

// 确保admin_users集合存在
async function ensureAdminCollection(db) {
  try {
    // 尝试创建集合
    await db.createCollection('admin_users');
    console.log('admin_users集合创建成功');
  } catch (err) {
    // 如果集合已存在，会抛出错误，但这不影响后续操作
    console.log('创建admin_users集合时出错(可能已存在):', err);
  }
}

// 获取管理员信息
async function getAdminInfo(db) {
  try {
    console.log('开始获取管理员信息');
    
    // 确保集合存在
    try {
      await ensureAdminCollection(db);
    } catch (createErr) {
      console.log('确保管理员集合存在时出错(可能已存在):', createErr);
    }
    
    // 尝试获取管理员信息
    try {
      // 首先尝试获取指定ID的管理员信息
      const result = await db.collection('admin_users').doc('admin').get()
        .catch(async (err) => {
          console.log('获取admin文档失败，尝试创建默认管理员:', err);
          // 如果获取失败，可能是没有admin文档，尝试创建默认管理员
          return await createDefaultAdmin(db);
        });
      
      if (result && result.data) {
        console.log('获取管理员信息成功');
        
        // 返回安全的管理员信息（不包含密码）
        const { password, salt, ...safeAdminInfo } = result.data;
        
        return {
          code: 0,
          message: '获取管理员信息成功',
          data: safeAdminInfo
        };
      }
      
      // 如果没有指定ID的管理员，尝试获取任何管理员
      console.log('尝试获取任何管理员信息');
      const anyResult = await db.collection('admin_users').limit(1).get();
      
      if (anyResult && anyResult.data && anyResult.data.length > 0) {
        console.log('获取到管理员信息');
        
        // 返回安全的管理员信息（不包含密码）
        const { password, salt, ...safeAdminInfo } = anyResult.data[0];
        
        return {
          code: 0,
          message: '获取管理员信息成功',
          data: safeAdminInfo
        };
      }
      
      // 如果没有任何管理员，创建默认管理员
      console.log('未找到任何管理员信息，创建默认管理员');
      return await createDefaultAdmin(db);
    } catch (getErr) {
      console.error('获取管理员信息失败:', getErr);
      
      // 尝试创建默认管理员
      console.log('尝试创建默认管理员');
      return await createDefaultAdmin(db);
    }
  } catch (err) {
    console.error('获取管理员信息过程中发生错误:', err);
    return {
      code: -1,
      message: '获取管理员信息失败: ' + (err.message || JSON.stringify(err)),
      error: err
    };
  }
}

// 创建默认管理员账户
async function createDefaultAdmin(db) {
  try {
    console.log('开始创建默认管理员账户');
    
    // 生成盐值和默认密码的哈希
    const salt = crypto.randomBytes(16).toString('hex');
    const defaultPassword = 'admin123'; // 默认密码
    const hashedPassword = crypto.createHash('sha256')
      .update(defaultPassword + salt)
      .digest('hex');
    
    // 创建默认管理员数据
    const adminData = {
      _id: 'admin',
      username: 'admin',
      role: 'admin',
      password: hashedPassword,
      salt: salt,
      created_at: new Date(),
      last_updated: new Date()
    };
    
    // 尝试添加管理员文档
    try {
      await db.collection('admin_users').add({
        data: adminData
      });
      
      console.log('创建默认管理员账户成功');
      
      // 返回安全的管理员信息（不包含密码）
      const { password, salt, ...safeAdminInfo } = adminData;
      
      return {
        code: 0,
        message: '创建默认管理员账户成功，默认密码为: ' + defaultPassword,
        data: safeAdminInfo
      };
    } catch (addErr) {
      console.error('创建默认管理员账户失败:', addErr);
      return {
        code: -1,
        message: '创建默认管理员账户失败: ' + (addErr.message || JSON.stringify(addErr)),
        error: addErr
      };
    }
  } catch (err) {
    console.error('创建默认管理员账户过程中发生错误:', err);
    return {
      code: -1,
      message: '创建默认管理员账户失败: ' + (err.message || JSON.stringify(err)),
      error: err
    };
  }
}

// 初始化管理员账户
async function initAdmin(db, username, password) {
  try {
    console.log('开始初始化管理员账户');
    
    if (!username || !password) {
      username = 'admin';
      password = 'admin123';
    }
    
    // 确保集合存在
    await ensureAdminCollection(db);
    
    // 生成盐值和密码的哈希
    const salt = crypto.randomBytes(16).toString('hex');
    const hashedPassword = crypto.createHash('sha256')
      .update(password + salt)
      .digest('hex');
    
    // 创建管理员数据
    const adminData = {
      _id: 'admin',
      username: username,
      role: 'admin',
      password: hashedPassword,
      salt: salt,
      created_at: new Date(),
      last_updated: new Date()
    };
    
    // 检查是否已有管理员账户
    try {
      const existingAdmin = await db.collection('admin_users').doc('admin').get()
        .catch(() => null);
      
      if (existingAdmin && existingAdmin.data) {
        console.log('管理员账户已存在，更新账户');
        
        // 更新管理员账户
        await db.collection('admin_users').doc('admin').update({
          data: {
            username: username,
            password: hashedPassword,
            salt: salt,
            last_updated: new Date()
          }
        });
        
        console.log('更新管理员账户成功');
        
        return {
          code: 0,
          message: '更新管理员账户成功',
          username: username
        };
      }
    } catch (getErr) {
      console.log('检查管理员账户时出错:', getErr);
    }
    
    // 如果没有管理员账户，创建新账户
    try {
      await db.collection('admin_users').add({
        data: adminData
      });
      
      console.log('创建管理员账户成功');
      
      return {
        code: 0,
        message: '创建管理员账户成功',
        username: username
      };
    } catch (addErr) {
      console.error('创建管理员账户失败:', addErr);
      
      // 尝试使用set方法
      try {
        await db.collection('admin_users').doc('admin').set({
          data: adminData
        });
        
        console.log('使用set方法创建管理员账户成功');
        
        return {
          code: 0,
          message: '创建管理员账户成功(备用方法)',
          username: username
        };
      } catch (setErr) {
        console.error('使用set方法创建管理员账户也失败:', setErr);
        
        return {
          code: -1,
          message: '创建管理员账户失败: ' + (setErr.message || JSON.stringify(setErr)),
          error: setErr
        };
      }
    }
  } catch (err) {
    console.error('初始化管理员账户过程中发生错误:', err);
    return {
      code: -1,
      message: '初始化管理员账户失败: ' + (err.message || JSON.stringify(err)),
      error: err
    };
  }
}

// 修改管理员密码
async function changePassword(db, oldPassword, newPassword) {
  try {
    console.log('开始修改管理员密码');
    
    if (!oldPassword || !newPassword) {
      return {
        code: -1,
        message: '原密码和新密码不能为空'
      };
    }
    
    // 验证新密码格式
    if (newPassword.length < 6) {
      return {
        code: -1,
        message: '新密码长度至少为6位'
      };
    }
    
    const hasLetter = /[a-zA-Z]/.test(newPassword);
    const hasNumber = /[0-9]/.test(newPassword);
    
    if (!hasLetter || !hasNumber) {
      return {
        code: -1,
        message: '新密码必须包含字母和数字'
      };
    }
    
    // 确保集合存在
    await ensureAdminCollection(db);
    
    // 获取管理员信息
    let adminInfo;
    try {
      // 首先尝试获取指定ID的管理员信息
      const result = await db.collection('admin_users').doc('admin').get()
        .catch(async () => {
          // 如果获取失败，可能是没有admin文档，尝试创建默认管理员
          const createResult = await createDefaultAdmin(db);
          if (createResult.code === 0) {
            // 创建成功后重新获取
            return await db.collection('admin_users').doc('admin').get();
          }
          return null;
        });
      
      if (result && result.data) {
        adminInfo = result.data;
      } else {
        // 如果没有指定ID的管理员，尝试获取任何管理员
        const anyResult = await db.collection('admin_users').limit(1).get();
        
        if (anyResult && anyResult.data && anyResult.data.length > 0) {
          adminInfo = anyResult.data[0];
        } else {
          // 如果没有任何管理员，创建默认管理员
          const createResult = await createDefaultAdmin(db);
          if (createResult.code === 0) {
            return {
              code: -1,
              message: '已创建默认管理员账户，请使用默认密码 admin123 登录后再修改密码'
            };
          } else {
            return {
              code: -1,
              message: '未找到管理员账号且创建默认账号失败'
            };
          }
        }
      }
    } catch (getErr) {
      console.error('获取管理员信息失败:', getErr);
      
      // 尝试创建默认管理员
      const createResult = await createDefaultAdmin(db);
      if (createResult.code === 0) {
        return {
          code: -1,
          message: '已创建默认管理员账户，请使用默认密码 admin123 登录后再修改密码'
        };
      } else {
        return {
          code: -1,
          message: '获取管理员信息失败: ' + (getErr.message || JSON.stringify(getErr)),
          error: getErr
        };
      }
    }
    
    // 验证原密码
    const hashedOldPassword = crypto.createHash('sha256')
      .update(oldPassword + (adminInfo.salt || ''))
      .digest('hex');
    
    if (hashedOldPassword !== adminInfo.password) {
      return {
        code: -1,
        message: '原密码不正确'
      };
    }
    
    // 生成新的盐值和哈希密码
    const salt = crypto.randomBytes(16).toString('hex');
    const hashedNewPassword = crypto.createHash('sha256')
      .update(newPassword + salt)
      .digest('hex');
    
    // 更新管理员密码
    try {
      await db.collection('admin_users').doc(adminInfo._id).update({
        data: {
          password: hashedNewPassword,
          salt: salt,
          last_updated: new Date()
        }
      });
      
      console.log('更新管理员密码成功');
      
      return {
        code: 0,
        message: '密码修改成功'
      };
    } catch (updateErr) {
      console.error('更新管理员密码失败:', updateErr);
      return {
        code: -1,
        message: '更新管理员密码失败: ' + (updateErr.message || JSON.stringify(updateErr)),
        error: updateErr
      };
    }
  } catch (err) {
    console.error('修改管理员密码过程中发生错误:', err);
    return {
      code: -1,
      message: '修改管理员密码失败: ' + (err.message || JSON.stringify(err)),
      error: err
    };
  }
}

// 验证管理员登录
async function verifyLogin(db, username, password) {
  try {
    console.log('开始验证管理员登录');
    
    if (!username || !password) {
      return {
        code: -1,
        message: '用户名和密码不能为空'
      };
    }
    
    // 确保集合存在
    await ensureAdminCollection(db);
    
    // 获取管理员信息
    let adminInfo;
    try {
      // 首先尝试获取指定ID的管理员信息
      const result = await db.collection('admin_users').doc('admin').get()
        .catch(async () => {
          // 如果获取失败，可能是没有admin文档，尝试创建默认管理员
          const createResult = await createDefaultAdmin(db);
          if (createResult.code === 0) {
            // 创建成功后重新获取
            return await db.collection('admin_users').doc('admin').get();
          }
          return null;
        });
      
      if (result && result.data) {
        adminInfo = result.data;
      } else {
        // 如果没有指定ID的管理员，尝试获取任何管理员
        const anyResult = await db.collection('admin_users').limit(1).get();
        
        if (anyResult && anyResult.data && anyResult.data.length > 0) {
          adminInfo = anyResult.data[0];
        } else {
          // 如果没有任何管理员，创建默认管理员
          const createResult = await createDefaultAdmin(db);
          if (createResult.code === 0) {
            adminInfo = {
              _id: 'admin',
              username: 'admin',
              password: createResult.data.password,
              salt: createResult.data.salt,
              role: 'admin'
            };
          } else {
            return {
              code: -1,
              message: '未找到管理员账号且创建默认账号失败'
            };
          }
        }
      }
    } catch (getErr) {
      console.error('获取管理员信息失败:', getErr);
      return {
        code: -1,
        message: '获取管理员信息失败: ' + (getErr.message || JSON.stringify(getErr)),
        error: getErr
      };
    }
    
    // 验证用户名和密码
    if (username !== adminInfo.username) {
      return {
        code: -1,
        message: '用户名或密码错误'
      };
    }
    
    // 验证密码
    const hashedPassword = crypto.createHash('sha256')
      .update(password + (adminInfo.salt || ''))
      .digest('hex');
    
    if (hashedPassword !== adminInfo.password) {
      return {
        code: -1,
        message: '用户名或密码错误'
      };
    }
    
    // 登录成功，返回安全的管理员信息
    const { password: pwd, salt, ...safeAdminInfo } = adminInfo;
    
    return {
      code: 0,
      message: '',
      data: safeAdminInfo
    };
  } catch (err) {
    console.error('验证管理员登录过程中发生错误:', err);
    return {
      code: -1,
      message: '验证管理员登录失败: ' + (err.message || JSON.stringify(err)),
      error: err
    };
  }
} 