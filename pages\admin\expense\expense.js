const app = getApp();
const utils = require('../../../utils/util.js');

Page({
  data: {
    loading: true,
    expenseData: {
      totalCount: 0,
      totalExpense: 0,
      staffStats: [],
      expenseList: []
    },
    staffList: [{name: '全部'}],
    selectedStaff: null,
    selectedStaffName: '全部',
    
    // 日期筛选
    today: '',
    yesterday: '',
    currentWeekStart: '',
    currentWeekEnd: '',
    currentMonthStart: '',
    currentMonthEnd: '',
    startDate: '',
    endDate: '',
    
    // 分页
    currentPage: 1,
    totalPages: 1,
    pageSize: 20,
    
    // 支出详情
    showExpenseDetail: false,
    currentExpense: null
  },

  onLoad: function (options) {
    // 检查管理员登录状态
    if (!app.globalData.isAdmin) {
      this.redirectToLogin();
      return;
    }
    
    // 初始化日期
    this.initDates();
    
    // 获取员工列表
    this.getStaffList();
    
    // 获取支出数据
    this.queryData();
  },
  
  // 初始化日期
  initDates: function() {
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    
    // 计算本周开始和结束日期
    const currentWeekStart = new Date(today);
    const day = currentWeekStart.getDay() || 7; // 如果是周日，getDay() 返回 0，我们将其视为 7
    currentWeekStart.setDate(currentWeekStart.getDate() - day + 1); // 设置为本周一
    
    const currentWeekEnd = new Date(currentWeekStart);
    currentWeekEnd.setDate(currentWeekEnd.getDate() + 6); // 设置为本周日
    
    // 计算本月开始和结束日期
    const currentMonthStart = new Date(today.getFullYear(), today.getMonth(), 1);
    const currentMonthEnd = new Date(today.getFullYear(), today.getMonth() + 1, 0);
    
    this.setData({
      today: utils.formatDate(today),
      yesterday: utils.formatDate(yesterday),
      currentWeekStart: utils.formatDate(currentWeekStart),
      currentWeekEnd: utils.formatDate(currentWeekEnd),
      currentMonthStart: utils.formatDate(currentMonthStart),
      currentMonthEnd: utils.formatDate(currentMonthEnd),
      startDate: utils.formatDate(today),
      endDate: utils.formatDate(today)
    });
  },
  
  // 获取员工列表
  getStaffList: function() {
    wx.cloud.callFunction({
      name: 'staffManager',
      data: {
        type: 'getStaffList'
      }
    }).then(res => {
      if (res.result && res.result.success) {
        const staffList = [{name: '全部'}].concat(res.result.data || []);
        this.setData({
          staffList: staffList
        });
      }
    }).catch(err => {
      console.error('获取员工列表失败：', err);
      wx.showToast({
        title: '获取员工列表失败',
        icon: 'none'
      });
    });
  },
  
  // 查询支出数据
  queryData: function() {
    this.setData({
      loading: true,
      currentPage: 1
    });
    
    const { startDate, endDate, selectedStaff, currentPage, pageSize } = this.data;
    
    wx.cloud.callFunction({
      name: 'expenseManager',
      data: {
        type: 'getExpenses',
        data: {
          startDate,
          endDate,
          staffId: selectedStaff ? selectedStaff._id : '',
          page: currentPage,
          pageSize
        }
      }
    }).then(res => {
      if (res.result && res.result.success) {
        let expenseList = res.result.list || [];

        // 确保按时间排序（最新的在前面）
        expenseList.sort((a, b) => {
          const dateA = new Date(a.createTime);
          const dateB = new Date(b.createTime);
          return dateB - dateA; // 降序排列，最新的在前面
        });

        // 格式化日期和金额
        expenseList.forEach(item => {
          if (item.createTime) {
            item.createTime = utils.formatDateTime(new Date(item.createTime));
          }
          if (item.amount) {
            item.amount = parseFloat(item.amount).toFixed(2);
          }
        });
        
        // 计算总记录数和总支出
        const totalCount = res.result.total || 0;
        const totalExpense = expenseList.reduce((sum, item) => sum + parseFloat(item.amount || 0), 0).toFixed(2);
        
        // 按员工分组统计
        const staffStats = {};
        expenseList.forEach(expense => {
          if (!staffStats[expense.staffId]) {
            staffStats[expense.staffId] = {
              staffId: expense.staffId,
              staffName: expense.staffName,
              totalAmount: 0,
              count: 0
            };
          }
          staffStats[expense.staffId].totalAmount += parseFloat(expense.amount || 0);
          staffStats[expense.staffId].count += 1;
        });
        
        // 转换为数组并格式化金额
        const staffStatsList = Object.values(staffStats).map(item => {
          item.totalAmount = item.totalAmount.toFixed(2);
          return item;
        });
        
        this.setData({
          expenseData: {
            totalCount,
            totalExpense,
            staffStats: staffStatsList,
            expenseList
          },
          totalPages: Math.ceil(totalCount / pageSize) || 1,
          loading: false
        });
      } else {
        this.setData({
          loading: false
        });
        wx.showToast({
          title: '获取支出数据失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      console.error('获取支出数据失败：', err);
      this.setData({
        loading: false
      });
      wx.showToast({
        title: '获取支出数据失败',
        icon: 'none'
      });
    });
  },
  
  // 设置日期范围
  setDateRange: function(e) {
    const range = e.currentTarget.dataset.range;
    let startDate, endDate;
    
    switch (range) {
      case 'today':
        startDate = this.data.today;
        endDate = this.data.today;
        break;
      case 'yesterday':
        startDate = this.data.yesterday;
        endDate = this.data.yesterday;
        break;
      case 'week':
        startDate = this.data.currentWeekStart;
        endDate = this.data.currentWeekEnd;
        break;
      case 'month':
        startDate = this.data.currentMonthStart;
        endDate = this.data.currentMonthEnd;
        break;
    }
    
    this.setData({
      startDate,
      endDate
    });
    
    this.queryData();
  },
  
  // 开始日期变化
  onStartDateChange: function(e) {
    this.setData({
      startDate: e.detail.value
    });
  },
  
  // 结束日期变化
  onEndDateChange: function(e) {
    this.setData({
      endDate: e.detail.value
    });
  },
  
  // 员工筛选变化
  onStaffChange: function(e) {
    const index = e.detail.value;
    const staff = this.data.staffList[index];
    
    if (index == 0) {
      this.setData({
        selectedStaff: null,
        selectedStaffName: '全部'
      });
    } else {
      this.setData({
        selectedStaff: staff,
        selectedStaffName: staff.name
      });
    }
    
    this.queryData();
  },
  
  // 清除员工筛选
  clearStaffFilter: function() {
    this.setData({
      selectedStaff: null,
      selectedStaffName: '全部'
    });
    
    this.queryData();
  },
  
  // 上一页
  prevPage: function() {
    if (this.data.currentPage > 1) {
      this.setData({
        currentPage: this.data.currentPage - 1
      });
      this.queryData();
    }
  },
  
  // 下一页
  nextPage: function() {
    if (this.data.currentPage < this.data.totalPages) {
      this.setData({
        currentPage: this.data.currentPage + 1
      });
      this.queryData();
    }
  },
  
  // 查看支出详情
  viewExpenseDetail: function(e) {
    const expense = e.currentTarget.dataset.expense;
    this.setData({
      currentExpense: expense,
      showExpenseDetail: true
    });
  },
  
  // 关闭支出详情
  closeExpenseDetail: function() {
    this.setData({
      showExpenseDetail: false
    });
  },
  
  // 预览图片
  previewImage: function(e) {
    const urls = e.currentTarget.dataset.urls;
    const current = e.currentTarget.dataset.current;
    
    wx.previewImage({
      urls,
      current
    });
  },
  
  // 删除支出记录
  deleteExpense: function(e) {
    const id = e.currentTarget.dataset.id;
    
    wx.showModal({
      title: '提示',
      content: '确定要删除这条支出记录吗？',
      success: (res) => {
        if (res.confirm) {
          this.doDeleteExpense(id);
        }
      }
    });
  },
  
  // 删除当前查看的支出记录
  deleteCurrentExpense: function() {
    if (this.data.currentExpense) {
      wx.showModal({
        title: '提示',
        content: '确定要删除这条支出记录吗？',
        success: (res) => {
          if (res.confirm) {
            this.doDeleteExpense(this.data.currentExpense._id);
            this.closeExpenseDetail();
          }
        }
      });
    }
  },
  
  // 执行删除操作
  doDeleteExpense: function(id) {
    wx.cloud.callFunction({
      name: 'expenseManager',
      data: {
        type: 'deleteExpense',
        data: {
          expenseId: id
        }
      }
    }).then(res => {
      if (res.result && res.result.success) {
        wx.showToast({
          title: '删除成功',
          icon: 'success'
        });
        
        // 重新查询数据
        this.queryData();
      } else {
        wx.showToast({
          title: '删除失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      console.error('删除支出记录失败：', err);
      wx.showToast({
        title: '删除失败',
        icon: 'none'
      });
    });
  },
  
  // 返回上一页
  goBack: function() {
    wx.navigateBack();
  },
  
  // 重定向到登录页
  redirectToLogin: function() {
    wx.showToast({
      title: '请先登录',
      icon: 'none'
    });
    
    setTimeout(() => {
      wx.redirectTo({
        url: '/pages/admin/login'
      });
    }, 1500);
  }
}); 