const app = getApp()

Page({
  data: {
    rechargePlans: [],
    loading: true,
    showAddModal: false,
    showEditModal: false,
    currentPlan: null,
    formData: {
      title: '',
      description: '',
      amount: '',
      bonus: '',
      promotionCommission: '',
      image: '',
      isVisible: true
    },
    tempImageUrl: '',
    uploadLoading: false
  },

  onLoad() {
    this.fetchRechargePlans()
  },

  onPullDownRefresh() {
    this.fetchRechargePlans()
    wx.stopPullDownRefresh()
  },

  // 获取充值方案列表
  fetchRechargePlans() {
    this.setData({ loading: true })

    wx.cloud.callFunction({
      name: 'rechargeManager',
      data: {
        type: 'admin',
        action: 'getRechargePlans'
      }
    })
    .then(res => {
      console.log('获取充值方案成功:', res)
      if (res.result && res.result.code === 0) {
        this.setData({
          rechargePlans: res.result.data || [],
          loading: false
        })
      } else {
        this.setData({ loading: false })
        wx.showToast({
          title: res.result?.message || '获取充值方案失败',
          icon: 'none'
        })
      }
    })
    .catch(err => {
      console.error('获取充值方案失败:', err)
      this.setData({ loading: false })
      wx.showToast({
        title: '获取充值方案失败',
        icon: 'none'
      })
    })
  },

  // 打开添加方案弹窗
  showAddPlanModal() {
    this.setData({
      showAddModal: true,
      formData: {
        title: '',
        description: '',
        amount: '',
        bonus: '',
        promotionCommission: '',
        image: '',
        isVisible: true
      },
      tempImageUrl: ''
    })
  },

  // 关闭添加方案弹窗
  closeAddModal() {
    this.setData({
      showAddModal: false
    })
  },

  // 打开编辑方案弹窗
  showEditPlanModal(e) {
    const { plan } = e.currentTarget.dataset
    
    this.setData({
      showEditModal: true,
      currentPlan: plan,
      formData: {
        title: plan.title || '',
        description: plan.description || '',
        amount: plan.amount || '',
        bonus: plan.bonus || '',
        promotionCommission: plan.promotionCommission || '',
        image: plan.image || '',
        isVisible: plan.isVisible !== false
      },
      tempImageUrl: plan.image || ''
    })
  },

  // 关闭编辑方案弹窗
  closeEditModal() {
    this.setData({
      showEditModal: false
    })
  },

  // 表单输入处理
  onInput(e) {
    const { field } = e.currentTarget.dataset
    const { value } = e.detail
    
    this.setData({
      [`formData.${field}`]: value
    })
  },

  // 表单开关处理
  onSwitchChange(e) {
    const { field } = e.currentTarget.dataset
    const { value } = e.detail
    
    this.setData({
      [`formData.${field}`]: value
    })
  },

  // 上传图片
  uploadImage() {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: res => {
        const tempFilePath = res.tempFilePaths[0]
        this.setData({
          tempImageUrl: tempFilePath,
          uploadLoading: true
        })
        
        const fileName = `recharge_plans/${Date.now()}_${Math.floor(Math.random() * 1000)}.jpg`
        
        wx.cloud.uploadFile({
          cloudPath: fileName,
          filePath: tempFilePath,
          success: res => {
            const fileID = res.fileID
            this.setData({
              'formData.image': fileID,
              uploadLoading: false
            })
            
            wx.showToast({
              title: '图片上传成功',
              icon: 'success'
            })
          },
          fail: err => {
            console.error('图片上传失败:', err)
            this.setData({
              uploadLoading: false
            })
            
            wx.showToast({
              title: '图片上传失败',
              icon: 'none'
            })
          }
        })
      }
    })
  },

  // 添加充值方案
  addRechargePlan() {
    const { formData } = this.data
    
    // 表单验证
    if (!formData.title) {
      wx.showToast({
        title: '请输入标题',
        icon: 'none'
      })
      return
    }
    
    if (!formData.amount || isNaN(Number(formData.amount))) {
      wx.showToast({
        title: '请输入有效的充值金额',
        icon: 'none'
      })
      return
    }
    
    if (!formData.bonus || isNaN(Number(formData.bonus))) {
      wx.showToast({
        title: '请输入有效的赠送金额',
        icon: 'none'
      })
      return
    }
    
    if (!formData.promotionCommission || isNaN(Number(formData.promotionCommission))) {
      wx.showToast({
        title: '请输入有效的推广佣金',
        icon: 'none'
      })
      return
    }
    
    // 转换数据类型
    const planData = {
      ...formData,
      amount: Number(formData.amount),
      bonus: Number(formData.bonus),
      promotionCommission: Number(formData.promotionCommission),
      totalAmount: Number(formData.amount) + Number(formData.bonus)
    }
    
    wx.showLoading({
      title: '添加中...',
      mask: true
    })
    
    wx.cloud.callFunction({
      name: 'rechargeManager',
      data: {
        type: 'admin',
        action: 'addRechargePlan',
        data: planData
      }
    })
    .then(res => {
      wx.hideLoading()
      
      if (res.result && res.result.code === 0) {
        wx.showToast({
          title: '添加成功',
          icon: 'success'
        })
        
        this.setData({
          showAddModal: false
        })
        
        // 重新获取方案列表
        this.fetchRechargePlans()
      } else {
        wx.showToast({
          title: res.result?.message || '添加失败',
          icon: 'none'
        })
      }
    })
    .catch(err => {
      wx.hideLoading()
      console.error('添加充值方案失败:', err)
      
      wx.showToast({
        title: '添加失败',
        icon: 'none'
      })
    })
  },

  // 更新充值方案
  updateRechargePlan() {
    const { formData, currentPlan } = this.data
    
    // 表单验证
    if (!formData.title) {
      wx.showToast({
        title: '请输入标题',
        icon: 'none'
      })
      return
    }
    
    if (!formData.amount || isNaN(Number(formData.amount))) {
      wx.showToast({
        title: '请输入有效的充值金额',
        icon: 'none'
      })
      return
    }
    
    if (!formData.bonus || isNaN(Number(formData.bonus))) {
      wx.showToast({
        title: '请输入有效的赠送金额',
        icon: 'none'
      })
      return
    }
    
    if (!formData.promotionCommission || isNaN(Number(formData.promotionCommission))) {
      wx.showToast({
        title: '请输入有效的推广佣金',
        icon: 'none'
      })
      return
    }
    
    // 转换数据类型
    const planData = {
      ...formData,
      amount: Number(formData.amount),
      bonus: Number(formData.bonus),
      promotionCommission: Number(formData.promotionCommission),
      totalAmount: Number(formData.amount) + Number(formData.bonus),
      id: currentPlan._id
    }
    
    wx.showLoading({
      title: '更新中...',
      mask: true
    })
    
    wx.cloud.callFunction({
      name: 'rechargeManager',
      data: {
        type: 'admin',
        action: 'updateRechargePlan',
        data: planData
      }
    })
    .then(res => {
      wx.hideLoading()
      
      if (res.result && res.result.code === 0) {
        wx.showToast({
          title: '更新成功',
          icon: 'success'
        })
        
        this.setData({
          showEditModal: false
        })
        
        // 重新获取方案列表
        this.fetchRechargePlans()
      } else {
        wx.showToast({
          title: res.result?.message || '更新失败',
          icon: 'none'
        })
      }
    })
    .catch(err => {
      wx.hideLoading()
      console.error('更新充值方案失败:', err)
      
      wx.showToast({
        title: '更新失败',
        icon: 'none'
      })
    })
  },

  // 删除充值方案
  deletePlan(e) {
    const { id } = e.currentTarget.dataset
    
    wx.showModal({
      title: '确认删除',
      content: '确定要删除此充值方案吗？',
      success: res => {
        if (res.confirm) {
          wx.showLoading({
            title: '删除中...',
            mask: true
          })
          
          wx.cloud.callFunction({
            name: 'rechargeManager',
            data: {
              type: 'admin',
              action: 'deleteRechargePlan',
              data: { id }
            }
          })
          .then(res => {
            wx.hideLoading()
            
            if (res.result && res.result.code === 0) {
              wx.showToast({
                title: '删除成功',
                icon: 'success'
              })
              
              // 重新获取方案列表
              this.fetchRechargePlans()
            } else {
              wx.showToast({
                title: res.result?.message || '删除失败',
                icon: 'none'
              })
            }
          })
          .catch(err => {
            wx.hideLoading()
            console.error('删除充值方案失败:', err)
            
            wx.showToast({
              title: '删除失败',
              icon: 'none'
            })
          })
        }
      }
    })
  },

  // 切换方案可见性
  toggleVisibility(e) {
    const { id, visible } = e.currentTarget.dataset
    const newVisibility = !visible
    
    wx.showLoading({
      title: newVisibility ? '显示中...' : '隐藏中...',
      mask: true
    })
    
    wx.cloud.callFunction({
      name: 'rechargeManager',
      data: {
        type: 'admin',
        action: 'updateRechargePlan',
        data: {
          id,
          isVisible: newVisibility
        }
      }
    })
    .then(res => {
      wx.hideLoading()
      
      if (res.result && res.result.code === 0) {
        wx.showToast({
          title: newVisibility ? '已显示' : '已隐藏',
          icon: 'success'
        })
        
        // 重新获取方案列表
        this.fetchRechargePlans()
      } else {
        wx.showToast({
          title: res.result?.message || '操作失败',
          icon: 'none'
        })
      }
    })
    .catch(err => {
      wx.hideLoading()
      console.error('更新充值方案可见性失败:', err)
      
      wx.showToast({
        title: '操作失败',
        icon: 'none'
      })
    })
  },

  // 返回上一页
  navigateBack() {
    wx.navigateBack()
  }
}) 