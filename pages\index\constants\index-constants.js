/**
 * 主页面常量定义
 * 从主页面提取的魔法数字、配置项、默认值等常量
 */

// ==================== 页面配置常量 ====================

/**
 * 分页配置
 */
const PAGINATION = {
  // 默认页码（从0开始）
  DEFAULT_PAGE: 0,
  // 每页数量
  PAGE_SIZE: 4,
  // 最大页数限制
  MAX_PAGES: 100
};

/**
 * 加载状态配置
 */
const LOADING_CONFIG = {
  // 首次加载超时时间（毫秒）
  FIRST_LOAD_TIMEOUT: 10000,
  // 普通加载超时时间（毫秒）
  NORMAL_LOAD_TIMEOUT: 5000,
  // 刷新超时时间（毫秒）
  REFRESH_TIMEOUT: 8000,
  // 加载重试次数
  MAX_RETRY_COUNT: 3,
  // 重试间隔时间（毫秒）
  RETRY_INTERVAL: 1000
};

/**
 * 缓存配置
 */
const CACHE_CONFIG = {
  // 视频列表缓存键名
  VIDEO_LIST_CACHE_KEY: 'video_list_cache',
  // 视频URL缓存键名前缀
  VIDEO_URL_CACHE_PREFIX: 'video_url_',
  // 缓存过期时间（毫秒）
  CACHE_EXPIRE_TIME: 30 * 60 * 1000, // 30分钟
  // 最大缓存数量
  MAX_CACHE_COUNT: 100,
  // URL缓存过期时间（毫秒）
  URL_CACHE_EXPIRE_TIME: 60 * 60 * 1000 // 1小时
};

// ==================== UI交互常量 ====================

/**
 * 滚动配置
 */
const SCROLL_CONFIG = {
  // 导航栏隐藏的滚动阈值
  NAVBAR_HIDE_THRESHOLD: 100,
  // 滚动节流延迟（毫秒）
  SCROLL_THROTTLE_DELAY: 16,
  // 滚动到顶部的动画时长（毫秒）
  SCROLL_TO_TOP_DURATION: 300,
  // 滚动结束检测延迟（毫秒）
  SCROLL_END_DELAY: 150,
  // UI更新阈值（像素）
  UI_UPDATE_THRESHOLD: 10
};

/**
 * 搜索配置
 */
const SEARCH_CONFIG = {
  // 搜索防抖延迟（毫秒）
  SEARCH_DEBOUNCE_DELAY: 300,
  // 最小搜索关键词长度
  MIN_SEARCH_LENGTH: 1,
  // 最大搜索关键词长度
  MAX_SEARCH_LENGTH: 50,
  // 搜索结果最大数量
  MAX_SEARCH_RESULTS: 100,
  // 搜索历史最大保存数量
  MAX_SEARCH_HISTORY: 10
};

/**
 * 视频播放配置
 */
const VIDEO_CONFIG = {
  // 视频播放超时时间（毫秒）
  PLAY_TIMEOUT: 10000,
  // 视频URL获取超时时间（毫秒）
  URL_FETCH_TIMEOUT: 5000,
  // 同时播放的最大视频数量
  MAX_CONCURRENT_VIDEOS: 1,
  // 视频预加载数量
  PRELOAD_COUNT: 2,
  // 视频封面默认图片
  DEFAULT_COVER_URL: '/static/logo.png',
  // 默认作者头像
  DEFAULT_AUTHOR_AVATAR: '/static/logo.png'
};

/**
 * 动画配置
 */
const ANIMATION_CONFIG = {
  // 页面过渡动画时长（毫秒）
  PAGE_TRANSITION_DURATION: 300,
  // 内容显示动画时长（毫秒）
  CONTENT_SHOW_DURATION: 200,
  // 加载动画时长（毫秒）
  LOADING_ANIMATION_DURATION: 1000,
  // 骨架屏动画时长（毫秒）
  SKELETON_ANIMATION_DURATION: 1500,
  // 默认缓动函数
  DEFAULT_EASING: 'ease-out'
};

/**
 * 性能配置
 */
const PERFORMANCE_CONFIG = {
  // 批量更新延迟（毫秒）
  BATCH_UPDATE_DELAY: 16,
  // 性能指标收集间隔（毫秒）
  METRICS_COLLECTION_INTERVAL: 5000,
  // 内存检查间隔（毫秒）
  MEMORY_CHECK_INTERVAL: 10000,
  // 高速滚动阈值（像素/毫秒）
  HIGH_VELOCITY_THRESHOLD: 2,
  // 数据过期时间（毫秒）
  DATA_EXPIRE_TIME: 60000,
  // 帧丢失阈值（毫秒）
  FRAME_DROP_THRESHOLD: 16,
  // 滚动时间警告阈值（毫秒）
  SCROLL_TIME_WARNING_THRESHOLD: 20,
  // 帧丢失率阈值
  FRAME_DROP_RATE_THRESHOLD: 0.1,
  // 内存警告阈值（MB）
  MEMORY_WARNING_THRESHOLD: 50,
  // 内存清理阈值（MB）
  MEMORY_CLEANUP_THRESHOLD: 80
};

// ==================== 状态常量 ====================

/**
 * 页面状态
 */
const PAGE_STATES = {
  // 初始状态
  INITIAL: 'initial',
  // 加载中
  LOADING: 'loading',
  // 加载完成
  LOADED: 'loaded',
  // 加载失败
  ERROR: 'error',
  // 刷新中
  REFRESHING: 'refreshing',
  // 搜索中
  SEARCHING: 'searching'
};

/**
 * 视频状态
 */
const VIDEO_STATES = {
  // 未开始播放
  IDLE: 'idle',
  // 正在播放
  PLAYING: 'playing',
  // 已暂停
  PAUSED: 'paused',
  // 播放结束
  ENDED: 'ended',
  // 播放错误
  ERROR: 'error',
  // 加载中
  LOADING: 'loading'
};

/**
 * 网络状态
 */
const NETWORK_STATES = {
  // 在线
  ONLINE: 'online',
  // 离线
  OFFLINE: 'offline',
  // 弱网
  WEAK: 'weak',
  // 未知
  UNKNOWN: 'unknown'
};

// ==================== 错误类型常量 ====================

/**
 * 错误类型
 */
const ERROR_TYPES = {
  // 网络错误
  NETWORK_ERROR: 'NETWORK_ERROR',
  // 数据错误
  DATA_ERROR: 'DATA_ERROR',
  // UI错误
  UI_ERROR: 'UI_ERROR',
  // 模块错误
  MODULE_ERROR: 'MODULE_ERROR',
  // 通用错误
  GENERIC_ERROR: 'GENERIC_ERROR',
  // 视频播放错误
  VIDEO_PLAY_ERROR: 'VIDEO_PLAY_ERROR',
  // 搜索错误
  SEARCH_ERROR: 'SEARCH_ERROR'
};

/**
 * 错误消息
 */
const ERROR_MESSAGES = {
  [ERROR_TYPES.NETWORK_ERROR]: '网络连接异常，请重试',
  [ERROR_TYPES.DATA_ERROR]: '数据加载失败',
  [ERROR_TYPES.UI_ERROR]: '界面操作异常',
  [ERROR_TYPES.MODULE_ERROR]: '模块加载失败',
  [ERROR_TYPES.GENERIC_ERROR]: '操作失败，请重试',
  [ERROR_TYPES.VIDEO_PLAY_ERROR]: '视频播放失败',
  [ERROR_TYPES.SEARCH_ERROR]: '搜索功能异常'
};

// ==================== 事件名称常量 ====================

/**
 * 页面事件
 */
const PAGE_EVENTS = {
  // 页面加载完成
  PAGE_LOADED: 'page:loaded',
  // 页面显示
  PAGE_SHOW: 'page:show',
  // 页面隐藏
  PAGE_HIDE: 'page:hide',
  // 页面卸载
  PAGE_UNLOAD: 'page:unload',
  // 数据更新
  DATA_UPDATE: 'data:update',
  // 状态变更
  STATE_CHANGE: 'state:change'
};

/**
 * 视频事件
 */
const VIDEO_EVENTS = {
  // 视频播放
  VIDEO_PLAY: 'video:play',
  // 视频暂停
  VIDEO_PAUSE: 'video:pause',
  // 视频结束
  VIDEO_END: 'video:end',
  // 视频错误
  VIDEO_ERROR: 'video:error',
  // 视频全屏变化
  VIDEO_FULLSCREEN_CHANGE: 'video:fullscreen:change',
  // 视频列表更新
  VIDEO_LIST_UPDATE: 'video:list:update',
  // 视频详情打开
  VIDEO_DETAIL_OPEN: 'video:detail:open',
  // 视频详情关闭
  VIDEO_DETAIL_CLOSE: 'video:detail:close'
};

/**
 * 搜索事件
 */
const SEARCH_EVENTS = {
  // 搜索开始
  SEARCH_START: 'search:start',
  // 搜索完成
  SEARCH_COMPLETE: 'search:complete',
  // 搜索清除
  SEARCH_CLEAR: 'search:clear',
  // 搜索焦点变化
  SEARCH_FOCUS_CHANGE: 'search:focus:change'
};

/**
 * 模块事件
 */
const MODULE_EVENTS = {
  // 模块注册
  MODULE_REGISTERED: 'module:registered',
  // 模块注销
  MODULE_UNREGISTERED: 'module:unregistered',
  // 模块错误
  MODULE_ERROR: 'module:error',
  // 模块初始化完成
  MODULE_INITIALIZED: 'module:initialized'
};

// ==================== 默认值常量 ====================

/**
 * 默认页面数据
 */
const DEFAULT_PAGE_DATA = {
  userInfo: null,
  videoList: [],
  loading: false,
  firstLoading: true,
  hasMore: true,
  page: PAGINATION.DEFAULT_PAGE,
  pageSize: PAGINATION.PAGE_SIZE,
  urlCache: {},
  urlFetchingIds: [],
  lastRefreshTime: 0,
  isFromLaunch: false,
  showContent: false,
  forceHideNavbar: false,
  isRefreshing: false,
  navigationHeight: 0,
  searchKeyword: '',
  isSearching: false,
  searchFocused: false,
  currentPlayingVideo: null,
  originalVideoList: null,
  startX: null,
  startY: null
};

/**
 * 默认视频数据
 */
const DEFAULT_VIDEO_DATA = {
  id: '',
  baseId: '',
  mainTitle: '',
  subTitle: '',
  coverUrl: VIDEO_CONFIG.DEFAULT_COVER_URL,
  videoUrl: '',
  playCount: '0',
  author: '',
  authorAvatar: VIDEO_CONFIG.DEFAULT_AUTHOR_AVATAR,
  isPlaying: false,
  description: '',
  urlError: false
};

// ==================== 正则表达式常量 ====================

/**
 * 验证正则表达式
 */
const REGEX_PATTERNS = {
  // 视频ID格式验证
  VIDEO_ID: /^[a-zA-Z0-9_-]+$/,
  // URL格式验证
  URL: /^https?:\/\/.+/,
  // 搜索关键词验证（允许中文、英文、数字、空格）
  SEARCH_KEYWORD: /^[\u4e00-\u9fa5a-zA-Z0-9\s]+$/,
  // 播放量数字验证
  PLAY_COUNT: /^\d+$/
};

// ==================== 样式类名常量 ====================

/**
 * CSS类名
 */
const CSS_CLASSES = {
  // 容器类
  CONTAINER: 'container',
  MAIN_SCROLL: 'main-scroll',
  CONTENT_WRAPPER: 'content-wrapper',
  
  // 状态类
  LOADING: 'loading',
  ERROR: 'error',
  HIDDEN: 'hidden',
  VISIBLE: 'visible',
  ACTIVE: 'active',
  
  // 视频相关类
  VIDEO_ITEM: 'video-item',
  VIDEO_COVER: 'video-cover',
  VIDEO_PLAYING: 'video-playing',
  
  // 搜索相关类
  SEARCH_CONTAINER: 'search-container',
  SEARCH_INPUT: 'search-input',
  SEARCH_RESULTS: 'search-results',
  
  // 导航相关类
  NAVBAR: 'navbar',
  NAVBAR_HIDDEN: 'navbar-hidden'
};

// ==================== 导出所有常量 ====================

module.exports = {
  // 页面配置
  PAGINATION,
  LOADING_CONFIG,
  CACHE_CONFIG,
  
  // UI交互
  SCROLL_CONFIG,
  SEARCH_CONFIG,
  VIDEO_CONFIG,
  ANIMATION_CONFIG,
  PERFORMANCE_CONFIG,
  
  // 状态
  PAGE_STATES,
  VIDEO_STATES,
  NETWORK_STATES,
  
  // 错误
  ERROR_TYPES,
  ERROR_MESSAGES,
  
  // 事件
  PAGE_EVENTS,
  VIDEO_EVENTS,
  SEARCH_EVENTS,
  MODULE_EVENTS,
  
  // 默认值
  DEFAULT_PAGE_DATA,
  DEFAULT_VIDEO_DATA,
  
  // 验证
  REGEX_PATTERNS,
  
  // 样式
  CSS_CLASSES
};