/* pages/admin/expense/expense.wxss */

/* 应用样式规范的颜色 */
/* 主色调：深灰色 #4a4a4a */
/* 强调色/主按钮：类苹果蓝 #0070c9 */
/* 成功色：绿色 #34c759 */
/* 危险色/警告色：红色 #ff3b30 */
/* 浅色背景：浅灰色 #f5f5f7 */
/* 文字主色：#333333 */
/* 次要文字色：#666666 */
/* 浅色文字：#999999 */

/* 容器样式 */
.expense-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100vh;
  background-color: #f5f5f7; /* 更新为规范的浅色背景 */
  position: relative;
  box-sizing: border-box;
}

/* 顶部标题栏 */
.header {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  padding-top: 90rpx; /* 确保在胶囊按钮下方 */
  background-color: #ffffff;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.back-icon {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  font-weight: bold;
  color: #4a4a4a; /* 更新为规范的主色调 */
}

.title {
  flex: 1;
  text-align: center;
  font-size: 34rpx;
  font-weight: bold;
  margin-right: 60rpx;
  color: #333333; /* 更新为规范的文字主色 */
}

/* 内容区域容器 */
.content-container {
  flex: 1;
  overflow-y: auto;
  padding: 0 20rpx; /* 减小左右内边距 */
  margin-top: 150rpx; /* 为顶部固定栏留出空间 */
  padding-bottom: 40rpx; /* 底部留出一些空间 */
  -webkit-overflow-scrolling: touch; /* iOS滚动优化 */
}

/* 日期筛选区域 */
.date-filter {
  background-color: #fff;
  padding: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  border-radius: 6rpx; /* 统一卡片圆角 */
}

.date-shortcuts {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.shortcut-btn {
  flex: 1;
  text-align: center;
  padding: 15rpx 0;
  margin: 0 10rpx;
  background-color: #f5f5f7; /* 更新为规范的浅色背景 */
  color: #666666; /* 更新为规范的次要文字色 */
  border-radius: 4rpx; /* 统一按钮圆角 */
  font-size: 28rpx;
  transition: all 0.2s ease;
}

.shortcut-btn.active {
  background-color: rgba(0, 112, 201, 0.1); /* 更新为规范的强调色背景 */
  color: #0070c9; /* 更新为规范的强调色 */
  font-weight: 500;
}

.date-range-picker {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  padding: 10rpx 0;
  border-top: 1rpx solid #e0e0e0; /* 更新为规范的边框颜色 */
}

.date-picker-item {
  display: flex;
  align-items: center;
  margin-right: 20rpx;
  margin-bottom: 10rpx;
}

.date-label {
  font-size: 28rpx;
  color: #666666; /* 更新为规范的次要文字色 */
  margin-right: 10rpx;
}

.date-value {
  background-color: #f9f9f9; /* 轻微的背景色 */
  padding: 10rpx 20rpx;
  border-radius: 4rpx; /* 统一按钮圆角 */
  font-size: 28rpx;
  color: #333333; /* 更新为规范的文字主色 */
  min-width: 180rpx;
}

.query-btn {
  padding: 10rpx 30rpx;
  background-color: #0070c9; /* 更新为规范的强调色 */
  color: #fff;
  border-radius: 4rpx; /* 统一按钮圆角 */
  font-size: 28rpx;
  margin-left: auto;
  box-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.1); /* 添加按钮阴影 */
  transition: all 0.2s ease;
}

.query-btn:active {
  opacity: 0.9;
  transform: translateY(1rpx);
}

/* 员工筛选 */
.staff-filter {
  display: flex;
  align-items: center;
  margin-top: 20rpx;
  padding-top: 20rpx;
  border-top: 1rpx solid #e0e0e0; /* 更新为规范的边框颜色 */
}

.filter-label {
  font-size: 28rpx;
  color: #666666; /* 更新为规范的次要文字色 */
  margin-right: 10rpx;
}

.filter-picker {
  background-color: #f9f9f9; /* 轻微的背景色 */
  padding: 10rpx 20rpx;
  border-radius: 4rpx; /* 统一按钮圆角 */
  font-size: 28rpx;
  color: #333333; /* 更新为规范的文字主色 */
  display: flex;
  align-items: center;
}

.picker-arrow {
  font-size: 20rpx;
  margin-left: 10rpx;
  color: #999999; /* 更新为规范的浅色文字 */
}

.clear-filter {
  width: 50rpx;
  height: 50rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999999; /* 更新为规范的浅色文字 */
  font-size: 32rpx;
}

/* 加载中 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #0070c9; /* 更新为规范的强调色 */
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 24rpx; /* 减小字体大小 */
  color: #999999; /* 更新为规范的浅色文字 */
}

/* 统计卡片 */
.stats-card {
  background-color: #fff;
  border-radius: 6rpx; /* 统一卡片圆角 */
  padding: 25rpx 30rpx; /* 调整内边距 */
  margin: 0 0 20rpx; /* 减小外边距 */
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.stats-card-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333; /* 更新为规范的文字主色 */
  margin-bottom: 20rpx;
  border-left: 6rpx solid #ff3b30; /* 更新为规范的危险色，保持支出相关的红色 */
  padding-left: 20rpx;
}

/* 基础统计数据 */
.stats-basic {
  display: flex;
  justify-content: space-around;
  margin-bottom: 20rpx;
}

.stats-basic-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20rpx;
  padding: 12rpx 5rpx; /* 添加内边距 */
  border-radius: 4rpx; /* 统一的小圆角 */
  transition: all 0.3s ease; /* 添加过渡效果 */
  box-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.03); /* 轻微的阴影 */
  background-color: #fafafa; /* 轻微的背景色 */
  width: 45%; /* 宽度适当调整 */
}

.stats-basic-value {
  font-size: 32rpx; /* 减小字体大小 */
  color: #ff3b30; /* 支出相关元素使用红色 */
  font-weight: bold;
  margin-bottom: 6rpx; /* 减小底部间距 */
}

.stats-basic-label {
  font-size: 22rpx; /* 减小标签字体大小 */
  color: #999999; /* 更新为规范的浅色文字 */
}

/* 员工支出统计 */
.staff-stats {
  width: 100%;
}

.staff-stats-header, .staff-stats-item {
  display: flex;
  padding: 15rpx 0;
  border-bottom: 1rpx solid #e0e0e0; /* 更新为规范的边框颜色 */
  font-size: 24rpx; /* 减小字体大小，与日期统计保持一致 */
  align-items: center; /* 垂直居中 */
}

.staff-stats-header {
  font-weight: bold;
  color: #666666; /* 更新为规范的次要文字色 */
  background-color: #f9f9f9; /* 添加轻微背景色 */
  padding: 12rpx 8rpx; /* 与日期统计保持一致 */
  border-radius: 4rpx; /* 统一的小圆角 */
  margin-bottom: 8rpx;
}

.staff-name-header, .staff-name {
  flex: 2;
  padding: 0 8rpx;
}

.staff-name {
  color: #333333; /* 更新为规范的文字主色 */
}

.staff-count-header, .staff-count {
  flex: 1;
  text-align: center;
  padding: 0 8rpx;
}

.staff-count {
  color: #666666; /* 更新为规范的次要文字色 */
}

.staff-amount-header, .staff-amount {
  flex: 1;
  text-align: center;
  padding: 0 8rpx;
}

.staff-amount {
  color: #ff3b30; /* 支出相关元素使用红色 */
  font-weight: 500;
}

/* 支出记录列表 */
.expense-list {
  width: 100%;
}

.expense-item {
  background-color: #fff;
  border-radius: 6rpx; /* 统一卡片圆角 */
  padding: 20rpx 25rpx; /* 增加内边距 */
  margin-bottom: 15rpx; /* 增加列表项间距 */
  position: relative;
  transition: all 0.2s ease;
  box-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.03); /* 添加轻微阴影 */
  border-left: 4rpx solid #ff3b30; /* 左侧添加红色边框，增强视觉层次 */
}

.expense-item:hover {
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.08);
  transform: translateY(-2rpx);
}

.expense-header {
  display: flex;
  justify-content: space-between;
  align-items: center; /* 垂直居中对齐 */
  margin-bottom: 15rpx;
  border-bottom: 1rpx solid #f0f0f0; /* 添加底部分隔线 */
  padding-bottom: 12rpx; /* 底部内边距 */
}

.expense-staff {
  color: #333333; /* 更新为规范的文字主色 */
  font-weight: bold;
  font-size: 28rpx; /* 增大字体大小 */
  background-color: rgba(0, 112, 201, 0.1); /* 添加背景色增强可读性 */
  padding: 4rpx 12rpx; /* 添加内边距 */
  border-radius: 4rpx; /* 统一按钮圆角 */
}

.expense-time {
  color: #999999; /* 更新为规范的浅色文字 */
  font-size: 24rpx;
}

.expense-body {
  display: flex;
  align-items: center;
  justify-content: space-between; /* 使元素分散对齐 */
  margin-bottom: 15rpx;
}

.expense-left {
  display: flex;
  align-items: center;
}

.expense-amount {
  font-size: 36rpx; /* 增大字体大小 */
  color: #ff3b30; /* 支出相关元素使用红色 */
  font-weight: bold;
  margin-right: 20rpx;
  min-width: 120rpx; /* 确保有最小宽度 */
}

.expense-remark {
  max-width: 200rpx; /* 限制宽度，防止过长 */
  font-size: 26rpx; /* 减小字体大小 */
  color: #666666; /* 更新为规范的次要文字色 */
  line-height: 1.5; /* 增加行高提高可读性 */
  background-color: #f9f9f9; /* 添加背景色 */
  padding: 8rpx 12rpx; /* 添加内边距 */
  border-radius: 4rpx; /* 统一按钮圆角 */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.expense-time-tag {
  font-size: 24rpx;
  color: #999999; /* 更新为规范的浅色文字 */
  background-color: rgba(0, 112, 201, 0.05); /* 使用与员工名称相似但更浅的背景 */
  padding: 4rpx 12rpx;
  border-radius: 4rpx;
  text-align: right;
  min-width: 80rpx;
}

.expense-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin-top: 15rpx;
}

.expense-images {
  display: flex;
  align-items: center;
  flex: 1;
}

.expense-image-thumb {
  width: 80rpx; /* 减小图片尺寸 */
  height: 80rpx;
  border-radius: 4rpx; /* 统一按钮圆角 */
  margin-right: 10rpx;
  border: 1rpx solid #e0e0e0; /* 添加边框 */
}

.image-count {
  font-size: 24rpx;
  color: #999999; /* 更新为规范的浅色文字 */
  background-color: #f5f5f7; /* 添加背景色 */
  padding: 4rpx 10rpx; /* 添加内边距 */
  border-radius: 20rpx; /* 圆角更大 */
}

.expense-action {
  display: flex;
  justify-content: flex-end;
  width: 100%; /* 确保占据整个空间 */
}

.view-detail-btn {
  display: inline-block;
  padding: 6rpx 20rpx;
  background-color: #0070c9; /* 更新为规范的强调色 */
  color: #fff;
  border-radius: 4rpx; /* 统一按钮圆角 */
  font-size: 24rpx;
  margin-right: 0; /* 移除右边距 */
  box-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.1); /* 添加按钮阴影 */
  transition: all 0.2s ease;
}

.view-detail-btn:active {
  opacity: 0.9;
  transform: translateY(1rpx);
}

.delete-btn {
  display: none; /* 隐藏删除按钮，但保留其功能 */
  padding: 6rpx 20rpx;
  background-color: #ff3b30; /* 更新为规范的危险色 */
  color: #fff;
  border-radius: 4rpx; /* 统一按钮圆角 */
  font-size: 24rpx;
  box-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.1); /* 添加按钮阴影 */
  transition: all 0.2s ease;
}

.delete-btn:active {
  opacity: 0.9;
  transform: translateY(1rpx);
}

/* 分页 */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 30rpx;
}

.page-btn {
  padding: 10rpx 30rpx;
  background-color: #f5f5f7; /* 更新为规范的浅色背景 */
  color: #666666; /* 更新为规范的次要文字色 */
  border-radius: 4rpx; /* 统一按钮圆角 */
  font-size: 28rpx;
  margin: 0 10rpx;
  transition: all 0.2s ease;
}

.page-btn:active:not(.disabled) {
  opacity: 0.9;
  transform: translateY(1rpx);
}

.page-btn.disabled {
  background-color: #f0f0f0;
  color: #cccccc;
}

.page-info {
  font-size: 28rpx;
  color: #666666; /* 更新为规范的次要文字色 */
}

/* 空状态 */
.empty-state {
  padding: 100rpx 0;
  text-align: center;
}

.empty-text {
  font-size: 24rpx; /* 减小字体大小 */
  color: #999999; /* 更新为规范的浅色文字 */
}

/* 支出详情弹窗 */
.expense-detail-panel {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  visibility: hidden;
  opacity: 0;
  transition: all 0.3s ease;
}

.expense-detail-panel.show {
  visibility: visible;
  opacity: 1;
}

.panel-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
}

.panel-content {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 70%;
  background-color: #fff;
  border-radius: 20rpx 20rpx 0 0; /* 顶部圆角 */
  overflow: hidden;
  transform: translateY(100%);
  transition: transform 0.3s ease;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1); /* 添加阴影效果 */
}

.expense-detail-panel.show .panel-content {
  transform: translateY(0);
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25rpx 30rpx; /* 调整内边距 */
  background-color: #f9f9f9; /* 添加背景色 */
  border-bottom: 1rpx solid #e0e0e0; /* 更新为规范的边框颜色 */
}

.panel-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333; /* 更新为规范的文字主色 */
  position: relative;
  padding-left: 20rpx; /* 为左侧装饰留出空间 */
}

.panel-title::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 6rpx;
  height: 32rpx;
  background-color: #ff3b30; /* 与支出记录项左侧边框颜色一致 */
  border-radius: 3rpx;
}

.panel-close {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  color: #999999; /* 更新为规范的浅色文字 */
  border-radius: 50%; /* 圆形按钮 */
  transition: all 0.2s ease;
}

.panel-close:active {
  background-color: rgba(0, 0, 0, 0.05);
  transform: scale(0.95);
}

.panel-body {
  height: calc(100% - 120rpx);
  overflow-y: auto;
  padding: 30rpx; /* 增加内边距 */
}

.detail-item {
  margin-bottom: 30rpx; /* 增加间距 */
  background-color: #f9f9f9; /* 添加背景色 */
  border-radius: 6rpx; /* 统一卡片圆角 */
  padding: 20rpx; /* 添加内边距 */
  transition: all 0.2s ease;
}

.detail-item:hover {
  background-color: #f5f5f7;
}

.detail-label {
  font-size: 26rpx;
  color: #999999; /* 更新为规范的浅色文字 */
  margin-bottom: 10rpx; /* 增加间距 */
  display: block;
}

.detail-value {
  font-size: 32rpx; /* 增大字体大小 */
  color: #333333; /* 更新为规范的文字主色 */
  font-weight: 500; /* 半粗体 */
  word-break: break-all; /* 允许在任意字符间断行 */
}

/* 支出金额特殊显示 */
.detail-item:nth-child(2) .detail-value {
  color: #ff3b30; /* 支出相关元素使用红色 */
  font-weight: bold;
  font-size: 36rpx; /* 更大的字体 */
}

.detail-images {
  display: flex;
  flex-wrap: wrap;
  margin-top: 15rpx; /* 增加间距 */
}

.detail-image {
  width: 210rpx; /* 略微增加图片尺寸 */
  height: 210rpx;
  margin-right: 15rpx;
  margin-bottom: 15rpx;
  border-radius: 6rpx; /* 统一卡片圆角 */
  border: 1rpx solid #e0e0e0; /* 添加边框 */
  box-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.05); /* 添加轻微阴影 */
}

.detail-action {
  margin-top: 50rpx; /* 增加间距 */
  display: flex;
  justify-content: center;
  min-height: 70rpx; /* 确保即使删除按钮隐藏，空间仍然保留 */
}

.detail-action .delete-btn {
  display: none; /* 隐藏详情弹窗中的删除按钮 */
  padding: 15rpx 80rpx; /* 增加按钮宽度 */
  font-size: 28rpx;
  background-color: #ff3b30; /* 更新为规范的危险色 */
  border-radius: 4rpx; /* 统一按钮圆角 */
  box-shadow: 0 4rpx 8rpx rgba(255, 59, 48, 0.2); /* 增强阴影效果 */
}

.detail-action .delete-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 4rpx rgba(255, 59, 48, 0.2);
}

/* 为空白的操作区域添加样式，确保布局正确 */
.expense-action:empty {
  display: none;
} 