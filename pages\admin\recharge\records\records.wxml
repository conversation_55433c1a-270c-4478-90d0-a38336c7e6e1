<view class="records-container">
  <!-- 状态栏安全区域 -->
  <view class="status-bar"></view>
  
  <!-- 头部导航栏 -->
  <view class="header">
    <view class="back-btn" bindtap="navigateBack">
      <view class="arrow-left"></view>
    </view>
    <view class="header-title">充值记录管理</view>
    <view class="placeholder-btn"></view>
  </view>

  <!-- 搜索框 -->
  <view class="search-box">
    <input class="search-input" placeholder="请输入用户ID搜索" value="{{searchKeyword}}" bindinput="inputKeyword" confirm-type="search" bindconfirm="search"></input>
    <view class="search-btn" bindtap="search">搜索</view>
    <view class="filter-btn" bindtap="showFilter">筛选</view>
  </view>

  <!-- 标签页 -->
  <view class="tab-container">
    <scroll-view scroll-x class="tabs" enable-flex>
      <block wx:for="{{tabs}}" wx:key="value">
        <view class="tab-item {{currentTab === item.value ? 'active' : ''}}" bindtap="switchTab" data-tab="{{item.value}}">{{item.label}}</view>
      </block>
    </scroll-view>
  </view>

  <!-- 记录列表 -->
  <view class="records-list" wx:if="{{!loading && rechargeRecords.length > 0}}">
    <view class="list-header">
      <view class="header-item">充值方案</view>
      <view class="header-item">金额</view>
      <view class="header-item">状态</view>
      <view class="header-item">时间</view>
    </view>

    <block wx:for="{{rechargeRecords}}" wx:key="_id">
      <view class="record-item" bindtap="viewRecordDetail" data-id="{{item._id}}">
        <view class="item-content">
          <view class="item-row">
            <view class="label">充值方案:</view>
            <view class="value">{{item.planTitle}}</view>
          </view>
          <view class="item-row">
            <view class="label">订单号:</view>
            <view class="value order-id">{{item.orderId}}</view>
          </view>
          <view class="item-row">
            <view class="label">用户ID:</view>
            <view class="value user-id">{{item.openid}}</view>
          </view>
          <view class="item-row">
            <view class="label">充值金额:</view>
            <view class="value">{{item.amountStr}}</view>
          </view>
          <view class="item-row">
            <view class="label">赠送金额:</view>
            <view class="value">{{item.bonusStr}}</view>
          </view>
          <view class="item-row">
            <view class="label">状态:</view>
            <view class="value status {{item.status}}">{{item.statusText}}</view>
          </view>
          <view class="item-row">
            <view class="label">创建时间:</view>
            <view class="value">{{item.createTimeStr}}</view>
          </view>
          <view class="item-row" wx:if="{{item.verifyTime}}">
            <view class="label">核销时间:</view>
            <view class="value">{{item.verifyTimeStr}}</view>
          </view>
          <view class="item-row" wx:if="{{item.cancelTime}}">
            <view class="label">取消时间:</view>
            <view class="value">{{item.cancelTimeStr}}</view>
          </view>
        </view>
      </view>
    </block>

    <!-- 加载更多 -->
    <view class="load-more" wx:if="{{loadingMore}}">加载中...</view>
    <view class="no-more" wx:if="{{!loadingMore && rechargeRecords.length >= totalRecords}}">没有更多数据</view>
  </view>

  <!-- 无数据提示 -->
  <view class="empty-container" wx:if="{{!loading && rechargeRecords.length === 0}}">
    <view class="empty-text">暂无充值记录~</view>
  </view>

  <!-- 加载中 -->
  <view class="loading-container" wx:if="{{loading && page === 1}}">
    <view class="loading">加载中...</view>
  </view>

  <!-- 筛选弹窗 -->
  <view class="filter-modal {{showFilterModal ? 'show' : ''}}" wx:if="{{showFilterModal}}">
    <view class="modal-mask" bindtap="closeFilter"></view>
    <view class="modal-content">
      <view class="modal-header">
        <text>筛选条件</text>
        <view class="close-icon" bindtap="closeFilter">×</view>
      </view>

      <view class="filter-form">
        <view class="form-item">
          <view class="form-label">开始日期</view>
          <picker mode="date" value="{{dateRange.start}}" bindchange="bindStartDateChange">
            <view class="picker">{{dateRange.start || '请选择开始日期'}}</view>
          </picker>
        </view>

        <view class="form-item">
          <view class="form-label">结束日期</view>
          <picker mode="date" value="{{dateRange.end}}" bindchange="bindEndDateChange">
            <view class="picker">{{dateRange.end || '请选择结束日期'}}</view>
          </picker>
        </view>

        <view class="filter-btns">
          <button class="btn-reset" bindtap="resetFilter">重置</button>
          <button class="btn-apply" bindtap="applyFilter">应用</button>
        </view>
      </view>
    </view>
  </view>
</view> 