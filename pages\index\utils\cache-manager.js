/**
 * 缓存管理器
 * 专门处理视频数据缓存、版本控制和缓存策略
 * @version 1.0.0
 */

const { 
  CACHE_CONFIG, 
  VIDEO_CONFIG,
  ERROR_TYPES 
} = require('../constants/index-constants');

class CacheManager {
  constructor() {
    this.cacheKeys = {
      VIDEO_LIST: CACHE_CONFIG.VIDEO_LIST_CACHE_KEY,
      VIDEO_URL_PREFIX: CACHE_CONFIG.VIDEO_URL_CACHE_PREFIX,
      CACHE_VERSION: 'cache_version',
      CACHE_STATS: 'cache_stats'
    };
    
    // 缓存版本，用于缓存失效
    this.currentVersion = '1.0.0';
    
    // 缓存统计
    this.stats = {
      hits: 0,
      misses: 0,
      writes: 0,
      deletes: 0
    };
    
    this.init();
  }

  /**
   * 初始化缓存管理器
   */
  init() {
    try {
      // 检查缓存版本
      this.checkCacheVersion();
      
      // 加载缓存统计
      this.loadCacheStats();
      
      // 清理过期缓存
      this.cleanExpiredCache();
      
      console.log('[CacheManager] 缓存管理器初始化完成');
    } catch (error) {
      console.error('[CacheManager] 初始化失败:', error);
    }
  }

  /**
   * 检查缓存版本，如果版本不匹配则清空所有缓存
   */
  checkCacheVersion() {
    try {
      const storedVersion = wx.getStorageSync(this.cacheKeys.CACHE_VERSION);
      
      if (storedVersion !== this.currentVersion) {
        console.log(`[CacheManager] 缓存版本不匹配 (${storedVersion} -> ${this.currentVersion})，清空缓存`);
        this.clearAllCache();
        wx.setStorageSync(this.cacheKeys.CACHE_VERSION, this.currentVersion);
      }
    } catch (error) {
      console.error('[CacheManager] 检查缓存版本失败:', error);
    }
  }

  /**
   * 缓存视频列表数据
   * @param {Array} videoList - 视频列表
   * @param {object} options - 缓存选项
   */
  cacheVideoList(videoList, options = {}) {
    try {
      if (!Array.isArray(videoList)) {
        throw new Error('videoList must be an array');
      }

      const cacheData = {
        data: videoList,
        timestamp: Date.now(),
        expireTime: options.expireTime || CACHE_CONFIG.CACHE_EXPIRE_TIME,
        version: this.currentVersion,
        count: videoList.length,
        checksum: this.generateChecksum(videoList)
      };

      wx.setStorageSync(this.cacheKeys.VIDEO_LIST, cacheData);
      this.stats.writes++;
      
      console.log(`[CacheManager] 视频列表已缓存，数量: ${videoList.length}`);
      return true;
    } catch (error) {
      console.error('[CacheManager] 缓存视频列表失败:', error);
      return false;
    }
  }

  /**
   * 获取缓存的视频列表
   * @param {object} options - 获取选项
   * @returns {Array|null} 视频列表或null
   */
  getCachedVideoList(options = {}) {
    try {
      const cacheData = wx.getStorageSync(this.cacheKeys.VIDEO_LIST);
      
      if (!cacheData) {
        this.stats.misses++;
        return null;
      }

      // 检查是否过期
      if (this.isCacheExpired(cacheData)) {
        wx.removeStorageSync(this.cacheKeys.VIDEO_LIST);
        this.stats.misses++;
        console.log('[CacheManager] 视频列表缓存已过期');
        return null;
      }

      // 验证数据完整性
      if (options.validateChecksum && !this.validateChecksum(cacheData)) {
        wx.removeStorageSync(this.cacheKeys.VIDEO_LIST);
        this.stats.misses++;
        console.log('[CacheManager] 视频列表缓存校验失败');
        return null;
      }

      this.stats.hits++;
      console.log(`[CacheManager] 获取缓存视频列表，数量: ${cacheData.count}`);
      return cacheData.data;
    } catch (error) {
      console.error('[CacheManager] 获取缓存视频列表失败:', error);
      this.stats.misses++;
      return null;
    }
  }

  /**
   * 缓存视频URL
   * @param {string} videoId - 视频ID
   * @param {string} videoUrl - 视频URL
   * @param {object} options - 缓存选项
   */
  cacheVideoUrl(videoId, videoUrl, options = {}) {
    try {
      if (!videoId || !videoUrl) {
        throw new Error('videoId and videoUrl are required');
      }

      const cacheKey = `${this.cacheKeys.VIDEO_URL_PREFIX}${videoId}`;
      const cacheData = {
        url: videoUrl,
        timestamp: Date.now(),
        expireTime: options.expireTime || CACHE_CONFIG.URL_CACHE_EXPIRE_TIME,
        version: this.currentVersion,
        videoId: videoId
      };

      wx.setStorageSync(cacheKey, cacheData);
      this.stats.writes++;
      
      console.log(`[CacheManager] 视频URL已缓存: ${videoId}`);
      return true;
    } catch (error) {
      console.error('[CacheManager] 缓存视频URL失败:', error);
      return false;
    }
  }

  /**
   * 获取缓存的视频URL
   * @param {string} videoId - 视频ID
   * @returns {string|null} 视频URL或null
   */
  getCachedVideoUrl(videoId) {
    try {
      if (!videoId) {
        return null;
      }

      const cacheKey = `${this.cacheKeys.VIDEO_URL_PREFIX}${videoId}`;
      const cacheData = wx.getStorageSync(cacheKey);
      
      if (!cacheData) {
        this.stats.misses++;
        return null;
      }

      // 检查是否过期
      if (this.isCacheExpired(cacheData)) {
        wx.removeStorageSync(cacheKey);
        this.stats.misses++;
        console.log(`[CacheManager] 视频URL缓存已过期: ${videoId}`);
        return null;
      }

      this.stats.hits++;
      console.log(`[CacheManager] 获取缓存视频URL: ${videoId}`);
      return cacheData.url;
    } catch (error) {
      console.error('[CacheManager] 获取缓存视频URL失败:', error);
      this.stats.misses++;
      return null;
    }
  }

  /**
   * 批量缓存视频URL
   * @param {object} urlMap - 视频ID到URL的映射
   */
  batchCacheVideoUrls(urlMap) {
    try {
      if (!urlMap || typeof urlMap !== 'object') {
        throw new Error('urlMap must be an object');
      }

      let successCount = 0;
      const entries = Object.entries(urlMap);
      
      entries.forEach(([videoId, videoUrl]) => {
        if (this.cacheVideoUrl(videoId, videoUrl)) {
          successCount++;
        }
      });

      console.log(`[CacheManager] 批量缓存视频URL完成: ${successCount}/${entries.length}`);
      return successCount;
    } catch (error) {
      console.error('[CacheManager] 批量缓存视频URL失败:', error);
      return 0;
    }
  }

  /**
   * 批量获取缓存的视频URL
   * @param {Array} videoIds - 视频ID数组
   * @returns {object} 视频ID到URL的映射
   */
  batchGetCachedVideoUrls(videoIds) {
    try {
      if (!Array.isArray(videoIds)) {
        throw new Error('videoIds must be an array');
      }

      const urlMap = {};
      let hitCount = 0;

      videoIds.forEach(videoId => {
        const url = this.getCachedVideoUrl(videoId);
        if (url) {
          urlMap[videoId] = url;
          hitCount++;
        }
      });

      console.log(`[CacheManager] 批量获取视频URL: ${hitCount}/${videoIds.length} 命中`);
      return urlMap;
    } catch (error) {
      console.error('[CacheManager] 批量获取视频URL失败:', error);
      return {};
    }
  }

  /**
   * 检查缓存是否过期
   * @param {object} cacheData - 缓存数据
   * @returns {boolean} 是否过期
   */
  isCacheExpired(cacheData) {
    if (!cacheData || !cacheData.timestamp) {
      return true;
    }

    const now = Date.now();
    const age = now - cacheData.timestamp;
    return age > cacheData.expireTime;
  }

  /**
   * 生成数据校验和
   * @param {*} data - 要生成校验和的数据
   * @returns {string} 校验和
   */
  generateChecksum(data) {
    try {
      const str = JSON.stringify(data);
      let hash = 0;
      
      for (let i = 0; i < str.length; i++) {
        const char = str.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash; // 转换为32位整数
      }
      
      return hash.toString(36);
    } catch (error) {
      console.error('[CacheManager] 生成校验和失败:', error);
      return '';
    }
  }

  /**
   * 验证数据校验和
   * @param {object} cacheData - 缓存数据
   * @returns {boolean} 校验是否通过
   */
  validateChecksum(cacheData) {
    try {
      if (!cacheData.checksum || !cacheData.data) {
        return false;
      }

      const currentChecksum = this.generateChecksum(cacheData.data);
      return currentChecksum === cacheData.checksum;
    } catch (error) {
      console.error('[CacheManager] 验证校验和失败:', error);
      return false;
    }
  }

  /**
   * 清理过期缓存
   */
  cleanExpiredCache() {
    try {
      const storageInfo = wx.getStorageInfoSync();
      const now = Date.now();
      let cleanedCount = 0;

      storageInfo.keys.forEach(key => {
        try {
          // 只处理我们的缓存键
          if (key.startsWith(this.cacheKeys.VIDEO_LIST) || 
              key.startsWith(this.cacheKeys.VIDEO_URL_PREFIX)) {
            const cacheData = wx.getStorageSync(key);
            
            if (this.isCacheExpired(cacheData)) {
              wx.removeStorageSync(key);
              cleanedCount++;
              this.stats.deletes++;
            }
          }
        } catch (e) {
          // 忽略单个缓存项的错误
          console.warn(`[CacheManager] 清理缓存项失败: ${key}`, e);
        }
      });

      if (cleanedCount > 0) {
        console.log(`[CacheManager] 清理了 ${cleanedCount} 个过期缓存`);
      }

      // 保存统计信息
      this.saveCacheStats();
    } catch (error) {
      console.error('[CacheManager] 清理过期缓存失败:', error);
    }
  }

  /**
   * 清空所有缓存
   */
  clearAllCache() {
    try {
      const storageInfo = wx.getStorageInfoSync();
      let clearedCount = 0;

      storageInfo.keys.forEach(key => {
        try {
          if (key.startsWith(this.cacheKeys.VIDEO_LIST) || 
              key.startsWith(this.cacheKeys.VIDEO_URL_PREFIX)) {
            wx.removeStorageSync(key);
            clearedCount++;
            this.stats.deletes++;
          }
        } catch (e) {
          console.warn(`[CacheManager] 清除缓存项失败: ${key}`, e);
        }
      });

      console.log(`[CacheManager] 已清空所有缓存，共 ${clearedCount} 项`);
      
      // 重置统计
      this.stats = { hits: 0, misses: 0, writes: 0, deletes: 0 };
      this.saveCacheStats();
    } catch (error) {
      console.error('[CacheManager] 清空缓存失败:', error);
    }
  }

  /**
   * 获取缓存统计信息
   * @returns {object} 统计信息
   */
  getCacheStats() {
    try {
      const totalRequests = this.stats.hits + this.stats.misses;
      const hitRate = totalRequests > 0 ? (this.stats.hits / totalRequests * 100).toFixed(2) : 0;
      
      return {
        ...this.stats,
        hitRate: `${hitRate}%`,
        totalRequests: totalRequests
      };
    } catch (error) {
      console.error('[CacheManager] 获取缓存统计失败:', error);
      return this.stats;
    }
  }

  /**
   * 加载缓存统计
   */
  loadCacheStats() {
    try {
      const savedStats = wx.getStorageSync(this.cacheKeys.CACHE_STATS);
      if (savedStats) {
        this.stats = { ...this.stats, ...savedStats };
      }
    } catch (error) {
      console.error('[CacheManager] 加载缓存统计失败:', error);
    }
  }

  /**
   * 保存缓存统计
   */
  saveCacheStats() {
    try {
      wx.setStorageSync(this.cacheKeys.CACHE_STATS, this.stats);
    } catch (error) {
      console.error('[CacheManager] 保存缓存统计失败:', error);
    }
  }

  /**
   * 获取缓存大小信息
   * @returns {object} 缓存大小信息
   */
  getCacheSize() {
    try {
      const storageInfo = wx.getStorageInfoSync();
      let cacheCount = 0;
      let totalSize = 0;

      storageInfo.keys.forEach(key => {
        if (key.startsWith(this.cacheKeys.VIDEO_LIST) || 
            key.startsWith(this.cacheKeys.VIDEO_URL_PREFIX)) {
          cacheCount++;
          // 估算大小（微信小程序无法直接获取存储大小）
          try {
            const data = wx.getStorageSync(key);
            totalSize += JSON.stringify(data).length;
          } catch (e) {
            // 忽略错误
          }
        }
      });

      return {
        count: cacheCount,
        estimatedSize: totalSize,
        formattedSize: this.formatBytes(totalSize)
      };
    } catch (error) {
      console.error('[CacheManager] 获取缓存大小失败:', error);
      return { count: 0, estimatedSize: 0, formattedSize: '0 B' };
    }
  }

  /**
   * 格式化字节数
   * @param {number} bytes - 字节数
   * @returns {string} 格式化后的大小
   */
  formatBytes(bytes) {
    if (bytes === 0) return '0 B';
    
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}

// 创建单例实例
const cacheManager = new CacheManager();

module.exports = cacheManager;