<!-- 员工登录页面 -->
<view class="staff-login-container" wx:if="{{pageReady}}">
  <view class="login-header">
    <view class="logo-container">
      <image class="logo" src="/static/logo.png" mode="aspectFit"></image>
    </view>
    <view class="staff-title">员工登录入口</view>
  </view>
  
  <view class="login-form">
    <view class="input-group">
      <view class="input-label">登录账号</view>
      <input 
        class="input-control" 
        type="number" 
        maxlength="11" 
        placeholder="请输入登录账号" 
        value="{{phoneNumber}}" 
        bindinput="inputPhoneNumber" 
      />
    </view>
    
    <view class="input-group">
      <view class="input-label">登录密码</view>
      <input 
        class="input-control" 
        password 
        placeholder="请输入登录密码" 
        value="{{password}}" 
        bindinput="inputPassword" 
      />
    </view>
    
    <button 
      class="login-btn" 
      bindtap="login" 
      loading="{{isLoading}}"
      hover-class="btn-hover"
    >登录</button>
    
    <view class="login-tips">
      <text>* 请联系管理员获取账号</text>
    </view>
  </view>
  
  <view class="back-btn" bindtap="goBack">返回</view>
  
  <view class="system-title">品牌运营管理系统</view>
</view>

<!-- 当页面未准备好时，显示空白页面，避免闪屏 -->
<view class="loading-container" wx:else>
  <!-- 不显示任何内容，保持页面空白，等待跳转 -->
</view> 