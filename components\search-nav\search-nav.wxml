<view class="search-nav {{isFullscreenMode ? 'search-nav-hidden' : ''}}">
  <!-- 状态栏 -->
  <view class="status-bar" style="height: {{statusBarHeight}}px"></view>
  
  <!-- 导航栏主体 -->
  <view class="navigation-bar" style="height: {{navigationBarHeight}}px">
    <!-- 搜索框 -->
    <view 
      class="search-bar"
      style="width: {{searchBarWidth}}; margin-left: 16px;"
    >
      <view class="search-bar-inner {{isFocused ? 'focused' : ''}}">
        <image class="search-icon" src="/static/搜索图标.svg" mode="aspectFit"></image>
        <input
          class="search-input"
          placeholder="{{placeholder}}"
          placeholder-style="color: rgba(255, 255, 255, 0.5);"
          confirm-type="search"
          focus="{{isFocused}}"
          value="{{searchValue}}"
          bindfocus="onSearchFocus"
          bindblur="onSearchBlur"
          bindinput="onSearchInput"
          bindconfirm="onSearchConfirm"
        />
        <view wx:if="{{searchValue}}" class="clear-button" catchtap="clearSearch">
          <text class="clear-icon">×</text>
        </view>
      </view>
    </view>
  </view>
</view>

<!-- 取消使用全屏遮罩层，改为点击空白区域触发事件 -->
<view class="search-backdrop" wx:if="{{isFocused && !isFullscreenMode}}" catchtap="onBackdropTap"></view>

<!-- 搜索历史和推荐区域 -->
<view class="search-suggestion" wx:if="{{isFocused && !searchValue && !isFullscreenMode}}">
  <!-- 搜索历史 -->
  <view class="search-section" wx:if="{{searchHistory.length > 0}}">
    <view class="section-header">
      <text class="section-title">搜索历史</text>
      <view class="clear-history" catchtap="clearSearchHistory">
        <image class="clear-icon-small" src="/static/退出图标.svg" mode="aspectFit"></image>
      </view>
    </view>
    <view class="keyword-list">
      <view 
        class="keyword-item" 
        wx:for="{{searchHistory}}" 
        wx:key="*this" 
        data-keyword="{{item}}"
        bindtap="onHistoryItemTap"
      >
        <image class="keyword-icon" src="/static/标签图标.svg" mode="aspectFit"></image>
        <text class="keyword-text">{{item}}</text>
      </view>
    </view>
  </view>
  
  <!-- 热门搜索 -->
  <view class="search-section">
    <view class="section-header">
      <text class="section-title">热门搜索</text>
    </view>
    <view class="hot-keyword-list">
      <view 
        class="hot-keyword-item" 
        wx:for="{{hotKeywords}}" 
        wx:key="*this"
        data-keyword="{{item}}"
        bindtap="onHotKeywordTap"
      >
        <text class="hot-keyword-text">{{item}}</text>
      </view>
    </view>
  </view>
</view> 