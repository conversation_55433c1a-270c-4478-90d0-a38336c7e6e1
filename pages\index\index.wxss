/* ================== 首页样式 (请勿修改) ================== */
/* @feature: 首页基础样式 */
/* @version: 1.0.0 */
/* @warning: 以下代码已完成首页基础样式，请勿修改 */

page {
  height: 100%;
  width: 100%;
  position: fixed;  /* 固定整个页面，防止页面级滚动 */
  overflow: hidden; /* 防止页面级滚动 */
  background: none; /* 移除页面背景，使用固定背景层 */
}

/* 固定的渐变背景层 */
.fixed-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--primary-gradient); /* 使用主题渐变背景 */
  z-index: 1; /* 最底层 */
}

/* 页面容器 */
.container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: transparent; /* 改为透明背景，显示下方的固定背景层 */
  overflow: hidden;
  z-index: 2; /* 确保在固定背景层之上 */
}

/* 内容区域 */
.content {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  z-index: 3; /* 在容器之上 */
}

/* 主滚动区域 */
.main-scroll {
  position: relative;
  min-height: 100%;
  width: 100%;
  box-sizing: border-box;
  padding-bottom: 60px;
  opacity: 0; /* 默认隐藏 */
  transform: translateY(68rpx) scale(0.96); /* 减少15%幅度：80rpx->68rpx, 0.95->0.96 */
  transition: opacity 1.5s cubic-bezier(0.25, 0.46, 0.45, 0.94), 
              transform 1.5s cubic-bezier(0.25, 0.46, 0.45, 0.94); /* 使用更舒适的缓动函数，增加到1.5秒 */
  touch-action: pan-y;
  z-index: 4; /* 在内容区域之上 */
}

/* 主滚动区域显示状态 */
.main-scroll.show-content {
  opacity: 1;
  transform: translateY(0) scale(1); /* 恢复正常大小和位置 */
}

/* 移除之前的 before 伪元素，因为现在背景已经固定在容器上 */
.main-scroll::before {
  display: none;
}

/* 主滚动样式 - 合并到上面的定义中，移除重复 */

.main-scroll.hidden {
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s ease;
}

/* ================== 在此线以下添加新功能样式 ================== */

/* 下拉刷新提示样式 */
.refresh-tip {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10rpx 20rpx;
  color: #ffffff;
  font-size: 26rpx;
  background-color: rgba(0, 0, 0, 0.7);
  border-radius: 8rpx;
  margin: 20rpx auto;
  z-index: 10;
}

.refresh-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 10rpx;
  border: 3rpx solid rgba(255, 255, 255, 0.3);
  border-top: 3rpx solid #ffffff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* ================== 视频列表样式 (v1.0.0) ================== */
/* 顶部空白占位视图，确保第一个视频卡与搜索栏保持足够距离 */
.top-spacing {
  width: 100%;
  height: 150rpx; /* 将高度从130rpx增加到150rpx，进一步增加间距 */
  display: block;
}

.top-spacing.hidden {
  display: none; /* 搜索状态下隐藏顶部占位，由容器的padding控制 */
}

/* 搜索状态下专用的顶部间距，确保第一个搜索结果完整显示 */
.search-top-spacing {
  width: 100%;
  height: 260rpx; /* 特意设置较大的值，确保搜索栏下方有足够空间 */
  display: block;
}

/* 搜索状态下调整顶部间距，确保第一个视频完整显示 */
.video-list.slide-in.search-active {
  padding-top: 40rpx; /* 减少顶部padding，因为已经有专门的间距元素 */
  padding-bottom: 60rpx; /* 增加底部间距，确保最后一个视频可以完整显示 */
}

/* 搜索结果的第一个视频项特殊样式 */
.video-list.search-active .video-item:first-child {
  margin-top: 10rpx; /* 给第一个搜索结果添加额外的顶部间距 */
  position: relative;
}

/* 底部空白占位视图，确保内容可以滚动超过底部导航栏 */
.bottom-spacing {
  width: 100%;
  height: 50rpx; /* 从100rpx减少到50rpx */
  padding-bottom: 30rpx; /* 从50rpx减少到30rpx */
  margin-top: 5rpx; /* 从10rpx减少到5rpx */
}

/* 视频列表容器 */
.video-list {
  position: relative;
  z-index: 2;
  min-height: 100%;
  padding-top: 0;
  padding-bottom: env(safe-area-inset-bottom);
}

/* 视频列表样式 - 简化为直接显示，无动画 */
.video-list {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  padding: 50rpx 0 40rpx 0;
  opacity: 1;
  transform: translateY(0) scale(1);
}

.video-list.slide-in {
  opacity: 1;
  transform: translateY(0) scale(1);
}

/* 添加新的动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.video-item {
  width: 100%;
  display: flex;
  justify-content: center;
  margin-bottom: 25rpx;
  opacity: 1;
  transform: translateY(0) scale(1);
}

/* 搜索结果动画效果 - 全新代码 */
.video-item.search-result {
  animation-name: fadeInUp;
  animation-duration: 0.5s;
  animation-timing-function: ease;
  animation-fill-mode: forwards;
  opacity: 0;
  transform: translateY(50px);
  transition: none;
}

/* 设置搜索结果的延迟时间 */
.video-item.search-result:nth-child(1) { animation-delay: 0.05s; }
.video-item.search-result:nth-child(2) { animation-delay: 0.1s; }
.video-item.search-result:nth-child(3) { animation-delay: 0.15s; }
.video-item.search-result:nth-child(4) { animation-delay: 0.2s; }
.video-item.search-result:nth-child(5) { animation-delay: 0.25s; }
.video-item.search-result:nth-child(6) { animation-delay: 0.3s; }
.video-item.search-result:nth-child(7) { animation-delay: 0.35s; }
.video-item.search-result:nth-child(8) { animation-delay: 0.4s; }

.video-item:last-child {
  margin-bottom: 20rpx; /* 从40rpx减少到20rpx，减小最后一个视频的底部间距 */
  position: relative;
}

.video-list.slide-in .video-item {
  opacity: 1;
  transform: translateY(0) scale(1);
}

/* 加载状态 - 保留容器但移除指示器样式，使用统一的loading-indicator组件 */
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx 0;
  width: 100%;
}

/* 移除旧的loading-indicator样式，使用新的loading-indicator组件替代 */

/* 仍需保留旋转动画，可能被其他元素使用 */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.no-data {
  text-align: center;
  color: var(--text-primary);
  font-size: 32rpx;
  padding: 40rpx;
  width: 100%;
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
  opacity: 0;
  transform: translateY(30rpx);
  transition: opacity 0.4s ease-out,
              transform 0.4s cubic-bezier(0.33, 1, 0.68, 1);
}

.video-list.slide-in .empty-container {
  opacity: 1;
  transform: translateY(0);
  transition-delay: 0.2s;
}

.empty-text {
  font-size: 30rpx;
  color: var(--text-primary);
}

/* ================== 在此线以下添加新功能 ================== */

/* 全屏弹窗内容样式 */
.fullscreen-modal-content {
  display: flex;
  flex-direction: column;
  min-height: 100%;
  padding: 0;
}

.modal-header-info {
  padding: 60rpx 40rpx;
  background-color: #000;
  color: var(--text-primary);
}

.modal-title-large {
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  color: var(--text-primary);
}

.modal-subtitle {
  font-size: 28rpx;
  opacity: 0.8;
  color: var(--text-secondary);
}

.modal-section {
  padding: 40rpx;
  border-bottom: 1rpx solid var(--border-color);
}

.section-title {
  font-size: 36rpx;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 30rpx;
}

.section-item {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.item-icon {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #4CAF50;
  color: white;
  border-radius: 50%;
  margin-right: 20rpx;
  font-size: 24rpx;
}

.item-text {
  font-size: 30rpx;
  color: var(--text-primary);
}

.image-grid {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -10rpx;
}

.grid-image {
  width: calc(50% - 20rpx);
  height: 200rpx;
  margin: 10rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
}

.modal-footer {
  padding: 40rpx;
  display: flex;
  justify-content: center;
  margin-top: auto;
}

.modal-button {
  background-color: #000;
  color: white;
  border-radius: 10rpx;
  font-size: 32rpx;
  padding: 20rpx 40rpx;
  min-width: 300rpx;
  text-align: center;
}

.custom-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #ffffff;
  border-bottom: 1rpx solid #f0f0f0;
  position: sticky;
  top: 0;
  z-index: 10;
}

.header-title {
  font-size: 34rpx;
  font-weight: 500;
  color: #333;
}

.header-close {
  font-size: 48rpx;
  color: #999;
  width: 60rpx;
  height: 60rpx;
  line-height: 56rpx;
  text-align: center;
  border-radius: 50%;
}

.header-close:active {
  background-color: #f5f5f5;
}
/* 为加载更多提示添加样式 */
.more-tip {
  text-align: center;
  color: #999;
  font-size: 24rpx;
  padding: 30rpx 0;
  width: 100%;
}

/* 全屏加载状态 */
.loading-full-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: transparent; /* 透明背景，显示下方的容器背景 */
  z-index: 1000; /* 确保在最上层 */
}

.loading-full-screen .loading-spinner {
  width: 80rpx;
  height: 80rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.15);
  border-top: 4rpx solid rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
  box-shadow: 0 0 10rpx rgba(0, 0, 0, 0.1);
}

.loading-full-screen .loading-text {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* 内联加载状态 */
.loading-inline {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30rpx 0;
}

.loading-inline .loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.2);
  border-top: 4rpx solid #ffffff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

/* ================== 闪光效果样式 =================== */
/* 闪光效果样式 - 用于下拉刷新时显示内容占位符 */
.loading-content-placeholder {
  position: relative;
  overflow: hidden;
  opacity: 0.7;
}

.loading-content-placeholder::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg, 
    rgba(255, 255, 255, 0) 0%, 
    rgba(255, 255, 255, 0.2) 50%, 
    rgba(255, 255, 255, 0) 100%
  );
  animation: loading-shimmer 1.5s infinite;
  z-index: 1;
}

@keyframes loading-shimmer {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(200%);
  }
}

/* 闪光效果的淡入淡出动画 */
.fade-transition {
  transition: opacity 0.3s ease;
}

/* ==================== 骨架屏样式 ==================== */

/* 骨架屏动画 */
@keyframes skeleton-loading {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

/* 骨架屏基础样式 */
.skeleton-item {
  width: 100%;
  display: flex;
  justify-content: center;
  margin-bottom: 25rpx;
  opacity: 1;
}

.skeleton-video-card {
  width: 690rpx;
  background: #1a1a1a;
  border-radius: 20rpx;
  overflow: hidden;
  border: 1rpx solid #333333;
  position: relative;
}

/* 视频封面骨架 */
.skeleton-cover {
  width: 100%;
  height: 388rpx;
  background: linear-gradient(90deg, #2a2a2a 25%, #404040 50%, #2a2a2a 75%);
  background-size: 200px 100%;
  animation: skeleton-loading 1.5s infinite;
  position: relative;
}

/* 播放按钮已移除，只保留纯净的封面框架 */

/* 视频信息骨架 */
.skeleton-info {
  padding: 30rpx;
  background: #1a1a1a;
}

.skeleton-title-line {
  height: 32rpx;
  background: linear-gradient(90deg, #2a2a2a 25%, #404040 50%, #2a2a2a 75%);
  background-size: 200px 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 6rpx;
  margin-bottom: 16rpx;
}

.skeleton-title-long {
  width: 85%;
}

.skeleton-title-short {
  width: 60%;
}

.skeleton-meta {
  display: flex;
  align-items: center;
  margin-top: 20rpx;
}

.skeleton-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: linear-gradient(90deg, #2a2a2a 25%, #404040 50%, #2a2a2a 75%);
  background-size: 200px 100%;
  animation: skeleton-loading 1.5s infinite;
  margin-right: 20rpx;
}

.skeleton-text {
  height: 24rpx;
  background: linear-gradient(90deg, #2a2a2a 25%, #404040 50%, #2a2a2a 75%);
  background-size: 200px 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 4rpx;
  margin-right: 30rpx;
}

.skeleton-author {
  width: 120rpx;
}

.skeleton-views {
  width: 80rpx;
}

