.search-nav {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background: var(--searchnav-bg);
  transition: opacity 0.3s ease;
}

/* 全屏模式下完全隐藏搜索栏 */
.search-nav-hidden {
  opacity: 0;
  visibility: hidden;
  pointer-events: none;
}

.status-bar {
  width: 100%;
}

.navigation-bar {
  position: relative;
  width: 100%;
  display: flex;
  align-items: center;
}

.search-bar {
  height: 32px;
  border-radius: 16px;
  overflow: hidden;
}

.search-bar-inner {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding: 0 15rpx;
  background: var(--searchnav-input-bg);
  border-radius: 16px;
  backdrop-filter: blur(5px);
  transition: all 0.3s ease;
}

.search-bar-inner.focused {
  background: var(--searchnav-input-focus-bg);
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
}

.search-icon {
  width: 18px;
  height: 18px;
  margin-left: 10rpx;
  flex-shrink: 0;
}

.search-input {
  flex: 1;
  height: 100%;
  line-height: 28px;
  padding: 0 10rpx;
  font-size: 14px;
  background-color: transparent;
  color: var(--searchnav-text);
}

.placeholder {
  color: var(--searchnav-placeholder);
  font-size: 14px;
}

/* 新的后备点击区域，只覆盖搜索框区域上方，不覆盖内容区域 */
.search-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 120px; /* 只覆盖到导航区域，不覆盖内容 */
  background: transparent; /* 完全透明 */
  z-index: 99;
}

/* 搜索建议区域 */
.search-suggestion {
  position: fixed;
  top: 0; /* 从顶部开始，而不是从导航栏下方 */
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--searchnav-suggestion-bg);
  backdrop-filter: blur(10px);
  z-index: 90; /* 降低z-index，确保在搜索栏下方 */
  padding: 0 30rpx 30rpx 30rpx; /* 移除顶部内边距，改为仅设置左右底部内边距 */
  padding-top: calc(var(--status-bar-height, 44px) + var(--nav-bar-height, 44px) + 60rpx); /* 增加顶部内边距 */
  overflow-y: auto;
  box-sizing: border-box;
  border-top: none; /* 确保没有顶部边框 */
}

/* 添加顶部渐变遮罩，覆盖搜索栏下方区域，创建无缝过渡效果 */
.search-suggestion::before {
  content: "";
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: calc(var(--status-bar-height, 44px) + var(--nav-bar-height, 44px)); /* 与导航栏高度一致 */
  background: var(--searchnav-bg); /* 使用与搜索栏相同的背景 */
  z-index: 91; /* 确保在搜索建议内容上方，但在搜索栏下方 */
  pointer-events: none; /* 允许点击穿透 */
}

/* 搜索区块样式 */
.search-section {
  margin-top: 30rpx; /* 增加顶部外边距 */
  margin-bottom: 40rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 30rpx;
  color: var(--searchnav-section-title);
  font-weight: 500;
}

.clear-history {
  padding: 10rpx;
}

.clear-icon-small {
  width: 32rpx;
  height: 32rpx;
  opacity: 0.6;
}

/* 搜索历史样式 */
.keyword-list {
  display: flex;
  flex-wrap: wrap;
}

.keyword-item {
  display: inline-flex;
  align-items: center; /* 垂直居中 */
  justify-content: center; /* 水平居中 */
  height: 60rpx;
  min-width: 120rpx;
  /* 恢复对称的内边距 */
  padding: 0 24rpx; 
  background: var(--searchnav-keyword-bg);
  border-radius: 30rpx;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
  box-sizing: border-box;
  position: relative;
}

/* 使用伪元素在右侧添加一个占位，和左侧图标平衡 */
.keyword-item::after {
  content: "";
  display: block;
  width: 24rpx; /* 与图标宽度相同 */
  height: 24rpx;
  margin-left: 8rpx; /* 与图标右边距相同 */
  opacity: 0; /* 不可见 */
  flex-shrink: 0;
}

.keyword-icon {
  width: 24rpx;
  height: 24rpx;
  margin-right: 8rpx;
  opacity: 0.7;
  flex-shrink: 0;
}

.keyword-text {
  font-size: 24rpx;
  color: var(--searchnav-keyword-text);
  line-height: 1;
}

/* 热门搜索样式 */
.hot-keyword-list {
  display: flex;
  flex-wrap: wrap;
}

.hot-keyword-item {
  display: flex;
  align-items: center; /* 垂直居中 */
  justify-content: center; /* 水平居中 */
  min-width: 120rpx;
  height: 60rpx;
  padding: 0 24rpx;
  background: var(--searchnav-keyword-bg);
  border-radius: 30rpx;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.hot-keyword-item:active {
  background: var(--searchnav-keyword-active-bg);
}

.hot-keyword-text {
  font-size: 24rpx;
  color: var(--searchnav-keyword-text);
  line-height: 1; /* 使用最简单的行高 */
}

.clear-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36rpx;
  height: 36rpx;
  margin-right: 10rpx;
}

.clear-icon {
  color: rgba(255, 255, 255, 0.7);
  font-size: 24px;
  line-height: 24px;
}

.search-results {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  max-height: 80vh; /* 增加最大高度 */
  height: auto;
  bottom: auto;
  z-index: 90; /* 确保在搜索栏下方 */
  background: var(--searchnav-suggestion-bg);
  backdrop-filter: blur(10px);
  padding: 0 20rpx 20rpx 20rpx; /* 移除顶部内边距，改为仅设置左右底部内边距 */
  padding-top: calc(var(--status-bar-height, 44px) + var(--nav-bar-height, 44px) + 60rpx); /* 增加顶部内边距 */
  overflow-y: auto;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateY(20px); /* 初始向下偏移 */
  opacity: 0;
  visibility: hidden;
  border-top: none; /* 确保没有顶部边框 */
  box-sizing: border-box;
}

.search-results.active {
  transform: translateY(0); /* 恢复位置 */
  opacity: 1;
  visibility: visible;
}

/* 添加顶部渐变遮罩，覆盖搜索栏下方区域，创建无缝过渡效果 */
.search-results::before {
  content: "";
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: calc(var(--status-bar-height, 44px) + var(--nav-bar-height, 44px)); /* 与导航栏高度一致 */
  background: var(--searchnav-bg); /* 使用与搜索栏相同的背景 */
  z-index: 91; /* 确保在搜索建议内容上方，但在搜索栏下方 */
  pointer-events: none; /* 允许点击穿透 */
}

.search-result-item {
  display: flex;
  padding: 20rpx;
  margin-bottom: 20rpx;
  background: var(--searchnav-result-bg);
  border-radius: 12rpx;
  transition: all 0.2s ease;
  animation: slideUp 0.3s ease-out forwards;
  animation-delay: calc(var(--item-index, 0) * 0.05s); /* 项目索引延迟 */
  opacity: 0;
  transform: translateY(20px);
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.search-result-item-hover {
  background: var(--searchnav-result-hover-bg);
  transform: scale(0.98);
}

.result-thumb {
  width: 200rpx;
  height: 120rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
  background-color: rgba(0, 0, 0, 0.2);
  object-fit: cover;
}

.result-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.result-title {
  font-size: 28rpx;
  color: var(--searchnav-result-title);
  margin-bottom: 8rpx;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.result-subtitle {
  font-size: 24rpx;
  color: var(--searchnav-result-subtitle);
  margin-bottom: 8rpx;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.result-meta {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.5);
}

.highlight {
  color: var(--searchnav-highlight);
  font-weight: bold;
}

.search-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx 0;
}

.loading-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 3rpx solid rgba(255, 255, 255, 0.3);
  border-top: 3rpx solid rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  animation: spin 0.8s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.7);
}

.empty-results {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 180rpx;
}

.empty-text {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.5);
  text-align: center;
} 