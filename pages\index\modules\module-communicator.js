/**
 * 模块通信管理器
 * 负责模块间的通信、事件发布订阅、模块注册管理等功能
 */
class ModuleCommunicator {
  constructor() {
    // 已注册的模块
    this.modules = new Map();
    
    // 事件监听器
    this.eventListeners = new Map();
    
    // 通信日志（调试用）
    this.communicationLog = [];
    
    // 最大日志条数
    this.maxLogEntries = 100;
    
    // 是否启用调试模式
    this.debugMode = false;
    
    console.log('[ModuleCommunicator] 通信管理器已初始化');
  }
  
  /**
   * 注册模块
   * @param {string} name - 模块名称
   * @param {Object} module - 模块实例
   */
  registerModule(name, module) {
    if (!name || !module) {
      throw new Error('模块名称和模块实例都不能为空');
    }
    
    if (this.modules.has(name)) {
      console.warn(`[ModuleCommunicator] 模块 ${name} 已存在，将被覆盖`);
    }
    
    this.modules.set(name, module);
    this.log('register', `模块 ${name} 已注册`);
    
    // 触发模块注册事件
    this.emit('module:registered', { name, module });
  }
  
  /**
   * 注销模块
   * @param {string} name - 模块名称
   */
  unregisterModule(name) {
    if (!this.modules.has(name)) {
      console.warn(`[ModuleCommunicator] 尝试注销不存在的模块: ${name}`);
      return false;
    }
    
    const module = this.modules.get(name);
    this.modules.delete(name);
    this.log('unregister', `模块 ${name} 已注销`);
    
    // 触发模块注销事件
    this.emit('module:unregistered', { name, module });
    
    return true;
  }
  
  /**
   * 获取模块
   * @param {string} name - 模块名称
   * @returns {Object|null}
   */
  getModule(name) {
    return this.modules.get(name) || null;
  }
  
  /**
   * 获取所有已注册的模块名称
   * @returns {string[]}
   */
  getModuleNames() {
    return Array.from(this.modules.keys());
  }
  
  /**
   * 检查模块是否已注册
   * @param {string} name - 模块名称
   * @returns {boolean}
   */
  hasModule(name) {
    return this.modules.has(name);
  }
  
  /**
   * 发布事件
   * @param {string} event - 事件名称
   * @param {any} data - 事件数据
   * @param {Object} options - 选项
   */
  emit(event, data = null, options = {}) {
    const {
      async = false,
      timeout = 5000,
      source = 'unknown'
    } = options;
    
    this.log('emit', `发布事件 ${event}`, { data, source });
    
    const listeners = this.eventListeners.get(event) || [];
    
    if (listeners.length === 0) {
      this.log('emit', `事件 ${event} 没有监听器`);
      return;
    }
    
    // 同步执行监听器
    if (!async) {
      listeners.forEach(listener => {
        this.executeListener(listener, event, data);
      });
    } else {
      // 异步执行监听器
      Promise.all(
        listeners.map(listener => 
          this.executeListenerAsync(listener, event, data, timeout)
        )
      ).catch(error => {
        console.error(`[ModuleCommunicator] 异步事件执行失败:`, error);
      });
    }
  }
  
  /**
   * 监听事件
   * @param {string} event - 事件名称
   * @param {Function} listener - 监听器函数
   * @param {Object} options - 选项
   */
  on(event, listener, options = {}) {
    if (typeof listener !== 'function') {
      throw new Error('监听器必须是函数');
    }
    
    const {
      once = false,
      priority = 0,
      module = 'unknown'
    } = options;
    
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    
    const listenerInfo = {
      listener,
      once,
      priority,
      module,
      id: this.generateListenerId()
    };
    
    const listeners = this.eventListeners.get(event);
    listeners.push(listenerInfo);
    
    // 按优先级排序（优先级高的先执行）
    listeners.sort((a, b) => b.priority - a.priority);
    
    this.log('on', `模块 ${module} 监听事件 ${event}`);
    
    // 返回取消监听的函数
    return () => this.off(event, listenerInfo.id);
  }
  
  /**
   * 监听事件（只执行一次）
   * @param {string} event - 事件名称
   * @param {Function} listener - 监听器函数
   * @param {Object} options - 选项
   */
  once(event, listener, options = {}) {
    return this.on(event, listener, { ...options, once: true });
  }
  
  /**
   * 取消监听事件
   * @param {string} event - 事件名称
   * @param {string|Function} listenerOrId - 监听器函数或ID
   */
  off(event, listenerOrId) {
    const listeners = this.eventListeners.get(event);
    if (!listeners) {
      return false;
    }
    
    let index = -1;
    
    if (typeof listenerOrId === 'string') {
      // 通过ID查找
      index = listeners.findIndex(item => item.id === listenerOrId);
    } else if (typeof listenerOrId === 'function') {
      // 通过函数引用查找
      index = listeners.findIndex(item => item.listener === listenerOrId);
    }
    
    if (index !== -1) {
      const removed = listeners.splice(index, 1)[0];
      this.log('off', `取消监听事件 ${event}`, { module: removed.module });
      return true;
    }
    
    return false;
  }
  
  /**
   * 清除所有事件监听器
   * @param {string} event - 事件名称（可选，不传则清除所有）
   */
  clearListeners(event = null) {
    if (event) {
      this.eventListeners.delete(event);
      this.log('clear', `清除事件 ${event} 的所有监听器`);
    } else {
      this.eventListeners.clear();
      this.log('clear', '清除所有事件监听器');
    }
  }
  
  /**
   * 模块间直接通信
   * @param {string} fromModule - 发送方模块名称
   * @param {string} toModule - 接收方模块名称
   * @param {string} method - 要调用的方法名
   * @param {Array} args - 方法参数
   * @returns {any}
   */
  callModule(fromModule, toModule, method, args = []) {
    const targetModule = this.getModule(toModule);
    
    if (!targetModule) {
      throw new Error(`目标模块 ${toModule} 不存在`);
    }
    
    if (typeof targetModule[method] !== 'function') {
      throw new Error(`模块 ${toModule} 没有方法 ${method}`);
    }
    
    this.log('call', `${fromModule} 调用 ${toModule}.${method}`, { args });
    
    try {
      return targetModule[method].apply(targetModule, args);
    } catch (error) {
      console.error(`[ModuleCommunicator] 模块调用失败:`, error);
      throw error;
    }
  }
  
  /**
   * 异步模块间通信
   * @param {string} fromModule - 发送方模块名称
   * @param {string} toModule - 接收方模块名称
   * @param {string} method - 要调用的方法名
   * @param {Array} args - 方法参数
   * @param {number} timeout - 超时时间
   * @returns {Promise<any>}
   */
  async callModuleAsync(fromModule, toModule, method, args = [], timeout = 5000) {
    return new Promise((resolve, reject) => {
      const timeoutId = setTimeout(() => {
        reject(new Error(`模块调用超时: ${fromModule} -> ${toModule}.${method}`));
      }, timeout);
      
      try {
        const result = this.callModule(fromModule, toModule, method, args);
        clearTimeout(timeoutId);
        
        // 如果结果是Promise，等待它完成
        if (result && typeof result.then === 'function') {
          result.then(resolve).catch(reject);
        } else {
          resolve(result);
        }
      } catch (error) {
        clearTimeout(timeoutId);
        reject(error);
      }
    });
  }
  
  /**
   * 执行监听器
   * @param {Object} listenerInfo - 监听器信息
   * @param {string} event - 事件名称
   * @param {any} data - 事件数据
   */
  executeListener(listenerInfo, event, data) {
    try {
      listenerInfo.listener(data, event);
      
      // 如果是一次性监听器，执行后移除
      if (listenerInfo.once) {
        this.off(event, listenerInfo.id);
      }
    } catch (error) {
      console.error(`[ModuleCommunicator] 监听器执行失败:`, error);
    }
  }
  
  /**
   * 异步执行监听器
   * @param {Object} listenerInfo - 监听器信息
   * @param {string} event - 事件名称
   * @param {any} data - 事件数据
   * @param {number} timeout - 超时时间
   * @returns {Promise<void>}
   */
  async executeListenerAsync(listenerInfo, event, data, timeout) {
    return new Promise((resolve, reject) => {
      const timeoutId = setTimeout(() => {
        reject(new Error(`监听器执行超时: ${event}`));
      }, timeout);
      
      try {
        const result = listenerInfo.listener(data, event);
        
        // 如果是一次性监听器，执行后移除
        if (listenerInfo.once) {
          this.off(event, listenerInfo.id);
        }
        
        clearTimeout(timeoutId);
        
        // 如果结果是Promise，等待它完成
        if (result && typeof result.then === 'function') {
          result.then(resolve).catch(reject);
        } else {
          resolve(result);
        }
      } catch (error) {
        clearTimeout(timeoutId);
        reject(error);
      }
    });
  }
  
  /**
   * 生成监听器ID
   * @returns {string}
   */
  generateListenerId() {
    return `listener_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
  
  /**
   * 记录通信日志
   * @param {string} type - 日志类型
   * @param {string} message - 日志消息
   * @param {any} data - 附加数据
   */
  log(type, message, data = null) {
    const logEntry = {
      type,
      message,
      data,
      timestamp: new Date().toISOString()
    };
    
    this.communicationLog.push(logEntry);
    
    // 限制日志条数
    if (this.communicationLog.length > this.maxLogEntries) {
      this.communicationLog.shift();
    }
    
    // 调试模式下输出到控制台
    if (this.debugMode) {
      console.log(`[ModuleCommunicator] ${type}: ${message}`, data || '');
    }
  }
  
  /**
   * 获取通信日志
   * @param {number} limit - 限制条数
   * @returns {Array}
   */
  getLog(limit = 50) {
    return this.communicationLog.slice(-limit);
  }
  
  /**
   * 清除通信日志
   */
  clearLog() {
    this.communicationLog = [];
    this.log('system', '通信日志已清除');
  }
  
  /**
   * 设置调试模式
   * @param {boolean} enabled - 是否启用
   */
  setDebugMode(enabled) {
    this.debugMode = enabled;
    this.log('system', `调试模式${enabled ? '已启用' : '已禁用'}`);
  }
  
  /**
   * 获取通信统计信息
   * @returns {Object}
   */
  getStats() {
    return {
      moduleCount: this.modules.size,
      eventCount: this.eventListeners.size,
      logEntries: this.communicationLog.length,
      modules: this.getModuleNames(),
      events: Array.from(this.eventListeners.keys())
    };
  }
  
  /**
   * 销毁通信管理器
   */
  destroy() {
    this.clearListeners();
    this.modules.clear();
    this.clearLog();
    console.log('[ModuleCommunicator] 通信管理器已销毁');
  }
}

module.exports = ModuleCommunicator;