// 员工订单详情页面
const app = getApp();

Page({
  data: {
    appointmentId: '',
    appointmentDetail: null,
    staffInfo: null,
    isLoading: true,
    isAssignedStaff: false,
    isAdmin: false
  },

  onLoad: function(options) {
    // 检查员工登录状态
    const staffInfo = wx.getStorageSync('staffInfo');
    if (!staffInfo) {
      this.redirectToLogin();
      return;
    }

    // 获取预约ID
    if (options.id) {
      this.setData({
        appointmentId: options.id,
        staffInfo: staffInfo,
        isAdmin: wx.getStorageSync('isAdmin') || false
      });
      this.fetchAppointmentDetail();
    } else {
      wx.showToast({
        title: '预约ID不存在',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  // 获取预约详情
  fetchAppointmentDetail: function() {
    this.setData({ isLoading: true });
    
    console.log('开始获取预约详情，预约ID:', this.data.appointmentId);

    wx.cloud.callFunction({
      name: 'appointmentManager',
      data: {
        type: 'staff',
        action: 'getAppointmentDetail',
        appointmentId: this.data.appointmentId
      },
      success: res => {
        console.log('预约详情返回结果:', res);
        
        if (res.result && res.result.code === 0) {
          const appointmentDetail = res.result.data;
          console.log('获取到的预约详情:', appointmentDetail);
          
          // 格式化订单状态
          appointmentDetail.statusText = this.getOrderStatusText(appointmentDetail.status);
          
          // 脱敏处理手机号
          if (appointmentDetail.phoneNumber) {
            appointmentDetail.maskedPhone = this.maskPhoneNumber(appointmentDetail.phoneNumber);
          }
          
          // 判断是否为被指定的服务人员
          const isAssignedStaff = this.data.staffInfo && 
            (this.data.staffInfo._id === appointmentDetail.preferredStaffId || 
             this.data.staffInfo._id === appointmentDetail.verifyStaffId);
          
          this.setData({
            appointmentDetail,
            isLoading: false,
            isAssignedStaff
          });
        } else {
          console.error('获取预约详情失败:', res.result);
          this.setData({ isLoading: false });
          wx.showToast({
            title: res.result?.message || '获取预约详情失败',
            icon: 'none'
          });
          setTimeout(() => {
            wx.navigateBack();
          }, 1500);
        }
      },
      fail: err => {
        console.error('调用云函数获取预约详情失败', err);
        this.setData({ isLoading: false });
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      }
    });
  },

  // 获取订单状态文本
  getOrderStatusText: function(status) {
    const statusMap = {
      'pending': '待确认',
      'confirmed': '已确认',
      'completed': '已完成',
      'cancelled': '已取消'
    };
    return statusMap[status] || '未知状态';
  },
  
  // 手机号码脱敏处理
  maskPhoneNumber: function(phone) {
    if (!phone || phone.length < 7) return '***';
    return phone.substr(0, 3) + '****' + phone.substr(-4);
  },

  // 预览图片
  previewImage: function(e) {
    const index = e.currentTarget.dataset.index;
    wx.previewImage({
      current: this.data.appointmentDetail.imageUrls[index],
      urls: this.data.appointmentDetail.imageUrls
    });
  },

  // 返回上一页
  goBack: function() {
    wx.navigateBack();
  },

  // 重定向到登录页
  redirectToLogin: function() {
    wx.redirectTo({
      url: '/pages/staff/login/login'
    });
  }
}); 