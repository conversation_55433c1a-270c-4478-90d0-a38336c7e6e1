// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const _ = db.command

// 初始化集合函数
async function initCollections() {
  try {
    // 检查并创建users集合
    try {
      await db.createCollection('users')
      console.log('创建users集合成功')
    } catch (err) {
      // 如果集合已存在，会抛出错误，这是正常的
      console.log('users集合已存在或创建失败:', err.message)
    }
    
    return true
  } catch (err) {
    console.error('初始化集合失败:', err)
    return false
  }
}

// 云函数入口函数
exports.main = async (event, context) => {
  // 初始化集合
  await initCollections()
  
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID
  
  // 获取传入的用户信息
  const { userInfo } = event
  
  try {
    // 检查用户是否已存在
    const userCheck = await db.collection('users').where({
      _openid: openid
    }).get()
    
    // 如果用户不存在，创建新用户
    if (userCheck.data.length === 0) {
      // 创建新用户记录
      const result = await db.collection('users').add({
        data: {
          _openid: openid,
          userInfo: userInfo,
          history: [],
          createTime: db.serverDate(),
          updateTime: db.serverDate()
        }
      })
      
      return {
        success: true,
        message: '用户信息创建成功',
        result: result
      }
    } 
    // 如果用户已存在，更新用户信息
    else {
      const result = await db.collection('users').where({
        _openid: openid
      }).update({
        data: {
          userInfo: userInfo,
          updateTime: db.serverDate()
        }
      })
      
      return {
        success: true,
        message: '用户信息更新成功',
        result: result
      }
    }
  } catch (error) {
    console.error('保存用户信息失败', error)
    return {
      success: false,
      message: '保存用户信息失败',
      error: error
    }
  }
} 