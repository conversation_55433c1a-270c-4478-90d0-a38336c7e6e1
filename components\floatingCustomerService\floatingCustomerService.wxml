<!-- 可拖动的客服按钮组件 -->
<view class="floating-customer-service" wx:if="{{!isHidden}}" style="{{top && left ? 'top:' + top + 'px; left:' + left + 'px;' : ''}}" bindtouchstart="touchStart" bindtouchmove="touchMove" bindtouchend="touchEnd">
  <view class="service-wrapper">
    <view class="close-btn" catchtap="hideCustomerService">×</view>
    <button open-type="contact" show-message-card="true" send-message-title="{{title}}" send-message-path="{{path}}" send-message-img="{{imageUrl}}" class="customer-service-btn">
      <image class="customer-service-icon" src="{{iconUrl || '/images/客服图标.png'}}"></image>
    </button>
  </view>
</view> 