/* pages/my/points/points.wxss */

/* 页面容器 */
.points-container {
  min-height: 100vh;
  background-color: #f8f8f8;
  padding-bottom: 120rpx; /* 为底部返回按钮预留空间 */
  box-sizing: border-box;
  position: relative;
}

/* 顶部标题栏 */
.header {
  background-color: #fff;
  padding: 20rpx 30rpx;
  padding-top: calc(44px + 20rpx); /* 状态栏高度 + 额外padding */
  text-align: center;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
}

.header-title {
  font-size: 34rpx;
  font-weight: bold;
  color: #333;
  position: relative;
  display: inline-block;
}

.header-title::after {
  display: none; /* 移除顶部"我的积分"下方的绿色横杠 */
}

/* 标签栏 */
.tabs {
  display: flex;
  background-color: #fff;
  border-bottom: none; /* 移除底部边框 */
  position: fixed;
  top: calc(44px + 84rpx); /* 状态栏高度 + 标题栏高度 */
  left: 0;
  right: 0;
  z-index: 10;
  box-shadow: none; /* 移除阴影 */
  padding-bottom: 0;
}

.tab {
  flex: 1;
  text-align: center;
  padding: 30rpx 0;
  font-size: 28rpx;
  color: #666;
  position: relative;
  transition: all 0.3s;
}

.tab.active {
  color: #333;
  font-weight: bold;
}

.tab.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 6rpx;
  background: linear-gradient(to right, #07c160, #10ad7a);
  border-radius: 3rpx;
  transition: width 0.3s;
}

.tab:hover::after {
  width: 80rpx;
}

/* 内容区域 */
.content-area {
  margin-top: calc(44px + 84rpx + 80rpx); /* 状态栏高度 + 标题栏高度 + 标签栏高度，移除额外间距 */
  padding: 0 30rpx;
  position: relative;
}

.content-area::before {
  display: none; /* 移除顶部装饰线 */
}

/* 标签内容 */
.tab-content {
  display: none;
}

.tab-content.active {
  display: block;
  animation: fadeIn 0.5s ease;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20rpx); }
  to { opacity: 1; transform: translateY(0); }
}

/* 加载中 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.loading {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #333;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #999;
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;
  position: relative;
  background-color: #fff;
  border-radius: 0 0 20rpx 20rpx; /* 只保留底部圆角 */
  box-shadow: 0 15rpx 30rpx rgba(0, 0, 0, 0.1), 0 5rpx 15rpx rgba(0, 0, 0, 0.05);
  transform: translateY(0); /* 移除上移效果 */
  z-index: 5;
  margin-bottom: 40rpx;
  border: 1rpx solid rgba(0, 0, 0, 0.03);
  border-top: none; /* 移除顶部边框 */
}

.empty-container::before {
  content: '';
  position: absolute;
  width: 200rpx;
  height: 200rpx;
  border-radius: 50%;
  background: rgba(7, 193, 96, 0.03);
  z-index: -1;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
  background-color: #f9f9f9;
  padding: 20rpx 40rpx;
  border-radius: 40rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  position: relative;
}

.empty-text::before {
  content: '';
  position: absolute;
  top: 50%;
  left: -8rpx;
  transform: translateY(-50%);
  width: 8rpx;
  height: 30rpx;
  background: linear-gradient(to bottom, #07c160, #10ad7a);
  border-radius: 4rpx;
}

/* 积分信息 */
.points-info {
  background: linear-gradient(135deg, #fff, #f9f9f9);
  border-radius: 0 0 20rpx 20rpx; /* 只保留底部圆角 */
  padding: 50rpx 40rpx;
  box-shadow: 0 15rpx 30rpx rgba(0, 0, 0, 0.1), 0 5rpx 15rpx rgba(0, 0, 0, 0.05);
  margin-bottom: 40rpx;
  position: relative;
  overflow: hidden;
  transform: translateY(0); /* 移除上移效果 */
  z-index: 5;
  border: 1rpx solid rgba(0, 0, 0, 0.03);
  border-top: none; /* 移除顶部边框 */
}

.points-info::before {
  content: '';
  position: absolute;
  top: -30rpx;
  right: -30rpx;
  width: 200rpx;
  height: 200rpx;
  border-radius: 50%;
  background: rgba(7, 193, 96, 0.05);
  z-index: 1;
}

.points-info::after {
  content: '';
  position: absolute;
  bottom: -50rpx;
  left: -50rpx;
  width: 250rpx;
  height: 250rpx;
  border-radius: 50%;
  background: rgba(7, 193, 96, 0.03);
  z-index: 1;
}

.points-amount-container {
  text-align: center;
  margin-bottom: 50rpx;
  position: relative;
  z-index: 2;
  padding: 20rpx 0;
}

.points-amount-container::before {
  content: '';
  position: absolute;
  top: -10rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 150rpx;
  height: 150rpx;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(7, 193, 96, 0.08) 0%, rgba(7, 193, 96, 0) 70%);
  z-index: -1;
}

.points-label {
  font-size: 30rpx;
  color: #666;
  position: relative;
  display: inline-block;
}

.points-label::after {
  display: none; /* 移除"当前积分"下方的绿色横杠 */
}

.points-amount {
  margin-top: 30rpx;
  position: relative;
}

.points-amount::before,
.points-amount::after {
  content: '';
  position: absolute;
  width: 10rpx;
  height: 10rpx;
  border-radius: 50%;
  background-color: rgba(7, 193, 96, 0.3);
}

.points-amount::before {
  top: 20rpx;
  left: 30%;
}

.points-amount::after {
  bottom: 10rpx;
  right: 30%;
}

.points-value {
  font-size: 100rpx;
  font-weight: bold;
  color: #333;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
  background: linear-gradient(to right, #333, #666);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  position: relative;
  display: inline-block;
}

.points-value::after {
  content: '';
  position: absolute;
  bottom: -10rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 80rpx;
  height: 4rpx;
  background: linear-gradient(to right, transparent, rgba(7, 193, 96, 0.5), transparent);
  border-radius: 2rpx;
}

/* 积分详情 */
.points-detail {
  margin-bottom: 50rpx;
  position: relative;
  z-index: 2;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 12rpx;
  padding: 20rpx;
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.03);
  backdrop-filter: blur(5px);
}

.detail-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.detail-label {
  font-size: 28rpx;
  color: #666;
}

.detail-value {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
}

/* 积分状态提示 */
.points-status-container {
  margin-top: 15rpx;
}

.status-hint {
  font-size: 24rpx;
  color: #999;
}

/* 积分状态 */
.points-status {
  text-align: center;
  margin-bottom: 30rpx;
  position: relative;
  z-index: 2;
}

.status-text {
  font-size: 28rpx;
  font-weight: bold;
  padding: 10rpx 30rpx;
  border-radius: 40rpx;
  display: inline-block;
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.05);
}

.status-text.pending {
  color: #fff;
  background: linear-gradient(to right, #ff9500, #ffaa33);
}

.status-text.available {
  color: #fff;
  background: linear-gradient(to right, #07c160, #10ad7a);
}

.status-text.unavailable {
  color: #fff;
  background: linear-gradient(to right, #999, #aaa);
}

.status-tip {
  font-size: 24rpx;
  color: #999;
  margin-top: 15rpx;
  display: block;
}

/* 提现按钮 */
.withdrawal-button-container {
  margin: 40rpx 0 20rpx;
  position: relative;
  z-index: 2;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.withdrawal-button {
  background: linear-gradient(to right, #07c160, #10ad7a);
  color: #fff;
  font-size: 32rpx;
  padding: 20rpx 0;
  border-radius: 50rpx;
  width: 60%;
  box-shadow: 0 6rpx 15rpx rgba(0, 0, 0, 0.15);
  transition: transform 0.3s, box-shadow 0.3s;
  position: relative;
  overflow: hidden;
}

.withdrawal-button::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 50%;
  height: 100%;
  background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.2), transparent);
  transform: skewX(-25deg);
  animation: shine 3s infinite;
}

@keyframes shine {
  0% {
    left: -100%;
  }
  20%, 100% {
    left: 100%;
  }
}

.withdrawal-button[disabled] {
  background: linear-gradient(to right, #ccc, #ddd);
  color: #fff;
  box-shadow: none;
}

.withdrawal-button[disabled]::after {
  display: none;
}

/* 积分规则 */
.points-rules {
  margin-top: 50rpx;
  background-color: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 15rpx 30rpx rgba(0, 0, 0, 0.08), 0 5rpx 15rpx rgba(0, 0, 0, 0.03);
  position: relative;
  overflow: hidden;
  z-index: 4;
}

.points-rules::before {
  content: '';
  position: absolute;
  bottom: -20rpx;
  left: -20rpx;
  width: 150rpx;
  height: 150rpx;
  border-radius: 50%;
  background: rgba(7, 193, 96, 0.05);
  z-index: 1;
}

.rules-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 25rpx;
  position: relative;
  display: inline-block;
}

.rules-title::after {
  display: none; /* 移除"积分规则"下方的绿色横杠 */
}

.rules-content {
  font-size: 26rpx;
  color: #666;
  position: relative;
  z-index: 2;
}

.rules-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.8;
  text-align: center;
  padding: 20rpx;
  background-color: #f9f9f9;
  border-radius: 12rpx;
  position: relative;
  z-index: 2;
}

/* 记录列表 */
.records-list {
  background-color: #fff;
  border-radius: 0 0 20rpx 20rpx; /* 只保留底部圆角 */
  overflow-y: auto; /* 添加垂直滚动 */
  max-height: 70vh; /* 限制最大高度，使其可滚动 */
  box-shadow: 0 15rpx 30rpx rgba(0, 0, 0, 0.1), 0 5rpx 15rpx rgba(0, 0, 0, 0.05);
  transform: translateY(0); /* 移除上移效果 */
  z-index: 5;
  position: relative;
  border: 1rpx solid rgba(0, 0, 0, 0.03);
  border-top: none; /* 移除顶部边框 */
}

.record-item {
  display: flex;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: background-color 0.3s;
  position: relative;
}

.record-item:hover {
  background-color: #f9f9f9;
}

.record-item:last-child {
  border-bottom: none;
}

.record-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 6rpx;
  height: 60%;
  background: linear-gradient(to bottom, #07c160, #10ad7a);
  border-radius: 3rpx;
  opacity: 0;
  transition: opacity 0.3s;
}

.record-item:hover::before {
  opacity: 1;
}

.record-date {
  width: 140rpx;
  font-size: 24rpx;
  color: #999;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.record-time {
  margin-top: 10rpx;
}

.record-content {
  flex: 1;
  margin-left: 20rpx;
}

.record-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.record-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.record-info {
  display: flex;
  align-items: center;
}

.record-label {
  font-size: 24rpx;
  color: #999;
  margin-right: 10rpx;
}

.record-value {
  font-size: 28rpx;
  color: #07c160; /* 将积分值改为绿色 */
  font-weight: bold;
}

.record-status {
  font-size: 24rpx;
  padding: 6rpx 16rpx;
  border-radius: 8rpx;
}

.status-pending {
  color: #ff9500;
  background-color: rgba(255, 149, 0, 0.1);
}

.status-completed {
  color: #07c160;
  background-color: rgba(7, 193, 96, 0.1);
}

.status-rejected {
  color: #fff;
  background-color: #ff3b30;
}

.status-unknown {
  color: #999;
  background-color: rgba(153, 153, 153, 0.1);
}

.record-qrcode {
  margin-top: 20rpx;
}

/* 备注信息样式 */
.record-remark {
  margin-top: 15rpx;
  padding: 12rpx 15rpx;
  background-color: #f8f8f8;
  border-radius: 8rpx;
  border-left: 4rpx solid #07c160;
  box-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.05);
}

.remark-text {
  font-size: 24rpx;
  color: #333;
  line-height: 1.4;
  font-weight: 500;
}

.qrcode-label {
  font-size: 24rpx;
  color: #576b95;
  text-decoration: underline;
}

/* 提现表单弹窗 */
.withdrawal-form-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 100;
  display: flex;
  align-items: center;
  justify-content: center;
}

.withdrawal-form {
  width: 85%;
  max-width: 600rpx;
  background: linear-gradient(135deg, #fff, #f9f9f9);
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 15rpx 30rpx rgba(0, 0, 0, 0.15);
  position: relative;
}

.withdrawal-form::before {
  content: '';
  position: absolute;
  top: -30rpx;
  right: -30rpx;
  width: 150rpx;
  height: 150rpx;
  border-radius: 50%;
  background: rgba(7, 193, 96, 0.05);
  z-index: 1;
}

.withdrawal-form::after {
  content: '';
  position: absolute;
  bottom: -50rpx;
  left: -50rpx;
  width: 200rpx;
  height: 200rpx;
  border-radius: 50%;
  background: rgba(7, 193, 96, 0.03);
  z-index: 1;
}

.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
  position: relative;
  z-index: 2;
  background-color: rgba(255, 255, 255, 0.8);
}

.header-spacer {
  width: 60rpx; /* 与关闭按钮宽度相同 */
}

.form-title {
  font-size: 34rpx;
  font-weight: bold;
  color: #333;
  position: relative;
  display: inline-block;
  flex: 1;
  text-align: center;
}

.form-close {
  font-size: 40rpx;
  color: #999;
  line-height: 1;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.3s;
}

.form-close:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.form-content {
  padding: 30rpx;
  position: relative;
  z-index: 2;
}

.form-info {
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.03);
}

.form-item {
  margin-bottom: 30rpx;
}

.form-item:last-child {
  margin-bottom: 0;
}

.form-label {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 10rpx;
  display: block;
}

.form-value {
  font-size: 34rpx;
  color: #333;
  font-weight: bold;
}

.amount-value {
  color: #07c160;
  background: linear-gradient(to right, #07c160, #10ad7a);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-size: 36rpx;
}

.form-qrcode-section {
  text-align: center;
  margin: 30rpx 0;
  padding: 20rpx;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 12rpx;
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.03);
  position: relative;
}

.qrcode-label {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
  display: block;
  text-decoration: none;
}

.upload-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.qrcode-preview {
  width: 240rpx;
  height: 240rpx;
  background-color: #f8f8f8;
  margin-bottom: 20rpx;
  border-radius: 8rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}

.upload-button {
  display: inline-block;
  padding: 16rpx 40rpx;
  background: linear-gradient(to right, #07c160, #10ad7a);
  border-radius: 50rpx;
  font-size: 28rpx;
  color: #fff;
  box-shadow: 0 4rpx 10rpx rgba(7, 193, 96, 0.2);
  transition: transform 0.3s, box-shadow 0.3s;
}

.upload-button:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 5rpx rgba(7, 193, 96, 0.2);
}

.form-textarea-container {
  margin-top: 30rpx;
  position: relative;
}

.form-textarea {
  width: 100%;
  height: 160rpx;
  background-color: #f8f8f8;
  border-radius: 12rpx;
  padding: 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
  border: 1rpx solid rgba(0, 0, 0, 0.05);
  color: #333;
  line-height: 1.4;
}

.form-textarea::placeholder {
  color: #999;
  text-align: center;
  position: absolute;
  top: 30%;
  left: 50%;
  transform: translateX(-50%);
  width: 90%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.optional-text {
  font-size: 24rpx;
  color: #999;
  text-align: center;
  margin-top: 10rpx;
}

.form-footer {
  padding: 0 30rpx 30rpx;
  position: relative;
  z-index: 2;
  display: flex;
  justify-content: center;
}

.submit-button {
  background: linear-gradient(to right, #07c160, #10ad7a);
  color: #fff;
  font-size: 32rpx;
  padding: 20rpx 0;
  border-radius: 50rpx;
  width: 55%;
  box-shadow: 0 6rpx 15rpx rgba(7, 193, 96, 0.2);
  transition: transform 0.3s, box-shadow 0.3s;
  position: relative;
  overflow: hidden;
}

.submit-button::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 50%;
  height: 100%;
  background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.2), transparent);
  transform: skewX(-25deg);
  animation: shine 3s infinite;
}

.submit-button[disabled] {
  background: linear-gradient(to right, #ccc, #ddd);
  color: #fff;
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.1);
}

.submit-button[disabled]::after {
  display: none;
}

/* 收款码预览 */
.qrcode-preview-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 110;
  display: flex;
  align-items: center;
  justify-content: center;
}

.qrcode-preview-container {
  width: 80%;
  max-width: 600rpx;
  background: linear-gradient(135deg, #fff, #f9f9f9);
  border-radius: 20rpx;
  overflow: hidden;
  padding: 40rpx;
  box-shadow: 0 15rpx 30rpx rgba(0, 0, 0, 0.15);
  position: relative;
}

.qrcode-preview-container::before {
  content: '';
  position: absolute;
  top: -30rpx;
  right: -30rpx;
  width: 150rpx;
  height: 150rpx;
  border-radius: 50%;
  background: rgba(7, 193, 96, 0.05);
  z-index: 1;
}

.qrcode-preview-image {
  width: 100%;
  height: 600rpx;
  border-radius: 12rpx;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 2;
}

.qrcode-preview-close {
  margin-top: 30rpx;
  text-align: center;
  font-size: 28rpx;
  color: #fff;
  padding: 16rpx 0;
  background: linear-gradient(to right, #07c160, #10ad7a);
  border-radius: 50rpx;
  box-shadow: 0 6rpx 15rpx rgba(7, 193, 96, 0.2);
  transition: transform 0.3s, box-shadow 0.3s;
  position: relative;
  z-index: 2;
  overflow: hidden;
}

.qrcode-preview-close::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 50%;
  height: 100%;
  background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.2), transparent);
  transform: skewX(-25deg);
  animation: shine 3s infinite;
}

.qrcode-preview-close:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 8rpx rgba(7, 193, 96, 0.2);
}

/* 底部返回按钮 */
.back-button-container {
  position: fixed;
  bottom: 80rpx;
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  z-index: 50;
}

.back-button {
  background: linear-gradient(to right, #333, #555);
  color: #fff;
  font-size: 30rpx;
  padding: 20rpx 80rpx;
  border-radius: 40rpx;
  box-shadow: 0 6rpx 15rpx rgba(0, 0, 0, 0.15);
  transition: transform 0.3s, box-shadow 0.3s;
  position: relative;
  overflow: hidden;
}

.back-button::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 50%;
  height: 100%;
  background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.2), transparent);
  transform: skewX(-25deg);
  animation: shine 3s infinite;
}

.back-button:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.15);
} 