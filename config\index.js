/**
 * 小程序配置文件
 * 使用时请修改以下配置项
 * 
 * 【配置项说明】
 * 1. 云开发环境ID(ENV_ID)：填写您的云开发环境ID，在微信云开发控制台可以找到
 * 2. 小程序ID(APP_ID)：填写您的小程序AppID，在微信公众平台可以找到
 * 3. 云存储配置(CLOUD_STORAGE_CONFIG)：配置腾讯云COS存储路径和目录结构
 *   - baseUrl: COS存储桶访问域名，格式为"https://存储桶名称.cos.地域.myqcloud.com/"
 *   - dirs: 各种内容的存储目录名称
 */

// ================== 基础配置 (必须修改) ==================
// 【必填】云开发环境ID - 从微信云开发控制台获取
const ENV_ID = 'cloud1-3gn5xcxz610ebe61';

// 【必填】小程序ID - 从微信公众平台获取
const APP_ID = 'wx03b63fba056c0b08';

// 【必填】云存储配置
const CLOUD_STORAGE_CONFIG = {
  // 【必填】COS存储桶访问域名 - 格式: https://存储桶名称.cos.地域.myqcloud.com/
  baseUrl: 'https://naildidi-1360370592.cos.ap-guangzhou.myqcloud.com/',
  
  // 临时链接有效期（秒）- 一般无需修改
  tempUrlExpire: 3600,
  
  // 【必填】文件夹路径 - 必须与云存储中创建的文件夹名称一致
  dirs: {
    launch: '启动页',          // 启动页图片文件夹
    video: '视频列表',          // 视频文件文件夹
    cover: '视频列表封面图',     // 视频封面图文件夹
    article: '视频列表详情'      // 视频文章内容文件夹
  }
};

// ================== 文件命名规范 ==================
// 以下是各类文件的命名规范，上传文件时请严格按照此规范命名
const FILE_NAMING = {
  // 启动页图片命名规范
  launch: {
    // 图片名称（不含扩展名）- 必须使用这两个名称
    first: 'launch1',    // 第一张启动图
    second: 'launch2',   // 第二张启动图
    // 支持的图片格式（按优先级排序）
    formats: ['webp', 'jpg', 'jpeg', 'png']
  },
  
  // 视频文件命名规范 - 格式: 序号_标题_副标题_播放量_video.mp4
  // 示例: 001_冥想音乐_轻音乐_2000_video.mp4
  video: {
    pattern: '{序号}_{标题}_{副标题}_{播放量}_video.mp4',
    example: '001_冥想音乐_轻音乐_2000_video.mp4'
  },
  
  // 视频封面图命名规范 - 必须与对应视频文件的前缀保持一致
  // 示例: 001_冥想音乐_轻音乐_2000_cover.jpg
  cover: {
    pattern: '{序号}_{标题}_{副标题}_{播放量}_cover.jpg',
    example: '001_冥想音乐_轻音乐_2000_cover.jpg'
  },
  
  // 视频详情HTML文件命名规范 - 使用视频序号命名
  // 示例: 001.html
  article: {
    pattern: '{序号}.html',
    example: '001.html'
  }
};

// ================== 启动页配置 (可选修改) ==================
const LAUNCH_CONFIG = {
  // 启动页图片配置
  images: {
    // 图片名称（不含扩展名）
    first: FILE_NAMING.launch.first,
    second: FILE_NAMING.launch.second,
    // 支持的图片格式（按优先级排序）
    formats: FILE_NAMING.launch.formats
  },
  // 动画时间配置（毫秒）
  animation: {
    displayDuration: 1500,     // 第一张图片显示时长(1.5秒)
    transitionDuration: 2300,  // 过渡动画时长(2.3秒)
    autoJumpDelay: 3000,       // 自动跳转延时(3秒)
    fadeOutDuration: 800,      // 淡出动画持续时间
    imageTransitionDelay: 700, // 图片切换延迟(0.7秒)
    firstImageDuration: 1500,  // 第一张图片显示持续时间(1.5秒)
    secondImageDelay: 700,     // 第二张图片开始显示的延迟时间(0.7秒)
    totalDuration: 3000        // 启动页最短显示时间(3秒)
  }
};

// ================== 网络请求配置 (一般无需修改) ==================
const REQUEST_CONFIG = {
  timeout: 10000,
  header: {
    'content-type': 'application/json'
  },
  // 缓存配置
  cache: {
    enable: true,              // 是否启用缓存
    maxAge: 60 * 60 * 1000,    // 缓存时间（毫秒）
    maxSize: 50                // 最大缓存条数
  }
};

module.exports = {
  ENV_ID,
  APP_ID,
  CLOUD_STORAGE_CONFIG,
  LAUNCH_CONFIG,
  REQUEST_CONFIG,
  FILE_NAMING
};

// ================== 在此线以下添加新功能配置 ==================