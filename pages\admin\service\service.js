// pages/admin/service/service.js
const app = getApp()

Page({

  /**
   * 页面的初始数据
   */
  data: {
    isLoading: true,
    videoList: [],
    currentPage: 1,
    pageSize: 10,
    hasMore: true,
    uploadModalVisible: false,
    currentVideoInfo: null,
    loadingMore: false // 添加加载更多状态标志
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 检查管理员登录状态
    if (!app.globalData.isAdmin) {
      this.redirectToLogin()
      return
    }
    
    // 先执行数据迁移确保所有视频有排序字段
    this.migrateVideoData()
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 每次显示页面时检查管理员状态
    if (!app.globalData.isAdmin) {
      this.redirectToLogin()
      return
    }
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.refreshList()
    wx.stopPullDownRefresh()
  },

  /**
   * 页面上拉触底事件的处理函数 - 现在由scroll-view的bindscrolltolower触发
   */
  onReachBottom() {
    if (this.data.hasMore && !this.data.loadingMore) {
      this.setData({
        currentPage: this.data.currentPage + 1,
        loadingMore: true // 设置加载更多状态
      })
      this.loadVideoList(false, true) // 传递loadingMore参数
    }
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },

  // 重定向到登录页
  redirectToLogin() {
    wx.showModal({
      title: '温馨提示',
      content: '请先登录管理员账号~',
      confirmText: '我知道了',
      showCancel: false,
      success: () => {
        wx.navigateBack()
      }
    })
  },
  
  // 返回上一页
  navigateBack() {
    wx.navigateBack()
  },
  
  // 加载视频列表
  loadVideoList(refresh = false, loadingMore = false) {
    if (refresh) {
      this.setData({
        currentPage: 1,
        hasMore: true,
        videoList: []
      })
    }
    
    if (!this.data.hasMore && !refresh) {
      return
    }
    
    // 仅当不是加载更多时，才显示全屏加载状态
    if (!loadingMore) {
      this.setData({ isLoading: true })
    }
    
    // 调用云函数获取视频列表 - 使用新的API
    wx.cloud.callFunction({
      name: 'videoManager',
      data: {
        type: 'admin', // 使用管理员API
        action: 'getAllVideos', // 获取所有视频，包括不可见的
        data: {
          page: this.data.currentPage,
          pageSize: this.data.pageSize
        }
      },
      success: res => {
        console.log('获取视频列表成功', res)
        
        if (res.result && res.result.code === 200) {
          const { videos, total, totalPages } = res.result.data
          
          // 处理数据，确保兼容性
          const processedList = videos.map(item => {
            return {
              ...item,
              id: item._id, // 确保id字段存在
              title: item.mainTitle, // 兼容旧字段名
              subtitle: item.subTitle, // 兼容旧字段名
              order: item.sortOrder, // 兼容旧字段名
              coverUrl: item.coverUrl || '', // 确保coverUrl存在
              videoUrl: item.videoUrl || '', // 确保videoUrl存在
              visible: item.isVisible // 可见性状态
            }
          })
          
          // 确保视频列表按照sortOrder排序
          let allVideos = refresh ? processedList : [...this.data.videoList, ...processedList]
          
          // 严格按照sortOrder字段排序 (数字越小越靠前)
          allVideos.sort((a, b) => {
            // 确保获取到数值，即使字段不存在也有默认值
            const aOrder = typeof a.sortOrder === 'number' ? a.sortOrder : 
                         (typeof a.order === 'number' ? a.order : Number.MAX_SAFE_INTEGER);
            const bOrder = typeof b.sortOrder === 'number' ? b.sortOrder : 
                         (typeof b.order === 'number' ? b.order : Number.MAX_SAFE_INTEGER);
            
            // 升序排列: 数字小的排在前面
            return aOrder - bOrder;
          })
          
          this.setData({
            videoList: allVideos,
            isLoading: false,
            loadingMore: false, // 重置加载更多状态
            hasMore: this.data.currentPage < totalPages
          })
        } else {
          this.setData({ 
            isLoading: false,
            loadingMore: false // 重置加载更多状态
          })
          
          wx.showToast({
            title: res.result?.message || '获取视频列表失败',
            icon: 'none'
          })
        }
      },
      fail: err => {
        console.error('获取视频列表失败', err)
        this.setData({ 
          isLoading: false,
          loadingMore: false // 重置加载更多状态
        })
        
        wx.showToast({
          title: '获取视频列表失败，请重试',
          icon: 'none'
        })
      }
    })
  },
  
  // 刷新列表
  refreshList() {
    // 保存当前滚动位置
    this.loadVideoList(true)
  },
  
  // 查看视频详情
  viewVideoDetail(e) {
    const { id } = e.currentTarget.dataset
    const videoInfo = this.data.videoList.find(item => item._id === id)
    
    if (!videoInfo) {
      wx.showToast({
        title: '未找到视频信息',
        icon: 'none'
      })
      return
    }
    
    // 使用视频详情模态框组件显示详情
    const videoDetailModal = this.selectComponent('#videoDetailModal')
    if (videoDetailModal) {
      videoDetailModal.showModal(videoInfo)
    }
  },
  
  // 编辑视频
  editVideo(e) {
    // 确保e存在并且有dataset
    const id = e && e.currentTarget ? e.currentTarget.dataset.id : '';
    
    if (!id) {
      wx.showToast({
        title: '无效的视频ID',
        icon: 'none'
      });
      return;
    }
    
    const videoInfo = this.data.videoList.find(item => item._id === id)
    
    if (!videoInfo) {
      wx.showToast({
        title: '未找到视频信息',
        icon: 'none'
      })
      return
    }
    
    // 显示编辑模态框
    this.setData({
      currentVideoInfo: videoInfo,
      uploadModalVisible: true
    })
  },
  
  // 删除视频
  deleteVideo(e) {
    // 确保e存在并且有dataset
    const id = e && e.currentTarget ? e.currentTarget.dataset.id : '';
    
    if (!id) {
      wx.showToast({
        title: '无效的视频ID',
        icon: 'none'
      });
      return;
    }
    
    // 获取完整的视频信息
    const videoInfo = this.data.videoList.find(item => item._id === id || item.id === id);
    
    if (!videoInfo) {
      console.error('未找到视频信息:', { id, videoList: this.data.videoList });
      wx.showToast({
        title: '未找到视频信息',
        icon: 'none'
      });
      return;
    }
    
    // 确认删除
    wx.showModal({
      title: '确认删除',
      content: `确定要删除视频"${videoInfo.title || videoInfo.mainTitle}"吗？此操作不可恢复。`,
      confirmText: '删除',
      confirmColor: '#FF4D4F',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          // 显示加载提示
          wx.showLoading({
            title: '正在删除...',
            mask: true
          });
          
          // 调用云函数删除视频
          wx.cloud.callFunction({
            name: 'videoManager',
            data: {
              type: 'admin',
              action: 'deleteVideo',
              data: {
                id: id
              }
            },
            success: (res) => {
              wx.hideLoading();
              
              if (res.result && res.result.code === 200) {
                wx.showToast({
                  title: '删除成功',
                  icon: 'success'
                });
                
                // 刷新列表
                this.refreshList();
              } else {
                wx.showToast({
                  title: res.result?.message || '删除失败',
                  icon: 'none'
                });
              }
            },
            fail: (err) => {
              console.error('删除视频失败', err);
              wx.hideLoading();
              
              wx.showToast({
                title: '删除失败，请重试',
                icon: 'none'
              });
            }
          });
        }
      }
    });
  },
  
  // 切换视频的可见性
  toggleVideoVisibility(e) {
    const { id, visible } = e.currentTarget.dataset
    const actionText = visible ? '隐藏' : '显示'
    
    wx.showModal({
      title: `确认${actionText}视频`,
      content: `确定要${actionText}这个视频吗？`,
      success: res => {
        if (res.confirm) {
          wx.showLoading({
            title: `正在${actionText}视频...`,
            mask: true
          })
          
          wx.cloud.callFunction({
            name: 'videoManager',
            data: {
              type: 'admin',
              action: 'toggleVideoVisibility',
              data: {
                videoId: id,
                visible: !visible, // 切换可见性
                updateTime: new Date() // 添加更新时间，便于前端识别新视频
              }
            },
            success: res => {
              wx.hideLoading()
              
              if (res.result && res.result.code === 200) {
                wx.showToast({
                  title: `${actionText}成功`,
                  icon: 'success'
                })
                
                // 无论是隐藏还是显示，都使用常规刷新方式
                // 前端会在下拉刷新时自动将新显示的视频排在前面
                this.refreshList()
              } else {
                wx.showToast({
                  title: res.result?.message || `${actionText}失败`,
                  icon: 'none'
                })
              }
            },
            fail: err => {
              console.error(`${actionText}视频失败`, err)
              wx.hideLoading()
              
              wx.showToast({
                title: `${actionText}失败，请重试`,
                icon: 'none'
              })
            }
          })
        }
      }
    })
  },
  
  // 添加新视频
  addNewVideo() {
    this.setData({
      currentVideoInfo: null,
      uploadModalVisible: true
    })
  },
  
  // 关闭上传模态框
  closeUploadModal() {
    this.setData({
      uploadModalVisible: false
    })
  },
  
  // 上传成功回调
  onUploadSuccess(e) {
    console.log('上传成功', e.detail)
    
    // 刷新列表
    this.refreshList()
  },
  
  // 迁移视频数据，确保所有视频有排序字段
  migrateVideoData() {
    wx.showLoading({
      title: '正在检查数据...',
      mask: true
    })
    
    wx.cloud.callFunction({
      name: 'videoManager',
      data: {
        type: 'admin',
        action: 'migrateVideoData'
      },
      success: res => {
        console.log('视频数据迁移结果:', res)
        wx.hideLoading()
        
        if (res.result && res.result.code === 200) {
          const { totalVideos, updatedVideos } = res.result.data || {}
          
          if (updatedVideos > 0) {
            wx.showToast({
              title: `成功更新${updatedVideos}个视频排序`,
              icon: 'none',
              duration: 2000
            })
          }
          
          // 加载视频列表
          this.loadVideoList()
        } else {
          wx.showToast({
            title: res.result?.message || '数据检查失败',
            icon: 'none'
          })
          
          // 即使迁移失败也尝试加载列表
          this.loadVideoList()
        }
      },
      fail: err => {
        console.error('视频数据迁移失败:', err)
        wx.hideLoading()
        
        wx.showToast({
          title: '数据检查失败，继续加载',
          icon: 'none'
        })
        
        // 即使迁移失败也尝试加载列表
        this.loadVideoList()
      }
    })
  }
})