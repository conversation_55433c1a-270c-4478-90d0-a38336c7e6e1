/**
 * 统一错误处理器单元测试
 * 测试错误收集、分析、处理和恢复机制
 */

const UnifiedErrorHandler = require('../modules/unified-error-handler');
const { createMockPageContext, waitFor, createTestError } = require('./test-utils');

// Mock wx API
global.wx = {
  showToast: jest.fn(),
  hideToast: jest.fn(),
  showLoading: jest.fn(),
  hideLoading: jest.fn(),
  getSystemInfoSync: jest.fn(() => ({
    platform: 'devtools',
    version: '1.0.0'
  })),
  getStorageSync: jest.fn(),
  setStorageSync: jest.fn(),
  onNetworkStatusChange: jest.fn(),
  onUnhandledRejection: jest.fn(),
  onError: jest.fn(),
  getNetworkType: jest.fn((options) => {
    options.success({ networkType: 'wifi' });
  })
};

// Mock getCurrentPages
global.getCurrentPages = jest.fn(() => [
  { route: 'pages/index/index' }
]);

// Mock getApp
global.getApp = jest.fn(() => ({
  globalData: { version: '1.0.0' }
}));

describe('UnifiedErrorHandler 统一错误处理器', () => {
  let errorHandler;
  let mockPageContext;

  beforeEach(() => {
    jest.clearAllMocks();
    mockPageContext = createMockPageContext();
    errorHandler = new UnifiedErrorHandler(mockPageContext);
  });

  afterEach(() => {
    if (errorHandler) {
      errorHandler.destroy();
    }
  });

  describe('初始化和配置', () => {
    test('应该成功初始化错误处理器', () => {
      errorHandler.init();
      
      expect(errorHandler.initialized).toBe(true);
      expect(errorHandler.errorStrategies.size).toBeGreaterThan(0);
      expect(errorHandler.recoveryMechanisms.size).toBeGreaterThan(0);
    });

    test('应该正确设置错误处理策略', () => {
      errorHandler.setupErrorStrategies();
      
      // 验证关键错误类型的策略
      expect(errorHandler.errorStrategies.has('NETWORK_ERROR')).toBe(true);
      expect(errorHandler.errorStrategies.has('MODULE_ERROR')).toBe(true);
      expect(errorHandler.errorStrategies.has('VIDEO_PLAY_ERROR')).toBe(true);
      
      // 验证策略配置结构
      const networkStrategy = errorHandler.errorStrategies.get('NETWORK_ERROR');
      expect(networkStrategy).toHaveProperty('priority');
      expect(networkStrategy).toHaveProperty('autoRetry');
      expect(networkStrategy).toHaveProperty('maxRetries');
      expect(networkStrategy).toHaveProperty('recovery');
    });

    test('应该正确设置错误恢复机制', () => {
      errorHandler.setupRecoveryMechanisms();
      
      // 验证恢复机制
      expect(errorHandler.recoveryMechanisms.has('networkRecovery')).toBe(true);
      expect(errorHandler.recoveryMechanisms.has('dataRecovery')).toBe(true);
      expect(errorHandler.recoveryMechanisms.has('moduleRecovery')).toBe(true);
      
      // 验证恢复机制是函数
      const networkRecovery = errorHandler.recoveryMechanisms.get('networkRecovery');
      expect(typeof networkRecovery).toBe('function');
    });

    test('应该设置全局错误监听', () => {
      errorHandler.setupGlobalErrorListeners();
      
      // 验证wx API被调用
      expect(wx.onNetworkStatusChange).toHaveBeenCalled();
    });
  });

  describe('错误处理', () => {
    beforeEach(() => {
      errorHandler.init();
    });

    test('应该正确处理网络错误', async () => {
      const networkError = createTestError('网络连接失败', 'NETWORK_ERROR');
      
      const result = await errorHandler.handleError(networkError, {
        source: 'network_request'
      });
      
      expect(result.success).toBeDefined();
      expect(result.errorType).toBe('NETWORK_ERROR');
      expect(errorHandler.errorStats.totalErrors).toBe(1);
    });

    test('应该正确处理模块错误', async () => {
      const moduleError = createTestError('模块初始化失败', 'MODULE_ERROR');
      
      const result = await errorHandler.handleError(moduleError, {
        source: 'module',
        moduleName: 'VideoList'
      });
      
      expect(result.errorType).toBe('MODULE_ERROR');
      expect(errorHandler.errorStats.errorsByType.get('MODULE_ERROR')).toBe(1);
    });

    test('应该正确处理视频播放错误', async () => {
      const videoError = createTestError('视频播放失败', 'VIDEO_PLAY_ERROR');
      
      const result = await errorHandler.handleError(videoError, {
        source: 'video_player',
        videoId: 'test_video'
      });
      
      expect(result.errorType).toBe('VIDEO_PLAY_ERROR');
      expect(wx.showToast).toHaveBeenCalled();
    });

    test('应该正确分析错误类型', () => {
      // 测试网络错误识别
      const networkError = new Error('network timeout');
      const networkType = errorHandler.analyzeErrorType(networkError, {});
      expect(networkType).toBe('NETWORK_ERROR');
      
      // 测试数据错误识别
      const dataError = new Error('JSON parse error');
      const dataType = errorHandler.analyzeErrorType(dataError, {});
      expect(dataType).toBe('DATA_ERROR');
      
      // 测试视频错误识别
      const videoError = new Error('video play failed');
      const videoType = errorHandler.analyzeErrorType(videoError, {});
      expect(videoType).toBe('VIDEO_PLAY_ERROR');
    });
  });

  describe('错误收集和记录', () => {
    beforeEach(() => {
      errorHandler.init();
    });

    test('应该正确收集错误信息', () => {
      const testError = createTestError('测试错误');
      const context = { source: 'test', userId: 'test_user' };
      
      const errorInfo = errorHandler.collectErrorInfo(testError, context);
      
      expect(errorInfo).toHaveProperty('id');
      expect(errorInfo).toHaveProperty('message', '测试错误');
      expect(errorInfo).toHaveProperty('timestamp');
      expect(errorInfo).toHaveProperty('context');
      expect(errorInfo).toHaveProperty('severity');
    });

    test('应该正确记录错误', () => {
      const errorInfo = {
        id: 'test_error_1',
        message: '测试错误',
        type: 'TEST_ERROR',
        timestamp: Date.now()
      };
      
      errorHandler.recordError(errorInfo);
      
      expect(errorHandler.errorCollector.errors).toContain(errorInfo);
      expect(errorHandler.errorCollector.errorCounts.get('TEST_ERROR_测试错误')).toBe(1);
    });

    test('应该限制错误记录数量', () => {
      // 设置较小的最大错误数量
      errorHandler.errorCollector.maxErrors = 5;
      
      // 添加超过限制的错误
      for (let i = 0; i < 10; i++) {
        errorHandler.recordError({
          id: `error_${i}`,
          message: `错误 ${i}`,
          timestamp: Date.now()
        });
      }
      
      expect(errorHandler.errorCollector.errors.length).toBe(5);
    });

    test('应该生成唯一的错误ID', () => {
      const id1 = errorHandler.generateErrorId();
      const id2 = errorHandler.generateErrorId();
      
      expect(id1).not.toBe(id2);
      expect(id1).toMatch(/^error_\d+_[a-z0-9]+$/);
    });
  });

  describe('错误恢复机制', () => {
    beforeEach(() => {
      errorHandler.init();
    });

    test('应该执行网络错误恢复', async () => {
      const networkError = createTestError('网络错误');
      const context = { source: 'network' };
      
      const recoveryMechanism = errorHandler.recoveryMechanisms.get('networkRecovery');
      const result = await recoveryMechanism(networkError, context);
      
      expect(result).toHaveProperty('success');
    });

    test('应该执行数据错误恢复', async () => {
      const dataError = createTestError('数据错误');
      const context = { source: 'data', cacheKey: 'test_cache' };
      
      const recoveryMechanism = errorHandler.recoveryMechanisms.get('dataRecovery');
      const result = await recoveryMechanism(dataError, context);
      
      expect(result).toHaveProperty('success');
    });

    test('应该执行模块错误恢复', async () => {
      const moduleError = createTestError('模块错误');
      const context = { source: 'module', moduleName: 'TestModule' };
      
      const recoveryMechanism = errorHandler.recoveryMechanisms.get('moduleRecovery');
      const result = await recoveryMechanism(moduleError, context);
      
      expect(result).toHaveProperty('success');
    });

    test('应该检查网络状态', async () => {
      const networkStatus = await errorHandler.checkNetworkStatus();
      
      expect(networkStatus).toHaveProperty('isConnected');
      expect(networkStatus).toHaveProperty('networkType');
    });
  });

  describe('错误分析和模式识别', () => {
    beforeEach(() => {
      errorHandler.init();
    });

    test('应该分析错误模式', () => {
      const errorInfo = {
        type: 'NETWORK_ERROR',
        context: { source: 'api_request' },
        timestamp: Date.now()
      };
      
      errorHandler.analyzeErrorPattern(errorInfo);
      
      const patternKey = 'NETWORK_ERROR_api_request';
      expect(errorHandler.errorCollector.errorPatterns.has(patternKey)).toBe(true);
    });

    test('应该检测高频错误', () => {
      const errorInfo = {
        type: 'TEST_ERROR',
        context: { source: 'test' },
        timestamp: Date.now()
      };
      
      // 模拟高频错误
      for (let i = 0; i < 10; i++) {
        errorHandler.analyzeErrorPattern({
          ...errorInfo,
          timestamp: Date.now() + i * 1000
        });
      }
      
      // 验证高频错误处理被触发
      // 这里需要根据实际实现来验证
    });

    test('应该执行错误分析', () => {
      // 添加一些错误数据
      errorHandler.errorStats.totalErrors = 10;
      errorHandler.errorStats.recoveredErrors = 7;
      errorHandler.errorStats.errorsByType.set('NETWORK_ERROR', 5);
      errorHandler.errorStats.errorsByType.set('MODULE_ERROR', 3);
      
      errorHandler.performErrorAnalysis();
      
      // 验证分析逻辑不抛出错误
      expect(errorHandler.errorStats.totalErrors).toBe(10);
    });

    test('应该生成优化建议', () => {
      // 设置错误统计数据
      errorHandler.errorStats.totalErrors = 20;
      errorHandler.errorStats.recoveredErrors = 10;
      errorHandler.errorStats.unrecoveredErrors = 10;
      errorHandler.errorStats.errorsByType.set('NETWORK_ERROR', 15);
      errorHandler.errorStats.errorsByType.set('MODULE_ERROR', 5);
      
      const recommendations = errorHandler.getPerformanceRecommendations();
      
      expect(Array.isArray(recommendations)).toBe(true);
      expect(recommendations.length).toBeGreaterThan(0);
      
      // 验证建议结构
      if (recommendations.length > 0) {
        const firstRec = recommendations[0];
        expect(firstRec).toHaveProperty('type');
        expect(firstRec).toHaveProperty('message');
        expect(firstRec).toHaveProperty('priority');
      }
    });
  });

  describe('用户通知管理', () => {
    beforeEach(() => {
      errorHandler.init();
    });

    test('应该检查是否需要通知用户', () => {
      // 第一次错误应该通知
      const shouldNotify1 = errorHandler.shouldNotifyUser('NETWORK_ERROR');
      expect(shouldNotify1).toBe(true);
      
      // 设置最近通知时间
      errorHandler.userNotifications.lastNotificationTime = Date.now();
      
      // 冷却期内不应该通知
      const shouldNotify2 = errorHandler.shouldNotifyUser('NETWORK_ERROR');
      expect(shouldNotify2).toBe(false);
    });

    test('应该发送用户通知', () => {
      const testError = createTestError('用户通知测试');
      
      errorHandler.notifyUser(testError, 'NETWORK_ERROR');
      
      expect(wx.showToast).toHaveBeenCalled();
      expect(errorHandler.userNotifications.lastNotificationTime).toBeGreaterThan(0);
    });

    test('应该抑制频繁错误通知', () => {
      // 模拟频繁错误
      for (let i = 0; i < 10; i++) {
        errorHandler.errorCollector.errorCounts.set('FREQUENT_ERROR', i + 1);
      }
      
      const shouldNotify = errorHandler.shouldNotifyUser('FREQUENT_ERROR');
      expect(shouldNotify).toBe(false);
    });
  });

  describe('性能监控', () => {
    beforeEach(() => {
      errorHandler.init();
    });

    test('应该启动错误监控', () => {
      errorHandler.startErrorMonitoring();
      
      expect(errorHandler.monitoringTimer).toBeDefined();
      expect(errorHandler.cleanupTimer).toBeDefined();
    });

    test('应该清理过期错误', () => {
      // 添加过期错误
      const expiredError = {
        id: 'expired_error',
        timestamp: Date.now() - 25 * 60 * 60 * 1000 // 25小时前
      };
      const recentError = {
        id: 'recent_error',
        timestamp: Date.now() - 1 * 60 * 60 * 1000 // 1小时前
      };
      
      errorHandler.errorCollector.errors.push(expiredError, recentError);
      
      errorHandler.cleanupExpiredErrors();
      
      expect(errorHandler.errorCollector.errors).toContain(recentError);
      expect(errorHandler.errorCollector.errors).not.toContain(expiredError);
    });

    test('应该获取错误统计', () => {
      // 设置一些统计数据
      errorHandler.errorStats.totalErrors = 5;
      errorHandler.errorStats.recoveredErrors = 3;
      errorHandler.errorStats.errorsByType.set('TEST_ERROR', 2);
      
      const stats = errorHandler.getErrorStats();
      
      expect(stats).toHaveProperty('totalErrors', 5);
      expect(stats).toHaveProperty('recoveredErrors', 3);
      expect(stats).toHaveProperty('errorsByType');
      expect(stats).toHaveProperty('recentErrors');
    });
  });

  describe('错误严重程度计算', () => {
    test('应该正确计算错误严重程度', () => {
      // 测试模块错误（高严重程度）
      const moduleError = createTestError('模块错误', 'MODULE_ERROR');
      const moduleSeverity = errorHandler.calculateErrorSeverity(moduleError, {});
      expect(moduleSeverity).toBe('high');
      
      // 测试网络错误（中等严重程度）
      const networkError = createTestError('网络错误', 'NETWORK_ERROR');
      const networkSeverity = errorHandler.calculateErrorSeverity(networkError, {});
      expect(networkSeverity).toBe('medium');
      
      // 测试UI错误（低严重程度）
      const uiError = createTestError('UI错误', 'UI_ERROR');
      const uiSeverity = errorHandler.calculateErrorSeverity(uiError, {});
      expect(uiSeverity).toBe('low');
      
      // 测试关键词错误（严重）
      const criticalError = createTestError('system crash error');
      const criticalSeverity = errorHandler.calculateErrorSeverity(criticalError, {});
      expect(criticalSeverity).toBe('critical');
    });
  });

  describe('网络状态处理', () => {
    beforeEach(() => {
      errorHandler.init();
    });

    test('应该处理网络恢复', () => {
      const networkInfo = {
        networkType: 'wifi',
        isConnected: true
      };
      
      errorHandler.handleNetworkRecovery(networkInfo);
      
      // 验证网络错误抑制被清除
      expect(errorHandler.userNotifications.suppressedErrors.has('NETWORK_ERROR')).toBe(false);
    });

    test('应该等待网络恢复', async () => {
      // Mock 网络状态检查
      errorHandler.checkNetworkStatus = jest.fn()
        .mockResolvedValueOnce({ isConnected: false })
        .mockResolvedValueOnce({ isConnected: true });
      
      const result = await errorHandler.waitForNetworkRecovery();
      
      expect(result).toHaveProperty('success');
    });
  });

  describe('边界情况和错误处理', () => {
    test('应该处理初始化错误', () => {
      const invalidErrorHandler = new UnifiedErrorHandler(null);
      
      expect(() => {
        invalidErrorHandler.init();
      }).toThrow();
    });

    test('应该处理无效的错误对象', async () => {
      errorHandler.init();
      
      const result = await errorHandler.handleError(null, {});
      expect(result.success).toBe(false);
    });

    test('应该处理恢复机制失败', async () => {
      errorHandler.init();
      
      // Mock 恢复机制抛出错误
      const originalRecovery = errorHandler.recoveryMechanisms.get('genericRecovery');
      errorHandler.recoveryMechanisms.set('genericRecovery', () => {
        throw new Error('恢复失败');
      });
      
      const testError = createTestError('测试错误');
      const result = await errorHandler.handleError(testError, {});
      
      expect(result.success).toBe(false);
      
      // 恢复原恢复机制
      errorHandler.recoveryMechanisms.set('genericRecovery', originalRecovery);
    });
  });

  describe('性能测试', () => {
    beforeEach(() => {
      errorHandler.init();
    });

    test('应该高效处理大量错误', async () => {
      const startTime = Date.now();
      
      // 处理100个错误
      const promises = [];
      for (let i = 0; i < 100; i++) {
        const error = createTestError(`错误 ${i}`);
        promises.push(errorHandler.handleError(error, { source: 'performance_test' }));
      }
      
      await Promise.all(promises);
      
      const endTime = Date.now();
      const executionTime = endTime - startTime;
      
      // 100个错误处理应该在1秒内完成
      expect(executionTime).toBeLessThan(1000);
      expect(errorHandler.errorStats.totalErrors).toBe(100);
    });

    test('应该高效执行错误分析', () => {
      // 添加大量错误数据
      for (let i = 0; i < 1000; i++) {
        errorHandler.errorStats.errorsByType.set(`ERROR_TYPE_${i}`, i);
      }
      
      const startTime = Date.now();
      errorHandler.performErrorAnalysis();
      const endTime = Date.now();
      
      const executionTime = endTime - startTime;
      
      // 错误分析应该在100ms内完成
      expect(executionTime).toBeLessThan(100);
    });
  });

  describe('资源清理', () => {
    test('应该正确销毁错误处理器', () => {
      errorHandler.init();
      
      // 验证初始化状态
      expect(errorHandler.monitoringTimer).toBeDefined();
      expect(errorHandler.cleanupTimer).toBeDefined();
      expect(errorHandler.errorCollector.errors.length).toBeGreaterThanOrEqual(0);
      
      errorHandler.destroy();
      
      // 验证清理状态
      expect(errorHandler.monitoringTimer).toBeNull();
      expect(errorHandler.cleanupTimer).toBeNull();
      expect(errorHandler.errorCollector.errors.length).toBe(0);
    });
  });
});