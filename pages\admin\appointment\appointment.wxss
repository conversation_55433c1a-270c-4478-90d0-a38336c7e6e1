/* pages/admin/appointment/appointment.wxss */
/* 预约管理页面样式 */
.appointment-admin-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}

/* 标题栏样式 */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  padding-top: 90rpx; /* 增加顶部内边距，避免与胶囊按钮重叠 */
  background-color: #ffffff;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 10;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
  flex: 1;
  text-align: center; /* 标题居中 */
}

.refresh-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.refresh-icon {
  width: 40rpx;
  height: 40rpx;
}

/* 添加返回按钮样式 */
.back-icon {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  color: #333333;
}

/* 筛选工具栏 */
.filter-bar {
  display: flex;
  flex-direction: column;
  padding: 20rpx 30rpx;
  background-color: #ffffff;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.filter-section {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.filter-section:last-child {
  margin-bottom: 0;
}

.filter-label {
  font-size: 28rpx;
  color: #666666;
  margin-right: 20rpx;
  white-space: nowrap;
}

.filter-picker {
  flex: 1;
  height: 60rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 8rpx;
  padding: 0 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 28rpx;
  color: #333333;
  background-color: #f9f9f9;
}

.picker-arrow {
  font-size: 24rpx;
  color: #999999;
}

.clear-filter {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-left: 10rpx;
  font-size: 32rpx;
  color: #999999;
}

/* 统计按钮 */
.stats-button-container {
  padding: 0 30rpx 20rpx;
  display: flex;
  justify-content: space-between;
}

.stats-button {
  background-color: #4a90e2;
  color: #ffffff;
  height: 80rpx;
  border-radius: 8rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 28rpx;
  font-weight: bold;
  flex: 1;
}

.staff-performance-button {
  background-color: #2dce89;
  margin-left: 20rpx;
}

/* 预约列表 */
.appointment-list {
  flex: 1;
  overflow-y: auto;
  padding: 0 30rpx;
}

/* 加载中和空状态 */
.loading-container, .empty-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 100rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid rgba(74, 144, 226, 0.2);
  border-top: 4rpx solid #4a90e2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text, .empty-text {
  font-size: 28rpx;
  color: #999999;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
  opacity: 0.5;
}

/* 预约卡片 */
.appointment-item {
  background-color: #ffffff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  margin-bottom: 20rpx;
  padding: 20rpx;
  position: relative;
}

.appointment-header {
  display: flex;
  justify-content: center;
  align-items: center;
  padding-bottom: 15rpx;
  border-bottom: 1rpx solid #f0f0f0;
  margin-bottom: 15rpx;
}

/* 状态标签 */
.appointment-status {
  padding: 6rpx 16rpx;
  border-radius: 100rpx;
  font-size: 22rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 38rpx; /* 固定高度 */
  box-sizing: border-box; /* 确保padding不会增加元素总高度 */
}

.appointment-status .status-text {
  text-align: center;
  display: inline-block;
  width: 100%;
  line-height: 1; /* 重置行高 */
  vertical-align: middle; /* 确保文字垂直居中 */
  margin-top: -18rpx; /* 向上微调文字位置，继续增加负边距值 */
}

.appointment-status.pending {
  background-color: #fff7e6;
  color: #fa8c16;
}

.appointment-status.confirmed {
  background-color: #e6f7ff;
  color: #1890ff;
}

.appointment-status.completed {
  background-color: #f6ffed;
  color: #52c41a;
}

.appointment-status.cancelled {
  background-color: #ffccc7; /* 改为红色背景 */
  color: #f5222d; /* 文字颜色保持红色 */
}

.appointment-status.rejected {
  background-color: #fff1f0;
  color: #f5222d;
}

.appointment-date {
  font-size: 24rpx;
  color: #666666;
}

.appointment-content {
  padding-bottom: 15rpx;
}

/* 服务信息 */
.service-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}

.service-name {
  font-size: 30rpx;
  font-weight: bold;
  color: #333333;
}

.service-price {
  font-size: 30rpx;
  color: #ff4d4f;
  font-weight: bold;
}

/* 用户信息 */
.user-info {
  display: flex;
  align-items: center;
  margin-top: 15rpx;
  padding-bottom: 15rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.user-avatar {
  width: 70rpx;
  height: 70rpx;
  border-radius: 50%;
  margin-right: 15rpx;
}

.user-detail {
  flex: 1;
}

.user-nickname {
  font-size: 26rpx;
  color: #333333;
  margin-bottom: 4rpx;
  display: block;
}

/* 电话号码容器 */
.phone-container {
  display: flex;
  align-items: center;
  margin-top: 4rpx;
  justify-content: space-between;
}

.user-phone {
  font-size: 22rpx;
  color: #999999;
  display: block;
}

.call-btn {
  font-size: 22rpx;
  color: #1890ff;
  background-color: #e6f7ff;
  padding: 2rpx 12rpx;
  border-radius: 100rpx;
  border: 1rpx solid #91d5ff;
  margin-right: 20rpx;
}

/* 可点击电话号码样式 */
.user-phone[bindtap] {
  color: #1890ff;
  text-decoration: underline;
}

/* 预约详情信息 */
.appointment-detail-info {
  margin-top: 15rpx;
  padding-bottom: 15rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.detail-row {
  display: flex;
  margin-bottom: 8rpx;
  font-size: 22rpx;
}

.detail-label {
  color: #666666;
  width: 140rpx;
  flex-shrink: 0;
}

.detail-value {
  color: #333333;
  flex: 1;
  word-break: break-all;
}

/* 拒绝理由 */
.reject-reason {
  margin-top: 15rpx;
  padding: 12rpx;
  background-color: #fff6f6;
  border-radius: 8rpx;
  border-bottom: 1rpx solid #f0f0f0;
  padding-bottom: 15rpx;
}

.reason-label {
  font-size: 22rpx;
  color: #ff6b6b;
  font-weight: bold;
  margin-bottom: 4rpx;
  display: block;
}

.reason-content {
  font-size: 22rpx;
  color: #666666;
  line-height: 1.4;
}

/* 时间信息行 */
.time-info-row {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  margin-bottom: 8rpx;
}

/* 时间信息列 */
.time-info-column {
  display: flex;
  flex-direction: column;
  margin-bottom: 8rpx;
}

.time-info-item {
  display: flex;
  font-size: 22rpx;
  margin-bottom: 6rpx;
}

.time-info-item:last-child {
  margin-bottom: 0;
}

.time-info-item .detail-label {
  width: 140rpx;
  flex-shrink: 0;
  color: #666666;
}

.time-info-item .detail-value {
  color: #333333;
  flex: 1;
  word-break: break-all;
}

/* 参考图片 */
.reference-images {
  margin-top: 15rpx;
  padding-bottom: 15rpx;
}

.images-title {
  font-size: 22rpx;
  color: #666666;
  margin-bottom: 10rpx;
  display: block;
}

.image-gallery {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  gap: 10rpx;
  overflow-x: auto;
}

.reference-image {
  width: 100rpx;
  height: 100rpx;
  border-radius: 6rpx;
  object-fit: cover;
  flex-shrink: 0;
}

/* 操作按钮区域 */
.appointment-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 15rpx;
  padding-top: 15rpx;
  border-top: 1rpx solid #f0f0f0;
}

/* 右侧按钮组 */
.action-buttons {
  display: flex;
  justify-content: flex-end;
}

.action-btn {
  padding: 8rpx 20rpx;
  border-radius: 100rpx;
  font-size: 24rpx;
  margin-left: 15rpx;
  text-align: center;
}

.action-btn.confirm {
  background-color: #e6f7ff;
  color: #1890ff;
  border: 1rpx solid #91d5ff;
}

.action-btn.reject {
  background-color: #fff1f0;
  color: #f5222d;
  border: 1rpx solid #ffa39e;
}

.action-btn.complete {
  background-color: #f6ffed;
  color: #52c41a;
  border: 1rpx solid #b7eb8f;
}

.action-btn.detail {
  background-color: #f5a623;
  color: #ffffff;
}

/* 预约详情弹窗 */
.detail-panel {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: center;
  visibility: hidden;
  opacity: 0;
  transition: all 0.3s ease;
}

.detail-panel.show {
  visibility: visible;
  opacity: 1;
}

.detail-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
}

.detail-content {
  position: relative;
  width: 90%;
  max-width: 600rpx;
  max-height: 90%;
  background-color: #ffffff;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  transform: translateY(50rpx);
  transition: transform 0.3s ease;
}

.detail-panel.show .detail-content {
  transform: translateY(0);
}

.detail-header {
  padding: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid #f0f0f0;
}

.detail-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
}

.detail-close {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 40rpx;
  color: #999999;
  cursor: pointer;
}

.detail-body {
  flex: 1;
  padding: 30rpx;
  overflow-y: auto;
  max-height: calc(90vh - 100rpx);
}

.detail-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
}

.detail-section {
  margin-bottom: 30rpx;
  padding-bottom: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.detail-section:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.section-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 20rpx;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15rpx;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.detail-label {
  font-size: 28rpx;
  color: #666666;
}

.detail-value {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
  text-align: right;
  max-width: 60%;
  word-break: break-all;
}

.detail-value.price {
  color: #ff6b6b;
  font-weight: bold;
}

.detail-status {
  display: inline-block;
  padding: 10rpx 30rpx;
  border-radius: 30rpx;
  font-size: 28rpx;
  color: #ffffff;
  margin-bottom: 20rpx;
}

.detail-status.pending {
  background-color: #f5a623;
}

.detail-status.confirmed {
  background-color: #4a90e2;
}

.detail-status.completed {
  background-color: #7ed321;
}

.detail-status.cancelled {
  background-color: #9b9b9b;
}

.detail-status.rejected {
  background-color: #ff6b6b;
}

.reject-reason-detail {
  padding: 20rpx;
  background-color: #fff6f6;
  border-radius: 10rpx;
  font-size: 28rpx;
  color: #666666;
  line-height: 1.5;
}

/* 图片预览区域 */
.image-gallery {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  margin-top: 20rpx;
}

.reference-image {
  width: 200rpx;
  height: 200rpx;
  border-radius: 8rpx;
  object-fit: cover;
}

/* 加载更多 */
.load-more {
  text-align: center;
  padding: 30rpx 0;
  font-size: 24rpx;
  color: #999999;
}

/* 统计面板 */
.stats-panel {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 100;
  display: flex;
  justify-content: center;
  align-items: center;
  transform: translateY(100%);
  transition: transform 0.3s ease-out;
}

.stats-panel.show {
  transform: translateY(0);
}

.stats-mask {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.6);
}

.stats-content {
  position: relative;
  width: 90%;
  height: 80%;
  background-color: #ffffff;
  border-radius: 12rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.stats-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.stats-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
}

.stats-close {
  font-size: 40rpx;
  color: #999999;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.stats-date-filter {
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.date-range-picker {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.date-picker-item {
  display: flex;
  align-items: center;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
}

.date-label {
  font-size: 28rpx;
  color: #666666;
  margin-right: 10rpx;
}

.date-value {
  font-size: 28rpx;
  color: #333333;
  padding: 10rpx 20rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 8rpx;
  background-color: #f9f9f9;
}

.stats-search-btn {
  margin-left: auto;
  margin-bottom: 20rpx;
  padding: 10rpx 30rpx;
  background-color: #4a90e2;
  color: #ffffff;
  border-radius: 8rpx;
  font-size: 28rpx;
}

.stats-body {
  flex: 1;
  padding: 20rpx 30rpx;
  overflow-y: auto;
}

.stats-loading {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 100rpx 0;
  font-size: 28rpx;
  color: #999999;
}

/* 统计卡片 */
.stats-card {
  background-color: #ffffff;
  border-radius: 12rpx;
  margin-bottom: 30rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.stats-card-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 20rpx;
  padding-left: 16rpx;
  border-left: 8rpx solid #4a90e2;
}

.stats-basic {
  display: flex;
  justify-content: space-around;
  padding: 20rpx 0;
}

.stats-basic-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stats-basic-value {
  font-size: 40rpx;
  font-weight: bold;
  color: #4a90e2;
  margin-bottom: 10rpx;
}

.stats-basic-label {
  font-size: 24rpx;
  color: #666666;
}

.stats-status {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.stats-status-item {
  width: 30%;
  background-color: #f9f9f9;
  border-radius: 8rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stats-status-label {
  font-size: 24rpx;
  color: #666666;
  margin-bottom: 10rpx;
}

.stats-status-value {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
}

.stats-service, .stats-date {
  max-height: 600rpx;
  overflow-y: auto;
}

.stats-service-item, .stats-date-item {
  padding: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.stats-service-name, .stats-date-label {
  font-size: 28rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 10rpx;
}

.stats-service-data, .stats-date-data {
  display: flex;
  justify-content: space-between;
}

.stats-service-count, .stats-date-count,
.stats-service-income, .stats-date-income {
  font-size: 24rpx;
  color: #666666;
}

.stats-empty {
  padding: 30rpx 0;
  text-align: center;
  font-size: 28rpx;
  color: #999999;
}

/* 员工订单列表样式 */
.staff-order-list {
  margin-top: 10rpx;
}

.staff-order-item {
  background-color: #f8f9fa;
  border-radius: 8rpx;
  padding: 15rpx;
  margin-bottom: 15rpx;
  border-left: 4rpx solid #2dce89;
}

.staff-order-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10rpx;
}

.staff-order-date {
  font-size: 24rpx;
  color: #666666;
}

.staff-order-status {
  font-size: 24rpx;
  color: #2dce89;
  font-weight: bold;
}

.staff-order-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.staff-order-service {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
  flex: 1;
}

.staff-order-price {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.staff-order-price .price-text {
  font-size: 24rpx;
  color: #666666;
  margin-bottom: 5rpx;
}

.staff-order-price .price-text:last-child {
  color: #e02020;
  margin-bottom: 0;
}

.staff-order-empty, .staff-date-empty {
  padding: 40rpx 0;
  text-align: center;
  font-size: 26rpx;
  color: #999999;
}

/* 员工日期统计样式 */
.staff-date-stats {
  margin-top: 10rpx;
}

.staff-date-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.staff-date-item:last-child {
  border-bottom: none;
}

.staff-date-header {
  display: flex;
  flex-direction: column;
}

.staff-date-label {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
  margin-bottom: 5rpx;
}

.staff-date-count {
  font-size: 24rpx;
  color: #666666;
}

.staff-date-data {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.staff-date-income {
  font-size: 24rpx;
  color: #666666;
  margin-bottom: 5rpx;
}

.staff-date-commission {
  font-size: 26rpx;
  color: #e02020;
  font-weight: 500;
}

/* 快速日期筛选按钮样式 */
.date-filter-options {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.quick-date-filters {
  display: flex;
  margin-top: 10rpx;
}

.quick-date-btn {
  padding: 6rpx 20rpx;
  font-size: 24rpx;
  background-color: #f5f5f5;
  color: #666;
  margin-right: 15rpx;
  border-radius: 4rpx;
  text-align: center;
}

.quick-date-btn.active {
  background-color: #007AFF;
  color: #fff;
}