const app = getApp()

Page({
  /**
   * 页面的初始数据
   */
  data: {
    staffInfo: null,
    todayPerformance: 0,
    yesterdayPerformance: 0,
    weekPerformance: 0,
    monthPerformance: 0,
    totalCommission: 0,
    serviceCommission: 0, // 服务佣金
    promotionCommission: 0, // 推广佣金
    loading: true,
    // 核销相关数据
    verifyCode: '',
    isVerifying: false,
    // 充值核销相关数据
    rechargeVerifyCode: '',
    isVerifyingRecharge: false,
    todayIncome: 0,
    yesterdayIncome: 0,
    weekIncome: 0,
    monthIncome: 0,
    // 日期范围查询相关数据
    showDateQuery: false,
    startDate: '',
    endDate: '',
    dateRangeCommission: 0,
    dateRangeIncome: 0,
    dateRangeLoading: false,
    dateRangeResult: false,
    dateRangeCount: 0,
    // 服务状态相关数据
    serviceStatus: 'available' // 默认为空闲状态
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 检查是否已登录
    const staffInfo = wx.getStorageSync('staffInfo');
    const isStaff = wx.getStorageSync('isStaff');
    
    if (!isStaff || !staffInfo) {
      this.redirectToLogin();
      return;
    }
    
    // 确保全局数据也设置了员工信息
    if (!app.globalData.isStaff || !app.globalData.staffInfo) {
      app.globalData.isStaff = true;
      app.globalData.staffInfo = staffInfo;
    }
    
    this.setData({
      staffInfo: staffInfo
    });
    
    // 获取业绩数据
    this.fetchPerformanceData();
    
    // 获取服务状态
    this.fetchStaffStatus();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 检查是否已登录
    const staffInfo = wx.getStorageSync('staffInfo');
    const isStaff = wx.getStorageSync('isStaff');
    
    if (!isStaff || !staffInfo) {
      this.redirectToLogin();
      return;
    }
  },

  /**
   * 获取业绩数据
   */
  fetchPerformanceData() {
    this.setData({ loading: true });
    
    const staffInfo = this.data.staffInfo;
    if (!staffInfo || !staffInfo._id) {
      wx.showToast({
        title: '员工信息不完整，请重新登录',
        icon: 'none'
      });
      this.setData({ loading: false });
      return;
    }
    
    // console.log('开始获取员工业绩数据，员工ID:', staffInfo._id);
    
    wx.cloud.callFunction({
      name: 'staffManager',
      data: {
        type: 'staff',
        action: 'getPerformance',
        data: {
          staffId: staffInfo._id
        }
      }
    }).then(res => {
      // console.log('获取业绩数据结果：', JSON.stringify(res.result));

      if (res.result && res.result.code === 0) {
        const data = res.result.data;
        
        // 查询充值佣金数据
        wx.cloud.callFunction({
          name: 'staffManager',
          data: {
            type: 'staff',
            action: 'getStaffCommissions',
            data: {
              staffId: staffInfo._id
            }
          }
        }).then(commissionRes => {
          let serviceCommission = parseFloat(data.totalCommission || 0);
          let promotionCommission = 0;

          if (commissionRes.result && commissionRes.result.code === 0) {
            // 获取推广佣金总额
            const commissionData = commissionRes.result.data || {};
            promotionCommission = parseFloat(commissionData.totalAmount || 0);

            // 服务佣金 = 总佣金 - 推广佣金
            serviceCommission = Math.max(0, serviceCommission - promotionCommission);
          }
          
          // 确保使用正确的数据字段，并提供默认值0
          this.setData({
            todayPerformance: parseFloat(data.todayCommission || 0).toFixed(2),
            yesterdayPerformance: parseFloat(data.yesterdayCommission || 0).toFixed(2),
            weekPerformance: parseFloat(data.weekCommission || 0).toFixed(2),
            monthPerformance: parseFloat(data.monthCommission || 0).toFixed(2),
            totalCommission: parseFloat(data.totalCommission || 0).toFixed(2),
            serviceCommission: serviceCommission.toFixed(2),
            promotionCommission: promotionCommission.toFixed(2),
            todayIncome: parseFloat(data.todayIncome || 0).toFixed(2),
            yesterdayIncome: parseFloat(data.yesterdayIncome || 0).toFixed(2),
            weekIncome: parseFloat(data.weekIncome || 0).toFixed(2),
            monthIncome: parseFloat(data.monthIncome || 0).toFixed(2),
            loading: false
          });
          
          // console.log('业绩数据设置完成，今日提成:', this.data.todayPerformance);
        }).catch(err => {
          console.error('获取佣金详情失败：', err);
          
          // 即使获取佣金详情失败，也设置基本的业绩数据
          this.setData({
            todayPerformance: parseFloat(data.todayCommission || 0).toFixed(2),
            yesterdayPerformance: parseFloat(data.yesterdayCommission || 0).toFixed(2),
            weekPerformance: parseFloat(data.weekCommission || 0).toFixed(2),
            monthPerformance: parseFloat(data.monthCommission || 0).toFixed(2),
            totalCommission: parseFloat(data.totalCommission || 0).toFixed(2),
            serviceCommission: parseFloat(data.totalCommission || 0).toFixed(2),
            promotionCommission: '0.00',
            todayIncome: parseFloat(data.todayIncome || 0).toFixed(2),
            yesterdayIncome: parseFloat(data.yesterdayIncome || 0).toFixed(2),
            weekIncome: parseFloat(data.weekIncome || 0).toFixed(2),
            monthIncome: parseFloat(data.monthIncome || 0).toFixed(2),
            loading: false
          });
        });
      } else {
        console.error('获取业绩数据失败:', res.result);
        wx.showToast({
          title: res.result?.message || '获取业绩数据失败',
          icon: 'none'
        });
        this.setData({ loading: false });
      }
    }).catch(err => {
      console.error('获取业绩数据失败：', err);
      wx.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      });
      this.setData({ loading: false });
    });
  },

  /**
   * 输入核销码
   */
  inputVerifyCode(e) {
    this.setData({
      verifyCode: e.detail.value
    });
  },

  /**
   * 通过核销码验证
   */
  verifyByCode(forceCash = false) {
    const { verifyCode, staffInfo } = this.data;
    
    if (!verifyCode || verifyCode.trim() === '') {
      wx.showToast({
        title: '请输入核销码',
        icon: 'none'
      });
      return;
    }

    this.setData({ isVerifying: true });
    console.log('开始核销，员工ID:', staffInfo._id, '核销码:', verifyCode, '强制现金:', forceCash);

    wx.cloud.callFunction({
      name: 'appointmentManager',
      data: {
        type: 'staff',
        action: 'verifyAppointment',
        verifyCode: verifyCode.trim(),
        staffId: staffInfo._id,
        staffName: staffInfo.name,
        forceCash: forceCash // 添加强制现金支付参数
      },
      success: res => {
        console.log('核销码验证结果', JSON.stringify(res.result));
        
        // 处理余额不足的情况
        if (res.result && res.result.code === 201) {
          // 余额不足，显示选择支付方式的弹窗
          const { serviceName, servicePrice, userBalance, shortage, userName } = res.result.data;
          
          wx.showModal({
            title: '余额不足提醒',
            content: `${userName} 的余额不足！\n服务项目：${serviceName}\n服务价格：¥${servicePrice}\n当前余额：¥${userBalance}\n还差：¥${shortage.toFixed(2)}`,
            confirmText: '现金支付',
            cancelText: '提示充值',
            success: (modalRes) => {
              if (modalRes.confirm) {
                // 用户选择现金支付，重新调用核销函数并强制使用现金
                console.log('用户选择现金支付');
                this.verifyByCode(true);
              } else {
                // 用户选择充值，提示员工引导用户充值
                wx.showModal({
                  title: '请引导用户充值',
                  content: '请引导用户前往"我的"-"余额充值"页面进行充值，充值后再次核销。',
                  showCancel: false,
                  confirmText: '我知道了'
                });
                // 不清空核销码，方便重新核销
              }
            }
          });
          
          this.setData({ isVerifying: false });
          return;
        }
        
        if (res.result && (res.result.code === 0 || res.result.success)) {
          // 清空输入框
          this.setData({
            verifyCode: ''
          });
          
          // 显示核销成功提示，不显示提成金额
          wx.showToast({
            title: '核销成功',
            icon: 'success',
            duration: 2000
          });
          
          // 延迟一秒后刷新业绩数据，确保云函数已完成数据写入
          setTimeout(() => {
            console.log('核销成功后延迟刷新业绩数据');
            this.fetchPerformanceData();
          }, 1000);
        } else {
          wx.showToast({
            title: res.result?.message || '核销失败',
            icon: 'none'
          });
        }
      },
      fail: err => {
        console.error('核销失败', err);
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      },
      complete: () => {
        this.setData({ isVerifying: false });
      }
    });
  },

  /**
   * 导航到我的预约页面
   */
  navigateToAssignedAppointments() {
    wx.navigateTo({
      url: '/pages/staff/assigned_appointments/assigned_appointments'
    });
  },

  /**
   * 导航到现金订单页面
   */
  navigateToCashOrders() {
    wx.navigateTo({
      url: '/pages/staff/orders/orders?paymentMethod=cash'
    });
  },

  /**
   * 导航到余额核销页面
   */
  navigateToBalanceOrders() {
    wx.navigateTo({
      url: '/pages/staff/orders/orders?paymentMethod=balance'
    });
  },

  /**
   * 导航到充值核销记录页面
   */
  navigateToRechargeRecords() {
    wx.navigateTo({
      url: '/pages/staff/recharge_records/recharge_records'
    });
  },

  /**
   * 导航到支出记录页面
   */
  navigateToExpense() {
    wx.navigateTo({
      url: '/pages/staff/expense/expense'
    });
  },

  /**
   * 返回上一页
   */
  goBack() {
    // 返回到"我的"页面
    wx.switchTab({
      url: '/pages/my/my'
    });
  },

  /**
   * 退出登录
   */
  logout() {
    wx.showModal({
      title: '提示',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          // 清除员工信息
          app.globalData.isStaff = false;
          app.globalData.staffInfo = null;
          
          // 清除本地存储
          wx.removeStorageSync('staffInfo');
          wx.removeStorageSync('isStaff');
          
          // 跳转到登录页
          this.redirectToLogin();
        }
      }
    });
  },

  /**
   * 跳转到登录页
   */
  redirectToLogin() {
    wx.redirectTo({
      url: '/pages/staff/login/login'
    });
  },

  /**
   * 显示/隐藏日期查询面板
   */
  toggleDateQuery() {
    this.setData({
      showDateQuery: !this.data.showDateQuery,
      dateRangeResult: false // 切换时重置结果显示
    });
  },

  /**
   * 设置开始日期
   */
  bindStartDateChange(e) {
    this.setData({
      startDate: e.detail.value
    });
  },

  /**
   * 设置结束日期
   */
  bindEndDateChange(e) {
    this.setData({
      endDate: e.detail.value
    });
  },

  /**
   * 按日期范围查询业绩
   */
  queryByDateRange() {
    const { startDate, endDate, staffInfo } = this.data;
    
    if (!startDate || !endDate) {
      wx.showToast({
        title: '请选择起止日期',
        icon: 'none'
      });
      return;
    }
    
    // 检查日期范围是否超过45天
    const start = new Date(startDate);
    const end = new Date(endDate);
    const diffDays = Math.ceil((end - start) / (1000 * 60 * 60 * 24));
    
    if (diffDays > 45) {
      wx.showToast({
        title: '查询范围不能超过45天',
        icon: 'none'
      });
      return;
    }
    
    if (end < start) {
      wx.showToast({
        title: '结束日期不能早于开始日期',
        icon: 'none'
      });
      return;
    }
    
    this.setData({ dateRangeLoading: true, dateRangeResult: false });
    
    wx.cloud.callFunction({
      name: 'staffManager',
      data: {
        type: 'staff',
        action: 'getPerformanceByDateRange',
        data: {
          staffId: staffInfo._id,
          startDate: startDate,
          endDate: endDate
        }
      }
    }).then(res => {
      console.log('日期范围查询结果：', res);
      
      if (res.result && res.result.code === 0) {
        const data = res.result.data;
        
        this.setData({
          dateRangeCommission: data.totalCommission || 0,
          dateRangeIncome: data.totalIncome || 0,
          dateRangeCount: data.count || 0,
          dateRangeResult: true,
          dateRangeLoading: false
        });
      } else {
        wx.showToast({
          title: res.result?.message || '查询失败',
          icon: 'none'
        });
        this.setData({ dateRangeLoading: false });
      }
    }).catch(err => {
      console.error('日期范围查询失败：', err);
      wx.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      });
      this.setData({ dateRangeLoading: false });
    });
  },

  /**
   * 输入充值核销码
   */
  inputRechargeVerifyCode(e) {
    this.setData({
      rechargeVerifyCode: e.detail.value
    });
  },

  /**
   * 通过核销码验证充值
   */
  verifyRechargeByCode() {
    const { rechargeVerifyCode, staffInfo } = this.data;
    
    if (!rechargeVerifyCode || rechargeVerifyCode.trim() === '') {
      wx.showToast({
        title: '请输入充值核销码',
        icon: 'none'
      });
      return;
    }

    this.setData({ isVerifyingRecharge: true });
    console.log('开始核销充值，员工ID:', staffInfo._id, '核销码:', rechargeVerifyCode);

    // 纠正数据结构，确保verifyCode字段正确传递
    wx.cloud.callFunction({
      name: 'rechargeManager',
      data: {
        type: 'staff',
        action: 'verifyRecharge',
        data: {
          verifyCode: rechargeVerifyCode.trim(),
          operatorId: staffInfo._id,
          operatorName: staffInfo.name
        }
      }
    }).then(res => {
      console.log('充值核销结果', res.result);
      
      if (res.result && res.result.code === 0) {
        // 清空输入框
        this.setData({
          rechargeVerifyCode: ''
        });
        
        // 显示核销成功提示，不显示金额信息
        wx.showToast({
          title: '充值核销成功',
          icon: 'success',
          duration: 2000
        });
        
        // 延迟刷新业绩数据
        setTimeout(() => {
          this.fetchPerformanceData();
        }, 1000);
      } else {
        wx.showToast({
          title: res.result?.message || '充值核销失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      console.error('充值核销失败', err);
      wx.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      });
    }).finally(() => {
      this.setData({ isVerifyingRecharge: false });
    });
  },

  /**
   * 获取服务状态
   */
  fetchStaffStatus() {
    const staffInfo = this.data.staffInfo;
    if (!staffInfo || !staffInfo._id) {
      this.setData({ serviceStatus: 'unavailable' });
      return;
    }

    wx.cloud.callFunction({
      name: 'staffManager',
      data: {
        type: 'staff',
        action: 'getStaffStatus',
        data: {
          staffId: staffInfo._id
        }
      }
    }).then(res => {
      if (res.result && res.result.code === 0) {
        this.setData({ serviceStatus: res.result.data.serviceStatus || 'available' });
      } else {
        this.setData({ serviceStatus: 'unavailable' });
      }
    }).catch(err => {
      console.error('获取服务状态失败：', err);
      this.setData({ serviceStatus: 'unavailable' });
    });
  },

  /**
   * 改变服务状态
   */
  changeStatus(e) {
    const staffInfo = this.data.staffInfo;
    if (!staffInfo || !staffInfo._id) {
      wx.showToast({
        title: '员工信息不完整，无法改变状态',
        icon: 'none'
      });
      return;
    }

    const status = e.currentTarget.dataset.status;
    // 状态对应的中文描述
    const statusText = {
      'available': '空闲',
      'busy': '服务中',
      'rest': '休息'
    };

    wx.cloud.callFunction({
      name: 'staffManager',
      data: {
        type: 'staff',
        action: 'updateServiceStatus',
        data: {
          staffId: staffInfo._id,
          status: status
        }
      }
    }).then(res => {
      if (res.result && res.result.code === 0) {
        wx.showToast({
          title: `更改为${statusText[status]}`,
          icon: 'success'
        });
        this.fetchStaffStatus(); // 刷新状态
      } else {
        wx.showToast({
          title: res.result?.message || '更新状态失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      console.error('更新状态失败：', err);
      wx.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      });
    });
  }
}) 
