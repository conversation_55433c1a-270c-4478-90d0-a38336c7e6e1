<view class="edit-container">
  <view class="page-header">
    <view class="back-icon" bindtap="navigateBack">
      <text class="iconfont icon-back">←</text>
    </view>
    <view class="header-title">{{isEdit ? '编辑内容' : '添加内容'}}</view>
    <view class="save-btn" bindtap="submitForm">保存</view>
  </view>
  
  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{isLoading}}">
    <view class="loading-spinner"></view>
    <view class="loading-text">加载中...</view>
  </view>
  
  <!-- 表单内容 -->
  <view class="form-container" wx:if="{{!isLoading}}">
    <!-- 通用字段 -->
    <view class="form-group">
      <view class="form-label">标题</view>
      <input 
        class="form-input" 
        placeholder="请输入标题" 
        value="{{formData.title}}"
        bindinput="onTitleInput"
      />
    </view>
    
    <!-- 画廊内容表单 -->
    <block wx:if="{{contentType === 'gallery'}}">
      <view class="form-group">
        <view class="form-label">封面图片</view>
        <view class="image-uploader" bindtap="chooseImage">
          <image 
            wx:if="{{tempImagePath}}" 
            class="preview-image" 
            src="{{tempImagePath}}" 
            mode="aspectFill"
          ></image>
          <view wx:else class="upload-placeholder">
            <view class="upload-icon">+</view>
            <view class="upload-text">点击上传图片</view>
          </view>
        </view>
        
        <!-- 上传进度 -->
        <view class="upload-progress" wx:if="{{showUploadProgress}}">
          <view class="progress-bar">
            <view class="progress-inner" style="width: {{uploadProgress}}%;"></view>
          </view>
          <view class="progress-text">{{uploadProgress}}%</view>
        </view>
      </view>
    </block>
    
    <!-- Banner内容表单 -->
    <block wx:if="{{contentType === 'banner'}}">
      <view class="form-group">
        <view class="form-label">Banner图片</view>
        <view class="image-uploader banner-uploader" bindtap="chooseImage">
          <image 
            wx:if="{{tempImagePath}}" 
            class="preview-image" 
            src="{{tempImagePath}}" 
            mode="aspectFill"
          ></image>
          <view wx:else class="upload-placeholder">
            <view class="upload-icon">+</view>
            <view class="upload-text">点击上传图片</view>
          </view>
        </view>
        
        <!-- 上传进度 -->
        <view class="upload-progress" wx:if="{{showUploadProgress}}">
          <view class="progress-bar">
            <view class="progress-inner" style="width: {{uploadProgress}}%;"></view>
          </view>
          <view class="progress-text">{{uploadProgress}}%</view>
        </view>
      </view>
      
      <view class="form-group">
        <view class="form-label">链接地址</view>
        <input 
          class="form-input" 
          placeholder="请输入链接地址" 
          value="{{formData.linkUrl}}"
          bindinput="onLinkInput"
        />
      </view>
    </block>
    
    <!-- 公告内容表单 -->
    <block wx:if="{{contentType === 'notice'}}">
      <view class="form-group">
        <view class="form-label">公告内容</view>
        <textarea 
          class="form-textarea" 
          placeholder="请输入公告内容" 
          value="{{formData.content}}"
          bindinput="onContentInput"
        ></textarea>
      </view>
    </block>
    
    <!-- 通用状态切换 -->
    <view class="form-group">
      <view class="form-label">状态</view>
      <view class="status-switch" bindtap="toggleStatus">
        <view class="status-text">{{formData.status === 1 ? '显示' : '隐藏'}}</view>
        <view class="switch-container {{formData.status === 1 ? 'active' : ''}}">
          <view class="switch-circle"></view>
        </view>
      </view>
    </view>
  </view>
  
  <!-- 底部提交按钮 -->
  <view class="submit-bar">
    <button 
      class="submit-btn" 
      bindtap="submitForm" 
      loading="{{isSaving}}"
      hover-class="btn-hover"
    >{{isEdit ? '保存修改' : '添加内容'}}</button>
  </view>
</view> 