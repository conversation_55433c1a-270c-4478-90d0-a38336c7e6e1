/* 导入WxParse样式 */
/* @import "../../wxParse/wxParse.wxss"; */

/* 移除遮罩层，采用更直接的方法 */

/* 富文本内容样式 */
.fullscreen-modal-content {
  position: fixed;
  width: 100vw; /* 修改为100%视口宽度，满屏显示 */
  height: 90vh;
  left: 0; /* 左侧无边距 */
  right: 0; /* 右侧无边距 */
  bottom: 0;
  /* 移除硬编码的背景色，使用CSS变量 */
  border-top-left-radius: 40rpx; /* 增加顶部圆角 */
  border-top-right-radius: 40rpx; /* 增加顶部圆角 */
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.15); /* 添加阴影效果 */
  z-index: 999999 !important; /* 设置极高的z-index，确保覆盖所有导航栏 */
  transform: translateY(100%); /* 初始位置在屏幕底部 */
  transition: transform 0.3s ease-out; /* 统一动画时间为300ms */
}

.fullscreen-modal-content.show {
  transform: translateY(0); /* 显示时移动到正常位置 */
}

/* 弹窗关闭时的向下滑出样式 */
.fullscreen-modal-content.closing {
  transform: translateY(100%); /* 关闭时向下滑出 */
}

/* 修改滚动内容区域，确保与外层容器同步移动 */
.scrollable-content {
  flex: 1;
  height: calc(100% - 120rpx); /* 从100rpx增加到120rpx，减少滚动区域高度 */
  margin-bottom: 0; /* 移除负的底部外边距 */
  position: relative;
  z-index: 1;
  -webkit-overflow-scrolling: touch;
  background-color: transparent; /* 改为透明背景 */
  padding: 20rpx 0 20rpx 0; /* 增加底部内边距 */
  box-sizing: border-box;
  overflow-y: auto;
  /* 优化滚动性能的属性 */
  will-change: transform;
  transform: translateZ(0);
  -webkit-transform: translateZ(0);
  /* 优化滚动流畅度 */
  transition: transform 0.1s ease-out;
}

/* 确保内容区域不会有独立的过渡动画 */
.hidden-content {
  /* 保持与父容器同步，不单独动画 */
  transform: none !important;
  transition: none !important;
  animation: none !important;
}

/* 确保内容在退出时保持可见并与背景同步 */
.fullscreen-modal-content.hidden .scrollable-content {
  transform: translateY(0) !important;
  opacity: 0.95;
  transition: opacity 0.2s ease-out;
}

/* 进一步优化：确保退出时所有子元素保持在容器内，不产生独立层叠效果 */
.fullscreen-modal-content.hidden > .modal-view,
.fullscreen-modal-content.hidden > .modal-scroll-view,
.fullscreen-modal-content.hidden .modal-div,
.fullscreen-modal-content.hidden .modal-p,
.fullscreen-modal-content.hidden .modal-image,
.fullscreen-modal-content.hidden .modal-button {
  /* 关键：禁止所有元素的独立动画和变换 */
  transition: none !important;
  transform: none !important;
  animation: none !important;
  /* 关键：禁止所有可能的3D效果 */
  transform-style: flat !important;
  -webkit-transform-style: flat !important;
  /* 禁止子元素创建独立的层叠上下文 */
  contain: none !important;
  /* 如果使用了will-change，强制停用 */
  will-change: auto !important;
}

/* 确保所有图片元素不会影响关闭动画 */
.fullscreen-modal-content.hidden .animation-container,
.fullscreen-modal-content.hidden .top-image-container,
.fullscreen-modal-content.hidden .top-image,
.fullscreen-modal-content.hidden .rich-content-image,
.fullscreen-modal-content.hidden .img-element,
.fullscreen-modal-content.hidden .rich-content-container {
  /* 关键：禁用所有可能的变换和动画 */
  transform: none !important;
  transition: none !important;
  animation: none !important;
  perspective: none !important;
  -webkit-perspective: none !important;
  backface-visibility: visible !important;
  -webkit-backface-visibility: visible !important;
  transform-style: flat !important;
  -webkit-transform-style: flat !important;
  /* 不使用any 3D acceleration，避免图层问题 */
  -webkit-transform: translateZ(0) !important;
  transform: translateZ(0) !important;
  /* 关闭GPU加速 */
  will-change: auto !important;
  /* 强制使用相同的过渡变换方式 */
  transform-origin: center center !important;
  /* 防止缩放 */
  scale: 1 !important;
  -webkit-scale: 1 !important;
}

/* 自定义标题栏 */
.custom-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 24rpx;
  background-color: var(--gallerymodal-bg);
  border-bottom: 1px solid var(--gallerymodal-divider, rgba(255, 255, 255, 0.05));
  position: relative;
  z-index: 10;
}

.header-title {
  font-size: 32rpx;
  font-weight: 500;
  color: var(--gallerymodal-header-text, #ffffff);
  text-align: center;
  flex: 1;
}

.header-left {
  width: 60rpx;
}

.header-close {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.close-icon {
  font-size: 44rpx;
  color: var(--gallerymodal-text, #ffffff);
  line-height: 1;
}

.header-close:active {
  background-color: rgba(255, 255, 255, 0.1);
}

/* 顶部安全区域 - 避免与胶囊按钮重叠，现在变为透明 */
.safe-area-top {
  width: 100%;
  background-color: transparent;
  box-sizing: border-box;
  min-height: 60px;
  position: relative;
  z-index: 5;
}

/* 视频播放区域 */
.video-container {
  width: 100vw; /* 使用视口宽度，确保占满整个屏幕宽度 */
  height: 56.25vw; /* 保持16:9比例 */
  background-color: #000000;
  position: relative;
  margin: 0;
  padding: 0;
  overflow: hidden;
  left: 0;
  right: 0;
}

.video-container .video-element {
  width: 100%;
  height: 100%;
  display: block;
  object-fit: cover;
  margin: 0;
  padding: 0;
}

/* 视频信息区域 */
.video-info {
  padding: 15rpx 20rpx; /* 减少内边距 */
  border-bottom: 1rpx solid #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: space-between; /* 两端对齐 */
}

.author-avatar {
  width: 70rpx;
  height: 70rpx;
  margin-right: 15rpx;
  flex-shrink: 0;
}

.author-avatar .author-avatar-image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}

.video-info-content {
  flex: 1;
  overflow: hidden;
  margin-right: 10rpx; /* 与播放量保持一定距离 */
}

.video-title {
  font-size: 28rpx;
  font-weight: bold;
  color: var(--gallerymodal-text, #ffffff);
  margin-bottom: 4rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.2;
}

.video-subtitle {
  font-size: 22rpx;
  color: var(--gallerymodal-text, rgba(255, 255, 255, 0.8));
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.2;
}

.play-count {
  font-size: 20rpx;
  color: var(--gallerymodal-text, #ffffff);
  line-height: 1.2;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  gap: 6rpx;
}

.view-icon {
  width: 48rpx;
  height: 36rpx;
  flex-shrink: 0;
}

/* 视频详情描述区域 */
.video-detail-section {
  padding: 20rpx 30rpx;
  flex: 1;
  background-color: rgba(0, 0, 0, 0.1);  /* 添加半透明黑色背景 */
  margin: 20rpx;  /* 添加外边距 */
  border-radius: 16rpx;  /* 添加圆角 */
}

/* 标题和刷新按钮行 */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
  padding: 0 16rpx;
}

.section-title {
  font-size: 24rpx;
  font-weight: 400;
  color: var(--gallerymodal-text, #ffffff);
  padding: 4rpx 15rpx;
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 20rpx;
}

/* 刷新按钮样式 */
.refresh-btn {
  font-size: 24rpx;
  color: #42b983;
  padding: 4rpx 15rpx;
  border: 1rpx solid #42b983;
  border-radius: 20rpx;
}

.refresh-btn:active {
  opacity: 0.7;
}

/* 调试信息样式 */
.debug-info {
  font-size: 24rpx;
  color: var(--gallerymodal-text, rgba(255, 255, 255, 0.9));
  background-color: rgba(0, 0, 0, 0.3);
  padding: 15rpx;
  margin: 10rpx 0;
  border-radius: 8rpx;
  word-break: break-all;
  white-space: pre-wrap;
  border-left: 4rpx solid #42b983;
}

/* 加载动画容器 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx 0;
  width: 100%;
}

/* 三点加载动画 */
.loading-dots {
  display: flex;
  justify-content: center;
  margin-bottom: 20rpx;
}

.loading-dot {
  width: 16rpx;
  height: 16rpx;
  background-color: var(--gallerymodal-loading-dot, rgba(255, 255, 255, 0.6));
  border-radius: 50%;
  margin: 0 8rpx;
  animation: dot-bounce 1.4s infinite ease-in-out both;
}

.loading-dot:nth-child(1) {
  animation-delay: -0.32s;
}

.loading-dot:nth-child(2) {
  animation-delay: -0.16s;
}

.loading-text {
  font-size: 28rpx;
  color: var(--gallerymodal-loading-text, rgba(255, 255, 255, 0.7));
}

@keyframes dot-bounce {
  0%, 80%, 100% { 
    transform: scale(0);
  } 
  40% { 
    transform: scale(1.0);
  }
}

.detail-content {
  margin-bottom: 30rpx;
}

.description-text {
  font-size: 28rpx;
  color: var(--gallerymodal-text, #ffffff);  /* 白色，增加可读性 */
  line-height: 1.6;
  padding: 0; /* 移除内边距 */
  width: 100%; /* 确保宽度100% */
  text-align: left; /* 文本左对齐 */
}

.description-placeholder {
  font-size: 28rpx;
  color: var(--gallerymodal-text, rgba(255, 255, 255, 0.6));
  line-height: 1.6;
  text-align: center;
  padding: 40rpx 0;
  width: 100%;
}

.rich-content-area {
  min-height: 200rpx;
}

/* 底部操作区 - 固定在屏幕底部 */
.modal-footer {
  height: var(--gallerymodal-footer-height, 120rpx);
  padding: 0 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: var(--gallerymodal-footer-bg, rgba(230, 173, 173, 0.15));
  backdrop-filter: var(--gallerymodal-footer-blur, blur(15px));
  -webkit-backdrop-filter: var(--gallerymodal-footer-blur, blur(15px));
  border-top: var(--gallerymodal-footer-border, 0.5px solid rgba(255, 255, 255, 0.08));
  position: absolute;
  bottom: 0; /* 恢复正常位置 */
  left: 0;
  right: 0;
  z-index: 100;
  width: 100%;
  box-sizing: border-box;
  gap: 20rpx;
  padding-bottom: 15rpx;
  border-bottom-left-radius: 0; /* 将左下角改为直角 */
  border-bottom-right-radius: 0; /* 将右下角改为直角 */
  border-top-left-radius: 16rpx; /* 添加左上角小圆角 */
  border-top-right-radius: 16rpx; /* 添加右上角小圆角 */
}

.action-button {
  padding: 0;
  background-color: transparent;
  font-size: 28rpx;
  line-height: normal;
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
  box-sizing: border-box;
  border: none;
  outline: none;
  box-shadow: none;
  position: relative;
  margin-top: 15rpx; /* 向上移动按钮位置 */
}

/* 移除默认按钮样式和点击效果 */
.action-button::after {
  display: none;
  border: none;
  content: none;
}

.action-button:hover,
.action-button:active,
.action-button.button-hover {
  background-color: transparent;
  opacity: 1;
}

/* 按钮图标样式 */
.button-icon,
.action-button-icon {
  width: var(--gallerymodal-button-icon-size, 48rpx); /* 从56rpx缩小到48rpx */
  height: var(--gallerymodal-button-icon-size, 48rpx); /* 从56rpx缩小到48rpx */
  filter: var(--gallerymodal-button-filter, brightness(0) invert(1)); /* 确保图标为白色 */
}

/* 滚动到顶部按钮 */
.scroll-top-button {
  flex: 1;
  color: var(--gallerymodal-button-text, #ffffff);
}

/* 关闭按钮 */
.close-button {
  flex: 1;
  color: var(--gallerymodal-button-text, #ffffff);
}



/* 图片区域容器 */
.image-area-container {
  position: relative;
  width: 100%;
  margin: 0;
  padding: 10rpx 0 30rpx 0; /* 减小顶部内边距，从25rpx改为10rpx */
  z-index: 3; /* 保持z-index为3，确保图片区在最上层 */
  overflow: visible; /* 允许内容溢出 */
  background-color: transparent; /* 确保背景透明 */
  transition: padding 0.3s ease; /* 添加过渡效果 */
}

/* 富文本容器样式 - 整合到滚动区域 */
.rich-content-container {
  width: 90%; /* 将宽度从88%调整为90%，在新的更窄容器中占据更大比例 */
  overflow: visible;
  box-sizing: border-box;
  font-size: 28rpx;
  padding: 80rpx 40rpx 30rpx 40rpx; /* 保持相同的内边距设置 */
  background-color: var(--gallerymodal-content-bg, rgba(255, 255, 255, 1)); /* 保持完全不透明 */
  margin-top: 15rpx; /* 与顶部图片保持一定间距 */
  margin-left: auto;
  margin-right: auto; /* 居中显示 */
  margin-bottom: 20rpx;
  border-radius: 24rpx; /* 与顶部图片容器保持一致的圆角 */
  position: relative;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.12); /* 保持轻微的阴影效果 */
  display: flex;
  flex-direction: column;
  align-items: center;
  color: var(--gallerymodal-content-text, #333333) !important;
  z-index: 2; /* 确保内容区域在背景上方，但在图片下方 */
}

/* 处理富文本内容中的图片 */
.rich-content-container .rich-content-image {
  max-width: 100% !important;
  width: auto !important;
  max-height: 80vh !important;
  height: auto !important;
  display: block !important;
  margin: 16rpx auto !important;
  border-radius: 6rpx;
  object-fit: contain;
}

/* 图片特殊处理 - 为宽度100%的图片应用特殊样式 */
.rich-content-container .rich-content-image-full-width {
  width: 100% !important;
  margin: 16rpx 0 !important;
  border-radius: 4rpx;
}

/* 调整富文本中段落的间距和对齐 */
.rich-content-container .rich-text-paragraph {
  margin: 20rpx 0 !important; /* 减小段落间距 */
  line-height: 1.7 !important;
  text-align: justify !important; /* 文本两端对齐，提高阅读体验 */
  text-justify: inter-ideograph !important; /* 针对中文排版优化 */
  word-break: break-word !important;
  width: 100% !important;
  padding: 0 6rpx !important; /* 增加段落的左右内边距 */
}

/* 调整富文本中标题样式 */
.rich-content-container .rich-text-h1,
.rich-content-container .rich-text-h2,
.rich-content-container .rich-text-h3,
.rich-content-container .rich-text-h4,
.rich-content-container .rich-text-h5 {
  margin: 30rpx 0 20rpx;
  font-weight: bold;
}

/* 列表样式 - 通过类选择器 */
.rich-content-container .rich-text-list {
  padding-left: 40rpx;
  margin: 20rpx 0;
}

.rich-content-container .rich-text-list-item {
  margin: 10rpx 0;
  font-size: 28rpx;
}

/* 富文本链接样式 - 通过类选择器 */
.rich-content-container .rich-text-link {
  color: var(--gallerymodal-button-text, #42b983);
  text-decoration: none;
}

/* 针对空段落的特殊处理 */
.rich-content-container .rich-text-p-empty {
  margin: 8rpx 0 !important;
  height: 8rpx !important;
}

/* 调整图片容器前后的间距 */
.rich-content-image-paragraph {
  margin: 30rpx 0 !important;
}

/* 特殊处理图片容器 */
.rich-content-full-width-div {
  margin: 16rpx 0 !important;
  width: 100% !important;
  padding: 0 !important;
}

/* 改进表格样式 */
.rich-table {
  /* 基础样式由内联样式处理 */
  border-collapse: collapse;
}

.rich-table-row {
  /* 简化样式，由内联样式决定 */
}

.rich-table-cell {
  /* 简化样式，由内联样式决定 */
}

.rich-table-header {
  /* 简化样式，由内联样式决定 */
}

/* 修复关闭动画添加一个新的包装器类 */
.animation-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

/* 退出时的图片容器样式 */
.exit-animation-container {
  opacity: 1 !important;
  transition: none !important;
  animation: none !important;
  transform: none !important;
  /* 强制图片层跟随容器移动 */
  contain: layout style paint;
  -webkit-contain: layout style paint;
}

/* 顶部图片容器 - 满屏显示 */
.top-image-container {
  width: 86%; /* 调整宽度为86%，在新的更窄容器中占据更大比例 */
  position: relative;
  overflow: hidden;
  background-color: transparent; /* 将背景色从白色改为透明 */
  margin: 0 auto; /* 居中显示 */
  padding: 0;
  padding-bottom: 105.5%; /* 略微增加高度比例 */
  z-index: 3; /* 保持z-index为3，确保图片区在最上层 */
  /* 完全禁用所有过渡效果 */
  transition: none !important;
  transform: none !important;
  animation: none !important;
  transform-style: flat !important;
  -webkit-transform-style: flat !important;
  /* 关键：与父容器保持一致的速度 */
  will-change: auto;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.3) !important; /* 添加阴影效果增强悬浮感 */
  border-radius: 24rpx; /* 四周都使用圆角 */
  margin-top: 10rpx; /* 减小顶部间距，从30rpx改为10rpx */
  margin-bottom: 30rpx; /* 保持底部间距不变 */
}

/* 顶部图片样式 */
.top-image {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  display: block;
  margin: 0;
  object-fit: cover; /* 使图片填充容器并保持宽高比 */
  vertical-align: top;
  /* 完全禁用所有过渡效果 */
  transition: none !important;
  transform: none !important;
  animation: none !important;
  transform-style: flat !important;
  -webkit-transform-style: flat !important;
  /* 关键：与父容器保持一致的速度 */
  will-change: auto;
  border-radius: 24rpx; /* 与容器保持一致的圆角 */
}

/* 修复rich-content-image在关闭过程中的闪烁问题 */
.rich-content-image {
  /* 确保没有任何动画或过渡效果 */
  transition: none !important;
  transform: none !important;
  animation: none !important;
}

/* 底部留白区样式调整 */
.bottom-space {
  height: 120rpx; /* 从80rpx增加到120rpx，增加底部留白 */
  width: 100%;
  padding-top: 10rpx; /* 保持顶部内边距 */
  margin-bottom: 0;
  background-color: transparent; /* 使用透明背景 */
  position: relative;
  z-index: 1;
}

/* 调整最后一个详情图片的特殊处理 */
.direct-detail-image-wrapper:last-child {
  margin-bottom: 30rpx; /* 从10rpx增加到30rpx，确保最后一张图片与底部有足够间距 */
}



/* 移除内容区底部渐变效果，不再需要 */
.rich-content-container::after {
  display: none;
}



/* 调整拖动指示器的位置，使其悬浮在图片上方 */
.drag-indicator {
  width: 80rpx; /* 增加宽度 */
  height: 8rpx; /* 增加高度 */
  border-radius: 4rpx;
  background-color: var(--gallerymodal-drag-indicator, rgba(255, 255, 255, 0.9));
  position: absolute;
  top: 15rpx; /* 调整位置靠上一些 */
  left: 50%;
  transform: translateX(-50%);
  z-index: 100;
  margin: 0;
}

/* 退出时的滚动内容区域样式 */
.exit-scrollable-content {
  opacity: 1 !important;
  background-color: var(--gallerymodal-bg) !important;
  /* 关键：确保与父容器共同移动 */
  will-change: transform !important;
  transform: none !important;
  transition: none !important;
  animation: none !important;
  /* 确保滚动视图作为容器的一部分整体移动 */
  contain: layout style paint;
  -webkit-contain: layout style paint;
}

/* 退出时的内容区样式 */
.exit-content {
  opacity: 1 !important;
  background-color: var(--gallerymodal-content-bg, rgba(255, 255, 255, 1)) !important; /* 保持完全不透明 */
  transition: none !important;
  animation: none !important;
  /* 强制所有子元素锁定到父级容器 */
  contain: layout style paint;
  -webkit-contain: layout style paint;
  /* 保持z-index确保覆盖关系在退出时也正确 */
  z-index: 2 !important;
  /* 更新与图片的覆盖关系 */
  margin-top: 15rpx !important; /* 与正常状态一致 */
  /* 更新圆角设置 */
  border-radius: 24rpx !important; /* 与正常状态一致 */
  /* 同步阴影效果 */
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.12) !important; /* 与正常状态一致 */
  padding: 80rpx 40rpx 30rpx 40rpx !important; /* 与正常状态一致 */
  width: 90% !important; /* 与正常状态一致 */
}

/* 确保退出时图片区域正确显示 */
.fullscreen-modal-content.hidden .image-area-container,
.fullscreen-modal-content.hidden .animation-container,
.fullscreen-modal-content.hidden .top-image-container {
  z-index: 3 !important; /* 修改：从1改为3，确保图片区在最上层 */
  opacity: 1 !important;
  transition: none !important;
  animation: none !important;
  transform: none !important;
}

/* 空白退出面板，用于退出状态下显示 */
.empty-exit-panel {
  width: 100%;
  height: 100%;
  background-color: var(--gallerymodal-bg);
  /* 确保背景颜色完全一致 */
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 10;
}

/* 防止空白面板有独立动画效果 */
.fullscreen-modal-content.hidden .empty-exit-panel {
  /* 继承父元素的动画效果 */
  animation: none !important;
  transform: none !important;
  transition: none !important;
}

/* 添加全局容器样式 */
.rich-content-global-container {
  color: #333333 !important;
  font-size: 28rpx;
  font-family: sans-serif;
  padding: 10rpx 0;
  box-sizing: border-box;
  width: 100%;
}

/* 处理富文本中标签的通用样式 */
.rich-text-node {
  display: block;
  margin: 8rpx 0;
  color: #333333;
}

/* 强制所有文本为黑色 */
.rich-content-container,
.rich-content-container .rich-content-view,
.rich-content-container .rich-text-content,
.rich-content-container .modal-div,
.rich-content-container .modal-p,
.rich-content-container .rich-text-paragraph,
.rich-content-container .rich-text-h1,
.rich-content-container .rich-text-h2,
.rich-content-container .rich-text-h3,
.rich-content-container .rich-text-h4,
.rich-content-container .rich-text-h5,
.rich-content-container .rich-text-h6,
.rich-content-container .rich-text-list-item {
  color: var(--gallerymodal-content-text, #333333) !important;
}

/* 按钮悬停效果 */
.button-hover {
  transform: scale(0.92);
  opacity: 0.9;
}

/* 脉冲动画效果，修改为居中定位 */
@keyframes pulse {
  0% {
    transform: scale(1) translate(-50%, -50%);
  }
  50% {
    transform: scale(1.05) translate(-48%, -48%);
  }
  100% {
    transform: scale(1) translate(-50%, -50%);
  }
}

/* 文章标题容器 */
.article-title-container {
  display: none;
}

.main-title {
  display: none;
}

/* 详情图片容器样式 */
.detail-images-container {
  width: 95%; /* 从90%增加到95% */
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 30rpx; /* 保持图片之间的间距 */
  padding: 30rpx 0 20rpx 0; /* 增加底部内边距 */
  margin-bottom: 10rpx; /* 添加额外的底部外边距 */
  /* 优化渲染性能 */
  contain: content;
  will-change: transform;
}

.detail-image-wrapper {
  width: 100%; /* 从90%增加到100% */
  display: flex;
  justify-content: center;
  margin-bottom: 5rpx;
  padding: 0;
  box-sizing: border-box;
  /* 优化渲染性能 */
  contain: layout;
  transform: translateZ(0);
  height: auto; /* 确保高度自适应 */
  max-height: none; /* 移除最大高度限制 */
}

.detail-image {
  width: 100%; /* 保持宽度为100% */
  max-height: none; /* 移除最大高度限制，允许图片完全展示 */
  height: auto; /* 确保高度自动根据宽度等比例缩放 */
  margin: 0 auto;
  display: block;
  border-radius: 8rpx;
  /* 优化渲染性能 */
  will-change: opacity, transform;
  transform: translateZ(0);
  -webkit-transform: translateZ(0);
  opacity: 0; /* 初始透明 */
  animation: fadeIn 0.3s ease forwards; /* 使用淡入动画 */
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* 描述文本样式 */
.description-text {
  padding: 20rpx 40rpx;
  font-size: 30rpx;
  line-height: 1.6;
  color: #333;
  text-align: justify;
  margin-bottom: 10rpx; /* 添加底部外边距 */
}

/* 无内容状态样式 */
.description-placeholder {
  padding: 40rpx;
  font-size: 30rpx;
  color: #999;
  text-align: center;
  margin-bottom: 20rpx; /* 添加底部外边距 */
}

/* 优化的滚动行为类 */
.improved-scroll-behavior {
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
  /* 启用GPU加速 */
  will-change: transform;
  transform: translateZ(0);
  -webkit-transform: translateZ(0);
  /* 防止橡皮筋效果导致的视觉问题 */
  overscroll-behavior: none;
  -webkit-overscroll-behavior: none;
  /* 优化滚动性能 */
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
  perspective: 1000;
  -webkit-perspective: 1000;
}

/* 确保transform属性不与滚动行为冲突 */
.improved-scroll-behavior::after {
  content: "";
  display: block;
  height: 1px;
  margin-bottom: -1px;
}

/* 确保所有滚动容器中的图像有平滑过渡 */
.scrollable-content image {
  transition: opacity 0.3s ease;
}

/* 直接显示详情图片的容器样式 */
.direct-detail-images-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20rpx; /* 减少图片之间的间距 */
  padding: 20rpx 0 10rpx 0; /* 减少底部内边距 */
  background-color: transparent; /* 确保背景透明 */
  margin-top: -15rpx; /* 向上移动15rpx */
}

.direct-detail-image-wrapper {
  width: 95%; /* 控制图片宽度占据屏幕的比例 */
  display: flex;
  justify-content: center;
  margin-bottom: 5rpx; /* 减少底部外边距 */
  padding: 0;
  box-sizing: border-box;
  /* 优化渲染性能 */
  contain: layout;
  transform: translateZ(0);
  height: auto; /* 确保高度自适应 */
  max-height: none; /* 移除最大高度限制 */
}

.direct-detail-image {
  width: 100%; /* 保持宽度为100% */
  max-height: none; /* 移除最大高度限制，允许图片完全展示 */
  height: auto; /* 确保高度自动根据宽度等比例缩放 */
  margin: 0 auto;
  display: block;
  border-radius: 8rpx;
  /* 优化渲染性能 */
  will-change: opacity, transform;
  transform: translateZ(0);
  -webkit-transform: translateZ(0);
  opacity: 0; /* 初始透明 */
  animation: fadeIn 0.3s ease forwards; /* 使用淡入动画 */
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
}

/* 直接显示的无内容状态样式 */
.direct-description-placeholder {
  padding: 40rpx;
  font-size: 30rpx;
  color: #999;
  text-align: center;
  margin: 20rpx 0; /* 上下外边距 */
  width: 90%;
  margin-left: auto;
  margin-right: auto;
}

