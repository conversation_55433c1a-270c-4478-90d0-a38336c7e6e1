/* pages/admin/staff/staff.wxss */

/* 应用样式规范的颜色变量 */
/* 主色调：深灰色 #4a4a4a */
/* 强调色/主按钮：类苹果蓝 #0070c9 */
/* 成功色：绿色 #34c759 */
/* 危险色/警告色：红色 #ff3b30 */
/* 浅色背景：浅灰色 #f5f5f7 */
/* 文字主色：#333333 */
/* 次要文字色：#666666 */
/* 浅色文字：#999999 */

.staff-container {
  background-color: #f5f5f7; /* 更新为规范的浅色背景 */
  min-height: 100vh;
  position: relative;
  height: 100vh;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  padding: 0 20rpx; /* 减小左右内边距 */
  padding-top: 150rpx; /* 为顶部标题栏预留空间 */
  box-sizing: border-box;
}

/* 顶部状态栏样式 */
.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  padding-top: 90rpx; /* 确保在胶囊按钮下方 */
  background-color: #ffffff;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 100;
  box-sizing: border-box;
}

.back-icon {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  color: #4a4a4a; /* 更新为规范的主色调 */
}

.header-title {
  font-size: 34rpx;
  font-weight: bold;
  color: #333333; /* 更新为规范的文字主色 */
  flex: 1;
  text-align: center;
}

/* 列表样式 */
.staff-list {
  background-color: white;
  border-radius: 6rpx; /* 统一卡片圆角 */
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;
  margin-bottom: 120rpx; /* 为底部按钮留出空间 */
}

.loading {
  padding: 60rpx 0;
  text-align: center;
}

.loading-icon {
  display: inline-block;
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #0070c9; /* 更新为规范的强调色 */
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 24rpx; /* 减小字体大小 */
  color: #999999; /* 更新为规范的浅色文字 */
  margin-top: 20rpx;
}

.empty-tip {
  padding: 60rpx 0;
  text-align: center;
  color: #999999; /* 更新为规范的浅色文字 */
  font-size: 24rpx; /* 减小字体大小 */
}

.list-content {
  width: 100%;
}

.list-header {
  display: flex;
  background-color: #f9f9f9;
  border-bottom: 1rpx solid #e0e0e0; /* 更新为规范的边框颜色 */
  font-weight: bold;
  color: #666666; /* 更新为规范的次要文字色 */
  font-size: 24rpx;
  padding: 12rpx 8rpx; /* 添加内边距，与日期统计保持一致 */
}

.list-item {
  display: flex;
  border-bottom: 1rpx solid #e0e0e0; /* 更新为规范的边框颜色 */
  transition: background-color 0.2s ease;
}

.list-item:hover {
  background-color: #f9f9f9;
}

.list-item:last-child {
  border-bottom: none;
}

.cell {
  padding: 16rpx 10rpx;
  display: flex;
  align-items: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: #333333; /* 更新为规范的文字主色 */
  font-size: 24rpx;
}

.name-cell {
  flex: 0.7;
  min-width: 90rpx;
}

.phone-cell {
  flex: 1;
  min-width: 140rpx;
  color: #666666; /* 更新为规范的次要文字色 */
}

.commission-cell {
  flex: 0.5;
  min-width: 70rpx;
  justify-content: center;
  text-align: center;
  color: #0070c9; /* 更新为规范的强调色 */
  font-weight: 500;
  flex-direction: column;
  white-space: normal;
}

.commission-cell view {
  font-size: 20rpx;
  line-height: 28rpx;
  margin: 2rpx 0;
}

.commission-cell view:first-child {
  color: #0070c9; /* 现金提成颜色 */
}

.commission-cell view:last-child {
  color: #4caf50; /* 余额提成颜色 */
}

.status-cell {
  flex: 0.6;
  min-width: 70rpx;
  justify-content: center;
}

.action-cell {
  flex: 0.8;
  min-width: 120rpx;
  justify-content: flex-end;
}

.status-tag {
  padding: 2rpx 12rpx;
  border-radius: 4rpx; /* 统一按钮圆角 */
  font-size: 22rpx;
  text-align: center;
  min-width: 70rpx;
}

.status-tag.active {
  background-color: rgba(52, 199, 89, 0.1); /* 更新为规范的成功色背景 */
  color: #34c759; /* 更新为规范的成功色 */
}

.status-tag.inactive {
  background-color: rgba(255, 59, 48, 0.1); /* 更新为规范的危险色背景 */
  color: #ff3b30; /* 更新为规范的危险色 */
}

.action-btn {
  margin-left: 10rpx;
  padding: 4rpx 16rpx;
  border-radius: 4rpx; /* 统一按钮圆角 */
  font-size: 22rpx;
  text-align: center;
  box-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.1); /* 添加按钮阴影 */
  transition: all 0.2s ease;
}

.action-btn:active {
  opacity: 0.9;
  transform: translateY(1rpx);
}

.edit-btn {
  background-color: rgba(0, 112, 201, 0.1); /* 更新为规范的强调色背景 */
  color: #0070c9; /* 更新为规范的强调色 */
}

.disable-btn {
  background-color: rgba(255, 59, 48, 0.1); /* 更新为规范的危险色背景 */
  color: #ff3b30; /* 更新为规范的危险色 */
}

.enable-btn {
  background-color: rgba(52, 199, 89, 0.1); /* 更新为规范的成功色背景 */
  color: #34c759; /* 更新为规范的成功色 */
}

/* 弹窗样式 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
  position: relative;
  width: 80%;
  max-width: 600rpx;
  background-color: white;
  border-radius: 6rpx; /* 统一卡片圆角 */
  overflow: hidden;
  z-index: 1001;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.modal-header {
  padding: 25rpx 30rpx; /* 调整内边距 */
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid #e0e0e0; /* 更新为规范的边框颜色 */
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333; /* 更新为规范的文字主色 */
}

.modal-close {
  font-size: 40rpx;
  color: #999999; /* 更新为规范的浅色文字 */
  line-height: 1;
}

.modal-body {
  padding: 25rpx 30rpx; /* 调整内边距 */
}

.form-item {
  margin-bottom: 20rpx; /* 减小间距 */
}

.form-label {
  font-size: 28rpx;
  color: #666666; /* 更新为规范的次要文字色 */
  margin-bottom: 10rpx;
}

.form-input {
  width: 100%;
  height: 80rpx;
  border: 1rpx solid #e0e0e0; /* 更新为规范的边框颜色 */
  border-radius: 4rpx; /* 统一按钮圆角 */
  padding: 0 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
  color: #333333; /* 更新为规范的文字主色 */
}

/* 添加placeholder样式 */
.form-input::placeholder {
  color: #999999; /* 更新为规范的浅色文字 */
}

.form-picker {
  width: 100%;
  height: 80rpx;
  border: 1rpx solid #e0e0e0; /* 更新为规范的边框颜色 */
  border-radius: 4rpx; /* 统一按钮圆角 */
  padding: 0 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}

.picker-text {
  line-height: 80rpx;
  color: #333333; /* 更新为规范的文字主色 */
}

.modal-footer {
  display: flex;
  border-top: 1rpx solid #e0e0e0; /* 更新为规范的边框颜色 */
}

.modal-btn {
  flex: 1;
  height: 90rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx; /* 减小字体大小 */
  transition: all 0.2s ease;
}

.modal-btn:active {
  opacity: 0.9;
}

.cancel-btn {
  color: #666666; /* 更新为规范的次要文字色 */
  border-right: 1rpx solid #e0e0e0; /* 更新为规范的边框颜色 */
}

.confirm-btn {
  color: #0070c9; /* 更新为规范的强调色 */
  font-weight: bold;
}

/* 悬浮添加按钮 */
.add-button {
  position: fixed;
  left: 50%;
  bottom: 60rpx;
  transform: translateX(-50%);
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  background: #0070c9; /* 更新为规范的强调色 */
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 112, 201, 0.3); /* 更新阴影颜色 */
  z-index: 99;
  transition: all 0.2s ease;
}

.add-button:active {
  transform: translateX(-50%) scale(0.92);
  box-shadow: 0 2rpx 10rpx rgba(0, 112, 201, 0.2); /* 更新阴影颜色 */
  opacity: 0.9;
}

.add-icon {
  color: #ffffff;
  font-size: 70rpx;
  font-weight: bold;
  margin-bottom: 6rpx;
}

.safe-bottom-area {
  height: 160rpx;
  width: 100%;
}

/* 提成滑块样式 */
.commission-slider {
  width: 100%;
  padding: 10rpx 0;
}

.commission-slider slider {
  margin: 0;
}

/* 更新滑块颜色 */
.commission-slider slider {
  margin: 0;
}

/* 添加头像上传相关样式 */
.avatar-upload {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.avatar-wrapper {
  position: relative;
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  overflow: hidden;
  background-color: #f5f5f7; /* 更新为规范的浅色背景 */
  margin-top: 10rpx;
  border: 1px dashed #e0e0e0; /* 更新为规范的边框颜色 */
}

.avatar-preview {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-upload-icon {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.2);
  color: #fff;
  font-size: 40rpx;
} 