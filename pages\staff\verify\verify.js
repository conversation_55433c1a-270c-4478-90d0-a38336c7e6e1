// 员工验单页面
const app = getApp();

Page({
  data: {
    staffInfo: null,
    orderList: [],
    isLoading: true,
    isEmpty: false,
    verifyCode: '',
    isVerifying: false
  },

  onLoad: function() {
    // 检查员工登录状态
    const staffInfo = wx.getStorageSync('staffInfo');
    if (!staffInfo) {
      this.redirectToLogin();
      return;
    }

    this.setData({ staffInfo });
    this.fetchPendingOrders();
  },

  onShow: function() {
    // 每次显示页面时重新获取待验证订单
    if (this.data.staffInfo) {
      this.fetchPendingOrders();
    }
  },

  // 获取待验证的订单
  fetchPendingOrders: function() {
    this.setData({ isLoading: true });

    const { staffInfo } = this.data;
    if (!staffInfo || !staffInfo._id) {
      console.error('员工信息不完整，无法获取预约');
      this.setData({ 
        isLoading: false,
        isEmpty: true
      });
      return;
    }

    wx.cloud.callFunction({
      name: 'appointmentManager',
      data: {
        type: 'staff',
        action: 'getAssignedAppointments',
        staffId: staffInfo._id,
        status: 'confirmed'  // 只获取待确认的预约
      },
      success: res => {
        console.log('获取员工分配的预约列表', res);
        
        if (res.result && res.result.code === 0) {
          const appointmentList = res.result.data || [];
          
          // 只保留status为confirmed的预约
          const confirmedAppointments = appointmentList.filter(
            appointment => appointment.status === 'confirmed'
          );
          
          // 格式化预约数据
          const formattedAppointments = confirmedAppointments.map(appointment => {
            return {
              ...appointment,
              formattedDate: appointment.date,
              appointmentTime: appointment.time,
              statusText: this.getOrderStatusText(appointment.status),
              serviceName: appointment.serviceName,
              userName: appointment.userName || '预约客户',
              userPhone: appointment.phoneNumber || '无'
            };
          });
          
          this.setData({
            orderList: formattedAppointments,
            isEmpty: formattedAppointments.length === 0,
            isLoading: false
          });
        } else {
          this.setData({
            orderList: [],
            isEmpty: true,
            isLoading: false
          });
          
          wx.showToast({
            title: res.result?.message || '获取预约失败',
            icon: 'none'
          });
        }
      },
      fail: err => {
        console.error('获取预约失败', err);
        this.setData({
          orderList: [],
          isEmpty: true,
          isLoading: false
        });
        
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      }
    });
  },

  // 获取订单状态文本
  getOrderStatusText: function(status) {
    const statusMap = {
      'pending': '待确认',
      'confirmed': '已确认',
      'completed': '已完成',
      'cancelled': '已取消'
    };
    return statusMap[status] || '未知状态';
  },

  // 查看订单详情
  viewOrderDetail: function(e) {
    const orderId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/staff/order_detail/order_detail?id=${orderId}`
    });
  },

  // 验证订单
  verifyOrder: function(e) {
    const orderId = e.currentTarget.dataset.id;
    const orderIndex = e.currentTarget.dataset.index;
    
    wx.showModal({
      title: '确认验证',
      content: '确定要验证此订单吗？验证后将标记为已完成状态',
      success: res => {
        if (res.confirm) {
          this.completeOrder(orderId, orderIndex);
        }
      }
    });
  },

  // 输入核销码
  inputVerifyCode: function(e) {
    this.setData({
      verifyCode: e.detail.value
    });
  },

  // 通过核销码验证
  verifyByCode: function() {
    const { verifyCode } = this.data;
    
    if (!verifyCode || verifyCode.trim() === '') {
      wx.showToast({
        title: '请输入核销码',
        icon: 'none'
      });
      return;
    }

    this.setData({ isVerifying: true });

    wx.cloud.callFunction({
      name: 'appointmentManager',
      data: {
        type: 'staff',
        action: 'verifyAppointment',
        verifyCode: verifyCode.trim(),
        staffId: this.data.staffInfo._id,
        staffName: this.data.staffInfo.name
      },
      success: res => {
        console.log('核销码验证结果', res);
        
        if (res.result && (res.result.code === 0 || res.result.success)) {
          wx.showToast({
            title: '验证成功',
            icon: 'success'
          });
          
          // 清空输入框
          this.setData({
            verifyCode: ''
          });
          
          // 刷新订单列表
          setTimeout(() => {
            this.fetchPendingOrders();
          }, 1500);
        } else {
          wx.showToast({
            title: res.result?.message || '验证失败',
            icon: 'none'
          });
        }
      },
      fail: err => {
        console.error('验证失败', err);
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      },
      complete: () => {
        this.setData({ isVerifying: false });
      }
    });
  },

  // 完成订单
  completeOrder: function(orderId, orderIndex) {
    wx.showLoading({
      title: '处理中...',
      mask: true
    });

    wx.cloud.callFunction({
      name: 'appointmentManager',
      data: {
        type: 'staff',
        action: 'verifyAppointment',
        appointmentId: orderId,
        staffId: this.data.staffInfo._id,
        staffName: this.data.staffInfo.name
      },
      success: res => {
        console.log('完成订单结果', res);
        
        if (res.result && (res.result.code === 0 || res.result.success)) {
          // 更新本地订单列表
          const orderList = [...this.data.orderList];
          orderList.splice(orderIndex, 1);
          
          this.setData({
            orderList,
            isEmpty: orderList.length === 0
          });
          
          wx.showToast({
            title: '订单已完成',
            icon: 'success'
          });
        } else {
          wx.showToast({
            title: res.result?.message || '操作失败',
            icon: 'none'
          });
        }
      },
      fail: err => {
        console.error('完成订单失败', err);
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      },
      complete: () => {
        wx.hideLoading();
      }
    });
  },

  // 返回员工首页
  goBack: function() {
    wx.navigateBack();
  },

  // 重定向到登录页
  redirectToLogin: function() {
    wx.redirectTo({
      url: '/pages/staff/login/login'
    });
  },

  // 下拉刷新
  onPullDownRefresh: function() {
    this.fetchPendingOrders();
    wx.stopPullDownRefresh();
  }
}); 