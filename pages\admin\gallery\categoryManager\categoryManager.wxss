/* 分类管理组件样式 */
.category-manager {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  visibility: hidden;
  opacity: 0;
  transition: visibility 0.3s, opacity 0.3s;
}

.category-manager.visible {
  visibility: visible;
  opacity: 1;
}

.category-manager-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
}

.category-manager-content {
  position: relative;
  width: 90%;
  max-width: 600rpx;
  height: 80%;
  max-height: 1000rpx;
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  z-index: 1001;
}

.category-manager-header {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.category-manager-title {
  font-size: 34rpx;
  font-weight: bold;
  color: #333;
}

.category-manager-close {
  font-size: 40rpx;
  color: #999;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: -15rpx;
  border-radius: 50%;
}

.category-manager-close:active {
  background-color: #f0f0f0;
}

.category-manager-body {
  flex: 1;
  padding: 30rpx;
  overflow-y: auto;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300rpx;
}

.loading {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #07c160;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #999;
}

.category-list {
  margin-bottom: 30rpx;
}

.empty-tip {
  text-align: center;
  padding: 60rpx 0;
  color: #999;
  font-size: 28rpx;
}

.category-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.category-info {
  flex: 1;
}

.category-name {
  font-size: 30rpx;
  color: #333;
}

.category-actions {
  display: flex;
}

.action-btn {
  padding: 10rpx 20rpx;
  font-size: 24rpx;
  border-radius: 6rpx;
  margin-left: 10rpx;
}

.edit-btn {
  background-color: #f0f0f0;
  color: #333;
}

.delete-btn {
  background-color: #ff4d4f;
  color: #fff;
}

.add-category-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 80rpx;
  background-color: #f6f6f6;
  border-radius: 10rpx;
  margin-top: 20rpx;
  color: #07c160;
  font-size: 28rpx;
}

.add-icon {
  font-size: 36rpx;
  margin-right: 10rpx;
}

.category-form {
  background-color: #f6f6f6;
  padding: 30rpx;
  border-radius: 10rpx;
  margin-top: 20rpx;
}

.form-title {
  font-size: 30rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  color: #333;
}

.form-item {
  margin-bottom: 20rpx;
}

.form-input {
  width: 100%;
  height: 80rpx;
  background-color: #fff;
  border-radius: 8rpx;
  padding: 0 20rpx;
  box-sizing: border-box;
  font-size: 28rpx;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
}

.form-btn {
  padding: 0 30rpx;
  height: 70rpx;
  line-height: 70rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  margin-left: 20rpx;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.cancel-btn {
  background-color: #f0f0f0;
  color: #333;
}

.confirm-btn {
  background-color: #07c160;
  color: #fff;
}

.btn-loading {
  width: 30rpx;
  height: 30rpx;
  border: 3rpx solid rgba(255, 255, 255, 0.3);
  border-top: 3rpx solid #fff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 10rpx;
} 