<!-- 移除WxParse模板导入 -->
<!-- <import src="../../wxParse/wxParse.wxml"/> -->

<modal 
  visible="{{visible}}" 
  showHeader="{{false}}"
  bindclose="onClose"
  bindnavbarControl="onNavbarControl"
  fullWidth="{{true}}"
  bindtouchmove="onTouchMove"
  custom-style="position:fixed; height:100vh;">
  <view 
    class="fullscreen-modal-content {{showGalleryAnimation ? 'show' : ''}} {{isClosing ? 'closing' : ''}}" 
    style="{{modalHeightStyle}}; background: var(--gallerymodal-bg);"
    bindtouchmove="onTouchMove"
    bindtouchend="onTouchEnd">
    
    <!-- 使用单一容器统一管理滚动区域，确保整体滑动 -->
    <scroll-view 
      id="modalScroll"
      class="scrollable-content improved-scroll-behavior" 
      scroll-y="{{true}}"
      scroll-top="{{scrollTop}}"
      scroll-with-animation="{{true}}"
      enhanced="{{true}}"
      bounces="{{false}}"
      show-scrollbar="{{false}}"
      fast-deceleration="{{true}}"
      bindscroll="onScrollEvent"
      bindtouchstart="onTouchStart"
      bindtouchmove="onTouchMove"
      bindtouchend="onTouchEnd"
      lower-threshold="300"
      style="-webkit-overflow-scrolling: touch;">
      
      <!-- 顶部图片区域和拖动指示器 -->
      <view class="image-area-container modal-view">
        <view class="drag-indicator modal-view"></view>
        
        <!-- 顶部图片 - 退出时也显示 -->
        <view class="animation-container modal-view {{isClosing ? 'exit-animation-container' : ''}}" wx:if="{{firstImageUrl}}">
          <view class="top-image-container modal-view">
            <image class="top-image modal-image img-element" src="{{firstImageUrl}}" mode="aspectFill" binderror="onImageError" data-src="{{firstImageUrl}}" bindtap="onImageTap"></image>
          </view>
        </view>
      </view>
      
      <!-- 详情图片展示区域 - 直接放在滚动区域中，不使用富文本容器包裹 -->
      <view wx:if="{{detailImages.length > 0}}" class="direct-detail-images-container modal-view">
        <block wx:for="{{detailImages}}" wx:key="*this">
          <view class="direct-detail-image-wrapper modal-view">
            <image 
              class="direct-detail-image modal-image img-element" 
              src="{{item}}" 
              mode="widthFix" 
              binderror="onImageError" 
              data-src="{{item}}" 
              data-index="{{index}}"
              bindtap="onImageTap"
              bindload="onImageLoad"
              lazy-load="{{true}}"
              webp="{{true}}"
              show-menu-by-longpress="{{true}}"
            ></image>
          </view>
        </block>
      </view>
      
      <!-- 无内容状态 - 直接显示在滚动区域 -->
      <view wx:elif="{{!detailImages.length}}" class="direct-description-placeholder modal-p">暂无详情图片</view>
      
      <view class="bottom-space modal-view">
        <!-- 底部留白 -->
      </view>
    </scroll-view>
    
    <!-- 底部操作区 - 退出时隐藏 -->
    <view class="modal-footer modal-view" bindtouchmove="onTouchMove" wx:if="{{!isClosing}}">
      <button class="action-button scroll-top-button modal-button" bindtap="scrollToTop">
        <image src="/static/置顶图标.svg" mode="aspectFit" class="button-icon action-button-icon modal-image img-element"></image>
      </button>
      

      
      <button class="action-button close-button modal-button" bindtap="onClose">
        <image src="/static/退出图标.svg" mode="aspectFit" class="button-icon action-button-icon modal-image img-element"></image>
      </button>
    </view>
  </view>
</modal>

