<view class="my-appointments-container">
  <!-- 顶部标题栏 -->
  <view class="header">
    <view class="title">我的预约</view>
  </view>
  
  <!-- 状态筛选 -->
  <view class="filter-bar">
    <view class="filter-label">状态筛选：</view>
    <picker mode="selector" range="{{statusOptions}}" range-key="label" bindchange="onStatusChange">
      <view class="filter-picker">
        <text>{{currentStatusLabel}}</text>
        <text class="picker-arrow">▼</text>
      </view>
    </picker>
  </view>
  
  <!-- 预约列表 -->
  <view class="appointment-list">
    <!-- 加载中 -->
    <view class="loading-container" wx:if="{{loading}}">
      <view class="loading-spinner"></view>
      <text class="loading-text">加载中...</text>
    </view>
    
    <!-- 空状态 -->
    <view class="empty-container" wx:elif="{{appointmentList.length === 0}}">
      <text class="empty-text">暂无预约记录</text>
      <view class="go-appointment-btn" bindtap="reAppointment">去预约</view>
    </view>
    
    <!-- 预约列表 -->
    <block wx:else>
      <view class="appointment-item" wx:for="{{appointmentList}}" wx:key="_id" bindtap="viewAppointmentDetail" data-appointment="{{item}}">
        <view class="appointment-header">
          <view class="appointment-status {{item.status}}">
            <text>{{item.statusText}}</text>
          </view>
          <view class="appointment-date">{{item.date}} {{item.time}}</view>
        </view>
        
        <view class="appointment-content">
          <view class="service-info">
            <text class="service-name">{{item.serviceName}}</text>
            <text class="service-price">¥{{item.servicePrice}}</text>
          </view>
          
          <!-- 添加员工信息显示 -->
          <view class="staff-info" wx:if="{{item.preferredStaffName || item.assignment_type}}">
            <text class="staff-label">服务员工：</text>
            <text class="staff-name" wx:if="{{item.preferredStaffName}}">{{item.preferredStaffName}}</text>
            <text class="staff-name" wx:elif="{{item.assignment_type === 'random'}}">随机分配</text>
            <text class="staff-name" wx:else>未指定</text>
          </view>
          
          <!-- 拒绝原因（如果有） -->
          <view class="reject-reason" wx:if="{{item.status === 'rejected' && item.rejectReason}}">
            <text class="reason-label">拒绝原因：</text>
            <text class="reason-content">{{item.rejectReason}}</text>
          </view>
        </view>
        
        <!-- 操作按钮 -->
        <view class="appointment-actions">
          <!-- 允许取消预约的状态：待确认、已确认 -->
          <view class="action-btn cancel" wx:if="{{item.status === 'pending' || item.status === 'confirmed'}}" catchtap="cancelAppointment" data-id="{{item._id}}">
            取消预约
          </view>
          
          <!-- 允许重新预约的状态：已拒绝、已取消 -->
          <view class="action-btn re-appointment" wx:if="{{item.status === 'rejected' || item.status === 'cancelled'}}" catchtap="reAppointment" data-service="{{item}}">
            重新预约
          </view>
        </view>
      </view>
      
      <!-- 加载更多 -->
      <view class="load-more" wx:if="{{hasMoreData && !loading}}">
        <text>上拉加载更多</text>
      </view>
      <view class="load-more" wx:if="{{!hasMoreData && appointmentList.length > 0}}">
        <text>已显示全部数据</text>
      </view>
    </block>
  </view>
</view> 