<!-- pages/admin/suggestions/suggestions.wxml -->
<view class="chat-container">
  <!-- 自定义导航栏 - 只在用户列表时显示 -->
  <view class="custom-nav-bar" wx:if="{{!selectedUser}}">
    <view class="nav-content" style="margin-top: {{headerPadding}}px;">
      <view class="nav-left" bindtap="goBack">
        <!-- 修改为黑色箭头 -->
        <text class="back-arrow">←</text>
      </view>
      <view class="nav-title">投诉建议管理</view>
      <view class="nav-right"></view>
    </view>
  </view>
  
  <!-- 主体内容 -->
  <view class="main-content {{selectedUser ? 'chat-mode' : ''}}" style="{{!selectedUser ? 'margin-top:' + mainContentMarginTop + ';' : ''}}">
    <!-- 左侧用户列表 -->
    <view class="user-list-container" wx:if="{{!selectedUser || screenWidth > 768}}">
      <!-- 顶部筛选栏 -->
      <view class="filter-bar">
        <view class="filter-item">
          <picker range="{{statusOptions}}" range-key="label" value="{{filterStatus}}" bindchange="onStatusFilterChange">
            <view class="picker-display">
              {{statusOptions[filterStatus].label || '全部'}}
              <image class="picker-arrow" src="/static/箭头.png" mode="aspectFit"></image>
            </view>
          </picker>
        </view>
        <view class="total-count">
          <text>共 {{totalCount}} 位用户</text>
        </view>
      </view>

      <!-- 用户列表 -->
      <scroll-view class="user-list" 
                  scroll-y="true" 
                  refresher-enabled="true"
                  refresher-triggered="{{refreshing}}"
                  bindrefresherrefresh="onPullDownRefresh"
                  bindscrolltolower="onReachBottom">
        
        <!-- 加载状态 -->
        <view class="loading-container" wx:if="{{loading && users.length === 0}}">
          <view class="loading-spinner"></view>
          <text class="loading-text">加载中...</text>
        </view>
        
        <!-- 空状态 -->
        <view class="empty-container" wx:elif="{{users.length === 0}}">
          <image class="empty-icon" src="/static/空状态图标.png" mode="aspectFit"></image>
          <text class="empty-text">暂无用户反馈</text>
        </view>
        
        <!-- 用户列表项 -->
        <view class="user-item {{selectedUserId === item.userId ? 'active' : ''}}" 
              wx:for="{{users}}" 
              wx:key="userId"
              bindtap="selectUser" 
              data-user="{{item}}">
          
          <view class="user-avatar-container">
            <image class="user-avatar" 
                  src="{{item.avatarUrl || '/static/default-avatar.png'}}" 
                  mode="aspectFill"></image>
            <view class="unread-badge" wx:if="{{item.unreadCount > 0}}">{{item.unreadCount}}</view>
          </view>
          
          <view class="user-info">
            <view class="user-name-row">
              <text class="user-name">{{item.isAnonymous ? '匿名用户' : (item.nickName || '用户')}}</text>
              <text class="last-time">{{item.lastTime}}</text>
            </view>
            <view class="last-message-row">
              <text class="last-message-preview">{{item.lastMessage || '暂无消息'}}</text>
              <view class="status-indicator {{item.status}}"></view>
            </view>
          </view>
        </view>
        
        <!-- 加载更多 -->
        <view class="load-more" wx:if="{{loading && users.length > 0}}">
          <view class="loading-spinner small"></view>
          <text class="loading-text">加载更多...</text>
        </view>
        
        <!-- 没有更多 -->
        <view class="no-more" wx:if="{{!hasMore && users.length > 0}}">
          <text>没有更多用户了</text>
        </view>
      </scroll-view>
    </view>
    
    <!-- 右侧聊天区域 - 简化版 -->
    <view class="chat-area {{keyboardHeight > 0 ? 'keyboard-active' : ''}}" wx:if="{{selectedUser}}">
      <!-- 聊天头部 - 修改后的顶部导航栏，只显示用户名称，动态设置安全距离 -->
      <view class="chat-header">
        <view class="chat-header-content" style="margin-top: {{headerPadding}}px;">
          <view class="back-btn" bindtap="backToUserList">
            <!-- 修改为黑色箭头 -->
            <text class="back-arrow">←</text>
          </view>
          <view class="chat-user-name">
            {{selectedUser.isAnonymous ? '匿名用户' : (selectedUser.nickName || '用户')}}
          </view>
        </view>
      </view>
      
      <!-- 聊天消息区域 -->
      <scroll-view class="chat-messages {{keyboardHeight > 0 ? 'keyboard-shown' : ''}}" 
                   scroll-y="true" 
                   scroll-into-view="{{scrollToMessage}}"
                   scroll-with-animation="true"
                   enhanced="true"
                   show-scrollbar="false"
                   bindfocus="onMessagesFocus"
                   bindkeyboardheightchange="onKeyboardHeightChange">
        <!-- 加载状态 -->
        <view class="loading-container" wx:if="{{loading && !conversations.length}}">
          <view class="loading-spinner"></view>
          <text class="loading-text">加载中...</text>
        </view>
        
        <!-- 空状态 - 无消息 -->
        <view class="empty-chat-container" wx:elif="{{!conversations.length}}">
          <image class="empty-icon" src="/static/空状态图标.png" mode="aspectFit"></image>
          <text class="empty-text">暂无聊天记录</text>
          <text class="empty-subtext">发送消息开始对话吧</text>
        </view>
        
        <!-- 消息列表 -->
        <view class="message-list" wx:else>
          <block wx:for="{{conversations}}" wx:key="id">
            <!-- 时间分割线 -->
            <view class="time-divider" wx:if="{{item.showTimeHeader}}">
              <text class="time-text">{{item.createTimeStr}}</text>
            </view>
            
            <!-- 消息气泡 -->
            <view id="msg-{{item.id}}" class="message-item {{item.sender === 'user' ? 'user-message' : 'admin-message'}}">
              <view class="message-avatar-container">
                <image class="message-avatar" 
                       src="{{item.sender === 'user' ? (selectedUser.avatarUrl || '/static/default-avatar.png') : '/static/logo.png'}}" 
                       mode="aspectFill"></image>
              </view>
              <view class="message-content">
                <!-- 文本内容 - 仅当没有图片且有内容时显示 -->
                <view class="message-bubble" wx:if="{{!(item.images && item.images.length > 0) && item.content}}">
                  <text class="message-text">{{item.content}}</text>
                </view>
                
                <!-- 图片内容 -->
                <view class="message-images" wx:if="{{item.images && item.images.length > 0}}">
                  <image wx:for="{{item.images}}" 
                         wx:for-item="img" 
                         wx:key="*this"
                         class="message-image" 
                         src="{{img}}" 
                         mode="aspectFill"
                         bindtap="previewImage"
                         data-url="{{img}}"
                         data-urls="{{item.images}}"></image>
                </view>
                
                <!-- 视频内容 -->
                <view class="message-videos" wx:if="{{item.videos && item.videos.length > 0}}">
                  <video wx:for="{{item.videos}}" 
                         wx:for-item="video" 
                         wx:key="*this"
                         class="message-video" 
                         src="{{video}}" 
                         controls></video>
                </view>
                
                <text class="message-time">{{item.createTimeStr}}</text>
              </view>
            </view>
          </block>
          
          <!-- 底部安全距离 - 调整为动态高度 -->
          <view class="safe-bottom-area" style="height: {{keyboardHeight > 0 ? (keyboardHeight + 120) + 'px' : '100rpx'}};"></view>
        </view>
      </scroll-view>
      
      <!-- 输入区域 - 添加固定定位以适应键盘弹出，并使用安全区域高度 -->
      <view class="chat-input-area {{keyboardHeight > 0 ? 'keyboard-active' : ''}}" style="{{keyboardHeight > 0 ? 'bottom: ' + keyboardHeight + 'px;' : 'padding-bottom: ' + (safeAreaBottom > 0 ? safeAreaBottom + 'px' : 'env(safe-area-inset-bottom, 34rpx);')}}">
        <!-- 添加媒体按钮 - 居中显示 -->
        <view class="media-btn" bindtap="chooseMedia">
          <text class="media-icon">+</text>
        </view>
        <view class="input-container">
          <textarea class="message-input" 
                   placeholder="输入回复内容..." 
                   value="{{replyContent}}"
                   bindinput="onReplyInput"
                   maxlength="500"
                   adjust-position="{{false}}"
                   bindfocus="onInputFocus"
                   bindblur="onInputBlur"
                   show-confirm-bar="{{false}}"
                   cursor-spacing="20"
                   hold-keyboard="{{true}}"
                   disable-default-padding="{{true}}"
                   auto-height></textarea>
        </view>
        <view class="send-btn {{replyContent ? 'active' : ''}}" bindtap="sendReply">发送</view>
      </view>
    </view>
    
    <!-- 首次进入提示 -->
    <view class="welcome-container" wx:if="{{!selectedUser && users.length > 0 && screenWidth > 768}}">
      <image class="welcome-icon" src="/static/空状态图标.png" mode="aspectFit"></image>
      <text class="welcome-text">请选择左侧用户开始回复</text>
    </view>
  </view>
</view>