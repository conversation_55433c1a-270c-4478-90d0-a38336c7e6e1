// 获取全局app实例
const app = getApp();

Component({
  properties: {
    // 默认提供4个大类，名称修改为四个字
    categories: {
      type: Array,
      value: [
        { id: 'all', name: '全部类别' },
        { id: 'type1', name: '喷绘作品' },
        { id: 'type2', name: '文创产品' },
        { id: 'type3', name: '精美饰品' },
        { id: 'type4', name: '商品展示' }
      ],
      observer(newVal) {
        // 当外部属性变更时，更新内部状态
        if (newVal && newVal.length > 0 && app && app.globalData && app.globalData.debugMode) {
          console.log('分类数据更新:', newVal);
        }
      }
    },
    // 当前选中的分类ID
    currentCategoryId: {
      type: String,
      value: 'all'
    }
  },

  data: {
    statusBarHeight: 20,
    navigationBarHeight: 44,
    menuButtonInfo: {},
    safeAreaWidth: '280px', // 默认安全宽度
    extraBottomPadding: 8 // 底部额外边距
  },

  lifetimes: {
    attached() {
      // 使用新的API获取系统信息
      const windowInfo = wx.getWindowInfo();
      const menuButtonInfo = wx.getMenuButtonBoundingClientRect();
      
      // 计算安全区域宽度（屏幕宽度减去胶囊左边距再减去右边安全距离）
      const safeWidth = menuButtonInfo.left - 32; // 预留安全距离
      
      // 计算导航栏高度，使底部与胶囊按钮底部对齐
      // 计算方式: 状态栏高度 + 胶囊到状态栏底部的距离 + 胶囊自身高度
      const navigationBarHeight = menuButtonInfo.height;
      const navBarTop = menuButtonInfo.top - windowInfo.statusBarHeight;
      
      // 添加底部额外边距
      const extraBottomPadding = 8;
      
      this.setData({
        statusBarHeight: windowInfo.statusBarHeight,
        menuButtonInfo,
        navigationBarHeight: navigationBarHeight, // 使用胶囊按钮的高度
        navBarTop: navBarTop, // 记录导航到状态栏的距离
        safeAreaWidth: safeWidth + 'px',
        extraBottomPadding: extraBottomPadding // 底部额外边距
      });
      
      // 只在调试模式下输出胶囊和导航栏信息
      if (app && app.globalData && app.globalData.debugMode) {
        console.log('胶囊位置:', menuButtonInfo);
        console.log('状态栏高度:', windowInfo.statusBarHeight);
        console.log('导航栏高度:', navigationBarHeight);
        console.log('导航栏到状态栏的距离:', navBarTop);
        console.log('安全宽度:', safeWidth);
      }
    }
  },

  methods: {
    // 点击分类
    onCategoryTap(e) {
      const id = e.currentTarget.dataset.id;
      this.setData({ currentCategoryId: id });
      // 通知父组件分类已改变
      this.triggerEvent('categorychange', { categoryId: id });
    }
  }
}); 