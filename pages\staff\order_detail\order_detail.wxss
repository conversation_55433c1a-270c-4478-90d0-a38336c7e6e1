/* 员工订单详情页面样式 */
.order-detail-container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 顶部状态栏样式 */
.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  padding-top: 90rpx;
  background-color: #ffffff;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.back-icon {
  font-size: 40rpx;
  color: #333333;
  width: 60rpx;
}

.header-title {
  font-size: 34rpx;
  font-weight: bold;
  color: #333333;
  flex: 1;
  text-align: center;
}

.placeholder {
  width: 60rpx;
}

/* 内容区域样式 */
.content {
  padding: 30rpx;
  padding-bottom: 60rpx;
}

/* 加载中样式 */
.loading {
  padding: 100rpx 0;
  text-align: center;
}

.loading-icon {
  display: inline-block;
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #ff6b81;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #999999;
  margin-top: 20rpx;
}

/* 订单状态样式 */
.status-section {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 40rpx 30rpx;
  margin-bottom: 30rpx;
  text-align: center;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.status-text {
  font-size: 40rpx;
  font-weight: bold;
  color: #ff6b81;
  margin-bottom: 10rpx;
}

.order-number {
  font-size: 26rpx;
  color: #666666;
}

/* 详情卡片样式 */
.detail-card {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.card-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 20rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.info-item {
  display: flex;
  margin-bottom: 20rpx;
}

.info-label {
  width: 160rpx;
  font-size: 28rpx;
  color: #666666;
}

.info-value {
  flex: 1;
  font-size: 28rpx;
  color: #333333;
}

/* 电话样式 */
.phone-item {
  cursor: pointer;
}

.phone-value {
  color: #3366cc;
  display: flex;
  align-items: center;
}

.call-icon {
  margin-left: 10rpx;
  font-size: 26rpx;
}

/* 价格样式 */
.price, .total-price {
  color: #333333;
  font-weight: bold;
}

/* 提成金额样式 */
.info-value.commission {
  color: #ff6b81;
  font-weight: bold;
  font-size: 30rpx;
}

/* 摘要样式 */
.summary-item {
  margin-top: 20rpx;
  padding-top: 20rpx;
  border-top: 1rpx dashed #f0f0f0;
}

.summary-text {
  font-size: 26rpx;
  color: #666666;
  text-align: center;
  width: 100%;
}

/* 操作按钮样式 */
.action-section {
  margin-top: 50rpx;
  padding: 0 30rpx;
}

.action-btn {
  background-color: #ff6b81;
  color: #ffffff;
  font-size: 32rpx;
  font-weight: bold;
  padding: 20rpx 0;
  border-radius: 40rpx;
  text-align: center;
}

/* 业绩信息 */
.total-price {
  color: #ff6b6b;
  font-weight: 500;
}

.commission {
  color: #4ecdc4;
  font-weight: 500;
}

.summary-item {
  border-top: 1px dashed #eaeaea;
  padding-top: 15rpx;
  margin-top: 10rpx;
}

.summary-text {
  font-size: 24rpx;
  color: #888;
  width: 100%;
  text-align: center;
}

/* 图片预览区域 */
.image-gallery {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  margin-top: 20rpx;
}

.reference-image {
  width: 200rpx;
  height: 200rpx;
  border-radius: 8rpx;
  object-fit: cover;
} 