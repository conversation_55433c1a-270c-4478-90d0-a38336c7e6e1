/**
 * 统一错误处理模块
 * 负责收集、分析、处理和恢复各种错误，提供统一的错误处理机制
 * @version 1.0.0
 */

const BaseModule = require("./base-module");
const {
  ERROR_TYPES,
  ERROR_MESSAGES,
  MODULE_EVENTS,
} = require("../constants/index-constants");

class UnifiedErrorHandler extends BaseModule {
  constructor(pageContext) {
    super(pageContext);
    this.moduleName = "UnifiedErrorHandler";

    // 错误收集器
    this.errorCollector = {
      errors: [],
      maxErrors: 100,
      errorCounts: new Map(),
      errorPatterns: new Map(),
    };

    // 错误处理策略
    this.errorStrategies = new Map();

    // 错误恢复机制
    this.recoveryMechanisms = new Map();

    // 错误监控配置
    this.monitoringConfig = {
      enableAutoRecovery: true,
      enableErrorReporting: true,
      enableUserNotification: true,
      maxRetryAttempts: 3,
      retryDelay: 1000,
      errorThreshold: 10,
    };

    // 错误统计
    this.errorStats = {
      totalErrors: 0,
      recoveredErrors: 0,
      unrecoveredErrors: 0,
      errorsByType: new Map(),
      errorsByModule: new Map(),
    };

    // 用户通知管理
    this.userNotifications = {
      lastNotificationTime: 0,
      notificationCooldown: 5000, // 5秒冷却时间
      suppressedErrors: new Set(),
    };
  }

  /**
   * 初始化统一错误处理器
   */
  init() {
    try {
      console.log("[UnifiedErrorHandler] 初始化统一错误处理器");

      // 设置错误处理策略
      this.setupErrorStrategies();

      // 设置错误恢复机制
      this.setupRecoveryMechanisms();

      // 设置全局错误监听
      this.setupGlobalErrorListeners();

      // 启动错误监控
      this.startErrorMonitoring();

      this.initialized = true;
      console.log("[UnifiedErrorHandler] 统一错误处理器初始化完成");
    } catch (error) {
      console.error("[UnifiedErrorHandler] 初始化失败:", error);
      throw error;
    }
  }

  /**
   * 设置错误处理策略
   */
  setupErrorStrategies() {
    try {
      // 网络错误处理策略
      this.errorStrategies.set(ERROR_TYPES.NETWORK_ERROR, {
        priority: 1,
        autoRetry: true,
        maxRetries: 3,
        retryDelay: 2000,
        userNotification: true,
        recovery: "networkRecovery",
      });

      // 数据错误处理策略
      this.errorStrategies.set(ERROR_TYPES.DATA_ERROR, {
        priority: 2,
        autoRetry: true,
        maxRetries: 2,
        retryDelay: 1000,
        userNotification: true,
        recovery: "dataRecovery",
      });

      // UI错误处理策略
      this.errorStrategies.set(ERROR_TYPES.UI_ERROR, {
        priority: 3,
        autoRetry: false,
        maxRetries: 0,
        retryDelay: 0,
        userNotification: false,
        recovery: "uiRecovery",
      });

      // 模块错误处理策略
      this.errorStrategies.set(ERROR_TYPES.MODULE_ERROR, {
        priority: 1,
        autoRetry: true,
        maxRetries: 2,
        retryDelay: 1500,
        userNotification: false,
        recovery: "moduleRecovery",
      });

      // 视频播放错误处理策略
      this.errorStrategies.set(ERROR_TYPES.VIDEO_PLAY_ERROR, {
        priority: 2,
        autoRetry: true,
        maxRetries: 2,
        retryDelay: 1000,
        userNotification: true,
        recovery: "videoRecovery",
      });

      // 搜索错误处理策略
      this.errorStrategies.set(ERROR_TYPES.SEARCH_ERROR, {
        priority: 3,
        autoRetry: true,
        maxRetries: 1,
        retryDelay: 500,
        userNotification: false,
        recovery: "searchRecovery",
      });

      // 通用错误处理策略
      this.errorStrategies.set(ERROR_TYPES.GENERIC_ERROR, {
        priority: 4,
        autoRetry: false,
        maxRetries: 1,
        retryDelay: 1000,
        userNotification: true,
        recovery: "genericRecovery",
      });

      // console.log("[UnifiedErrorHandler] 错误处理策略设置完成");
    } catch (error) {
      console.error("[UnifiedErrorHandler] 设置错误处理策略失败:", error);
    }
  }

  /**
   * 设置错误恢复机制
   */
  setupRecoveryMechanisms() {
    try {
      // 网络错误恢复
      this.recoveryMechanisms.set("networkRecovery", async (error, context) => {
        console.log("[UnifiedErrorHandler] 执行网络错误恢复");

        // 检查网络状态
        const networkStatus = await this.checkNetworkStatus();
        if (!networkStatus.isConnected) {
          // 等待网络恢复
          return this.waitForNetworkRecovery();
        }

        // 重试网络请求
        return this.retryNetworkOperation(context);
      });

      // 数据错误恢复
      this.recoveryMechanisms.set("dataRecovery", async (error, context) => {
        console.log("[UnifiedErrorHandler] 执行数据错误恢复");

        // 尝试从缓存恢复数据
        const cachedData = await this.recoverFromCache(context);
        if (cachedData) {
          return { success: true, data: cachedData };
        }

        // 重新加载数据
        return this.reloadData(context);
      });

      // UI错误恢复
      this.recoveryMechanisms.set("uiRecovery", async (error, context) => {
        console.log("[UnifiedErrorHandler] 执行UI错误恢复");

        // 重置UI状态
        await this.resetUIState(context);

        // 重新渲染UI
        return this.reRenderUI(context);
      });

      // 模块错误恢复
      this.recoveryMechanisms.set("moduleRecovery", async (error, context) => {
        console.log("[UnifiedErrorHandler] 执行模块错误恢复");

        // 重启模块
        return this.restartModule(context);
      });

      // 视频播放错误恢复
      this.recoveryMechanisms.set("videoRecovery", async (error, context) => {
        console.log("[UnifiedErrorHandler] 执行视频播放错误恢复");

        // 重新获取视频URL
        const newUrl = await this.refreshVideoUrl(context);
        if (newUrl) {
          return { success: true, videoUrl: newUrl };
        }

        // 使用备用视频源
        return this.useBackupVideoSource(context);
      });

      // 搜索错误恢复
      this.recoveryMechanisms.set("searchRecovery", async (error, context) => {
        console.log("[UnifiedErrorHandler] 执行搜索错误恢复");

        // 清理搜索状态
        await this.clearSearchState();

        // 重新初始化搜索
        return this.reinitializeSearch();
      });

      // 通用错误恢复
      this.recoveryMechanisms.set("genericRecovery", async (error, context) => {
        console.log("[UnifiedErrorHandler] 执行通用错误恢复");

        // 记录错误
        const errorInfo = this.collectErrorInfo(error, context);
        this.recordError(errorInfo);

        // 清理可能的问题状态
        await this.cleanupProblemState();

        return { success: false, message: "已记录错误，请稍后重试" };
      });

      // console.log("[UnifiedErrorHandler] 错误恢复机制设置完成");
    } catch (error) {
      console.error("[UnifiedErrorHandler] 设置错误恢复机制失败:", error);
    }
  }

  /**
   * 设置全局错误监听
   */
  setupGlobalErrorListeners() {
    try {
      // 监听未捕获的Promise错误
      if (typeof wx.onUnhandledRejection === "function") {
        wx.onUnhandledRejection((res) => {
          console.error("[UnifiedErrorHandler] 未处理的Promise拒绝:", res);
          this.handleError(res.reason, {
            type: "unhandled_promise_rejection",
            source: "global",
          });
        });
      }

      // 监听页面错误
      if (typeof wx.onError === "function") {
        wx.onError((error) => {
          console.error("[UnifiedErrorHandler] 页面错误:", error);
          this.handleError(new Error(error), {
            type: "page_error",
            source: "global",
          });
        });
      }

      // 监听网络状态变化
      wx.onNetworkStatusChange((res) => {
        if (!res.isConnected) {
          this.handleError(new Error("网络连接断开"), {
            type: ERROR_TYPES.NETWORK_ERROR,
            source: "network_status_change",
            networkType: res.networkType,
          });
        } else {
          console.log("[UnifiedErrorHandler] 网络已恢复:", res.networkType);
          this.handleNetworkRecovery(res);
        }
      });

      console.log("[UnifiedErrorHandler] 全局错误监听设置完成");
    } catch (error) {
      console.error("[UnifiedErrorHandler] 设置全局错误监听失败:", error);
    }
  }

  /**
   * 启动错误监控
   */
  startErrorMonitoring() {
    try {
      // 定期检查错误统计
      this.monitoringTimer = setInterval(() => {
        this.performErrorAnalysis();
      }, 30000); // 每30秒分析一次

      // 定期清理过期错误
      this.cleanupTimer = setInterval(() => {
        this.cleanupExpiredErrors();
      }, 60000); // 每分钟清理一次

      console.log("[UnifiedErrorHandler] 错误监控已启动");
    } catch (error) {
      console.error("[UnifiedErrorHandler] 启动错误监控失败:", error);
    }
  }

  /**
   * 处理错误（主要入口方法）
   * @param {Error} error - 错误对象
   * @param {object} context - 错误上下文
   * @returns {Promise<object>} 处理结果
   */
  async handleError(error, context = {}) {
    try {
      console.log("[UnifiedErrorHandler] 处理错误:", error.message, context);

      // 收集错误信息
      const errorInfo = this.collectErrorInfo(error, context);

      // 记录错误
      this.recordError(errorInfo);

      // 分析错误类型
      const errorType = this.analyzeErrorType(error, context);

      // 获取处理策略
      const strategy = this.getErrorStrategy(errorType);

      // 检查是否需要用户通知
      if (strategy.userNotification && this.shouldNotifyUser(errorType)) {
        this.notifyUser(error, errorType);
      }

      // 执行错误恢复
      let recoveryResult = null;
      if (this.monitoringConfig.enableAutoRecovery) {
        recoveryResult = await this.executeRecovery(error, context, strategy);
      }

      // 更新错误统计
      this.updateErrorStats(errorType, recoveryResult);

      // 触发错误处理完成事件
      this.emit("errorHandled", {
        error: errorInfo,
        strategy: strategy,
        recovery: recoveryResult,
        timestamp: Date.now(),
      });

      return {
        success: recoveryResult ? recoveryResult.success : false,
        errorType: errorType,
        strategy: strategy,
        recovery: recoveryResult,
      };
    } catch (handlingError) {
      console.error("[UnifiedErrorHandler] 错误处理失败:", handlingError);
      return {
        success: false,
        error: handlingError.message,
      };
    }
  }

  /**
   * 收集错误信息
   * @param {Error} error - 错误对象
   * @param {object} context - 错误上下文
   * @returns {object} 错误信息
   */
  collectErrorInfo(error, context) {
    try {
      return {
        id: this.generateErrorId(),
        message: error.message || "未知错误",
        stack: error.stack || "",
        type: error.type || ERROR_TYPES.GENERIC_ERROR,
        code: error.code || "UNKNOWN",
        timestamp: Date.now(),
        context: {
          ...context,
          userAgent: wx.getSystemInfoSync ? wx.getSystemInfoSync() : {},
          pageRoute:
            getCurrentPages().length > 0
              ? getCurrentPages()[getCurrentPages().length - 1].route
              : "",
          appVersion: getApp().globalData
            ? getApp().globalData.version
            : "unknown",
        },
        severity: this.calculateErrorSeverity(error, context),
      };
    } catch (error) {
      console.error("[UnifiedErrorHandler] 收集错误信息失败:", error);
      return {
        id: Date.now().toString(),
        message: "错误信息收集失败",
        timestamp: Date.now(),
        severity: "medium",
      };
    }
  }

  /**
   * 生成错误ID
   * @returns {string} 错误ID
   */
  generateErrorId() {
    return `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 计算错误严重程度
   * @param {Error} error - 错误对象
   * @param {object} context - 错误上下文
   * @returns {string} 严重程度
   */
  calculateErrorSeverity(error, context) {
    try {
      // 根据错误类型判断严重程度
      if (error.type === ERROR_TYPES.MODULE_ERROR) {
        return "high";
      } else if (error.type === ERROR_TYPES.NETWORK_ERROR) {
        return "medium";
      } else if (error.type === ERROR_TYPES.UI_ERROR) {
        return "low";
      }

      // 根据错误消息判断
      const criticalKeywords = ["crash", "fatal", "critical", "system"];
      if (
        criticalKeywords.some((keyword) =>
          error.message.toLowerCase().includes(keyword)
        )
      ) {
        return "critical";
      }

      return "medium";
    } catch (error) {
      return "medium";
    }
  }

  /**
   * 记录错误
   * @param {object} errorInfo - 错误信息
   */
  recordError(errorInfo) {
    try {
      // 添加到错误收集器
      this.errorCollector.errors.push(errorInfo);

      // 限制错误数量
      if (this.errorCollector.errors.length > this.errorCollector.maxErrors) {
        this.errorCollector.errors.shift();
      }

      // 更新错误计数
      const errorKey = `${errorInfo.type}_${errorInfo.message}`;
      const currentCount = this.errorCollector.errorCounts.get(errorKey) || 0;
      this.errorCollector.errorCounts.set(errorKey, currentCount + 1);

      // 分析错误模式
      this.analyzeErrorPattern(errorInfo);

      // 如果启用错误报告，发送到服务器
      if (this.monitoringConfig.enableErrorReporting) {
        this.reportErrorToServer(errorInfo);
      }
    } catch (error) {
      console.error("[UnifiedErrorHandler] 记录错误失败:", error);
    }
  }

  /**
   * 分析错误类型
   * @param {Error} error - 错误对象
   * @param {object} context - 错误上下文
   * @returns {string} 错误类型
   */
  analyzeErrorType(error, context) {
    try {
      // 如果错误对象已经有类型，直接使用
      if (error.type && Object.values(ERROR_TYPES).includes(error.type)) {
        return error.type;
      }

      // 根据错误消息分析类型
      const message = error.message.toLowerCase();

      // 首先检查JavaScript错误类型
      if (
        error instanceof TypeError ||
        error instanceof ReferenceError ||
        message.includes("is not a function") ||
        message.includes("is not defined") ||
        message.includes("cannot read property")
      ) {
        return ERROR_TYPES.SCRIPT_ERROR;
      }

      if (
        message.includes("network") ||
        message.includes("request") ||
        message.includes("timeout") ||
        (message.includes("连接") && !message.includes("function"))
      ) {
        return ERROR_TYPES.NETWORK_ERROR;
      }

      if (
        message.includes("data") ||
        message.includes("parse") ||
        message.includes("json") ||
        message.includes("数据")
      ) {
        return ERROR_TYPES.DATA_ERROR;
      }

      if (
        message.includes("video") ||
        message.includes("play") ||
        message.includes("视频") ||
        message.includes("播放")
      ) {
        return ERROR_TYPES.VIDEO_PLAY_ERROR;
      }

      if (message.includes("search") || message.includes("搜索")) {
        return ERROR_TYPES.SEARCH_ERROR;
      }

      if (
        message.includes("module") ||
        message.includes("模块") ||
        context.source === "module"
      ) {
        return ERROR_TYPES.MODULE_ERROR;
      }

      if (
        message.includes("ui") ||
        message.includes("界面") ||
        context.source === "ui"
      ) {
        return ERROR_TYPES.UI_ERROR;
      }

      return ERROR_TYPES.GENERIC_ERROR;
    } catch (error) {
      return ERROR_TYPES.GENERIC_ERROR;
    }
  }

  /**
   * 获取错误处理策略
   * @param {string} errorType - 错误类型
   * @returns {object} 处理策略
   */
  getErrorStrategy(errorType) {
    try {
      return (
        this.errorStrategies.get(errorType) ||
        this.errorStrategies.get(ERROR_TYPES.GENERIC_ERROR)
      );
    } catch (error) {
      console.error("[UnifiedErrorHandler] 获取错误策略失败:", error);
      return {
        priority: 4,
        autoRetry: false,
        maxRetries: 0,
        retryDelay: 0,
        userNotification: true,
        recovery: "genericRecovery",
      };
    }
  }

  /**
   * 检查是否需要通知用户
   * @param {string} errorType - 错误类型
   * @returns {boolean} 是否需要通知
   */
  shouldNotifyUser(errorType) {
    try {
      const now = Date.now();

      // 检查冷却时间
      if (
        now - this.userNotifications.lastNotificationTime <
        this.userNotifications.notificationCooldown
      ) {
        return false;
      }

      // 检查是否被抑制
      if (this.userNotifications.suppressedErrors.has(errorType)) {
        return false;
      }

      // 检查错误频率
      const errorCount = this.errorCollector.errorCounts.get(errorType) || 0;
      if (errorCount > 5) {
        // 频繁错误，抑制通知
        this.userNotifications.suppressedErrors.add(errorType);
        return false;
      }

      return true;
    } catch (error) {
      console.error("[UnifiedErrorHandler] 检查用户通知失败:", error);
      return false;
    }
  }

  /**
   * 通知用户
   * @param {Error} error - 错误对象
   * @param {string} errorType - 错误类型
   */
  notifyUser(error, errorType) {
    try {
      const message =
        ERROR_MESSAGES[errorType] || error.message || "操作失败，请重试";

      wx.showToast({
        title: message,
        icon: "none",
        duration: 2000,
      });

      // 更新通知时间
      this.userNotifications.lastNotificationTime = Date.now();

      console.log("[UnifiedErrorHandler] 用户通知已发送:", message);
    } catch (error) {
      console.error("[UnifiedErrorHandler] 发送用户通知失败:", error);
    }
  }

  /**
   * 执行错误恢复
   * @param {Error} error - 错误对象
   * @param {object} context - 错误上下文
   * @param {object} strategy - 处理策略
   * @returns {Promise<object>} 恢复结果
   */
  async executeRecovery(error, context, strategy) {
    try {
      console.log("[UnifiedErrorHandler] 执行错误恢复:", strategy.recovery);

      const recoveryMechanism = this.recoveryMechanisms.get(strategy.recovery);
      if (!recoveryMechanism) {
        console.warn(
          "[UnifiedErrorHandler] 未找到恢复机制:",
          strategy.recovery
        );
        return { success: false, message: "未找到恢复机制" };
      }

      // 执行恢复
      const result = await recoveryMechanism(error, context);

      console.log("[UnifiedErrorHandler] 错误恢复结果:", result);
      return result;
    } catch (recoveryError) {
      console.error("[UnifiedErrorHandler] 错误恢复失败:", recoveryError);
      return { success: false, error: recoveryError.message };
    }
  }

  /**
   * 更新错误统计
   * @param {string} errorType - 错误类型
   * @param {object} recoveryResult - 恢复结果
   */
  updateErrorStats(errorType, recoveryResult) {
    try {
      // 更新总错误数
      this.errorStats.totalErrors++;

      // 更新按类型统计
      const typeCount = this.errorStats.errorsByType.get(errorType) || 0;
      this.errorStats.errorsByType.set(errorType, typeCount + 1);

      // 更新恢复统计
      if (recoveryResult && recoveryResult.success) {
        this.errorStats.recoveredErrors++;
      } else {
        this.errorStats.unrecoveredErrors++;
      }
    } catch (error) {
      console.error("[UnifiedErrorHandler] 更新错误统计失败:", error);
    }
  }

  /**
   * 分析错误模式
   * @param {object} errorInfo - 错误信息
   */
  analyzeErrorPattern(errorInfo) {
    try {
      const patternKey = `${errorInfo.type}_${
        errorInfo.context.source || "unknown"
      }`;
      const pattern = this.errorCollector.errorPatterns.get(patternKey) || {
        count: 0,
        firstOccurrence: errorInfo.timestamp,
        lastOccurrence: errorInfo.timestamp,
        frequency: 0,
      };

      pattern.count++;
      pattern.lastOccurrence = errorInfo.timestamp;
      pattern.frequency =
        pattern.count /
        ((errorInfo.timestamp - pattern.firstOccurrence) / 60000); // 每分钟频率

      this.errorCollector.errorPatterns.set(patternKey, pattern);

      // 检查是否需要特殊处理
      if (pattern.frequency > 5) {
        // 每分钟超过5次
        console.warn(
          "[UnifiedErrorHandler] 检测到高频错误模式:",
          patternKey,
          pattern
        );
        this.handleHighFrequencyError(patternKey, pattern);
      }
    } catch (error) {
      console.error("[UnifiedErrorHandler] 分析错误模式失败:", error);
    }
  }

  /**
   * 处理高频错误
   * @param {string} patternKey - 模式键
   * @param {object} pattern - 错误模式
   */
  handleHighFrequencyError(patternKey, pattern) {
    try {
      console.log("[UnifiedErrorHandler] 处理高频错误:", patternKey);

      // 触发高频错误事件
      this.emit("highFrequencyError", {
        pattern: patternKey,
        data: pattern,
        timestamp: Date.now(),
      });

      // 可以在这里实现特殊的处理逻辑，比如：
      // 1. 临时禁用相关功能
      // 2. 切换到备用方案
      // 3. 发送紧急报告
    } catch (error) {
      console.error("[UnifiedErrorHandler] 处理高频错误失败:", error);
    }
  }

  /**
   * 执行错误分析
   */
  performErrorAnalysis() {
    try {
      console.log("[UnifiedErrorHandler] 执行错误分析");

      const analysis = {
        timestamp: Date.now(),
        totalErrors: this.errorStats.totalErrors,
        recoveryRate:
          this.errorStats.totalErrors > 0
            ? (
                (this.errorStats.recoveredErrors /
                  this.errorStats.totalErrors) *
                100
              ).toFixed(2) + "%"
            : "0%",
        topErrorTypes: this.getTopErrorTypes(),
        errorPatterns: Array.from(this.errorCollector.errorPatterns.entries()),
        recommendations: this.generateRecommendations(),
      };

      // 触发分析完成事件
      this.emit("errorAnalysisComplete", analysis);

      // 只在有实际错误时输出分析结果
      if (analysis.totalErrors > 0) {
        console.log(
          "[UnifiedErrorHandler] 发现错误:",
          analysis.totalErrors,
          "个"
        );
      }
    } catch (error) {
      console.error("[UnifiedErrorHandler] 执行错误分析失败:", error);
    }
  }

  /**
   * 获取最常见的错误类型
   * @returns {Array} 错误类型排序
   */
  getTopErrorTypes() {
    try {
      return Array.from(this.errorStats.errorsByType.entries())
        .sort((a, b) => b[1] - a[1])
        .slice(0, 5)
        .map(([type, count]) => ({ type, count }));
    } catch (error) {
      console.error("[UnifiedErrorHandler] 获取顶级错误类型失败:", error);
      return [];
    }
  }

  /**
   * 生成优化建议
   * @returns {Array} 建议列表
   */
  generateRecommendations() {
    try {
      const recommendations = [];

      // 基于错误统计生成建议
      if (this.errorStats.errorsByType.get(ERROR_TYPES.NETWORK_ERROR) > 10) {
        recommendations.push({
          type: "network_optimization",
          message: "网络错误频繁，建议优化网络请求重试机制",
          priority: "high",
        });
      }

      if (this.errorStats.errorsByType.get(ERROR_TYPES.MODULE_ERROR) > 5) {
        recommendations.push({
          type: "module_stability",
          message: "模块错误较多，建议检查模块初始化和依赖关系",
          priority: "medium",
        });
      }

      if (
        this.errorStats.unrecoveredErrors / this.errorStats.totalErrors >
        0.3
      ) {
        recommendations.push({
          type: "recovery_improvement",
          message: "错误恢复率较低，建议完善恢复机制",
          priority: "high",
        });
      }

      return recommendations;
    } catch (error) {
      console.error("[UnifiedErrorHandler] 生成建议失败:", error);
      return [];
    }
  }

  /**
   * 清理过期错误
   */
  cleanupExpiredErrors() {
    try {
      const now = Date.now();
      const expireTime = 24 * 60 * 60 * 1000; // 24小时

      // 清理过期错误记录
      this.errorCollector.errors = this.errorCollector.errors.filter(
        (error) => now - error.timestamp < expireTime
      );

      // 清理过期错误计数
      this.errorCollector.errorCounts.forEach((count, key) => {
        // 这里可以实现更复杂的清理逻辑
      });

      // 重置抑制的错误类型（每小时重置一次）
      if (now % (60 * 60 * 1000) < 60000) {
        this.userNotifications.suppressedErrors.clear();
      }

      console.log("[UnifiedErrorHandler] 过期错误清理完成");
    } catch (error) {
      console.error("[UnifiedErrorHandler] 清理过期错误失败:", error);
    }
  }

  // ==================== 恢复机制实现 ====================

  /**
   * 检查网络状态
   * @returns {Promise<object>} 网络状态
   */
  async checkNetworkStatus() {
    return new Promise((resolve) => {
      wx.getNetworkType({
        success: (res) => {
          resolve({
            isConnected: res.networkType !== "none",
            networkType: res.networkType,
          });
        },
        fail: () => {
          resolve({ isConnected: false, networkType: "unknown" });
        },
      });
    });
  }

  /**
   * 等待网络恢复
   * @returns {Promise<object>} 恢复结果
   */
  async waitForNetworkRecovery() {
    return new Promise((resolve) => {
      const checkInterval = setInterval(async () => {
        const status = await this.checkNetworkStatus();
        if (status.isConnected) {
          clearInterval(checkInterval);
          resolve({ success: true, message: "网络已恢复" });
        }
      }, 2000);

      // 30秒超时
      setTimeout(() => {
        clearInterval(checkInterval);
        resolve({ success: false, message: "网络恢复超时" });
      }, 30000);
    });
  }

  /**
   * 重试网络操作
   * @param {object} context - 上下文
   * @returns {Promise<object>} 重试结果
   */
  async retryNetworkOperation(context) {
    try {
      // 这里需要根据具体的网络操作进行重试
      // 由于这是通用的错误处理器，具体的重试逻辑应该由调用方提供
      console.log("[UnifiedErrorHandler] 重试网络操作");
      return { success: true, message: "网络操作重试成功" };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  /**
   * 从缓存恢复数据
   * @param {object} context - 上下文
   * @returns {Promise<any>} 缓存数据
   */
  async recoverFromCache(context) {
    try {
      // 尝试从本地存储恢复数据
      const cacheKey = context.cacheKey || "fallback_data";
      const cachedData = wx.getStorageSync(cacheKey);

      if (cachedData) {
        console.log("[UnifiedErrorHandler] 从缓存恢复数据成功");
        return cachedData;
      }

      return null;
    } catch (error) {
      console.error("[UnifiedErrorHandler] 从缓存恢复数据失败:", error);
      return null;
    }
  }

  /**
   * 重新加载数据
   * @param {object} context - 上下文
   * @returns {Promise<object>} 加载结果
   */
  async reloadData(context) {
    try {
      console.log("[UnifiedErrorHandler] 重新加载数据");
      // 这里需要根据具体情况实现数据重新加载
      return { success: true, message: "数据重新加载成功" };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  /**
   * 重置UI状态
   * @param {object} context - 上下文
   * @returns {Promise<void>}
   */
  async resetUIState(context) {
    try {
      console.log("[UnifiedErrorHandler] 重置UI状态");

      // 重置基本UI状态
      this.page.setData({
        loading: false,
        showContent: true,
        error: null,
      });
    } catch (error) {
      console.error("[UnifiedErrorHandler] 重置UI状态失败:", error);
    }
  }

  /**
   * 重新渲染UI
   * @param {object} context - 上下文
   * @returns {Promise<object>} 渲染结果
   */
  async reRenderUI(context) {
    try {
      console.log("[UnifiedErrorHandler] 重新渲染UI");

      // 强制重新渲染
      this.page.setData({});

      return { success: true, message: "UI重新渲染成功" };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  /**
   * 重启模块
   * @param {object} context - 上下文
   * @returns {Promise<object>} 重启结果
   */
  async restartModule(context) {
    try {
      console.log("[UnifiedErrorHandler] 重启模块");

      // 这里需要与主控制器协作来重启模块
      // 触发模块重启事件
      this.emit("moduleRestartRequest", {
        moduleName: context.moduleName,
        reason: "error_recovery",
      });

      return { success: true, message: "模块重启请求已发送" };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  /**
   * 获取错误统计
   * @returns {object} 错误统计信息
   */
  getErrorStats() {
    try {
      return {
        ...this.errorStats,
        errorsByType: Object.fromEntries(this.errorStats.errorsByType),
        errorsByModule: Object.fromEntries(this.errorStats.errorsByModule),
        recentErrors: this.errorCollector.errors.slice(-10),
        errorPatterns: Object.fromEntries(this.errorCollector.errorPatterns),
      };
    } catch (error) {
      console.error("[UnifiedErrorHandler] 获取错误统计失败:", error);
      return {};
    }
  }

  /**
   * 报告错误到服务器
   * @param {object} errorInfo - 错误信息
   */
  async reportErrorToServer(errorInfo) {
    try {
      // 这里实现向服务器报告错误的逻辑
      console.log("[UnifiedErrorHandler] 报告错误到服务器:", errorInfo.id);

      // 由于这是示例，这里只是记录日志
      // 实际实现中应该发送HTTP请求到错误收集服务
    } catch (error) {
      console.error("[UnifiedErrorHandler] 报告错误到服务器失败:", error);
    }
  }

  /**
   * 处理网络恢复
   * @param {object} networkInfo - 网络信息
   */
  handleNetworkRecovery(networkInfo) {
    try {
      console.log("[UnifiedErrorHandler] 处理网络恢复:", networkInfo);

      // 触发网络恢复事件
      this.emit("networkRecovered", {
        networkType: networkInfo.networkType,
        timestamp: Date.now(),
      });

      // 清除网络相关的错误抑制
      this.userNotifications.suppressedErrors.delete(ERROR_TYPES.NETWORK_ERROR);
    } catch (error) {
      console.error("[UnifiedErrorHandler] 处理网络恢复失败:", error);
    }
  }

  /**
   * 销毁错误处理器
   */
  destroy() {
    try {
      console.log("[UnifiedErrorHandler] 销毁统一错误处理器");

      // 清理定时器
      if (this.monitoringTimer) {
        clearInterval(this.monitoringTimer);
        this.monitoringTimer = null;
      }

      if (this.cleanupTimer) {
        clearInterval(this.cleanupTimer);
        this.cleanupTimer = null;
      }

      // 清理数据
      this.errorCollector.errors = [];
      this.errorCollector.errorCounts.clear();
      this.errorCollector.errorPatterns.clear();
      this.errorStrategies.clear();
      this.recoveryMechanisms.clear();
      this.userNotifications.suppressedErrors.clear();

      super.destroy();
    } catch (error) {
      console.error("[UnifiedErrorHandler] 销毁错误处理器失败:", error);
    }
  }
}

module.exports = UnifiedErrorHandler;
