const app = getApp()

Page({
  data: {
    services: [],
    loading: true,
    showAddForm: false,
    showEditForm: false,
    currentService: null,
    formData: {
      name: '',
      price: '',
      originalPrice: '',
      description: '',
      image: '/static/default-service.png',
      detailImage: ''
    },
    uploadPath: 'service-images/',
    tempImagePath: '',
    tempDetailImagePath: ''
  },

  onLoad() {
    this.fetchServices();
  },

  // 处理下拉刷新
  onPullDownRefresh() {
    console.log('下拉刷新触发');
    // 重新获取服务列表
    this.fetchServices().then(() => {
      // 停止下拉刷新
      wx.stopPullDownRefresh();
      wx.showToast({
        title: '刷新成功',
        icon: 'success',
        duration: 1000
      });
    });
  },

  // 获取服务列表
  fetchServices() {
    this.setData({ loading: true });
    console.log('开始获取服务列表...');
    
    return new Promise((resolve, reject) => {
      wx.cloud.callFunction({
        name: 'appointmentManager',
        data: {
          action: 'getServices',
          type: 'admin'
        },
        success: res => {
          console.log('获取服务列表成功', res);
          
          if (res.result && res.result.success) {
            const services = res.result.data || [];
            console.log('服务列表数据:', services);
            
            // 打印每个服务的更新时间，便于调试
            if (services.length > 0) {
              console.log('服务排序情况:');
              services.forEach((service, index) => {
                console.log(`${index + 1}. ${service.name} - 更新时间: ${new Date(service.updateTime).toLocaleString()} - 可见性: ${service.isVisible ? '可见' : '隐藏'}`);
              });
            }
            
            this.setData({
              services: services,
              loading: false
            });
            resolve(services);
          } else {
            this.setData({
              services: [],
              loading: false
            });
            
            wx.showToast({
              title: '获取服务列表失败',
              icon: 'none'
            });
            reject(new Error('获取服务列表失败'));
          }
        },
        fail: err => {
          console.error('获取服务列表失败', err);
          
          this.setData({
            services: [],
            loading: false
          });
          
          wx.showToast({
            title: '网络异常，请稍后重试',
            icon: 'none'
          });
          reject(err);
        }
      });
    });
  },

  // 获取管理员权限
  getAdminPermission() {
    wx.showLoading({
      title: '获取权限中...',
    });
    
    wx.cloud.callFunction({
      name: 'appointmentManager',
      data: {
        action: 'addAdminPermission'
      },
      success: res => {
        wx.hideLoading();
        
        console.log('获取管理员权限结果:', res);
        
        if (res.result && res.result.success) {
          wx.showToast({
            title: res.result.message,
            icon: 'success'
          });
          
          // 重新获取服务列表
          this.fetchServices();
        } else {
          wx.showToast({
            title: res.result?.message || '获取权限失败',
            icon: 'none'
          });
        }
      },
      fail: err => {
        wx.hideLoading();
        console.error('获取管理员权限失败', err);
        
        wx.showToast({
          title: '网络异常，请稍后重试',
          icon: 'none'
        });
      }
    });
  },

  // 显示添加表单
  showAddForm() {
    this.setData({
      showAddForm: true,
      formData: {
        name: '',
        price: '',
        originalPrice: '',
        description: '',
        image: '/static/default-service.png',
        detailImage: ''
      },
      tempImagePath: '',
      tempDetailImagePath: ''
    });
  },

  // 显示编辑表单
  showEditForm(e) {
    const { service } = e.currentTarget.dataset;
    
    // 设置表单数据
    this.setData({
      showEditForm: true,
      currentService: service,
      formData: {
        name: service.name,
        price: service.price,
        originalPrice: service.originalPrice || '',
        description: service.description || '',
        image: service.image || '/static/default-service.png',
        detailImage: service.detailImage || ''
      },
      tempImagePath: '',
      tempDetailImagePath: ''
    });
  },

  // 关闭表单
  closeForm() {
    this.setData({
      showAddForm: false,
      showEditForm: false,
      currentService: null,
      formData: {
        name: '',
        price: '',
        originalPrice: '',
        description: '',
        image: '/static/default-service.png',
        detailImage: ''
      },
      tempImagePath: '',
      tempDetailImagePath: ''
    });
  },

  // 表单输入
  inputChange(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;
    
    this.setData({
      [`formData.${field}`]: value
    });
  },



  // 选择图片
  chooseImage() {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: res => {
        const tempFilePath = res.tempFilePaths[0];

        this.setData({
          tempImagePath: tempFilePath
        });
      }
    });
  },

  // 选择详情图片
  chooseDetailImage() {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: res => {
        const tempFilePath = res.tempFilePaths[0];

        this.setData({
          tempDetailImagePath: tempFilePath
        });
      }
    });
  },

  // 上传图片
  uploadImage() {
    if (!this.data.tempImagePath) {
      return Promise.resolve(this.data.formData.image);
    }

    const cloudPath = `${this.data.uploadPath}${new Date().getTime()}_${Math.floor(Math.random() * 1000)}.jpg`;

    wx.showLoading({
      title: '上传图片中...',
    });

    return new Promise((resolve, reject) => {
      wx.cloud.uploadFile({
        cloudPath: cloudPath,
        filePath: this.data.tempImagePath,
        success: res => {
          const fileID = res.fileID;
          resolve(fileID);
        },
        fail: err => {
          console.error('上传图片失败', err);
          reject(err);
        },
        complete: () => {
          wx.hideLoading();
        }
      });
    });
  },

  // 上传详情图片
  uploadDetailImage() {
    if (!this.data.tempDetailImagePath) {
      return Promise.resolve(this.data.formData.detailImage);
    }

    const cloudPath = `${this.data.uploadPath}detail_${new Date().getTime()}_${Math.floor(Math.random() * 1000)}.jpg`;

    wx.showLoading({
      title: '上传详情图片中...',
    });

    return new Promise((resolve, reject) => {
      wx.cloud.uploadFile({
        cloudPath: cloudPath,
        filePath: this.data.tempDetailImagePath,
        success: res => {
          const fileID = res.fileID;
          resolve(fileID);
        },
        fail: err => {
          console.error('上传详情图片失败', err);
          reject(err);
        },
        complete: () => {
          wx.hideLoading();
        }
      });
    });
  },

  // 提交添加表单
  async submitAddForm() {
    const { name, price, originalPrice, description } = this.data.formData;
    
    if (!name || !price) {
      wx.showToast({
        title: '请填写服务名称和价格',
        icon: 'none'
      });
      return;
    }
    
    try {
      // 上传图片
      const imageUrl = await this.uploadImage();
      const detailImageUrl = await this.uploadDetailImage();

      // 添加服务
      wx.showLoading({
        title: '添加中...',
      });

      console.log('准备调用云函数添加服务:', {
        action: 'addService',
        type: 'admin',
        name: name,
        price: Number(price),
        originalPrice: originalPrice ? Number(originalPrice) : null,
        description: description,
        image: imageUrl,
        detailImage: detailImageUrl
      });

      wx.cloud.callFunction({
        name: 'appointmentManager',
        data: {
          action: 'addService',
          type: 'admin',
          name: name,
          price: Number(price),
          originalPrice: originalPrice ? Number(originalPrice) : null,
          description: description,
          image: imageUrl,
          detailImage: detailImageUrl
        },
        success: res => {
          wx.hideLoading();
          
          console.log('添加服务结果:', res);
          
          if (res.result && res.result.success) {
            wx.showToast({
              title: '添加成功',
              icon: 'success'
            });
            
            this.closeForm();
            this.fetchServices();
          } else {
            wx.showToast({
              title: res.result?.message || '添加失败',
              icon: 'none',
              duration: 2000
            });
            
            console.error('添加服务失败:', res.result);
          }
        },
        fail: err => {
          wx.hideLoading();
          console.error('添加服务云函数调用失败', err);
          
          wx.showToast({
            title: '网络异常，请稍后重试',
            icon: 'none',
            duration: 2000
          });
        }
      });
    } catch (err) {
      wx.hideLoading();
      console.error('提交表单失败', err);
      
      wx.showToast({
        title: '上传图片失败，请重试',
        icon: 'none',
        duration: 2000
      });
    }
  },

  // 提交编辑表单
  async submitEditForm() {
    const { name, price, originalPrice, description } = this.data.formData;
    const { currentService } = this.data;
    
    if (!name || !price || !currentService) {
      wx.showToast({
        title: '请填写服务名称和价格',
        icon: 'none'
      });
      return;
    }
    
    try {
      // 上传图片
      const imageUrl = await this.uploadImage();
      const detailImageUrl = await this.uploadDetailImage();

      // 更新服务
      wx.showLoading({
        title: '更新中...',
      });

      wx.cloud.callFunction({
        name: 'appointmentManager',
        data: {
          action: 'updateService',
          type: 'admin',
          serviceId: currentService.id,
          name: name,
          price: Number(price),
          originalPrice: originalPrice ? Number(originalPrice) : null,
          description: description,
          image: imageUrl,
          detailImage: detailImageUrl
        },
        success: res => {
          wx.hideLoading();
          
          if (res.result && res.result.success) {
            wx.showToast({
              title: '更新成功',
              icon: 'success'
            });
            
            this.closeForm();
            this.fetchServices();
          } else {
            wx.showToast({
              title: res.result?.message || '更新失败',
              icon: 'none'
            });
          }
        },
        fail: err => {
          wx.hideLoading();
          console.error('更新服务失败', err);
          
          wx.showToast({
            title: '网络异常，请稍后重试',
            icon: 'none'
          });
        }
      });
    } catch (err) {
      wx.hideLoading();
      console.error('提交表单失败', err);
      
      wx.showToast({
        title: '上传图片失败，请重试',
        icon: 'none'
      });
    }
  },

  // 删除服务
  deleteService(e) {
    const { service } = e.currentTarget.dataset;
    
    wx.showModal({
      title: '确认删除',
      content: `确定要删除服务"${service.name}"吗？`,
      success: res => {
        if (res.confirm) {
          wx.showLoading({
            title: '删除中...',
          });
          
          wx.cloud.callFunction({
            name: 'appointmentManager',
            data: {
              action: 'deleteService',
              type: 'admin',
              serviceId: service.id
            },
            success: res => {
              wx.hideLoading();
              
              if (res.result && res.result.success) {
                wx.showToast({
                  title: '删除成功',
                  icon: 'success'
                });
                
                this.fetchServices();
              } else {
                wx.showToast({
                  title: res.result?.message || '删除失败',
                  icon: 'none'
                });
              }
            },
            fail: err => {
              wx.hideLoading();
              console.error('删除服务失败', err);
              
              wx.showToast({
                title: '网络异常，请稍后重试',
                icon: 'none'
              });
            }
          });
        }
      }
    });
  },

  // 切换服务可见性
  toggleServiceVisibility(e) {
    const { service } = e.currentTarget.dataset;
    const visible = !service.isVisible; // 取反，实现切换效果
    const actionText = visible ? '显示' : '隐藏';
    
    wx.showModal({
      title: `确认${actionText}服务`,
      content: `确定要${actionText}服务"${service.name}"吗？`,
      success: res => {
        if (res.confirm) {
          wx.showLoading({
            title: `正在${actionText}服务...`,
            mask: true
          });
          
          wx.cloud.callFunction({
            name: 'appointmentManager',
            data: {
              type: 'admin',
              action: 'toggleServiceVisibility',
              serviceId: service.id,
              visible: visible
            },
            success: res => {
              wx.hideLoading();
              
              if (res.result && res.result.success) {
                wx.showToast({
                  title: res.result.message,
                  icon: 'success'
                });
                
                // 刷新列表并在完成后显示排序结果
                setTimeout(() => {
                  this.fetchServices().then(() => {
                    console.log('服务列表已更新并按最新更新时间排序');
                    
                    // 如果是设置为显示，提示用户服务已排序到前面
                    if (visible) {
                      setTimeout(() => {
                        wx.showToast({
                          title: '服务已排序到最前',
                          icon: 'success',
                          duration: 2000
                        });
                      }, 500); // 缩短第二个提示的延迟时间
                    }
                  }).catch(err => {
                    console.error('刷新服务列表失败', err);
                  });
                }, 500); // 添加延迟，确保服务器处理完成
              } else {
                wx.showToast({
                  title: res.result?.message || `${actionText}失败`,
                  icon: 'none'
                });
              }
            },
            fail: err => {
              wx.hideLoading();
              console.error(`${actionText}服务失败`, err);
              
              wx.showToast({
                title: `${actionText}失败，请重试`,
                icon: 'none'
              });
            }
          });
        }
      }
    });
  },

  // 返回上一页
  navigateBack() {
    wx.navigateBack();
  }
}) 