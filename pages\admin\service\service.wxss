/* pages/admin/service/service.wxss */
.page-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f2f2f7;
  overflow: hidden; /* 防止整体溢出 */
}

.page-header {
  position: sticky;
  top: 0;
  z-index: 100;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  padding-top: 90rpx; /* 增加顶部内边距，确保在胶囊按钮下方 */
  margin-bottom: 20rpx;
  background-color: rgba(242, 242, 247, 0.9); /* 半透明背景 */
  backdrop-filter: blur(10px); /* 毛玻璃效果 */
  -webkit-backdrop-filter: blur(10px);
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
  flex-shrink: 0; /* 防止被压缩 */
}

.header-title {
  font-size: 36rpx;
  font-weight: 600; /* iOS 粗体 */
  color: #000000;
}

.header-actions {
  display: flex;
  align-items: center;
}

/* 添加按钮 */
.add-button {
  position: fixed;
  left: 50%;
  bottom: 60rpx; /* 与底部保持安全距离 */
  transform: translateX(-50%); /* 水平居中 */
  width: 110rpx; /* 统一尺寸 */
  height: 110rpx; /* 统一尺寸 */
  border-radius: 50%;
  background-color: #0070c9; /* 标准蓝色 */
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 6rpx 15rpx rgba(0, 112, 201, 0.3); /* 蓝色阴影 */
  z-index: 99;
  transition: all 0.2s ease; /* 统一过渡时间 */
}

.add-button:active {
  transform: translateX(-50%) scale(0.95); /* 保持水平居中的同时缩小 */
  box-shadow: 0 2rpx 8rpx rgba(0, 112, 201, 0.2);
}

.add-icon {
  color: #ffffff;
  font-size: 70rpx;
  font-weight: bold;
  margin-bottom: 6rpx; /* 轻微下移，视觉上更居中 */
}

/* 安全区域，确保内容不被底部遮挡 */
.safe-bottom-area {
  height: 160rpx; /* 增加高度，为添加按钮留出足够空间 */
  width: 100%;
}

/* 内容区域 - 修改为滚动视图样式 */
.content-admin-container {
  flex: 1;
  height: calc(100vh - 180rpx); /* 减去头部高度 */
  padding: 0 30rpx;
  box-sizing: border-box;
  overflow: hidden; /* 防止溢出 */
}

/* 视频项样式 */
.video-item {
  display: flex;
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 20rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04); /* 轻微阴影 */
  transition: transform 0.2s ease;
  position: relative;
  align-items: flex-start; /* 顶部对齐 */
}

.video-item:active {
  transform: scale(0.98);
}

/* 视频封面 - 左侧 */
.video-cover {
  width: 180rpx; /* 放大尺寸 */
  height: 180rpx; /* 保持正方形 */
  border-radius: 12rpx; /* 圆角 */
  object-fit: cover;
  background-color: #f2f2f7;
  flex-shrink: 0;
  margin-right: 20rpx; /* 与右侧内容的间距 */
  align-self: flex-start; /* 顶部对齐 */
}

/* 右侧内容区域 */
.right-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;
  height: 180rpx; /* 与图片高度一致 */
  justify-content: flex-start; /* 改为顶部对齐，手动控制底部元素位置 */
  position: relative; /* 添加相对定位 */
}

/* 标题和序号一行 */
.title-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12rpx;
}

/* 视频标题 */
.video-title {
  font-size: 30rpx; /* 减小标题字体大小 */
  font-weight: 600;
  color: #333333;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
  margin-right: 12rpx; /* 与序号的间距 */
}

/* 序号样式 */
.video-order {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.8); /* 深黑色背景，带透明度 */
  padding: 0;
  width: 44rpx; /* 固定宽度 */
  height: 44rpx; /* 固定高度，保持圆形 */
  border-radius: 50%; /* 圆形 */
  color: #ffffff; /* 白色文字 */
  font-weight: 700; /* 粗体 */
  font-size: 24rpx;
  text-align: center;
  flex-shrink: 0; /* 防止被压缩 */
}

/* 副标题样式 */
.video-subtitle {
  font-size: 26rpx;
  color: #8a8a8f; /* 调整颜色为更柔和的灰色 */
  margin-top: 10rpx; /* 微调顶部边距 */
  margin-bottom: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 75%; /* 稍微增加宽度 */
  line-height: 1.2; /* 添加行高 */
  font-weight: 400; /* 添加字重 */
}

/* 状态容器样式 */
.status-container {
  position: absolute;
  top: 74rpx; /* 微调位置与副标题平行 */
  right: 0;
  bottom: auto;
  z-index: 5;
  display: flex;
  justify-content: flex-end;
  padding-right: 10rpx;
}

/* 视频可见性状态样式 */
.video-visibility {
  padding: 0;
  display: flex;
  align-items: center;
  width: fit-content;
  position: relative;
}

/* 状态指示器样式 */
.status-indicator {
  width: 26rpx;
  height: 26rpx;
  border-radius: 50%;
}

/* 可见状态（绿色）- 添加呼吸动画 */
.video-visibility.visible .status-indicator {
  background-color: #34c759; /* iOS 绿色 */
  box-shadow: 0 0 6rpx rgba(52, 199, 89, 0.5); /* 发光效果 */
  animation: breathe 3s ease-in-out infinite; /* 添加呼吸动画，3秒一个周期 */
}

/* 隐藏状态（深红色） */
.video-visibility.hidden .status-indicator {
  background-color: #d70015; /* 深红色 */
  box-shadow: 0 0 6rpx rgba(215, 0, 21, 0.5); /* 发光效果 */
}

/* 呼吸动画关键帧 - 增强效果 */
@keyframes breathe {
  0% {
    transform: scale(1);
    opacity: 1;
    background-color: #2eb352; /* 深一点的绿色 */
    box-shadow: 0 0 6rpx rgba(52, 199, 89, 0.5);
  }
  50% {
    transform: scale(1.4); /* 放大更明显 */
    opacity: 0.7; /* 更透明 */
    background-color: #34c759; /* 标准绿色 */
    box-shadow: 0 0 12rpx rgba(52, 199, 89, 0.8); /* 发光效果增强 */
  }
  100% {
    transform: scale(1);
    opacity: 1;
    background-color: #2eb352; /* 深一点的绿色 */
    box-shadow: 0 0 6rpx rgba(52, 199, 89, 0.5);
  }
}

/* 操作按钮区域 */
.video-actions {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between; /* 均匀分布 */
  width: 100%; /* 确保占满整行 */
  position: absolute; /* 绝对定位 */
  bottom: 2rpx; /* 轻微调整底部位置 */
  left: 0;
  min-height: 48rpx; /* 确保按钮有足够的高度 */
}

/* 按钮样式 */
.action-button {
  padding: 0; /* 减小内边距 */
  border-radius: 12rpx; /* iOS 风格圆角 */
  font-size: 26rpx; /* 稍微增大字体 */
  text-align: center;
  transition: all 0.2s ease;
  height: 46rpx; /* 轻微减小高度 */
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500; /* iOS 中等字重 */
  flex: 1; /* 平均分配空间 */
  margin: 0 8rpx; /* 按钮之间的间距 */
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.05); /* 轻微阴影 */
}

/* 第一个按钮去除左边距 */
.action-button:first-child {
  margin-left: 0;
}

/* 最后一个按钮去除右边距 */
.action-button:last-child {
  margin-right: 0;
}

.action-button.edit {
  color: #007aff; /* iOS 蓝色 */
  background-color: rgba(0, 122, 255, 0.1);
  border: none;
}

.action-button.edit:active {
  background-color: rgba(0, 122, 255, 0.2);
}

.action-button.visibility {
  color: #5856d6; /* iOS 紫色 */
  background-color: rgba(88, 86, 214, 0.1);
  border: none;
}

.action-button.visibility:active {
  background-color: rgba(88, 86, 214, 0.2);
}

.action-button.delete {
  color: #ff3b30; /* iOS 红色 */
  background-color: rgba(255, 59, 48, 0.1);
  border: none;
}

.action-button.delete:active {
  background-color: rgba(255, 59, 48, 0.2);
}

/* 加载中样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx 0;
  flex: 1; /* 占满剩余空间 */
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid rgba(0, 122, 255, 0.1);
  border-top-color: #007aff; /* iOS 蓝色 */
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #8e8e93; /* iOS 辅助文字颜色 */
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-icon {
  width: 160rpx;
  height: 160rpx;
  margin-bottom: 30rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 30rpx;
  color: #8e8e93; /* iOS 辅助文字颜色 */
  text-align: center;
  line-height: 1.5;
}

.back-icon {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  color: #333333;
}

.video-list {
  padding-bottom: 180rpx; /* 增加底部边距，为添加按钮留出足够空间 */
  box-sizing: border-box;
}

.video-meta {
  font-size: 24rpx;
  color: #999999;
  display: flex;
  align-items: center;
  margin-top: 10rpx; /* 增加间距 */
}

.empty-subtext {
  font-size: 28rpx;
  color: #999999;
  text-align: center;
}

/* 刷新按钮样式 */
.refresh-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10rpx 20rpx;
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 30rpx;
  font-size: 24rpx;
  color: #333333;
}

.refresh-icon {
  margin-right: 6rpx;
  font-size: 28rpx;
}