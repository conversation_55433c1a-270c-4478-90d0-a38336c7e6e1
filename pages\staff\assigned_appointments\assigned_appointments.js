// 员工查看被指定预约页面
const app = getApp();

Page({
  /**
   * 页面的初始数据
   */
  data: {
    staffInfo: null,
    appointmentList: [],
    isLoading: true,
    isLoadingMore: false,
    hasMoreData: false,
    page: 1,
    pageSize: 10,
    isRefreshing: false, // 刷新状态变量
    currentStatus: '', // 当前筛选状态，空字符串表示显示全部
    statusOptions: [
      { value: '', label: '全部' },
      { value: 'confirmed', label: '待核销' },
      { value: 'pending', label: '待确认' },
      { value: 'completed', label: '已完成' },
      { value: 'cancelled', label: '已取消' }
    ]
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function(options) {
    // 检查员工登录状态
    const staffInfo = wx.getStorageSync('staffInfo');
    if (!staffInfo) {
      this.redirectToLogin();
      return;
    }

    this.setData({
      staffInfo: staffInfo
    });

    this.fetchAssignedAppointments();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function() {
    // 检查员工登录状态
    const staffInfo = wx.getStorageSync('staffInfo');
    if (!staffInfo) {
      this.redirectToLogin();
      return;
    }

    // 只有第一次显示页面或明确需要刷新时才加载数据
    if (!this.data.appointmentList.length) {
      this.setData({
        page: 1,
        appointmentList: []
      });
      this.fetchAssignedAppointments();
    }
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function() {
    console.log('下拉刷新');
    this.setData({
      page: 1,
      appointmentList: [],
      isRefreshing: true
    });
    this.fetchAssignedAppointments();
    
    setTimeout(() => {
      wx.stopPullDownRefresh();
      this.setData({
        isRefreshing: false
      });
    }, 1000);
  },

  /**
   * 获取分配给员工的预约列表
   */
  fetchAssignedAppointments: function(loadMore = false) {
    if (loadMore) {
      this.setData({ isLoadingMore: true });
    } else {
      this.setData({ 
        isLoading: true,
        appointmentList: [] // 非加载更多时清空列表
      });
    }

    const { staffInfo, page, pageSize } = this.data;
    
    console.log('获取员工预约列表, staffId:', staffInfo._id, 'page:', page);
    
    wx.cloud.callFunction({
      name: 'appointmentManager',
      data: {
        type: 'staff',
        action: 'getAssignedAppointments',
        staffId: staffInfo._id,
        page: loadMore ? page : 1,
        pageSize: pageSize,
        includeImageUrls: true, // 请求包含图片URL
        status: this.data.currentStatus // 传递当前筛选状态
      },
      success: res => {
        console.log('获取指定预约列表结果:', res);
        
        if (res.result && res.result.code === 0) {
          const { list, total, pageCount } = res.result.data;
          
          // 处理预约数据
          const processedList = list.map(item => {
            // 添加状态文本
            item.statusText = this.getStatusText(item.status);
            
            // 脱敏手机号
            if (item.phoneNumber) {
              item.maskedPhone = this.maskPhoneNumber(item.phoneNumber);
            }
            
            return item;
          });
          
          this.setData({
            appointmentList: loadMore ? [...this.data.appointmentList, ...processedList] : processedList,
            hasMoreData: page < pageCount,
            page: loadMore ? page + 1 : 2,
            isLoading: false,
            isLoadingMore: false
          });
        } else {
          wx.showToast({
            title: res.result?.message || '获取预约列表失败',
            icon: 'none'
          });
          this.setData({
            isLoading: false,
            isLoadingMore: false
          });
        }
      },
      fail: err => {
        console.error('调用云函数获取预约列表失败', err);
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
        this.setData({
          isLoading: false,
          isLoadingMore: false
        });
      }
    });
  },

  /**
   * 获取状态文本
   */
  getStatusText: function(status) {
    const statusMap = {
      'pending': '待确认',
      'confirmed': '已确认',
      'completed': '已完成',
      'cancelled': '已取消',
      'rejected': '已拒绝'
    };
    return statusMap[status] || '未知状态';
  },

  /**
   * 手机号码脱敏处理
   */
  maskPhoneNumber: function(phone) {
    if (!phone || phone.length < 7) return '***';
    return phone.substr(0, 3) + '****' + phone.substr(-4);
  },

  /**
   * 预览图片
   */
  previewImage: function(e) {
    const urls = e.currentTarget.dataset.urls;
    const current = e.currentTarget.dataset.current;
    
    wx.previewImage({
      current: current,
      urls: urls
    });
  },

  /**
   * 拨打电话
   */
  callPhone: function(e) {
    // 获取电话号码
    const phone = e.currentTarget.dataset.phone || '';
    
    console.log('准备拨打电话:', phone);
    
    if (!phone || phone.trim() === '') {
      console.log('电话号码为空，无法拨打');
      wx.showToast({
        title: '电话号码无效',
        icon: 'none'
      });
      return;
    }
    
    // 确保电话号码格式正确
    const phoneNumber = phone.trim();
    
    wx.makePhoneCall({
      phoneNumber: phoneNumber,
      success: function() {
        console.log('拨打电话成功');
      },
      fail: function(err) {
        console.log('拨打电话失败或用户取消:', err);
      }
    });
    
    // 阻止事件继续传播
    return false;
  },

  /**
   * 加载更多预约
   */
  loadMoreAppointments: function() {
    if (this.data.hasMoreData && !this.data.isLoadingMore) {
      this.fetchAssignedAppointments(true);
    }
  },

  /**
   * 返回上一页
   */
  goBack: function() {
    wx.navigateBack();
  },

  /**
   * 重定向到登录页
   */
  redirectToLogin: function() {
    wx.redirectTo({
      url: '/pages/staff/login/login'
    });
  },

  /**
   * 按状态筛选预约
   */
  filterByStatus: function(e) {
    const status = e.currentTarget.dataset.status;
    console.log('筛选状态:', status);
    
    if (status === this.data.currentStatus) {
      return; // 如果点击的是当前选中的状态，不做任何操作
    }
    
    this.setData({
      currentStatus: status,
      page: 1, // 重置页码
      appointmentList: [] // 清空列表
    });
    
    this.fetchAssignedAppointments(); // 重新加载数据
  }
}); 