/**
 * 主控制器模块
 * 负责协调所有子模块的初始化、生命周期管理和模块间通信
 * @version 1.0.0
 */

const BaseModule = require("./base-module");
const ModuleCommunicator = require("./module-communicator");
const { ErrorHandler } = require("../utils/error-handler");
const APICompatibility = require("./api-compatibility");
const UnifiedErrorHandler = require("./unified-error-handler");
const {
  MODULE_EVENTS,
  ERROR_TYPES,
  PAGE_STATES,
} = require("../constants/index-constants");

// 导入所有子模块
const DataManagerModule = require("./data-manager");
const VideoListModule = require("./video-list");
const VideoPlayerModule = require("./video-player");
const SearchModule = require("./search");
const ShareModule = require("./share");
const NavigationModule = require("./navigation");
const UIStateModule = require("./ui-state");
const ScrollPerformanceModule = require("./scroll-performance");

class MainController extends BaseModule {
  constructor(pageContext) {
    super(pageContext);
    this.moduleName = "MainController";

    // 模块注册表
    this.modules = new Map();

    // 模块初始化状态
    this.moduleStates = new Map();

    // 模块依赖关系
    this.moduleDependencies = new Map();

    // 页面状态
    this.pageState = PAGE_STATES.INITIAL;

    // 初始化失败的模块列表
    this.failedModules = new Set();

    // 模块通信器
    this.communicator = new ModuleCommunicator();

    // 错误处理器
    this.errorHandler = new ErrorHandler();

    // API兼容层
    this.apiCompatibility = null;

    // 统一错误处理器
    this.unifiedErrorHandler = null;
  }

  /**
   * 初始化主控制器
   */
  init() {
    try {
      console.log("[MainController] 开始初始化主控制器");

      // 设置页面状态
      this.pageState = PAGE_STATES.LOADING;

      // 初始化错误处理
      this.initErrorHandling();

      // 注册所有模块
      this.registerModules();

      // 设置模块依赖关系
      this.setupModuleDependencies();

      // 初始化模块通信
      this.initModuleCommunication();

      // 按依赖顺序初始化模块
      this.initializeModulesInOrder();

      // 初始化API兼容层
      this.initAPICompatibility();

      // 初始化统一错误处理器
      this.initUnifiedErrorHandler();

      this.initialized = true;
      this.pageState = PAGE_STATES.LOADED;

      console.log("[MainController] 主控制器初始化完成");

      // 触发初始化完成事件
      this.emit(MODULE_EVENTS.MODULE_INITIALIZED, {
        controller: "MainController",
        modulesCount: this.modules.size,
        failedModules: Array.from(this.failedModules),
      });
    } catch (error) {
      this.pageState = PAGE_STATES.ERROR;
      this.handleError(error, "init");
    }
  }

  /**
   * 初始化错误处理
   */
  initErrorHandling() {
    try {
      // 检查错误处理器是否存在必要的方法
      if (this.errorHandler && typeof this.errorHandler.handleError === 'function') {
        // 设置全局错误处理回调
        this.globalErrorHandler = (error, context) => {
          console.error("[MainController] 全局错误:", error, context);

          // 使用错误处理器处理错误
          this.errorHandler.handleError(error, context || 'MainController');

          // 触发错误事件
          this.emit("globalError", { error, context });

          // 尝试恢复
          this.attemptRecovery(error, context);
        };

        console.log("[MainController] 错误处理初始化完成");
      } else {
        console.warn("[MainController] 错误处理器不可用，跳过初始化");
      }
    } catch (error) {
      console.error("[MainController] 错误处理初始化失败:", error);
    }
  }

  /**
   * 注册所有模块
   */
  registerModules() {
    try {
      // 减少注册模块日志
      // console.log("[MainController] 开始注册模块");

      // 首先注册主控制器自身 - 使用与其他模块一致的格式
      this.modules.set("MainController", {
        instance: this,
        initialized: true,
        priority: 0,
        dependencies: []
      });
      console.log("[MainController] 主控制器已注册到模块系统");
      
      // 按优先级顺序注册模块
      const moduleConfigs = [
        { name: "UIState", class: UIStateModule, priority: 1 },
        { name: "DataManager", class: DataManagerModule, priority: 2 },
        { name: "Navigation", class: NavigationModule, priority: 3 },
        {
          name: "ScrollPerformance",
          class: ScrollPerformanceModule,
          priority: 4,
        },
        { name: "VideoList", class: VideoListModule, priority: 5 },
        { name: "VideoPlayer", class: VideoPlayerModule, priority: 6 },
        { name: "Search", class: SearchModule, priority: 7 },
        { name: "Share", class: ShareModule, priority: 8 },
      ];

      // 注册每个模块
      moduleConfigs.forEach((config) => {
        try {
          const moduleInstance = new config.class(this.page);

          this.modules.set(config.name, {
            instance: moduleInstance,
            priority: config.priority,
            initialized: false,
            error: null,
          });

          this.moduleStates.set(config.name, "registered");

          // 减少模块注册成功日志
          // console.log(`[MainController] 模块 ${config.name} 注册成功`);

          // 触发模块注册事件
          this.emit(MODULE_EVENTS.MODULE_REGISTERED, {
            moduleName: config.name,
            priority: config.priority,
          });
        } catch (error) {
          console.error(
            `[MainController] 模块 ${config.name} 注册失败:`,
            error
          );
          this.failedModules.add(config.name);
        }
      });

      // 只在有失败模块时输出日志
      if (this.failedModules.size > 0) {
        console.log(
          `[MainController] 模块注册完成，成功: ${this.modules.size}, 失败: ${this.failedModules.size}`
        );
      }
    } catch (error) {
      this.handleError(error, "registerModules");
    }
  }

  /**
   * 设置模块依赖关系
   */
  setupModuleDependencies() {
    try {
      // 定义模块依赖关系
      const dependencies = {
        DataManager: [], // 无依赖，最先初始化
        UIState: [], // 无依赖
        Navigation: ["UIState"], // 依赖UI状态管理
        ScrollPerformance: ["Navigation"], // 依赖导航模块
        VideoList: ["DataManager", "UIState"], // 依赖数据管理和UI状态
        VideoPlayer: ["VideoList", "UIState"], // 依赖视频列表和UI状态
        Search: ["VideoList", "DataManager"], // 依赖视频列表和数据管理
        Share: ["VideoList"], // 依赖视频列表
      };

      // 设置依赖关系
      Object.entries(dependencies).forEach(([moduleName, deps]) => {
        this.moduleDependencies.set(moduleName, deps);
      });

      // 减少依赖关系设置日志
      // console.log("[MainController] 模块依赖关系设置完成");
    } catch (error) {
      this.handleError(error, "setupModuleDependencies");
    }
  }

  /**
   * 初始化模块通信
   */
  initModuleCommunication() {
    try {
      // 为每个模块设置通信器
      this.modules.forEach((moduleInfo, moduleName) => {
        const moduleInstance = moduleInfo.instance;

        // 设置模块通信器
        if (typeof moduleInstance.setCommunicator === "function") {
          moduleInstance.setCommunicator(this.communicator);
        }

        // 监听模块事件
        this.setupModuleEventListeners(moduleName, moduleInstance);
      });

      // 减少模块通信初始化日志
      // console.log("[MainController] 模块通信初始化完成");
    } catch (error) {
      this.handleError(error, "initModuleCommunication");
    }
  }

  /**
   * 设置模块事件监听
   * @param {string} moduleName - 模块名称
   * @param {object} moduleInstance - 模块实例
   */
  setupModuleEventListeners(moduleName, moduleInstance) {
    try {
      // 监听模块错误事件
      moduleInstance.on("error", (error) => {
        console.error(`[MainController] 模块 ${moduleName} 发生错误:`, error);
        this.handleModuleError(moduleName, error);
      });

      // 监听模块状态变化事件
      moduleInstance.on("stateChange", (state) => {
        console.log(`[MainController] 模块 ${moduleName} 状态变化:`, state);
        this.handleModuleStateChange(moduleName, state);
      });

      // 监听模块初始化完成事件
      moduleInstance.on("initialized", () => {
        console.log(`[MainController] 模块 ${moduleName} 初始化完成`);
        this.handleModuleInitialized(moduleName);
      });
    } catch (error) {
      this.handleError(error, "setupModuleEventListeners");
    }
  }

  /**
   * 按依赖顺序初始化模块
   */
  initializeModulesInOrder() {
    try {
      // 减少初始化顺序日志
      // console.log("[MainController] 开始按依赖顺序初始化模块");

      // 获取初始化顺序
      const initOrder = this.getModuleInitOrder();

      // 按顺序初始化模块
      initOrder.forEach((moduleName, index) => {
        setTimeout(() => {
          this.initializeModule(moduleName);
        }, index * 50); // 每个模块间隔50ms初始化，避免阻塞
      });
    } catch (error) {
      this.handleError(error, "initializeModulesInOrder");
    }
  }

  /**
   * 获取模块初始化顺序
   * @returns {Array} 模块初始化顺序数组
   */
  getModuleInitOrder() {
    try {
      const visited = new Set();
      const visiting = new Set();
      const order = [];

      // 深度优先搜索，处理依赖关系
      const visit = (moduleName) => {
        if (visiting.has(moduleName)) {
          throw new Error(`检测到循环依赖: ${moduleName}`);
        }

        if (visited.has(moduleName)) {
          return;
        }

        visiting.add(moduleName);

        // 先初始化依赖的模块
        const dependencies = this.moduleDependencies.get(moduleName) || [];
        dependencies.forEach((dep) => {
          if (this.modules.has(dep)) {
            visit(dep);
          }
        });

        visiting.delete(moduleName);
        visited.add(moduleName);
        order.push(moduleName);
      };

      // 访问所有模块
      this.modules.forEach((_, moduleName) => {
        if (!visited.has(moduleName)) {
          visit(moduleName);
        }
      });

      // 减少模块初始化顺序日志
      // console.log("[MainController] 模块初始化顺序:", order);
      return order;
    } catch (error) {
      this.handleError(error, "getModuleInitOrder");
      // 返回按优先级排序的顺序作为备用方案
      return Array.from(this.modules.entries())
        .sort((a, b) => a[1].priority - b[1].priority)
        .map(([name]) => name);
    }
  }

  /**
   * 初始化单个模块
   * @param {string} moduleName - 模块名称
   */
  initializeModule(moduleName) {
    try {
      const moduleInfo = this.modules.get(moduleName);
      if (!moduleInfo) {
        console.warn(`[MainController] 模块 ${moduleName} 不存在`);
        return;
      }

      if (moduleInfo.initialized) {
        // console.log(`[MainController] 模块 ${moduleName} 已经初始化`);
        return;
      }

      // 减少初始化日志输出
      // console.log(`[MainController] 开始初始化模块 ${moduleName}`);
      this.moduleStates.set(moduleName, "initializing");

      // 检查依赖是否已初始化
      const dependencies = this.moduleDependencies.get(moduleName) || [];
      const unreadyDeps = dependencies.filter((dep) => {
        const depInfo = this.modules.get(dep);
        return !depInfo || !depInfo.initialized;
      });

      if (unreadyDeps.length > 0) {
        console.warn(
          `[MainController] 模块 ${moduleName} 的依赖未就绪:`,
          unreadyDeps
        );
        // 延迟初始化
        setTimeout(() => {
          this.initializeModule(moduleName);
        }, 100);
        return;
      }

      // 初始化模块
      const moduleInstance = moduleInfo.instance;
      moduleInstance.init();

      // 标记为已初始化
      moduleInfo.initialized = true;
      moduleInfo.error = null;
      this.moduleStates.set(moduleName, "initialized");

      // 减少模块初始化成功日志
      // console.log(`[MainController] 模块 ${moduleName} 初始化成功`);
    } catch (error) {
      console.error(`[MainController] 模块 ${moduleName} 初始化失败:`, error);

      const moduleInfo = this.modules.get(moduleName);
      if (moduleInfo) {
        moduleInfo.error = error;
        this.moduleStates.set(moduleName, "error");
      }

      this.failedModules.add(moduleName);
      this.handleModuleError(moduleName, error);
    }
  }

  /**
   * 处理模块错误
   * @param {string} moduleName - 模块名称
   * @param {Error} error - 错误对象
   */
  handleModuleError(moduleName, error) {
    try {
      console.error(`[MainController] 处理模块 ${moduleName} 错误:`, error);

      // 触发模块错误事件
      this.emit(MODULE_EVENTS.MODULE_ERROR, {
        moduleName: moduleName,
        error: error,
        timestamp: Date.now(),
      });

      // 尝试恢复模块
      this.attemptModuleRecovery(moduleName, error);
    } catch (recoveryError) {
      console.error(
        `[MainController] 模块 ${moduleName} 恢复失败:`,
        recoveryError
      );
    }
  }

  /**
   * 处理模块状态变化
   * @param {string} moduleName - 模块名称
   * @param {object} state - 状态信息
   */
  handleModuleStateChange(moduleName, state) {
    try {
      console.log(`[MainController] 模块 ${moduleName} 状态变化:`, state);

      // 更新模块状态
      this.moduleStates.set(moduleName, state.current || "unknown");

      // 触发状态变化事件
      this.emit("moduleStateChange", {
        moduleName: moduleName,
        state: state,
        timestamp: Date.now(),
      });
    } catch (error) {
      this.handleError(error, "handleModuleStateChange");
    }
  }

  /**
   * 处理模块初始化完成
   * @param {string} moduleName - 模块名称
   */
  handleModuleInitialized(moduleName) {
    try {
      const moduleInfo = this.modules.get(moduleName);
      if (moduleInfo) {
        moduleInfo.initialized = true;
        this.moduleStates.set(moduleName, "initialized");
      }

      // 检查是否所有模块都已初始化
      this.checkAllModulesInitialized();
    } catch (error) {
      this.handleError(error, "handleModuleInitialized");
    }
  }

  /**
   * 检查所有模块是否都已初始化
   */
  checkAllModulesInitialized() {
    try {
      const totalModules = this.modules.size;
      const initializedModules = Array.from(this.modules.values()).filter(
        (info) => info.initialized
      ).length;

      // 减少模块初始化进度日志
      // console.log(
      //   `[MainController] 模块初始化进度: ${initializedModules}/${totalModules}`
      // );

      if (initializedModules === totalModules) {
        // 只在首次加载时输出完成日志
        console.log("[MainController] 所有模块初始化完成");
        this.onAllModulesInitialized();
      }
    } catch (error) {
      this.handleError(error, "checkAllModulesInitialized");
    }
  }

  /**
   * 所有模块初始化完成后的处理
   */
  onAllModulesInitialized() {
    try {
      console.log("[MainController] 执行所有模块初始化完成后的处理");

      // 设置页面状态
      this.pageState = PAGE_STATES.LOADED;

      // 启动模块间协调
      this.startModuleCoordination();

      // 触发所有模块初始化完成事件
      this.emit("allModulesInitialized", {
        totalModules: this.modules.size,
        failedModules: Array.from(this.failedModules),
        timestamp: Date.now(),
      });
    } catch (error) {
      this.handleError(error, "onAllModulesInitialized");
    }
  }

  /**
   * 启动模块间协调
   */
  startModuleCoordination() {
    try {
      console.log("[MainController] 启动模块间协调");

      // 设置模块间事件转发
      this.setupModuleEventForwarding();

      // 启动定期健康检查
      this.startHealthCheck();

      console.log("[MainController] 模块间协调启动完成");
    } catch (error) {
      this.handleError(error, "startModuleCoordination");
    }
  }

  /**
   * 设置模块间事件转发
   */
  setupModuleEventForwarding() {
    try {
      // 设置关键事件的转发规则
      const eventForwardingRules = {
        // 视频列表更新 -> 视频播放器、搜索模块
        videoListUpdated: ["VideoPlayer", "Search"],
        // 搜索状态变化 -> UI状态、导航模块
        searchStateChange: ["UIState", "Navigation"],
        // 播放状态变化 -> UI状态模块
        playStateChange: ["UIState"],
        // 滚动事件 -> 导航、性能模块
        scroll: ["Navigation", "ScrollPerformance"],
        // UI状态变化 -> 所有模块
        uiStateChange: [
          "VideoList",
          "VideoPlayer",
          "Search",
          "Share",
          "Navigation",
        ],
      };

      // 设置事件转发
      Object.entries(eventForwardingRules).forEach(
        ([eventName, targetModules]) => {
          this.communicator.on(eventName, (data) => {
            targetModules.forEach((moduleName) => {
              const moduleInfo = this.modules.get(moduleName);
              if (moduleInfo && moduleInfo.initialized) {
                moduleInfo.instance.emit(eventName, data);
              }
            });
          });
        }
      );

      console.log("[MainController] 模块间事件转发设置完成");
    } catch (error) {
      this.handleError(error, "setupModuleEventForwarding");
    }
  }

  /**
   * 启动健康检查
   */
  startHealthCheck() {
    try {
      // 暂时禁用健康检查，避免误判导致的系统优化
      // 每30秒进行一次健康检查
      // this.healthCheckTimer = setInterval(() => {
      //   this.performHealthCheck();
      // }, 30000);

      console.log("[MainController] 健康检查已禁用（避免误判）");
    } catch (error) {
      this.handleError(error, "startHealthCheck");
    }
  }

  /**
   * 执行健康检查
   */
  performHealthCheck() {
    try {
      console.log("[MainController] 执行健康检查");

      const healthReport = {
        timestamp: Date.now(),
        totalModules: this.modules.size,
        healthyModules: 0,
        unhealthyModules: [],
        failedModules: Array.from(this.failedModules),
        pageState: this.pageState,
      };

      // 检查每个模块的健康状态
      this.modules.forEach((moduleInfo, moduleName) => {
        try {
          if (moduleInfo.initialized && !moduleInfo.error) {
            // 检查模块是否响应
            if (typeof moduleInfo.instance.getStatus === "function") {
              const status = moduleInfo.instance.getStatus();
              if (status && status.healthy !== false) {
                healthReport.healthyModules++;
              } else {
                healthReport.unhealthyModules.push({
                  name: moduleName,
                  status: status,
                  reason: "unhealthy_status",
                });
              }
            } else {
              healthReport.healthyModules++;
            }
          } else {
            healthReport.unhealthyModules.push({
              name: moduleName,
              initialized: moduleInfo.initialized,
              error: moduleInfo.error,
              reason: "not_initialized_or_error",
            });
          }
        } catch (error) {
          healthReport.unhealthyModules.push({
            name: moduleName,
            error: error.message,
            reason: "health_check_error",
          });
        }
      });

      // 触发健康检查完成事件
      this.emit("healthCheckComplete", healthReport);

      // 如果有不健康的模块，尝试恢复
      if (healthReport.unhealthyModules.length > 0) {
        console.warn(
          "[MainController] 发现不健康的模块:",
          healthReport.unhealthyModules
        );
        this.handleUnhealthyModules(healthReport.unhealthyModules);
      }
    } catch (error) {
      this.handleError(error, "performHealthCheck");
    }
  }

  /**
   * 处理不健康的模块
   * @param {Array} unhealthyModules - 不健康的模块列表
   */
  handleUnhealthyModules(unhealthyModules) {
    try {
      unhealthyModules.forEach((moduleInfo) => {
        console.log(
          `[MainController] 尝试恢复不健康的模块: ${moduleInfo.name}`
        );
        this.attemptModuleRecovery(moduleInfo.name, moduleInfo.error);
      });
    } catch (error) {
      this.handleError(error, "handleUnhealthyModules");
    }
  }

  /**
   * 尝试模块恢复
   * @param {string} moduleName - 模块名称
   * @param {Error} error - 错误对象
   */
  attemptModuleRecovery(moduleName, error) {
    try {
      console.log(`[MainController] 尝试恢复模块: ${moduleName}`);

      const moduleInfo = this.modules.get(moduleName);
      if (!moduleInfo) {
        console.warn(`[MainController] 模块 ${moduleName} 不存在，无法恢复`);
        return;
      }

      // 重置模块状态
      moduleInfo.initialized = false;
      moduleInfo.error = null;
      this.moduleStates.set(moduleName, "recovering");

      // 尝试重新初始化
      setTimeout(() => {
        try {
          console.log(`[MainController] 重新初始化模块: ${moduleName}`);
          moduleInfo.instance.init();

          moduleInfo.initialized = true;
          this.moduleStates.set(moduleName, "recovered");
          this.failedModules.delete(moduleName);

          console.log(`[MainController] 模块 ${moduleName} 恢复成功`);

          // 触发恢复成功事件
          this.emit("moduleRecovered", {
            moduleName: moduleName,
            timestamp: Date.now(),
          });
        } catch (recoveryError) {
          console.error(
            `[MainController] 模块 ${moduleName} 恢复失败:`,
            recoveryError
          );
          moduleInfo.error = recoveryError;
          this.moduleStates.set(moduleName, "recovery_failed");
          this.failedModules.add(moduleName);
        }
      }, 1000);
    } catch (error) {
      this.handleError(error, "attemptModuleRecovery");
    }
  }

  /**
   * 尝试系统恢复
   * @param {Error} error - 错误对象
   * @param {string} context - 错误上下文
   */
  attemptRecovery(error, context) {
    try {
      console.log("[MainController] 尝试系统恢复:", context);

      // 根据错误类型采取不同的恢复策略
      if (error.type === ERROR_TYPES.MODULE_ERROR) {
        // 模块错误，尝试重启相关模块
        this.restartFailedModules();
      } else if (error.type === ERROR_TYPES.NETWORK_ERROR) {
        // 网络错误，等待网络恢复
        this.waitForNetworkRecovery();
      } else {
        // 其他错误，执行通用恢复
        this.performGeneralRecovery();
      }
    } catch (recoveryError) {
      console.error("[MainController] 系统恢复失败:", recoveryError);
    }
  }

  /**
   * 重启失败的模块
   */
  restartFailedModules() {
    try {
      console.log("[MainController] 重启失败的模块");

      this.failedModules.forEach((moduleName) => {
        this.attemptModuleRecovery(moduleName, new Error("Module restart"));
      });
    } catch (error) {
      this.handleError(error, "restartFailedModules");
    }
  }

  /**
   * 等待网络恢复
   */
  waitForNetworkRecovery() {
    try {
      console.log("[MainController] 等待网络恢复");

      // 监听网络状态变化
      wx.onNetworkStatusChange((res) => {
        if (res.isConnected) {
          console.log("[MainController] 网络已恢复，重新初始化相关模块");

          // 重新初始化依赖网络的模块
          const networkDependentModules = ["VideoList", "Share"];
          networkDependentModules.forEach((moduleName) => {
            if (this.failedModules.has(moduleName)) {
              this.attemptModuleRecovery(
                moduleName,
                new Error("Network recovery")
              );
            }
          });
        }
      });
    } catch (error) {
      this.handleError(error, "waitForNetworkRecovery");
    }
  }

  /**
   * 执行通用恢复
   */
  performGeneralRecovery() {
    try {
      console.log("[MainController] 执行通用恢复");

      // 清理资源
      this.cleanupResources();

      // 重置页面状态
      this.pageState = PAGE_STATES.LOADING;

      // 延迟重新初始化
      setTimeout(() => {
        this.reinitialize();
      }, 2000);
    } catch (error) {
      this.handleError(error, "performGeneralRecovery");
    }
  }

  /**
   * 清理资源
   */
  cleanupResources() {
    try {
      console.log("[MainController] 清理资源");

      // 清理定时器
      if (this.healthCheckTimer) {
        clearInterval(this.healthCheckTimer);
        this.healthCheckTimer = null;
      }

      // 清理模块资源
      this.modules.forEach((moduleInfo, moduleName) => {
        try {
          if (typeof moduleInfo.instance.cleanup === "function") {
            moduleInfo.instance.cleanup();
          }
        } catch (error) {
          console.warn(
            `[MainController] 清理模块 ${moduleName} 资源失败:`,
            error
          );
        }
      });
    } catch (error) {
      this.handleError(error, "cleanupResources");
    }
  }

  /**
   * 重新初始化
   */
  reinitialize() {
    try {
      console.log("[MainController] 重新初始化系统");

      // 重置状态
      this.failedModules.clear();
      this.moduleStates.clear();

      // 重新初始化所有模块
      this.modules.forEach((moduleInfo) => {
        moduleInfo.initialized = false;
        moduleInfo.error = null;
      });

      // 重新开始初始化流程
      this.initializeModulesInOrder();
    } catch (error) {
      this.handleError(error, "reinitialize");
    }
  }

  /**
   * 初始化API兼容层
   */
  initAPICompatibility() {
    try {
      // 减少API兼容层初始化日志
      // console.log("[MainController] 初始化API兼容层");

      // 创建API兼容层实例
      this.apiCompatibility = new APICompatibility(this, this.page);

      // 初始化兼容层
      this.apiCompatibility.init();

      // 获取兼容性报告
      const report = this.apiCompatibility.getCompatibilityReport();
      // 只在有错误时输出兼容性报告
      if (report.errors && report.errors.length > 0) {
        console.log("[MainController] API兼容性报告:", report);
      }

      // 触发兼容层初始化完成事件
      this.emit("apiCompatibilityInitialized", {
        report: report,
        timestamp: Date.now(),
      });

      // 减少API兼容层初始化完成日志
      // console.log("[MainController] API兼容层初始化完成");
    } catch (error) {
      console.error("[MainController] API兼容层初始化失败:", error);
      this.handleError(error, "initAPICompatibility");
    }
  }

  /**
   * 初始化统一错误处理器
   */
  initUnifiedErrorHandler() {
    try {
      // 减少统一错误处理器初始化日志
      // console.log("[MainController] 初始化统一错误处理器");

      // 创建统一错误处理器实例
      this.unifiedErrorHandler = new UnifiedErrorHandler(this.page);

      // 初始化错误处理器
      this.unifiedErrorHandler.init();

      // 设置为全局错误处理器
      this.setGlobalErrorHandler();

      // 监听错误处理器事件
      this.setupErrorHandlerListeners();

      // 减少统一错误处理器初始化完成日志
      // console.log("[MainController] 统一错误处理器初始化完成");
    } catch (error) {
      console.error("[MainController] 统一错误处理器初始化失败:", error);
      this.handleError(error, "initUnifiedErrorHandler");
    }
  }

  /**
   * 设置全局错误处理器
   */
  setGlobalErrorHandler() {
    try {
      // 重写主控制器的错误处理方法，使用统一错误处理器
      const originalHandleError = this.handleError.bind(this);

      this.handleError = async (error, context) => {
        try {
          // 使用统一错误处理器处理错误
          const result = await this.unifiedErrorHandler.handleError(error, {
            ...context,
            source: "MainController",
            moduleName: this.moduleName,
          });

          // 如果统一错误处理器处理失败，使用原始处理方法
          if (!result.success) {
            originalHandleError(error, context);
          }

          return result;
        } catch (handlerError) {
          console.error(
            "[MainController] 统一错误处理器处理失败:",
            handlerError
          );
          originalHandleError(error, context);
        }
      };

      // 为所有模块设置统一错误处理器
      this.modules.forEach((moduleInfo, moduleName) => {
        const moduleInstance = moduleInfo.instance;
        if (typeof moduleInstance.setErrorHandler === "function") {
          moduleInstance.setErrorHandler(this.unifiedErrorHandler);
        }
      });

      // console.log("[MainController] 全局错误处理器设置完成");
    } catch (error) {
      console.error("[MainController] 设置全局错误处理器失败:", error);
    }
  }

  /**
   * 设置错误处理器事件监听
   */
  setupErrorHandlerListeners() {
    try {
      // 监听高频错误事件
      this.unifiedErrorHandler.on("highFrequencyError", (data) => {
        console.warn("[MainController] 检测到高频错误:", data);
        this.handleHighFrequencyError(data);
      });

      // 监听错误分析完成事件
      this.unifiedErrorHandler.on("errorAnalysisComplete", (analysis) => {
        // 只在有实际错误时输出日志
        if (analysis.totalErrors > 0) {
          console.log(
            "[MainController] 错误分析完成:",
            analysis.totalErrors,
            "个错误"
          );
        }
        this.handleErrorAnalysis(analysis);
      });

      // 监听模块重启请求事件
      this.unifiedErrorHandler.on("moduleRestartRequest", (data) => {
        console.log("[MainController] 收到模块重启请求:", data);
        this.attemptModuleRecovery(data.moduleName, new Error(data.reason));
      });

      // 监听网络恢复事件
      this.unifiedErrorHandler.on("networkRecovered", (data) => {
        console.log("[MainController] 网络已恢复:", data);
        this.handleNetworkRecovery(data);
      });

      // console.log("[MainController] 错误处理器事件监听设置完成");
    } catch (error) {
      console.error("[MainController] 设置错误处理器事件监听失败:", error);
    }
  }

  /**
   * 处理高频错误
   * @param {object} data - 高频错误数据
   */
  handleHighFrequencyError(data) {
    try {
      console.log("[MainController] 处理高频错误:", data.pattern);

      // 根据错误模式采取相应措施
      if (data.pattern.includes("NETWORK_ERROR")) {
        // 网络错误频繁，可能需要切换到离线模式
        this.switchToOfflineMode();
      } else if (data.pattern.includes("MODULE_ERROR")) {
        // 模块错误频繁，可能需要禁用相关功能
        this.disableProblematicModule(data.pattern);
      }

      // 触发高频错误处理事件
      this.emit("highFrequencyErrorHandled", {
        pattern: data.pattern,
        action: "handled",
        timestamp: Date.now(),
      });
    } catch (error) {
      console.error("[MainController] 处理高频错误失败:", error);
    }
  }

  /**
   * 处理错误分析结果
   * @param {object} analysis - 错误分析结果
   */
  handleErrorAnalysis(analysis) {
    try {
      // 减少日志输出，只在有错误时输出
      // console.log("[MainController] 处理错误分析结果");

      // 应用优化建议
      if (analysis.recommendations && analysis.recommendations.length > 0) {
        this.applyOptimizationRecommendations(analysis.recommendations);
      }

      // 修复错误恢复率判断逻辑
      const recoveryRate = parseFloat(analysis.recoveryRate) || 0;
      const totalErrors = analysis.totalErrors || 0;

      // 只有在真正有错误且恢复率低的情况下才触发优化
      // 如果没有错误，恢复率为0%是正常的
      if (totalErrors > 0 && recoveryRate < 70) {
        console.warn("[MainController] 检测到错误且恢复率过低，触发系统优化");
        this.triggerSystemOptimization();
      } else if (totalErrors === 0) {
        // 系统正常运行时不输出日志
        // console.log("[MainController] 系统运行正常，无错误发生");
      } else {
        console.log("[MainController] 错误恢复率正常:", recoveryRate + "%");
      }

      // 触发错误分析处理完成事件
      this.emit("errorAnalysisHandled", {
        analysis: analysis,
        timestamp: Date.now(),
      });
    } catch (error) {
      console.error("[MainController] 处理错误分析结果失败:", error);
    }
  }

  /**
   * 应用优化建议
   * @param {Array} recommendations - 优化建议列表
   */
  applyOptimizationRecommendations(recommendations) {
    try {
      console.log("[MainController] 应用优化建议:", recommendations.length);

      recommendations.forEach((rec) => {
        switch (rec.type) {
          case "network_optimization":
            this.optimizeNetworkHandling();
            break;
          case "module_stability":
            this.improveModuleStability();
            break;
          case "recovery_improvement":
            this.enhanceRecoveryMechanisms();
            break;
          default:
            console.log("[MainController] 未知的优化建议类型:", rec.type);
        }
      });
    } catch (error) {
      console.error("[MainController] 应用优化建议失败:", error);
    }
  }

  /**
   * 切换到离线模式
   */
  switchToOfflineMode() {
    try {
      console.log("[MainController] 切换到离线模式");

      // 通知相关模块切换到离线模式
      const offlineModeModules = ["VideoList", "Search", "Share"];
      offlineModeModules.forEach((moduleName) => {
        const moduleInfo = this.modules.get(moduleName);
        if (moduleInfo && moduleInfo.initialized) {
          const moduleInstance = moduleInfo.instance;
          if (typeof moduleInstance.switchToOfflineMode === "function") {
            moduleInstance.switchToOfflineMode();
          }
        }
      });

      // 触发离线模式切换事件
      this.emit("offlineModeActivated", {
        timestamp: Date.now(),
      });
    } catch (error) {
      console.error("[MainController] 切换到离线模式失败:", error);
    }
  }

  /**
   * 禁用有问题的模块
   * @param {string} pattern - 错误模式
   */
  disableProblematicModule(pattern) {
    try {
      console.log("[MainController] 禁用有问题的模块:", pattern);

      // 从错误模式中提取模块名
      const moduleName = this.extractModuleNameFromPattern(pattern);
      if (moduleName) {
        const moduleInfo = this.modules.get(moduleName);
        if (moduleInfo) {
          // 标记模块为禁用状态
          this.moduleStates.set(moduleName, "disabled");

          // 通知模块进入安全模式
          if (typeof moduleInfo.instance.enterSafeMode === "function") {
            moduleInfo.instance.enterSafeMode();
          }
        }
      }
    } catch (error) {
      console.error("[MainController] 禁用有问题的模块失败:", error);
    }
  }

  /**
   * 从错误模式中提取模块名
   * @param {string} pattern - 错误模式
   * @returns {string|null} 模块名
   */
  extractModuleNameFromPattern(pattern) {
    try {
      // 简单的模式匹配，实际实现可能更复杂
      const moduleNames = Array.from(this.modules.keys());
      return moduleNames.find((name) => pattern.includes(name)) || null;
    } catch (error) {
      console.error("[MainController] 提取模块名失败:", error);
      return null;
    }
  }

  /**
   * 优化网络处理
   */
  optimizeNetworkHandling() {
    try {
      console.log("[MainController] 优化网络处理");

      // 增加网络请求的重试次数和延迟
      const networkModules = ["VideoList", "Share"];
      networkModules.forEach((moduleName) => {
        const moduleInfo = this.modules.get(moduleName);
        if (moduleInfo && moduleInfo.initialized) {
          const moduleInstance = moduleInfo.instance;
          if (typeof moduleInstance.optimizeNetworkSettings === "function") {
            moduleInstance.optimizeNetworkSettings({
              maxRetries: 5,
              retryDelay: 3000,
              timeout: 10000,
            });
          }
        }
      });
    } catch (error) {
      console.error("[MainController] 优化网络处理失败:", error);
    }
  }

  /**
   * 改善模块稳定性
   */
  improveModuleStability() {
    try {
      console.log("[MainController] 改善模块稳定性");

      // 增加模块健康检查频率
      if (this.healthCheckTimer) {
        clearInterval(this.healthCheckTimer);
        this.healthCheckTimer = setInterval(() => {
          this.performHealthCheck();
        }, 15000); // 从30秒改为15秒
      }

      // 为所有模块启用更严格的错误处理
      this.modules.forEach((moduleInfo, moduleName) => {
        if (moduleInfo.initialized) {
          const moduleInstance = moduleInfo.instance;
          if (typeof moduleInstance.enableStrictErrorHandling === "function") {
            moduleInstance.enableStrictErrorHandling();
          }
        }
      });
    } catch (error) {
      console.error("[MainController] 改善模块稳定性失败:", error);
    }
  }

  /**
   * 增强恢复机制
   */
  enhanceRecoveryMechanisms() {
    try {
      console.log("[MainController] 增强恢复机制");

      // 为统一错误处理器启用更积极的恢复策略
      if (this.unifiedErrorHandler) {
        this.unifiedErrorHandler.monitoringConfig.enableAutoRecovery = true;
        this.unifiedErrorHandler.monitoringConfig.maxRetryAttempts = 5;
        this.unifiedErrorHandler.monitoringConfig.retryDelay = 500;
      }
    } catch (error) {
      console.error("[MainController] 增强恢复机制失败:", error);
    }
  }

  /**
   * 触发系统优化
   */
  triggerSystemOptimization() {
    try {
      console.log("[MainController] 触发系统优化");

      // 执行全面的系统优化
      this.optimizeNetworkHandling();
      this.improveModuleStability();
      this.enhanceRecoveryMechanisms();

      // 清理系统资源
      this.cleanupResources();

      // 触发系统优化完成事件
      this.emit("systemOptimizationComplete", {
        timestamp: Date.now(),
      });
    } catch (error) {
      console.error("[MainController] 触发系统优化失败:", error);
    }
  }

  /**
   * 处理网络恢复
   * @param {object} data - 网络恢复数据
   */
  handleNetworkRecovery(data) {
    try {
      console.log("[MainController] 处理网络恢复");

      // 重新启用网络相关功能
      const networkModules = ["VideoList", "Share"];
      networkModules.forEach((moduleName) => {
        const moduleInfo = this.modules.get(moduleName);
        if (moduleInfo && moduleInfo.initialized) {
          const moduleInstance = moduleInfo.instance;
          if (typeof moduleInstance.switchToOnlineMode === "function") {
            moduleInstance.switchToOnlineMode();
          }
        }
      });

      // 触发网络恢复处理完成事件
      this.emit("networkRecoveryHandled", {
        networkType: data.networkType,
        timestamp: Date.now(),
      });
    } catch (error) {
      console.error("[MainController] 处理网络恢复失败:", error);
    }
  }

  /**
   * 获取错误统计信息
   * @returns {object} 错误统计
   */
  getErrorStats() {
    try {
      if (this.unifiedErrorHandler) {
        return this.unifiedErrorHandler.getErrorStats();
      }
      return {};
    } catch (error) {
      console.error("[MainController] 获取错误统计失败:", error);
      return {};
    }
  }

  /**
   * 获取模块实例
   * @param {string} moduleName - 模块名称
   * @returns {object|null} 模块实例
   */
  getModule(moduleName) {
    try {
      const moduleInfo = this.modules.get(moduleName);
      return moduleInfo ? moduleInfo.instance : null;
    } catch (error) {
      this.handleError(error, "getModule");
      return null;
    }
  }

  /**
   * 获取所有模块状态
   * @returns {object} 模块状态信息
   */
  getModulesStatus() {
    try {
      const status = {
        totalModules: this.modules.size,
        initializedModules: 0,
        failedModules: Array.from(this.failedModules),
        pageState: this.pageState,
        modules: {},
      };

      this.modules.forEach((moduleInfo, moduleName) => {
        status.modules[moduleName] = {
          initialized: moduleInfo.initialized,
          state: this.moduleStates.get(moduleName),
          error: moduleInfo.error ? moduleInfo.error.message : null,
          priority: moduleInfo.priority,
        };

        if (moduleInfo.initialized) {
          status.initializedModules++;
        }
      });

      return status;
    } catch (error) {
      this.handleError(error, "getModulesStatus");
      return {};
    }
  }

  // ==================== 页面生命周期处理方法 ====================

  /**
   * 处理页面加载
   * @param {object} options - 页面参数
   */
  handlePageLoad(options) {
    try {
      console.log("[MainController] 处理页面加载:", options);

      // 处理分享参数
      if (this.modules.has("Share")) {
        const shareModule = this.modules.get("Share").instance;
        if (
          shareModule &&
          typeof shareModule.processShareParams === "function"
        ) {
          shareModule.processShareParams(options);
        }
      }

      // 开始加载视频列表
      this.loadInitialData();

      console.log("[MainController] 页面加载处理完成");
    } catch (error) {
      this.handleError(error, "handlePageLoad");
    }
  }

  /**
   * 处理页面显示
   */
  handlePageShow() {
    try {
      console.log("[MainController] 处理页面显示");

      // 更新全局状态
      const app = getApp();
      if (app && app.globalData) {
        app.globalData.currentTabIndex = 1;
      }

      // 通知所有模块页面显示
      this.modules.forEach((moduleInfo, moduleName) => {
        if (moduleInfo.initialized) {
          const moduleInstance = moduleInfo.instance;
          if (typeof moduleInstance.onPageShow === "function") {
            moduleInstance.onPageShow();
          }
        }
      });

      console.log("[MainController] 页面显示处理完成");
    } catch (error) {
      this.handleError(error, "handlePageShow");
    }
  }

  /**
   * 处理页面隐藏
   */
  handlePageHide() {
    try {
      console.log("[MainController] 处理页面隐藏");

      // 暂停所有视频
      if (this.modules.has("VideoPlayer")) {
        const videoPlayerModule = this.modules.get("VideoPlayer").instance;
        if (
          videoPlayerModule &&
          typeof videoPlayerModule.pauseAllVideos === "function"
        ) {
          videoPlayerModule.pauseAllVideos();
        }
      }

      // 通知所有模块页面隐藏
      this.modules.forEach((moduleInfo, moduleName) => {
        if (moduleInfo.initialized) {
          const moduleInstance = moduleInfo.instance;
          if (typeof moduleInstance.onPageHide === "function") {
            moduleInstance.onPageHide();
          }
        }
      });

      console.log("[MainController] 页面隐藏处理完成");
    } catch (error) {
      this.handleError(error, "handlePageHide");
    }
  }

  /**
   * 处理页面卸载
   */
  handlePageUnload() {
    try {
      console.log("[MainController] 处理页面卸载");

      // 销毁所有模块
      this.destroy();

      console.log("[MainController] 页面卸载处理完成");
    } catch (error) {
      this.handleError(error, "handlePageUnload");
    }
  }

  /**
   * 加载初始数据
   */
  loadInitialData() {
    try {
      console.log("[MainController] 开始加载初始数据");

      // 设置页面状态为加载中
      this.page.setData({
        loading: true,
        firstLoading: true,
        showContent: false,
      });

      // 调用视频列表模块加载数据
      if (this.modules.has("VideoList")) {
        const videoListModule = this.modules.get("VideoList").instance;
        if (
          videoListModule &&
          typeof videoListModule.loadVideoList === "function"
        ) {
          videoListModule
            .loadVideoList(false)
            .then(() => {
              console.log("[MainController] 初始数据加载完成");

              // 确保内容显示 - 修复状态重置问题
              this.page.setData({
                loading: false, // 重要：停止加载状态
                firstLoading: false, // 重要：停止首次加载状态
                showContent: true, // 显示内容
              });
            })
            .catch((error) => {
              console.error("[MainController] 初始数据加载失败:", error);

              // 即使失败也要显示内容区域
              this.page.setData({
                loading: false,
                firstLoading: false,
                showContent: true,
              });
            });
        } else {
          // 降级处理
          console.warn("[MainController] 视频列表模块不可用，使用降级处理");
          this.page.setData({
            loading: false,
            firstLoading: false,
            showContent: true,
          });
        }
      } else {
        // 模块不存在的降级处理
        console.warn("[MainController] 视频列表模块不存在，使用降级处理");
        this.page.setData({
          loading: false,
          firstLoading: false,
          showContent: true,
        });
      }

      // 添加超时保护，确保页面不会永远卡在加载状态
      setTimeout(() => {
        const currentData = this.page.data;
        if (currentData.loading || currentData.firstLoading) {
          console.warn("[MainController] 检测到页面仍在加载状态，强制重置");
          this.page.setData({
            loading: false,
            firstLoading: false,
            showContent: true,
          });
        }
      }, 5000); // 5秒超时保护
    } catch (error) {
      this.handleError(error, "loadInitialData");

      // 错误情况下也要确保页面可用
      this.page.setData({
        loading: false,
        firstLoading: false,
        showContent: true,
      });
    }
  }

  /**
   * 页面显示时调用（兼容性方法）
   */
  onShow() {
    this.handlePageShow();
  }

  /**
   * 页面隐藏时调用（兼容性方法）
   */
  onHide() {
    this.handlePageHide();
  }

  /**
   * 销毁主控制器
   */
  destroy() {
    try {
      console.log("[MainController] 销毁主控制器");

      // 清理资源
      this.cleanupResources();

      // 销毁所有模块
      this.modules.forEach((moduleInfo, moduleName) => {
        try {
          if (typeof moduleInfo.instance.destroy === "function") {
            moduleInfo.instance.destroy();
          }
        } catch (error) {
          console.warn(`[MainController] 销毁模块 ${moduleName} 失败:`, error);
        }
      });

      // 清理通信器
      if (this.communicator) {
        this.communicator.destroy();
      }

      // 清理错误处理器
      if (this.errorHandler) {
        this.errorHandler.destroy();
      }

      // 清理API兼容层
      if (this.apiCompatibility) {
        this.apiCompatibility.destroy();
      }

      // 清理统一错误处理器
      if (this.unifiedErrorHandler) {
        this.unifiedErrorHandler.destroy();
      }

      // 清理状态
      this.modules.clear();
      this.moduleStates.clear();
      this.moduleDependencies.clear();
      this.failedModules.clear();

      super.destroy();
    } catch (error) {
      console.error("[MainController] 销毁主控制器失败:", error);
    }
  }
}

module.exports = MainController;
