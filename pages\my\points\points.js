// pages/my/points/points.js
Page({
  /**
   * 页面的初始数据
   */
  data: {
    activeTab: 'points', // 当前激活的标签页：points(积分), records(分享记录), withdrawals(提现记录)
    points: 0, // 当前积分
    totalPoints: 0, // 累计积分
    frozenPoints: 0, // 冻结积分
    shareRecords: [], // 分享记录
    withdrawalRecords: [], // 提现记录
    loadingPoints: true, // 加载积分状态
    loadingRecords: true, // 加载记录状态
    loadingWithdrawals: true, // 加载提现记录状态
    paymentQrCode: '', // 收款码图片路径
    description: '', // 提现说明
    showWithdrawalForm: false, // 是否显示提现表单
    submittingWithdrawal: false, // 提现申请提交中
    hasPendingWithdrawal: false, // 是否有待处理的提现申请
    pendingWithdrawal: null, // 待处理的提现申请
    showQrCodePreview: false, // 是否显示收款码预览
    previewQrCode: '' // 预览的收款码
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    // 加载用户积分信息
    this.loadUserPoints();
    
    // 加载分享记录
    this.loadShareRecords();
    
    // 加载提现记录
    this.loadWithdrawalRecords();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    // 页面显示时刷新数据
    this.loadUserPoints();
    this.loadWithdrawalRecords();
  },
  
  /**
   * 返回上一页
   */
  navigateBack: function() {
    wx.navigateBack({
      delta: 1
    });
  },

  /**
   * 切换标签页
   */
  switchTab: function (e) {
    const tab = e.currentTarget.dataset.tab;
    this.setData({
      activeTab: tab
    });
  },
  
  /**
   * 阻止事件冒泡
   */
  preventBubble: function(e) {
    // 阻止事件冒泡传递
  },

  /**
   * 加载用户积分信息
   */
  loadUserPoints: function () {
    console.log('开始加载用户积分信息');
    this.setData({ loadingPoints: true });
    
    wx.cloud.callFunction({
      name: 'pointsManager',
      data: {
        action: 'getUserPoints'
      }
    }).then(res => {
      console.log('获取积分信息结果:', res.result);
      
      if (res.result && res.result.code === 0) {
        console.log('积分信息加载成功:', {
          points: res.result.points,
          totalPoints: res.result.totalPoints,
          frozenPoints: res.result.frozenPoints
        });
        
        this.setData({
          points: res.result.points || 0,
          totalPoints: res.result.totalPoints || 0,
          frozenPoints: res.result.frozenPoints || 0,
          loadingPoints: false
        });
      } else {
        console.error('获取积分信息失败:', res.result ? res.result.message : '未知错误');
        wx.showToast({
          title: '获取积分信息失败',
          icon: 'none'
        });
        this.setData({ loadingPoints: false });
      }
    }).catch(err => {
      console.error('获取积分信息失败:', err);
      wx.showToast({
        title: '获取积分信息失败',
        icon: 'none'
      });
      this.setData({ loadingPoints: false });
    });
  },

  /**
   * 加载分享记录
   */
  loadShareRecords: function () {
    console.log('开始加载分享记录');
    this.setData({ loadingRecords: true });
    
    // 获取openid，确保已登录
    const openid = wx.getStorageSync('openid');
    if (!openid) {
      console.error('加载分享记录失败: 用户未登录');
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      this.setData({ loadingRecords: false });
      return;
    }
    
    wx.cloud.callFunction({
      name: 'pointsManager',
      data: {
        action: 'getUserShareRecords'
      }
    }).then(res => {
      console.log('获取分享记录结果:', res.result);
      
      if (res.result && res.result.code === 0) {
        // 检查是否有分享记录
        if (!res.result.shareRecords || res.result.shareRecords.length === 0) {
          console.log('没有分享记录');
          this.setData({
            shareRecords: [],
            loadingRecords: false
          });
          return;
        }
        
        try {
          // 格式化分享记录，添加时间和内容类型显示
          const formattedRecords = res.result.shareRecords.map(record => {
            try {
              // 格式化时间
              let dateStr = '未知日期';
              let timeStr = '未知时间';
              
              if (record.createTime) {
                const createTime = new Date(record.createTime);
                dateStr = `${createTime.getFullYear()}-${String(createTime.getMonth() + 1).padStart(2, '0')}-${String(createTime.getDate()).padStart(2, '0')}`;
                timeStr = `${String(createTime.getHours()).padStart(2, '0')}:${String(createTime.getMinutes()).padStart(2, '0')}`;
              }
              
              // 格式化内容类型
              let contentTypeText = '未知内容';
              switch (record.contentType) {
                case 'service':
                  contentTypeText = '服务项目';
                  break;
                case 'article':
                  contentTypeText = '灵感文章';
                  break;
                case 'video':
                  contentTypeText = '视频内容';
                  break;
                default:
                  contentTypeText = '其他内容';
              }
              
              return {
                ...record,
                dateStr,
                timeStr,
                contentTypeText
              };
            } catch (err) {
              console.error('格式化分享记录失败:', err, record);
              // 返回原始记录，添加默认格式化字段
              return {
                ...record,
                dateStr: '格式化错误',
                timeStr: '格式化错误',
                contentTypeText: record.contentType || '未知内容'
              };
            }
          });
          
          console.log('格式化后的分享记录:', formattedRecords.length);
          
          this.setData({
            shareRecords: formattedRecords,
            loadingRecords: false
          });
        } catch (formatErr) {
          console.error('处理分享记录数据失败:', formatErr);
          this.setData({
            shareRecords: [],
            loadingRecords: false
          });
          wx.showToast({
            title: '处理分享记录失败',
            icon: 'none'
          });
        }
      } else {
        console.error('获取分享记录失败:', res.result ? res.result.message : '未知错误');
        wx.showToast({
          title: '获取分享记录失败',
          icon: 'none'
        });
        this.setData({ loadingRecords: false });
      }
    }).catch(err => {
      console.error('获取分享记录失败:', err);
      wx.showToast({
        title: '获取分享记录失败',
        icon: 'none'
      });
      this.setData({ loadingRecords: false });
    });
  },

  /**
   * 加载提现记录
   */
  loadWithdrawalRecords: function () {
    this.setData({ loadingWithdrawals: true });
    
    wx.cloud.callFunction({
      name: 'pointsManager',
      data: {
        action: 'getUserWithdrawals'
      }
    }).then(res => {
      console.log('获取提现记录成功:', res.result);
      
      if (res.result.code === 0) {
        // 格式化提现记录，添加时间和状态显示
        const formattedRecords = res.result.withdrawals.map(record => {
          // 格式化时间
          const createTime = new Date(record.createTime);
          const dateStr = `${createTime.getFullYear()}-${String(createTime.getMonth() + 1).padStart(2, '0')}-${String(createTime.getDate()).padStart(2, '0')}`;
          const timeStr = `${String(createTime.getHours()).padStart(2, '0')}:${String(createTime.getMinutes()).padStart(2, '0')}`;
          
          // 格式化状态
          let statusText = '';
          let statusClass = '';
          switch (record.status) {
            case 'pending':
              statusText = '提现中';
              statusClass = 'status-pending';
              break;
            case 'completed':
              statusText = '已提现';
              statusClass = 'status-completed';
              break;
            case 'rejected':
              statusText = '已拒绝';
              statusClass = 'status-rejected';
              break;
            default:
              statusText = '未知状态';
              statusClass = 'status-unknown';
          }
          
          // 确保备注字段存在，如果没有则使用默认值
          const remark = record.remark || (record.status === 'completed' ? '提现申请已通过，资金将尽快到账' : 
                                         (record.status === 'rejected' ? '提现申请被拒绝' : ''));
          
          return {
            ...record,
            dateStr,
            timeStr,
            statusText,
            statusClass,
            remark: remark
          };
        });
        
        // 检查是否有待处理的提现申请
        const pendingWithdrawal = formattedRecords.find(record => record.status === 'pending');
        
        this.setData({
          withdrawalRecords: formattedRecords,
          loadingWithdrawals: false,
          hasPendingWithdrawal: !!pendingWithdrawal,
          pendingWithdrawal: pendingWithdrawal || null
        });
      } else {
        wx.showToast({
          title: '获取提现记录失败',
          icon: 'none'
        });
        this.setData({ loadingWithdrawals: false });
      }
    }).catch(err => {
      console.error('获取提现记录失败:', err);
      wx.showToast({
        title: '获取提现记录失败',
        icon: 'none'
      });
      this.setData({ loadingWithdrawals: false });
    });
  },

  /**
   * 显示提现表单
   */
  showWithdrawalForm: function () {
    // 检查是否有积分可提现
    if (this.data.points <= 0) {
      wx.showToast({
        title: '暂无积分可提现',
        icon: 'none'
      });
      return;
    }
    
    // 检查是否有待处理的提现申请
    if (this.data.hasPendingWithdrawal) {
      wx.showToast({
        title: '您有提现申请正在处理中',
        icon: 'none'
      });
      return;
    }
    
    this.setData({
      showWithdrawalForm: true,
      paymentQrCode: '',
      description: ''
    });
  },

  /**
   * 隐藏提现表单
   */
  hideWithdrawalForm: function () {
    this.setData({
      showWithdrawalForm: false
    });
  },

  /**
   * 输入提现说明
   */
  inputDescription: function (e) {
    this.setData({
      description: e.detail.value
    });
  },

  /**
   * 上传收款码
   */
  uploadQrCode: function () {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: res => {
        const tempFilePath = res.tempFilePaths[0];
        
        // 显示上传中
        wx.showLoading({
          title: '上传中...',
          mask: true
        });
        
        // 上传图片到云存储
        const cloudPath = `payment_qrcodes/${Date.now()}_${Math.floor(Math.random() * 1000)}.${tempFilePath.match(/\.([^.]+)$/)[1]}`;
        
        wx.cloud.uploadFile({
          cloudPath: cloudPath,
          filePath: tempFilePath,
          success: res => {
            console.log('上传收款码成功:', res);
            this.setData({
              paymentQrCode: res.fileID
            });
            wx.hideLoading();
          },
          fail: err => {
            console.error('上传收款码失败:', err);
            wx.hideLoading();
            wx.showToast({
              title: '上传收款码失败',
              icon: 'none'
            });
          }
        });
      }
    });
  },

  /**
   * 预览收款码
   */
  previewQrCode: function (e) {
    const qrCode = e.currentTarget.dataset.qrcode;
    if (qrCode) {
      this.setData({
        showQrCodePreview: true,
        previewQrCode: qrCode
      });
    }
  },

  /**
   * 关闭收款码预览
   */
  closeQrCodePreview: function () {
    this.setData({
      showQrCodePreview: false
    });
  },

  /**
   * 提交提现申请
   */
  submitWithdrawal: function () {
    // 检查收款码是否已上传
    if (!this.data.paymentQrCode) {
      wx.showToast({
        title: '请上传收款码',
        icon: 'none'
      });
      return;
    }
    
    // 设置提交中状态
    this.setData({ submittingWithdrawal: true });
    
    // 调用云函数提交提现申请
    wx.cloud.callFunction({
      name: 'pointsManager',
      data: {
        action: 'submitWithdrawal',
        data: {
          points: this.data.points,
          paymentQrCode: this.data.paymentQrCode,
          description: this.data.description
        }
      }
    }).then(res => {
      console.log('提交提现申请结果:', res.result);
      
      if (res.result.code === 0) {
        wx.showToast({
          title: '提现申请已提交',
          icon: 'success'
        });
        
        // 隐藏表单并刷新数据
        this.setData({
          showWithdrawalForm: false,
          submittingWithdrawal: false
        });
        
        // 刷新积分和提现记录
        this.loadUserPoints();
        this.loadWithdrawalRecords();
      } else {
        wx.showToast({
          title: res.result.message || '提交提现申请失败',
          icon: 'none'
        });
        this.setData({ submittingWithdrawal: false });
      }
    }).catch(err => {
      console.error('提交提现申请失败:', err);
      wx.showToast({
        title: '提交提现申请失败',
        icon: 'none'
      });
      this.setData({ submittingWithdrawal: false });
    });
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh: function () {
    // 同时刷新所有数据
    Promise.all([
      this.loadUserPoints(),
      this.loadShareRecords(),
      this.loadWithdrawalRecords()
    ]).then(() => {
      wx.stopPullDownRefresh();
    }).catch(() => {
      wx.stopPullDownRefresh();
    });
  }
}) 