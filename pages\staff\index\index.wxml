<!-- 员工首页 -->
<scroll-view scroll-y="true" class="staff-index-scroll">
  <view class="staff-index-container">
    <!-- 顶部状态栏 -->
    <view class="page-header">
      <view class="back-btn" bindtap="goBack">←</view>
      <view class="header-title">{{staffInfo.name || '员工中心'}}</view>
      <view class="placeholder"></view>
    </view>
    
    <!-- 业绩概览 -->
    <view class="performance-overview">
      
      <view wx:if="{{loading}}" class="loading">
        <view class="loading-icon"></view>
        <view class="loading-text">加载中...</view>
      </view>
      
      <view wx:else class="performance-cards">
        <!-- 主要提成数据 -->
        <view class="main-performance">
          <view class="main-performance-card">
            <view class="main-card-title">总提成</view>
            <view class="main-card-value">¥{{totalCommission}}</view>
          </view>
          
          <view class="main-performance-card">
            <view class="main-card-title">今日提成</view>
            <view class="main-card-value">¥{{todayPerformance}}</view>
          </view>
        </view>
        
        <!-- 次要提成数据 -->
        <view class="secondary-performance">
          <view class="performance-card">
            <view class="card-title">昨日提成</view>
            <view class="card-value">¥{{yesterdayPerformance}}</view>
          </view>
          
          <view class="performance-card">
            <view class="card-title">本周提成</view>
            <view class="card-value">¥{{weekPerformance}}</view>
          </view>
          
          <view class="performance-card">
            <view class="card-title">本月提成</view>
            <view class="card-value">¥{{monthPerformance}}</view>
          </view>
        </view>
      </view>
      
      <!-- 佣金组成 -->
      <view class="commission-breakdown">
        <view class="commission-items">
          <view class="commission-item">
            <text class="commission-label">服务佣金</text>
            <text class="commission-value">
              <text class="plus-icon">+</text>¥{{serviceCommission || '0.00'}}
            </text>
          </view>
          <view class="commission-item">
            <text class="commission-label">推广佣金</text>
            <text class="commission-value highlight">
              <text class="plus-icon">+</text>¥{{promotionCommission || '0.00'}}
            </text>
          </view>
        </view>
      </view>
      
      <!-- 日期范围查询按钮 -->
      <view class="date-query-toggle" bindtap="toggleDateQuery">
        <text>{{showDateQuery ? '收起查询' : '按日期查询'}}</text>
        <text class="toggle-icon">{{showDateQuery ? '▲' : '▼'}}</text>
      </view>
    </view>
    
    <!-- 日期范围查询面板 -->
    <view class="date-query-panel" wx:if="{{showDateQuery}}">
      <view class="date-query-title">日期范围查询</view>
      
      <view class="date-picker-container">
        <view class="date-picker-item">
          <text class="date-label">开始日期</text>
          <picker mode="date" value="{{startDate}}" start="2020-01-01" end="2030-12-31" bindchange="bindStartDateChange">
            <view class="date-picker-value">{{startDate || '请选择'}}</view>
          </picker>
        </view>
        
        <view class="date-picker-item">
          <text class="date-label">结束日期</text>
          <picker mode="date" value="{{endDate}}" start="2020-01-01" end="2030-12-31" bindchange="bindEndDateChange">
            <view class="date-picker-value">{{endDate || '请选择'}}</view>
          </picker>
        </view>
      </view>
      
      <button class="query-button" bindtap="queryByDateRange" loading="{{dateRangeLoading}}">查询</button>
      
      <!-- 查询结果 -->
      <view class="date-range-result" wx:if="{{dateRangeResult}}">
        <view class="result-title">查询结果</view>
        <view class="result-item">
          <text class="result-label">时间范围</text>
          <text class="result-value">{{startDate}} 至 {{endDate}}</text>
        </view>
        <view class="result-item">
          <text class="result-label">提成总额</text>
          <text class="result-value highlight">¥{{dateRangeCommission}}</text>
        </view>
        <view class="result-item">
          <text class="result-label">订单数量</text>
          <text class="result-value">{{dateRangeCount}}单</text>
        </view>
      </view>
    </view>
    
    <!-- 状态切换按钮组 -->
    <view class="status-control">
      <view class="status-title">我的状态</view>
      <view class="status-buttons">
        <button class="status-btn {{serviceStatus === 'available' ? 'active' : ''}}" 
                bindtap="changeStatus" data-status="available">空闲</button>
        <button class="status-btn {{serviceStatus === 'busy' ? 'active' : ''}}" 
                bindtap="changeStatus" data-status="busy">服务中</button>
        <button class="status-btn {{serviceStatus === 'rest' ? 'active' : ''}}" 
                bindtap="changeStatus" data-status="rest">休息</button>
      </view>
    </view>
    
    <!-- 核销码输入区域 -->
    <view class="verify-code-section">
      <view class="verify-code-title">核销订单</view>
      <view class="verify-code-input-container">
        <input 
          class="verify-code-input" 
          type="number" 
          placeholder="输入核销码" 
          value="{{verifyCode}}" 
          bindinput="inputVerifyCode"
          maxlength="8"
        />
        <button 
          class="verify-button" 
          bindtap="verifyByCode" 
          loading="{{isVerifying}}"
        >核销</button>
      </view>
    </view>
    
    <!-- 充值核销码输入区域 -->
    <view class="verify-code-section">
      <view class="verify-code-title">核销充值</view>
      <view class="verify-code-input-container">
        <input 
          class="verify-code-input" 
          type="number" 
          placeholder="输入充值核销码" 
          value="{{rechargeVerifyCode}}" 
          bindinput="inputRechargeVerifyCode"
          maxlength="8"
        />
        <button 
          class="verify-button recharge-verify-button" 
          bindtap="verifyRechargeByCode" 
          loading="{{isVerifyingRecharge}}"
        >核销</button>
      </view>
    </view>
    
    <!-- 功能菜单 -->
    <view class="function-menu">
      <view class="function-item" bindtap="navigateToAssignedAppointments">
        <view class="function-icon appointment-icon">预</view>
        <view class="function-name">我的预约</view>
      </view>
      <view class="function-item" bindtap="navigateToCashOrders">
        <view class="function-icon order-icon">现</view>
        <view class="function-name">现金核销</view>
      </view>
      <view class="function-item" bindtap="navigateToBalanceOrders">
        <view class="function-icon balance-icon">余</view>
        <view class="function-name">余额核销</view>
      </view>
      <view class="function-item" bindtap="navigateToRechargeRecords">
        <view class="function-icon recharge-icon">充</view>
        <view class="function-name">充值核销</view>
      </view>
      <view class="function-item" bindtap="navigateToExpense">
        <view class="function-icon expense-icon">支</view>
        <view class="function-name">支出记录</view>
      </view>
    </view>
    
    <!-- 提示信息 -->
    <view class="tips">
      <view class="tip-item">* 我的预约：查看指定给您的预约记录</view>
      <view class="tip-item">* 核销订单：输入核销码完成订单</view>
      <view class="tip-item">* 核销充值：输入充值核销码为用户完成充值</view>
      <view class="tip-item">* 现金核销：查看已完成的现金支付核销记录</view>
      <view class="tip-item">* 余额核销：查看已完成的余额支付核销记录</view>
      <view class="tip-item">* 充值核销：查看已完成的充值核销记录</view>
      <view class="tip-item">* 支出记录：用于记录店铺日常经营支出或采购费用，请及时上传支出凭证以便报销</view>
    </view>
    
    <!-- 底部退出按钮 -->
    <view class="logout-container">
      <button class="logout-btn-bottom" bindtap="logout">退出登录</button>
    </view>
  </view>
</scroll-view> 