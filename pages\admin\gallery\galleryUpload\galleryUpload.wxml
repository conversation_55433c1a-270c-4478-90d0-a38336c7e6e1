<!-- components/galleryUpload/galleryUpload.wxml -->
<view class="gallery-upload-container {{visible ? 'visible' : ''}}">
  <view class="gallery-upload-content">
    <view class="gallery-upload-header">
      <view class="back-icon" catchtap="onClose">
        <text class="iconfont icon-back">←</text>
      </view>
      <text class="gallery-upload-title">{{isEdit ? '编辑画廊' : '添加画廊'}}</text>
      <view class="header-actions"></view>
    </view>
    
    <!-- 加载状态 -->
    <view class="loading-container" wx:if="{{isLoading}}">
      <view class="loading-spinner"></view>
      <text class="loading-text">加载中...</text>
    </view>
    
    <scroll-view class="gallery-upload-form" scroll-y wx:else>
      <!-- 主标题 -->
      <view class="form-item">
        <text class="form-label">主标题 <text class="required">*</text></text>
        <input class="form-input" value="{{mainTitle}}" bindinput="onInputMainTitle" placeholder="" />
      </view>
      
      <!-- 副标题 -->
      <view class="form-item">
        <text class="form-label">副标题</text>
        <input class="form-input" value="{{subTitle}}" bindinput="onInputSubTitle" placeholder="" />
      </view>
      
      <!-- 分类选择 -->
      <view class="form-item">
        <text class="form-label">分类 <text class="required">*</text></text>
        <view class="category-selector-container">
          <view class="category-selector" catchtap="showCategorySelector">
            <text class="category-name">{{selectedCategoryName || '请选择分类'}}</text>
            <view class="category-arrow">▼</view>
          </view>
          <view class="manage-category-btn" catchtap="showCategoryManager">
            <text>添加</text>
          </view>
        </view>
      </view>
      
      <!-- 封面图片 -->
      <view class="form-item">
        <text class="form-label">封面图片 <text class="required">*</text></text>
        <view class="image-upload" catchtap="chooseCoverImage">
          <block wx:if="{{coverUrl}}">
            <image class="cover-preview" src="{{coverUrl}}" mode="aspectFill"></image>
          </block>
          <block wx:else>
            <view class="upload-placeholder">
              <view class="upload-icon">+</view>
              <text class="upload-text">选择图片</text>
            </view>
          </block>
        </view>
        <view class="upload-tip" wx:if="{{!coverUrl}}">建议尺寸: 750×500 像素</view>
        <view class="upload-tip" wx:else>点击图片可更换</view>
      </view>
      
      <!-- 详情图片 - 修改为只显示一张 -->
      <view class="form-item">
        <text class="form-label">详情图片</text>
        <view class="detail-images">
          <!-- 有图片时显示第一张，点击可直接更换 -->
          <block wx:if="{{detailImages.length > 0}}">
            <view class="detail-image-item" catchtap="chooseDetailImages">
              <image class="detail-image" src="{{detailImages[0]}}" mode="aspectFill"></image>
            </view>
          </block>
          <!-- 没有图片时显示添加按钮 -->
          <view class="detail-image-add" catchtap="chooseDetailImages" wx:if="{{detailImages.length === 0}}">
            <view class="upload-icon">+</view>
            <text class="upload-text">选择图片</text>
          </view>
        </view>
        <view class="upload-tip" wx:if="{{detailImages.length === 0}}">请上传详情图片</view>
        <view class="upload-tip" wx:else>点击图片可更换</view>
      </view>
    </scroll-view>
    
    <view class="gallery-upload-footer">
      <button class="cancel-btn" catchtap="onClose">取消</button>
      <button class="save-btn" catchtap="saveGallery" loading="{{isSaving}}">保存</button>
    </view>
  </view>
</view>

<!-- 分类选择弹窗 -->
<view class="category-picker-mask {{showCategoryPicker ? 'visible' : ''}}" catchtap="hideCategorySelector"></view>
<view class="category-picker {{showCategoryPicker ? 'visible' : ''}}">
  <view class="category-picker-header">
    <text>选择分类</text>
    <view class="category-picker-close" catchtap="hideCategorySelector">×</view>
  </view>
  <scroll-view class="category-picker-content" scroll-y>
    <block wx:if="{{categories.length === 0}}">
      <view class="category-empty">{{categories.length === 0 ? '加载中...' : '暂无分类'}}</view>
    </block>
    <block wx:else>
      <view 
        class="category-item {{selectedCategoryId === item._id ? 'selected' : ''}}" 
        wx:for="{{categories}}" 
        wx:key="_id"
        data-id="{{item._id}}"
        data-name="{{item.name}}"
        catchtap="onSelectCategory"
      >
        <text>{{item.name}}</text>
        <view wx:if="{{selectedCategoryId === item._id}}" class="category-selected-icon">✓</view>
      </view>
    </block>
  </scroll-view>
</view>

<!-- 分类管理模态框 -->
<view class="category-manager-modal" wx:if="{{showCategoryManagerModal}}">
  <view class="category-manager-container">
    <view class="category-manager-header">
      <text class="category-manager-title">添加分类</text>
      <view class="category-manager-close" bindtap="hideCategoryManager">×</view>
    </view>
    
    <view class="category-manager-content">
      <view class="category-add-section">
        <input class="category-input" placeholder="输入新分类名称" value="{{newCategoryName}}" bindinput="onInputNewCategoryName" />
        <button class="category-add-btn" bindtap="addNewCategory" loading="{{isAddingCategory}}">添加</button>
      </view>
      
      <view class="category-list">
        <block wx:if="{{categories.length > 0}}">
          <view class="category-item" wx:for="{{categories}}" wx:key="_id">
            <text class="category-item-name">{{item.name}}</text>
            <view class="category-item-delete" data-id="{{item._id}}" bindtap="deleteCategory">删除</view>
          </view>
        </block>
        <view class="no-categories" wx:else>暂无分类</view>
      </view>
    </view>
  </view>
</view> 