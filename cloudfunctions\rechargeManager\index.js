// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV }) // 使用当前云环境
const db = cloud.database()
const _ = db.command
const $ = db.command.aggregate

// 获取当前时间
function getCurrentTime() {
  return db.serverDate()
}

// 获取北京时间的辅助函数（用于充值核销时间统一）
function getBJTime() {
  // 获取当前UTC时间
  const now = new Date();
  // 计算北京时间（UTC+8）
  const bjTime = new Date(now.getTime() + 8 * 60 * 60 * 1000);
  return bjTime;
}

// 格式化日期时间的辅助函数（与预约核销保持一致）
function formatDateTime(date) {
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
}

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID
  
  console.log('rechargeManager云函数被调用，参数:', event)
  
  try {
    // 初始化数据库集合
    await initCollections()
  } catch (err) {
    console.error('初始化集合过程中出错，但将继续执行:', err)
  }
  
  const { type, action } = event
  
  // 管理员API
  if (type === 'admin') {
    switch (action) {
      case 'addRechargePlan': return addRechargePlan(event.data);
      case 'updateRechargePlan': return updateRechargePlan(event.data);
      case 'deleteRechargePlan': return deleteRechargePlan(event.data);
      case 'getRechargePlans': return getRechargePlans(event.data);
      case 'getRechargeRecords': return getRechargeRecords(event.data);
      case 'getBalanceConsumptions': return getBalanceConsumptions(event.data);
      case 'getUserBalanceStats': return getUserBalanceStats(event.data);
      case 'refundUserBalance': return refundUserBalance(event.data);
      case 'getRechargeStats': return getRechargeStats(event.data);
      case 'getCommissionList': return getCommissionList(event.query, event.skip, event.limit);
      case 'settleCommission': return settleCommission(event.ids);
      default: return { code: 400, message: '无效的操作' };
    }
  }
  
  // 用户端API
  if (type === 'frontend') {
    switch (action) {
      case 'getPlans': return getUserRechargePlans();
      case 'createRechargeOrder': return createRechargeOrder(event.openid || openid, event.planId);
      case 'cancelRechargeOrder': return cancelRechargeOrder(event.openid || openid, event.recordId);
      case 'getRechargeRecords': return getUserRechargeRecords(event.openid || openid, event);
      case 'getUserBalance': return getUserBalance(event.openid || openid);
      case 'getBalanceConsumptions': return getUserBalanceConsumptions(event.openid || openid, event);
      case 'hideRechargeRecord': return hideRechargeRecord(event.openid || openid, event.recordId);
      default: return { code: 400, message: '无效的操作' };
    }
  }
  
  // 员工端API
  if (type === 'staff') {
    switch (action) {
      case 'verifyRecharge': return verifyRecharge(event.data);
      case 'balancePayment': return balancePayment(event.data);
      case 'getStaffCommissions': return getStaffCommissions(event.data);
      case 'getOperatorRechargeRecords': return getOperatorRechargeRecords(event.data);
      default: return { code: 400, message: '无效的操作' };
    }
  }
  
  return { code: 400, message: '无效的请求类型' };
}

// 初始化数据库集合
async function initCollections() {
  try {
    // 获取所有集合列表
    let collections;
    try {
      collections = await db.listCollections().get();
    } catch (err) {
      console.error('获取集合列表失败:', err);
      // 如果无法获取集合列表，尝试直接创建集合
      await createCollectionsSafely();
      return true;
    }
    
    const collectionNames = collections.data.map(collection => collection.name);
    console.log('现有集合列表:', collectionNames);
    
    // 需要创建的集合列表
    const requiredCollections = [
      'recharge_plans',
      'recharge_records',
      'balance_consumption',
      'commission_settlements',
      'expenses'  // 添加支出记录表
    ];
    
    // 检查并创建每个必需的集合
    for (const collectionName of requiredCollections) {
      if (!collectionNames.includes(collectionName)) {
        try {
          await db.createCollection(collectionName);
          console.log(`成功创建集合: ${collectionName}`);
        } catch (err) {
          console.error(`创建集合 ${collectionName} 失败:`, err);
          // 继续尝试创建其他集合，不中断流程
        }
      } else {
        console.log(`集合 ${collectionName} 已存在`);
      }
    }
    
    return true;
  } catch (err) {
    console.error('初始化集合失败:', err);
    return false;
  }
}

// 安全创建集合的辅助函数
async function createCollectionsSafely() {
  const collections = [
    'recharge_plans',
    'recharge_records',
    'balance_consumption',
    'commission_settlements',
    'expenses'  // 添加支出记录表
  ];
  
  for (const collection of collections) {
    try {
      await db.createCollection(collection);
      console.log(`创建集合成功: ${collection}`);
    } catch (err) {
      // 如果错误是因为集合已存在，这是正常的
      if (err.message && err.message.indexOf('collection already exists') !== -1) {
        console.log(`集合 ${collection} 已存在`);
      } else {
        console.error(`创建集合 ${collection} 失败:`, err);
      }
    }
  }
}

// ==================== 充值方案相关功能 ====================

// 添加充值方案
async function addRechargePlan(data) {
  if (!data.title || !data.amount || !data.bonus) {
    return {
      code: 400,
      message: '缺少必要参数'
    };
  }
  
  try {
    // 准备方案数据
    const planData = {
      title: data.title,
      description: data.description || '',
      amount: Number(data.amount),
      bonus: Number(data.bonus),
      totalAmount: Number(data.amount) + Number(data.bonus),
      promotionCommission: Number(data.promotionCommission || 0),
      image: data.image || '',
      isVisible: data.isVisible !== false, // 默认显示
      sortOrder: data.sortOrder || 0,
      createTime: getCurrentTime(),
      updateTime: getCurrentTime()
    };
    
    console.log('准备创建充值方案:', planData);
    
    // 添加到数据库
    const result = await db.collection('recharge_plans').add({
      data: planData
    });
    
    console.log('创建充值方案成功:', result);
    
    return {
      code: 0,
      data: {
        id: result._id
      },
      message: '创建充值方案成功'
    };
  } catch (err) {
    console.error('创建充值方案失败:', err);
    return {
      code: -1,
      message: '创建充值方案失败: ' + err.message
    };
  }
}

// 更新充值方案
async function updateRechargePlan(data) {
  if (!data.id) {
    return {
      code: 400,
      message: '缺少方案ID'
    };
  }
  
  try {
    // 检查方案是否存在
    try {
      await db.collection('recharge_plans').doc(data.id).get();
    } catch (err) {
      return {
        code: 404,
        message: '充值方案不存在'
      };
    }
    
    // 准备更新数据
    const updateData = {
      updateTime: getCurrentTime()
    };
    
    // 只更新提供的字段
    if (data.title !== undefined) updateData.title = data.title;
    if (data.description !== undefined) updateData.description = data.description;
    if (data.amount !== undefined) {
      updateData.amount = Number(data.amount);
      // 如果更新了充值金额，也要更新总金额
      if (data.bonus !== undefined) {
        updateData.totalAmount = Number(data.amount) + Number(data.bonus);
      } else {
        // 获取当前bonus
        const plan = await db.collection('recharge_plans').doc(data.id).get();
        updateData.totalAmount = Number(data.amount) + Number(plan.data.bonus || 0);
      }
    }
    if (data.bonus !== undefined) {
      updateData.bonus = Number(data.bonus);
      // 如果更新了赠送金额，也要更新总金额
      if (data.amount !== undefined) {
        updateData.totalAmount = Number(data.amount) + Number(data.bonus);
      } else {
        // 获取当前amount
        const plan = await db.collection('recharge_plans').doc(data.id).get();
        updateData.totalAmount = Number(plan.data.amount || 0) + Number(data.bonus);
      }
    }
    if (data.promotionCommission !== undefined) updateData.promotionCommission = Number(data.promotionCommission);
    if (data.image !== undefined) updateData.image = data.image;
    if (data.isVisible !== undefined) updateData.isVisible = data.isVisible;
    if (data.sortOrder !== undefined) updateData.sortOrder = data.sortOrder;
    
    console.log('更新充值方案:', data.id, updateData);
    
    // 更新数据库
    await db.collection('recharge_plans').doc(data.id).update({
      data: updateData
    });
    
    return {
      code: 0,
      message: '更新充值方案成功'
    };
  } catch (err) {
    console.error('更新充值方案失败:', err);
    return {
      code: -1,
      message: '更新充值方案失败: ' + err.message
    };
  }
}

// 删除充值方案
async function deleteRechargePlan(data) {
  if (!data.id) {
    return {
      code: 400,
      message: '缺少方案ID'
    };
  }
  
  try {
    console.log('删除充值方案:', data.id);
    
    // 删除方案
    await db.collection('recharge_plans').doc(data.id).remove();
    
    return {
      code: 0,
      message: '删除充值方案成功'
    };
  } catch (err) {
    console.error('删除充值方案失败:', err);
    return {
      code: -1,
      message: '删除充值方案失败: ' + err.message
    };
  }
}

// 获取充值方案列表
async function getRechargePlans(data) {
  try {
    console.log('获取充值方案列表');
    
    // 构建查询
    let query = db.collection('recharge_plans');
    
    // 如果指定了isVisible字段
    if (data && data.isVisible !== undefined) {
      query = query.where({
        isVisible: data.isVisible
      });
    }
    
    // 排序（默认按sortOrder和createTime排序）
    query = query.orderBy('sortOrder', 'desc').orderBy('createTime', 'desc');
    
    // 分页
    const pageSize = (data && data.pageSize) || 100; // 默认100条
    const page = (data && data.page) || 1;
    
    // 查询数据
    const plans = await query.skip((page - 1) * pageSize).limit(pageSize).get();
    
    return {
      code: 0,
      data: plans.data,
      message: '获取充值方案成功'
    };
  } catch (err) {
    console.error('获取充值方案失败:', err);
    return {
      code: -1,
      message: '获取充值方案失败: ' + err.message
    };
  }
}

// 获取用户端充值方案列表（只返回可见的方案）
async function getUserRechargePlans() {
  try {
    console.log('获取用户端充值方案列表');
    
    // 只查询可见的方案
    const plans = await db.collection('recharge_plans')
      .where({
        isVisible: true
      })
      .orderBy('sortOrder', 'desc')
      .orderBy('createTime', 'desc')
      .get();
    
    return {
      code: 0,
      data: plans.data,
      message: '获取充值方案成功'
    };
  } catch (err) {
    console.error('获取用户端充值方案失败:', err);
    return {
      code: -1,
      message: '获取充值方案失败: ' + err.message
    };
  }
}

// ==================== 充值记录相关功能 ====================

// 创建充值订单
async function createRechargeOrder(openid, planId) {
  if (!planId) {
    return {
      success: false,
      message: '缺少充值方案ID'
    };
  }
  
  try {
    // 查询充值方案
    let plan;
    try {
      plan = await db.collection('recharge_plans').doc(planId).get();
      plan = plan.data;
    } catch (err) {
      return {
        success: false,
        message: '充值方案不存在'
      };
    }
    
    // 检查方案是否可见
    if (!plan.isVisible) {
      return {
        success: false,
        message: '该充值方案不可用'
      };
    }
    
    // 生成订单号
    const orderId = `RC${Date.now()}${Math.floor(Math.random() * 1000)}`;
    
    // 生成6位核销码
    const verifyCode = Math.floor(100000 + Math.random() * 900000).toString();
    
    // 创建充值记录
    const rechargeRecord = {
      openid: openid,
      orderId: orderId,
      planId: planId,
      planTitle: plan.title,
      amount: plan.amount,
      bonus: plan.bonus,
      totalAmount: plan.totalAmount,
      promoterCommission: plan.promotionCommission || 0, // 推广佣金
      status: 'pending', // pending（待核销）, verified（已核销）, cancelled（已取消）
      paymentMethod: 'offline',
      verifyCode: verifyCode, // 核销码
      operatorId: null, // 核销员
      promoter: null, // 推广员openid，在核销时填充
      promoterId: null, // 推广员ID，在核销时填充
      remark: '',
      createTime: getCurrentTime(),
      updateTime: getCurrentTime(),
      verifyTime: null,
    };
    
    console.log('创建充值记录:', rechargeRecord);
    
    // 添加到数据库
    const result = await db.collection('recharge_records').add({
      data: rechargeRecord
    });
    
    return {
      success: true,
      orderId: orderId,
      recordId: result._id,
      verifyCode: verifyCode,
      message: '创建充值订单成功'
    };
  } catch (err) {
    console.error('创建充值订单失败:', err);
    return {
      success: false,
      message: '创建充值订单失败: ' + err.message
    };
  }
}

// 取消充值订单（用户端）
async function cancelRechargeOrder(openid, recordId) {
  if (!recordId) {
    return {
      success: false,
      message: '缺少充值记录ID'
    };
  }

  try {
    // 查询充值记录
    let record;
    try {
      record = await db.collection('recharge_records').doc(recordId).get();
      record = record.data;
    } catch (err) {
      return {
        success: false,
        message: '充值记录不存在'
      };
    }

    // 检查记录是否属于当前用户
    if (record.openid !== openid) {
      return {
        success: false,
        message: '无权限操作此记录'
      };
    }

    // 检查记录状态，只有pending状态的记录可以取消
    if (record.status !== 'pending') {
      return {
        success: false,
        message: '该充值记录无法取消'
      };
    }

    // 直接删除充值记录，不保留任何痕迹（用户关闭弹窗，不进行充值）
    await db.collection('recharge_records').doc(recordId).remove();

    console.log('删除临时充值订单成功:', recordId);

    return {
      success: true,
      message: '删除临时充值订单成功'
    };
  } catch (err) {
    console.error('取消充值订单失败:', err);
    return {
      success: false,
      message: '取消充值订单失败: ' + err.message
    };
  }
}

// 用户隐藏充值记录（仅前端不显示，后台依然可查）
async function hideRechargeRecord(openid, recordId) {
  if (!recordId) {
    return {
      success: false,
      message: '缺少充值记录ID'
    };
  }

  try {
    // 查询充值记录
    let record;
    try {
      record = await db.collection('recharge_records').doc(recordId).get();
      record = record.data;
    } catch (err) {
      return {
        success: false,
        message: '充值记录不存在'
      };
    }

    // 检查记录是否属于当前用户
    if (record.openid !== openid) {
      return {
        success: false,
        message: '无权限操作此记录'
      };
    }

    // 检查记录状态，只有已核销的记录可以隐藏
    if (record.status !== 'verified') {
      return {
        success: false,
        message: '只能隐藏已核销的记录'
      };
    }

    // 更新记录，添加用户隐藏标记
    await db.collection('recharge_records').doc(recordId).update({
      data: {
        hiddenByUser: true,
        hideTime: getCurrentTime(),
        updateTime: getCurrentTime()
      }
    });

    console.log('用户隐藏充值记录成功:', recordId);

    return {
      success: true,
      message: '记录已隐藏'
    };
  } catch (err) {
    console.error('隐藏充值记录失败:', err);
    return {
      success: false,
      message: '隐藏记录失败: ' + err.message
    };
  }
}

// 核销充值订单（员工端）
async function verifyRecharge(data) {
  if (!data.verifyCode && !data.orderId && !data.id) {
    return {
      code: 400,
      message: '缺少核销码或订单ID'
    };
  }
  
  if (!data.operatorId) {
    return {
      code: 400,
      message: '缺少操作人ID'
    };
  }
  
  try {
    // 查询充值记录
    let query = {};
    if (data.verifyCode) {
      query.verifyCode = data.verifyCode;
    } else if (data.orderId) {
      query.orderId = data.orderId;
    } else {
      query._id = data.id;
    }
    
    const record = await db.collection('recharge_records').where(query).get();
    
    if (record.data.length === 0) {
      return {
        code: 404,
        message: '充值订单不存在或核销码无效'
      };
    }
    
    const rechargeRecord = record.data[0];
    
    // 检查订单状态
    if (rechargeRecord.status === 'verified') {
      return {
        code: 400,
        message: '该订单已被核销'
      };
    }
    
    if (rechargeRecord.status === 'cancelled') {
      return {
        code: 400,
        message: '该订单已被取消'
      };
    }
    
    // 更新订单状态 - 使用北京时间格式与预约核销保持一致
    const beijingTime = getBJTime();
    const verifyTimeStr = formatDateTime(beijingTime);

    console.log('充值核销时间（北京时间）:', verifyTimeStr);

    // 将核销员工作为推广员
    const updateData = {
      status: 'verified',
      operatorId: data.operatorId,
      operatorName: data.operatorName || '',
      verifyTime: verifyTimeStr, // 使用北京时间字符串格式
      updateTime: getCurrentTime(), // updateTime保持使用原来的格式
      promoterId: data.operatorId, // 核销员工ID作为推广员ID
      promoter: data.operatorId // 核销员工ID作为推广员OpenID (后续会获取实际OpenID)
    };
    
    // 从staff集合中获取员工openid
    try {
      const staffInfo = await db.collection('staff').doc(data.operatorId).get();
      if (staffInfo.data && staffInfo.data.openid) {
        updateData.promoter = staffInfo.data.openid; // 使用员工的openid
        console.log('已获取员工openid作为推广员:', staffInfo.data.openid);
      }
    } catch (err) {
      console.error('获取员工信息失败:', err);
    }

    // 打印最终更新的数据
    console.log('更新充值记录数据:', updateData);

    const updateResult = await db.collection('recharge_records').doc(rechargeRecord._id).update({
      data: updateData
    });
    
    // 更新用户余额
    await updateUserBalance(
      rechargeRecord.openid, 
      rechargeRecord.amount, 
      rechargeRecord.bonus,
      rechargeRecord._id,
      '充值'
    );
    
    // 处理推广佣金 - 核销员工获得推广佣金
    if (rechargeRecord.promoterCommission > 0) {
      // 使用核销员工的ID作为推广员ID
      const promoterStaffId = data.operatorId;

      console.log('记录推广佣金，推广员工ID:', promoterStaffId, '佣金金额:', rechargeRecord.promoterCommission);

      await recordCommission(
        promoterStaffId,
        rechargeRecord.openid,
        rechargeRecord._id,
        rechargeRecord.promoterCommission
      );
    }
    
    return {
      code: 0,
      message: '核销充值订单成功',
      data: {
        recordId: rechargeRecord._id,
        amount: rechargeRecord.amount,
        bonus: rechargeRecord.bonus,
        totalAmount: rechargeRecord.amount + rechargeRecord.bonus
      }
    };
  } catch (err) {
    console.error('核销充值订单失败:', err);
    return {
      code: -1,
      message: '核销充值订单失败: ' + err.message
    };
  }
}

// 记录推广佣金
async function recordCommission(promoterStaffId, customerOpenid, rechargeRecordId, amount) {
  try {
    console.log('开始记录推广佣金:', {
      promoterStaffId,
      customerOpenid,
      rechargeRecordId,
      amount
    });

    // 添加佣金记录
    const result = await db.collection('commission_settlements').add({
      data: {
        promoterStaffId,        // 使用员工ID而不是openid
        promoterOpenid: null,   // 保留字段以兼容旧数据，但设为null
        customerOpenid,
        rechargeRecordId,
        amount,
        status: 'pending', // pending（待结算）, settled（已结算）
        createTime: getCurrentTime(),
        updateTime: getCurrentTime(),
        settleTime: null
      }
    });

    console.log(`已记录推广佣金成功: 推广员工ID ${promoterStaffId}, 金额 ${amount}, 记录ID: ${result._id}`);
    return true;
  } catch (err) {
    console.error('记录推广佣金失败:', err);
    return false;
  }
}

// 更新用户余额
async function updateUserBalance(openid, amount, bonus, recordId, remark) {
  const _ = db.command;
  
  try {
    // 查询用户信息
    const userResult = await db.collection('users').where({
      openid: openid
    }).get();
    
    if (userResult.data.length === 0) {
      console.error('用户不存在:', openid);
      return false;
    }
    
    const user = userResult.data[0];
    
    // 当前余额
    const currentBalance = user.balance || 0;
    const currentBonus = user.bonusBalance || 0;
    
    // 新余额
    const newBalance = currentBalance + amount;
    const newBonus = currentBonus + bonus;
    
    // 更新用户余额
    await db.collection('users').doc(user._id).update({
      data: {
        balance: newBalance,
        bonusBalance: newBonus,
        updateTime: getCurrentTime()
      }
    });
    
    console.log(`用户余额更新: ${openid}, 充值金额: ${amount}, 赠送金额: ${bonus}, 余额: ${newBalance}, 赠送余额: ${newBonus}`);
    return true;
  } catch (err) {
    console.error('更新用户余额失败:', err);
    return false;
  }
}

// 获取用户充值记录
async function getUserRechargeRecords(openid, data) {
  try {
    // 构建查询条件
    let whereCondition = {
      openid: openid,
      hiddenByUser: _.neq(true) // 过滤掉用户隐藏的记录
    };

    // 根据状态筛选
    if (data && data.status) {
      whereCondition.status = data.status;
    }

    // 分页
    const pageSize = (data && data.pageSize) || 10;
    const page = (data && data.page) || 1;

    // 构建查询
    const query = db.collection('recharge_records')
      .where(whereCondition)
      .orderBy('createTime', 'desc')
      .skip((page - 1) * pageSize)
      .limit(pageSize);

    // 查询数据
    const records = await query.get();

    console.log('获取用户充值记录:', {
      openid: openid,
      whereCondition: whereCondition,
      pageSize: pageSize,
      page: page,
      recordsCount: records.data.length,
      records: records.data.map(r => ({
        _id: r._id,
        orderId: r.orderId,
        status: r.status,
        verifyCode: r.verifyCode,
        createTime: r.createTime,
        planTitle: r.planTitle
      }))
    });

    return {
      success: true,
      records: records.data,
      total: records.data.length,
      message: '获取充值记录成功'
    };
  } catch (err) {
    console.error('获取用户充值记录失败:', err);
    return {
      success: false,
      message: '获取充值记录失败: ' + err.message
    };
  }
}

// 管理员获取充值记录
async function getRechargeRecords(data) {
  try {
    // 构建查询条件
    let condition = {};
    
    // 根据状态筛选
    if (data && data.status) {
      condition.status = data.status;
    }
    
    // 根据用户openid筛选
    if (data && data.openid) {
      condition.openid = data.openid;
    }
    
    // 根据操作员筛选
    if (data && data.operatorId) {
      condition.operatorId = data.operatorId;
    }
    
    // 根据日期范围筛选
    if (data && data.startDate && data.endDate) {
      condition.createTime = _.and(_.gte(new Date(data.startDate)), _.lte(new Date(data.endDate)));
    }
    
    // 构建查询
    let query = db.collection('recharge_records').where(condition);
    
    // 分页
    const pageSize = (data && data.pageSize) || 20;
    const page = (data && data.page) || 1;
    
    // 排序
    query = query.orderBy('createTime', 'desc');
    
    // 查询数据
    const records = await query.skip((page - 1) * pageSize).limit(pageSize).get();
    
    // 统计总数 - 使用相同的条件，而不是从query对象中获取
    const countResult = await db.collection('recharge_records').where(condition).count();
    
    return {
      code: 0,
      data: {
        list: records.data,
        total: countResult.total,
        page: page,
        pageSize: pageSize
      },
      message: '获取充值记录成功'
    };
  } catch (err) {
    console.error('获取充值记录失败:', err);
    return {
      code: -1,
      message: '获取充值记录失败: ' + err.message
    };
  }
}

// ==================== 余额消费相关功能 ====================

// 余额支付
async function balancePayment(data) {
  if (!data.openid || !data.amount) {
    return {
      code: 400,
      message: '缺少必要参数'
    };
  }
  
  try {
    const openid = data.openid;
    const amount = Number(data.amount);
    const operatorId = data.operatorId;
    const remark = data.remark || '消费';
    
    // 查询用户信息
    const userResult = await db.collection('users').where({
      openid: openid
    }).get();
    
    if (userResult.data.length === 0) {
      return {
        code: 404,
        message: '用户不存在'
      };
    }
    
    const user = userResult.data[0];
    
    // 检查余额
    const currentBalance = user.balance || 0;
    const currentBonus = user.bonusBalance || 0;
    const totalAvailable = currentBalance + currentBonus;
    
    if (totalAvailable < amount) {
      return {
        code: 400,
        message: '余额不足'
      };
    }
    
    // 计算扣除规则（优先使用赠送金额）
    let deductBonus = Math.min(currentBonus, amount);
    let deductBalance = amount - deductBonus;
    
    // 更新用户余额
    await db.collection('users').doc(user._id).update({
      data: {
        balance: currentBalance - deductBalance,
        bonusBalance: currentBonus - deductBonus,
        updateTime: getCurrentTime()
      }
    });
    
    // 记录消费记录
    const consumptionRecord = {
      openid: openid,
      amount: amount,
      deductBalance: deductBalance,
      deductBonus: deductBonus,
      operatorId: operatorId,
      type: 'consumption', // consumption（消费）, refund（退款）
      remark: remark,
      createTime: getCurrentTime()
    };
    
    await db.collection('balance_consumption').add({
      data: consumptionRecord
    });
    
    return {
      code: 0,
      data: {
        balance: currentBalance - deductBalance,
        bonusBalance: currentBonus - deductBonus,
        deductBalance: deductBalance,
        deductBonus: deductBonus
      },
      message: '支付成功'
    };
  } catch (err) {
    console.error('余额支付失败:', err);
    return {
      code: -1,
      message: '余额支付失败: ' + err.message
    };
  }
}

// 获取用户余额
async function getUserBalance(openid) {
  try {
    // 查询用户信息
    const userResult = await db.collection('users').where({
      openid: openid
    }).get();
    
    if (userResult.data.length === 0) {
      return {
        success: true,
        balance: 0,
        bonusBalance: 0, 
        totalBalance: 0,
        message: '用户不存在，余额为0'
      };
    }
    
    const user = userResult.data[0];
    const balance = user.balance || 0;
    const bonusBalance = user.bonusBalance || 0;
    
    return {
      success: true,
      balance: balance,
      bonusBalance: bonusBalance,
      totalBalance: balance + bonusBalance,
      message: '获取余额成功'
    };
  } catch (err) {
    console.error('获取用户余额失败:', err);
    return {
      success: false,
      message: '获取余额失败: ' + err.message,
      balance: 0,
      bonusBalance: 0,
      totalBalance: 0
    };
  }
}

// 获取用户余额消费记录
async function getUserBalanceConsumptions(openid, data) {
  try {
    // 构建查询
    const query = db.collection('balance_consumption').where({
      openid: openid
    });
    
    // 根据类型筛选
    if (data && data.type) {
      query.where({
        type: data.type
      });
    }
    
    // 分页
    const pageSize = (data && data.pageSize) || 10;
    const page = (data && data.page) || 1;
    
    // 排序
    query.orderBy('createTime', 'desc');
    
    // 查询数据
    const records = await query.skip((page - 1) * pageSize).limit(pageSize).get();
    
    return {
      code: 0,
      data: records.data,
      message: '获取消费记录成功'
    };
  } catch (err) {
    console.error('获取用户消费记录失败:', err);
    return {
      code: -1,
      message: '获取消费记录失败: ' + err.message
    };
  }
}

// 管理员获取余额消费记录
async function getBalanceConsumptions(data) {
  try {
    // 构建查询条件
    let condition = {};
    
    // 根据类型筛选
    if (data && data.type) {
      condition.type = data.type;
    }
    
    // 根据用户openid筛选
    if (data && data.openid) {
      condition.openid = data.openid;
    }
    
    // 根据操作员筛选
    if (data && data.operatorId) {
      condition.operatorId = data.operatorId;
    }
    
    // 根据日期范围筛选
    if (data && data.startDate && data.endDate) {
      condition.createTime = _.and(_.gte(new Date(data.startDate)), _.lte(new Date(data.endDate)));
    }
    
    // 分页
    const pageSize = (data && data.pageSize) || 20;
    const page = (data && data.page) || 1;
    
    // 查询数据
    const records = await db.collection('balance_consumption')
      .where(condition)
      .orderBy('createTime', 'desc')
      .skip((page - 1) * pageSize)
      .limit(pageSize)
      .get();
    
    // 统计总数 - 使用相同的条件，而不是从query对象中获取
    const countResult = await db.collection('balance_consumption').where(condition).count();
    
    return {
      code: 0,
      data: {
        list: records.data,
        total: countResult.total,
        page: page,
        pageSize: pageSize
      },
      message: '获取消费记录成功'
    };
  } catch (err) {
    console.error('获取消费记录失败:', err);
    return {
      code: -1,
      message: '获取消费记录失败: ' + err.message
    };
  }
}

// ==================== 统计和佣金相关功能 ====================

// 获取充值统计数据
async function getRechargeStats(data) {
  try {
    // 默认获取最近30天的数据
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - 30);
    
    // 如果提供了日期范围，则使用提供的日期
    const start = data && data.startDate ? new Date(data.startDate) : startDate;
    const end = data && data.endDate ? new Date(data.endDate) : endDate;
    
    // 汇总充值记录
    const rechargeStats = await db.collection('recharge_records')
      .aggregate()
      .match({
        status: 'verified',
        verifyTime: _.and(_.gte(start), _.lte(end))
      })
      .group({
        _id: null,
        totalAmount: $.sum('$amount'),
        totalBonus: $.sum('$bonus'),
        totalCount: $.sum(1),
        avgAmount: $.avg('$amount')
      })
      .end();
      
    // 汇总消费记录
    const consumptionStats = await db.collection('balance_consumption')
      .aggregate()
      .match({
        type: 'consumption',
        createTime: _.and(_.gte(start), _.lte(end))
      })
      .group({
        _id: null,
        totalConsumption: $.sum('$amount'),
        consumptionCount: $.sum(1),
        avgConsumption: $.avg('$amount'),
        totalDeductBalance: $.sum('$deductBalance'),
        totalDeductBonus: $.sum('$deductBonus')
      })
      .end();
      
    // 获取日期范围内每天的充值和消费数据
    const dailyStats = await getDailyStats(start, end);
    
    return {
      code: 0,
      data: {
        recharge: rechargeStats.list[0] || {
          totalAmount: 0,
          totalBonus: 0,
          totalCount: 0,
          avgAmount: 0
        },
        consumption: consumptionStats.list[0] || {
          totalConsumption: 0,
          consumptionCount: 0,
          avgConsumption: 0,
          totalDeductBalance: 0,
          totalDeductBonus: 0
        },
        daily: dailyStats
      },
      message: '获取统计数据成功'
    };
  } catch (err) {
    console.error('获取充值统计数据失败:', err);
    return {
      code: -1,
      message: '获取统计数据失败: ' + err.message
    };
  }
}

// 获取每天的充值和消费统计
async function getDailyStats(startDate, endDate) {
  // 创建日期范围内的每一天
  const days = [];
  const currentDate = new Date(startDate);
  
  while (currentDate <= endDate) {
    days.push(new Date(currentDate));
    currentDate.setDate(currentDate.getDate() + 1);
  }
  
  // 格式化日期为 YYYY-MM-DD 格式
  function formatDate(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }
  
  // 准备每日数据结构
  const dailyStats = days.map(day => {
    return {
      date: formatDate(day),
      rechargeAmount: 0,
      rechargeCount: 0,
      consumptionAmount: 0,
      consumptionCount: 0
    };
  });
  
  try {
    // 获取每日充值数据
    const rechargeDailyStats = await db.collection('recharge_records')
      .aggregate()
      .match({
        status: 'verified',
        verifyTime: _.and(_.gte(startDate), _.lte(endDate))
      })
      .addFields({
        dateString: $.dateToString({
          date: '$verifyTime',
          format: '%Y-%m-%d'
        })
      })
      .group({
        _id: '$dateString',
        rechargeAmount: $.sum('$amount'),
        rechargeCount: $.sum(1)
      })
      .end();
      
    // 获取每日消费数据
    const consumptionDailyStats = await db.collection('balance_consumption')
      .aggregate()
      .match({
        type: 'consumption',
        createTime: _.and(_.gte(startDate), _.lte(endDate))
      })
      .addFields({
        dateString: $.dateToString({
          date: '$createTime',
          format: '%Y-%m-%d'
        })
      })
      .group({
        _id: '$dateString',
        consumptionAmount: $.sum('$amount'),
        consumptionCount: $.sum(1)
      })
      .end();
      
    // 合并数据
    if (rechargeDailyStats.list) {
      rechargeDailyStats.list.forEach(item => {
        const day = dailyStats.find(d => d.date === item._id);
        if (day) {
          day.rechargeAmount = item.rechargeAmount;
          day.rechargeCount = item.rechargeCount;
        }
      });
    }
    
    if (consumptionDailyStats.list) {
      consumptionDailyStats.list.forEach(item => {
        const day = dailyStats.find(d => d.date === item._id);
        if (day) {
          day.consumptionAmount = item.consumptionAmount;
          day.consumptionCount = item.consumptionCount;
        }
      });
    }
    
    return dailyStats;
  } catch (err) {
    console.error('获取每日统计数据失败:', err);
    return [];
  }
}

// 获取员工佣金统计
async function getStaffCommissions(data) {
  if (!data.staffOpenid) {
    return {
      code: 400,
      message: '缺少员工ID'
    };
  }
  
  try {
    const staffOpenid = data.staffOpenid;
    
    // 构建查询条件
    const query = {
      promoterOpenid: staffOpenid
    };
    
    // 根据状态筛选
    if (data.status) {
      query.status = data.status;
    }
    
    // 根据日期筛选
    if (data.startDate && data.endDate) {
      query.createTime = _.and(_.gte(new Date(data.startDate)), _.lte(new Date(data.endDate)));
    }
    
    // 统计佣金总额
    const totalResult = await db.collection('commission_settlements')
      .where(query)
      .count();
      
    const totalAmount = await db.collection('commission_settlements')
      .aggregate()
      .match(query)
      .group({
        _id: null,
        total: $.sum('$amount')
      })
      .end();
      
    // 分页获取佣金详情
    const pageSize = data.pageSize || 20;
    const page = data.page || 1;
    
    const commissions = await db.collection('commission_settlements')
      .where(query)
      .orderBy('createTime', 'desc')
      .skip((page - 1) * pageSize)
      .limit(pageSize)
      .get();
      
    return {
      code: 0,
      data: {
        list: commissions.data,
        total: totalResult.total,
        totalAmount: totalAmount.list.length > 0 ? totalAmount.list[0].total : 0,
        page,
        pageSize
      },
      message: '获取佣金记录成功'
    };
  } catch (err) {
    console.error('获取员工佣金记录失败:', err);
    return {
      code: -1,
      message: '获取佣金记录失败: ' + err.message
    };
  }
}

// 结算佣金
async function settleCommission(data) {
  if (!data.ids || !Array.isArray(data.ids) || data.ids.length === 0) {
    return {
      code: 400,
      message: '请选择要结算的佣金记录'
    };
  }
  
  try {
    const settleTime = getCurrentTime();
    const ids = data.ids;
    
    // 查询佣金记录
    const commissions = await db.collection('commission_settlements')
      .where({
        _id: _.in(ids),
        status: 'pending'
      })
      .get();
      
    if (commissions.data.length === 0) {
      return {
        code: 400,
        message: '没有可结算的佣金记录'
      };
    }
    
    // 批量更新状态
    const updatePromises = commissions.data.map(comm => {
      return db.collection('commission_settlements').doc(comm._id).update({
        data: {
          status: 'settled',
          settleTime: settleTime,
          updateTime: settleTime,
          operatorId: data.operatorId || null,
          remark: data.remark || '系统结算'
        }
      });
    });
    
    await Promise.all(updatePromises);
    
    // 计算总金额
    const totalAmount = commissions.data.reduce((sum, curr) => sum + curr.amount, 0);
    
    return {
      code: 0,
      data: {
        count: commissions.data.length,
        totalAmount: totalAmount
      },
      message: `成功结算${commissions.data.length}条佣金记录`
    };
  } catch (err) {
    console.error('结算佣金失败:', err);
    return {
      code: -1,
      message: '结算佣金失败: ' + err.message
    };
  }
}

// 获取员工充值记录
async function getOperatorRechargeRecords(data) {
  if (!data.operatorId) {
    return {
      code: 400,
      message: '缺少操作员ID'
    };
  }
  
  try {
    const { operatorId, page = 1, pageSize = 20, startDate, endDate } = data;
    const skip = (page - 1) * pageSize;
    
    // 构建查询条件
    const whereCondition = {
      operatorId: data.operatorId,
      status: 'verified' // 只获取已核销的记录
    };
    
    // 如果提供了日期范围，添加到查询条件
    if (startDate && endDate) {
      const startDateTime = new Date(startDate);
      startDateTime.setHours(0, 0, 0, 0);
      
      const endDateTime = new Date(endDate);
      endDateTime.setHours(23, 59, 59, 999);
      
      whereCondition.verifyTime = _.gte(startDateTime).and(_.lte(endDateTime));
    }
    
    // 查询记录总数
    const countResult = await db.collection('recharge_records')
      .where(whereCondition)
      .count();
    
    const total = countResult.total;
    
    // 查询记录列表
    const queryResult = await db.collection('recharge_records')
      .where(whereCondition)
      .orderBy('verifyTime', 'desc')
      .skip(skip)
      .limit(pageSize)
      .get();
    
    // 获取记录的用户信息
    const records = await Promise.all(queryResult.data.map(async record => {
      if (record.openid) {
        try {
          // 查询用户信息
          const userResult = await db.collection('users').where({
            _openid: record.openid
          }).get();
          
          if (userResult.data.length > 0) {
            const user = userResult.data[0];
            record.userName = user.nickName || '未知用户';
            record.userAvatar = user.avatarUrl;
          }
        } catch (err) {
          console.error('获取用户信息失败:', err);
        }
      }
      return record;
    }));
    
    return {
      code: 0,
      data: {
        records: records,
        total: total,
        page: page,
        pageSize: pageSize
      },
      message: '获取充值核销记录成功'
    };
  } catch (err) {
    console.error('获取充值核销记录失败:', err);
    return {
      code: -1,
      message: '获取充值核销记录失败: ' + err.message
    };
  }
}

// 获取用户余额统计信息
async function getUserBalanceStats(data) {
  try {
    const { page = 1, pageSize = 20, keyword = '' } = data || {};
    const _ = db.command;
    
    // 构建查询条件
    let query = {};
    
    // 如果有搜索关键词，尝试匹配用户ID或昵称
    if (keyword) {
      query = _.or([
        {
          openid: db.RegExp({
            regexp: keyword,
            options: 'i',
          })
        },
        {
          nickName: db.RegExp({
            regexp: keyword,
            options: 'i',
          })
        }
      ]);
    }
    
    // 只查询有余额的用户
    query = _.and([
      query,
      _.or([
        { balance: _.gt(0) },
        { bonusBalance: _.gt(0) }
      ])
    ]);
    
    // 查询用户总数
    const countResult = await db.collection('users').where(query).count();
    const total = countResult.total;
    
    // 查询用户列表
    const usersResult = await db.collection('users')
      .where(query)
      .skip((page - 1) * pageSize)
      .limit(pageSize)
      .get();
    
    // 处理用户数据
    const usersList = usersResult.data.map(user => {
      const balance = user.balance || 0;
      const bonusBalance = user.bonusBalance || 0;
      const totalBalance = balance + bonusBalance;
      
      return {
        openid: user.openid,
        nickName: user.nickName || '未知用户',
        avatarUrl: user.avatarUrl || '',
        balance,
        bonusBalance,
        totalBalance,
        // 添加最近消费记录的时间
        lastUpdateTime: user.updateTime || null
      };
    });
    
    // 按总余额降序排序
    usersList.sort((a, b) => b.totalBalance - a.totalBalance);
    
    // 计算总余额统计
    let totalStats = {
      totalUsers: total,
      totalBalance: 0,
      totalBonusBalance: 0,
      totalAmount: 0
    };
    
    // 如果需要统计所有用户的总余额
    if (data && data.needTotalStats) {
      const allUsersResult = await db.collection('users')
        .where(_.or([
          { balance: _.gt(0) },
          { bonusBalance: _.gt(0) }
        ]))
        .get();
      
      allUsersResult.data.forEach(user => {
        totalStats.totalBalance += user.balance || 0;
        totalStats.totalBonusBalance += user.bonusBalance || 0;
      });
      
      totalStats.totalAmount = totalStats.totalBalance + totalStats.totalBonusBalance;
    }
    
    return {
      code: 0,
      data: {
        list: usersList,
        total,
        page,
        pageSize,
        stats: totalStats
      },
      message: '获取用户余额统计成功'
    };
  } catch (err) {
    console.error('获取用户余额统计失败:', err);
    return {
      code: -1,
      message: '获取用户余额统计失败: ' + err.message
    };
  }
}

// 退款用户余额
async function refundUserBalance(data) {
  if (!data.openid || !data.amount) {
    return {
      code: 400,
      message: '缺少必要参数'
    };
  }
  
  try {
    const openid = data.openid;
    const amount = Number(data.amount);
    const clearBalance = data.clearBalance || false;
    const operatorId = data.operatorId || '管理员';
    const remark = data.remark || '用户余额退款';
    
    // 查询用户信息
    const userResult = await db.collection('users').where({
      openid: openid
    }).get();
    
    if (userResult.data.length === 0) {
      return {
        code: 404,
        message: '用户不存在'
      };
    }
    
    const user = userResult.data[0];
    
    // 获取用户当前余额
    const currentBalance = user.balance || 0;
    const currentBonus = user.bonusBalance || 0;
    const totalAvailable = currentBalance + currentBonus;
    
    // 检查退款金额是否合理
    if (amount > totalAvailable) {
      return {
        code: 400,
        message: '退款金额不能超过用户总余额'
      };
    }
    
    // 计算退款金额分配（优先从现金余额中扣除）
    let refundBalance = Math.min(currentBalance, amount);
    let refundBonus = amount - refundBalance;
    
    // 如果选择清零余额，则退还全部
    let newBalance = currentBalance - refundBalance;
    let newBonus = currentBonus - refundBonus;
    
    // 保存用户输入的原始退款金额，无论是否选择清零余额
    const originalRefundAmount = amount;
    
    console.log('退款前状态:', {
      originalAmount: originalRefundAmount,
      currentBalance,
      currentBonus,
      totalAvailable,
      refundBalance,
      refundBonus,
      clearBalance
    });
    
    if (clearBalance) {
      console.log('执行余额清零操作');
      refundBalance = currentBalance;
      refundBonus = currentBonus;
      newBalance = 0;
      newBonus = 0;
    }
    
    console.log('退款后状态:', {
      originalAmount: originalRefundAmount,
      refundBalance,
      refundBonus,
      newBalance,
      newBonus,
      totalRefund: refundBalance + refundBonus
    });
    
    // 更新用户余额
    await db.collection('users').doc(user._id).update({
      data: {
        balance: newBalance,
        bonusBalance: newBonus,
        updateTime: getCurrentTime()
      }
    });
    
    // 记录退款记录 - 作为财务支出
    const refundRecord = {
      type: 'refund',
      openid: openid,
      amount: originalRefundAmount, // 使用管理员填写的协商退款金额作为财务支出
      actualDeductAmount: refundBalance + refundBonus, // 实际从用户余额中扣除的金额
      refundBalance: refundBalance, // 退款中的现金部分
      refundBonus: refundBonus, // 退款中的赠送金额部分
      operatorId: operatorId,
      remark: remark + ` (协商退款: ${originalRefundAmount}元, 实际扣除: ${refundBalance + refundBonus}元)`,
      createTime: getCurrentTime(),
      // 添加以下字段，用于正确计算退款支出
      originalAmount: originalRefundAmount, // 管理员输入的原始退款金额，始终使用管理员输入的金额
      inputAmount: originalRefundAmount, // 备用字段，管理员输入的金额
      clearBalance: clearBalance, // 是否清零操作
      // 添加用户总余额信息，便于调试
      userTotalBalance: totalAvailable,
      userCashBalance: currentBalance,
      userBonusBalance: currentBonus
    };
    
    console.log('创建退款记录:', {
      originalAmount: originalRefundAmount,
      amount: refundBalance + refundBonus,
      refundBalance,
      refundBonus,
      clearBalance,
      remark: refundRecord.remark
    });
    
    // 添加到支出记录表
    await db.collection('expenses').add({
      data: {
        ...refundRecord,
        category: 'refund', // 支出类别：退款
      }
    });
    
    // 添加到余额变动表（记录用户余额变动）
    await db.collection('balance_consumption').add({
      data: {
        ...refundRecord,
        deductBalance: -refundBalance, // 负数表示增加
        deductBonus: -refundBonus,
      }
    });
    
    return {
      code: 0,
      data: {
        refundBalance,
        refundBonus,
        totalRefund: refundBalance + refundBonus,
        newBalance,
        newBonus
      },
      message: '退款成功'
    };
  } catch (err) {
    console.error('退款失败:', err);
    return {
      code: -1,
      message: '退款失败: ' + err.message
    };
  }
} 