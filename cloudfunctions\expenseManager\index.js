// 支出管理云函数
const cloud = require('wx-server-sdk');

// 初始化云环境
try {
  cloud.init({
    env: cloud.DYNAMIC_CURRENT_ENV
  });
  console.log('云环境初始化成功');
} catch (error) {
  console.error('云环境初始化失败:', error);
}

const db = cloud.database();
const expensesCollection = db.collection('expenses');
const _ = db.command;

// 获取北京时间的辅助函数（与其他云函数保持一致）
function getBJTime() {
  // 获取当前UTC时间
  const now = new Date();
  // 计算北京时间（UTC+8）
  const bjTime = new Date(now.getTime() + 8 * 60 * 60 * 1000);
  return bjTime;
}

// 格式化日期时间的辅助函数（与充值核销保持一致）
function formatDateTime(date) {
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
}

exports.main = async (event, context) => {
  const { type, data } = event;
  const wxContext = cloud.getWXContext();
  const openid = wxContext.OPENID;
  
  console.log('收到请求:', { type, data });
  
  try {
    switch (type) {
      case 'addExpense':
        return await addExpense(data, openid);
      case 'getExpenses':
        return await getExpenses(data, openid);
      case 'getExpenseStats':
        return await getExpenseStats(data);
      case 'deleteExpense':
        return await deleteExpense(data, openid);
      default:
        return { success: false, message: '未知操作类型' };
    }
  } catch (error) {
    console.error('操作执行失败:', error);
    return { 
      success: false, 
      message: '操作执行失败: ' + error.message,
      error: error.toString(),
      stack: error.stack
    };
  }
};

// 添加支出记录
async function addExpense(data, openid) {
  const { staffId, staffName, amount, images, remark } = data;
  
  console.log('添加支出记录:', { staffId, staffName, amount });
  
  try {
    // 检查集合是否存在
    try {
      await db.createCollection('expenses');
      console.log('创建expenses集合成功');
    } catch (e) {
      // 如果集合已存在，会抛出异常，这是正常的
      console.log('expenses集合已存在或创建失败:', e.message);
    }
    
    const result = await expensesCollection.add({
      data: {
        staffId,
        staffName,
        amount: parseFloat(amount),
        images: images || [],
        remark: remark || '',
        createTime: formatDateTime(getBJTime())
      }
    });
    
    console.log('添加支出记录成功:', result);
    return { success: true, expenseId: result._id };
  } catch (error) {
    console.error('添加支出记录失败:', error);
    return { 
      success: false, 
      message: '添加支出记录失败: ' + error.message,
      error: error.toString(),
      stack: error.stack
    };
  }
}

// 获取支出记录列表
async function getExpenses(data, openid) {
  const { staffId, startDate, endDate, page = 1, pageSize = 20 } = data;
  const skip = (page - 1) * pageSize;
  
  let query = {};
  
  // 如果指定了员工ID，只查询该员工的记录
  if (staffId) {
    query.staffId = staffId;
  }
  
  // 如果指定了日期范围，按日期筛选
  if (startDate && endDate) {
    try {
      // 处理开始日期 - 确保是当天的开始
      const startDateTime = new Date(startDate);
      startDateTime.setHours(0, 0, 0, 0);
      
      // 处理结束日期 - 确保是当天的结束
      const endDateTime = new Date(endDate);
      endDateTime.setHours(23, 59, 59, 999);
      
      console.log('日期范围查询(优化后):', {
        startDateTime: startDateTime.toISOString(),
        endDateTime: endDateTime.toISOString(),
        startDate,
        endDate
      });
      
      query.createTime = _.gte(startDateTime).and(_.lte(endDateTime));
    } catch (err) {
      console.error('日期处理错误:', err);
      // 如果日期解析失败，使用更简单的字符串比较方法
      console.log('使用备用日期处理方法');
      
      // 尝试仅使用日期部分进行比较
      const getDatePart = (dateStr) => {
        if (typeof dateStr === 'string') {
          return dateStr.split(' ')[0]; // 提取YYYY-MM-DD部分
        }
        return dateStr;
      };
      
      const startDatePart = getDatePart(startDate);
      const endDatePart = getDatePart(endDate);
      
      // 创建日期对象用于比较
      const startDateObj = new Date(startDatePart);
      startDateObj.setHours(0, 0, 0, 0);
      
      const endDateObj = new Date(endDatePart);
      endDateObj.setHours(23, 59, 59, 999);
      
      query.createTime = _.gte(startDateObj).and(_.lte(endDateObj));
    }
  }
  
  try {
    // 如果有日期筛选，需要特殊处理时间格式问题
    let expenseList = [];
    let expenseCount = 0;

    if (startDate && endDate) {
      // 先获取所有记录（不包含时间筛选）
      let baseQuery = {};
      if (staffId) {
        baseQuery.staffId = staffId;
      }

      const allExpenseResult = await expensesCollection.where(baseQuery).get();

      console.log('获取到的所有支出记录数量:', allExpenseResult.data.length);

      // 在内存中过滤日期范围
      const startDateTime = new Date(startDate);
      startDateTime.setHours(0, 0, 0, 0);

      const endDateTime = new Date(endDate);
      endDateTime.setHours(23, 59, 59, 999);

      const filteredExpenses = allExpenseResult.data.filter(expense => {
        if (!expense.createTime) {
          return false;
        }

        let expenseDate;

        // 处理不同的时间格式
        if (typeof expense.createTime === 'string') {
          // 字符串格式，直接解析
          expenseDate = new Date(expense.createTime);
        } else if (expense.createTime instanceof Date) {
          // Date对象格式，转换为北京时间
          expenseDate = new Date(expense.createTime.getTime() + 8 * 60 * 60 * 1000);
        } else if (expense.createTime && expense.createTime._seconds) {
          // Firestore时间戳格式，转换为北京时间
          expenseDate = new Date((expense.createTime._seconds * 1000) + 8 * 60 * 60 * 1000);
        } else {
          console.log('无法解析支出时间格式:', expense.createTime);
          return false;
        }

        // 检查日期是否在范围内
        const isInRange = expenseDate >= startDateTime && expenseDate <= endDateTime;

        console.log('支出记录时间比较(expenseManager):', {
          expenseId: expense._id,
          createTime: expense.createTime,
          expenseDate: expenseDate.toISOString(),
          isInRange: isInRange
        });

        return isInRange;
      });

      console.log('过滤后的支出记录数量:', filteredExpenses.length);

      // 按时间倒序排序
      filteredExpenses.sort((a, b) => {
        let dateA, dateB;

        if (typeof a.createTime === 'string') {
          dateA = new Date(a.createTime);
        } else if (a.createTime instanceof Date) {
          dateA = a.createTime;
        } else if (a.createTime && a.createTime._seconds) {
          dateA = new Date(a.createTime._seconds * 1000);
        }

        if (typeof b.createTime === 'string') {
          dateB = new Date(b.createTime);
        } else if (b.createTime instanceof Date) {
          dateB = b.createTime;
        } else if (b.createTime && b.createTime._seconds) {
          dateB = new Date(b.createTime._seconds * 1000);
        }

        return dateB - dateA; // 倒序
      });

      expenseCount = filteredExpenses.length;
      expenseList = filteredExpenses.slice(skip, skip + pageSize);

    } else {
      // 没有日期筛选，使用原有逻辑
      const expenseCountResult = await expensesCollection.where(query).count();
      expenseCount = expenseCountResult.total;

      const expenseListResult = await expensesCollection
        .where(query)
        .orderBy('createTime', 'desc')
        .skip(skip)
        .limit(pageSize)
        .get();

      expenseList = expenseListResult.data;
    }
    
    // 如果指定了员工ID为'system'或没有指定员工ID，则获取积分提现记录
    if (!staffId || staffId === 'system') {
      // 获取积分提现记录 - 已通过的提现
      let withdrawalQuery = {
        status: 'completed' // 只获取已通过的提现申请
      };
      
      // 如果指定了日期范围，按处理时间筛选
      if (startDate && endDate) {
        try {
          // 处理开始日期 - 确保是当天的开始
          const startDateTime = new Date(startDate);
          startDateTime.setHours(0, 0, 0, 0);
          
          // 处理结束日期 - 确保是当天的结束
          const endDateTime = new Date(endDate);
          endDateTime.setHours(23, 59, 59, 999);
          
          withdrawalQuery.processTime = _.gte(startDateTime).and(_.lte(endDateTime));
        } catch (err) {
          console.error('提现日期处理错误:', err);
        }
      }
      
      // 获取积分设置，用于计算兑换金额
      const settingsCollection = db.collection('system_settings');
      const settingsResult = await settingsCollection.where({ type: 'points' }).get();
      const pointsSettings = settingsResult.data[0] || {};
      const exchangeRatio = (pointsSettings.settings && pointsSettings.settings.exchangeRatio) || 1;
      
      // 查询withdrawals集合
      const withdrawalsCollection = db.collection('withdrawals');
      const withdrawalCountResult = await withdrawalsCollection.where(withdrawalQuery).count();
      const withdrawalCount = withdrawalCountResult.total;
      
      // 计算总记录数和分页
      const totalCount = expenseCount + withdrawalCount;
      
      // 如果当前页码超过了常规支出记录的范围，则获取积分提现记录
      if (skip >= expenseCount) {
        // 计算积分提现记录的偏移量和数量
        const withdrawalSkip = skip - expenseCount;
        
        const withdrawalListResult = await withdrawalsCollection
          .where(withdrawalQuery)
          .orderBy('processTime', 'desc')
          .skip(withdrawalSkip)
          .limit(pageSize)
          .get();
        
        const withdrawalList = withdrawalListResult.data;
        
        // 转换积分提现记录为支出记录格式
        const withdrawalExpenseList = withdrawalList.map(item => {
          // 计算提现金额
          const points = parseFloat(item.points || 0);
          const amount = points / exchangeRatio;
          
          // 创建支出记录对象
          return {
            _id: item._id,
            staffId: 'system',
            staffName: '系统(积分提现)',
            amount: amount,
            formattedAmount: amount.toFixed(2),
            remark: `用户积分提现 - ${item.points}积分 - ${item.userInfo ? item.userInfo.nickName : '用户'}`,
            createTime: item.processTime || item.createTime,
            type: 'withdrawal',
            withdrawalId: item._id
          };
        });

        // 按创建时间排序
        withdrawalExpenseList.sort((a, b) => {
          return new Date(b.createTime) - new Date(a.createTime);
        });

        return {
          success: true,
          total: totalCount,
          list: withdrawalExpenseList,
          page,
          pageSize
        };
      } else {
        // 如果当前页码在常规支出记录范围内，但需要补充积分提现记录
        const remainingCount = pageSize - expenseList.length;
        
        if (remainingCount > 0 && withdrawalCount > 0) {
          const withdrawalListResult = await withdrawalsCollection
            .where(withdrawalQuery)
            .orderBy('processTime', 'desc')
            .limit(remainingCount)
            .get();
          
          const withdrawalList = withdrawalListResult.data;
          
          // 转换积分提现记录为支出记录格式
          const withdrawalExpenseList = withdrawalList.map(item => {
            // 计算提现金额
            const points = parseFloat(item.points || 0);
            const amount = points / exchangeRatio;
            
            // 创建支出记录对象
            return {
              _id: item._id,
              staffId: 'system',
              staffName: '系统(积分提现)',
              amount: amount,
              formattedAmount: amount.toFixed(2),
              remark: `用户积分提现 - ${item.points}积分 - ${item.userInfo ? item.userInfo.nickName : '用户'}`,
              createTime: item.processTime || item.createTime,
              type: 'withdrawal',
              withdrawalId: item._id
            };
          });
          
          // 合并两种记录
          const combinedList = [...expenseList, ...withdrawalExpenseList];
          
          // 按创建时间排序
          combinedList.sort((a, b) => {
            return new Date(b.createTime) - new Date(a.createTime);
          });
          
          return {
            success: true,
            total: totalCount,
            list: combinedList,
            page,
            pageSize
          };
        }
      }
    }
    
    // 格式化金额
    expenseList.forEach(item => {
      if (item.amount) {
        item.formattedAmount = parseFloat(item.amount).toFixed(2);
      }
    });
    
    return {
      success: true,
      total: expenseCount,
      list: expenseList,
      page,
      pageSize
    };
  } catch (error) {
    console.error('获取支出记录失败:', error);
    return { 
      success: false, 
      message: '获取支出记录失败: ' + error.message,
      error: error.toString()
    };
  }
}

// 获取支出统计数据
async function getExpenseStats(data) {
  const { startDate, endDate } = data;
  
  let query = {};
  
  // 如果指定了日期范围，按日期筛选
  if (startDate && endDate) {
    try {
      // 处理开始日期 - 确保是当天的开始
      const startDateTime = new Date(startDate);
      startDateTime.setHours(0, 0, 0, 0);
      
      // 处理结束日期 - 确保是当天的结束
      const endDateTime = new Date(endDate);
      endDateTime.setHours(23, 59, 59, 999);
      
      console.log('统计日期范围查询(优化后):', {
        startDateTime: startDateTime.toISOString(),
        endDateTime: endDateTime.toISOString(),
        startDate,
        endDate
      });
      
      query.createTime = _.gte(startDateTime).and(_.lte(endDateTime));
    } catch (err) {
      console.error('日期处理错误:', err);
      // 如果日期解析失败，使用更简单的字符串比较方法
      console.log('使用备用日期处理方法');
      
      // 尝试仅使用日期部分进行比较
      const getDatePart = (dateStr) => {
        if (typeof dateStr === 'string') {
          return dateStr.split(' ')[0]; // 提取YYYY-MM-DD部分
        }
        return dateStr;
      };
      
      const startDatePart = getDatePart(startDate);
      const endDatePart = getDatePart(endDate);
      
      // 创建日期对象用于比较
      const startDateObj = new Date(startDatePart);
      startDateObj.setHours(0, 0, 0, 0);
      
      const endDateObj = new Date(endDatePart);
      endDateObj.setHours(23, 59, 59, 999);
      
      query.createTime = _.gte(startDateObj).and(_.lte(endDateObj));
    }
  }
  
  try {
    // 获取常规支出记录 - 使用与getExpenseList相同的逻辑
    let expenseList = [];

    if (startDate && endDate) {
      // 先获取所有记录（不包含时间筛选）
      const allExpenseResult = await expensesCollection.get();

      console.log('获取到的所有支出记录数量(统计):', allExpenseResult.data.length);

      // 在内存中过滤日期范围
      const startDateTime = new Date(startDate);
      startDateTime.setHours(0, 0, 0, 0);

      const endDateTime = new Date(endDate);
      endDateTime.setHours(23, 59, 59, 999);

      const filteredExpenses = allExpenseResult.data.filter(expense => {
        if (!expense.createTime) {
          return false;
        }

        let expenseDate;

        // 处理不同的时间格式
        if (typeof expense.createTime === 'string') {
          // 字符串格式，直接解析
          expenseDate = new Date(expense.createTime);
        } else if (expense.createTime instanceof Date) {
          // Date对象格式，转换为北京时间
          expenseDate = new Date(expense.createTime.getTime() + 8 * 60 * 60 * 1000);
        } else if (expense.createTime && expense.createTime._seconds) {
          // Firestore时间戳格式，转换为北京时间
          expenseDate = new Date((expense.createTime._seconds * 1000) + 8 * 60 * 60 * 1000);
        } else {
          console.log('无法解析支出时间格式(统计):', expense.createTime);
          return false;
        }

        // 检查日期是否在范围内
        const isInRange = expenseDate >= startDateTime && expenseDate <= endDateTime;

        console.log('支出记录时间比较(统计):', {
          expenseId: expense._id,
          createTime: expense.createTime,
          expenseDate: expenseDate.toISOString(),
          isInRange: isInRange
        });

        return isInRange;
      });

      console.log('过滤后的支出记录数量(统计):', filteredExpenses.length);
      expenseList = filteredExpenses;

    } else {
      // 没有日期筛选，使用原有逻辑
      const expenseResult = await expensesCollection.where(query).get();
      expenseList = expenseResult.data || [];
    }

    // 计算总支出金额
    let totalExpense = 0;
    expenseList.forEach(item => {
      totalExpense += parseFloat(item.amount || 0);
    });
    
    // 按员工分组统计
    const staffStats = {};
    expenseList.forEach(item => {
      const staffId = item.staffId || 'unknown';
      const staffName = item.staffName || '未知员工';
      const amount = parseFloat(item.amount || 0);
      
      if (!staffStats[staffId]) {
        staffStats[staffId] = {
          staffId,
          staffName,
          count: 0,
          totalAmount: 0
        };
      }
      
      staffStats[staffId].count += 1;
      staffStats[staffId].totalAmount += amount;
    });
    
    // 获取积分提现记录 - 已通过的提现
    let withdrawalQuery = {
      status: 'completed' // 只获取已通过的提现申请
    };
    
    // 如果指定了日期范围，按处理时间筛选
    if (startDate && endDate) {
      try {
        // 处理开始日期 - 确保是当天的开始
        const startDateTime = new Date(startDate);
        startDateTime.setHours(0, 0, 0, 0);
        
        // 处理结束日期 - 确保是当天的结束
        const endDateTime = new Date(endDate);
        endDateTime.setHours(23, 59, 59, 999);
        
        withdrawalQuery.processTime = _.gte(startDateTime).and(_.lte(endDateTime));
      } catch (err) {
        console.error('提现日期处理错误:', err);
      }
    }
    
    // 查询withdrawals集合
    const withdrawalsCollection = db.collection('withdrawals');
    const withdrawalResult = await withdrawalsCollection.where(withdrawalQuery).get();
    const withdrawalList = withdrawalResult.data || [];
    
    // 获取积分设置，用于计算兑换金额
    const settingsCollection = db.collection('system_settings');
    const settingsResult = await settingsCollection.where({ type: 'points' }).get();
    const pointsSettings = settingsResult.data[0] || {};
    const exchangeRatio = (pointsSettings.settings && pointsSettings.settings.exchangeRatio) || 1;
    
    // 计算积分提现的总金额
    let totalWithdrawalAmount = 0;
    const withdrawalExpenseList = [];
    
    withdrawalList.forEach(item => {
      // 计算提现金额
      const points = parseFloat(item.points || 0);
      const amount = points / exchangeRatio;
      totalWithdrawalAmount += amount;
      
      // 创建支出记录对象
      withdrawalExpenseList.push({
        _id: item._id,
        staffId: 'system',
        staffName: '系统(积分提现)',
        amount: amount,
        remark: `用户积分提现 - ${item.points}积分 - ${item.userInfo ? item.userInfo.nickName : '用户'}`,
        createTime: item.processTime || item.createTime,
        type: 'withdrawal',
        withdrawalId: item._id
      });
    });
    
    // 将积分提现记录添加到支出列表
    const allExpenseList = [...expenseList, ...withdrawalExpenseList];
    
    // 更新总支出金额，包括积分提现
    totalExpense += totalWithdrawalAmount;
    
    // 添加系统(积分提现)到员工统计
    if (totalWithdrawalAmount > 0) {
      staffStats['system'] = {
        staffId: 'system',
        staffName: '系统(积分提现)',
        count: withdrawalList.length,
        totalAmount: totalWithdrawalAmount.toFixed(2)
      };
    }
    
    // 将员工统计转换为数组
    const staffStatsArray = Object.values(staffStats);
    
    // 按总金额排序
    staffStatsArray.sort((a, b) => b.totalAmount - a.totalAmount);
    
    // 格式化金额，保留两位小数
    staffStatsArray.forEach(item => {
      item.totalAmount = parseFloat(item.totalAmount).toFixed(2);
    });
    
    return {
      success: true,
      totalExpense: totalExpense.toFixed(2),
      expenseList: allExpenseList,
      staffStats: staffStatsArray,
      withdrawalStats: {
        count: withdrawalList.length,
        totalAmount: totalWithdrawalAmount.toFixed(2)
      }
    };
  } catch (error) {
    console.error('获取支出统计数据失败:', error);
    return { 
      success: false, 
      message: '获取支出统计数据失败: ' + error.message,
      error: error.toString()
    };
  }
}

// 删除支出记录
async function deleteExpense(data, openid) {
  const { expenseId } = data;
  
  try {
    await expensesCollection.doc(expenseId).remove();
    return { success: true };
  } catch (error) {
    console.error('删除支出记录失败:', error);
    return { 
      success: false, 
      message: '删除支出记录失败: ' + error.message,
      error: error.toString()
    };
  }
} 