/* global Component, wx, console */

// 添加日志控制函数
function log(message, data) {
  // 设置为false可以禁用所有日志，生产环境建议设置为false
  const enableLogging = true;
  if (enableLogging) {
    if (data !== undefined) {
      console.log(message, typeof data === 'string' ? data : (JSON.stringify(data).length > 200 ? JSON.stringify(data).substring(0, 200) + '...' : data));
    } else {
      console.log(message);
    }
  }
}

// 小程序组件定义
Component({
  properties: {
    visible: {
      type: Boolean,
      value: false,
      observer(newVal, oldVal) {
        // 只有当值真正变化时才触发
        if (newVal !== oldVal) {
          if (newVal) {
            this._showModal();
          } else {
            this._hideModal();
          }
        }
      }
    },
    videoInfo: {
      type: Object,
      value: {},
      observer(newVal) {
        if (newVal && Object.keys(newVal).length > 0) {
          this._updateVideoInfo(newVal);
        }
      }
    }
  },

  data: {
    mainTitle: '',
    subTitle: '',
    videoUrl: '',
    coverUrl: '',
    authorAvatar: '',
    author: '',
    playCount: '0',
    description: '',
    showNavbar: true,
    showControls: false,
    modalHeightStyle: '', // 用于存储动态高度样式
    loadingContent: false, // 是否正在加载详情内容
    detailImages: [], // 用于存储详情图片的URL
    scrollTop: 0,  // 滚动位置
    scrollPositions: {}, // 存储不同视频ID的滚动位置
    isClosing: false,
    touchStartY: null, // 触摸开始位置，用于下拉关闭功能
    isPlaying: true, // 初始化时设为 true，因为我们要自动播放
    videoContext: null, // 用于存储视频上下文
    moveTransform: '', // 初始化transform样式
    currentPlayTime: 0, // 当前视频播放时间点
    videoPlaybackPositions: {}, // 存储不同视频ID的播放位置
    _pendingVideoUrl: '', // 临时存储视频URL，用于延迟加载
    _initialSeekDone: false, // 标记是否已完成初始seek操作
    isFullscreenMode: false, // 添加全屏模式标记
    isPortraitVideo: false, // 添加视频方向标记
    controlsTimer: null, // 控制器定时器
    _currentVideoInfo: null, // 用于存储当前的视频信息
    loadingVideo: false // 用于标记是否正在加载视频
  },

  lifetimes: {
    attached() {
      // 获取胶囊按钮的位置信息
      this._getCapsuleInfo();
      
      // 从本地存储恢复视频播放位置
      try {
        const savedPositions = wx.getStorageSync('videoPlaybackPositions');
        if (savedPositions) {
          this.setData({
            videoPlaybackPositions: savedPositions
          });
        }
        
        // 从本地存储恢复滚动位置
        const savedScrollPositions = wx.getStorageSync('videoScrollPositions');
        if (savedScrollPositions) {
          this.setData({
            scrollPositions: savedScrollPositions
          });
        }
      } catch (e) {
        // 忽略恢复错误
      }
      
      // 确保初始状态为不可见
      this.setData({
        visible: false,
        isClosing: false,
        videoUrl: '', // 确保初始视频URL为空，防止自动加载
        touchStartY: null,
        richContent: ''
      });
      
      // 初始获取计算样式
      this._getComputedStyles();
      
      // 移除可能存在的videoPlayerInsert事件监听器，避免重复添加
      wx.offVideoPlayerInsert && wx.offVideoPlayerInsert();
    },
    detached() {
      // 在组件实例被从页面节点树移除时执行
      console.log('视频详情组件被卸载，清理资源');
      
      // 确保暂停并清理视频
      this._resetVideoPlayer();
      
      // 清理可能的其他资源
      this.setData({
        visible: false,
        isClosing: false,
        videoUrl: '',
        richContent: '',
        isPlaying: false,
        touchStartY: null
      });
      
      // 移除视频事件监听器，防止内存泄漏
      wx.offVideoPlayerInsert && wx.offVideoPlayerInsert();
    }
  },

  methods: {
    // 获取胶囊按钮的位置信息，用于调整弹窗高度
    _getCapsuleInfo() {
      try {
        // 获取窗口信息（使用新的API）
        const windowInfo = wx.getWindowInfo();
        const menuButtonInfo = wx.getMenuButtonBoundingClientRect();
        
        // 计算胶囊按钮底部到屏幕顶部的距离
        const capsuleBottom = menuButtonInfo.bottom;
        
        // 计算合适的弹窗高度，使其顶部与胶囊底部平行
        const screenHeight = windowInfo.windowHeight;
        const modalHeight = screenHeight - capsuleBottom;
        const modalHeightPercentage = (modalHeight / screenHeight) * 100;
        
        // 约束高度，确保至少有60vh的显示空间
        const finalHeight = Math.max(modalHeightPercentage, 60);
        
        // 设置数据，更新弹窗高度
        this.setData({
          modalHeightStyle: `height: ${finalHeight}vh !important;`
        });
      } catch (error) {
        console.error('获取胶囊信息失败:', error);
        // 使用默认高度
        this.setData({
          modalHeightStyle: 'height: 88vh !important;'
        });
      }
    },

    // 显示弹窗 - 替换原有_showModal方法
    _showModal() {
      // 先确保弹窗内容可见，但视频暂时不加载
      this.setData({
        isClosing: false
      });
      
      // 恢复之前保存的滚动位置
      this._restoreScrollPosition();
      
      // 获取CSS变量值
      this._getComputedStyles();
      
      // 延迟设置视频URL，确保DOM已经渲染完成
      setTimeout(() => {
        // 如果有待加载的视频URL
        if (this.data._pendingVideoUrl) {
          log('[详情模态框] 设置视频URL:', this.data._pendingVideoUrl.substring(0, 50) + '...');
          this.setData({
            videoUrl: this.data._pendingVideoUrl
          }, () => {
            // 设置完URL后延迟初始化播放器，给DOM留出时间
            setTimeout(() => {
              this._initVideoWithPosition();
            }, 300);
          });
        }
      }, 300); // 增加延迟时间
    },
    
    // 计算样式
    _getComputedStyles() {
      log('[详情模态框] 样式现在通过CSS变量控制，无需手动计算');
    },
    
    // 恢复滚动位置
    _restoreScrollPosition() {
      const videoId = (this.data.videoInfo || {})._id || (this.data.videoInfo || {}).id || (this.data.videoInfo || {}).videoId;
      if (!videoId) return;
      
      const scrollPositions = this.data.scrollPositions || {};
      const savedPosition = scrollPositions[videoId] || 0;
      
      this.setData({
        scrollTop: savedPosition
      });
    },
    
    // 保存滚动位置
    _saveScrollPosition() {
      const videoId = (this.data.videoInfo || {})._id || (this.data.videoInfo || {}).id || (this.data.videoInfo || {}).videoId;
      if (!videoId) return;
      
      // 获取当前滚动位置
      wx.createSelectorQuery().in(this)
        .select('#modalScroll')
        .scrollOffset()
        .exec(res => {
          if (res && res[0]) {
            const scrollTop = res[0].scrollTop || 0;
            
            // 更新内存中的滚动位置
            const scrollPositions = {...this.data.scrollPositions};
            scrollPositions[videoId] = scrollTop;
            
            this.setData({
              scrollPositions: scrollPositions
            });
            
            // 同步到本地存储，延迟以减少写入频率
            clearTimeout(this._saveScrollTimer);
            this._saveScrollTimer = setTimeout(() => {
              try {
                wx.setStorageSync('videoScrollPositions', scrollPositions);
              } catch (e) {
                // 忽略保存错误
              }
            }, 300);
          }
        });
    },
    
    // 处理滚动事件，只保留必要功能
    onScroll(e) {
      // 保存当前滚动位置
      this._saveScrollPosition();
    },
    
    // 移除触底检测功能
    _checkScrollReachBottom(e) {
      // 该方法已不再需要触底检测功能
    },
    
    // 使用指定位置初始化视频播放器
    _initVideoWithPosition() {
      try {
        log('[详情模态框] 初始化视频播放器');
        
        // 在创建新的视频上下文前，先清理可能存在的事件监听器
        if (this.videoContext) {
          // 尝试移除之前可能添加的监听器
          wx.offVideoPlayerInsert && wx.offVideoPlayerInsert();
        }
        
        const videoContext = wx.createVideoContext('detailVideo', this);
        if (videoContext) {
          this.videoContext = videoContext;
          
          // 初始化时显示控制器
          this.setData({ 
            showControls: true
          });
          
          log('[详情模态框] 播放器已创建，设置初始位置:', this.data.currentPlayTime);
          
          // 设置初始播放位置 - 只有当播放位置大于0时才seek
          if (this.data.currentPlayTime > 0) {
            // 增加延迟确保seek操作在播放之前完成
            setTimeout(() => {
              videoContext.seek(this.data.currentPlayTime);
              log('[详情模态框] 已seek到指定位置:', this.data.currentPlayTime);
            }, 100);
          }
          
          // 自动播放 - 进一步延迟确保UI已完全渲染
          setTimeout(() => {
            if (this.videoContext) {
              log('[详情模态框] 开始播放视频');
              this.videoContext.play();
              
              // 播放状态将由onVideoPlay事件处理
              // 开始播放后立即调用统一方法处理控件显示
              this._showControlsWithTimeout();
            }
          }, 500); // 增加延迟，确保DOM已完全准备好
        }
      } catch (error) {
        console.error('[详情模态框] 播放器初始化失败', error);
      }
    },
    
    // 监听视频seek完成
    _listenToVideoSeekComplete() {
      // 小程序不直接支持seekComplete事件，因此我们模拟这个行为
      // 通过onTimeUpdate事件来判断seek是否已完成
      this.setData({
        _initialSeekDone: false
      });
    },
    
    // 接收视频信息并显示弹窗
    showModal(videoInfo) {
      if (!videoInfo) return;
      
      // 再次获取胶囊按钮信息（确保正确高度）
      this._getCapsuleInfo();
      
      // 尝试停止所有正在播放的视频
      this._stopAllOtherVideos();
      
      // 保存当前视频信息，但不直接设置videoInfo属性
      // 这样可以避免触发observer导致重复调用_updateVideoInfo
      this._currentVideoInfo = videoInfo;
      
      // 手动调用一次更新方法
      this._updateVideoInfo(videoInfo);
      
      // 设置为可见（放在最后防止多次触发observer）
      this.setData({
        isClosing: false,
        visible: true
      });
      
      // 通知页面隐藏导航栏
      this.triggerEvent('navbarControl', { action: 'hide' });
    },
    
    // 停止所有其他视频播放
    _stopAllOtherVideos() {
      try {
        // 尝试获取全局app实例
        const app = getApp();
        
        // 方法1: 使用全局视频卡片实例列表
        if (app && app.videoCardInstances) {
          app.videoCardInstances.forEach(instance => {
            if (instance && typeof instance.pause === 'function') {
              instance.pause();
            }
          });
        }
        
        // 方法2: 尝试通过页面查找视频播放器模块
        const currentPages = getCurrentPages();
        if (currentPages && currentPages.length > 0) {
          const currentPage = currentPages[currentPages.length - 1];
          
          // 尝试访问videoPlayerModule
          if (currentPage.videoPlayerModule && 
              typeof currentPage.videoPlayerModule.pauseAllVideos === 'function') {
            currentPage.videoPlayerModule.pauseAllVideos();
          }
          
          // 尝试访问mainController
          if (currentPage.mainController && 
              currentPage.mainController.modules &&
              currentPage.mainController.modules.get) {
            const videoPlayer = currentPage.mainController.modules.get('VideoPlayer');
            if (videoPlayer && typeof videoPlayer.pauseAllVideos === 'function') {
              videoPlayer.pauseAllVideos();
            }
          }
        }
        
        console.log('[详情模态框] 已尝试停止所有其他视频');
      } catch (error) {
        console.error('[详情模态框] 停止其他视频失败:', error);
      }
    },
    
    // 更新视频信息
    _updateVideoInfo(videoInfo) {
      if (!videoInfo) return;
      
      log('[详情模态框] 更新视频信息:', videoInfo);
      
      // 保存当前视频信息
      this.setData({
        _currentVideoInfo: videoInfo
      });
      
      // 提取视频信息
      const mainTitle = videoInfo.mainTitle || videoInfo.title || '';
      const subTitle = videoInfo.subTitle || videoInfo.subtitle || '';
      const videoUrl = videoInfo.videoUrl || '';
      const coverUrl = videoInfo.coverUrl || '';
      const playCount = videoInfo.playCount || 0;
      const description = videoInfo.description || '';
      const authorAvatar = videoInfo.authorAvatar || '/static/logo.png';
      
      // 设置视频信息
      this.setData({
        mainTitle,
        subTitle,
        coverUrl,
        playCount: playCount.toString(),
        description,
        authorAvatar,
        detailImages: [], // 清空之前的详情图片
        loadingContent: true // 设置为加载中状态
      });
      
      // 检查是否有视频URL
      if (videoUrl) {
        log('[详情模态框] 视频URL可用:', videoUrl.substring(0, 50) + '...');
        this.setData({
          _pendingVideoUrl: videoUrl
        });
      } else {
        log('[详情模态框] 视频URL不可用，尝试手动获取');
        // 尝试手动获取视频URL
        this._fetchVideoUrlManually(videoInfo.id || videoInfo.baseId);
      }
      
      // 获取详情图片
      const videoId = videoInfo.id || videoInfo.baseId;
      if (videoId) {
        this._fetchDetailImages(videoId);
      } else {
        console.error('[详情模态框] 无法获取详情图片，缺少视频ID');
        this.setData({
          loadingContent: false
        });
      }
    },
    
    // 手动获取视频URL
    _fetchVideoUrlManually(videoId) {
      if (!videoId) {
        console.error('[详情模态框] 无法获取视频URL，缺少视频ID');
        return;
      }
      
      console.log('[详情模态框] 开始手动获取视频URL, ID:', videoId);
      
      // 尝试使用不同格式的ID
      let id = videoId;
      let numericId = videoId;
      
      // 如果ID包含非数字字符，尝试提取纯数字ID
      if (typeof videoId === 'string' && /\D/.test(videoId)) {
        const matches = videoId.match(/\d+/);
        if (matches && matches[0]) {
          numericId = matches[0];
          console.log('[详情模态框] 提取到纯数字ID:', numericId);
        }
      }
      
      // 设置加载状态
      this.setData({
        loadingVideo: true
      });
      
      // 获取视频URL
      wx.cloud.callFunction({
        name: 'videoManager',
        data: {
          type: 'frontend',
          action: 'getVideoUrl',
          data: {
            id: id,
            numericId: numericId
          }
        },
        success: res => {
          console.log('[详情模态框] 获取视频URL结果:', JSON.stringify(res.result).substring(0, 200) + '...');
          
          if (res.result && res.result.code === 200 && res.result.data && res.result.data.videoUrl) {
            console.log('[详情模态框] 成功获取视频URL:', res.result.data.videoUrl.substring(0, 50) + '...');
            
            this.setData({
              videoUrl: res.result.data.videoUrl,
              loadingVideo: false
            });
            
            // 如果有详情图片，也一并更新
            if (res.result.data.detailUrls && res.result.data.detailUrls.length > 0) {
              this.setData({
                detailImages: res.result.data.detailUrls
              });
            } else if (res.result.data.detailUrl) {
              this.setData({
                detailImages: [res.result.data.detailUrl]
              });
            }
            
            // 延迟播放视频
            setTimeout(() => {
              this._playVideo();
            }, 500);
          } else {
            console.error('[详情模态框] 获取视频URL失败:', res.result);
            this.setData({
              loadingVideo: false
            });
          }
        },
        fail: err => {
          console.error('[详情模态框] 获取视频URL失败:', err);
          this.setData({
            loadingVideo: false
          });
        }
      });
      
      // 同时尝试获取详情图片
      this._fetchDetailImages(videoId);
    },
    
    // 获取详情图片
    _fetchDetailImages(videoId) {
      if (!videoId) {
        console.error('[详情模态框] 无法获取详情图片，缺少视频ID');
        return;
      }
      
      log('[详情模态框] 开始获取详情图片, ID:', videoId);
      
      // 设置加载状态
      this.setData({
        loadingContent: true
      });
      
      // 获取详情图片
      wx.cloud.callFunction({
        name: 'videoManager',
        data: {
          type: 'frontend',
          action: 'getVideoDetail', // 使用getVideoDetail接口获取详情
          data: {
            id: videoId
          }
        },
        success: res => {
          log('[详情模态框] 获取详情图片结果:', res.result);
          
          if (res.result && res.result.code === 200 && res.result.data) {
            // 处理详情图片
            let detailImages = [];
            
            // 检查是否有多张详情图片
            if (res.result.data.detailUrls && Array.isArray(res.result.data.detailUrls) && res.result.data.detailUrls.length > 0) {
              detailImages = res.result.data.detailUrls;
              log(`[详情模态框] 使用多张详情图片，数量: ${detailImages.length}`);
            } 
            // 检查是否有单张详情图片
            else if (res.result.data.detailUrl) {
              detailImages = [res.result.data.detailUrl];
              log('[详情模态框] 使用单张详情图片');
            }
            
            // 更新详情图片
            if (detailImages.length > 0) {
              this.setData({
                detailImages: detailImages,
                loadingContent: false
              });
              log('[详情模态框] 详情图片已更新');
            } else {
              log('[详情模态框] 未找到详情图片');
              this.setData({
                loadingContent: false,
                description: '暂无详情内容'
              });
            }
          } else {
            console.error('[详情模态框] 获取详情图片失败:', res.result);
            this.setData({
              loadingContent: false,
              description: '获取详情失败，请重试'
            });
          }
        },
        fail: err => {
          console.error('[详情模态框] 获取详情图片失败:', err);
          this.setData({
            loadingContent: false,
            description: '获取详情失败，请重试'
          });
        }
      });
    },
    
    // 预览详情图片
    previewDetailImage(e) {
      const index = e.currentTarget.dataset.index;
      const detailImages = this.data.detailImages;
      
      if (detailImages && detailImages.length > 0) {
        wx.previewImage({
          current: detailImages[index], // 当前显示图片的链接
          urls: detailImages // 需要预览的图片链接列表
        });
      }
    },
    
    // 隐藏弹窗 - 处理关闭动画
    _hideModal() {
      // 防止已在关闭过程中
      if (this.data._isClosingAnimation) return;
      
      // 标记为正在关闭
      this.data._isClosingAnimation = true;
      
      // 先重置视频播放器状态
      this._resetVideoPlayer();
      
      this.setData({
        visible: false,
        isClosing: false,
        touchStartY: null
      });
      
      // 延迟后重置关闭标志，允许再次打开
      setTimeout(() => {
        this.data._isClosingAnimation = false;
      }, 300);
    },
    
    // 重置视频播放器
    _resetVideoPlayer() {
      try {
        if (this.videoContext) {
          // 暂停视频播放
          this.videoContext.pause();
          this.videoContext.stop();
          this.videoContext = null;
        }
        
        // 清空视频URL和播放状态
        this.setData({
          isPlaying: false,
          videoUrl: ''
        });
        
        // 移除视频事件监听器
        wx.offVideoPlayerInsert && wx.offVideoPlayerInsert();
        
      } catch (error) {
        console.error('重置视频播放器失败', error);
      }
    },
    
    // 刷新内容
    refreshContent() {
      console.log('[详情模态框] 刷新内容');
      
      // 获取当前视频ID
      const videoInfo = this.data._currentVideoInfo;
      if (!videoInfo) {
        console.error('[详情模态框] 无法刷新内容，缺少视频信息');
        wx.showToast({
          title: '无法刷新内容',
          icon: 'none'
        });
        return;
      }
      
      // 获取详情图片
      const videoId = videoInfo.id || videoInfo.baseId;
      if (videoId) {
        this._fetchDetailImages(videoId);
      } else {
        console.error('[详情模态框] 无法获取详情图片，缺少视频ID');
        wx.showToast({
          title: '无法刷新内容',
          icon: 'none'
        });
      }
    },
    
    // 滚动到顶部
    scrollToTop() {
      // 添加短暂振动反馈
      wx.vibrateShort({
        type: 'light' // 轻度振动
      });
      
      // 通过setData直接设置scrollTop值
      this.setData({
        scrollTop: 0
      });
    },
    
    // 辅助方法：设置控件显示并在1秒后开始淡出（过渡效果需要1秒，总共2秒完成）
    _showControlsWithTimeout() {
      // 显示控件
      this.setData({
        showControls: true
      });
      
      // 清除之前的定时器
      if (this.data.controlsTimer) {
        clearTimeout(this.data.controlsTimer);
        this.data.controlsTimer = null;
      }
      
      // 如果正在播放，设置1秒后开始淡出，再用1秒完成淡出
      if (this.data.isPlaying) {
        this.data.controlsTimer = setTimeout(() => {
          this.setData({
            showControls: false
          });
        }, 1000); // 1秒后开始淡出
      }
    },
    
    // 视频播放事件处理
    onVideoPlay() {
      log('[详情模态框] 视频开始播放');
      // 更新状态
      this.setData({
        isPlaying: true
      });
      
      // 显示控件并设置定时隐藏
      this._showControlsWithTimeout();
      
      this.triggerEvent('videoPlay');
    },
    
    // 视频暂停事件处理
    onVideoPause() {
      console.log('[详情模态框] 视频已暂停');
      // 更新状态
      this.setData({
        isPlaying: false,
        showControls: true // 暂停时保持控件显示
      });
      
      // 清除之前的定时器，防止控件被隐藏
      if (this.data.controlsTimer) {
        clearTimeout(this.data.controlsTimer);
        this.data.controlsTimer = null;
      }
      
      this.triggerEvent('videoPause');
    },
    
    // 视频结束事件处理
    onVideoEnd() {
      // 视频播放完毕，清除播放位置记录
      const videoId = (this.data.videoInfo || {})._id || (this.data.videoInfo || {}).id || (this.data.videoInfo || {}).videoId;
      if (videoId) {
        // 清除内存中的播放位置
        const playbackPositions = {...this.data.videoPlaybackPositions};
        delete playbackPositions[videoId];
        
        this.setData({
          videoPlaybackPositions: playbackPositions,
          currentPlayTime: 0 // 重置当前播放时间
        });
        
        // 同时清除本地存储中的播放位置
        try {
          wx.setStorageSync('videoPlaybackPositions', playbackPositions);
        } catch (e) {
          // 忽略本地存储错误
        }
      }
      
      // 通知父组件视频结束
      this.triggerEvent('videoEnd');
    },
    
    // 视频错误事件处理
    onVideoError(e) {
      console.error('[详情模态框] 视频加载失败:', e.detail);
      // 检查视频URL是否有效，只在第一次错误时处理
      if (this.data.videoUrl) {
        // 防止重复报错和重复加载，清空视频URL
        this.setData({
          videoUrl: ''
        });
        
        // 只在视频URL有效但加载失败时才显示提示
        wx.showToast({
          title: '视频加载失败',
          icon: 'none'
        });
      }
    },
    
    // 切换控制器显示状态
    toggleControls() {
      // 点击直接显示控件，不切换状态
      this._showControlsWithTimeout();
    },
    
    // 切换视频播放状态
    toggleVideoPlayStatus() {
      try {
        const videoContext = wx.createVideoContext('detailVideo', this);
        if (videoContext) {
          this.videoContext = videoContext;
          
          // 直接切换播放状态
          if (this.data.isPlaying) {
            videoContext.pause();
            // 不再在这里设置isPlaying状态，让onVideoPause事件处理
          } else {
            videoContext.play();
            // 不再在这里设置isPlaying状态，让onVideoPlay事件处理
          }
        }
      } catch (error) {
        console.error('视频播放器操作失败');
      }
    },
    
    // 记录触摸开始位置
    onTouchStart(e) {
      const touch = e.touches[0];
      if (touch) {
        this.setData({
          touchStartY: touch.clientY,
          // 初始化transform样式
          moveTransform: ''
        });
      }
    },
    
    // 阻止滚动穿透，同时添加下拉关闭功能
    onTouchMove(e) {
      // 获取触摸点位置
      const touch = e.touches[0];
      
      // 如果没有存储起始点，直接返回（正常情况不会发生，因为onTouchStart会设置）
      if (!this.data.touchStartY || !touch) {
        return false;
      }
      
      // 计算下拉距离
      const distance = touch.clientY - this.data.touchStartY;
      
      // 只有下拉时才处理（忽略上滑）
      if (distance > 0) {
        // 检查是否能获取到滚动容器
        wx.createSelectorQuery().in(this)
          .select('#modalScroll')
          .scrollOffset()
          .exec(res => {
            if (res && res[0]) {
              const scrollTop = res[0].scrollTop;
              
              // 只有在顶部位置才允许下拉关闭
              if (scrollTop <= 0) {
                // 添加下拉位移效果，让内容跟随手指移动
                const moveY = Math.min(distance * 0.5, 200); // 限制最大移动距离，并减缓移动速度
                this.setData({
                  moveTransform: `translateY(${moveY}px)`
                });
                
                // 如果下拉距离大于80px，关闭弹窗
                if (distance > 80) {
                  this.setData({
                    touchStartY: null,
                    isClosing: true, // 立即标记为关闭中，防止视图更新
                    moveTransform: '' // 重置transform
                  });
                  // 确保在关闭前暂停视频，但不清除视频URL
                  this._pauseVideoOnly();
                  this.onClose();
                  return false;
                }
              }
            }
          });
      }
      
      // 什么都不做，只阻止冒泡
      return false;
    },
    
    // 只暂停视频但不清除URL（防止闪烁）
    _pauseVideoOnly() {
      try {
        const videoContext = wx.createVideoContext('detailVideo', this);
        if (videoContext) {
          // 只暂停视频，不清除视频URL
          videoContext.pause();
          videoContext.stop();
          
          // 因为stop不会触发onVideoPause事件，所以这里需要手动设置状态
          this.setData({
            isPlaying: false
          });
        }
      } catch (err) {
        // 忽略错误
      }
    },
    
    // 添加触摸结束事件处理
    onTouchEnd() {
      // 重置触摸起始位置和位移效果
      this.setData({
        touchStartY: null,
        moveTransform: '' // 重置transform
      });
    },
    
    // 视频播放时间更新事件
    onVideoTimeUpdate(e) {
      // 更新当前播放时间
      const currentTime = e.detail.currentTime;
      const duration = e.detail.duration || 0;
      
      // 检查是否是初始seek操作完成
      if (!this.data._initialSeekDone && Math.abs(currentTime - this.data.currentPlayTime) < 0.5) {
        this.setData({
          _initialSeekDone: true
        });
      }
      
      // 检查是否接近视频结尾（最后1秒内）
      // 这用于捕获用户手动拖动到视频末尾的情况
      if (duration > 0 && duration - currentTime <= 1 && currentTime > 1) {
        // 如果接近结尾并且不是刚开始播放，认为用户已看完视频
      }
      
      // 避免频繁更新setData，只在时间变化较大时更新
      if (Math.abs(currentTime - this.data.currentPlayTime) > 1) {
        this.setData({
          currentPlayTime: currentTime
        });
        
        // 获取视频ID并保存播放位置
        const videoId = (this.data.videoInfo || {})._id || (this.data.videoInfo || {}).id || (this.data.videoInfo || {}).videoId;
        if (videoId) {
          const playbackPositions = {...this.data.videoPlaybackPositions};
          playbackPositions[videoId] = currentTime;
          this.setData({
            videoPlaybackPositions: playbackPositions
          });
        }
      }
    },
    
    // 关闭弹窗
    onClose() {
      // 添加短暂振动反馈
      wx.vibrateShort({
        type: 'light' // 轻度振动
      });
      
      // 保存当前播放位置
      this._savePlaybackPosition();
      
      // 保存当前滚动位置
      this._saveScrollPosition();
      
      // 先暂停视频播放，但在关闭动画期间不清除视频URL
      this._pauseVideoOnly();
      
      // 先恢复位置，然后再触发关闭动画
      this.setData({ 
        moveTransform: '', // 确保先重置位置
        touchStartY: null // 确保触摸状态被重置
      });
      
      // 标记为关闭中，触发CSS过渡效果
      this.setData({ 
        isClosing: true
      });
      
      // 在过渡动画完成后，隐藏弹窗
      setTimeout(() => {
        this.setData({
          visible: false,
          isClosing: false,
          videoUrl: '', // 只在完全关闭后清空视频URL
          _pendingVideoUrl: '', // 清空临时URL
          _initialSeekDone: false, // 重置seek状态
          isPlaying: false // 确保播放状态被重置
          // 注意：不清空richContent和currentPlayTime，保留播放位置记录
        });
        
        this.triggerEvent('close');
        // 通知页面显示导航栏
        this.triggerEvent('navbarControl', { action: 'show' });
      }, 300); // 动画持续时间
    },
    
    // 保存当前播放位置
    _savePlaybackPosition() {
      // 获取视频ID
      const videoId = (this.data.videoInfo || {})._id || (this.data.videoInfo || {}).id || (this.data.videoInfo || {}).videoId;
      if (!videoId) return;
      
      // 如果有视频上下文，获取当前播放位置
      if (this.videoContext) {
        try {
          // 将当前播放位置保存到缓存
          const playbackPositions = {...this.data.videoPlaybackPositions};
          playbackPositions[videoId] = this.data.currentPlayTime;
          
          this.setData({
            videoPlaybackPositions: playbackPositions
          });
          
          // 同时保存到本地存储，确保下次打开小程序时也能记住
          try {
            wx.setStorageSync('videoPlaybackPositions', playbackPositions);
          } catch (e) {
            // 忽略本地存储错误
          }
        } catch (err) {
          // 忽略错误
        }
      }
    },
    
    // 分享
    onShare() {
      this.triggerEvent('share');
    },
    
    // 导航栏控制
    onNavbarControl(e) {
      this.triggerEvent('navbarControl', e.detail);
    },
    

    
    // 检查全屏状态
    _checkFullscreenState() {
      // 此方法已被更高效的onFullscreenChange事件替代
      // 为防止旧代码干扰，保留方法但不执行实际操作
      console.log('使用原生fullscreenchange事件替代此方法');
    },

    // 全屏变化事件处理器
    onFullscreenChange(e) {
      const isFullscreen = e.detail.fullScreen;
      
      this.setData({
        isFullscreenMode: isFullscreen
      });

      if (isFullscreen) {
        this.setData({
          'modalHeightStyle': 'height: 100vh !important; position: fixed; top: 0; left: 0; right: 0; bottom: 0; z-index: 99999;'
        });

        if (this.data.isPortraitVideo) {
          this.setData({
            'modalHeightStyle': this.data.modalHeightStyle + 'display: flex; align-items: center; justify-content: center;'
          });
        }
      } else {
        this._getCapsuleInfo();
      }
      
      // 全屏切换时显示控件并设置定时隐藏
      this._showControlsWithTimeout();

      this.triggerEvent('fullscreenchange', {
        fullscreen: isFullscreen
      });
    },

    // 视频加载完成时检测方向
    onVideoLoadForDimensions(e) {
      // 创建选择器
      const query = this.createSelectorQuery();
      
      // 获取视频元素信息
      query.select('#detailVideo').boundingClientRect(res => {
        if (res) {
          // 判断视频方向
          const isPortrait = res.height > res.width;
          this.setData({
            isPortraitVideo: isPortrait
          });
          
          // 更新视频容器类名
          const container = this.selectComponent('.video-container');
          if (container) {
            if (isPortrait) {
              container.addClass('portrait');
            } else {
              container.removeClass('portrait');
            }
          }
        }
      }).exec();
    },

    // 视频播放准备就绪事件处理
    onVideoReady(e) {
      log('[详情模态框] 视频播放器已就绪');
      
      // 如果之前设置了播放，确保视频开始播放
      if (this.data.isPlaying) {
        setTimeout(() => {
          if (this.videoContext) {
            this.videoContext.play();
            log('[详情模态框] 视频就绪后触发播放');
          }
        }, 200);
      }
    },



    // 添加视频元数据加载完成事件处理
    onVideoLoadedMetaData(e) {
      log('[详情模态框] 视频元数据加载完成');
      
      // 视频已准备好，可以更可靠地进行操作
      if (this.videoContext && this.data.currentPlayTime > 0) {
        setTimeout(() => {
          this.videoContext.seek(this.data.currentPlayTime);
          log('[详情模态框] 元数据加载后重新seek到:', this.data.currentPlayTime);
        }, 100);
      }
    }
  }
});