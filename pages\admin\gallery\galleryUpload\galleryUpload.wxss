/* components/galleryUpload/galleryUpload.wxss */
.modal-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s, visibility 0.3s;
}

.modal-container.visible {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  width: 90%;
  max-width: 680rpx;
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  max-height: 90%;
}

.modal-header {
  padding: 24rpx 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid #f2f2f2;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.close-btn {
  font-size: 40rpx;
  color: #999;
  line-height: 1;
  padding: 10rpx;
  margin: -10rpx;
}

.modal-body {
  padding: 30rpx;
  flex: 1;
  max-height: 60vh;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300rpx;
  padding: 40rpx;
}

.loading-spinner {
  width: 80rpx;
  height: 80rpx;
  border: 4rpx solid #f5f5f5;
  border-top: 4rpx solid #07c160;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 30rpx;
  font-size: 28rpx;
  color: #999;
}

.form-item {
  margin-bottom: 30rpx;
  background-color: rgba(255, 255, 255, 1);
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  position: relative;
  z-index: 5;
  margin-left: 0;
  margin-right: 0;
  width: 100%;
  box-sizing: border-box;
}

.form-label {
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 16rpx;
  font-weight: bold;
  display: block;
  width: 100%;
  box-sizing: border-box;
}

.form-label.required::before {
  content: '*';
  color: #f56c6c;
  margin-right: 4rpx;
}

.form-content {
  width: 100%;
  box-sizing: border-box;
}

.form-input {
  width: 100%;
  height: 80rpx;
  border: 1rpx solid #dddddd;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
  background-color: #ffffff;
  color: #333333;
  position: relative;
  z-index: 5;
}

.form-input::placeholder {
  color: rgba(153, 153, 153, 0.8);
  opacity: 0.8;
}

.form-textarea {
  width: 100%;
  height: 200rpx;
  border: 2rpx solid #666;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
  background-color: #ffffff;
  color: #000000;
  font-weight: 600;
}

.upload-btn {
  width: 300rpx;
  height: 200rpx;
  border: 2rpx dashed #ddd;
  border-radius: 8rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #fafafa;
  cursor: pointer;
}

.upload-icon {
  font-size: 50rpx;
  color: #999;
  margin-bottom: 10rpx;
  line-height: 1;
}

.upload-text {
  font-size: 26rpx;
  color: #999;
}

.image-preview-container {
  position: relative;
  width: 300rpx;
  height: 200rpx;
  border-radius: 8rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.cover-preview {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s;
}

.image-preview-container:hover .image-overlay {
  opacity: 1;
}

.loading-overlay {
  opacity: 1;
  background-color: rgba(0, 0, 0, 0.7);
  flex-direction: column;
}

.mini-loading {
  width: 40rpx;
  height: 40rpx;
  border: 3rpx solid rgba(255, 255, 255, 0.3);
  border-top: 3rpx solid #fff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 10rpx;
}

.overlay-text {
  color: #fff;
  font-size: 26rpx;
}

.image-grid {
  display: flex;
  flex-wrap: wrap;
  margin: -10rpx;
}

.detail-image-container {
  position: relative;
  width: 150rpx;
  height: 150rpx;
  margin: 10rpx;
  border-radius: 8rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.detail-image-preview {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.delete-btn {
  position: absolute;
  top: 0;
  right: 0;
  width: 40rpx;
  height: 40rpx;
  background-color: rgba(0, 0, 0, 0.6);
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 26rpx;
  line-height: 1;
  border-bottom-left-radius: 8rpx;
}

.upload-detail-btn {
  width: 150rpx;
  height: 150rpx;
  margin: 10rpx;
  border: 2rpx dashed #ddd;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fafafa;
}

.image-tip {
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
}

.modal-footer {
  padding: 20rpx 30rpx 30rpx;
  display: flex;
  justify-content: flex-end;
  border-top: 1rpx solid #f2f2f2;
}

.btn {
  padding: 16rpx 40rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: 500;
  line-height: 1.5;
  transition: all 0.3s;
  margin-left: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-cancel {
  background-color: #f5f5f5;
  color: #666;
}

.btn-cancel:active {
  background-color: #e8e8e8;
}

.btn-primary {
  background-color: #3cba92;
  color: white;
}

.btn-primary:active {
  background-color: #36a583;
  transform: scale(0.98);
}

.btn[disabled] {
  opacity: 0.6;
  pointer-events: none;
}

.btn-loading {
  width: 30rpx;
  height: 30rpx;
  border: 3rpx solid rgba(255, 255, 255, 0.3);
  border-top: 3rpx solid #fff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 10rpx;
}

/* 画廊上传组件样式 - 全屏设计 */
.gallery-upload-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  background-color: #f5f5f7; /* 标准浅灰色背景 */
  visibility: hidden;
  opacity: 0;
  transition: visibility 0.2s, opacity 0.2s; /* 统一过渡时间 */
}

.gallery-upload-container.visible {
  visibility: visible;
  opacity: 1;
}

.gallery-upload-content {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f7; /* 标准浅灰色背景 */
  z-index: 1001;
}

.gallery-upload-header {
  padding: 20rpx 30rpx;
  padding-top: 90rpx; /* 适应状态栏高度 */
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #ffffff;
  border-bottom: 1rpx solid #e0e0e0; /* 标准边框颜色 */
  position: relative;
  z-index: 10;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.back-icon {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  color: #4a4a4a; /* 标准深灰色 */
  transition: all 0.2s ease; /* 统一过渡时间 */
}

.back-icon:active {
  opacity: 0.8;
  transform: scale(0.95);
}

.gallery-upload-title {
  font-size: 34rpx;
  font-weight: bold;
  color: #4a4a4a; /* 标准深灰色 */
  flex: 1;
  text-align: center;
}

.header-actions {
  width: 60rpx;
}

.gallery-upload-form {
  flex: 1;
  padding: 30rpx 40rpx;
  background-color: #f5f5f5;
  overflow-y: auto;
  box-sizing: border-box;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300rpx;
  padding: 40rpx;
}

.loading-spinner {
  width: 80rpx;
  height: 80rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #ff9a9e;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 20rpx;
  color: #999999;
  font-size: 28rpx;
}

.form-item {
  margin-bottom: 30rpx;
  background-color: #ffffff;
  border-radius: 6rpx; /* 标准卡片圆角 */
  padding: 24rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  position: relative;
  z-index: 5;
  margin-left: 0;
  margin-right: 0;
  width: 100%;
  box-sizing: border-box;
}

.form-label {
  font-size: 28rpx;
  color: #4a4a4a; /* 标准深灰色 */
  margin-bottom: 16rpx;
  font-weight: bold;
  display: block;
  width: 100%;
  box-sizing: border-box;
}

.required {
  color: #ff3b30; /* 标准红色 */
  margin-left: 4rpx;
}

.form-input {
  width: 100%;
  height: 80rpx;
  border: 1rpx solid #e0e0e0; /* 标准边框颜色 */
  border-radius: 4rpx; /* 标准按钮圆角 */
  padding: 0 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
  background-color: #ffffff;
  color: #333333;
  position: relative;
  z-index: 5;
  transition: all 0.2s ease; /* 统一过渡时间 */
}

.form-input:focus {
  border-color: #0070c9; /* 标准蓝色 */
  background-color: #ffffff;
}

.form-textarea {
  width: 100%;
  height: 200rpx;
  border: 1rpx solid #dddddd;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
  background-color: #ffffff;
  color: #333333;
}

/* 分类选择器容器 */
.category-selector-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

/* 分类选择器样式 */
.category-selector {
  flex: 1;
  height: 80rpx;
  border: 1rpx solid #e0e0e0; /* 标准边框颜色 */
  border-radius: 4rpx; /* 标准按钮圆角 */
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20rpx;
  background-color: #ffffff;
  margin-right: 20rpx;
  transition: all 0.2s ease; /* 统一过渡时间 */
}

.category-selector:active {
  background-color: #f8f8f8;
  border-color: #0070c9; /* 标准蓝色 */
}

.category-name {
  font-size: 28rpx;
  color: #333333;
}

.manage-category-btn {
  width: 120rpx;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  background-color: #0070c9; /* 标准蓝色 */
  color: #ffffff;
  font-size: 28rpx;
  border-radius: 4rpx; /* 标准按钮圆角 */
  transition: all 0.2s ease; /* 统一过渡时间 */
}

.manage-category-btn:active {
  background-color: #005ba3; /* 深蓝色，用于点击效果 */
  transform: scale(0.98);
}

/* 图片上传样式 - 封面图片放大并居中 */
.image-upload {
  width: 300rpx;
  height: 300rpx;
  border: 1rpx dashed #dddddd;
  border-radius: 8rpx;
  overflow: hidden;
  position: relative;
  margin: 0 auto;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  transition: transform 0.2s, box-shadow 0.2s;
  cursor: pointer;
}

.image-upload:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.15);
}

.cover-preview {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.upload-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: #f5f7fa;
}

.upload-icon {
  font-size: 60rpx;
  color: #999999;
  margin-bottom: 10rpx;
}

.upload-text {
  font-size: 26rpx;
  color: #999999;
}

/* 详情图片样式 - 居中显示 */
.detail-images {
  display: flex;
  justify-content: center;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
}

.detail-image-item {
  width: 300rpx;
  height: 300rpx;
  margin: 10rpx auto;
  position: relative;
  border-radius: 8rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  transition: transform 0.2s, box-shadow 0.2s;
  cursor: pointer;
}

.detail-image-item:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.15);
}

.detail-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 隐藏添加按钮，但保持功能可用 */
.detail-image-add {
  width: 300rpx;
  height: 300rpx;
  margin: 10rpx auto;
  border: 1rpx dashed #dddddd;
  border-radius: 8rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f7fa;
  transition: transform 0.2s, background-color 0.2s;
  cursor: pointer;
}

.detail-image-add:active {
  transform: scale(0.98);
  background-color: #eef0f3;
}

/* 底部按钮样式 */
.gallery-upload-footer {
  padding: 20rpx 30rpx;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
  display: flex;
  justify-content: space-between;
  background-color: #ffffff;
  border-top: 1rpx solid #e0e0e0; /* 标准边框颜色 */
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.cancel-btn {
  width: 220rpx;
  height: 90rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f5f7; /* 标准浅灰色背景 */
  color: #4a4a4a; /* 标准深灰色 */
  font-size: 32rpx;
  border-radius: 4rpx; /* 标准按钮圆角 */
  border: none;
  transition: all 0.2s ease; /* 统一过渡时间 */
}

.cancel-btn:active {
  background-color: #e0e0e0;
  transform: scale(0.98);
}

.save-btn {
  width: 400rpx;
  height: 90rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #0070c9; /* 标准蓝色 */
  color: #ffffff;
  font-size: 32rpx;
  border-radius: 4rpx; /* 标准按钮圆角 */
  border: none;
  box-shadow: 0 2rpx 8rpx rgba(0, 112, 201, 0.3); /* 蓝色阴影 */
  transition: all 0.2s ease; /* 统一过渡时间 */
}

.save-btn:active {
  background-color: #005ba3; /* 深蓝色，用于点击效果 */
  transform: scale(0.98);
  box-shadow: 0 1rpx 5rpx rgba(0, 112, 201, 0.2);
}

/* 帮助提示区域样式 */
.help-section {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.help-content {
  font-size: 26rpx;
  color: #666666;
}

.help-item {
  margin-bottom: 10rpx;
  line-height: 1.5;
}

/* 分类选择弹窗样式 */
.category-picker-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1001;
  visibility: hidden;
  opacity: 0;
  transition: visibility 0.3s, opacity 0.3s;
}

.category-picker-mask.visible {
  visibility: visible;
  opacity: 1;
}

.category-picker {
  position: fixed;
  top: 50%;
  left: 50%;
  width: 80%;
  max-width: 560rpx;
  height: 60%;
  max-height: 800rpx;
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  z-index: 1002;
  transform: translate(-50%, -50%) scale(0.8);
  opacity: 0;
  visibility: hidden;
  transition: transform 0.3s, opacity 0.3s, visibility 0.3s;
  display: flex;
  flex-direction: column;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.category-picker.visible {
  transform: translate(-50%, -50%) scale(1);
  opacity: 1;
  visibility: visible;
}

.category-picker-header {
  padding: 24rpx 30rpx;
  border-bottom: 1rpx solid #f2f2f2;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #ffffff;
}

.category-picker-close {
  font-size: 40rpx;
  color: #999999;
  cursor: pointer;
}

.category-picker-content {
  flex: 1;
  overflow-y: auto;
}

.category-item {
  padding: 30rpx;
  border-bottom: 1rpx solid #f2f2f2;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #333333;
  font-weight: 500;
}

.category-item.selected {
  background-color: #f5f7fa;
}

.category-selected-icon {
  color: #ff9a9e;
  font-weight: bold;
}

.category-empty {
  padding: 60rpx 0;
  text-align: center;
  color: #999999;
}

/* 分类管理模态框 */
.category-manager-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1050;
}

.category-manager-container {
  width: 90%;
  max-width: 600rpx;
  background-color: #ffffff;
  border-radius: 6rpx; /* 标准卡片圆角 */
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.2);
  z-index: 1051;
}

.category-manager-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 30rpx;
  border-bottom: 1rpx solid #e0e0e0; /* 标准边框颜色 */
  background-color: #ffffff;
}

.category-manager-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #4a4a4a; /* 标准深灰色 */
}

.category-manager-close {
  font-size: 40rpx;
  color: #999999;
  padding: 0 10rpx;
}

.category-manager-content {
  padding: 24rpx;
  max-height: 70vh;
  overflow-y: auto;
  background-color: #f5f5f5;
}

.category-add-section {
  display: flex;
  margin-bottom: 32rpx;
  background-color: #ffffff;
  padding: 20rpx;
  border-radius: 6rpx; /* 标准卡片圆角 */
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.category-input {
  flex: 1;
  height: 72rpx;
  border: 1rpx solid #e0e0e0; /* 标准边框颜色 */
  border-radius: 4rpx; /* 标准按钮圆角 */
  padding: 0 16rpx;
  font-size: 28rpx;
  margin-right: 16rpx;
  background-color: #ffffff;
  color: #333333;
  transition: all 0.2s ease; /* 统一过渡时间 */
}

.category-input:focus {
  border-color: #0070c9; /* 标准蓝色 */
}

.category-add-btn {
  width: 140rpx;
  height: 72rpx;
  line-height: 72rpx;
  text-align: center;
  background-color: #0070c9; /* 标准蓝色 */
  color: #ffffff;
  font-size: 28rpx;
  border-radius: 4rpx; /* 标准按钮圆角 */
  padding: 0;
  transition: all 0.2s ease; /* 统一过渡时间 */
}

.category-add-btn:active {
  background-color: #005ba3; /* 深蓝色，用于点击效果 */
  transform: scale(0.98);
}

.category-list {
  max-height: 50vh;
  overflow-y: auto;
  background-color: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.category-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 16rpx;
  border-bottom: 1rpx solid #f2f2f2;
}

.category-item-name {
  font-size: 28rpx;
  color: #333333;
  flex: 1;
}

.category-item-delete {
  color: #ff3b30; /* 标准红色 */
  font-size: 26rpx;
  padding: 4rpx 12rpx;
  transition: all 0.2s ease; /* 统一过渡时间 */
}

.category-item-delete:active {
  opacity: 0.8;
  transform: scale(0.95);
}

.category-input::placeholder {
  color: rgba(153, 153, 153, 0.8);
  opacity: 0.8;
}

/* 上传提示文本居中 */
.upload-tip {
  font-size: 24rpx;
  color: #999999;
  margin-top: 10rpx;
  text-align: center;
  padding: 0 20rpx;
} 