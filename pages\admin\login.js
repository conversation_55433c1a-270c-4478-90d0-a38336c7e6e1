const app = getApp()

Page({
  data: {
    username: '',
    password: '',
    isLoading: false
  },

  // 用户名输入
  onUsernameInput(e) {
    this.setData({
      username: e.detail.value
    })
  },

  // 密码输入
  onPasswordInput(e) {
    this.setData({
      password: e.detail.value
    })
  },

  // 返回上一页
  navigateBack() {
    wx.navigateBack({
      delta: 1
    })
  },

  // 管理员登录
  login() {
    const { username, password } = this.data
    
    // 输入验证
    if (!username || !password) {
      wx.showToast({
        title: '请输入用户名和密码',
        icon: 'none'
      })
      return
    }
    
    this.setData({ isLoading: true })
    
    // 使用云函数验证登录
    wx.cloud.callFunction({
      name: 'adminManager',
      data: {
        action: 'login',
        username: username,
        password: password
      },
      success: res => {
        console.log('登录验证结果:', res)
        
        if (res.result && res.result.code === 0) {
          // 登录成功
          app.globalData.isAdmin = true
          app.globalData.adminInfo = {
            adminId: res.result.data._id || 'admin-id',
            username: res.result.data.username
          }
          
          // 获取当前用户openid并设置为管理员
          wx.cloud.callFunction({
            name: 'setAdminUser',
            data: {
              username: username
            },
            success: setRes => {
              console.log('设置管理员成功:', setRes)
              
              // 直接跳转到管理页面，静默无提示
              wx.redirectTo({
                url: '/pages/admin/admin'
              })
            },
            fail: err => {
              console.error('设置管理员失败:', err)
              // 即使设置管理员失败，也继续登录流程，直接跳转
              wx.redirectTo({
                url: '/pages/admin/admin'
              })
            }
          })
        } else {
          // 登录失败
          wx.showToast({
            title: res.result?.message || '账号或密码错误',
            icon: 'none'
          })
          this.setData({ isLoading: false })
        }
      },
      fail: err => {
        console.error('登录验证失败:', err)
        
        // 检查是否是集合不存在错误
        if (err.errMsg && err.errMsg.includes('collection not exists')) {
          // 尝试初始化管理员账户
          this.initAdminAccount()
        } else {
          wx.showToast({
            title: '网络错误，请重试',
            icon: 'none'
          })
        }
        
        this.setData({ isLoading: false })
      }
    })
  },
  
  // 初始化管理员账户
  initAdminAccount() {
    console.log('尝试初始化管理员账户')
    
    wx.cloud.callFunction({
      name: 'adminManager',
      data: {
        action: 'initAdmin',
        username: 'admin',
        password: 'admin123'
      },
      success: res => {
        console.log('初始化管理员账户结果:', res)
        
        if (res.result && res.result.code === 0) {
          wx.showModal({
            title: '系统提示',
            content: '首次使用需要初始化管理员账户。默认账户：admin，默认密码：admin123',
            showCancel: false,
            success: () => {
              // 自动填充默认账号密码
              this.setData({
                username: 'admin',
                password: 'admin123'
              })
            }
          })
        } else {
          wx.showToast({
            title: '初始化失败，请重试',
            icon: 'none'
          })
        }
      },
      fail: err => {
        console.error('初始化管理员账户失败:', err)
        
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        })
      }
    })
  }
}) 