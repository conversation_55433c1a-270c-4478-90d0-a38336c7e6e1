/* 员工登录页面样式 */
.staff-login-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  min-height: 100vh;
  width: 100%;
  background: #ffffff;
  position: relative;
  padding: 0 40rpx;
  box-sizing: border-box;
  margin-top: 0;
}

.login-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  margin-bottom: 40rpx;
  margin-top: 120rpx;
}

.logo-container {
  width: 180rpx;
  height: 180rpx;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.95);
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 200rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.logo {
  width: 120rpx;
  height: 120rpx;
}

/* 员工登录入口标题 */
.staff-title {
  font-size: 32rpx;
  color: #333;
  margin-top: 20rpx;
  font-weight: 500;
}

.login-form {
  width: 100%;
  background-color: #fff;
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  box-sizing: border-box;
}

.input-group {
  margin-bottom: 30rpx;
}

.input-label {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.input-control {
  width: 100%;
  height: 80rpx;
  border-bottom: 1rpx solid #eee;
  font-size: 30rpx;
  padding: 0 10rpx;
  box-sizing: border-box;
  background: transparent;
  color: #333;
}

.input-control:focus {
  border-bottom-color: #07C160;
}

.input-control::placeholder {
  color: #cccccc;
}

.login-btn {
  width: 100%;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f9f9f9;
  color: #000000;
  font-size: 32rpx;
  border-radius: 44rpx;
  margin-top: 40rpx;
  border: 1rpx solid #ddd;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.btn-hover {
  background-color: #f0f0f0;
}

.login-tips {
  margin-top: 30rpx;
  font-size: 24rpx;
  color: #999;
  text-align: center;
}

.back-btn {
  position: fixed;
  bottom: 160rpx;
  left: 50%;
  transform: translateX(-50%);
  font-size: 28rpx;
  color: #07C160;
  padding: 10rpx 20rpx;
  border-radius: 30rpx;
}

/* 品牌运营管理系统标题 */
.system-title {
  position: fixed;
  bottom: 100rpx;
  left: 50%;
  transform: translateX(-50%);
  font-size: 28rpx;
  color: #999;
}

.loading-container {
  width: 100%;
  height: 100vh;
  background-color: #ffffff;
} 