/**
 * 视频相关工具函数
 * 从主页面提取的视频处理、格式化、去重等工具函数
 */

/**
 * 格式化播放量数字
 * @param {number|string} count - 播放量数字
 * @returns {string} 格式化后的播放量字符串
 */
function formatCount(count) {
  count = parseInt(count, 10) || 0;
  
  if (count >= 10000) {
    // 对于万级数字，显示为"x.xw"
    return (count / 10000).toFixed(1).replace(/\.0$/, '') + 'w';
  } else if (count >= 1000) {
    // 对于千级数字，显示为"x.xk"
    return (count / 1000).toFixed(1).replace(/\.0$/, '') + 'k';
  } else {
    // 对于小于1000的数字，直接显示
    return count.toString();
  }
}

/**
 * 根据ID去重视频列表
 * @param {Array} videoList - 视频列表
 * @returns {Array} 去重后的视频列表
 */
function deduplicateVideoList(videoList) {
  if (!Array.isArray(videoList)) {
    console.warn('[VideoUtils] deduplicateVideoList: 输入不是数组');
    return [];
  }
  
  const uniqueVideos = [];
  const videoIds = new Set();
  
  for (const video of videoList) {
    if (!video || !video.id) {
      console.warn('[VideoUtils] 跳过无效视频数据:', video);
      continue;
    }
    
    if (!videoIds.has(video.id)) {
      videoIds.add(video.id);
      uniqueVideos.push(video);
    } else {
      console.log('[VideoUtils] 检测到重复视频ID，已过滤:', video.id);
    }
  }
  
  console.log(`[VideoUtils] 原始视频数量: ${videoList.length}, 去重后: ${uniqueVideos.length}`);
  return uniqueVideos;
}

/**
 * 清理和标准化视频数据
 * @param {Object} item - 原始视频数据
 * @returns {Object} 清理后的视频数据
 */
function sanitizeVideoData(item) {
  if (!item) {
    console.warn('[VideoUtils] sanitizeVideoData: 输入数据为空');
    return null;
  }
  
  return {
    id: item.id || '',
    baseId: item.baseId || item.id || '',
    mainTitle: item.mainTitle || item.title || '',
    subTitle: item.subTitle || item.subtitle || '',
    coverUrl: item.coverUrl || '',
    videoUrl: item.videoUrl || '',
    playCount: formatCount(item.playCount || 0),
    author: item.author || '',
    authorAvatar: item.authorAvatar || '/static/logo.png',
    isPlaying: false,
    description: item.description || '',
    urlError: false,
    // 保留原始数据的其他字段
    fileKey: item.fileKey || '',
    createTime: item.createTime || '',
    updateTime: item.updateTime || ''
  };
}

/**
 * 验证视频数据的完整性
 * @param {Object} video - 视频数据
 * @returns {Object} 验证结果
 */
function validateVideoData(video) {
  const result = {
    valid: true,
    errors: [],
    warnings: []
  };
  
  if (!video) {
    result.valid = false;
    result.errors.push('视频数据为空');
    return result;
  }
  
  // 必需字段检查
  if (!video.id) {
    result.valid = false;
    result.errors.push('缺少视频ID');
  }
  
  if (!video.mainTitle && !video.title) {
    result.valid = false;
    result.errors.push('缺少视频标题');
  }
  
  // 可选字段警告
  if (!video.coverUrl) {
    result.warnings.push('缺少封面图片');
  }
  
  if (!video.author) {
    result.warnings.push('缺少作者信息');
  }
  
  return result;
}

/**
 * 批量验证视频列表
 * @param {Array} videoList - 视频列表
 * @returns {Object} 批量验证结果
 */
function validateVideoList(videoList) {
  if (!Array.isArray(videoList)) {
    return {
      valid: false,
      error: '输入不是数组',
      validCount: 0,
      invalidCount: 0
    };
  }
  
  let validCount = 0;
  let invalidCount = 0;
  const invalidVideos = [];
  
  videoList.forEach((video, index) => {
    const validation = validateVideoData(video);
    if (validation.valid) {
      validCount++;
    } else {
      invalidCount++;
      invalidVideos.push({
        index,
        video,
        errors: validation.errors
      });
    }
  });
  
  return {
    valid: invalidCount === 0,
    validCount,
    invalidCount,
    invalidVideos,
    total: videoList.length
  };
}

/**
 * 过滤有效的视频数据
 * @param {Array} videoList - 视频列表
 * @returns {Array} 有效的视频列表
 */
function filterValidVideos(videoList) {
  if (!Array.isArray(videoList)) {
    return [];
  }
  
  return videoList.filter(video => {
    const validation = validateVideoData(video);
    if (!validation.valid) {
      console.warn('[VideoUtils] 过滤无效视频:', validation.errors);
    }
    return validation.valid;
  });
}

/**
 * 按播放量排序视频列表
 * @param {Array} videoList - 视频列表
 * @param {string} order - 排序方式 ('asc' | 'desc')
 * @returns {Array} 排序后的视频列表
 */
function sortVideosByPlayCount(videoList, order = 'desc') {
  if (!Array.isArray(videoList)) {
    return [];
  }
  
  return [...videoList].sort((a, b) => {
    const countA = parseInt(a.playCount, 10) || 0;
    const countB = parseInt(b.playCount, 10) || 0;
    
    return order === 'desc' ? countB - countA : countA - countB;
  });
}

/**
 * 按时间排序视频列表
 * @param {Array} videoList - 视频列表
 * @param {string} order - 排序方式 ('asc' | 'desc')
 * @returns {Array} 排序后的视频列表
 */
function sortVideosByTime(videoList, order = 'desc') {
  if (!Array.isArray(videoList)) {
    return [];
  }
  
  return [...videoList].sort((a, b) => {
    const timeA = new Date(a.createTime || a.updateTime || 0).getTime();
    const timeB = new Date(b.createTime || b.updateTime || 0).getTime();
    
    return order === 'desc' ? timeB - timeA : timeA - timeB;
  });
}

/**
 * 搜索视频列表
 * @param {Array} videoList - 视频列表
 * @param {string} keyword - 搜索关键词
 * @param {Object} options - 搜索选项
 * @returns {Array} 搜索结果
 */
function searchVideos(videoList, keyword, options = {}) {
  if (!Array.isArray(videoList) || !keyword) {
    return [];
  }
  
  const {
    caseSensitive = false,
    searchFields = ['mainTitle', 'subTitle', 'description', 'author']
  } = options;
  
  const searchTerm = caseSensitive ? keyword : keyword.toLowerCase();
  
  return videoList.filter(video => {
    return searchFields.some(field => {
      const fieldValue = video[field] || '';
      const searchValue = caseSensitive ? fieldValue : fieldValue.toLowerCase();
      return searchValue.includes(searchTerm);
    });
  });
}

/**
 * 获取视频统计信息
 * @param {Array} videoList - 视频列表
 * @returns {Object} 统计信息
 */
function getVideoStats(videoList) {
  if (!Array.isArray(videoList)) {
    return {
      total: 0,
      totalPlayCount: 0,
      averagePlayCount: 0,
      withCover: 0,
      withoutCover: 0,
      authors: []
    };
  }
  
  let totalPlayCount = 0;
  let withCover = 0;
  let withoutCover = 0;
  const authors = new Set();
  
  videoList.forEach(video => {
    // 统计播放量
    const playCount = parseInt(video.playCount, 10) || 0;
    totalPlayCount += playCount;
    
    // 统计封面图
    if (video.coverUrl) {
      withCover++;
    } else {
      withoutCover++;
    }
    
    // 统计作者
    if (video.author) {
      authors.add(video.author);
    }
  });
  
  return {
    total: videoList.length,
    totalPlayCount,
    averagePlayCount: videoList.length > 0 ? Math.round(totalPlayCount / videoList.length) : 0,
    withCover,
    withoutCover,
    authors: Array.from(authors),
    authorCount: authors.size
  };
}

/**
 * 创建视频分享数据
 * @param {Object} video - 视频数据
 * @returns {Object} 分享数据
 */
function createVideoShareData(video) {
  if (!video) {
    return null;
  }
  
  // 组合主标题和副标题
  const shareTitle = video.subTitle 
    ? `${video.mainTitle || ''} - ${video.subTitle}` 
    : (video.mainTitle || '精彩视频');
  
  return {
    contentType: 'video',
    contentId: video.id,
    title: shareTitle,
    path: `/pages/index/index?videoId=${video.id}`,
    imageUrl: video.coverUrl || ''
  };
}

/**
 * 批量创建视频分享数据
 * @param {Array} videoList - 视频列表
 * @returns {Array} 分享数据列表
 */
function createVideoShareDataBatch(videoList) {
  if (!Array.isArray(videoList)) {
    return [];
  }
  
  return videoList
    .map(video => createVideoShareData(video))
    .filter(shareData => shareData !== null);
}

/**
 * 检查视频是否正在播放
 * @param {Object} video - 视频数据
 * @returns {boolean} 是否正在播放
 */
function isVideoPlaying(video) {
  return !!(video && video.isPlaying);
}

/**
 * 设置视频播放状态
 * @param {Object} video - 视频数据
 * @param {boolean} isPlaying - 是否播放
 * @returns {Object} 更新后的视频数据
 */
function setVideoPlayingState(video, isPlaying) {
  if (!video) {
    return null;
  }
  
  return {
    ...video,
    isPlaying: !!isPlaying
  };
}

/**
 * 批量设置视频播放状态
 * @param {Array} videoList - 视频列表
 * @param {string} playingVideoId - 正在播放的视频ID
 * @returns {Array} 更新后的视频列表
 */
function setVideoListPlayingState(videoList, playingVideoId) {
  if (!Array.isArray(videoList)) {
    return [];
  }
  
  return videoList.map(video => ({
    ...video,
    isPlaying: video.id === playingVideoId
  }));
}

// 导出所有工具函数
module.exports = {
  // 基础工具函数
  formatCount,
  deduplicateVideoList,
  sanitizeVideoData,
  
  // 验证函数
  validateVideoData,
  validateVideoList,
  filterValidVideos,
  
  // 排序和搜索
  sortVideosByPlayCount,
  sortVideosByTime,
  searchVideos,
  
  // 统计和分析
  getVideoStats,
  
  // 分享相关
  createVideoShareData,
  createVideoShareDataBatch,
  
  // 播放状态管理
  isVideoPlaying,
  setVideoPlayingState,
  setVideoListPlayingState
};