/* components/serviceDetailModal/serviceDetailModal.wxss */

/* 弹窗容器 */
.service-detail-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 遮罩层 */
.modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: opacity 0.3s ease;
}

.modal-overlay.fade-in {
  opacity: 1;
}

.modal-overlay.fade-out {
  opacity: 0;
}

/* 弹窗内容 */
.modal-content {
  position: relative;
  max-width: 90vw;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  transform: scale(0.8);
  transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* 呼吸式动画 - 进入 */
.modal-content.breathe-in {
  animation: breatheIn 0.5s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
}

/* 呼吸式动画 - 退出 */
.modal-content.breathe-out {
  animation: breatheOut 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

@keyframes breatheIn {
  0% {
    transform: scale(0.7) translateY(30rpx);
    opacity: 0;
    filter: blur(4rpx);
  }
  30% {
    transform: scale(0.9) translateY(10rpx);
    opacity: 0.6;
    filter: blur(2rpx);
  }
  70% {
    transform: scale(1.08) translateY(-5rpx);
    opacity: 0.9;
    filter: blur(0);
  }
  100% {
    transform: scale(1) translateY(0);
    opacity: 1;
    filter: blur(0);
  }
}

@keyframes breatheOut {
  0% {
    transform: scale(1) translateY(0);
    opacity: 1;
    filter: blur(0);
  }
  30% {
    transform: scale(1.05) translateY(-10rpx);
    opacity: 0.8;
    filter: blur(1rpx);
  }
  70% {
    transform: scale(0.9) translateY(10rpx);
    opacity: 0.4;
    filter: blur(2rpx);
  }
  100% {
    transform: scale(0.7) translateY(30rpx);
    opacity: 0;
    filter: blur(4rpx);
  }
}

/* 详情图片 */
.detail-image {
  width: 100%;
  height: auto;
  max-width: 90vw;
  max-height: 70vh;
  min-height: 200rpx;
  border-radius: 12rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.3);
  background-color: rgba(255, 255, 255, 0.1);
}

/* 关闭提示 */
.close-hint {
  margin-top: 30rpx;
  color: rgba(255, 255, 255, 0.8);
  font-size: 24rpx;
  text-align: center;
  opacity: 0;
  animation: fadeInDelay 0.5s ease 0.3s forwards;
}

@keyframes fadeInDelay {
  from {
    opacity: 0;
    transform: translateY(10rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
