/**
 * 基础模块类
 * 所有功能模块的基类，提供通用的初始化、销毁、错误处理等功能
 */
class BaseModule {
  constructor(pageContext) {
    if (!pageContext) {
      throw new Error('BaseModule requires pageContext');
    }
    
    // 保存页面上下文引用
    this.page = pageContext;
    this.data = pageContext.data;
    this.setData = pageContext.setData.bind(pageContext);
    
    // 模块状态
    this.initialized = false;
    this.destroyed = false;
    
    // 模块名称（子类应该重写）
    this.moduleName = this.constructor.name;
    
    // 错误处理器引用
    this.errorHandler = null;
    
    // 事件监听器列表（用于清理）
    this.eventListeners = [];
    
    // 事件发射器 - 简单实现
    this.events = new Map();
    
    console.log(`[${this.moduleName}] 模块已创建`);
  }
  
  /**
   * 初始化方法 - 子类必须实现
   * @returns {Promise<void>}
   */
  async init() {
    throw new Error(`${this.moduleName}: init method must be implemented by subclass`);
  }
  
  /**
   * 销毁方法 - 清理资源
   */
  destroy() {
    if (this.destroyed) {
      return;
    }
    
    try {
      // 清理事件监听器
      this.clearEventListeners();
      
      // 标记为已销毁
      this.destroyed = true;
      this.initialized = false;
      
      console.log(`[${this.moduleName}] 模块已销毁`);
    } catch (error) {
      this.handleError(error, 'destroy');
    }
  }
  
  /**
   * 检查模块是否已初始化
   * @returns {boolean}
   */
  isInitialized() {
    return this.initialized && !this.destroyed;
  }
  
  /**
   * 检查模块是否已销毁
   * @returns {boolean}
   */
  isDestroyed() {
    return this.destroyed;
  }
  
  /**
   * 安全的 setData 调用
   * @param {Object} data - 要设置的数据
   * @param {Function} callback - 回调函数
   */
  safeSetData(data, callback) {
    if (this.destroyed) {
      console.warn(`[${this.moduleName}] 尝试在已销毁的模块上调用 setData`);
      return;
    }
    
    try {
      this.setData(data, callback);
    } catch (error) {
      this.handleError(error, 'safeSetData');
    }
  }
  
  /**
   * 安全的异步操作包装
   * @param {Function} asyncFn - 异步函数
   * @param {string} context - 操作上下文
   * @returns {Promise<any>}
   */
  async safeAsync(asyncFn, context = 'unknown') {
    if (this.destroyed) {
      console.warn(`[${this.moduleName}] 尝试在已销毁的模块上执行异步操作: ${context}`);
      return null;
    }
    
    try {
      return await asyncFn();
    } catch (error) {
      this.handleError(error, context);
      return null;
    }
  }
  
  /**
   * 错误处理方法
   * @param {Error} error - 错误对象
   * @param {string} context - 错误上下文
   */
  handleError(error, context = 'unknown') {
    const errorInfo = {
      module: this.moduleName,
      context: context,
      message: error.message || error,
      stack: error.stack,
      timestamp: new Date().toISOString()
    };
    
    console.error(`[${this.moduleName}] Error in ${context}:`, errorInfo);
    
    // 如果页面有错误处理方法，调用它
    if (this.page && typeof this.page.handleModuleError === 'function') {
      try {
        this.page.handleModuleError(this.moduleName, error, context);
      } catch (handlerError) {
        console.error(`[${this.moduleName}] Error in error handler:`, handlerError);
      }
    }
    
    // 如果有全局错误处理器，也调用它
    if (this.errorHandler && typeof this.errorHandler.handleModuleError === 'function') {
      try {
        this.errorHandler.handleModuleError(this.moduleName, error, context);
      } catch (handlerError) {
        console.error(`[${this.moduleName}] Error in global error handler:`, handlerError);
      }
    }
  }
  
  /**
   * 设置错误处理器
   * @param {Object} errorHandler - 错误处理器对象
   */
  setErrorHandler(errorHandler) {
    this.errorHandler = errorHandler;
  }
  
  /**
   * 添加事件监听器（用于后续清理）
   * @param {string} event - 事件名称
   * @param {Function} listener - 监听器函数
   * @param {Object} target - 事件目标对象
   */
  addEventListener(event, listener, target = null) {
    this.eventListeners.push({
      event,
      listener,
      target
    });
  }
  
  /**
   * 清理所有事件监听器
   */
  clearEventListeners() {
    this.eventListeners.forEach(({ event, listener, target }) => {
      try {
        if (target && typeof target.off === 'function') {
          target.off(event, listener);
        } else if (target && typeof target.removeEventListener === 'function') {
          target.removeEventListener(event, listener);
        }
      } catch (error) {
        console.warn(`[${this.moduleName}] 清理事件监听器失败:`, error);
      }
    });
    
    this.eventListeners = [];
  }
  
  /**
   * 获取页面数据
   * @param {string} key - 数据键名
   * @returns {any}
   */
  getPageData(key) {
    if (this.destroyed) {
      console.warn(`[${this.moduleName}] 尝试在已销毁的模块上获取页面数据`);
      return null;
    }
    
    return key ? this.data[key] : this.data;
  }
  
  /**
   * 设置页面数据
   * @param {string} key - 数据键名
   * @param {any} value - 数据值
   * @param {Function} callback - 回调函数
   */
  setPageData(key, value, callback) {
    if (this.destroyed) {
      console.warn(`[${this.moduleName}] 尝试在已销毁的模块上设置页面数据`);
      return;
    }
    
    const data = {};
    data[key] = value;
    this.safeSetData(data, callback);
  }
  
  /**
   * 批量设置页面数据
   * @param {Object} data - 数据对象
   * @param {Function} callback - 回调函数
   */
  setPageDataBatch(data, callback) {
    if (this.destroyed) {
      console.warn(`[${this.moduleName}] 尝试在已销毁的模块上批量设置页面数据`);
      return;
    }
    
    this.safeSetData(data, callback);
  }
  
  /**
   * 日志记录方法
   * @param {string} level - 日志级别 (log, warn, error)
   * @param {string} message - 日志消息
   * @param {any} data - 附加数据
   */
  log(level = 'log', message, data = null) {
    const logMessage = `[${this.moduleName}] ${message}`;
    
    if (data) {
      console[level](logMessage, data);
    } else {
      console[level](logMessage);
    }
  }
  
  /**
   * 调试日志（只在调试模式下输出）
   * @param {string} message - 日志消息
   * @param {any} data - 附加数据
   */
  debug(message, data = null) {
    // 检查是否开启调试模式
    const app = getApp();
    if (app && app.globalData && app.globalData.debugMode) {
      this.log('log', `[DEBUG] ${message}`, data);
    }
  }

  // ==================== 事件发射器方法 ====================

  /**
   * 监听事件
   * @param {string} event - 事件名称
   * @param {Function} listener - 监听器函数
   */
  on(event, listener) {
    if (!this.events.has(event)) {
      this.events.set(event, []);
    }
    this.events.get(event).push(listener);
  }

  /**
   * 监听事件（只触发一次）
   * @param {string} event - 事件名称
   * @param {Function} listener - 监听器函数
   */
  once(event, listener) {
    const onceWrapper = (...args) => {
      this.off(event, onceWrapper);
      listener.apply(this, args);
    };
    this.on(event, onceWrapper);
  }

  /**
   * 移除事件监听器
   * @param {string} event - 事件名称
   * @param {Function} listener - 监听器函数
   */
  off(event, listener) {
    if (!this.events.has(event)) {
      return;
    }
    
    const listeners = this.events.get(event);
    const index = listeners.indexOf(listener);
    if (index > -1) {
      listeners.splice(index, 1);
    }
    
    // 如果没有监听器了，删除事件
    if (listeners.length === 0) {
      this.events.delete(event);
    }
  }

  /**
   * 发射事件
   * @param {string} event - 事件名称
   * @param {...any} args - 事件参数
   */
  emit(event, ...args) {
    if (!this.events.has(event)) {
      return false;
    }
    
    const listeners = this.events.get(event);
    listeners.forEach(listener => {
      try {
        listener.apply(this, args);
      } catch (error) {
        console.error(`[${this.moduleName}] Error in event listener for '${event}':`, error);
      }
    });
    
    return true;
  }

  /**
   * 移除所有事件监听器
   * @param {string} event - 事件名称（可选，如果不提供则清除所有事件）
   */
  removeAllListeners(event) {
    if (event) {
      this.events.delete(event);
    } else {
      this.events.clear();
    }
  }

  /**
   * 获取事件监听器数量
   * @param {string} event - 事件名称
   * @returns {number}
   */
  listenerCount(event) {
    if (!this.events.has(event)) {
      return 0;
    }
    return this.events.get(event).length;
  }
}

module.exports = BaseModule;