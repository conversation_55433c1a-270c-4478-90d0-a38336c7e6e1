.tab-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 750rpx;
  height: 160rpx;
  z-index: 999; /* 降低层级,让弹窗能显示在上层 */
  background-color: transparent;
  pointer-events: auto;
  opacity: 1; /* 确保完全不透明 */
  visibility: visible; /* 确保始终可见 */
  transform: translateZ(0); /* 启用硬件加速，提高渲染性能 */
}

.tab-bar-bg {
  position: absolute;
  width: 100%;
  height: calc(100% + env(safe-area-inset-bottom) + 20rpx);
  left: 0;
  bottom: -10rpx;
  z-index: 0;
  transform: scale(1.05);
}

.tab-bar-border {
  display: none;
}

.tab-bar-item {
  flex: 1;
  position: relative;
  z-index: 1;
  height: 96rpx;
}

.tab-bar-item-content {
  position: absolute;
  width: 100%;
  height: 96rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  top: 8rpx;
}

.tab-bar-item .icon {
  width: 50rpx;
  height: 50rpx;
  margin-bottom: 6rpx;
}

.tab-bar-item .text {
  font-size: 20rpx;
  line-height: 1;
}

/* Layout utilities */
.flex-col {
  display: flex;
  flex-direction: column;
}

.justify-start {
  justify-content: flex-start;
}

.self-center {
  align-self: center;
}

.self-stretch {
  align-self: stretch;
}

.relative {
  position: relative;
  width: 100%;
  height: 100%;
}

/* Navigation bar background */
.nav-bar-bg {
  width: 100%;
  height: 160rpx;
  position: absolute;
  bottom: 0;
  left: 0;
  z-index: 0;
}

/* Navigation items - 均匀分布四个导航项 */
.nav-item-1 {
  position: absolute;
  left: 93rpx; /* 第一个项目位置 */
  width: 71.99rpx;
  height: 100%;
  z-index: 2;
}

.nav-item-2 {
  position: absolute;
  left: 260rpx; /* 第二个项目位置 */
  width: 71.99rpx;
  height: 100%;
  z-index: 2;
}

.nav-item-3 {
  position: absolute;
  left: 427rpx; /* 第三个项目位置 */
  width: 71.99rpx;
  height: 100%;
  z-index: 2;
}

.nav-item-4 {
  position: absolute;
  left: 594rpx; /* 第四个项目位置 */
  width: 71.99rpx;
  height: 100%;
  z-index: 2;
}

/* Text container for all items */
.nav-item-1 .text-container,
.nav-item-2 .text-container,
.nav-item-3 .text-container,
.nav-item-4 .text-container {
  width: 120rpx;
  height: 40rpx;
  position: absolute;
  left: -24rpx;
  top: 82rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* Icon container */
.icon-container {
  width: 53.98rpx;
  height: 53.98rpx;
  background-color: transparent;
  margin-top: 16rpx;
  position: relative;
}

/* Text style */
.nav-text {
  font-size: 20rpx;
  font-family: Poppins;
  line-height: 1;
  color: #ffffff;
  text-align: center;
}

/* 添加覆盖层的样式，扩大点击区域 - 四等分 */
.tab-hit-area {
  position: absolute;
  height: 100%;
  bottom: 0;
  z-index: 1;
  background-color: transparent; /* 完全透明 */
  touch-action: none; /* 防止默认触摸行为干扰 */
}

.area-1 {
  left: 0;
  width: 25%; /* 第一个四分之一 */
}

.area-2 {
  left: 25%;
  width: 25%; /* 第二个四分之一 */
}

.area-3 {
  left: 50%;
  width: 25%; /* 第三个四分之一 */
}

.area-4 {
  left: 75%;
  width: 25%; /* 第四个四分之一 */
}

/* Navigation items - 提高图标层级，确保图标显示在透明覆盖层之上 */
.nav-item-1, .nav-item-2, .nav-item-3, .nav-item-4 {
  z-index: 3; /* 比覆盖层z-index更高，确保图标仍然可见 */
} 