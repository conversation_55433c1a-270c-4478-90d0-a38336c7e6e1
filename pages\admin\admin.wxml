<view class="admin-container">
  <view class="admin-header">
    <view class="back-icon" bindtap="navigateBack">
      <view class="arrow-left"></view>
    </view>
    <view class="header-title">管理控制台</view>
  </view>
  
  <scroll-view class="admin-menu" scroll-y="true">
    <!-- 分类菜单 -->
    <view class="menu-categories">
      <block wx:for="{{menuCategories}}" wx:key="id" wx:for-index="categoryIndex">
        <!-- 分类标题 -->
        <view class="category-header {{item.isOpen ? 'active' : ''}}" 
              bindtap="toggleCategory" 
              data-index="{{categoryIndex}}">
          <view class="category-name">{{item.name}}</view>
          <view class="category-arrow {{item.isOpen ? 'open' : ''}}"></view>
        </view>
        
        <!-- 分类内容 -->
        <view class="category-content category-{{categoryIndex}} {{item.isOpen ? 'open' : ''}}" 
              style="{{item.isOpen ? 'height: auto; opacity: 1;' : 'height: 0; opacity: 0;'}}">
          <view class="menu-items">
            <block wx:for="{{item.items}}" wx:key="id" wx:for-item="menuItem">
              <view class="menu-item" bindtap="navigateToFunction" data-url="{{menuItem.url}}">
                <view class="menu-name">{{menuItem.name}}</view>
              </view>
            </block>
          </view>
        </view>
      </block>
    </view>
  </scroll-view>
  
  <view class="admin-footer">
    <view class="exit-btn" bindtap="exitAdmin">退出管理员模式</view>
  </view>
</view> 