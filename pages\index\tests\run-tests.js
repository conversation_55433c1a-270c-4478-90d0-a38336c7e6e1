/**
 * 测试运行脚本
 * 提供便捷的测试运行和报告生成功能
 */

const { execSync } = require('child_process');
const path = require('path');

// 测试配置
const testConfig = {
  configPath: path.join(__dirname, 'jest.config.js'),
  testDir: __dirname,
  coverageDir: path.join(__dirname, 'coverage')
};

/**
 * 运行所有测试
 */
function runAllTests() {
  console.log('🚀 开始运行所有测试...\n');
  
  try {
    const command = `npx jest --config=${testConfig.configPath} --testPathPattern=${testConfig.testDir}`;
    execSync(command, { stdio: 'inherit' });
    
    console.log('\n✅ 所有测试运行完成！');
  } catch (error) {
    console.error('\n❌ 测试运行失败:', error.message);
    process.exit(1);
  }
}

/**
 * 运行单元测试
 */
function runUnitTests() {
  console.log('🧪 开始运行单元测试...\n');
  
  try {
    const command = `npx jest --config=${testConfig.configPath} --testPathPattern="(?!performance)"`;
    execSync(command, { stdio: 'inherit' });
    
    console.log('\n✅ 单元测试运行完成！');
  } catch (error) {
    console.error('\n❌ 单元测试运行失败:', error.message);
    process.exit(1);
  }
}

/**
 * 运行性能测试
 */
function runPerformanceTests() {
  console.log('⚡ 开始运行性能测试...\n');
  
  try {
    const command = `npx jest --config=${testConfig.configPath} --testPathPattern=performance.test.js`;
    execSync(command, { stdio: 'inherit' });
    
    console.log('\n✅ 性能测试运行完成！');
  } catch (error) {
    console.error('\n❌ 性能测试运行失败:', error.message);
    process.exit(1);
  }
}

/**
 * 生成覆盖率报告
 */
function generateCoverageReport() {
  console.log('📊 生成覆盖率报告...\n');
  
  try {
    const command = `npx jest --config=${testConfig.configPath} --coverage --coverageDirectory=${testConfig.coverageDir}`;
    execSync(command, { stdio: 'inherit' });
    
    console.log('\n✅ 覆盖率报告生成完成！');
    console.log(`📁 报告位置: ${testConfig.coverageDir}/index.html`);
  } catch (error) {
    console.error('\n❌ 覆盖率报告生成失败:', error.message);
    process.exit(1);
  }
}

/**
 * 监听模式运行测试
 */
function runTestsInWatchMode() {
  console.log('👀 启动测试监听模式...\n');
  
  try {
    const command = `npx jest --config=${testConfig.configPath} --watch --testPathPattern=${testConfig.testDir}`;
    execSync(command, { stdio: 'inherit' });
  } catch (error) {
    console.error('\n❌ 监听模式启动失败:', error.message);
    process.exit(1);
  }
}

/**
 * 运行核心模块测试
 */
function runCoreTests() {
  console.log('🏗️ 开始运行核心模块测试...\n');
  
  try {
    const command = `npx jest --config=${testConfig.configPath} --testPathPattern="(main-controller|api-compatibility|unified-error-handler)"`;
    execSync(command, { stdio: 'inherit' });
    
    console.log('\n✅ 核心模块测试运行完成！');
  } catch (error) {
    console.error('\n❌ 核心模块测试运行失败:', error.message);
    process.exit(1);
  }
}

/**
 * 运行功能模块测试
 */
function runModuleTests() {
  console.log('🔧 开始运行功能模块测试...\n');
  
  try {
    const command = `npx jest --config=${testConfig.configPath} --testPathPattern="(video-list|video-player|search|share|navigation|ui-state|scroll-performance)"`;
    execSync(command, { stdio: 'inherit' });
    
    console.log('\n✅ 功能模块测试运行完成！');
  } catch (error) {
    console.error('\n❌ 功能模块测试运行失败:', error.message);
    process.exit(1);
  }
}

/**
 * 运行集成测试
 */
function runIntegrationTests() {
  console.log('🔗 开始运行集成测试...\n');
  
  try {
    const command = `npx jest --config=${testConfig.configPath} --testPathPattern="integration"`;
    execSync(command, { stdio: 'inherit' });
    
    console.log('\n✅ 集成测试运行完成！');
  } catch (error) {
    console.error('\n❌ 集成测试运行失败:', error.message);
    process.exit(1);
  }
}

/**
 * 运行兼容性测试
 */
function runCompatibilityTests() {
  console.log('🔄 开始运行兼容性测试...\n');
  
  try {
    const command = `npx jest --config=${testConfig.configPath} --testPathPattern="compatibility"`;
    execSync(command, { stdio: 'inherit' });
    
    console.log('\n✅ 兼容性测试运行完成！');
  } catch (error) {
    console.error('\n❌ 兼容性测试运行失败:', error.message);
    process.exit(1);
  }
}

/**
 * 运行特定测试文件
 */
function runSpecificTest(filename) {
  console.log(`🎯 开始运行特定测试: ${filename}...\n`);
  
  try {
    const command = `npx jest --config=${testConfig.configPath} --testPathPattern="${filename}"`;
    execSync(command, { stdio: 'inherit' });
    
    console.log(`\n✅ 测试 ${filename} 运行完成！`);
  } catch (error) {
    console.error(`\n❌ 测试 ${filename} 运行失败:`, error.message);
    process.exit(1);
  }
}

/**
 * 清理测试缓存
 */
function clearTestCache() {
  console.log('🧹 清理测试缓存...\n');
  
  try {
    const command = 'npx jest --clearCache';
    execSync(command, { stdio: 'inherit' });
    
    console.log('\n✅ 测试缓存清理完成！');
  } catch (error) {
    console.error('\n❌ 缓存清理失败:', error.message);
    process.exit(1);
  }
}

/**
 * 显示帮助信息
 */
function showHelp() {
  console.log(`
📋 主页面重构模块测试运行器

用法: node run-tests.js [命令] [参数]

命令:
  all           运行所有测试 (默认)
  unit          只运行单元测试
  perf          只运行性能测试
  integration   运行集成测试
  compatibility 运行兼容性测试
  core          运行核心模块测试 (主控制器、API兼容性、错误处理)
  modules       运行功能模块测试 (视频、搜索、分享等)
  coverage      生成覆盖率报告
  watch         监听模式运行测试
  file          运行特定测试文件
  clear         清理测试缓存
  help          显示此帮助信息

示例:
  node run-tests.js all
  node run-tests.js unit
  node run-tests.js core
  node run-tests.js modules
  node run-tests.js integration
  node run-tests.js compatibility
  node run-tests.js coverage
  node run-tests.js file main-controller
  `);
}

// 解析命令行参数
const command = process.argv[2] || 'all';
const parameter = process.argv[3];

switch (command) {
  case 'all':
    runAllTests();
    break;
  case 'unit':
    runUnitTests();
    break;
  case 'perf':
  case 'performance':
    runPerformanceTests();
    break;
  case 'core':
    runCoreTests();
    break;
  case 'modules':
    runModuleTests();
    break;
  case 'integration':
    runIntegrationTests();
    break;
  case 'compatibility':
    runCompatibilityTests();
    break;
  case 'coverage':
    generateCoverageReport();
    break;
  case 'watch':
    runTestsInWatchMode();
    break;
  case 'file':
    if (parameter) {
      runSpecificTest(parameter);
    } else {
      console.error('❌ 请指定要运行的测试文件名');
      console.log('示例: node run-tests.js file main-controller');
      process.exit(1);
    }
    break;
  case 'clear':
    clearTestCache();
    break;
  case 'help':
  case '--help':
  case '-h':
    showHelp();
    break;
  default:
    console.error(`❌ 未知命令: ${command}`);
    showHelp();
    process.exit(1);
}