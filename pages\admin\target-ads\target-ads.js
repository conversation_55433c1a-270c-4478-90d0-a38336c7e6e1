const app = getApp()
const targetAdUtils = require('../../../utils/target-ad-utils')

Page({
  data: {
    // 广告配置
    config: {
      imageUrl: '',
      jumpUrl: '',
      triggerTime: 30,
      enabled: true
    },

    // 触发时间选项
    triggerTimeOptions: [],
    triggerTimeIndex: 0,

    // 上传状态
    uploading: false,

    // 保存状态
    saving: false,

    // 内容选择相关
    showContentModal: false,
    contentType: 'none', // 默认为无跳转，也可以选择 'video'、'gallery'、'recharge'
    contentList: [],
    loadingContent: false,
    selectedContent: {
      id: 'none',
      title: '仅展示广告图片',
      type: 'none',
      jumpUrl: ''
    }
  },

  onLoad() {
    // 检查管理员登录状态
    if (!app.globalData.isAdmin) {
      this.redirectToLogin()
      return
    }

    // 初始化触发时间选项
    const options = targetAdUtils.getTriggerTimeOptions()
    this.setData({
      triggerTimeOptions: options.map(item => item.label)
    })

    // 加载当前配置
    this.loadCurrentConfig()
  },

  // 重定向到登录页
  redirectToLogin() {
    wx.redirectTo({
      url: '/pages/admin/login'
    })
  },

  // 返回上一页
  navigateBack() {
    wx.navigateBack({
      delta: 1
    })
  },

  // 加载当前配置
  loadCurrentConfig() {
    wx.showLoading({
      title: '加载配置中...'
    })
    
    wx.cloud.callFunction({
      name: 'targetAdManager',
      data: {
        action: 'getConfig'
      }
    }).then(res => {
      wx.hideLoading()
      
      if (res.result && res.result.success) {
        if (res.result.data) {
          const config = res.result.data
          
          // 找到对应的触发时间索引
          const options = targetAdUtils.getTriggerTimeOptions()
          const timeIndex = options.findIndex(item => item.value === config.triggerTime)
          
          this.setData({
            config: {
              imageUrl: config.imageUrl || '',
              jumpUrl: config.jumpUrl || '',
              triggerTime: config.triggerTime || 30,
              enabled: config.enabled !== false
            },
            triggerTimeIndex: timeIndex >= 0 ? timeIndex : 0
          })

          // 解析已选择的内容（添加错误处理）
          try {
            this.parseSelectedContent(config.jumpUrl)
          } catch (parseError) {
            // 静默处理解析错误，不影响页面正常使用
            if (app.globalData && app.globalData.debugMode) {
              console.error('解析选中内容失败:', parseError)
            }
          }

          if (app.globalData && app.globalData.debugMode) {
            console.log('加载配置成功:', config)
          }
        } else {
          if (app.globalData && app.globalData.debugMode) {
            console.log('暂无配置，使用默认值')
          }
        }
      } else {
        console.error('加载配置失败:', res.result)
        wx.showToast({
          title: '加载配置失败',
          icon: 'none'
        })
      }
    }).catch(err => {
      wx.hideLoading()
      console.error('加载配置出错:', err)
      wx.showToast({
        title: '加载配置出错',
        icon: 'none'
      })
    })
  },

  // 选择图片
  chooseImage() {
    if (this.data.uploading) {
      return
    }
    
    wx.chooseMedia({
      count: 1,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      maxDuration: 30,
      camera: 'back',
      success: (res) => {
        const tempFilePath = res.tempFiles[0].tempFilePath
        this.uploadImage(tempFilePath)
      },
      fail: (err) => {
        console.error('选择图片失败:', err)
        wx.showToast({
          title: '选择图片失败',
          icon: 'none'
        })
      }
    })
  },

  // 上传图片
  uploadImage(filePath) {
    this.setData({
      uploading: true
    })
    
    wx.showLoading({
      title: '上传中...'
    })
    
    // 生成文件名
    const timestamp = Date.now()
    const random = Math.floor(Math.random() * 1000)
    const fileName = `target-ads/${timestamp}_${random}.jpg`
    
    wx.cloud.uploadFile({
      cloudPath: fileName,
      filePath: filePath,
      success: (res) => {
        console.log('图片上传成功:', res)
        
        this.setData({
          'config.imageUrl': res.fileID,
          uploading: false
        })
        
        wx.hideLoading()
        wx.showToast({
          title: '上传成功',
          icon: 'success'
        })
      },
      fail: (err) => {
        console.error('图片上传失败:', err)
        
        this.setData({
          uploading: false
        })
        
        wx.hideLoading()
        wx.showToast({
          title: '上传失败',
          icon: 'none'
        })
      }
    })
  },

  // 显示内容选择器
  showContentSelector() {
    this.setData({
      showContentModal: true
    })

    // 加载内容列表
    this.loadContentList()
  },

  // 隐藏内容选择器
  hideContentSelector() {
    this.setData({
      showContentModal: false
    })
  },

  // 切换内容类型
  switchContentType(e) {
    const type = e.currentTarget.dataset.type
    this.setData({
      contentType: type
    })

    // 重新加载内容列表
    this.loadContentList()
  },

  // 加载内容列表
  loadContentList() {
    this.setData({
      loadingContent: true,
      contentList: []
    })

    const { contentType } = this.data

    if (contentType === 'none') {
      // 无跳转类型，显示一个特殊的选项
      this.setData({
        loadingContent: false,
        contentList: [{
          id: 'none',
          title: '仅展示广告图片',
          subtitle: '点击后不跳转到任何页面',
          coverUrl: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiByeD0iOCIgZmlsbD0iIzMzMzMzMyIvPgo8dGV4dCB4PSIyMCIgeT0iMjQiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iI2ZmZmZmZiIgdGV4dC1hbmNob3I9Im1pZGRsZSI+5peg6Lez6L2sPC90ZXh0Pgo8L3N2Zz4K', // 使用base64编码的SVG图标
          jumpUrl: '',
          type: 'none'
        }]
      })
    } else if (contentType === 'service') {
      this.loadServiceList()
    } else if (contentType === 'video') {
      this.loadVideoList()
    } else if (contentType === 'gallery') {
      this.loadGalleryList()
    } else if (contentType === 'recharge') {
      this.loadRechargeList()
    }
  },

  // 加载视频列表
  loadVideoList() {
    console.log('开始加载视频列表')

    wx.cloud.callFunction({
      name: 'videoManager',
      data: {
        type: 'admin',
        action: 'getAllVideos',
        data: {
          page: 1,
          pageSize: 50
        }
      }
    }).then(res => {
      console.log('视频列表响应:', res)

      this.setData({
        loadingContent: false
      })

      if (res.result && res.result.code === 200) {
        const videos = res.result.data.videos || []
        console.log('获取到的视频数据:', videos)

        const contentList = videos.filter(item => item.isVisible !== false).map((item, index) => ({
          id: item._id,
          title: item.mainTitle || item.title || '无标题',
          subtitle: item.subTitle || item.subtitle || '',
          coverUrl: item.coverUrl || '/static/logo.png',
          type: 'video',
          position: index + 1, // 添加位置信息
          jumpUrl: `/pages/index/index?videoPosition=${index + 1}&videoTitle=${encodeURIComponent(item.mainTitle || item.title || '无标题')}`
        }))

        this.setData({
          contentList: contentList
        })

        console.log('处理后的视频列表:', contentList)
      } else {
        console.error('加载视频列表失败:', res.result)
        // 如果没有数据，显示空列表而不是错误
        this.setData({
          contentList: []
        })
      }
    }).catch(err => {
      this.setData({
        loadingContent: false,
        contentList: []
      })
      console.error('加载视频列表出错:', err)
      wx.showToast({
        title: '加载视频出错',
        icon: 'none'
      })
    })
  },

  // 加载画廊列表
  loadGalleryList() {
    console.log('开始加载画廊列表')

    wx.cloud.callFunction({
      name: 'galleryManager',
      data: {
        action: 'getGalleryList',
        data: {
          page: 1,
          pageSize: 50
        }
      }
    }).then(res => {
      console.log('画廊列表响应:', res)

      this.setData({
        loadingContent: false
      })

      if (res.result && res.result.success) {
        const galleryData = res.result.data || {}
        console.log('获取到的画廊数据:', galleryData)

        // 画廊数据结构是 {list: Array, page: 1, pageSize: 50, total: 3}
        const galleries = galleryData.list || []

        const contentList = galleries.filter(item => item.isVisible !== false).map((item, index) => ({
          id: item._id,
          title: item.mainTitle || item.title || '无标题',
          subtitle: item.subTitle || item.subtitle || '',
          coverUrl: item.coverUrl || '/static/logo.png',
          type: 'gallery',
          position: index + 1, // 添加位置信息
          jumpUrl: `/pages/gallery/gallery?galleryPosition=${index + 1}&galleryTitle=${encodeURIComponent(item.mainTitle || item.title || '无标题')}`
        }))

        this.setData({
          contentList: contentList
        })

        console.log('处理后的画廊列表:', contentList)
      } else {
        console.error('加载画廊列表失败:', res.result)
        // 如果没有数据，显示空列表而不是错误
        this.setData({
          contentList: []
        })
      }
    }).catch(err => {
      this.setData({
        loadingContent: false,
        contentList: []
      })
      console.error('加载画廊列表出错:', err)
      wx.showToast({
        title: '加载画廊出错',
        icon: 'none'
      })
    })
  },

  // 加载充值列表
  loadRechargeList() {
    console.log('开始加载充值方案列表')

    wx.cloud.callFunction({
      name: 'rechargeManager',
      data: {
        type: 'admin',
        action: 'getRechargePlans',
        data: {}
      }
    }).then(res => {
      console.log('充值方案列表响应:', res)

      this.setData({
        loadingContent: false
      })

      if (res.result && (res.result.code === 0 || res.result.code === 200 || res.result.success)) {
        const plans = res.result.data || []
        console.log('获取到的充值方案数据:', plans)

        const contentList = plans.map((item, index) => ({
          id: item._id,
          title: `充值${item.amount}元`,
          subtitle: item.bonus ? `赠送${item.bonus}元，共${item.amount + item.bonus}元` : `充值${item.amount}元`,
          coverUrl: item.image || '/static/logo.png', // 使用充值方案的图片
          type: 'recharge',
          position: index + 1, // 添加位置信息
          jumpUrl: `/pages/my/my?rechargePosition=${index + 1}&rechargeAmount=${item.amount}`
        }))

        this.setData({
          contentList: contentList
        })

        console.log('处理后的充值方案列表:', contentList)
      } else {
        console.error('加载充值方案失败:', res.result)
        // 如果没有数据，显示空列表而不是错误
        this.setData({
          contentList: []
        })
      }
    }).catch(err => {
      this.setData({
        loadingContent: false
      })
      console.error('加载充值方案出错:', err)
      wx.showToast({
        title: '加载充值方案出错',
        icon: 'none'
      })
    })
  },

  // 加载服务列表
  loadServiceList() {
    console.log('开始加载服务列表')

    wx.cloud.callFunction({
      name: 'appointmentManager',
      data: {
        action: 'getServices',
        type: 'user' // 只获取可见的服务
      },
      success: res => {
        this.setData({
          loadingContent: false
        })

        if (res.result && res.result.success) {
          const services = res.result.data || []
          console.log('服务列表加载成功，数量:', services.length)

          // 转换服务数据格式
          const contentList = services.map(service => ({
            id: service._id || service.id,
            title: service.name,
            subtitle: service.description,
            coverUrl: service.image || '/static/default-service.png',
            jumpUrl: `/pages/appointment/appointment?serviceId=${service._id || service.id}&showModal=true`,
            type: 'service'
          }))

          this.setData({
            contentList: contentList
          })
        } else {
          console.error('加载服务列表失败:', res.result)
          wx.showToast({
            title: '加载服务列表失败',
            icon: 'none'
          })
        }
      },
      fail: err => {
        this.setData({
          loadingContent: false
        })
        console.error('加载服务列表出错:', err)
        wx.showToast({
          title: '加载服务列表出错',
          icon: 'none'
        })
      }
    })
  },

  // 选择内容
  selectContent(e) {
    const item = e.currentTarget.dataset.item

    // 处理无跳转的情况
    if (item.id === 'none') {
      this.setData({
        selectedContent: {
          id: 'none',
          title: '仅展示广告图片',
          type: 'none',
          jumpUrl: ''
        },
        'config.jumpUrl': '', // 设置为空字符串表示无跳转
        showContentModal: false
      })
    } else {
      this.setData({
        selectedContent: {
          id: item.id,
          title: item.title,
          type: item.type,
          jumpUrl: item.jumpUrl
        },
        'config.jumpUrl': item.jumpUrl,
        showContentModal: false
      })
    }

    if (app.globalData && app.globalData.debugMode) {
      console.log('选择的内容:', item)
    }
  },

  // 解析已选择的内容（从jumpUrl中解析）
  parseSelectedContent(jumpUrl) {
    if (!jumpUrl) {
      // 如果jumpUrl为空，设置为无跳转状态
      this.setData({
        selectedContent: {
          id: 'none',
          title: '仅展示广告图片',
          type: 'none',
          jumpUrl: ''
        }
      })
      return
    }

    // 解析视频内容
    if (jumpUrl.includes('/pages/index/index?videoPosition=')) {
      const urlParams = this.parseUrlParams(jumpUrl.split('?')[1])
      const position = urlParams.videoPosition
      const title = decodeURIComponent(urlParams.videoTitle || '视频内容')

      this.setData({
        selectedContent: {
          id: `video-${position}`,
          title: `第${position}个视频: ${title}`,
          type: 'video',
          jumpUrl: jumpUrl
        }
      })
    }
    // 兼容旧的videoId格式
    else if (jumpUrl.includes('/pages/index/index?videoId=')) {
      const videoId = jumpUrl.split('videoId=')[1]
      this.setData({
        selectedContent: {
          id: videoId,
          title: '视频内容（ID: ' + videoId + '）',
          type: 'video',
          jumpUrl: jumpUrl
        }
      })
    }
    // 解析画廊内容
    else if (jumpUrl.includes('/pages/gallery/gallery?galleryPosition=')) {
      const urlParams = this.parseUrlParams(jumpUrl.split('?')[1])
      const position = urlParams.galleryPosition
      const title = decodeURIComponent(urlParams.galleryTitle || '画廊内容')

      this.setData({
        selectedContent: {
          id: `gallery-${position}`,
          title: `第${position}个画廊: ${title}`,
          type: 'gallery',
          jumpUrl: jumpUrl
        }
      })
    }
    // 兼容旧的articleId格式
    else if (jumpUrl.includes('/pages/gallery/gallery?articleId=')) {
      const articleId = jumpUrl.split('articleId=')[1]
      this.setData({
        selectedContent: {
          id: articleId,
          title: '画廊内容（ID: ' + articleId + '）',
          type: 'gallery',
          jumpUrl: jumpUrl
        }
      })
    }
    // 解析充值内容
    else if (jumpUrl.includes('/pages/my/my?rechargePosition=')) {
      const urlParams = this.parseUrlParams(jumpUrl.split('?')[1])
      const position = urlParams.rechargePosition
      const amount = urlParams.rechargeAmount

      this.setData({
        selectedContent: {
          id: `recharge-${position}`,
          title: `第${position}个充值方案: ${amount}元`,
          type: 'recharge',
          jumpUrl: jumpUrl
        }
      })
    }
    // 兼容旧的充值格式
    else if (jumpUrl.includes('/pages/my/points/points?rechargePosition=')) {
      const urlParams = this.parseUrlParams(jumpUrl.split('?')[1])
      const position = urlParams.rechargePosition
      const amount = urlParams.rechargeAmount

      this.setData({
        selectedContent: {
          id: `recharge-${position}`,
          title: `第${position}个充值方案: ${amount}元`,
          type: 'recharge',
          jumpUrl: jumpUrl
        }
      })
    }
    // 兼容旧的planId格式
    else if (jumpUrl.includes('/pages/my/points/points?planId=')) {
      const planId = jumpUrl.split('planId=')[1]
      this.setData({
        selectedContent: {
          id: planId,
          title: '充值优惠（ID: ' + planId + '）',
          type: 'recharge',
          jumpUrl: jumpUrl
        }
      })
    }
    // 解析服务内容
    else if (jumpUrl.includes('/pages/appointment/appointment?serviceId=')) {
      const urlParams = this.parseUrlParams(jumpUrl.split('?')[1])
      const serviceId = urlParams.serviceId

      // 尝试从服务列表中找到对应的服务名称
      this.loadServiceForParsing(serviceId, jumpUrl)
    }
    // 其他类型的跳转地址
    else {
      this.setData({
        selectedContent: {
          id: '',
          title: jumpUrl,
          type: 'other',
          jumpUrl: jumpUrl
        }
      })
    }
  },

  // 触发时间选择
  onTriggerTimeChange(e) {
    const index = parseInt(e.detail.value)
    const options = targetAdUtils.getTriggerTimeOptions()
    
    this.setData({
      triggerTimeIndex: index,
      'config.triggerTime': options[index].value
    })
  },

  // 启用状态切换
  onEnabledChange(e) {
    this.setData({
      'config.enabled': e.detail.value
    })
  },

  // 保存配置
  saveConfig() {
    const { config } = this.data
    
    // 验证必要字段
    if (!config.imageUrl) {
      wx.showToast({
        title: '请上传广告图片',
        icon: 'none'
      })
      return
    }

    // 注意：jumpUrl可以为空，表示无跳转状态
    // 不再验证jumpUrl是否为空，允许"仅展示广告图片"的情况
    
    this.setData({
      saving: true
    })
    
    wx.showLoading({
      title: '保存中...'
    })
    
    wx.cloud.callFunction({
      name: 'targetAdManager',
      data: {
        action: 'updateConfig',
        imageUrl: config.imageUrl,
        jumpUrl: config.jumpUrl,
        triggerTime: config.triggerTime,
        enabled: config.enabled
      }
    }).then(res => {
      wx.hideLoading()
      this.setData({
        saving: false
      })
      
      if (res.result && res.result.success) {
        wx.showToast({
          title: '保存成功',
          icon: 'success'
        })
        
        console.log('配置保存成功:', res.result.data)
      } else {
        console.error('保存配置失败:', res.result)
        wx.showToast({
          title: res.result.message || '保存失败',
          icon: 'none'
        })
      }
    }).catch(err => {
      wx.hideLoading()
      this.setData({
        saving: false
      })
      
      console.error('保存配置出错:', err)
      wx.showToast({
        title: '保存出错',
        icon: 'none'
      })
    })
  },

  // 加载服务信息用于解析
  loadServiceForParsing(serviceId, jumpUrl) {
    wx.cloud.callFunction({
      name: 'appointmentManager',
      data: {
        action: 'getServices',
        type: 'user'
      },
      success: res => {
        if (res.result && res.result.success) {
          const services = res.result.data || []
          const service = services.find(s => (s._id === serviceId || s.id === serviceId))

          if (service) {
            this.setData({
              selectedContent: {
                id: serviceId,
                title: `服务预约: ${service.name}`,
                type: 'service',
                jumpUrl: jumpUrl
              }
            })
          } else {
            // 如果找不到服务，显示默认信息
            this.setData({
              selectedContent: {
                id: serviceId,
                title: '服务预约（ID: ' + serviceId + '）',
                type: 'service',
                jumpUrl: jumpUrl
              }
            })
          }
        } else {
          // 加载失败时显示默认信息
          this.setData({
            selectedContent: {
              id: serviceId,
              title: '服务预约（ID: ' + serviceId + '）',
              type: 'service',
              jumpUrl: jumpUrl
            }
          })
        }
      },
      fail: err => {
        console.error('加载服务信息失败:', err)
        // 加载失败时显示默认信息
        this.setData({
          selectedContent: {
            id: serviceId,
            title: '服务预约（ID: ' + serviceId + '）',
            type: 'service',
            jumpUrl: jumpUrl
          }
        })
      }
    })
  },

  // 解析URL参数的辅助函数（兼容小程序环境）
  parseUrlParams(queryString) {
    if (!queryString) {
      return {}
    }

    const params = {}
    const pairs = queryString.split('&')

    for (let i = 0; i < pairs.length; i++) {
      const pair = pairs[i].split('=')
      if (pair.length === 2) {
        params[pair[0]] = pair[1]
      }
    }

    return params
  }
})
