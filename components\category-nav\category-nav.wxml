<view class="category-nav">
  <!-- 状态栏 -->
  <view class="status-bar" style="height: {{statusBarHeight}}px"></view>
  
  <!-- 导航栏主体 - 调整样式使其与胶囊按钮底部对齐 -->
  <view class="navigation-bar" style="height: {{navigationBarHeight + extraBottomPadding}}px; margin-top: {{navBarTop}}px;">
    <!-- 分类标签容器 - 限制宽度 -->
    <scroll-view 
      class="category-scroll" 
      scroll-x 
      enable-flex
      scroll-with-animation
      show-scrollbar="{{false}}"
      enhanced="{{true}}"
      style="width: {{safeAreaWidth}}; max-width: {{safeAreaWidth}}; padding-bottom: {{extraBottomPadding}}px; position: relative;"
    >
      <view class="category-list">
        <view 
          wx:for="{{categories}}" 
          wx:key="id" 
          class="category-item {{currentCategoryId === item.id ? 'active' : ''}}"
          bindtap="onCategoryTap"
          data-id="{{item.id}}"
        >
          <text class="category-text">{{item.name}}</text>
        </view>
      </view>
    </scroll-view>
  </view>
</view>

<!-- 占位元素，使内容不被导航栏遮挡 -->
<view class="nav-placeholder" style="height: {{statusBarHeight + navigationBarHeight + navBarTop + extraBottomPadding}}px"></view> 