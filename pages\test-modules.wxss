/* 模块测试页面样式 */
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 40rpx;
  padding: 30rpx;
  background: white;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.subtitle {
  display: block;
  font-size: 28rpx;
  color: #666;
}

.status-section {
  margin-bottom: 30rpx;
}

.status-card {
  background: white;
  padding: 30rpx;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
  text-align: center;
}

.status-text {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
}

.button-section {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  margin-bottom: 40rpx;
}

.test-button {
  background: #007aff;
  color: white;
  border-radius: 12rpx;
  font-size: 32rpx;
  padding: 20rpx;
}

.status-button {
  background: #34c759;
  color: white;
  border-radius: 12rpx;
  font-size: 30rpx;
  padding: 18rpx;
}

.clear-button {
  background: #ff3b30;
  color: white;
  border-radius: 8rpx;
  font-size: 26rpx;
  margin-top: 10rpx;
}

.results-section {
  background: white;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.results-header {
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 2rpx solid #eee;
}

.results-title {
  font-size: 34rpx;
  font-weight: bold;
  color: #333;
}

.result-item {
  margin-bottom: 30rpx;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
  border-left: 6rpx solid #ddd;
}

.result-item:last-child {
  margin-bottom: 0;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}

.result-name {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
}

.result-status {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.result-status.pass {
  color: #34c759;
}

.result-status.fail {
  color: #ff3b30;
}

.status-icon {
  font-size: 24rpx;
}

.result-message {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}

.info-section {
  background: white;
  padding: 30rpx;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.info-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.info-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  white-space: pre-line;
}