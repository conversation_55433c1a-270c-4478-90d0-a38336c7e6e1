// 员工订单页面
const app = getApp();

Page({
  data: {
    staffInfo: null,
    orderList: [],
    isLoading: true,
    isEmpty: false,
    currentTab: 0, // 默认显示已完成
    tabNames: ['已完成'], // 只保留已完成选项卡
    paymentMethod: 'cash', // 默认显示现金支付记录
    pageTitle: '现金核销记录' // 默认标题
  },

  onLoad: function(options) {
    // 检查员工登录状态
    const staffInfo = wx.getStorageSync('staffInfo');
    if (!staffInfo) {
      this.redirectToLogin();
      return;
    }

    // 获取支付方式参数
    const paymentMethod = options.paymentMethod || 'cash';
    const pageTitle = paymentMethod === 'cash' ? '现金核销记录' : '余额核销记录';

    this.setData({ 
      staffInfo,
      paymentMethod,
      pageTitle
    });
    
    this.fetchVerifiedAppointments();
  },

  onShow: function() {
    // 每次显示页面时重新获取订单
    if (this.data.staffInfo) {
      this.fetchVerifiedAppointments();
    }
  },

  // 获取员工核销过的预约记录
  fetchVerifiedAppointments: function() {
    this.setData({ isLoading: true });
    
    console.log('开始获取员工核销记录，员工ID:', this.data.staffInfo._id, '支付方式:', this.data.paymentMethod);

    wx.cloud.callFunction({
      name: 'staffManager', // 使用staffManager云函数
      data: {
        type: 'staff',
        action: 'getStaffVerifiedAppointments', // 获取核销记录的action
        data: {
          staffId: this.data.staffInfo._id,
          status: this.getStatusFilter(),
          paymentMethod: this.data.paymentMethod
        }
      },
      success: res => {
        console.log('核销记录列表返回结果:', res);
        
        if (res.result && res.result.code === 0) {
          const appointmentList = res.result.data || [];
          console.log('获取到的核销记录数量:', appointmentList.length);
          
          // 确保按核销时间降序排序（最新的在前面）
          const sortedAppointments = appointmentList.sort((a, b) => {
            // 如果verifyTime是字符串格式，先转换为时间戳再比较
            // 修复iOS日期格式问题
            const timeA = a.verifyTime ? (typeof a.verifyTime === 'string' ? this.parseDate(a.verifyTime).getTime() : a.verifyTime) : 0;
            const timeB = b.verifyTime ? (typeof b.verifyTime === 'string' ? this.parseDate(b.verifyTime).getTime() : b.verifyTime) : 0;
            // 降序排序，最新的在前面
            return timeB - timeA;
          });
          
          // 格式化预约数据
          const formattedAppointments = sortedAppointments.map(appointment => {
            return {
              ...appointment,
              formattedDate: appointment.date,
              appointmentTime: appointment.time,
              statusText: this.getOrderStatusText(appointment.status),
              // 确保敏感信息不显示完整
              userPhone: appointment.phoneNumber ? this.maskPhoneNumber(appointment.phoneNumber) : '未提供',
              // 添加支付方式显示
              paymentMethodText: appointment.paymentMethod === 'balance' ? '余额支付' : '现金支付'
            };
          });
          
          this.setData({
            orderList: formattedAppointments,
            isEmpty: formattedAppointments.length === 0,
            isLoading: false
          });
        } else {
          console.error('获取核销记录失败:', res.result);
          this.setData({
            orderList: [],
            isEmpty: true,
            isLoading: false
          });
          
          wx.showToast({
            title: res.result?.message || '获取核销记录失败',
            icon: 'none'
          });
        }
      },
      fail: err => {
        console.error('调用云函数获取核销记录失败', err);
        this.setData({
          orderList: [],
          isEmpty: true,
          isLoading: false
        });
        
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      }
    });
  },

  // 解析日期字符串为兼容iOS的格式
  parseDate: function(dateString) {
    // 如果已经是标准格式，直接返回
    if (dateString.includes('T') || dateString.includes('/')) {
      return new Date(dateString);
    }
    
    // 将 "yyyy-MM-dd HH:mm" 或 "yyyy-MM-dd HH:mm:ss" 转换为 "yyyy-MM-ddTHH:mm:ss"
    const parts = dateString.split(' ');
    if (parts.length === 2) {
      const datePart = parts[0];
      const timePart = parts[1] + (parts[1].split(':').length === 2 ? ':00' : '');
      return new Date(datePart + 'T' + timePart);
    }
    
    // 如果无法解析，尝试其他兼容格式
    return new Date(dateString.replace(' ', 'T'));
  },

  // 获取订单状态文本
  getOrderStatusText: function(status) {
    const statusMap = {
      'pending': '待确认',
      'confirmed': '已确认',
      'completed': '已完成',
      'cancelled': '已取消'
    };
    return statusMap[status] || '未知状态';
  },

  // 获取状态过滤条件
  getStatusFilter: function() {
    // 始终返回"已完成"状态
    return 'completed';
  },

  // 切换选项卡 - 保留函数但不再需要实际切换
  switchTab: function(e) {
    // 由于只有一个选项卡，此函数可以保留但不需要实际切换
    // 如果将来需要添加其他选项卡，可以重新启用此功能
  },

  // 查看订单详情
  viewOrderDetail: function(e) {
    const appointmentId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/staff/order_detail/order_detail?id=${appointmentId}`
    });
  },

  // 返回员工首页
  goBack: function() {
    wx.navigateBack();
  },

  // 重定向到登录页
  redirectToLogin: function() {
    wx.redirectTo({
      url: '/pages/staff/login/login'
    });
  },

  // 下拉刷新
  onPullDownRefresh: function() {
    this.fetchVerifiedAppointments();
    wx.stopPullDownRefresh();
  },
  
  // 手机号码脱敏处理
  maskPhoneNumber: function(phone) {
    if (!phone || phone.length < 7) return '***';
    return phone.substr(0, 3) + '****' + phone.substr(-4);
  }
}); 