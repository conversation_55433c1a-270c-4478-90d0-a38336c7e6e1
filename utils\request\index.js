/**
 * 统一网络请求管理
 * 所有组件必须通过此模块发起网络请求
 */

// ================== 网络请求管理 (请勿修改) ==================
// @feature: 统一网络请求
// @version: 1.0.0
// @warning: 以下代码已完成网络请求管理功能，请勿修改

const { ENV_ID, REQUEST_CONFIG } = require('../../config/index');

// 请求基础配置
const BASE_CONFIG = REQUEST_CONFIG;

/**
 * 发起GET请求
 * @param {String} url 请求地址
 * @param {Object} data 请求参数
 * @param {Object} options 配置项
 */
const get = (url, data = {}, options = {}) => {
  return request('GET', url, data, options);
};

/**
 * 发起POST请求
 * @param {String} url 请求地址
 * @param {Object} data 请求参数
 * @param {Object} options 配置项
 */
const post = (url, data = {}, options = {}) => {
  return request('POST', url, data, options);
};

/**
 * 统一请求方法
 * @param {String} method 请求方法
 * @param {String} url 请求地址
 * @param {Object} data 请求参数
 * @param {Object} options 配置项
 */
const request = (method, url, data, options) => {
  const config = Object.assign({}, BASE_CONFIG, options);
  
  return new Promise((resolve, reject) => {
    wx.request({
      url,
      data,
      method,
      header: config.header,
      timeout: config.timeout,
      success: (res) => {
        if (res.statusCode >= 200 && res.statusCode < 300) {
          resolve(res.data);
        } else {
          // 处理错误状态码
          reject({
            code: res.statusCode,
            message: res.data.message || '请求失败',
            data: res.data
          });
        }
      },
      fail: (err) => {
        reject({
          code: -1,
          message: err.errMsg || '网络请求失败',
          error: err
        });
      }
    });
  });
};

/**
 * 云函数调用方法
 * @param {String} name 云函数名称
 * @param {Object} data 请求参数
 */
const callCloudFunction = (name, data = {}) => {
  return new Promise((resolve, reject) => {
    wx.cloud.callFunction({
      name,
      data,
      success: res => {
        resolve(res.result);
      },
      fail: err => {
        reject(err);
      }
    });
  });
};

module.exports = {
  get,
  post,
  request,
  callCloudFunction,
  ENV_ID
};

// ================== 在此线以下添加新功能 ================== 