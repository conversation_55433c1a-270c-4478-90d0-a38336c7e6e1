<!-- ================== 首页结构 (请勿修改) ================== -->
<!-- @feature: 首页基础结构 -->
<!-- @version: 1.0.0 -->
<!-- @warning: 以下代码已完成首页基础功能，请勿修改 -->

<view class="container" 
      catchtouchmove="catchHorizontalMove"
      bindtouchstart="catchHorizontalMove"
      bindtouchend="catchHorizontalMove"
      bindtouchcancel="catchHorizontalMove">
  <!-- 固定的渐变背景层，始终显示 -->
  <view class="fixed-background"></view>
  
  <!-- 移除首次加载动画，直接显示内容 -->
  
  <!-- 导航栏组件 -->
  <custom-nav id="customNav" title="首页" forceHidden="{{forceHideNavbar}}"></custom-nav>
  
  <!-- 顶部区域容器 -->
  <view class="header-container" style="opacity: {{showContent ? 1 : 0}}; transition: opacity 0.3s ease;">
    <!-- 下拉刷新提示，与画廊页面保持一致 -->
    <view class="refresh-tip" wx:if="{{isRefreshing}}">
      <view class="refresh-icon"></view>
      <text>正在刷新...</text>
    </view>
  </view>
  
  <!-- 滚动区域 - 修改显示逻辑，允许在搜索时显示结果 -->
  <scroll-view 
    class="main-scroll {{showContent ? 'show-content' : ''}}" 
    scroll-y="{{true}}" 
    bindscroll="handleScroll" 
    refresher-enabled="{{true}}" 
    refresher-triggered="{{isRefreshing}}" 
    refresher-threshold="80"
    refresher-background="#000000"
    refresher-default-style="dark"
    bindrefresherrefresh="onPullDownRefresh"
    bindscrolltolower="onReachBottom"
    enhanced="{{true}}"
    bounces="{{true}}"
    show-scrollbar="{{false}}"
    enable-passive>

    <!-- 视频列表 -->
    <view class="video-list {{showContent ? 'slide-in' : ''}} {{isSearching ? 'search-active' : ''}}">
      <!-- 添加一个空白占位视图，确保第一个视频不紧贴搜索栏 -->
      <view class="top-spacing {{isSearching ? 'hidden' : ''}}"></view>
      
      <!-- 搜索状态下额外的顶部间距，确保第一个结果完整显示 -->
      <view wx:if="{{isSearching}}" class="search-top-spacing"></view>
      
      <!-- 骨架屏：当没有数据时显示 -->
      <block wx:if="{{!videoList || videoList.length === 0}}">
        <view wx:for="{{[1,2,3,4,5]}}" wx:key="*this" class="skeleton-item">
          <view class="skeleton-video-card">
            <!-- 视频封面骨架 -->
            <view class="skeleton-cover"></view>
            <!-- 视频信息骨架 -->
            <view class="skeleton-info">
              <view class="skeleton-title-line skeleton-title-long"></view>
              <view class="skeleton-title-line skeleton-title-short"></view>
              <view class="skeleton-meta">
                <view class="skeleton-avatar"></view>
                <view class="skeleton-text skeleton-author"></view>
                <view class="skeleton-text skeleton-views"></view>
              </view>
            </view>
          </view>
        </view>
      </block>

      <!-- 真实视频列表 -->
      <block wx:if="{{videoList && videoList.length > 0}}">
        <view wx:for="{{videoList}}" wx:for-index="idx" wx:key="idx" class="video-item {{isSearching ? 'search-result' : ''}} {{isRefreshing ? 'loading-content-placeholder' : ''}}">
          <video-card
            id="video-card-{{item.id}}"
            videoId="{{item.id}}"
            mainTitle="{{item.mainTitle}}"
            subTitle="{{item.subTitle}}"
            coverUrl="{{item.coverUrl}}"
            videoUrl="{{item.videoUrl}}"
            playCount="{{item.playCount}}"
            authorAvatar="{{item.authorAvatar || '/static/logo.png'}}"
            isMuted="{{true}}"
            videoInfo="{{item}}"
            bind:videoPlay="onVideoPlay"
            bind:videoPause="onVideoPause"
            bind:videoEnd="onVideoEnd"
            bind:authorTap="onAuthorTap"
            bind:share="onShareButtonTap"
            bind:muteChange="onMuteChange"
            bind:videotap="onVideoTap"
            bind:detail="onVideoDetail"
            data-video="{{item}}"
          />
        </view>
      </block>
      
      <!-- 移除"暂无视频"空状态，避免闪现问题 -->
      <!-- 保留搜索无结果提示，因为这是用户主动搜索的结果 -->
      <view wx:if="{{!loading && videoList.length === 0 && searchKeyword && !searchFocused}}" class="empty-container">
        <text class="empty-text">未找到"{{searchKeyword}}"相关视频</text>
      </view>
      
      <!-- 底部空白占位，确保可以滚动到底部内容 -->
      <view class="bottom-spacing">
        <!-- 当还有更多数据时显示加载更多提示 -->
        <view wx:if="{{hasMore && videoList.length > 0}}" class="more-tip">
          -- 上拉加载更多 --
        </view>
        <!-- 添加适当的底部空白区域 -->
        <view style="height: 20rpx;"></view>
      </view>
    </view>
    
    <!-- 加载中提示 -->
    <view wx:if="{{loading && !videoList.length}}" class="loading">
      <!-- 使用与gallery页面一致的加载动画 -->
      <view class="loading-inline">
        <view class="loading-spinner"></view>
        <text class="loading-text">加载中...</text>
      </view>
    </view>
  </scroll-view>
</view>

<!-- ================== 在此线以下添加新功能 ================== -->

<!-- 搜索导航栏 -->
<search-nav
  bind:search="onSearchTap"
  bind:input="onSearchInput"
  bind:confirm="onSearchConfirm"
  bind:clear="clearSearch"
  bind:searchfocus="onSearchFocus"
  bind:resultTap="onSearchResultTap"
  placeholder="搜索"
  videoList="{{originalVideoList || videoList}}"
  class="{{isRefreshing ? 'loading-content-placeholder' : ''}}"
/>

<!-- 视频详情弹窗 -->
<video-detail-modal 
  id="videoDetailModal" 
  visible="{{showVideoDetail}}"
  videoInfo="{{selectedVideoDetail}}"
  bindclose="onVideoDetailClose"
  bindnavbarControl="onNavbarControl"
  bindfullscreenChange="onVideoFullscreenChange"
/>

<!-- 悬浮客服按钮 -->
<floating-customer-service
  iconUrl="/images/客服图标.png"
  title="欢迎咨询"
  path="pages/index/index"
  imageUrl="/images/客服图标.png"
/>

<!-- 指向广告弹窗 -->
<target-ad-modal
  show="{{showTargetAd}}"
  imageUrl="{{targetAdData.imageUrl}}"
  jumpUrl="{{targetAdData.jumpUrl}}"
  bind:close="onTargetAdClose"
/>
