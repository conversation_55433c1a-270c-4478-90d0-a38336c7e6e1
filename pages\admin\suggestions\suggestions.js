// pages/admin/suggestions/suggestions.js
Page({
  data: {
    users: [], // 用户列表
    suggestions: [], // 原始建议数据（兼容旧接口）
    loading: false, // 是否正在加载
    currentPage: 1, // 当前页码
    pageSize: 20, // 每页数量
    hasMore: true, // 是否还有更多数据
    totalCount: 0, // 总数量
    
    // 筛选条件
    filterStatus: '', // 筛选状态：pending, replied, closed
    statusOptions: [
      { value: '', label: '全部' },
      { value: 'pending', label: '待处理' },
      { value: 'user_replied', label: '用户已回复' },
      { value: 'replied', label: '已回复' },
      { value: 'closed', label: '已关闭' }
    ],
    
    // 用户选择
    selectedUser: null,
    selectedUserId: null,
    conversations: [], // 当前选中用户的对话
    
    // 回复相关
    replyContent: '',
    
    // 刷新相关
    refreshing: false,
    
    // 状态映射
    statusMap: {
      'pending': 1, 
      'user_replied': 2, 
      'replied': 3, 
      'closed': 4
    },
    
    // 屏幕宽度，用于响应式布局
    screenWidth: 375,
    
    // 滚动控制
    scrollToMessage: '',

    // 键盘相关
    keyboardHeight: 0,
    inputFocused: false,
    
    // 胶囊按钮相关
    capsuleHeight: 0,
    capsuleTop: 0,
    statusBarHeight: 0,
    headerPadding: 0,
    safeAreaBottom: 0 // 底部安全区域高度
  },

  onLoad() {
    // 初始化缓存对象，用于提升性能
    this._cache = {
      systemInfo: null,
      lastScrollId: ''
    };

    // 获取系统信息
    const systemInfo = wx.getSystemInfoSync();
    this._cache.systemInfo = systemInfo;
    
    // 获取胶囊按钮位置信息
    const menuButtonInfo = wx.getMenuButtonBoundingClientRect();
    
    // 计算胶囊下方的安全距离
    const statusBarHeight = systemInfo.statusBarHeight;
    const capsuleHeight = menuButtonInfo.height;
    const capsuleTop = menuButtonInfo.top;
    const capsuleBottom = menuButtonInfo.top + menuButtonInfo.height;
    
    // 安全距离 = 胶囊底部位置 - 状态栏高度 + 额外间距
    const headerPadding = capsuleBottom - statusBarHeight + 8;
    
    // 获取底部安全区域高度
    const safeAreaBottom = systemInfo.safeArea ? 
      systemInfo.screenHeight - systemInfo.safeArea.bottom : 0;
    
    this.setData({
      screenWidth: systemInfo.windowWidth,
      statusBarHeight: statusBarHeight,
      capsuleHeight: capsuleHeight,
      capsuleTop: capsuleTop,
      headerPadding: headerPadding,
      safeAreaBottom: safeAreaBottom // 保存底部安全区域高度
    });

    // 动态设置主内容的margin-top
    // 88rpx是导航栏高度，headerPadding已经包含了胶囊底部位置
    const navHeight = 88 / 2; // 转换为px
    const mainContentMarginTop = headerPadding + navHeight;
    
    // 设置主内容的margin-top
    wx.nextTick(() => {
      this.setData({
        mainContentMarginTop: mainContentMarginTop + 'px'
      });
    });
    
    // 加载用户列表
    this.loadUserList();
  },

  /**
   * 返回上一页
   */
  goBack() {
    // 如果当前正在查看用户对话，则返回到用户列表
    if (this.data.selectedUser) {
      this.backToUserList();
    } else {
      // 否则返回上一页
      wx.navigateBack({
        delta: 1
      });
    }
  },

  /**
   * 重置表单状态 - 提取为独立函数以提高复用性和性能
   */
  resetInputState() {
    // 重置输入相关状态
    this._lastReplyHadContent = false;
    
    // 只在需要时进行setData
    if (this.data.replyContent || this.data.keyboardHeight > 0 || this.data.inputFocused) {
      this.setData({
        replyContent: '',
        keyboardHeight: 0,
        inputFocused: false
      });
    }
  },

  onShow() {
    // 页面显示时检查一次键盘状态，确保恢复到正确状态
    if (this.data.keyboardHeight > 0 && !this.data.inputFocused) {
      this.setData({
        keyboardHeight: 0
      });
    }
    
    // 每次显示页面时刷新数据
    this.refreshData();
  },

  /**
   * 加载用户列表 (由原loadSuggestions改造)
   */
  loadUserList(refresh = false) {
    if (this.data.loading) return;
    
    if (refresh) {
      this.setData({
        currentPage: 1,
        users: [],
        suggestions: [],
        hasMore: true
      });
    }
    
    this.setData({ loading: true });
    
    // 调用云函数获取投诉建议数据
    wx.cloud.callFunction({
      name: 'suggestionManager',
      data: {
        action: 'getAllSuggestions',
        data: {
          status: this.data.filterStatus,
          page: this.data.currentPage,
          pageSize: this.data.pageSize,
          groupByUser: true // 新增参数，告诉后端按用户分组
        }
      },
      success: (res) => {
        if (res.result && res.result.code === 0) {
          const { data, total, page } = res.result;
          
          // 处理旧接口返回数据兼容性
          // 这里我们尝试按用户ID分组，如果后端未实现groupByUser
          let userMap = new Map();
          
          data.forEach(item => {
            // 用户标识
            const userId = item.userId || item.openid || item._id;
            
            // 基本信息
            const userInfo = {
              userId: userId,
              nickName: item.userInfo?.nickName || '用户',
              avatarUrl: item.userInfo?.avatarUrl,
              isAnonymous: item.isAnonymous || false,
              contactPhone: item.contactPhone,
              status: item.status,
              lastMessage: this.getLastMessage(item.conversations),
              lastTime: this.formatTime(item.updateTime || item.createTime),
              firstTime: this.formatTime(item.createTime),
              unreadCount: this.getUnreadCount(item.conversations),
              suggestionId: item._id
            };
            
            // 如果此用户已存在，只在最近的消息更新
            if (userMap.has(userId)) {
              const existingUser = userMap.get(userId);
              
              // 更新最近消息和时间（如果更新时间更晚）
              if (new Date(item.updateTime) > new Date(existingUser.updateTime)) {
                existingUser.lastMessage = userInfo.lastMessage;
                existingUser.lastTime = userInfo.lastTime;
                existingUser.status = userInfo.status;
              }
              
              // 合并未读消息数
              existingUser.unreadCount += userInfo.unreadCount;
              
              // 添加建议ID到建议列表
              existingUser.suggestionIds = existingUser.suggestionIds || [];
              existingUser.suggestionIds.push(item._id);
            } else {
              // 新用户，初始化
              userInfo.suggestionIds = [item._id];
              userInfo.updateTime = item.updateTime; // 保存原始updateTime用于比较
              userMap.set(userId, userInfo);
            }
          });
          
          // 转换为数组并按更新时间排序
          let userList = Array.from(userMap.values()).sort((a, b) => {
            // 优先显示有未读消息的用户
            if (a.unreadCount > 0 && b.unreadCount === 0) return -1;
            if (a.unreadCount === 0 && b.unreadCount > 0) return 1;
            
            // 然后按最后更新时间排序
            return new Date(b.lastTime) - new Date(a.lastTime);
          });
          
          this.setData({
            users: refresh ? userList : [...this.data.users, ...userList],
            suggestions: refresh ? data : [...this.data.suggestions, ...data], // 保存原始数据
            totalCount: userMap.size, // 更新为用户数量
            currentPage: page,
            hasMore: data.length === this.data.pageSize,
            loading: false
          });
        } else {
          wx.showToast({
            title: res.result?.message || '加载失败',
            icon: 'none'
          });
          this.setData({ loading: false });
        }
      },
      fail: (err) => {
        console.error('加载数据失败:', err);
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
        this.setData({ loading: false });
      }
    });
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    this.setData({ refreshing: true });
    this.loadUserList(true);
    setTimeout(() => {
      this.setData({ refreshing: false });
    }, 1000);
  },

  /**
   * 上拉加载更多
   */
  onReachBottom() {
    if (this.data.hasMore && !this.data.loading) {
      this.setData({ 
        currentPage: this.data.currentPage + 1 
      });
      this.loadUserList();
    }
  },

  /**
   * 刷新数据
   */
  refreshData() {
    this.loadUserList(true);
  },

  /**
   * 筛选状态变化
   */
  onStatusFilterChange(e) {
    const index = e.detail.value;
    this.setData({
      filterStatus: this.data.statusOptions[index].value,
      selectedUser: null,
      selectedUserId: null
    });
    this.loadUserList(true);
  },

  /**
   * 选择用户
   */
  selectUser(e) {
    const user = e.currentTarget.dataset.user;
    
    this.setData({
      selectedUser: user,
      selectedUserId: user.userId,
      replyContent: '',
      conversations: []
    });
    
    // 加载该用户的所有对话
    this.loadUserConversations(user);
  },
  
  /**
   * 返回用户列表（在小屏幕上使用）
   */
  backToUserList() {
    // 重置状态
    this.resetInputState();
    
    // 返回到用户列表视图
    this.setData({
      selectedUser: null,
      selectedUserId: null,
      conversations: [], // 清空对话列表，减少内存占用
      scrollToMessage: '' // 重置滚动状态
    });
  },
  
  /**
   * 加载用户的所有对话
   */
  loadUserConversations(user) {
    // 先设置loading状态
    this.setData({
      loading: true
    });
    
    wx.showLoading({
      title: '加载对话...',
      mask: true
    });
    
    // 调用新的云函数方法获取特定用户的所有对话
    wx.cloud.callFunction({
      name: 'suggestionManager',
      data: {
        action: 'getUserConversations',
        data: {
          userId: user.userId,
          suggestionIds: user.suggestionIds // 如果有具体ID列表则传入
        }
      },
      success: (res) => {
        wx.hideLoading();
        
        if (res.result && res.result.code === 0) {
          let allConversations = [];
          const suggestions = res.result.data || [];
          
          // 合并所有建议的对话
          suggestions.forEach(suggestion => {
            if (suggestion.conversations && suggestion.conversations.length) {
              suggestion.conversations.forEach(conv => {
                // 添加建议ID和创建时间
                conv.suggestionId = suggestion._id;
                conv.originalTime = conv.createTime;
                
                // 格式化时间
                conv.createTimeStr = this.formatTime(conv.createTime);
                
                // 添加到总列表
                allConversations.push(conv);
              });
            }
          });
          
          // 按时间排序
          allConversations.sort((a, b) => new Date(a.createTime) - new Date(b.createTime));
          
          // 添加时间分割线标记
          if (allConversations.length > 0) {
            allConversations[0].showTimeHeader = true;
            
            for (let i = 1; i < allConversations.length; i++) {
              // 如果与前一条消息相差超过5分钟，显示时间分割线
              const prevTime = new Date(allConversations[i-1].createTime);
              const currTime = new Date(allConversations[i].createTime);
              
              if ((currTime - prevTime) > 5 * 60 * 1000) { // 5分钟
                allConversations[i].showTimeHeader = true;
              }
            }
          }
          
          // 更新对话列表
          this.setData({
            conversations: allConversations,
            loading: false // 加载完成
          });
          
          // 滚动到最后一条消息
          if (allConversations.length > 0) {
            const lastMessage = allConversations[allConversations.length - 1];
            this.setData({
              scrollToMessage: `msg-${lastMessage.id}`
            });
          }
          
          // 标记此用户的所有消息为已读
          if (user.unreadCount > 0) {
            this.markUserMessagesRead(user);
          }
        } else {
          this.setData({
            loading: false,
            conversations: [] // 清空对话列表
          });
          
          console.error('获取对话失败:', res.result?.message);
          wx.showToast({
            title: res.result?.message || '加载对话失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        wx.hideLoading();
        console.error('加载用户对话失败:', err);
        
        this.setData({
          loading: false,
          conversations: [] // 清空对话列表
        });
        
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      }
    });
  },
  
  /**
   * 标记用户消息为已读
   */
  markUserMessagesRead(user) {
    // 调用云函数标记已读
    wx.cloud.callFunction({
      name: 'suggestionManager',
      data: {
        action: 'markUserMessagesRead',
        data: {
          userId: user.userId,
          suggestionIds: user.suggestionIds
        }
      },
      success: (res) => {
        if (res.result && res.result.code === 0) {
          // 更新本地用户列表的未读数
          const userIndex = this.data.users.findIndex(u => u.userId === user.userId);
          if (userIndex !== -1) {
            this.setData({
              [`users[${userIndex}].unreadCount`]: 0
            });
          }
        }
      },
      fail: (err) => {
        console.error('标记消息已读失败:', err);
      }
    });
  },

  /**
   * 回复内容输入
   */
  onReplyInput(e) {
    // 直接获取值，避免频繁setData
    this.data.replyContent = e.detail.value;
    
    // 使用节流方式更新按钮状态，只更新必要的数据
    if (e.detail.value && !this._lastReplyHadContent) {
      this._lastReplyHadContent = true;
      this.setData({
        'replyContent': e.detail.value
      });
    } else if (!e.detail.value && this._lastReplyHadContent) {
      this._lastReplyHadContent = false;
      this.setData({
        'replyContent': ''
      });
    }
  },

  /**
   * 监听键盘高度变化
   */
  onKeyboardHeightChange(e) {
    const height = e.detail.height;
    const keyboardHeight = height > 0 ? height : 0;
    
    // 只有当键盘高度发生实质变化时才更新
    if (Math.abs(this.data.keyboardHeight - keyboardHeight) > 10) {
      this.setData({
        keyboardHeight: keyboardHeight
      });
      
      // 如果键盘弹出
      if (keyboardHeight > 0) {
        // 确保聊天内容可见，滚动到最新消息
        setTimeout(() => {
          // 减少不必要的DOM操作，直接滚动到最新消息
          if (this.data.conversations && this.data.conversations.length > 0) {
            const lastMessage = this.data.conversations[this.data.conversations.length - 1];
            this.setData({
              scrollToMessage: `msg-${lastMessage.id}`
            });
          }
        }, 100); // 短延迟确保CSS转换已应用
      }
    }
  },
  
  /**
   * 处理输入框获得焦点
   */
  onInputFocus(e) {
    if (this.data.inputFocused) return; // 避免重复处理
    
    // 更新输入框焦点状态
    this.setData({
      inputFocused: true
    });
    
    // 获取系统信息和键盘高度 - 使用缓存避免重复获取
    if (!this._systemInfo) {
      this._systemInfo = wx.getSystemInfoSync();
    }
    
    // 预估键盘高度并触发相应处理
    const estimatedKeyboardHeight = this._systemInfo.windowHeight * 0.4;
    if (estimatedKeyboardHeight > 0 && Math.abs(this.data.keyboardHeight - estimatedKeyboardHeight) > 10) {
      this.setData({
        keyboardHeight: estimatedKeyboardHeight
      });
      
      // 滚动到最新消息 - 延迟较短以提高响应速度
      setTimeout(() => {
        if (this.data.conversations && this.data.conversations.length > 0) {
          const lastMessage = this.data.conversations[this.data.conversations.length - 1];
          this.setData({
            scrollToMessage: `msg-${lastMessage.id}`
          });
        }
      }, 50);
    }
  },
  
  /**
   * 处理输入框失去焦点
   */
  onInputBlur() {
    // 获取当前输入内容，更新到data中
    const currentContent = this.data.replyContent;
    
    // 不要立即设置inputFocused为false，而是延迟处理，避免视觉跳跃
    this.setData({
      inputFocused: false
    });
    
    // 延迟设置键盘高度为0，以避免视觉抖动
    // 通过RAF (requestAnimationFrame)模拟，微信小程序没有原生RAF，用setTimeout替代
    setTimeout(() => {
      // 保存最后一条消息的ID，用于后续滚动
      let lastMessageId = '';
      if (this.data.conversations && this.data.conversations.length > 0) {
        const lastMessage = this.data.conversations[this.data.conversations.length - 1];
        lastMessageId = `msg-${lastMessage.id}`;
      }
      
      this.setData({
        keyboardHeight: 0,
        // 一次性更新所有需要更新的数据，减少setData调用次数
        scrollToMessage: lastMessageId || ''
      });
    }, 150); // 缩短延时，提升响应速度
  },
  
  /**
   * 处理消息列表获得焦点
   */
  onMessagesFocus() {
    // 确保键盘收起时，输入框仍然可见
    if (this.data.keyboardHeight > 0) {
      this.setData({
        keyboardHeight: 0,
        inputFocused: false
      });
    }
  },

  /**
   * 发送回复
   */
  sendReply() {
    // 使用缓存的replyContent，避免多余的data访问
    const replyContent = this.data.replyContent;
    const selectedUser = this.data.selectedUser;
    
    if (!replyContent || !replyContent.trim()) {
      wx.showToast({
        title: '请输入回复内容',
        icon: 'none'
      });
      return;
    }
    
    if (!selectedUser) {
      wx.showToast({
        title: '请先选择用户',
        icon: 'none'
      });
      return;
    }
    
    // 优化：先清空输入框，提高响应速度
    this.setData({
      replyContent: ''
    });
    this._lastReplyHadContent = false; // 重置状态
    
    wx.showLoading({
      title: '发送中...',
      mask: true
    });
    
    // 调用云函数发送回复
    wx.cloud.callFunction({
      name: 'suggestionManager',
      data: {
        action: 'adminReply',
        data: {
          suggestionId: selectedUser.suggestionIds[0], // 回复最新的建议
          content: replyContent.trim(),
          adminInfo: {
            name: '管理员'
          }
        }
      },
      success: (res) => {
        wx.hideLoading();
        
        if (res.result && res.result.code === 0) {
          // 重新加载对话
          this.loadUserConversations(selectedUser);
          
          // 更新用户状态
          const userIndex = this.data.users.findIndex(u => u.userId === selectedUser.userId);
          if (userIndex !== -1) {
            this.setData({
              [`users[${userIndex}].status`]: 'replied',
              [`selectedUser.status`]: 'replied'
            });
          }
        } else {
          wx.showToast({
            title: res.result?.message || '回复失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        wx.hideLoading();
        console.error('回复失败:', err);
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 预览图片
   */
  previewImage(e) {
    const { url, urls } = e.currentTarget.dataset;
    wx.previewImage({
      current: url,
      urls: urls || [url]
    });
  },

  /**
   * 拨打电话
   */
  makePhoneCall(e) {
    const phone = e.currentTarget.dataset.phone;
    if (!phone) return;
    
    wx.makePhoneCall({
      phoneNumber: phone
    });
  },

  /**
   * 格式化时间
   */
  formatTime(time) {
    if (!time) return '';
    
    const date = new Date(time);
    const now = new Date();
    
    // 今天的消息只显示时间
    if (date.toDateString() === now.toDateString()) {
      return this.formatTimeOnly(date);
    }
    
    // 昨天的消息显示"昨天 + 时间"
    const yesterday = new Date(now);
    yesterday.setDate(now.getDate() - 1);
    if (date.toDateString() === yesterday.toDateString()) {
      return `昨天 ${this.formatTimeOnly(date)}`;
    }
    
    // 一周内的消息显示"星期几 + 时间"
    const weekDays = ['日', '一', '二', '三', '四', '五', '六'];
    const dayDiff = Math.floor((now - date) / (24 * 60 * 60 * 1000));
    if (dayDiff < 7) {
      return `星期${weekDays[date.getDay()]} ${this.formatTimeOnly(date)}`;
    }
    
    // 其他时间显示"年-月-日 时间"
    return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')} ${this.formatTimeOnly(date)}`;
  },
  
  /**
   * 只格式化时间部分
   */
  formatTimeOnly(date) {
    return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
  },

  /**
   * 获取状态文本
   */
  getStatusText(status) {
    const statusMap = {
      'pending': '待处理',
      'user_replied': '用户已回复',
      'replied': '已回复',
      'closed': '已关闭'
    };
    return statusMap[status] || '未知状态';
  },

  /**
   * 获取最后消息内容
   */
  getLastMessage(conversations) {
    if (!conversations || conversations.length === 0) {
      return '暂无消息';
    }
    
    const lastMessage = conversations[conversations.length - 1];
    
    // 如果有图片，显示[图片]
    if (lastMessage.images && lastMessage.images.length > 0) {
      return '[图片]'; // 用户列表中统一显示[图片]
    }
    
    // 如果有视频，显示[视频]
    if (lastMessage.videos && lastMessage.videos.length > 0) {
      return '[视频]';
    }
    
    return lastMessage.content || '暂无内容';
  },

  /**
   * 获取未读消息数
   * 统计从后往前直到管理员回复为止的用户消息数量
   */
  getUnreadCount(conversations) {
    if (!conversations || conversations.length === 0) {
      return 0;
    }
    
    let unreadCount = 0;
    for (let i = conversations.length - 1; i >= 0; i--) {
      if (conversations[i].sender === 'user' && !conversations[i].isRead) {
        unreadCount++;
      } else if (conversations[i].sender === 'admin') {
        break; // 遇到管理员回复就停止计数
      }
    }
    
    return unreadCount;
  },

  /**
   * 选择媒体文件（图片）
   */
  chooseMedia() {
    const { selectedUser } = this.data;
    
    // 先检查是否选择了用户
    if (!selectedUser || !selectedUser.userId) {
      wx.showToast({
        title: '请先选择用户',
        icon: 'none'
      });
      return;
    }

    wx.chooseImage({
      count: 1, // 默认9，设置为1表示一次只能选择一张图片
      sizeType: ['original', 'compressed'], // 可以指定是原图还是压缩图，默认二者都有
      sourceType: ['album', 'camera'], // 可以指定来源是相册还是相机，默认二者都有
      success: (res) => {
        // 返回选定照片的本地文件路径列表，tempFilePath可以作为img标签的src属性显示图片
        const tempFilePaths = res.tempFilePaths;
        
        if (tempFilePaths && tempFilePaths.length > 0) {
          // 显示加载
          wx.showLoading({
            title: '正在发送...',
            mask: true
          });
          
          console.log('选择图片成功，准备上传: ', tempFilePaths[0]);
          
          // 上传图片并发送
          this.sendMediaMessage(tempFilePaths[0]);
        }
      },
      fail: (err) => {
        console.error('选择图片失败:', err);
      }
    });
  },

  /**
   * 上传并发送媒体消息
   * @param {string} filePath - 媒体文件的临时路径
   */
  sendMediaMessage(filePath) {
    // 获取当前选中的用户
    const { selectedUser } = this.data;
    
    if (!selectedUser) {
      wx.hideLoading();
      wx.showToast({
        title: '请先选择用户',
        icon: 'none'
      });
      return;
    }
    
    // 获取文件扩展名
    const ext = filePath.substring(filePath.lastIndexOf('.') + 1);
    
    // 上传到云存储
    const cloudPath = `admin_media/${Date.now()}-${Math.random().toString(36).substring(2)}.${ext}`;
    
    wx.cloud.uploadFile({
      cloudPath: cloudPath,
      filePath: filePath,
      success: (res) => {
        // 上传成功后发送消息
        const fileID = res.fileID;
        
        // 获取最近的一个suggestionId
        const suggestionId = selectedUser.suggestionIds && selectedUser.suggestionIds.length > 0 
          ? selectedUser.suggestionIds[0] 
          : null;
        
        // 准备请求数据
        const requestData = {
          userId: selectedUser.userId, // 添加用户ID
          content: " ", // 空格而非空字符串
          images: [fileID],
          adminInfo: {
            name: '管理员'
          }
        };
        
        // 如果有suggestionId则添加
        if (suggestionId) {
          requestData.suggestionId = suggestionId;
        }
          
        console.log('发送图片请求数据:', requestData);
        
        // 使用标准adminReply格式发送
        wx.cloud.callFunction({
          name: 'suggestionManager',
          data: {
            action: 'adminReply',
            data: requestData
          },
          success: (res) => {
            wx.hideLoading();
            
            console.log('云函数返回结果:', res);
            
            if (res.result && res.result.code === 0) {
              console.log('图片发送成功:', res.result);
              
              // 重新加载对话
              this.loadUserConversations(selectedUser);
              
              // 更新用户状态
              const userIndex = this.data.users.findIndex(u => u.userId === selectedUser.userId);
              if (userIndex !== -1) {
                this.setData({
                  [`users[${userIndex}].status`]: 'replied',
                  [`selectedUser.status`]: 'replied'
                });
              }
            } else {
              console.error('发送图片失败, 返回结果:', res);
              wx.showToast({
                title: res.result?.message || '发送失败',
                icon: 'none'
              });
            }
          },
          fail: (err) => {
            wx.hideLoading();
            console.error('发送媒体失败:', err);
            wx.showToast({
              title: '网络错误，请重试',
              icon: 'none'
            });
          }
        });
      },
      fail: (err) => {
        wx.hideLoading();
        console.error('上传图片失败:', err);
        wx.showToast({
          title: '上传图片失败',
          icon: 'none'
        });
      }
    });
  }
});