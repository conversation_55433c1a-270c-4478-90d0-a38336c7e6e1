const app = getApp()

Page({
  data: {
    adminInfo: null,
    // 定义四个分类
    menuCategories: [
      {
        id: 'display',
        name: '前端展示',
        isOpen: true, // 默认展开第一个分类
        items: [
          {
            id: 'content',
            name: '广告页管理（我的页面）',
            icon: '/static/icons/content.png',
            url: '/pages/admin/launch/launch'
          },
          {
            id: 'carousel',
            name: '轮播图管理（服务页面）',
            icon: '/static/icons/content.png',
            url: '/pages/admin/carousel/carousel'
          },
          {
            id: 'service',
            name: '展示管理（视频管理）',
            icon: '/static/icons/service.png',
            url: '/pages/admin/service/service'
          },
          {
            id: 'gallery',
            name: '主页管理（画廊管理）',
            icon: '/static/icons/service.png',
            url: '/pages/admin/gallery/gallery'
          },
          {
            id: 'targetAds',
            name: '指向广告管理',
            icon: '/static/icons/content.png',
            url: '/pages/admin/target-ads/target-ads'
          }
        ]
      },
      {
        id: 'data',
        name: '前端数据管理',
        isOpen: false,
        items: [
          {
            id: 'serviceItems',
            name: '服务管理',
            icon: '/static/icons/service.png',
            url: '/pages/admin/serviceItems/serviceItems'
          },
          {
            id: 'points',
            name: '积分管理',
            icon: '/static/icons/积分图标.png',
            url: '/pages/admin/points/points'
          },
          {
            id: 'suggestions',
            name: '建议管理',
            icon: '/static/管理图标.png',
            url: '/pages/admin/suggestions/suggestions'
          },
          {
            id: 'recharge',
            name: '充值管理',
            icon: '/static/icons/recharge.png',
            url: '/pages/admin/recharge/recharge'
          },
          {
            id: 'appointment',
            name: '预约管理',
            icon: '/static/icons/appointment.png',
            url: '/pages/admin/appointment/appointment'
          }
        ]
      },
      {
        id: 'business',
        name: '经营管理',
        isOpen: false,
        items: [
          {
            id: 'businessManage',
            name: '经营管理',
            icon: '/static/营业图标.png',
            url: '/pages/admin/business/business'
          },
          {
            id: 'expense',
            name: '支出管理',
            icon: '/static/icons/expense.png',
            url: '/pages/admin/expense/expense'
          },
          {
            id: 'staff',
            name: '员工管理',
            icon: '/static/icons/user.png',
            url: '/pages/admin/staff/staff'
          }
        ]
      },
      {
        id: 'system',
        name: '系统',
        isOpen: false,
        items: [
          {
            id: 'settings',
            name: '系统设置',
            icon: '/static/刷新图标.png',
            url: '/pages/admin/systemSettings/index'
          }
        ]
      }
    ],
    // 存储每个分类内容的高度
    categoryHeights: [0, 0, 0, 0]
  },

  onLoad() {
    // 检查管理员登录状态
    if (!app.globalData.isAdmin) {
      this.redirectToLogin()
      return
    }
    
    // 获取管理员信息
    this.setData({
      adminInfo: app.globalData.adminInfo || {
        username: '管理员'
      }
    })
    
    // 延迟计算各个分类内容的高度
    setTimeout(() => {
      this.calculateCategoryHeights()
    }, 100)
  },
  
  onShow() {
    // 每次显示页面时检查管理员状态
    if (!app.globalData.isAdmin) {
      this.redirectToLogin()
    }
  },
  
  // 计算各个分类内容的高度
  calculateCategoryHeights() {
    const query = wx.createSelectorQuery()
    const heights = []
    
    // 查询每个分类内容的高度
    this.data.menuCategories.forEach((_, index) => {
      query.select(`.category-${index} .menu-items`).boundingClientRect()
    })
    
    query.exec((res) => {
      if (res && res[0]) {
        res.forEach((rect, index) => {
          if (rect) {
            heights[index] = rect.height + 'px'
          } else {
            heights[index] = 'auto'
          }
        })
        
        this.setData({
          categoryHeights: heights
        })
      }
    })
  },
  
  // 重定向到登录页
  redirectToLogin() {
    wx.showToast({
      title: '请先登录',
      icon: 'none'
    })
    
    setTimeout(() => {
      wx.redirectTo({
        url: '/pages/admin/login'
      })
    }, 1500)
  },
  
  // 切换分类折叠状态
  toggleCategory(e) {
    const { index } = e.currentTarget.dataset
    const categories = this.data.menuCategories
    
    // 关闭其他所有分类
    categories.forEach((category, i) => {
      if (i !== index) {
        category.isOpen = false
      }
    })
    
    // 切换当前分类的状态
    categories[index].isOpen = !categories[index].isOpen
    
    this.setData({
      menuCategories: categories
    })
  },
  
  // 导航到功能页面
  navigateToFunction(e) {
    const { url } = e.currentTarget.dataset
    
    if (!url) {
      wx.showToast({
        title: '功能开发中',
        icon: 'none'
      })
      return
    }
    
    wx.navigateTo({
      url: url
    })
  },
  
  // 返回上一页
  navigateBack() {
    wx.navigateBack({
      delta: 1
    })
  },
  
  // 退出管理员模式
  exitAdmin() {
    wx.showModal({
      title: '提示',
      content: '确定要退出管理员模式吗？',
      success: (res) => {
        if (res.confirm) {
          app.globalData.isAdmin = false
          app.globalData.adminInfo = null
          
          wx.showToast({
            title: '已退出管理员模式',
            icon: 'success'
          })
          
          setTimeout(() => {
            wx.switchTab({
              url: '/pages/my/my'
            })
          }, 1500)
        }
      }
    })
  }
}) 