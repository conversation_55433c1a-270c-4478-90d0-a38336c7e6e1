.category-nav {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background: var(--categorynav-bg, linear-gradient(to top, rgb(15, 15, 15), #000000));
}

.status-bar {
  width: 100%;
}

.navigation-bar {
  position: relative;
  width: 100%;
  display: flex;
  align-items: flex-start;
  padding: 0 16px;
  box-sizing: border-box;
}

.category-scroll {
  /* 宽度由JS动态计算，确保不与胶囊按钮重叠 */
  height: auto;
  white-space: nowrap;
  overflow-x: auto;
  box-sizing: border-box;
}

.category-list {
  display: flex;
  padding: 6px 0 14px 0;
  align-items: center;
  height: auto;
  transform: translateY(-8rpx);
  /* If you want to use CSS var for this transform, uncomment below */
  /* transform: translateY(var(--categorynav-list-offset, -8rpx)); */
}

.category-item {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 20rpx;
  margin-right: var(--categorynav-item-margin, 16rpx);
  height: var(--categorynav-item-height, 56rpx);
  border-radius: var(--categorynav-item-radius, 10rpx);
  background: var(--categorynav-item-bg, rgba(255, 255, 255, 0.08));
  transition: all 0.3s ease;
  flex-shrink: 0; /* 防止压缩 */
  width: auto; /* 使用自动宽度，根据内容调整 */
  margin-bottom: 8rpx; /* 确保文字容器与底部边距保持8rpx */
}

.category-item:last-child {
  margin-right: 0;
}

.category-item.active {
  background: var(--categorynav-item-active-bg, rgba(255, 255, 255, 0.15));
}

.category-text {
  font-size: var(--categorynav-font-size, 28rpx);
  color: var(--categorynav-text, rgba(255, 255, 255, 0.9));
  line-height: 1.2;
  white-space: nowrap;
  overflow: visible; /* 确保文本不会被截断 */
  display: block; /* 确保文本正确显示 */
}

.category-item.active .category-text {
  color: var(--categorynav-text-active, #ffffff);
}

.nav-placeholder {
  width: 100%;
  /* 高度由JS计算，包含状态栏+导航栏+navBarTop的高度 */
} 