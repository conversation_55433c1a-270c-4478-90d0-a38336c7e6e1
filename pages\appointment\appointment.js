const app = getApp()

Page({
  data: {
    // 服务列表
    services: [],
    loadingServices: false, // 改为false，避免初始loading状态
    selectedService: null,
    autoShowModalServiceId: '', // 用于自动弹出预约弹窗的服务ID
    
    // 轮播图数据
    carousels: [],
    loadingCarousels: false, // 改为false，避免初始loading状态
    currentCarouselIndex: 0, // 当前轮播图索引
    
    // 预约步骤控制
    currentStep: 1, // 1=选择服务项目, 2=选择日期和时间
    
    // 日期选择
    dateList: [],
    selectedDate: '',
    
    // 时间选择
    timeList: [],
    loadingTimeSlots: false,
    selectedTime: '',
    
    // 手机号输入
    phoneNumber: '',
    
    // 预约表单显示控制
    showDatePicker: false,
    
    // 预约弹窗
    showModal: false,
    showAppointmentAnimation: false, // 控制预约弹窗动画状态
    isClosing: false, // 控制预约弹窗关闭动画状态
    currentService: null,
    showServiceDropdown: false, // 添加服务下拉菜单显示控制
    dropdownOpen: false, // 控制箭头方向的类名状态
    
    // 设置今天的日期
    today: '',
    
    // 表单控制
    canSubmit: false,
    submitting: false,
    
    // 修改预约相关
    modifyingAppointment: false,
    appointmentIdToModify: null,
    
    // 七天后的日期
    endDate: '',
    
    // 多列选择器相关
    multiArray: [[], [], []], // 分别存放月、日、时间
    multiIndex: [0, 0, 0],
    monthList: [], // 可选的月份
    dayList: [], // 可选的日期
    hourList: [], // 可选的时间段
    
    // 登录相关
    showLoginModal: false, // 是否显示登录提示弹窗
    isLoggedIn: false, // 用户是否已登录
    
    // 分享链接相关
    sharedServiceId: null,
    
    // 员工选择相关
    staffList: [], // 员工列表
    loadingStaff: false, // 是否正在加载员工列表
    showStaffSelection: false, // 是否显示员工选择区域
    selectedStaffId: '', // 选中的员工ID
    selectedStaffName: '', // 选中的员工姓名
    
    // 登录后临时保存的服务信息
    tempCurrentService: null,
    
    // 手机号输入框高亮状态
    highlightPhoneInput: false,

    // 详情图片弹窗相关
    showDetailModal: false,
    detailImageUrl: '',
    
    // 下拉刷新状态
    isRefreshing: false, // 是否正在下拉刷新
    
    // 页面内容显示控制
    showContent: false, // 控制页面内容的渐进显示

    // 指向广告相关
    showTargetAd: false,
    targetAdData: {
      imageUrl: '',
      jumpUrl: ''
    },

    // 图片上传相关
    uploadedImages: [], // 存储上传的图片临时路径
    showImageUpload: false, // 控制图片上传区域的显示/隐藏
  },

  // 强制清除可能存在的过期预约状态
  forceClearAppointmentStatus() {
    // console.log('强制检查并清除可能过期的预约状态标记');
    
    const lastCheckTime = wx.getStorageSync('lastStatusCheckTime');
    const now = Date.now();
    
    // 如果最后一次检查是在超过30分钟前，或者没有记录，则强制检查
    if (!lastCheckTime || (now - lastCheckTime > 30 * 60 * 1000)) {
      // console.log('距离上次检查已超过30分钟或首次检查，强制与服务器同步预约状态');
      
      // 调用云函数检查用户是否真的有未完成的预约
      wx.cloud.callFunction({
        name: 'appointmentManager',
        data: {
          type: 'frontend',  // 添加必要的type参数
          action: 'checkExistingAppointments',
          date: '',
          time: ''
        }
      }).then(res => {
        // console.log('强制检查预约状态结果:', res.result);
        
        if (res.result && res.result.code === 0) {
          // 更新本地存储和全局状态
          wx.setStorageSync('hasActiveAppointment', res.result.hasExisting);
          wx.setStorageSync('lastStatusCheckTime', now);
          
          if (app.globalData) {
            app.globalData.hasActiveAppointment = res.result.hasExisting;
          }
          
          if (res.result.hasExisting) {
            console.log('服务器确认用户有未完成预约');
          } else {
            // console.log('服务器确认用户没有未完成预约，已清除本地状态');
          }
        }
      }).catch(err => {
        console.error('强制检查预约状态失败:', err);
      });
    } else {
      console.log('最近已检查过预约状态，跳过强制检查');
    }
  },

  onLoad(options) {
    // 引入设备工具函数
    const deviceUtils = require('../../utils/device.js');
    
    // 强制清除可能存在的过期预约状态
    this.forceClearAppointmentStatus();
    
    // 原有的页面加载逻辑
    this.setData({
      isLoading: true
    });
    
    // 处理分享参数
    if (options.sharer && options.contentType && options.contentId) {
      // 获取设备标识 - 优先使用URL中传递的设备标识，如果没有则生成新的
      let deviceIdentifier;
      if (options.deviceIdentifier) {
        // 使用URL中传递的设备标识
        deviceIdentifier = decodeURIComponent(options.deviceIdentifier);
        console.log('使用URL传递的设备标识:', deviceIdentifier);
      } else {
        // 生成新的设备标识
        deviceIdentifier = deviceUtils.getDeviceIdentifier();
        console.log('生成新的设备标识:', deviceIdentifier);
      }
      
      // 验证分享并奖励积分
      wx.cloud.callFunction({
        name: 'pointsManager',
        data: {
          action: 'verifyShareAndAddPoints',
          data: {
            sharer: options.sharer,
            deviceIdentifier: deviceIdentifier,
            contentId: options.contentId,
            contentType: options.contentType
          }
        }
      }).then(res => {
        console.log('分享验证结果:', res.result);
        if (res.result && res.result.code === 0) {
          if (res.result.alreadyRewarded) {
            console.log('该内容已被此设备打开过，不重复奖励积分');
          } else {
            console.log('分享积分添加成功，积分:', res.result.pointsAdded);
          }
        } else {
          console.error('分享积分未添加:', res.result ? res.result.message : '未知原因');
        }
      }).catch(err => {
        console.error('分享验证失败:', err);
      });
    }
    
    // 如果有服务ID参数，处理服务选择和弹窗显示
    if (options.serviceId) {
      this.setData({
        selectedServiceId: options.serviceId
      });

      // 如果同时有showModal参数，表示需要自动弹出预约弹窗
      if (options.showModal === 'true') {
        // 保存参数，等服务列表加载完成后再弹出弹窗
        this.setData({
          autoShowModalServiceId: options.serviceId
        });
      }
    }
    
    // 加载轮播图数据
    this.loadCarousels();
    
    // 加载服务项目数据
    this.loadServices();
    
    // 生成日期列表
    this.generateDateList();
    
    // 初始化时间选择器
    this.initMultiSelector();
    
    // 检查登录状态
    this.checkLoginStatus();

    // 监听指向广告显示事件
    this.setupTargetAdListener();
  },

  // 检查用户状态更新
  checkUserStatusUpdates() {
    if (!app.globalData.userInfo || !app.globalData.openid) {
      return;
    }
    
    wx.cloud.callFunction({
      name: 'getUserStatusUpdates',
      data: {
        type: 'clear_appointment_status'
      }
    }).then(res => {
      console.log('检查用户状态更新结果:', res.result);
      
      if (res.result && res.result.code === 0 && res.result.hasUpdates) {
        // 有状态更新，清除本地的预约状态
        wx.removeStorageSync('hasActiveAppointment');
        if (app.globalData) {
          app.globalData.hasActiveAppointment = false;
        }
        console.log('已根据后端通知清除预约状态标记');
      }
    }).catch(err => {
      console.error('检查用户状态更新失败:', err);
    });
  },

  onShow() {
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        selected: 2 // 设置预约为第三个标签
      });
    }
    
    // 延迟显示内容，让用户能看到0.8秒的舒缓渐进动画
    setTimeout(() => {
      this.setData({
        showContent: true
      });
    }, 100); // 100ms延迟，确保页面已准备好再开始动画
    
    // 检查用户状态更新
    this.checkUserStatusUpdates();
    
    // 每次显示页面时，重新检查用户的预约状态
    if (app.globalData.userInfo) {
      // 调用云函数检查用户是否真的有未完成的预约
      wx.cloud.callFunction({
        name: 'appointmentManager',
        data: {
          type: 'frontend',  // 确保这个参数存在
          action: 'checkExistingAppointments',
          date: '', // 空字符串表示不限制日期
          time: ''  // 空字符串表示不限制时间
        }
      }).then(res => {
        console.log('检查用户预约状态结果:', res.result);
        
        if (res.result && res.result.code === 0) {
          // 更新本地存储和全局状态
          wx.setStorageSync('hasActiveAppointment', res.result.hasExisting);
          if (app.globalData) {
            app.globalData.hasActiveAppointment = res.result.hasExisting;
          }
        }
      }).catch(err => {
        console.error('检查用户预约状态失败:', err);
      });
    }
    
    // 检查数据并设置内容
    const hasServiceData = this.data.services && this.data.services.length > 0;
    const hasCarouselData = this.data.carousels && this.data.carousels.length > 0;
    const hasGlobalServiceData = app.globalData.serviceList && app.globalData.serviceList.length > 0;
    const hasGlobalCarouselData = app.globalData.carouselList && app.globalData.carouselList.length > 0;
    
    // console.log('[Appointment] onShow数据检查');
    
    // 如果有任何数据（本地或全局），立即显示
    if (hasServiceData || hasCarouselData || hasGlobalServiceData || hasGlobalCarouselData) {
      // 使用现有数据或全局数据
      const servicesToUse = hasServiceData ? this.data.services : 
                           (hasGlobalServiceData ? app.globalData.serviceList : this.data.services);
      const carouselsToUse = hasCarouselData ? this.data.carousels : 
                            (hasGlobalCarouselData ? app.globalData.carouselList : this.data.carousels);
      
      this.setData({
        services: servicesToUse,
        carousels: carouselsToUse,
        loadingServices: false,
        loadingCarousels: false
      });
      
      // console.log('[Appointment] 使用现有数据显示内容');
    } else {
      // 如果没有任何数据，设置基本的占位符
      this.setData({
        services: [],
        carousels: [],
        loadingServices: false,
        loadingCarousels: false
      });
      
      // console.log('[Appointment] 没有数据，显示空状态');
    }
    
    // 检查是否是修改预约模式
    if (app.globalData.isModifyingAppointment && app.globalData.appointmentToModify) {
      // 获取要修改的预约信息
      const appointment = app.globalData.appointmentToModify;
      
      // 重置修改标记，避免重复处理
      app.globalData.isModifyingAppointment = false;
      
      // 查找对应的服务
      this.fetchServices().then(() => {
        const service = this.data.services.find(s => 
          s._id === appointment.serviceId || s.id === appointment.serviceId
        );
        
        if (service) {
          // 显示预约弹窗
          this.setData({
            showModal: true,
            currentService: service,
            selectedDate: appointment.date,
            selectedTime: '',  // 先清空时间，因为需要重新获取可用时间段
            phoneNumber: appointment.phoneNumber || '',
            modifyingAppointment: true,
            appointmentIdToModify: appointment._id
          });
          
          // 获取可用时间段
          this.fetchAvailableTimeSlots();
          
          // 显示修改提示
          wx.showToast({
            title: '请选择新的预约时间',
            icon: 'none',
            duration: 2000
          });
        } else {
          wx.showToast({
            title: '未找到对应的服务',
            icon: 'none'
          });
          
          // 清除修改数据
          app.globalData.appointmentToModify = null;
        }
      });
    }
    
    // 如果显示员工选择区域，获取员工列表
    if (this.data.showStaffSelection) {
      this.fetchStaffList();
    }
  },

  onReady() {
    // 页面渲染完成
  },

  onHide() {
    // 不再需要清除定时刷新
  },
  
  // 阻止事件冒泡
  stopPropagation(e) {
    // 阻止事件冒泡
    e.stopPropagation();
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    console.log('触发页面下拉刷新');
    
    // 如果已经在刷新中，则不重复刷新
    if (this.data.isRefreshing) {
      wx.stopPullDownRefresh();
      return;
    }
    
    // 设置刷新状态，但不设置loading状态，避免现有内容消失
    this.setData({
      isRefreshing: true
    });
    
    // 同时刷新服务列表和轮播图
    Promise.all([
      this.fetchServices(true, true), // 添加参数表示是刷新操作且静默刷新
      this.fetchCarousels(true, true) // 添加参数表示是刷新操作且静默刷新
    ])
    .then(() => {
      console.log('页面下拉刷新完成');
      // 停止下拉刷新动画，不显示成功提示
      wx.stopPullDownRefresh();
      // 重置刷新状态
      this.setData({
        isRefreshing: false
      });
    })
    .catch((error) => {
      console.error('刷新失败:', error);
      // 停止下拉刷新动画
      wx.stopPullDownRefresh();
      // 重置刷新状态
      this.setData({
        isRefreshing: false
      });
    });
  },

  /**
   * 处理scroll-view的下拉刷新事件
   */
  onScrollViewRefresh() {
    console.log('触发scroll-view下拉刷新');
    
    // 设置下拉刷新状态为true，但不设置loading状态，避免现有内容消失
    this.setData({
      isRefreshing: true
    });
    
    // 同时刷新服务列表和轮播图
    Promise.all([
      this.fetchServices(true, true), // 添加参数表示是刷新操作且静默刷新
      this.fetchCarousels(true, true) // 添加参数表示是刷新操作且静默刷新
    ])
    .then(() => {
      console.log('下拉刷新完成');
      // 设置下拉刷新状态为false
      this.setData({
        isRefreshing: false
      });
    })
    .catch((error) => {
      console.error('刷新失败:', error);
      // 设置下拉刷新状态为false
      this.setData({
        isRefreshing: false
      });
    });
  },

  // 获取轮播图数据 - 优先使用预加载的数据
  loadCarousels() {
    // console.log('开始加载轮播图数据');

    // 检查全局数据中是否有预加载的轮播图数据
    if (app.globalData.carouselList && app.globalData.carouselList.length > 0) {
      // console.log('使用预加载的轮播图数据，数量:', app.globalData.carouselList.length);
      
      // 过滤掉包含cloud://的图片URL
      const filteredData = app.globalData.carouselList.map(item => {
        if (item.imageUrl && item.imageUrl.includes('cloud://')) {
          item.imageUrl = ''; // 清空无效的云存储路径
        }
        return item;
      });
      
      this.setData({
        carousels: filteredData,
        loadingCarousels: false
      });
      
      // 在后台继续刷新轮播图数据，确保数据是最新的
      setTimeout(() => {
        this.fetchCarousels(true, true); // 静默刷新
      }, 3000);
      
      return Promise.resolve(filteredData);
    } else {
      console.log('未找到预加载的轮播图数据，从服务器获取');
      return this.fetchCarousels();
    }
  },
  
  // 获取轮播图数据
  fetchCarousels(refresh = false, silentRefresh = false) {
    console.log('开始获取轮播图数据', refresh ? '(刷新模式)' : '', silentRefresh ? '(静默刷新)' : '');
    
    // 只有在非静默刷新模式下才设置loading状态
    if (!silentRefresh) {
      this.setData({
        loadingCarousels: true
      });
    }
    
    // 如果是刷新操作，添加时间戳参数避免缓存
    const timestamp = refresh ? Date.now() : '';
    
    return wx.cloud.callFunction({
      name: 'appointmentManager',
      data: {
        action: 'getCarousel',
        timestamp: timestamp // 添加时间戳参数避免缓存
      }
    })
    .then(res => {
      // console.log('获取轮播图成功');
      if (res && res.result && res.result.data) {
        
        // 过滤掉包含cloud://的图片URL
        const filteredData = res.result.data.map(item => {
          if (item.imageUrl && item.imageUrl.includes('cloud://')) {
            item.imageUrl = ''; // 清空无效的云存储路径
          }
          return item;
        });
        
        this.setData({
          carousels: filteredData,
          loadingCarousels: false
        });
        // console.log('轮播图数据已设置到data中');
        
        // 更新全局数据
        app.globalData.carouselList = filteredData;
        app.globalData.carouselLoaded = true;
        
        // 如果没有获取到轮播图数据，尝试初始化
        if (filteredData.length === 0) {
          // console.log('未获取到轮播图数据，尝试初始化');
          return this.initTestCarousels();
        }
        return filteredData;
      } else {
        console.log('未获取到轮播图数据或数据格式不正确');
        // 只有在非静默刷新模式下才更新UI
        if (!silentRefresh) {
          this.setData({
            carousels: [],
            loadingCarousels: false
          });
        }
        
        // 尝试初始化测试数据
        return this.initTestCarousels();
      }
    })
    .catch(err => {
      console.error('获取轮播图失败:', err);
      // 只有在非静默刷新模式下才更新UI
      if (!silentRefresh) {
        this.setData({
          carousels: [],
          loadingCarousels: false
        });
      }
      
      // 尝试初始化测试数据
      return this.initTestCarousels();
    });
  },
  
  // 初始化测试轮播图数据
  initTestCarousels() {
    console.log('初始化测试轮播图数据');
    
    // 调用云函数初始化轮播图
    wx.cloud.callFunction({
      name: 'appointmentManager',
      data: {
        action: 'initCarousel',
        type: 'admin'
      }
    })
    .then(res => {
      console.log('初始化轮播图成功:', res);
      // 重新获取轮播图数据
      this.fetchCarousels();
    })
    .catch(err => {
      console.error('初始化轮播图失败:', err);
      
      // 如果云函数调用失败，使用本地测试数据
      const testCarousels = [
        {
          id: 'test_carousel_1',
          title: '测试轮播图1',
          imageUrl: '',
          order: 1
        },
        {
          id: 'test_carousel_2',
          title: '测试轮播图2',
          imageUrl: '',
          order: 2
        },
        {
          id: 'test_carousel_3',
          title: '测试轮播图3',
          imageUrl: '',
          order: 3
        }
      ];
      
      this.setData({
        carousels: testCarousels,
        loadingCarousels: false
      });
      
      console.log('已设置本地测试轮播图数据');
    });
  },
  
  // 获取服务列表 - 优先使用预加载的数据
  loadServices() {
    // console.log('开始加载服务列表');

    // 检查全局数据中是否有预加载的服务列表数据
    if (app.globalData.serviceList && app.globalData.serviceList.length > 0) {
      // console.log('使用预加载的服务列表数据，数量:', app.globalData.serviceList.length);
      
      // 处理服务列表数据，添加必要的字段
      const serviceData = app.globalData.serviceList.map(service => {
        // 确保id字段存在
        if (!service.id && service._id) {
          service.id = service._id;
        }
        
        // 确保name字段存在
        if (!service.name && service.title) {
          service.name = service.title;
        }
        
        // 确保image字段存在
        if (!service.image && service.coverUrl) {
          service.image = service.coverUrl;
        }
        
        return service;
      });
      
      this.setData({
        services: serviceData,
        loadingServices: false
      });

      // 检查是否需要自动弹出预约弹窗
      this.checkAutoShowModal(serviceData);

      // 在后台继续刷新服务列表数据，确保数据是最新的
      setTimeout(() => {
        this.fetchServices(true, true); // 静默刷新
      }, 3000);

      return Promise.resolve(serviceData);
    } else {
      console.log('未找到预加载的服务列表数据，从服务器获取');
      return this.fetchServices();
    }
  },
  
  // 获取服务列表，返回Promise以便链式调用
  fetchServices(refresh = false, silentRefresh = false) {
    return new Promise((resolve, reject) => {
      console.log('开始获取服务列表', refresh ? '(刷新模式)' : '', silentRefresh ? '(静默刷新)' : '');
      
      // 只有在非静默刷新模式下才设置loading状态
      if (!silentRefresh) {
        this.setData({
          loadingServices: true
        });
      }

      // 如果是刷新操作，添加时间戳参数避免缓存
      const timestamp = refresh ? Date.now() : '';

      wx.cloud.callFunction({
        name: 'appointmentManager',
        data: {
          action: 'getServices',
          type: 'user', // 使用user类型，服务端会自动筛选可见的服务并按更新时间排序
          timestamp: timestamp // 添加时间戳参数避免缓存
        },
        success: res => {
          // console.log('获取服务列表成功');

          let serviceData = [];

          // 尝试多种可能的数据结构
          if (res.result && res.result.data) {
            serviceData = res.result.data;
          } else if (res.result && res.result.code === 0 && res.result.services) {
            serviceData = res.result.services;
          } else if (Array.isArray(res.result)) {
            serviceData = res.result;
          } else if (res.result) {
            // 尝试其他可能的结构
            const possibleArrays = Object.values(res.result).filter(val => Array.isArray(val));
            if (possibleArrays.length > 0) {
              serviceData = possibleArrays[0];
            }
          }

          // 确保更新时间属性存在，并添加服务列表更新提示
          if (serviceData && serviceData.length > 0) {
            let hasNewServices = false;
            const lastRefreshTime = wx.getStorageSync('lastServiceRefreshTime') || 0;
            const currentTime = new Date().getTime();
            
            // 获取7天前的时间戳，在此时间之后更新的服务会被标记为"最新"
            const oneWeekAgo = currentTime - 7 * 24 * 60 * 60 * 1000;
            
            serviceData.forEach(service => {
              // 检查服务是否为新添加的或最近更新的
              if (service.updateTime && service.updateTime > lastRefreshTime) {
                hasNewServices = true;
              }
              
              // 标记7天内更新过的服务为"最新"
              if (service.updateTime && service.updateTime > oneWeekAgo) {
                service.isNew = true;
              } else {
                service.isNew = false;
              }
            });
            
            // 存储本次刷新时间
            wx.setStorageSync('lastServiceRefreshTime', currentTime);
            
            // 如果有新服务或更新的服务，且不是下拉刷新操作，显示提示
            if (hasNewServices && !refresh) {
              wx.showToast({
                title: '服务列表已更新',
                icon: 'success'
              });
            }
            
            // 更新全局数据
            app.globalData.serviceList = serviceData;
            app.globalData.serviceLoaded = true;
          }
          
          // 只有在非静默刷新模式下或者有数据时才更新UI
          if (!silentRefresh || (serviceData && serviceData.length > 0)) {
            this.setData({
              services: serviceData || [],
              loadingServices: false
            });
          }
          
          // 只在非刷新模式下显示空数据提示
          if (serviceData && serviceData.length === 0 && !refresh && !silentRefresh) {
            wx.showToast({
              title: '暂无可预约的服务',
              icon: 'none'
            });
          }

          // 检查是否需要自动弹出预约弹窗（只在非静默刷新模式下）
          if (!silentRefresh) {
            this.checkAutoShowModal(serviceData);
          }

          resolve(serviceData);
        },
        fail: err => {
          console.error('获取服务列表失败：', err);
          
          // 只有在非静默刷新模式下才更新UI
          if (!silentRefresh) {
            this.setData({
              loadingServices: false,
              services: []
            });
          }
          
          // 只在非刷新模式下显示错误提示
          if (!refresh && !silentRefresh) {
            wx.showToast({
              title: '网络错误，请重试',
              icon: 'none'
            });
          }
          
          reject(err);
        }
      });
    });
  },
  
  // 生成未来7天的日期列表
  generateDateList() {
    const dateList = [];
    const dayNames = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
    
    // 只生成未来7天的日期
    for (let i = 0; i < 7; i++) {
      const date = new Date();
      date.setDate(date.getDate() + i);
      
      const year = date.getFullYear();
      const month = date.getMonth() + 1;
      const day = date.getDate();
      const dayName = dayNames[date.getDay()];
      
      const formattedDate = `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;
      const dateText = `${month}/${day}`;
      
      dateList.push({
        date: formattedDate,
        dateText: dateText,
        dayName: dayName
      });
    }
    
    // 默认选择第一天
    const selectedDate = dateList.length > 0 ? dateList[0].date : '';
    
    this.setData({
      dateList: dateList,
      selectedDate: selectedDate
    });
  },

  // 检查是否需要自动弹出预约弹窗
  checkAutoShowModal(serviceData) {
    const { autoShowModalServiceId } = this.data;

    if (!autoShowModalServiceId || !serviceData || serviceData.length === 0) {
      return;
    }

    // 查找对应的服务
    const targetService = serviceData.find(service =>
      service._id === autoShowModalServiceId || service.id === autoShowModalServiceId
    );

    if (targetService) {
      console.log('找到目标服务，自动弹出预约弹窗:', targetService.name);

      // 清除自动弹窗标记
      this.setData({
        autoShowModalServiceId: ''
      });

      // 延迟一点时间确保页面渲染完成
      setTimeout(() => {
        // 模拟点击事件数据
        const mockEvent = {
          currentTarget: {
            dataset: {
              service: targetService
            }
          }
        };

        // 调用显示预约弹窗方法
        this.showAppointmentModal(mockEvent);
      }, 500);
    } else {
      console.log('未找到目标服务，serviceId:', autoShowModalServiceId);
    }
  },

  // 显示预约弹窗
  showAppointmentModal(e) {
    // 获取服务信息
    const service = e.currentTarget.dataset.service;

    // 不再在这里检查登录状态，直接打开预约弹窗
    console.log('[Appointment] 打开预约弹窗，服务:', service.name);

    // 直接打开预约弹窗（无论是否登录）
    // 先显示弹窗（但保持在底部隐藏状态）
    this.setData({
      showModal: true,
      currentService: service,
      selectedDate: this.data.selectedDate || '',
      selectedTime: this.data.selectedTime || '',
      // 重置员工选择相关数据
      showStaffSelection: true, // 每次打开弹窗时都展开服务人员选择区域
      selectedStaffId: '',
      selectedStaffName: '',
      // 重置服务下拉菜单状态
      showServiceDropdown: false,
      dropdownOpen: false,
      // 重置图片上传相关状态
      uploadedImages: [],
      showImageUpload: false
    });
    
    // 隐藏TabBar导航栏
    console.log('[Appointment] 预约弹窗打开，隐藏导航栏');
    this.hideTabBar();
    
    // 获取员工列表
    this.fetchStaffList();
    
    // 获取可用时间段
    this.fetchAvailableTimeSlots();

    // 延时触发向上滑入动画
    setTimeout(() => {
      this.setData({
        showAppointmentAnimation: true // 触发向上滑入动画
      });
    }, 50); // 短暂延时确保DOM已渲染
  },
  
  // 隐藏预约弹窗
  hideAppointmentModal() {
    // 添加关闭动画
    this.setData({
      isClosing: true
    });
    
    // 延迟隐藏弹窗，等待动画完成
    setTimeout(() => {
      this.setData({
        showModal: false,
        isClosing: false,
        showServiceDropdown: false,
        dropdownOpen: false,
        selectedStaffId: '', // 清空已选员工
        selectedStaffName: '',
        showStaffSelection: false, // 隐藏员工选择区域
        uploadedImages: [], // 清空上传的图片
        showImageUpload: false // 隐藏图片上传区域
      });
      
      // 显示底部TabBar
      this.showTabBar();
    }, 300);
  },

  // 隐藏TabBar导航栏
  hideTabBar() {
    try {
      if (typeof this.getTabBar === 'function' && this.getTabBar()) {
        this.getTabBar().hide();
        console.log('[Appointment] TabBar已隐藏');
      }
    } catch (error) {
      console.error('[Appointment] 隐藏TabBar失败:', error);
    }
  },

  // 显示TabBar导航栏
  showTabBar() {
    try {
      if (typeof this.getTabBar === 'function' && this.getTabBar()) {
        this.getTabBar().show();
        console.log('[Appointment] TabBar已显示');
      }
    } catch (error) {
      console.error('[Appointment] 显示TabBar失败:', error);
    }
  },
  
  // 选择服务
  selectService(e) {
    const serviceId = e.currentTarget.dataset.id;
    this.setData({
      selectedService: serviceId
    });
    
    // 更新可提交状态
    this.updateSubmitStatus();
  },
  
  // 进入下一步
  goToNextStep() {
    if (this.data.selectedService) {
      this.setData({
        currentStep: 2,
        showDatePicker: true
      });
      
      // 获取可用时间段
      this.fetchAvailableTimeSlots();
    } else {
      wx.showToast({
        title: '请先选择服务',
        icon: 'none'
      });
    }
  },
  
  // 返回上一步
  goToPrevStep() {
    this.setData({
      currentStep: 1,
      showDatePicker: false
    });
  },
  
  // 选择日期
  selectDate(e) {
    const date = e.currentTarget.dataset.date;
    
    if (date === this.data.selectedDate) {
      return;
    }
    
    // 验证日期是否在允许范围内
    const selectedDateObj = new Date(date);
    const today = new Date();
    today.setHours(0, 0, 0, 0); // 设置时间为当天开始
    
    const endDate = new Date(this.data.endDate);
    endDate.setHours(23, 59, 59, 999); // 设置时间为当天结束
    
    if (selectedDateObj < today || selectedDateObj > endDate) {
      wx.showToast({
        title: '只能选择未来7天内的日期',
        icon: 'none'
      });
      return;
    }
    
    this.setData({
      selectedDate: date,
      selectedTime: '' // 重置选中的时间
    });
    
    // 获取新日期的可用时间段
    this.fetchAvailableTimeSlots();
    
    // 更新可提交状态
    this.updateSubmitStatus();
  },
  
  // 日期选择改变
  dateChange(e) {
    const date = e.detail.value;
    console.log('选择日期：', date);
    
    // 验证日期是否在允许范围内
    const selectedDateObj = new Date(date);
    const today = new Date();
    today.setHours(0, 0, 0, 0); // 设置时间为当天开始
    
    const endDate = new Date(this.data.endDate);
    endDate.setHours(23, 59, 59, 999); // 设置时间为当天结束
    
    if (selectedDateObj < today || selectedDateObj > endDate) {
      wx.showToast({
        title: '只能选择未来7天内的日期',
        icon: 'none'
      });
      return;
    }
    
    this.setData({
      selectedDate: date
    });
    
    // 获取选择日期的可用时间段
    this.fetchAvailableTimes(date);
    
    // 更新提交状态
    this.updateSubmitStatus();
  },
  
  // 处理日期选择器点击事件
  onDatePickerTap() {
    // 设置日期选择器为激活状态，并保持这个状态（不再使用定时器恢复）
    this.setData({
      datePickerActive: true
    });
  },

  // 获取可用时间段
  fetchAvailableTimeSlots(date) {
    const selectedDate = date || this.data.selectedDate;
    if (!this.data.currentService) {
      return;
    }
    
    this.setData({ loadingTimeSlots: true });
    
    // 如果没有选择日期，调用默认时间段生成方法
    if (!selectedDate) {
      this.generateDefaultTimeSlots();
      return;
    }
    
    // 直接在前端生成固定的时间段列表（9:00-20:00，每30分钟一个时间段）
    const timeSlots = this.generateTimeSlots();
    
    console.log('生成的时间段列表:', timeSlots);
    console.log('可用的时间段数量:', timeSlots.filter(slot => slot.available).length);
    
    // 设置时间段列表
    this.setData({
      timeList: timeSlots,
      loadingTimeSlots: false
    });
  },

  // 新增：生成默认时间段方法
  generateDefaultTimeSlots() {
    const timeSlots = this.generateTimeSlots();
    
    this.setData({
      timeList: timeSlots,
      loadingTimeSlots: false
    });
  },

  // 新增：提取生成时间段的逻辑为独立方法
  generateTimeSlots() {
    const timeSlots = [];
    const startTime = new Date(`2000-01-01T09:00:00`); // 早上9点
    const endTime = new Date(`2000-01-01T20:00:00`);   // 晚上8点
    const intervalMinutes = 30; // 每30分钟一个时间段
    
    // 获取当前日期和时间
    const now = new Date();
    const currentHours = now.getHours();
    const currentMinutes = now.getMinutes();
    
    // 检查选择的日期是否是今天
    const isToday = this.isSelectedDateToday();
    
    let current = new Date(startTime);
    while (current < endTime) {
      const hours = current.getHours();
      const minutes = current.getMinutes();
      
      // 对于今天的时间段，过滤已经过去的时间
      let isAvailable = true;
      if (isToday) {
        // 如果是今天，并且当前时间已经超过了这个时间段，则标记为不可用
        if (hours < currentHours || (hours === currentHours && minutes <= currentMinutes)) {
          isAvailable = false;
        }
      }
      
      timeSlots.push({
        time: `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`,
        available: isAvailable // 根据时间是否已过来设置可用性
      });
      
      current = new Date(current.getTime() + intervalMinutes * 60000);
    }
    
    return timeSlots;
  },

  // 新增：判断选中的日期是否是今天
  isSelectedDateToday() {
    if (!this.data.selectedDate) {
      return false;
    }
    
    const today = new Date();
    const year = today.getFullYear();
    const month = (today.getMonth() + 1).toString().padStart(2, '0');
    const day = today.getDate().toString().padStart(2, '0');
    const todayStr = `${year}-${month}-${day}`;
    
    return this.data.selectedDate === todayStr;
  },

  // 选择时间
  selectTime(e) {
    const time = e.currentTarget.dataset.time;
    const available = e.currentTarget.dataset.available;
    
    console.log('选择时间段：', time, '是否可用：', available);
    
    if (!available) {
      wx.showToast({
        title: '该时段已过或不可用',
        icon: 'none'
      });
      return;
    }
    
    this.setData({
      selectedTime: time,
      canSubmit: this.data.phoneNumber.length === 11 && time
    });
  },
  
  // 输入手机号
  inputPhoneNumber(e) {
    const phoneNumber = e.detail.value;
    console.log('输入手机号：', phoneNumber);
    
    this.setData({
      phoneNumber: phoneNumber,
      canSubmit: phoneNumber.length === 11 && this.data.selectedTime
    });
  },

  // 更新提交按钮状态
  updateSubmitStatus() {
    const { currentService, selectedDate, selectedTime, phoneNumber } = this.data;
    
    // 验证手机号格式：11位数字
    const isPhoneValid = phoneNumber && /^1\d{10}$/.test(phoneNumber);
    // 需要选择服务、日期、时间，并且手机号有效
    const canSubmit = currentService && selectedDate && selectedTime && isPhoneValid;
    
    this.setData({
      canSubmit
    });
  },

  // 检查用户登录状态
  checkLoginStatus() {
    // 从全局数据或存储中获取用户登录状态
    const isLoggedIn = app.globalData.userInfo !== null && app.globalData.userInfo !== undefined;
    
    this.setData({
      isLoggedIn: isLoggedIn
    });
    
    // console.log('用户登录状态:', isLoggedIn ? '已登录' : '未登录');
    
    return isLoggedIn;
  },

  // 提交预约
  submitAppointment() {
    // 检查是否可以提交
    if (!this.data.canSubmit || this.data.submitting) {
      return;
    }

    // 在提交时检查用户是否已登录
    const isLoggedIn = this.checkLoginStatus();
    if (!isLoggedIn) {
      console.log('[Appointment] 提交预约时检查到用户未登录，显示登录弹窗');
      // 临时保存当前服务信息，以便登录后继续提交
      this.setData({
        showLoginModal: true,
        tempCurrentService: this.data.currentService
      });
      return;
    }
    
    // 检查是否已选择服务
    if (!this.data.currentService) {
      wx.showToast({
        title: '请选择服务项目',
        icon: 'none'
      });
      return;
    }
    
    // 检查是否已选择日期和时间
    if (!this.data.selectedDate || !this.data.selectedTime) {
      wx.showToast({
        title: '请选择预约时间',
        icon: 'none'
      });
      return;
    }

    // 检查是否已输入手机号
    if (!this.data.phoneNumber) {
      this.setData({
        highlightPhoneInput: true
      });
      wx.showToast({
        title: '请输入手机号',
        icon: 'none'
      });
      return;
    }
    
    // 验证手机号格式
    if (!/^1\d{10}$/.test(this.data.phoneNumber)) {
      this.setData({
        highlightPhoneInput: true
      });
      wx.showToast({
        title: '请输入正确的手机号',
        icon: 'none'
      });
      return;
    }
    
    // 设置提交中状态
    this.setData({
      submitting: true
      });

    // 如果是修改预约
    if (this.data.modifyingAppointment && this.data.appointmentIdToModify) {
      this.updateExistingAppointment();
      return;
    }

    // 检查是否有未完成的预约（限制每位用户只能有一个有效预约）
    this.checkExistingAppointments();
  },

  // 检查是否有相同时间段的预约
  checkExistingAppointments() {
    wx.showLoading({
      title: '检查预约...',
      mask: true
    });

    // 先检查本地存储中是否已有标记表示用户有未完成预约
    const hasActiveAppointment = wx.getStorageSync('hasActiveAppointment') || 
                               (app.globalData && app.globalData.hasActiveAppointment);
    
    if (hasActiveAppointment) {
      wx.hideLoading();
      console.log('本地检测到用户已有未完成预约');
      
      // 有未完成的预约，不允许继续预约
      wx.showModal({
        title: '预约提示',
        content: '您已有未完成的预约，每位用户仅限一个预约，暂不能再次预约',
        showCancel: false,
        confirmText: '我知道了',
        success: () => {
          // 用户确认后，重置提交状态
          this.setData({
            submitting: false
          });
        }
      });
      return;
    }

    // 如果本地没有标记，再调用云函数检查
    wx.cloud.callFunction({
      name: 'appointmentManager',
      data: {
        type: 'frontend',
        action: 'checkExistingAppointments',
        date: this.data.selectedDate,
        time: this.data.selectedTime
      }
    }).then(res => {
      wx.hideLoading();
      console.log('检查预约结果:', res.result);
      
      if (res.result && res.result.code === 0) {
        if (res.result.hasExisting) {
          // 有未完成的预约，不允许继续预约
          wx.showModal({
            title: '预约提示',
            content: '您已有未完成的预约，每位用户仅限一个预约，暂不能再次预约',
            showCancel: false,
            confirmText: '我知道了',
            success: () => {
              // 用户确认后，重置提交状态
              this.setData({
                submitting: false
              });
            }
          });
          
          // 同时更新本地存储和全局状态，避免重复调用云函数
          wx.setStorageSync('hasActiveAppointment', true);
          if (app.globalData) {
            app.globalData.hasActiveAppointment = true;
          }
        } else {
          // 没有相同时间段的预约，直接提交
          this.processAppointmentSubmission();
        }
      } else {
        // 检查失败，提示错误
        wx.showToast({
          title: res.result ? res.result.message : '检查预约失败',
          icon: 'none'
        });
        this.setData({
          submitting: false
        });
      }
    }).catch(err => {
      wx.hideLoading();
      console.error('检查预约失败:', err);
      wx.showToast({
        title: '检查预约失败，请重试',
        icon: 'none'
      });
      this.setData({
        submitting: false
      });
    });
  },

  // 处理预约提交
  processAppointmentSubmission() {
    // 如果有上传图片，先上传到云存储
    if (this.data.uploadedImages.length > 0) {
      wx.showLoading({
        title: '正在上传图片...',
        mask: true
      });
      
      const uploadTasks = this.data.uploadedImages.map(filePath => {
        return wx.cloud.uploadFile({
          cloudPath: `appointment_images/${Date.now()}_${Math.random().toString(36).substr(2, 10)}.png`,
          filePath: filePath
        });
      });
      
      Promise.all(uploadTasks).then(results => {
        const fileIDs = results.map(res => res.fileID);
        // 继续提交预约，并附带图片fileID
        this.createAppointmentWithImages(fileIDs);
      }).catch(err => {
        console.error('上传图片失败', err);
        wx.hideLoading();
        wx.showToast({
          title: '图片上传失败，请重试',
          icon: 'none'
        });
        this.setData({
          submitting: false
        });
    });
    } else {
      // 无图片，直接提交预约
      this.createAppointmentWithImages([]);
    }
  },

  // 创建预约（带图片）
  createAppointmentWithImages(imageFileIDs) {
    wx.showLoading({
      title: '提交预约...',
      mask: true
    });
    
    // 更新用户手机号到云端
    this.updateUserPhoneToCloud(this.data.phoneNumber);

    // 检查服务ID格式
    let serviceId = this.data.currentService._id || this.data.currentService.id;
    
    // 如果ID以"service_"开头，去掉前缀
    if (typeof serviceId === 'string' && serviceId.startsWith('service_')) {
      serviceId = serviceId.substring(8);
    }
    
    console.log('原始服务ID:', this.data.currentService._id || this.data.currentService.id);
    console.log('处理后的服务ID:', serviceId);
    console.log('当前服务信息:', JSON.stringify(this.data.currentService));

    // 调用云函数创建预约
    wx.cloud.callFunction({
      name: 'appointmentManager',
      data: {
        type: 'frontend',
        action: 'createAppointment',
        serviceId: serviceId,
        date: this.data.selectedDate,
        time: this.data.selectedTime,
        phoneNumber: this.data.phoneNumber,
        preferredStaffId: this.data.selectedStaffId || '',
        preferredStaffName: this.data.selectedStaffName || '',
        imageFileIDs: imageFileIDs // 添加图片fileID
      }
    }).then(res => {
      wx.hideLoading();
      
      console.log('预约提交结果:', res);
        
      if (res.result && res.result.code === 0) {
        // 预约成功
        wx.showToast({
          title: '预约成功',
          icon: 'success'
        });
        
        // 保存预约状态到本地存储，标记用户已有未完成预约
        wx.setStorageSync('hasActiveAppointment', true);
        
        // 同时更新全局状态
        if (app.globalData) {
          app.globalData.hasActiveAppointment = true;
        }
        
        // 重置表单
        this.setData({
          selectedDate: '',
          selectedTime: '',
          submitting: false,
          showModal: false,
          uploadedImages: [] // 清空上传的图片
        });
        
        // 延迟关闭弹窗，让用户看到成功提示
        setTimeout(() => {
          this.hideAppointmentModal();
        }, 1500);
      } else {
        // 预约失败
        wx.showToast({
          title: res.result ? res.result.message : '预约失败',
          icon: 'none'
        });
        this.setData({
          submitting: false
        });
      }
    }).catch(err => {
      wx.hideLoading();
      console.error('预约失败:', err);
      wx.showToast({
        title: '预约失败，请重试',
        icon: 'none'
      });
      this.setData({
        submitting: false
      });
    });
  },
  
  // 创建预约
  createAppointment() {
    // 保存用户手机号到本地存储
    wx.setStorageSync('userPhoneNumber', this.data.phoneNumber);
    
    // 如果用户已登录，也将手机号保存到用户信息中
    if (app.globalData.userInfo) {
      // 更新全局用户信息中的手机号
      app.globalData.userInfo.phoneNumber = this.data.phoneNumber;
      
      // 保存到本地存储
      wx.setStorageSync('userInfo', app.globalData.userInfo);
      
      // 可选：同步更新到云端
      this.updateUserPhoneToCloud(this.data.phoneNumber);
    }

    // 检查当前服务信息是否存在
    if (!this.data.currentService) {
      wx.showToast({
        title: '服务信息不完整，请重新选择',
        icon: 'none',
        duration: 2000
      });
      this.setData({ submitting: false });
      return;
    }

    // 准备预约数据
    const appointmentData = {
      serviceId: this.data.currentService._id || this.data.currentService.id,
      serviceName: this.data.currentService.name,
      servicePrice: this.data.currentService.price,
      date: this.data.selectedDate,
      time: this.data.selectedTime,
      phoneNumber: this.data.phoneNumber
    };
    
    // 添加员工选择信息
    if (this.data.selectedStaffId && this.data.selectedStaffName) {
      appointmentData.preferredStaffId = this.data.selectedStaffId;
      appointmentData.preferredStaffName = this.data.selectedStaffName;
      appointmentData.assignment_type = 'specified'; // 指定员工
      console.log('已选择指定员工：', this.data.selectedStaffName, '(ID:', this.data.selectedStaffId, ')');
    } else {
      appointmentData.assignment_type = 'random'; // 随机分配
      console.log('未选择指定员工，将随机分配');
    }

    // 显示加载提示
    wx.showLoading({
      title: '提交预约中...',
      mask: true
    });

    console.log('准备提交预约数据：', appointmentData);

    // 调用云函数创建预约
    wx.cloud.callFunction({
      name: 'appointmentManager',
      data: {
        type: 'frontend',
        action: 'createAppointment',
        ...appointmentData
      },
      success: res => {
        // 隐藏加载提示
        wx.hideLoading();
        
        console.log('预约提交结果：', res);
        console.log('返回的result详细内容：', JSON.stringify(res.result));
        console.log('result中是否有code字段：', res.result ? (res.result.code !== undefined ? '有' : '无') : '无');
        console.log('result中是否有message字段：', res.result ? (res.result.message !== undefined ? '有' : '无') : '无');
        
        // 检查返回结果中的code字段
        if (res.result && res.result.code === 0) {
          // 预约成功
          wx.showToast({
            title: '预约提交成功',
            icon: 'success'
          });
          
          // 隐藏预约弹窗
          this.setData({
            showModal: false
          });
          
          // 延迟后跳转到我的预约页面
          setTimeout(() => {
            wx.navigateTo({
              url: '/pages/myAppointments/myAppointments'
            });
          }, 1500);
        } else {
          // 预约失败
          wx.showToast({
            title: res.result && res.result.message ? res.result.message : '预约提交失败',
            icon: 'none'
          });
        }
      },
      fail: err => {
        // 隐藏加载提示
        wx.hideLoading();
        
        console.error('预约提交失败：', err);
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      },
      complete: () => {
        this.setData({
          submitting: false
        });
      }
    });
  },

  // 将用户手机号同步到云端
  updateUserPhoneToCloud(phoneNumber) {
    if (!app.globalData.openid) return; // 如果没有openid则不执行
    
    // 获取当前用户信息
    const userInfo = app.globalData.userInfo || {};
    userInfo.phoneNumber = phoneNumber; // 添加手机号
    
    // 调用云函数更新用户信息
    wx.cloud.callFunction({
      name: 'userManager',
      data: {
        type: 'frontend',
        action: 'saveUserInfo',
        userInfo: userInfo
      },
      success: res => {
        console.log('手机号更新到云端成功:', res);
      },
      fail: err => {
        console.error('手机号更新到云端失败:', err);
      }
    });
  },

  // 获取可用时间段
  fetchAvailableTimes(date) {
    // 这个方法已被fetchAvailableTimeSlots替代，保留这个方法是为了向后兼容
    this.fetchAvailableTimeSlots(date);
  },

  // 初始化多列选择器数据
  initMultiSelector() {
    // 获取未来7天的月份和日期
    const monthList = []; // 存放月份名称
    const dayList = []; // 存放日期名称
    const monthValues = []; // 存放月份值 (MM格式)
    const dayValues = []; // 存放日期值 (DD格式)
    const now = new Date();
    
    // 今天到未来7天的日期
    for (let i = 0; i < 7; i++) {
      const date = new Date();
      date.setDate(now.getDate() + i);
      
      const month = date.getMonth() + 1;
      const day = date.getDate();
      
      // 添加月份
      const monthStr = `${month}月`;
      const monthValue = month.toString().padStart(2, '0');
      if (!monthValues.includes(monthValue)) {
        monthValues.push(monthValue);
        monthList.push(monthStr);
      }
      
      // 添加日期
      let dayStr;
      if (i === 0) {
        dayStr = `${day}日(今天)`;
      } else if (i === 1) {
        dayStr = `${day}日(明天)`;
      } else if (i === 2) {
        dayStr = `${day}日(后天)`;
      } else {
        dayStr = `${day}日`;
      }
      
      dayValues.push(day.toString().padStart(2, '0'));
      dayList.push(dayStr);
    }
    
    // 使用新方法生成时间列表，默认为今天(偏移量为0)
    const hourList = this.generateHourListForDay(0);
    
    // 判断今天是否还有可用时间段
    let defaultDayIndex = 0; // 默认选择今天
    let defaultTimeIndex = 0; // 默认选择第一个时间段
    let selectedHourList = hourList;
    
    // 如果今天没有可用时间段，自动选择明天
    if (hourList.length === 0 && dayList.length > 1) {
      // 自动切换到明天
      defaultDayIndex = 1; // 选择明天
      selectedHourList = this.generateHourListForDay(1); // 生成明天的时间列表
      
      // console.log('今天已无可用时间段，自动切换到明天');
    }
    
    // 初始化要显示的默认数据
    let selectedDate;
    let selectedTime;
    
    if (this.data.selectedDate && this.data.selectedTime) {
      // 已有选中的日期和时间
      selectedDate = this.data.selectedDate;
      selectedTime = this.data.selectedTime;
    } else {
      // 默认选择当前日期索引对应的日期和第一个可用时间段
      selectedDate = this.getDateString(defaultDayIndex);
      selectedTime = selectedHourList.length > 0 ? selectedHourList[0] : '';
    }
    
    // 设置数据
    this.setData({
      monthList: monthList,
      dayList: dayList,
      hourList: selectedHourList,
      multiArray: [monthList, dayList, selectedHourList],
      multiIndex: [0, defaultDayIndex, defaultTimeIndex],
      selectedDate: selectedDate,
      selectedTime: selectedTime
    });
  },
  
  // 获取指定偏移天数的日期字符串
  getDateString(dayOffset) {
    const date = new Date();
    date.setDate(date.getDate() + dayOffset);
    
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    
    return `${year}-${month}-${day}`;
  },
  
  // 多列选择器列变化事件
  bindMultiPickerColumnChange(e) {
    console.log('修改的列为', e.detail.column, '，值为', e.detail.value);
    
    // 获取当前的multiIndex
    const data = {
      multiArray: this.data.multiArray,
      multiIndex: this.data.multiIndex
    };
    
    // 更新选中的索引
    data.multiIndex[e.detail.column] = e.detail.value;
    
    // 如果修改的是日期列(第二列)，需要更新时间列表
    if (e.detail.column === 1) {
      // 获取选择的日期偏移量（0代表今天，1代表明天，以此类推）
      const selectedDayOffset = e.detail.value;
      
      // 根据选择的日期生成时间列表
      const hourList = this.generateHourListForDay(selectedDayOffset);
      
      // 如果选择的是今天，但今天已无可用时间，自动切换到明天
      if (selectedDayOffset === 0 && hourList.length === 0 && data.multiArray[1].length > 1) {
        // 切换到明天
        data.multiIndex[1] = 1; // 选择明天
        const tomorrowHourList = this.generateHourListForDay(1);
        data.multiArray[2] = tomorrowHourList;
        data.multiIndex[2] = 0; // 选择第一个时间段
        
        // 设置数据并提示用户
        this.setData(data);
        
        wx.showToast({
          title: '今日已无可预约时段，已切换到明天',
          icon: 'none',
          duration: 2000
        });
        
        return;
      }
      
      // 更新时间列数据
      data.multiArray[2] = hourList;
      
      // 如果当前选中的时间超出了新的时间列表长度，重置为第一个时间
      if (data.multiIndex[2] >= hourList.length) {
        data.multiIndex[2] = 0;
      }
    }
    
    this.setData(data);
  },
  
  // 根据日期偏移生成对应的时间列表
  generateHourListForDay(dayOffset) {
    const hourList = [];
    const now = new Date();
    
    // 判断选择的日期是否是今天
    const isToday = dayOffset === 0;
    
    // 当前时间的小时和分钟
    const currentHour = now.getHours();
    const currentMinute = now.getMinutes();
    
    // 生成时间段列表（9:00-20:00，每30分钟一个时间段）
    for (let hour = 9; hour <= 20; hour++) {
      for (let minute of [0, 30]) {
        // 最后一个时间段是20:00，不需要20:30
        if (hour === 20 && minute === 30) continue;
        
        // 格式化时间字符串
        const timeStr = `${hour}:${minute.toString().padStart(2, '0')}`;
        
        // 判断是否应该添加这个时间段
        let shouldAdd = true;
        
        // 如果是今天，检查时间是否已过
        if (isToday) {
          if (hour < currentHour) {
            // 小时已过
            shouldAdd = false;
          } else if (hour === currentHour && minute <= currentMinute) {
            // 同一小时，但分钟已过或等于当前分钟
            shouldAdd = false;
          }
        }
        
        if (shouldAdd) {
          hourList.push(timeStr);
        }
      }
    }
    
    // 不再显示提示，因为我们已经在initMultiSelector中处理了自动切换到明天的逻辑
    
    return hourList;
  },
  
  // 多列选择器值变化事件
  bindMultiPickerChange(e) {
    console.log('picker发送选择改变，携带值为', e.detail.value);
    
    // 获取选择的月份、日期和时间
    const monthIndex = e.detail.value[0];
    const dayIndex = e.detail.value[1];
    const timeIndex = e.detail.value[2];
    
    // 生成日期字符串
    const selectedDate = this.getDateString(dayIndex);
    
    // 获取选中的时间
    const timeStr = this.data.multiArray[2][timeIndex];
    
    console.log('选中的日期：', selectedDate);
    console.log('选中的时间：', timeStr);
    
    // 校验选择的时间是否有效
    if (dayIndex === 0) { // 如果选择的是今天
      const now = new Date();
      const currentHour = now.getHours();
      const currentMinute = now.getMinutes();
      
      // 解析选中的时间
      const [hourStr, minuteStr] = timeStr.split(':');
      const hour = parseInt(hourStr, 10);
      const minute = parseInt(minuteStr, 10);
      
      // 检查选中的时间是否已过
      if (hour < currentHour || (hour === currentHour && minute <= currentMinute)) {
        // 自动切换到明天的第一个时间段
        if (this.data.dayList.length > 1) { // 确保有明天可选
          const tomorrowHourList = this.generateHourListForDay(1);
          if (tomorrowHourList.length > 0) {
            const tomorrowDate = this.getDateString(1);
            const tomorrowFirstTime = tomorrowHourList[0];
            
            this.setData({
              selectedDate: tomorrowDate,
              selectedTime: tomorrowFirstTime,
              multiIndex: [monthIndex, 1, 0], // 选择明天的第一个时间段
              hourList: tomorrowHourList,
              multiArray: [this.data.monthList, this.data.dayList, tomorrowHourList]
            });
            
            // 提示用户已自动切换到明天
            wx.showToast({
              title: '已为您切换到明天可用时段',
              icon: 'none',
              duration: 2000
            });
            
            // 更新可提交状态
            this.updateSubmitStatus();
            return;
          }
        }
      }
    }
    
    this.setData({
      selectedDate: selectedDate,
      selectedTime: timeStr,
      multiIndex: [monthIndex, dayIndex, timeIndex]
    });
    
    // 更新可提交状态
    this.updateSubmitStatus();
  },

  // 关闭登录提示弹窗
  closeLoginModal() {
    this.setData({
      showLoginModal: false
    });
  },
  
  // 微信一键登录
  onGetUserInfo(e) {
    if (e.detail.userInfo) {
      // 显示加载中提示
      wx.showLoading({
        title: '登录中...',
        mask: true
      });

      // 获取到用户基本信息
      const basicUserInfo = e.detail.userInfo;

      // 设置默认值
      if (!basicUserInfo.nickName) {
        basicUserInfo.nickName = '昵称';
      }

      // 使用微信图标作为默认头像
      if (!basicUserInfo.customAvatar) {
        basicUserInfo.avatarUrl = '/static/微信图标.png';
      }

      // 临时保存基本信息到全局（后面会用云端完整数据替换）
      const app = getApp();
      app.globalData.userInfo = basicUserInfo;
      
      // 显示已登录状态并关闭登录弹窗
      this.setData({
        isLoggedIn: true,
        showLoginModal: false
      });

      // 调用云函数保存基本用户信息，同时获取用户openid
      wx.cloud.callFunction({
        name: 'userManager',
        data: {
          type: 'frontend',
          action: 'saveUserInfo',
          userInfo: basicUserInfo
        },
        success: res => {
          console.log('保存用户基本信息成功', res);
          if (res.result && res.result.code === 0) {
            // 保存openid到全局变量
            if (res.result.data && res.result.data.openid) {
              app.globalData.openid = res.result.data.openid;
            }
            
            // 获取用户完整信息（包括自定义头像等）
            this.fetchUserDetailAfterLogin();
          } else {
            // 隐藏加载提示
            wx.hideLoading();
            
            // 显示成功提示并恢复预约弹窗
            wx.showToast({
              title: '登录成功',
              icon: 'success',
              duration: 2000,
              success: () => {
                // 延迟恢复预约弹窗
                this.restoreAppointmentModalAfterLogin();
              }
            });
          }
        },
        fail: err => {
          console.error('保存用户信息失败', err);
          
          // 隐藏加载提示
          wx.hideLoading();
          
          // 显示成功提示并恢复预约弹窗
          wx.showToast({
            title: '登录成功',
            icon: 'success',
            duration: 2000,
            success: () => {
              // 延迟恢复预约弹窗
              this.restoreAppointmentModalAfterLogin();
            }
          });
        }
      });
    } else {
      console.log('用户拒绝授权');
    }
  },
  
  // 登录后恢复预约弹窗或继续提交预约
  restoreAppointmentModalAfterLogin() {
    setTimeout(() => {
      if (this.data.tempCurrentService) {
        // 检查当前是否已经在预约弹窗中（即用户已经填写了预约信息）
        if (this.data.showModal && this.data.currentService) {
          // 用户在预约弹窗中登录，登录后直接继续提交预约
          console.log('[Appointment] 登录成功，继续提交预约');
          this.setData({
            tempCurrentService: null,
            isLoggedIn: true
          });
          // 延迟一点时间让登录成功提示显示完毕，然后继续提交
          setTimeout(() => {
            this.submitAppointment();
          }, 500);
        } else {
          // 用户在点击预约按钮时登录，登录后打开预约弹窗
          console.log('[Appointment] 登录成功，打开预约弹窗');
          this.setData({
            currentService: this.data.tempCurrentService,
            showModal: true,
            tempCurrentService: null,
            showStaffSelection: true // 确保服务人员选择区域默认展开
          });

          // 隐藏TabBar导航栏
          this.hideTabBar();

          // 获取员工列表
          this.fetchStaffList();

          // 获取可用时间段
          this.fetchAvailableTimeSlots();

          // 延时触发向上滑入动画
          setTimeout(() => {
            this.setData({
              showAppointmentAnimation: true // 触发向上滑入动画
            });
          }, 50); // 短暂延时确保DOM已渲染
        }
      }
    }, 2000); // 等待登录成功提示显示完毕
  },
  
  // 登录后获取用户完整信息
  fetchUserDetailAfterLogin() {
    const app = getApp();
    
    wx.cloud.callFunction({
      name: 'userManager',
      data: {
        type: 'frontend',
        action: 'getUserDetail'
      },
      success: res => {
        console.log('获取用户完整信息', res);
        
        // 隐藏加载提示
        wx.hideLoading();
        
        if (res.result && res.result.code === 0 && res.result.data) {
          // 获取到了用户的完整信息（包括自定义头像、昵称和手机号）
          const fullUserInfo = res.result.data;
          console.log('云端用户完整信息:', fullUserInfo);

          // 确保用户信息完整
          if (!fullUserInfo.nickName) {
            fullUserInfo.nickName = '昵称';
          }

          // 如果没有自定义头像，使用微信图标作为默认头像
          if (!fullUserInfo.customAvatar) {
            fullUserInfo.avatarUrl = '/static/微信图标.png';
          }

          // 检查手机号
          if (fullUserInfo.phoneNumber) {
            this.setData({
              phoneNumber: fullUserInfo.phoneNumber
            });
          }

          // 更新全局数据 - 以云端数据为准
          app.globalData.userInfo = fullUserInfo;

          // 保存到本地存储
          wx.setStorageSync('userInfo', fullUserInfo);
        } else {
          // 如果没有获取到完整信息，使用基本信息
          console.log('未找到云端信息，使用基本信息');

          // 保存到本地存储
          const basicUserInfo = app.globalData.userInfo || {};

          // 设置默认值
          if (!basicUserInfo.nickName) {
            basicUserInfo.nickName = '昵称';
          }

          // 使用微信图标作为默认头像
          if (!basicUserInfo.customAvatar) {
            basicUserInfo.avatarUrl = '/static/微信图标.png';
          }

          console.log('使用基本信息并设置默认头像:', basicUserInfo);

          // 更新全局数据
          app.globalData.userInfo = basicUserInfo;

          wx.setStorageSync('userInfo', basicUserInfo);
        }
        
        // 登录成功后，检查用户是否有未完成的预约
        wx.cloud.callFunction({
          name: 'appointmentManager',
          data: {
            type: 'frontend',
            action: 'checkExistingAppointments',
            date: '',
            time: ''
          }
        }).then(checkRes => {
          console.log('登录后检查预约状态结果:', checkRes.result);
          
          if (checkRes.result && checkRes.result.code === 0) {
            // 更新本地存储和全局状态
            wx.setStorageSync('hasActiveAppointment', checkRes.result.hasExisting);
            if (app.globalData) {
              app.globalData.hasActiveAppointment = checkRes.result.hasExisting;
            }
          }
          
          // 登录成功提示
          wx.showToast({
            title: '登录成功',
            icon: 'success',
            duration: 1500,
            success: () => {
              // 延迟恢复预约弹窗
              setTimeout(() => {
                this.restoreAppointmentModalAfterLogin();
              }, 100); // 短暂延迟，让登录成功提示显示一会
            }
          });
        }).catch(err => {
          console.error('登录后检查预约状态失败:', err);
          
          // 直接显示登录成功提示
          wx.showToast({
            title: '登录成功',
            icon: 'success',
            duration: 1500,
            success: () => {
              // 延迟恢复预约弹窗
              setTimeout(() => {
                this.restoreAppointmentModalAfterLogin();
              }, 100); // 短暂延迟，让登录成功提示显示一会
            }
          });
        });
      },
      fail: err => {
        console.error('获取用户完整信息失败', err);
        
        // 隐藏加载提示
        wx.hideLoading();
        
        // 显示登录成功提示
        wx.showToast({
          title: '登录成功',
          icon: 'success',
          duration: 1500,
          success: () => {
            // 延迟恢复预约弹窗
            setTimeout(() => {
              this.restoreAppointmentModalAfterLogin();
            }, 100); // 短暂延迟，让登录成功提示显示一会
          }
        });
      }
    });
  },
  
  // 保存用户信息到云端
  saveUserInfo(userInfo) {
    wx.cloud.callFunction({
      name: 'userManager',
      data: {
        type: 'frontend',
        userInfo: userInfo
      },
      success: res => {
        console.log('保存用户信息成功:', res);
      },
      fail: err => {
        console.error('保存用户信息失败:', err);
      }
    });
  },

  // 打开腾讯地图
  openLocation() {
    wx.openLocation({
      latitude: 23.144894,  // 广州越秀区广园西路121号美博城的纬度
      longitude: 113.270213, // 广州越秀区广园西路121号美博城的经度
      name: '指家科技',
      address: '广州市越秀区广园西路121号美博城',
      scale: 18
    })
  },

  // 处理服务分享按钮点击
  onShareService(e) {
    // 获取服务信息
    const serviceInfo = e.currentTarget.dataset.service;
    console.log('分享服务按钮点击，服务信息:', serviceInfo);
    
    // 验证服务信息完整性
    if (!serviceInfo || (!serviceInfo._id && !serviceInfo.id)) {
      console.error('分享的服务信息不完整');
      return;
    }
    
    const serviceId = serviceInfo._id || serviceInfo.id;
    console.log('准备分享服务，ID:', serviceId);
  },

  // 分享小程序
  onShareAppMessage(options) {
    console.log('触发分享事件:', options);
    
    // 引入设备工具函数
    const deviceUtils = require('../../utils/device.js');
    
    // 判断是否是从分享按钮触发
    if (options.from === 'button') {
      // 获取按钮上的数据
      let serviceInfo = options.target.dataset.service || options.target.dataset.video;
      console.log('分享服务信息:', serviceInfo);
      
      if (!serviceInfo || (!serviceInfo._id && !serviceInfo.id)) {
        console.error('分享的服务信息不完整');
        return {
          title: '预约服务',
          path: '/pages/appointment/appointment'
        };
      }
      
      const serviceId = serviceInfo._id || serviceInfo.id;
      const shareTitle = serviceInfo.name || serviceInfo.title || '精选服务';
      const shareImage = serviceInfo.coverUrl || serviceInfo.image || '/static/default-service.png';
      
      console.log('构建分享参数:', {
        contentType: 'service',
        contentId: serviceId,
        title: shareTitle,
        path: `/pages/appointment/appointment?serviceId=${serviceId}`,
        imageUrl: shareImage
      });
      
      // 使用全局分享辅助函数（同步版本）
      return getApp().shareWithPointsSync({
        contentType: 'service',
        contentId: serviceId,
        title: shareTitle,
        path: `/pages/appointment/appointment?serviceId=${serviceId}`,
        imageUrl: shareImage
      });
    }
    
    // 默认分享内容
    return {
      title: '预约服务',
      path: '/pages/appointment/appointment'
    };
  },

  // 获取员工列表
  fetchStaffList() {
    this.setData({ loadingStaff: true });
    
    wx.cloud.callFunction({
      name: 'staffManager',
      data: {
        type: 'admin',
        action: 'getStaffList',
        data: {
          status: 'active' // 只获取状态为正常的员工
        }
      }
    }).then(res => {
      console.log('获取员工列表成功：', res);
      
      if (res.result && res.result.code === 0) {
        const staffList = res.result.data.list || [];
        
        // 处理员工状态显示
        staffList.forEach(staff => {
          // 如果没有serviceStatus字段，默认为available
          if (!staff.serviceStatus) {
            staff.serviceStatus = 'available';
          }
        });
        
        // 按状态排序：空闲的排在前面
        staffList.sort((a, b) => {
          if (a.serviceStatus === 'available' && b.serviceStatus !== 'available') return -1;
          if (a.serviceStatus !== 'available' && b.serviceStatus === 'available') return 1;
          return 0;
        });
        
        this.setData({
          staffList: staffList,
          loadingStaff: false
        });
      } else {
        this.setData({
          staffList: [],
          loadingStaff: false
        });
        
        console.error('获取员工列表失败：', res.result?.message || '未知错误');
      }
    }).catch(err => {
      console.error('获取员工列表失败：', err);
      this.setData({
        staffList: [],
        loadingStaff: false
      });
    });
  },

  // 展开/折叠员工选择区域
  toggleStaffSelection() {
    const currentState = this.data.showStaffSelection;
    
    // 更新显示状态
    this.setData({
      showStaffSelection: !currentState
    });
    
    // 如果是展开员工选择面板，则立即获取最新的员工列表
    if (!currentState) {
      this.fetchStaffList();
    }
  },

  // 选择员工
  selectStaff(e) {
    const { id, name } = e.currentTarget.dataset;
    
    // 如果已经选中，则取消选择
    if (this.data.selectedStaffId === id) {
      this.clearStaffSelection();
      return;
    }
    
    this.setData({
      selectedStaffId: id,
      selectedStaffName: name
    });
    
    wx.showToast({
      title: `已选择${name}`,
      icon: 'none',
      duration: 1500
    });
  },

  // 清除员工选择
  clearStaffSelection() {
    this.setData({
      selectedStaffId: '',
      selectedStaffName: ''
    });
    
    wx.showToast({
      title: '已清除选择',
      icon: 'none',
      duration: 1500
    });
  },

  // 获取我的预约列表 - 添加空实现，避免报错
  getMyAppointments() {
    console.log('getMyAppointments方法被调用，但此页面不需要实际实现');
    // 在预约页面不需要实际实现此方法，只是为了避免调用时报错
  },

  // 切换服务下拉菜单显示状态
  toggleServiceDropdown() {
    this.setData({
      showServiceDropdown: !this.data.showServiceDropdown,
      dropdownOpen: !this.data.dropdownOpen
    });
  },

  // 切换图片上传区域显示状态
  toggleImageUpload: function() {
    this.setData({
      showImageUpload: !this.data.showImageUpload
    });
  },
  
  // 选择图片
  chooseImage: function() {
    const that = this;
    if (that.data.uploadedImages.length >= 3) {
      wx.showToast({
        title: '最多上传3张图片',
        icon: 'none'
      });
      return;
    }
    
    wx.chooseImage({
      count: 3 - that.data.uploadedImages.length,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: function(res) {
        that.setData({
          uploadedImages: [...that.data.uploadedImages, ...res.tempFilePaths]
        });
      }
    });
  },

  // 删除图片
  deleteImage: function(e) {
    const index = e.currentTarget.dataset.index;
    const images = [...this.data.uploadedImages];
    images.splice(index, 1);
    this.setData({
      uploadedImages: images
    });
  },

  // 预览图片
  previewImage: function(e) {
    const index = e.currentTarget.dataset.index;
    wx.previewImage({
      current: this.data.uploadedImages[index],
      urls: this.data.uploadedImages
    });
  },

  // 切换服务
  switchService(e) {
    const service = e.currentTarget.dataset.service;
    
    // 更新当前服务
    this.setData({
      currentService: service,
      showServiceDropdown: false // 选择后关闭下拉菜单
    });
    
    // 如果已经选择了日期，重新获取可用时间段
    if (this.data.selectedDate) {
      this.fetchAvailableTimeSlots();
    }
    
    // 更新提交状态
    this.updateSubmitStatus();
  },

  // 轮播图切换事件处理函数
  onCarouselChange(e) {
    const current = e.detail.current;
    this.setData({
      currentCarouselIndex: current
    });
  },

  // 显示服务详情图片
  showServiceDetail(e) {
    const detailImage = e.currentTarget.dataset.detailImage;

    if (!detailImage) {
      wx.showToast({
        title: '暂无详情图片',
        icon: 'none'
      });
      return;
    }

    // 直接使用页面数据显示弹窗
    this.setData({
      showDetailModal: true,
      detailImageUrl: detailImage
    });
  },

  // 关闭详情图片弹窗
  closeDetailModal() {
    this.setData({
      showDetailModal: false,
      detailImageUrl: ''
    });
  },

  // 阻止点击图片时关闭弹窗
  preventClose(e) {
    // 添加安全检查，确保事件对象存在且有stopPropagation方法
    if (e && typeof e.stopPropagation === 'function') {
      e.stopPropagation();
    }
  },

  // 详情图片加载成功
  onDetailImageLoad() {
    // 图片加载成功
  },

  // 详情图片加载失败
  onDetailImageError(e) {
    wx.showToast({
      title: '图片加载失败',
      icon: 'none'
    });
    this.closeDetailModal();
  },
  
  // 处理轮播图图片加载错误
  onCarouselImageError(e) {
    const index = e.currentTarget.dataset.index;
    console.log(`轮播图 ${index} 图片加载失败`);

    // 修改当前轮播图的imageUrl为空字符串
    const carousels = this.data.carousels;
    if (carousels && carousels[index]) {
      carousels[index].imageUrl = '';
      this.setData({ carousels });
    }
  },

  // 设置指向广告事件监听
  setupTargetAdListener() {
    const app = getApp();
    if (app.globalData.eventCenter) {
      app.globalData.eventCenter.on('showTargetAd', (data) => {
        console.log('[Appointment] 收到显示指向广告事件:', data);

        // 只在当前页面是目标页面时显示广告
        const currentRoute = 'pages/appointment/appointment';
        if (!data.targetPage || data.targetPage === currentRoute) {
          this.setData({
            showTargetAd: true,
            targetAdData: {
              imageUrl: data.imageUrl || '',
              jumpUrl: data.jumpUrl || ''
            }
          });
        }
      });

      // 监听全局关闭广告事件
      app.globalData.eventCenter.on('closeTargetAdGlobally', () => {
        console.log('[Appointment] 收到全局关闭广告事件');
        this.setData({
          showTargetAd: false,
          targetAdData: {
            imageUrl: '',
            jumpUrl: ''
          }
        });
      });
    }
  },

  // 指向广告关闭事件
  onTargetAdClose() {
    console.log('[Appointment] 指向广告关闭');
    this.setData({
      showTargetAd: false,
      targetAdData: {
        imageUrl: '',
        jumpUrl: ''
      }
    });
  }
})