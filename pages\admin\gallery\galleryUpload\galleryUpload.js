Component({
  /**
   * 组件的属性列表
   */
  properties: {
    visible: {
      type: Boolean,
      value: false,
      observer: function(newVal) {
        if (newVal) {
          console.log('模态框显示, isEdit:', this.data.isEdit);
          this.setData({
            visible: true
          });
          
          // 如果是编辑模式且有galleryInfo，加载画廊信息
          if (this.data.isEdit && this.properties.galleryInfo) {
            console.log('编辑模式，重新处理画廊信息');
            // 重新处理galleryInfo，确保数据正确加载
            this.processGalleryInfo(this.properties.galleryInfo);
          }
        }
      }
    },
    galleryInfo: {
      type: Object,
      value: null,
      observer: function(newVal) {
        if (newVal) {
          console.log('接收到画廊信息:', newVal);
          
          this.setData({
            isEdit: true,
            galleryId: newVal._id || '',
            mainTitle: newVal.mainTitle || '',
            subTitle: newVal.subTitle || '',
            coverUrl: newVal.coverUrl || '',
            detailImages: newVal.detailImages || [],
            selectedCategoryId: newVal.categoryId || '',
            selectedCategoryName: newVal.categoryId ? '加载中...' : '请选择分类'
          });
          
          // 如果有分类ID，获取分类名称
          if (newVal.categoryId) {
            this.updateCategoryName(newVal.categoryId);
          }
          
          // 设置同样的数据到galleryData属性，保持一致性
          this.properties.galleryData = newVal;
        }
      }
    },
    editMode: {
      type: Boolean,
      value: false
    },
    galleryData: {
      type: Object,
      value: null
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    isEdit: false,
    galleryId: '',
    coverUrl: '',
    mainTitle: '',
    subTitle: '',
    detailImages: [],
    isLoading: false,
    isSaving: false,
    tempCoverUrl: '',
    maxDetailImages: 9, // 最大详情图片数
    uploadProgress: 0,  // 上传进度
    categories: [], // 分类列表
    selectedCategoryId: '', // 选中的分类ID
    selectedCategoryName: '请选择分类', // 选中的分类名称
    showCategoryPicker: false, // 是否显示分类选择器
    showCategoryManagerModal: false, // 是否显示分类管理器
    newCategoryName: '', // 新分类名称
    isAddingCategory: false, // 是否正在添加新分类
    isDeletingCategory: false, // 是否正在删除分类
  },

  lifetimes: {
    attached() {
      // 加载分类列表
      this.loadCategories();
    }
  },

  observers: {
    'visible': function(visible) {
      if (visible) {
        this.initData();
      }
    },
    'galleryData': function(galleryData) {
      if (galleryData && this.properties.editMode) {
        this.setData({
          mainTitle: galleryData.mainTitle || '',
          subTitle: galleryData.subTitle || '',
          coverUrl: galleryData.coverUrl || '',
          detailImages: galleryData.detailImages || [],
          selectedCategoryId: galleryData.categoryId || '',
          selectedCategoryName: '加载中...'
        });
        
        // 如果有分类ID，获取分类名称
        if (galleryData.categoryId) {
          this.updateCategoryName(galleryData.categoryId);
        }
      }
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 打开上传模态框（新增模式）
    open: function() {
      this.setData({
        visible: true,
        isEdit: false,
        galleryId: '',
        coverUrl: '',
        mainTitle: '',
        subTitle: '',
        detailImages: [],
        isLoading: false,
        isSaving: false,
        tempCoverUrl: '',
        uploadProgress: 0
      });
    },
    
    // 打开编辑模态框（编辑模式）
    openEdit: function(galleryId) {
      this.setData({
        visible: true,
        isEdit: true,
        galleryId: galleryId,
        isLoading: true,
        isSaving: false,
        uploadProgress: 0
      });
      
      this.loadGalleryDetail(galleryId);
    },
    
    // 加载画廊详情数据
    loadGalleryDetail: function(galleryId) {
      wx.cloud.callFunction({
        name: 'galleryManager',
        data: {
          action: 'getGalleryDetail',
          data: {
            _id: galleryId
          }
        }
      }).then(res => {
        const result = res.result;
        if (result.success) {
          const data = result.data;
          this.setData({
            mainTitle: data.mainTitle || '',
            subTitle: data.subTitle || '',
            coverUrl: data.coverUrl || '',
            detailImages: data.detailImages || [],
            selectedCategoryId: data.categoryId || '',
            isLoading: false
          });
          
          // 如果有分类ID，获取分类名称
          if (data.categoryId) {
            this.updateCategoryName(data.categoryId);
          }
        } else {
          this.showToast('获取画廊详情失败：' + result.message);
          this.close();
        }
      }).catch(err => {
        console.error('获取画廊详情失败', err);
        this.showToast('获取画廊详情失败，请稍后重试');
        this.close();
      });
    },
    
    // 关闭模态框
    close: function() {
      // 如果正在保存或加载中，则不允许关闭
      if (this.data.isSaving || this.data.isLoading) return;
      
      this.setData({
        visible: false
      });
      
      // 触发关闭事件
      this.triggerEvent('close');
    },
    
    // 关闭模态框 - 新方法，用于wxml中的onClose绑定
    onClose: function() {
      this.close();
    },
    
    // 点击遮罩层关闭
    onMaskTap: function() {
      this.close();
    },
    
    // 防止冒泡
    onContentTap: function(e) {
      // 在微信小程序中，有些事件可能没有stopPropagation方法
      // 使用catchtap在wxml中已经阻止了冒泡，这里不需要额外处理
      // 如果需要额外处理，可以使用return false
      return false;
    },
    
    // 上传封面图
    uploadCover: function() {
      wx.chooseMedia({
        count: 1,
        mediaType: ['image'],
        sourceType: ['album', 'camera'],
        success: (res) => {
          const tempFilePath = res.tempFiles[0].tempFilePath;
          this.setData({
            tempCoverUrl: tempFilePath
          });
          
          // 直接上传到云存储
          this.uploadImageToCloud(tempFilePath, 'cover');
        }
      });
    },
    
    // 上传详情图片
    uploadDetailImages: function() {
      const { detailImages, maxDetailImages } = this.data;
      const remainingCount = maxDetailImages - detailImages.length;
      
      if (remainingCount <= 0) {
        this.showToast(`最多只能上传${maxDetailImages}张详情图片`);
        return;
      }
      
      wx.chooseMedia({
        count: remainingCount,
        mediaType: ['image'],
        sourceType: ['album', 'camera'],
        success: (res) => {
          const tempFilePaths = res.tempFiles.map(file => file.tempFilePath);
          
          // 上传所有选择的图片
          Promise.all(tempFilePaths.map(path => this.uploadFile(path)))
            .then(fileIDs => {
              // 更新详情图片列表
              this.setData({
                detailImages: [...this.data.detailImages, ...fileIDs]
              });
            })
            .catch(err => {
              console.error('上传详情图片失败', err);
              this.showToast('上传详情图片失败，请稍后重试');
            });
        }
      });
    },
    
    // 上传图片到云存储
    uploadImageToCloud: function(filePath, type) {
      const cloudPath = `gallery/${Date.now()}-${Math.floor(Math.random() * 1000)}${filePath.match(/\.[^.]+?$/)[0]}`;
      
      wx.cloud.uploadFile({
        cloudPath,
        filePath,
        success: res => {
          const fileID = res.fileID;
          
          if (type === 'cover') {
            this.setData({
              coverUrl: fileID
            });
          } else if (type === 'detail') {
            this.setData({
              detailImages: [...this.data.detailImages, fileID]
            });
          }
        },
        fail: err => {
          console.error('上传图片失败', err);
          this.showToast('上传图片失败，请稍后重试');
        }
      });
    },
    
    // 显示提示
    showToast: function(title, icon = 'none') {
      wx.showToast({
        title,
        icon,
        duration: 2000
      });
    },
    
    // 加载分类列表
    loadCategories() {
      wx.cloud.callFunction({
        name: 'galleryManager',
        data: {
          action: 'getCategoryList'
        }
      }).then(res => {
        const result = res.result;
        if (result.success) {
          this.setData({
            categories: result.data || []
          });
          
          // 如果是编辑模式且已选择分类，更新分类名称
          if (this.data.isEdit && this.data.selectedCategoryId) {
            this.updateCategoryName(this.data.selectedCategoryId);
          }
        } else {
          console.error('获取分类列表失败', result.message);
        }
      }).catch(err => {
        console.error('获取分类列表失败', err);
      });
    },
    
    // 更新分类名称
    updateCategoryName(categoryId) {
      const { categories } = this.data;
      const category = categories.find(item => item._id === categoryId);
      
      if (category) {
        this.setData({
          selectedCategoryName: category.name
        });
      } else {
        // 如果在当前列表中找不到，尝试从服务器获取
        wx.cloud.callFunction({
          name: 'galleryManager',
          data: {
            action: 'getCategoryById',
            data: {
              categoryId
            }
          }
        }).then(res => {
          const result = res.result;
          if (result.success && result.data) {
            this.setData({
              selectedCategoryName: result.data.name
            });
          } else {
            this.setData({
              selectedCategoryName: '未知分类'
            });
          }
        }).catch(err => {
          console.error('获取分类信息失败', err);
          this.setData({
            selectedCategoryName: '未知分类'
          });
        });
      }
    },
    
    // 显示分类选择器
    showCategorySelector() {
      this.setData({
        showCategoryPicker: true
      });
    },
    
    // 隐藏分类选择器
    hideCategorySelector() {
      this.setData({
        showCategoryPicker: false
      });
    },
    
    // 选择分类
    onSelectCategory(e) {
      const { id, name } = e.currentTarget.dataset;
      
      this.setData({
        selectedCategoryId: id,
        selectedCategoryName: name,
        showCategoryPicker: false
      });
    },
    
    // 显示分类管理器
    showCategoryManager() {
      this.setData({
        showCategoryManagerModal: true
      });
    },
    
    // 隐藏分类管理器
    hideCategoryManager() {
      this.setData({
        showCategoryManagerModal: false,
        newCategoryName: ''
      });
      
      // 重新加载分类列表
      this.loadCategories();
    },
    
    // 添加新分类
    addNewCategory() {
      const { newCategoryName } = this.data;
      
      if (!newCategoryName.trim()) {
        this.showToast('请输入分类名称');
        return;
      }
      
      this.setData({
        isAddingCategory: true
      });
      
      wx.cloud.callFunction({
        name: 'galleryManager',
        data: {
          action: 'addCategory',
          data: {
            name: newCategoryName.trim()
          }
        }
      }).then(res => {
        const result = res.result;
        if (result.success) {
          this.showToast('添加分类成功', 'success');
          
          // 更新分类列表
          this.loadCategories();
          
          // 清空输入框
          this.setData({
            newCategoryName: ''
          });
        } else {
          this.showToast('添加分类失败：' + result.message);
        }
      }).catch(err => {
        console.error('添加分类失败', err);
        this.showToast('添加分类失败，请稍后重试');
      }).finally(() => {
        this.setData({
          isAddingCategory: false
        });
      });
    },
    
    // 直接添加分类（如果不存在）
    addCategoryDirectly(name) {
      return new Promise((resolve, reject) => {
        // 检查是否已存在同名分类
        const { categories } = this.data;
        const existingCategory = categories.find(item => item.name === name);
        
        if (existingCategory) {
          resolve(existingCategory._id);
          return;
        }
        
        // 添加新分类
        wx.cloud.callFunction({
          name: 'galleryManager',
          data: {
            action: 'addCategory',
            data: {
              name
            }
          }
        }).then(res => {
          const result = res.result;
          if (result.success) {
            // 更新分类列表
            this.loadCategories();
            
            // 返回新分类ID
            resolve(result.data._id);
          } else {
            reject(new Error(result.message));
          }
        }).catch(err => {
          console.error('添加分类失败', err);
          reject(err);
        });
      });
    },
    
    // 删除分类
    deleteCategory(e) {
      const categoryId = e.currentTarget.dataset.id;
      
      wx.showModal({
        title: '确认删除',
        content: '确定要删除该分类吗？如果该分类下有画廊项目，将无法删除。',
        success: (res) => {
          if (res.confirm) {
            this.setData({
              isDeletingCategory: true
            });
            
            wx.cloud.callFunction({
              name: 'galleryManager',
              data: {
                action: 'deleteCategory',
                data: {
                  _id: categoryId
                }
              }
            }).then(res => {
              const result = res.result;
              if (result.success) {
                this.showToast('删除分类成功', 'success');
                
                // 更新分类列表
                this.loadCategories();
                
                // 如果当前选中的分类被删除，清空选择
                if (this.data.selectedCategoryId === categoryId) {
                  this.setData({
                    selectedCategoryId: '',
                    selectedCategoryName: '请选择分类'
                  });
                }
              } else {
                // 显示详细的错误信息
                const errorMsg = result && result.message ? result.message : '未知错误';
                this.showToast('删除分类失败：' + errorMsg);
              }
            }).catch(err => {
              console.error('删除分类失败', err);
              this.showToast('删除分类失败，请稍后重试');
            }).finally(() => {
              this.setData({
                isDeletingCategory: false
              });
            });
          }
        }
      });
    },
    
    // 监听新分类名称输入
    onInputNewCategoryName(e) {
      this.setData({
        newCategoryName: e.detail.value
      });
    },
    
    // 初始化数据
    initData() {
      if (!this.data.isEdit) {
        this.setData({
          galleryId: '',
          mainTitle: '',
          subTitle: '',
          coverUrl: '',
          detailImages: [],
          selectedCategoryId: '',
          selectedCategoryName: '请选择分类'
        });
      }
      
      // 加载分类列表
      this.loadCategories();
    },
    
    // 选择封面图片
    chooseCoverImage() {
      wx.chooseMedia({
        count: 1,
        mediaType: ['image'],
        sourceType: ['album', 'camera'],
        success: (res) => {
          const tempFilePath = res.tempFiles[0].tempFilePath;
          
          // 显示加载中
          wx.showLoading({
            title: '上传中...',
            mask: true
          });
          
          // 上传到云存储
          this.uploadFile(tempFilePath).then(fileID => {
            this.setData({
              coverUrl: fileID
            });
            wx.hideLoading();
          }).catch(err => {
            console.error('上传封面图片失败', err);
            this.showToast('上传封面图片失败，请稍后重试');
            wx.hideLoading();
          });
        }
      });
    },
    
    // 选择详情图片
    chooseDetailImages() {
      wx.chooseMedia({
        count: 1,
        mediaType: ['image'],
        sourceType: ['album', 'camera'],
        success: (res) => {
          const tempFilePath = res.tempFiles[0].tempFilePath;
          
          // 显示加载中
          wx.showLoading({
            title: '上传中...',
            mask: true
          });
          
          // 上传图片
          this.uploadFile(tempFilePath)
            .then(fileID => {
              // 替换详情图片列表，只保留一张
              this.setData({
                detailImages: [fileID]
              });
              wx.hideLoading();
            })
            .catch(err => {
              console.error('上传详情图片失败', err);
              this.showToast('上传详情图片失败，请稍后重试');
              wx.hideLoading();
            });
        }
      });
    },
    
    // 监听主标题输入
    onInputMainTitle(e) {
      this.setData({
        mainTitle: e.detail.value
      });
    },
    
    // 监听副标题输入
    onInputSubTitle(e) {
      this.setData({
        subTitle: e.detail.value
      });
    },
    
    // 保存画廊
    async saveGallery() {
      const { galleryId, mainTitle, subTitle, coverUrl, detailImages, selectedCategoryId, isEdit } = this.data;
      
      // 表单验证
      if (!mainTitle.trim()) {
        this.showToast('请输入主标题');
        return;
      }
      
      if (!selectedCategoryId) {
        this.showToast('请选择分类');
        return;
      }
      
      if (!coverUrl) {
        this.showToast('请上传封面图片');
        return;
      }
      
      this.setData({
        isSaving: true
      });
      
      try {
        let categoryId = selectedCategoryId;
        
        // 构建要保存的数据
        const galleryData = {
          mainTitle: mainTitle.trim(),
          subTitle: subTitle.trim(),
          coverUrl,
          detailImages,
          categoryId,
          updateTime: new Date()
        };
        
        // 如果是编辑模式，需要传入ID
        if (isEdit) {
          galleryData._id = galleryId;
        } else {
          // 新增模式，设置创建时间和可见性
          galleryData.createTime = new Date();
          galleryData.isVisible = true;
        }
        
        // 调用云函数保存数据
        const res = await wx.cloud.callFunction({
          name: 'galleryManager',
          data: {
            action: isEdit ? 'updateGallery' : 'addGallery',
            data: galleryData
          }
        });
        
        const result = res.result;
        
        if (result.success) {
          this.showToast(isEdit ? '更新成功' : '添加成功', 'success');
          
          // 关闭模态框
          this.close();
          
          // 触发成功事件
          this.triggerEvent('success', {
            galleryId: isEdit ? galleryId : result.data._id
          });
        } else {
          this.showToast((isEdit ? '更新' : '添加') + '失败：' + result.message);
        }
      } catch (err) {
        console.error('保存画廊失败', err);
        this.showToast('保存失败，请稍后重试');
      } finally {
        this.setData({
          isSaving: false
        });
      }
    },
    
    // 上传文件通用方法
    uploadFile(filePath) {
      return new Promise((resolve, reject) => {
        const cloudPath = `gallery/${Date.now()}-${Math.floor(Math.random() * 1000)}${filePath.match(/\.[^.]+?$/)[0]}`;
        
        wx.cloud.uploadFile({
          cloudPath,
          filePath,
          success: res => {
            resolve(res.fileID);
          },
          fail: err => {
            reject(err);
          }
        });
      });
    },
    
    // 处理画廊信息
    processGalleryInfo(galleryInfo) {
      if (galleryInfo) {
        this.setData({
          isEdit: true,
          galleryId: galleryInfo._id || '',
          mainTitle: galleryInfo.mainTitle || '',
          subTitle: galleryInfo.subTitle || '',
          coverUrl: galleryInfo.coverUrl || '',
          detailImages: galleryInfo.detailImages || [],
          selectedCategoryId: galleryInfo.categoryId || '',
          selectedCategoryName: galleryInfo.categoryId ? '加载中...' : '请选择分类'
        });
        
        // 如果有分类ID，获取分类名称
        if (galleryInfo.categoryId) {
          this.updateCategoryName(galleryInfo.categoryId);
        }
      }
    }
  }
}) 