/* 页面容器 */
.service-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
  position: relative;
  height: 100vh;
  overflow-y: scroll;
  -webkit-overflow-scrolling: touch;
}

/* 页面标题 */
.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  padding-top: 90rpx;
  background-color: #ffffff;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  position: sticky;
  top: 0;
  z-index: 100;
}

.back-btn, .add-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  color: #333333;
}

.back-icon, .add-icon {
  font-size: 40rpx;
  font-weight: bold;
}

.header-title {
  font-size: 34rpx;
  font-weight: bold;
  color: #333333;
  flex: 1;
  text-align: center;
}

/* 加载中 */
.loading-container {
  padding: 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.loading-spinner {
  width: 80rpx;
  height: 80rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #ff9a9e;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 20rpx;
  color: #999999;
  font-size: 28rpx;
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.5;
  font-size: 80rpx;
  color: #999;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2rpx dashed #999;
  border-radius: 50%;
}

.empty-text {
  font-size: 30rpx;
  color: #999;
  margin-bottom: 30rpx;
}

.empty-action {
  padding: 20rpx 40rpx;
  background-color: #333;
  color: #fff;
  border-radius: 40rpx;
  font-size: 28rpx;
}

/* 服务列表 */
.service-list {
  flex: 1;
  padding: 20rpx;
}

.service-items {
  display: flex;
  flex-direction: column;
}

.service-item {
  margin-bottom: 30rpx;
}

/* 服务卡片 */
.service-card {
  position: relative;
  background-color: #fff;
  border-radius: 8rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.08);
  padding: 15rpx;
  margin-bottom: 15rpx;
  overflow: hidden;
}

/* 服务内容容器 */
.service-content {
  display: flex;
  flex-direction: row;
  align-items: center;
}

/* 服务图片 */
.service-image {
  width: 100rpx;
  height: 100rpx;
  border-radius: 6rpx;
  object-fit: cover;
  margin-left: 15rpx;
  flex-shrink: 0;
}

/* 服务信息 */
.service-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* 服务标题 */
.service-header {
  display: flex;
  align-items: center;
  margin-bottom: 6rpx;
}

.service-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333333;
  line-height: 1.3;
}

/* 价格行 */
.service-price-row {
  display: flex;
  align-items: baseline;
  margin-bottom: 10rpx;
}

.service-price {
  font-size: 26rpx;
  color: #ff4d4f;
  font-weight: bold;
  margin-right: 8rpx;
}

.original-price {
  font-size: 20rpx;
  color: #999;
}

/* 操作按钮容器 */
.service-actions {
  margin-top: 5rpx;
}

.action-btns {
  display: flex;
  flex-wrap: wrap;
}

/* 按钮样式 */
.action-btn {
  margin-right: 10rpx;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  font-size: 20rpx;
  color: white;
  margin-bottom: 5rpx;
  line-height: 1.3;
}

.visibility-btn.show-btn {
  background-color: #67C23A;
}

.visibility-btn.hide-btn {
  background-color: #F56C6C;
}

.edit-btn {
  background-color: #409EFF;
}

.delete-btn {
  background-color: #F56C6C;
}

/* 可见性状态指示器 */
.visibility-indicator {
  position: absolute;
  top: 15rpx;
  right: 15rpx;
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  z-index: 2;
}

.visibility-indicator.visible {
  background-color: #67C23A;
  box-shadow: 0 0 4rpx #67C23A;
}

.visibility-indicator.hidden {
  background-color: #F56C6C;
  box-shadow: 0 0 4rpx #F56C6C;
}

/* 表单模态框 */
.form-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.form-container {
  width: 80%;
  max-height: 80%;
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;
}

.form-title {
  font-size: 34rpx;
  font-weight: bold;
  color: #333333;
}

.close-btn {
  font-size: 40rpx;
  color: #999;
}

.form-body {
  flex: 1;
  padding: 30rpx;
  overflow-y: auto;
}

.form-item {
  margin-bottom: 30rpx;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 15rpx;
}

.form-input {
  width: 100%;
  height: 80rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
  color: #333;
  background-color: #f9f9f9;
}

.form-textarea {
  width: 100%;
  min-height: 200rpx;
  max-height: 400rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
  line-height: 1.5;
  box-sizing: border-box;
  color: #333;
  background-color: #f9f9f9;
  word-break: break-all;
  word-wrap: break-word;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  /* 增强移动端可点击区域 */
  position: relative;
  z-index: 1;
}

.image-uploader {
  width: 100%;
  height: 300rpx;
  border: 1rpx dashed #ddd;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  background-color: #f9f9f9;
}

.uploaded-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 上传覆盖层样式 */
.upload-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(249, 249, 249, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.upload-icon {
  font-size: 60rpx;
  color: #ff9a9e;
  margin-bottom: 20rpx;
  font-weight: bold;
}

.upload-text {
  font-size: 30rpx;
  color: #333;
  margin-bottom: 10rpx;
  font-weight: 500;
}

.upload-hint {
  font-size: 24rpx;
  color: #999;
}

/* 编辑提示样式 */
.upload-edit-hint {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 15rpx 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
}

.edit-icon {
  font-size: 28rpx;
  color: #fff;
  margin-right: 10rpx;
}

.edit-text {
  font-size: 26rpx;
  color: #fff;
}

/* 底部添加按钮 */
.floating-add-button {
  position: fixed;
  left: 50%;
  bottom: 100rpx; /* 与底部保持安全距离 */
  transform: translateX(-50%); /* 水平居中 */
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  background-color: #0070c9; /* 修改为规范中的强调色/主按钮色 */
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.1); /* 规范中的按钮阴影 */
  z-index: 99;
  transition: all 0.2s ease;
}

.floating-add-button:active {
  transform: translateX(-50%) scale(0.92);
  opacity: 0.9;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.floating-add-icon {
  color: #ffffff;
  font-size: 70rpx;
  font-weight: bold;
  margin-bottom: 6rpx; /* 轻微下移，视觉上更居中 */
}

/* 底部安全区域 */
.safe-bottom-area {
  height: 160rpx; /* 为底部按钮留出空间 */
  width: 100%;
}

/* 价格信息容器 */
.price-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

/* 表单底部按钮 */
.form-footer {
  display: flex;
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #e0e0e0; /* 规范中的边框颜色 */
}

.form-btn {
  flex: 1;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4rpx; /* 规范中的按钮圆角 */
  font-size: 30rpx;
  transition: all 0.2s ease;
}

.form-btn:active {
  opacity: 0.9;
  transform: translateY(1rpx);
}

.cancel-btn {
  background-color: #f5f5f7; /* 规范中的浅色背景 */
  color: #666666; /* 规范中的次要文字色 */
  margin-right: 20rpx;
}

.submit-btn {
  background-color: #0070c9; /* 修改为规范中的强调色/主按钮色 */
  color: #fff;
  box-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.1); /* 规范中的按钮阴影 */
} 