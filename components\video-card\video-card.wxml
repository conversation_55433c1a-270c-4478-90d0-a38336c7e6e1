<wxs module="config">
module.exports = {
  cosBaseUrl: "{{cosBaseUrl}}",  // 这将从JS中传入
  defaultAvatar: "{{config.defaultAvatar}}",
  shareIcon: "{{config.shareIcon}}",
  muteIcon: "{{config.muteIcon}}",
  unmuteIcon: "{{config.unmuteIcon}}"
};
</wxs>

<view 
  class="video-card-root {{isTouched ? 'touched' : ''}} {{isMoving && touchDirection === 'up' ? 'moving-up' : ''}} {{isMoving && touchDirection === 'down' ? 'moving-down' : ''}} {{isReleased ? 'released' : ''}}"
  bindtouchstart="handleTouchStart"
  bindtouchmove="handleTouchMove"
  bindtouchend="handleTouchEnd"
  bindtouchcancel="handleTouchEnd"
>
  <view class="video-card">
    <!-- 视频容器 -->
    <view class="video-container" catch:tap="">
      <!-- 视频播放器（底层） -->
      <video 
        id="video-{{videoId}}"
        class="video absolute-fill"
        src="{{videoUrl}}"
        show-center-play-btn="{{false}}"
        controls="{{false}}"
        muted="{{_isMuted}}"
        object-fit="cover"
        bind:play="onVideoPlay"
        bind:pause="onVideoPause"
        bind:ended="onVideoEnded"
        bind:error="onVideoError"
        bind:loadedmetadata="onVideoLoaded"
        catch:tap="onVideoTap"
      />
      
      <!-- 封面图（视频容器内的最上层，只在初始和结束时显示） -->
      <view 
        class="cover-layer absolute-fill"
        wx:if="{{!hasStartedPlaying || hasEnded}}"
        catch:tap="onCoverTap"
      >
        <image 
          class="cover-image absolute-fill"
          mode="aspectFill" 
          src="{{coverUrl}}"
        />
        
        <!-- 视频加载动画 - 仅在用户主动点击播放且视频正在加载时显示 -->
        <view class="video-loading" wx:if="{{videoUrl && !isVideoLoaded && isVideoLoading}}">
          <view class="loading-spinner"></view>
          <text class="loading-text">加载中...</text>
        </view>
      </view>

      <!-- 视频控制层（用于捕获点击事件） -->
      <view 
        class="video-control-layer"
        wx:if="{{hasStartedPlaying && !hasEnded}}"
        catch:tap="onVideoTap"
      ></view>

      <!-- 控制按钮 -->
      <view class="control-buttons">
        <!-- 静音按钮 -->
        <view class="mute-button {{_isMuted ? 'muted' : 'unmuted'}}" catch:tap="toggleMute">
          <image 
            class="mute-icon" 
            src="{{_isMuted ? '/static/静音图标.svg' : '/static/取消静音.svg'}}"
            mode="aspectFit"
          />
        </view>
      </view>
    </view>
    
    <!-- 内容区域容器 -->
    <view class="content-container">
      <!-- 头像 -->
      <view class="avatar-container" bindtap="onCardTap">
        <image class="avatar" src="{{authorAvatar || '/static/logo.png'}}" mode="aspectFill" />
      </view>
      
      <!-- 主标题 -->
      <view class="main-title-container" bindtap="onCardTap">
        <view class="video-title">{{mainTitle}}</view>
      </view>

      <!-- 播放量 -->
      <view class="play-count-container" bindtap="onCardTap">
        <text class="play-count-text">播放量•{{playCount}}</text>
      </view>
      
      <!-- 副标题 -->
      <view class="subtitle-container" bindtap="onCardTap">
        <text class="video-subtitle">{{subTitle}}</text>
      </view>
      
      <!-- 视频标签 -->
      <view class="tag-container" bindtap="onCardTap">
        <text class="category-tag">视频•MP4</text>
      </view>
      
      <!-- 详情按钮 -->
      <view class="detail-button-container" catch:tap="onDetailButtonTap">
        <image class="detail-icon" src="/static/详情图标.png" mode="aspectFit"></image>
      </view>
      
      <!-- 分享按钮 -->
      <view class="share-button-container enlarged-share">
        <share-button 
          videoInfo="{{videoInfo}}" 
          bind:share="onShareVideo">
        </share-button>
      </view>

      <!-- 添加省略号图标 -->
      <view class="decoration-2" catchtap="onCardTap">
        <image class="icon-image" src="/static/省略号图标.png" mode="aspectFit"></image>
      </view>
    </view>
  </view>
</view> 