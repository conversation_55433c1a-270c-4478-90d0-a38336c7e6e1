<!-- pages/my/my.wxml -->
<view class="my-container" bindtap="closeBalancePanel">
  <!-- 顶部用户信息区域 -->
  <view class="user-info-section">
    <!-- 用户信息容器 - 不包含退出按钮 -->
    <view class="user-info-container" wx:if="{{hasUserInfo}}" bindtap="openUserEditor">
      <image class="user-avatar" src="{{userInfo.avatarUrl}}" mode="aspectFill"></image>
      <view class="user-detail">
        <text class="user-nickname">{{userInfo.nickName}}</text>
        <text class="user-welcome">{{userInfo.signature || "欢迎使用"}}</text>
      </view>
    </view>
    <!-- 退出登录按钮 - 独立容器，防止事件冲突 -->
    <view class="logout-btn-container" wx:if="{{hasUserInfo}}" bindtap="logout" catchtap="logout">
      <image class="logout-icon" src="/static/退出图标.png" mode="aspectFit"></image>
    </view>
    <view class="login-container" wx:else bindtap="{{isLoading ? '' : 'getUserProfile'}}">
      <image class="default-avatar" src="/static/default-avatar.png" mode="aspectFill"></image>
      <view class="login-info">
        <text class="login-title">登录 / 注册</text>
        <text class="login-tip">{{isLoading ? '正在登录...' : '点击登录，享受更多服务'}}</text>
      </view>
    </view>
  </view>

  <!-- 余额管理折叠容器 -->
  <view class="balance-container" catchtap="preventBubble">
    <!-- 标签栏 -->
    <view class="balance-tabs">
      <!-- 余额标签 - 添加点击事件 -->
      <view class="balance-tab {{activeBalanceTab === 'balance' ? 'active' : ''}}" bindtap="switchBalanceTab" data-tab="balance">
        <text>我的余额</text>
      </view>
      <!-- 充值优惠标签 -->
      <view class="balance-tab {{activeBalanceTab === 'recharge' ? 'active' : ''}}" bindtap="switchBalanceTab" data-tab="recharge">
        <text>充值优惠</text>
      </view>
      <!-- 充值记录标签 -->
      <view class="balance-tab {{activeBalanceTab === 'record' ? 'active' : ''}}" bindtap="switchBalanceTab" data-tab="record">
        <text>充值记录</text>
      </view>
    </view>

    <!-- 内容区域 -->
    <view class="balance-content {{showBalancePanel ? 'expanded' : 'collapsed'}}">
      <!-- 余额页面 -->
      <view class="balance-page {{activeBalanceTab === 'balance' ? 'active' : ''}}">
        <!-- 未登录状态显示 -->
        <view wx:if="{{!hasUserInfo}}">
          <view class="balance-amount-container">
            <text class="balance-label">当前余额</text>
            <view class="balance-amount">
              <text class="balance-currency">¥</text>
              <text class="balance-value">0.00</text>
            </view>
          </view>
          <!-- 未登录状态的引导文字 -->
          <view class="balance-guide-text">
            <text class="balance-guide-link">登录后查看您的账户余额</text>
          </view>
        </view>

        <!-- 已登录状态显示 -->
        <view wx:else>
          <view class="balance-amount-container">
            <text class="balance-label">当前余额</text>
            <view class="balance-amount">
              <text class="balance-currency">¥</text>
              <text class="balance-value">{{userBalance || '0.00'}}</text>
            </view>
          </view>
          <!-- 已登录状态的引导文字 -->
          <view class="balance-guide-text">
            <text class="balance-guide-link" bindtap="switchBalanceTab" data-tab="recharge">前往优惠中心</text>
          </view>
        </view>
      </view>
      
      <!-- 充值优惠页面 -->
      <view class="recharge-page {{activeBalanceTab === 'recharge' ? 'active' : ''}}" >
        <view class="loading-container" wx:if="{{loadingRechargePlans}}">
          <view class="loading"></view>
          <text class="loading-text">加载中...</text>
        </view>
        <view class="empty-balance-container" wx:elif="{{rechargePlans.length === 0}}">
          <text class="empty-balance-tip">我们正在准备更多优惠</text>
        </view>
        <view class="recharge-plans-list" wx:else>
          <view class="plan-card plan-shimmer-effect" 
                wx:for="{{rechargePlans}}" 
                wx:key="_id" 
                bindtap="confirmRecharge" 
                data-id="{{item._id}}"
                data-plan="{{item}}">
            <image class="plan-card-image"
                   src="{{item.image || '/static/default-recharge.jpg'}}"
                   mode="aspectFit"></image>
          </view>
        </view>
      </view>

      <!-- 充值记录页面 -->
      <view class="record-page {{activeBalanceTab === 'record' ? 'active' : ''}}" >
        <!-- 未登录状态显示 -->
        <view class="empty-balance-container" wx:if="{{!hasUserInfo}}">
          <text class="empty-balance-tip">登录后查看您的充值记录</text>
        </view>

        <!-- 已登录状态显示 -->
        <view wx:else>
          <view class="loading-container" wx:if="{{loadingRechargeRecords}}">
            <view class="loading"></view>
            <text class="loading-text">加载中...</text>
          </view>
          <view class="empty-balance-container" wx:elif="{{rechargeRecords.length === 0}}">
            <text class="empty-balance-tip">暂无任何充值记录</text>
          </view>
          <view class="recharge-records-list" wx:else>
          <view class="record-item" wx:for="{{rechargeRecords}}" wx:key="_id">
            <view class="record-date">
              <text>{{item.dateStr}}</text>
              <text class="record-time">{{item.timeStr}}</text>
            </view>
            <view class="record-content">
              <view class="record-title">{{item.title}}</view>
              <view class="record-details">
                <view class="record-info">
                  <text class="record-label">充值金额</text>
                  <text class="record-value">¥{{item.price}}</text>
                </view>
                <view class="record-info" wx:if="{{item.bonus > 0}}">
                  <text class="record-label">赠送金额</text>
                  <text class="record-value">¥{{item.bonus}}</text>
                </view>
                <view class="record-status {{item.status === 'verified' ? 'verified' : (item.status === 'cancelled' ? 'cancelled' : 'pending')}}">
                  {{item.status === 'verified' ? '已核销' : (item.status === 'cancelled' ? '已取消' : '待核销')}}
                </view>
              </view>
              <view class="record-code" wx:if="{{item.status === 'pending'}}">
                <text class="code-label">核销码</text>
                <text class="code-value">{{item.verifyCode}}</text>
              </view>
              <!-- 操作按钮 -->
              <view class="record-actions" wx:if="{{item.status === 'pending' || item.status === 'verified'}}">
                <!-- 取消按钮，只对待核销的记录显示 -->
                <button wx:if="{{item.status === 'pending'}}" class="cancel-recharge-btn" bindtap="cancelRechargeRecord" data-id="{{item._id}}" data-title="{{item.title}}">取消充值</button>
                <!-- 删除按钮，只对已核销的记录显示 -->
                <button wx:if="{{item.status === 'verified'}}" class="delete-recharge-btn" bindtap="hideRechargeRecord" data-id="{{item._id}}" data-title="{{item.title}}">删除记录</button>
              </view>
            </view>
          </view>
        </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 可下拉刷新区域（包含功能列表和广告） -->
  <scroll-view 
    class="refreshable-area"
    scroll-y="{{true}}"
    refresher-enabled="{{true}}"
    refresher-threshold="{{80}}"
    refresher-default-style="black"
    refresher-background="transparent"
    refresher-triggered="{{refreshing}}"
    bindrefresherrefresh="onAreaRefresh">

    <!-- 功能列表区域 -->
    <view class="function-list">
      <!-- 我的预约 -->
      <view class="function-item" bindtap="navigateToMyAppointments">
        <view class="function-icon-container">
          <image class="function-icon" src="/static/历史图标.png" mode="aspectFit"></image>
        </view>
        <view class="function-info">
          <text class="function-name">我的预约</text>
          <image class="arrow-icon {{showAppointments ? 'arrow-up' : ''}}" src="/static/箭头.png" mode="aspectFit"></image>
        </view>
      </view>
      
      <!-- 预约记录展示面板 -->
      <view class="appointments-container" wx:if="{{showAppointments}}">
        <view class="loading-container" wx:if="{{loadingAppointments}}">
          <view class="loading"></view>
          <text class="loading-text">加载中...</text>
        </view>
        <view class="no-appointments" wx:elif="{{appointments.length === 0}}">
          <text>暂无预约记录</text>
        </view>
        <scroll-view class="appointments-scroll" scroll-y="true" wx:else>
          <view class="appointment-item" 
                wx:for="{{appointments}}" 
                wx:key="_id"
                data-id="{{item._id}}">
            <view class="appointment-card">
              <!-- 顶部标题栏 - 移除右上角容器 -->
              <view class="appointment-header">
                <view class="appointment-title">
                  <!-- 移除顶部核销码 -->
                </view>
                <!-- 移除右上角状态显示 -->
              </view>
              
              <!-- 分割线 -->
              <view class="divider"></view>
              
              <!-- 预约内容 -->
              <view class="appointment-content">
                <!-- 服务项目 -->
                <view class="content-row service-row">
                  <text class="service-name">{{item.serviceName}}</text>
                  <text class="service-price">¥{{item.servicePrice}}</text>
                </view>
                
                <!-- 预约时间 -->
                <view class="content-row">
                  <text class="row-label">预约方式</text>
                  <text class="row-value">堂食</text>
                </view>
                
                <view class="content-row">
                  <text class="row-label">预约时间</text>
                  <text class="row-value">{{item.date}} {{item.time}}</text>
                </view>
                
                <!-- 核销信息 -->
                <view class="content-row" wx:if="{{item.verifyTime}}">
                  <text class="row-label">核销时间</text>
                  <text class="row-value">{{item.verifyTime}}</text>
                </view>
                
                <!-- 服务人员 -->
                <view class="content-row" wx:if="{{item.verifyStaffName}}">
                  <text class="row-label">服务人员</text>
                  <text class="row-value">{{item.verifyStaffName}}</text>
                </view>
                
                <view class="content-row" wx:if="{{item.preferredStaffName && !item.verifyStaffName}}">
                  <text class="row-label">{{item.status === 'completed' ? '预约技师' : '已预约'}}</text>
                  <text class="row-value">{{item.preferredStaffName}}</text>
                </view>
                
                <!-- 原价信息 -->
                <view class="content-row" wx:if="{{item.originalPrice && item.originalPrice > item.servicePrice}}">
                  <text class="row-label">原价</text>
                  <text class="row-value original-price">¥{{item.originalPrice}}</text>
                </view>
                
                <!-- 待服务状态 -->
                <view class="content-row" wx:if="{{item.status === 'pending' || item.status === 'confirmed'}}">
                  <text class="row-label">状态</text>
                  <text class="row-value status-pending">待服务</text>
                </view>
                
                <!-- 核销码 -->
                <view class="verify-code-container" wx:if="{{item.verifyCode && (item.status === 'pending' || item.status === 'confirmed')}}">
                  <text class="verify-code-label">核销码</text>
                  <text class="verify-code">{{item.verifyCode}}</text>
                </view>
              </view>
              
              <!-- 底部按钮区 -->
              <view class="appointment-footer" wx:if="{{item.status === 'pending' || item.status === 'confirmed'}}">
                <button class="cancel-btn" data-id="{{item._id}}" bindtap="cancelAppointment">取消预约</button>
              </view>
              
              <view class="appointment-footer" wx:if="{{item.status === 'completed' || item.status === 'cancelled'}}">
                <button class="delete-btn" bindtap="deleteAppointment" data-id="{{item._id}}">删除订单</button>
              </view>
            </view>
          </view>
        </scroll-view>
      </view>
      
      <!-- 我的积分 -->
      <view class="function-item" bindtap="navigateToPoints">
        <view class="function-icon-container">
          <image class="function-icon" src="/static/积分图标.png" mode="aspectFit"></image>
        </view>
        <view class="function-info">
          <text class="function-name">我的积分</text>
          <image class="arrow-icon" src="/static/箭头.png" mode="aspectFit"></image>
        </view>
      </view>
      
      <!-- 我的客服 -->
      <button class="function-item" open-type="contact" show-message-card="true" send-message-title="欢迎咨询" send-message-path="pages/my/my" send-message-img="/images/客服图标.png">
        <view class="function-icon-container">
          <image class="function-icon" src="/images/客服图标.png" mode="aspectFit"></image>
        </view>
        <view class="function-info">
          <text class="function-name">我的客服</text>
          <image class="arrow-icon" src="/static/箭头.png" mode="aspectFit"></image>
        </view>
      </button>
      
      <!-- 投诉建议 -->
      <view class="function-item" bindtap="openSuggestionsModal">
        <view class="function-icon-container">
          <image class="function-icon" src="/static/管理图标.png" mode="aspectFit"></image>
        </view>
        <view class="function-info">
          <text class="function-name">投诉建议</text>
          <image class="arrow-icon" src="/static/箭头.png" mode="aspectFit"></image>
        </view>
      </view>
      
      <!-- 店铺地址 -->
      <view class="function-item" bindtap="openLocation">
        <view class="function-icon-container">
          <image class="function-icon" src="/static/定位图标.png" mode="aspectFit"></image>
        </view>
        <view class="function-info">
          <text class="function-name">店铺地址</text>
          <image class="arrow-icon" src="/static/箭头.png" mode="aspectFit"></image>
        </view>
      </view>
    </view>
    
    <!-- 添加一个空白分隔视图，创建30rpx的间距 -->
    <view style="height: 30rpx; width: 100%; background: transparent;"></view>
    
    <!-- 创建堆叠容器，包含三层重叠内容 -->
    <view class="stacked-container">
      <!-- 底层：广告 -->
      <view class="ad-card">
        <!-- 有广告内容时显示图片 -->
        <block wx:if="{{hasAdContent}}">
          <image 
            class="ad-image fade-image"
            src="{{adItems[adFadeIndex].imageUrl}}"
            mode="aspectFill"
            style="opacity: {{adFadeOpacity}}; transition: opacity 0.8s;"
          />
        </block>
        <!-- 无广告内容时显示提示文字 -->
        <view class="empty-ad-tip" wx:else>
          <text>暂无任何展示信息</text>
        </view>
      </view>
      
      <!-- 中间层：当已登录且有未完成的预约时显示预约信息 -->
      <view class="active-appointment-card main-latest-appointment-card" wx:if="{{hasUserInfo && latestAppointment}}">
        <!-- 背景图片 -->
        <image class="appointment-bg-image" src="/static/预约背景.png" mode="aspectFill"></image>
        
        <!-- 核销码数字 -->
        <view class="main-latest-verify-code-no-bg" wx:if="{{latestAppointment.verifyCode}}">
          <text class="main-latest-verify-value-no-bg">{{latestAppointment.verifyCode}}</text>
        </view>
        
        <view class="main-latest-info new-layout">
          <!-- 项目名称 -->
          <view class="main-latest-row">
            <view class="main-latest-service new-service">{{latestAppointment.serviceName}}</view>
          </view>
          
          <!-- 预约时间 -->
          <view class="main-latest-row">
            <view class="better-time-label">预约时间:</view>
            <view class="better-time-value">{{latestAppointment.date}} {{latestAppointment.time}}</view>
          </view>
          
          <!-- 价格展示 -->
          <view class="main-latest-row">
            <view class="better-price-label">优惠价:</view>
            <view class="better-price-value">¥{{latestAppointment.servicePrice}}</view>
            
            <!-- 添加服务人员信息 -->
            <view class="better-staff-label" wx:if="{{latestAppointment.preferredStaffName}}">已预约:</view>
            <view class="better-staff-value" wx:if="{{latestAppointment.preferredStaffName}}">{{latestAppointment.preferredStaffName}}</view>
          </view>
          
          <!-- 移除单独的服务人员信息行 -->
        </view>
        
        <!-- 取消预约按钮 - 位置保持不变 -->
        <view class="main-latest-actions better-bottom-btn">
          <button class="main-latest-cancel-btn better-cancel-btn" bindtap="cancelLatestAppointment">取消预约</button>
        </view>
      </view>
      
      <!-- 顶层：隐藏入口 -->
      <view class="hidden-entry-container">
        <view class="admin-entry" bindtap="onAdminTap"></view>
        <view class="staff-entry" bindtap="onStaffTap"></view>
      </view>
    </view>
  </scroll-view>

  <!-- 底部版本信息已移除 -->
</view>

<!-- 用户信息编辑弹窗 -->
<view class="edit-modal-container">
  <view class="edit-modal {{keyboardHeight > 0 ? 'keyboard-active' : ''}} {{closingAnimation ? 'closing' : ''}}" wx:if="{{showUserEditor}}" bind:keyboardheightchange="onKeyboardHeightChange">
    <view class="edit-content {{closingAnimation ? 'closing' : ''}}">
      <view class="edit-header">
        <text class="edit-title">个人信息</text>
        <view class="edit-close" bindtap="closeUserEditor">×</view>
      </view>
      
      <!-- 调试信息隐藏，只在开发环境显示 -->
      <view class="debug-info" wx:if="{{false}}">
        <text>键盘高度: {{keyboardHeight}}px</text>
      </view>
      
      <view class="edit-body">
        <!-- 头像选择区域 - 始终显示 -->
        <view class="avatar-section">
          <image class="edit-avatar" src="{{editUserInfo.avatarUrl}}" mode="aspectFill" bindtap="chooseAvatar"></image>
          <text class="avatar-hint">点击更换头像</text>
        </view>
        
        <!-- 昵称编辑区域 - 横向布局 -->
        <view class="info-item-row">
          <text class="info-label-inline">昵称</text>
          <input class="info-input-inline" value="{{editUserInfo.nickName}}" bindinput="onNicknameInput" bindfocus="onInputFocus" data-field="nickname" bindblur="onInputBlur" placeholder="请输入您的昵称" maxlength="16" adjust-position="{{false}}" />
        </view>
        
        <!-- 签名编辑区域 - 单行输入框 -->
        <view class="info-item-row">
          <text class="info-label-inline">签名</text>
          <input class="info-input-inline" value="{{editUserInfo.signature}}" bindinput="onSignatureInput" bindfocus="onInputFocus" data-field="signature" bindblur="onInputBlur" placeholder="请输入您的签名" maxlength="20" adjust-position="{{false}}" />
        </view>
      </view>
      
      <view class="edit-footer">
        <button class="cancel-btn" bindtap="closeUserEditor">取消</button>
        <button class="save-btn" bindtap="saveUserInfo">完成</button>
      </view>
    </view>
  </view>
</view>

<!-- 登录提示弹窗 -->
<view class="login-modal" wx:if="{{showLoginModal}}">
  <view class="modal-mask" bindtap="closeLoginModal"></view>
  <view class="modal-content">
    <view class="modal-header">
      <text class="modal-title">登录提示</text>
      <view class="modal-close" bindtap="closeLoginModal">×</view>
    </view>

    <view class="modal-body">
      <view class="login-tip">
        <text class="login-tip-text">您需要登录后才能使用此功能</text>
      </view>

      <button
        class="login-btn"
        open-type="getUserInfo"
        bindgetuserinfo="onGetUserInfo"
      >微信登录</button>
    </view>
  </view>
</view>

<!-- 悬浮客服按钮 -->
<floating-customer-service
  iconUrl="/images/客服图标.png"
  title="欢迎咨询"
  path="pages/my/my"
  imageUrl="/images/客服图标.png"
/>

<!-- 充值计划详情对话框 -->
<view class="modal" wx:if="{{showPlanDetailModal}}">
  <view class="modal-mask" bindtap="hidePlanDetailModal"></view>
  <view class="modal-content">
    <view class="modal-header">
      <text class="modal-title" style="color: #07C160;">{{currentPlan.title}}</text>
      <view class="modal-close" bindtap="hidePlanDetailModal">×</view>
    </view>
    
    <view class="modal-body">
      <!-- 计划图片 -->
      <image class="plan-detail-image" 
             src="{{currentPlan.image || '/static/default-recharge.jpg'}}" 
             mode="aspectFit"></image>

      <!-- 计划详情 -->
      <view class="plan-detail-info">
        <view class="plan-detail-item">
          <text class="plan-detail-label">充值金额</text>
          <text class="plan-detail-value price-color">¥{{currentPlan.price}}</text>
        </view>
        
        <view class="plan-detail-item">
          <text class="plan-detail-label">赠送金额</text>
          <text class="plan-detail-value bonus-color">¥{{currentPlan.bonus}}</text>
        </view>
        
        <view class="plan-detail-item">
          <text class="plan-detail-label">到账总额</text>
          <text class="plan-detail-value total-color">¥{{currentPlan.total}}</text>
        </view>
        
        <view class="plan-description" wx:if="{{currentPlan.description}}">
          <text>{{currentPlan.description}}</text>
        </view>
      </view>
    </view>
    
    <view class="modal-footer">
      <button class="cancel-btn" bindtap="hidePlanDetailModal">取消</button>
      <button class="wx-confirm-btn" bindtap="confirmRecharge">确认充值</button>
    </view>
  </view>
</view>

<!-- 充值成功对话框 -->
<view class="modal" wx:if="{{showRechargeSuccessModal}}">
  <view class="modal-mask"></view>
  <view class="modal-content eco-success-modal {{rechargeModalClosing ? 'closing' : ''}}">
    <!-- 弹窗头部 -->
    <view class="modal-header eco-header">
      <text class="modal-title">充值优惠</text>
      <text class="modal-subtitle">待核销后生效</text>
    </view>

    <view class="modal-body">
      <!-- 方案信息卡片 -->
      <view class="plan-info-card">
        <view class="plan-title-section">
          <text class="plan-title">{{currentPlan.title}}</text>
          <text class="plan-description" wx:if="{{currentPlan.description}}">{{currentPlan.description}}</text>
        </view>

        <!-- 金额详情 -->
        <view class="amount-details">
          <view class="amount-row">
            <text class="amount-label">充值金额</text>
            <text class="amount-value recharge-amount">¥{{currentPlan.price}}</text>
          </view>
          <view class="amount-row bonus-row">
            <text class="amount-label">赠送金额</text>
            <text class="amount-value bonus-amount">+¥{{currentPlan.bonus}}</text>
          </view>
          <view class="amount-divider"></view>
          <view class="amount-row total-row">
            <text class="amount-label total-label">到账总额</text>
            <text class="amount-value total-amount">¥{{currentPlan.total}}</text>
          </view>
        </view>
      </view>

      <!-- 核销码容器 -->
      <view class="verify-code-container eco-verify-container">
        <text class="verify-code-title">核销码</text>
        <text class="verify-code eco-verify-code">{{rechargeVerifyCode}}</text>
        <text class="verify-code-tip">请将此码展示给店员进行核销</text>
        <view class="verify-note">
          <text class="note-text">核销后余额立即到账，可用于支付服务费用</text>
        </view>
      </view>
    </view>

    <view class="modal-footer">
      <button class="eco-cancel-btn" bindtap="closeRechargeSuccessModal">
        <text class="btn-text">关闭</text>
      </button>
      <button class="eco-confirm-btn" bindtap="confirmRechargeOrder">
        <text class="btn-text">确认充值</text>
      </button>
    </view>
  </view>
</view>

<!-- 投诉建议悬浮弹窗 -->
<view class="suggestions-modal {{isClosingSuggestions ? 'closing' : ''}}" wx:if="{{showSuggestionsModal}}">
  <view class="suggestions-modal-mask" bindtap="closeSuggestionsModal"></view>
  <view class="suggestions-modal-content {{showSuggestionsAnimation ? 'show' : ''}}">
    <!-- 弹窗头部 -->
    <view class="suggestions-modal-header">
      <text class="suggestions-modal-title">投诉或建议</text>
      <view class="suggestions-modal-close" bindtap="closeSuggestionsModal">×</view>
    </view>
    
    <!-- 聊天界面主体 -->
    <view class="chat-container">
      <!-- 对话记录区域 -->
      <scroll-view class="chat-messages"
                   scroll-y="true"
                   scroll-into-view="{{scrollToView}}">
        <view wx:if="{{conversations.length === 0}}" class="empty-chat-container">
          <image class="empty-icon" src="/static/空状态图标.png" mode="aspectFit"></image>
          <view class="empty-text-container">
            <text class="empty-text">我们会用最快的时间</text>
            <text class="empty-text">回复您的每一条建议或投诉</text>
          </view>
        </view>
        <view wx:else class="message-list">
          <view wx:for="{{conversations}}" wx:key="id" class="message-item {{item.sender === 'user' ? 'user-message' : 'admin-message'}}" id="msg-{{item.id}}">
            <view class="message-avatar-container">
              <image class="message-avatar"
                     src="{{item.sender === 'user' ? (userInfo.avatarUrl || '/static/default-avatar.png') : '/static/logo.png'}}"
                     mode="aspectFill"></image>
            </view>
            <view class="message-content">
              <!-- 文本内容 - 仅当没有图片且有内容时显示 -->
              <view class="message-bubble" wx:if="{{!(item.images && item.images.length > 0) && item.content}}">
                <text class="message-text">{{item.content}}</text>
              </view>

              <!-- 图片内容 -->
              <view class="message-images" wx:if="{{item.images && item.images.length > 0}}">
                <image wx:for="{{item.images}}"
                       wx:for-item="img"
                       wx:key="*this"
                       class="message-image"
                       src="{{img}}"
                       mode="aspectFill"
                       bindtap="previewImage"
                       data-url="{{img}}"
                       data-urls="{{item.images}}"></image>
              </view>

              <!-- 视频内容 -->
              <view class="message-videos" wx:if="{{item.videos && item.videos.length > 0}}">
                <video wx:for="{{item.videos}}"
                       wx:for-item="video"
                       wx:key="*this"
                       class="message-video"
                       src="{{video}}"
                       controls></video>
              </view>

              <text class="message-time">{{item.createTime}}</text>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 底部输入区域 -->
    <view class="chat-input-area">
      <!-- 媒体选择按钮 -->
      <view class="media-btn" bindtap="chooseImage">
        <text class="media-icon">+</text>
      </view>
      <!-- 输入框容器 -->
      <view class="input-container" bindtap="focusInputAtEnd">
        <textarea class="message-input"
                 placeholder="输入消息内容..."
                 value="{{messageText}}"
                 bindinput="onMessageInput"
                 maxlength="500"
                 adjust-position="{{true}}"
                 show-confirm-bar="{{false}}"
                 cursor-spacing="20"
                 auto-height
                 focus="{{inputFocus}}"
                 bindfocus="onInputFocus"
                 bindblur="onInputBlur"></textarea>
      </view>
      <!-- 发送按钮 -->
      <view class="send-btn {{sendBtnActive ? 'active' : ''}}" bindtap="sendMessage">发送</view>
    </view>
  </view>
</view>

<!-- 指向广告弹窗 -->
<target-ad-modal
  show="{{showTargetAd}}"
  imageUrl="{{targetAdData.imageUrl}}"
  jumpUrl="{{targetAdData.jumpUrl}}"
  bind:close="onTargetAdClose"
/>