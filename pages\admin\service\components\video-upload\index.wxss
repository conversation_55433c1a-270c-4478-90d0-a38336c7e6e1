.video-upload-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s;
}

.video-upload-modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-hidden {
  display: none;
}

.modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  z-index: 999;
  transition: all 0.3s ease-in-out;
}

.modal-content {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #f5f5f7;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  animation: slideInUp 0.3s ease-in-out;
  padding-bottom: env(safe-area-inset-bottom);
}

@keyframes slideInUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  padding-top: 90rpx;
  background-color: #ffffff;
  border-bottom: 1rpx solid #e0e0e0;
  position: sticky;
  top: 0;
  z-index: 101;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.close-button {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  color: #4a4a4a;
  transition: all 0.2s ease;
}

.close-button:active {
  opacity: 0.8;
  transform: scale(0.95);
}

.modal-title {
  font-size: 34rpx;
  font-weight: bold;
  color: #4a4a4a;
  flex: 1;
  text-align: center;
}

.save-button {
  padding: 8rpx 24rpx;
  border-radius: 4rpx;
  background-color: #0070c9;
  color: #ffffff;
  font-size: 28rpx;
  font-weight: bold;
  transition: all 0.2s ease;
}

.save-button:active {
  opacity: 0.9;
  transform: scale(0.97);
  background-color: #005ba3;
}

.form-container {
  flex: 1;
  padding: 30rpx;
  padding-bottom: calc(130rpx + env(safe-area-inset-bottom));
}

.form-section {
  margin-bottom: 30rpx;
  background-color: #ffffff;
  border-radius: 6rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #4a4a4a;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  display: inline-block;
  width: 8rpx;
  height: 32rpx;
  background: #0070c9;
  border-radius: 4rpx;
  margin-right: 16rpx;
}

.input-field {
  margin-bottom: 20rpx;
}

.input-label {
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 12rpx;
  display: flex;
  align-items: center;
}

.required-mark {
  color: #ff3b30;
  margin-left: 8rpx;
}

.text-input {
  width: 100%;
  height: 80rpx;
  border-radius: 4rpx;
  background-color: #f8f8f8;
  padding: 0 20rpx;
  box-sizing: border-box;
  font-size: 28rpx;
  color: #333333;
  border: 2rpx solid #e0e0e0;
  transition: all 0.2s ease;
}

.text-input:focus {
  border-color: #0070c9;
  background-color: #ffffff;
}

.textarea-input {
  width: 100%;
  height: 200rpx;
  border-radius: 12rpx;
  background-color: #f8f8f8;
  padding: 20rpx;
  box-sizing: border-box;
  font-size: 28rpx;
  color: #333333;
  border: 2rpx solid #eeeeee;
  transition: all 0.3s;
}

.textarea-input:focus {
  border-color: #ff9a9e;
  background-color: #ffffff;
}

.switch-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.switch-label {
  font-size: 28rpx;
  color: #666666;
}

.upload-field {
  margin-bottom: 20rpx;
}

.upload-title {
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 12rpx;
  display: flex;
  align-items: center;
}

.upload-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  border-radius: 6rpx;
  background-color: #f8f8f8;
  padding: 20rpx;
  box-sizing: border-box;
  border: 2rpx dashed #e0e0e0;
  transition: all 0.2s ease;
}

.upload-container:active {
  background-color: #f0f0f0;
  border-color: #cccccc;
}

.upload-image {
  width: 100%;
  height: 360rpx;
  border-radius: 8rpx;
  background-color: #eeeeee;
  position: relative;
  margin-bottom: 20rpx;
  overflow: hidden;
}

.upload-image image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.video-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f0f0f0;
}

.placeholder-icon {
  font-size: 80rpx;
  color: #999999;
  margin-bottom: 20rpx;
}

.placeholder-text {
  font-size: 28rpx;
  color: #666666;
  text-align: center;
}

.upload-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx 0;
  border-radius: 4rpx;
  background-color: rgba(0, 112, 201, 0.1);
  color: #0070c9;
  font-size: 28rpx;
  transition: all 0.2s ease;
}

.upload-button:active {
  background-color: rgba(0, 112, 201, 0.2);
}

.upload-icon {
  font-size: 32rpx;
  margin-right: 10rpx;
}

.detail-images-container {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -10rpx;
}

.detail-image-item {
  width: calc(50% - 20rpx);
  height: 200rpx;
  margin: 10rpx;
  border-radius: 8rpx;
  overflow: hidden;
  position: relative;
  background-color: #f0f0f0;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.detail-image-item:active {
  transform: scale(0.97);
}

.detail-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.remove-detail-image {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  width: 44rpx;
  height: 44rpx;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  font-size: 24rpx;
  z-index: 2;
}

.add-detail-image {
  width: calc(50% - 20rpx);
  height: 200rpx;
  margin: 10rpx;
  border-radius: 8rpx;
  background-color: #f8f8f8;
  border: 2rpx dashed #dddddd;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.add-detail-image:active {
  background-color: #f0f0f0;
  border-color: #cccccc;
}

.add-detail-icon {
  font-size: 60rpx;
  color: #cccccc;
  margin-bottom: 10rpx;
}

.add-detail-text {
  font-size: 24rpx;
  color: #999999;
}

.upload-tip {
  font-size: 24rpx;
  color: #999999;
  margin-top: 20rpx;
}

.tip-highlight {
  color: #0070c9;
}

.loading-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 100;
}

.loading-spinner {
  width: 80rpx;
  height: 80rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #0070c9;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 20rpx;
  color: #666666;
  font-size: 28rpx;
}

/* 安全区域，确保内容不被底部遮挡 */
.safe-bottom-area {
  height: 120rpx; /* 增加高度，为底部按钮留出空间 */
  width: 100%;
}

/* 底部保存按钮容器 */
.bottom-save-container {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 20rpx 30rpx;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
  background-color: #ffffff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 101;
  display: flex;
  justify-content: center;
  transition: all 0.2s ease;
}

.bottom-save-container.disabled {
  opacity: 0.8;
}

.bottom-save-button {
  width: 100%;
  height: 90rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #0070c9;
  color: #ffffff;
  font-size: 32rpx;
  font-weight: bold;
  border-radius: 4rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 112, 201, 0.3);
  transition: all 0.2s ease;
}

.bottom-save-button:active {
  transform: scale(0.98);
  background-color: #005ba3;
  box-shadow: 0 1rpx 5rpx rgba(0, 112, 201, 0.2);
}

.bottom-save-button.disabled {
  background-color: #cccccc;
  box-shadow: none;
}

/* 可见性开关样式 */
.visibility-toggle {
  display: flex;
  align-items: center;
}

.toggle-switch {
  width: 80rpx;
  height: 40rpx;
  background-color: #e0e0e0;
  border-radius: 20rpx;
  position: relative;
  transition: all 0.2s ease;
  margin-right: 20rpx;
}

.toggle-switch.active {
  background-color: #0070c9;
}

.toggle-handle {
  width: 36rpx;
  height: 36rpx;
  border-radius: 50%;
  background-color: #ffffff;
  position: absolute;
  top: 2rpx;
  left: 2rpx;
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}

.toggle-switch.active .toggle-handle {
  left: 42rpx;
}

.toggle-text {
  font-size: 28rpx;
  color: #333333;
}

/* 详情图片区域样式 */
.detail-images-area {
  padding: 10px;
}

.detail-images-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.detail-image-item {
  position: relative;
  width: calc(33.33% - 10px);
  aspect-ratio: 1/1;
  border-radius: 8px;
  overflow: hidden;
}

.detail-image-item image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.upload-tip {
  margin-top: 10px;
  font-size: 12px;
  color: #999;
  text-align: right;
}

.detail-images-preview {
  margin-top: 30rpx;
}

.preview-title {
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 15rpx;
  display: flex;
  align-items: center;
}

.video-container {
  position: relative;
  width: 100%;
  height: 360rpx;
  background-color: #000000;
  overflow: hidden;
}

.video-container video {
  width: 100%;
  height: 100%;
  display: block;
  object-fit: contain; /* 确保视频内容完整显示 */
}

/* 添加排序提示文本样式 */
.input-tip {
  font-size: 24rpx;
  color: #999;
  margin-top: 8rpx;
  line-height: 1.4;
}

/* 非必填项的标记样式 */
.non-required {
  color: #999999;
  font-size: 24rpx;
  font-weight: normal;
  margin-left: 8rpx;
} 