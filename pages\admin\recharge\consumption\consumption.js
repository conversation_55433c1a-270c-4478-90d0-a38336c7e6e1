const app = getApp()

Page({
  data: {
    consumptionRecords: [],
    totalRecords: 0,
    page: 1,
    pageSize: 20,
    loading: false,
    loadingMore: false,
    currentTab: 'all',
    tabs: [
      { value: 'all', label: '全部' },
      { value: 'consumption', label: '消费' },
      { value: 'refund', label: '退款' },
      { value: 'balance', label: '余额统计' }
    ],
    dateRange: {
      start: '',
      end: ''
    },
    searchKeyword: '',
    showFilterModal: false,
    
    // 用户余额统计相关数据
    userBalanceList: [],
    totalUserBalance: 0,
    balancePage: 1,
    balancePageSize: 20,
    loadingBalance: false,
    loadingMoreBalance: false,
    totalBalanceStats: {
      totalUsers: 0,
      totalBalance: 0,
      totalBonusBalance: 0,
      totalAmount: 0
    },
    
    // 退款相关
    showRefundModal: false,
    currentUser: null,
    refundAmount: '',
    refundRemark: '用户余额退款',
    clearBalance: false
  },

  onLoad() {
    // 设置日期范围（默认近30天）
    const today = new Date()
    const thirtyDaysAgo = new Date()
    thirtyDaysAgo.setDate(today.getDate() - 30)
    
    this.setData({
      dateRange: {
        start: this.formatDate(thirtyDaysAgo),
        end: this.formatDate(today)
      }
    })
    
    // 加载初始数据
    this.loadConsumptionRecords()
  },
  
  onPullDownRefresh() {
    this.setData({ page: 1 })
    this.loadConsumptionRecords().then(() => {
      wx.stopPullDownRefresh()
    })
  },
  
  onReachBottom() {
    if (this.data.currentTab === 'balance') {
      if (this.data.userBalanceList.length < this.data.totalUserBalance && !this.data.loadingMoreBalance) {
        this.loadUserBalanceStats(true)
      }
    } else {
      if (this.data.consumptionRecords.length < this.data.totalRecords && !this.data.loadingMore) {
        this.loadConsumptionRecords(true)
      }
    }
  },
  
  // 加载消费记录
  async loadConsumptionRecords(loadMore = false) {
    if (this.data.loading) return
    
    this.setData({ loading: true })
    
    try {
      const { page, pageSize, currentTab, dateRange, searchKeyword } = this.data
      
      // 准备请求参数
      const data = {
        page,
        pageSize,
        startDate: dateRange.start,
        endDate: dateRange.end
      }
      
      // 如果选择了类型筛选
      if (currentTab !== 'all') {
        data.type = currentTab
      }
      
      // 如果有搜索关键词，尝试作为openid搜索
      if (searchKeyword) {
        data.openid = searchKeyword
      }
      
      // 调用云函数
      const res = await wx.cloud.callFunction({
        name: 'rechargeManager',
        data: {
          type: 'admin',
          action: 'getBalanceConsumptions',
          data
        }
      })
      
      if (res.result && res.result.code === 0) {
        const newRecords = res.result.data.list || []
        
        // 格式化数据
        newRecords.forEach(record => {
          // 格式化日期
          if (record.createTime) {
            record.createTimeStr = this.formatDateTime(new Date(record.createTime))
          }
          // 格式化类型
          record.typeText = record.type === 'consumption' ? '消费' : '退款'
          // 格式化金额
          record.amountStr = '￥' + record.amount.toFixed(2)
          record.deductBalanceStr = '￥' + record.deductBalance.toFixed(2)
          record.deductBonusStr = '￥' + record.deductBonus.toFixed(2)
        })
        
        this.setData({
          consumptionRecords: loadMore ? [...this.data.consumptionRecords, ...newRecords] : newRecords,
          totalRecords: res.result.data.total || 0,
          loading: false,
          loadingMore: false
        })
      } else {
        wx.showToast({
          title: res.result?.message || '加载失败',
          icon: 'none'
        })
      }
    } catch (err) {
      console.error('加载消费记录失败:', err)
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      })
    } finally {
      this.setData({ loading: false, loadingMore: false })
    }
  },
  
  // 切换标签页
  switchTab(e) {
    const tab = e.currentTarget.dataset.tab
    
    if (tab === this.data.currentTab) return
    
    this.setData({
      currentTab: tab,
      page: 1,
      consumptionRecords: []
    })
    
    if (tab === 'balance') {
      this.loadUserBalanceStats()
    } else {
      this.loadConsumptionRecords()
    }
  },
  
  // 打开筛选弹窗
  showFilter() {
    this.setData({
      showFilterModal: true
    })
  },
  
  // 关闭筛选弹窗
  closeFilter() {
    this.setData({
      showFilterModal: false
    })
  },
  
  // 应用筛选条件
  applyFilter() {
    this.setData({
      page: 1,
      consumptionRecords: [],
      showFilterModal: false
    })
    
    this.loadConsumptionRecords()
  },
  
  // 重置筛选条件
  resetFilter() {
    // 设置日期范围（默认近30天）
    const today = new Date()
    const thirtyDaysAgo = new Date()
    thirtyDaysAgo.setDate(today.getDate() - 30)
    
    this.setData({
      dateRange: {
        start: this.formatDate(thirtyDaysAgo),
        end: this.formatDate(today)
      },
      searchKeyword: '',
      currentTab: 'all',
      page: 1,
      consumptionRecords: []
    })
    
    this.loadConsumptionRecords()
    this.closeFilter()
  },
  
  // 输入搜索关键词
  inputKeyword(e) {
    this.setData({
      searchKeyword: e.detail.value.trim()
    })
  },
  
  // 搜索
  search() {
    this.setData({
      page: 1,
      consumptionRecords: [],
      balancePage: 1,
      userBalanceList: []
    })
    
    if (this.data.currentTab === 'balance') {
      this.loadUserBalanceStats()
    } else {
      this.loadConsumptionRecords()
    }
  },
  
  // 选择开始日期
  bindStartDateChange(e) {
    this.setData({
      'dateRange.start': e.detail.value
    })
  },
  
  // 选择结束日期
  bindEndDateChange(e) {
    this.setData({
      'dateRange.end': e.detail.value
    })
  },
  
  // 格式化日期：YYYY-MM-DD
  formatDate(date) {
    const year = date.getFullYear()
    const month = (date.getMonth() + 1).toString().padStart(2, '0')
    const day = date.getDate().toString().padStart(2, '0')
    return `${year}-${month}-${day}`
  },
  
  // 格式化日期时间：YYYY-MM-DD HH:MM:SS
  formatDateTime(date) {
    const year = date.getFullYear()
    const month = (date.getMonth() + 1).toString().padStart(2, '0')
    const day = date.getDate().toString().padStart(2, '0')
    const hours = date.getHours().toString().padStart(2, '0')
    const minutes = date.getMinutes().toString().padStart(2, '0')
    
    return `${year}-${month}-${day} ${hours}:${minutes}`
  },
  
  // 返回上一页
  navigateBack() {
    wx.navigateBack()
  },
  
  // 加载用户余额统计
  async loadUserBalanceStats(loadMore = false) {
    if (this.data.loadingBalance) return
    
    const page = loadMore ? this.data.balancePage + 1 : 1
    
    this.setData({ 
      loadingBalance: true,
      loadingMoreBalance: loadMore
    })
    
    try {
      const { balancePageSize, searchKeyword } = this.data
      
      // 准备请求参数
      const data = {
        page,
        pageSize: balancePageSize,
        keyword: searchKeyword,
        needTotalStats: page === 1 // 只在第一页请求时获取总统计
      }
      
      // 调用云函数
      const res = await wx.cloud.callFunction({
        name: 'rechargeManager',
        data: {
          type: 'admin',
          action: 'getUserBalanceStats',
          data
        }
      })
      
      if (res.result && res.result.code === 0) {
        const newRecords = res.result.data.list || []
        const stats = res.result.data.stats || {
          totalUsers: 0,
          totalBalance: 0,
          totalBonusBalance: 0,
          totalAmount: 0
        }
        
        // 格式化数据
        newRecords.forEach(user => {
          // 格式化余额
          user.balanceStr = '￥' + user.balance.toFixed(2)
          user.bonusBalanceStr = '￥' + user.bonusBalance.toFixed(2)
          user.totalBalanceStr = '￥' + user.totalBalance.toFixed(2)
          
          // 格式化更新时间
          if (user.lastUpdateTime) {
            user.lastUpdateTimeStr = this.formatDateTime(new Date(user.lastUpdateTime))
          }
        })
        
        // 格式化统计数据
        if (stats) {
          stats.totalBalanceStr = '￥' + (stats.totalBalance || 0).toFixed(2)
          stats.totalBonusBalanceStr = '￥' + (stats.totalBonusBalance || 0).toFixed(2)
          stats.totalAmountStr = '￥' + (stats.totalAmount || 0).toFixed(2)
        }
        
        this.setData({
          userBalanceList: loadMore ? [...this.data.userBalanceList, ...newRecords] : newRecords,
          totalUserBalance: res.result.data.total || 0,
          balancePage: page,
          totalBalanceStats: stats,
          loadingBalance: false,
          loadingMoreBalance: false
        })
      } else {
        wx.showToast({
          title: res.result?.message || '加载失败',
          icon: 'none'
        })
      }
    } catch (err) {
      console.error('加载用户余额统计失败:', err)
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      })
    } finally {
      this.setData({ loadingBalance: false, loadingMoreBalance: false })
    }
  },
  
  // 显示退款弹窗
  showRefundModal(e) {
    const user = e.currentTarget.dataset.user
    
    this.setData({
      showRefundModal: true,
      currentUser: user,
      refundAmount: user.totalBalance.toFixed(2), // 默认退还全部余额
      clearBalance: true
    })
  },
  
  // 关闭退款弹窗
  closeRefundModal() {
    this.setData({
      showRefundModal: false,
      currentUser: null,
      refundAmount: '',
      refundRemark: '用户余额退款',
      clearBalance: false
    })
  },
  
  // 输入退款金额
  inputRefundAmount(e) {
    this.setData({
      refundAmount: e.detail.value
    })
  },
  
  // 输入退款备注
  inputRefundRemark(e) {
    this.setData({
      refundRemark: e.detail.value
    })
  },
  
  // 切换是否清零余额
  toggleClearBalance() {
    const clearBalance = !this.data.clearBalance
    
    this.setData({
      clearBalance,
      refundAmount: clearBalance ? this.data.currentUser.totalBalance.toFixed(2) : ''
    })
  },
  
  // 确认退款
  async confirmRefund() {
    const { currentUser, refundAmount, refundRemark, clearBalance } = this.data
    
    if (!currentUser) {
      return
    }
    
    // 验证退款金额
    const amount = parseFloat(refundAmount)
    if (isNaN(amount) || amount <= 0) {
      wx.showToast({
        title: '请输入有效的退款金额',
        icon: 'none'
      })
      return
    }
    
    // 验证退款金额不能超过用户总余额
    if (amount > currentUser.totalBalance) {
      wx.showToast({
        title: '退款金额不能超过用户总余额',
        icon: 'none'
      })
      return
    }
    
    try {
      wx.showLoading({
        title: '处理中...',
        mask: true
      })
      
      // 调用云函数处理退款
      const res = await wx.cloud.callFunction({
        name: 'rechargeManager',
        data: {
          type: 'admin',
          action: 'refundUserBalance',
          data: {
            openid: currentUser.openid,
            amount: amount,
            remark: refundRemark,
            clearBalance: clearBalance,
            operatorId: getApp().globalData.userInfo?.openid || '管理员'
          }
        }
      })
      
      if (res.result && res.result.code === 0) {
        wx.showToast({
          title: '退款成功',
          icon: 'success'
        })
        
        // 关闭弹窗
        this.closeRefundModal()
        
        // 重新加载用户余额数据
        this.loadUserBalanceStats()
      } else {
        wx.showToast({
          title: res.result?.message || '退款失败',
          icon: 'none'
        })
      }
    } catch (err) {
      console.error('退款处理失败:', err)
      wx.showToast({
        title: '退款处理失败，请重试',
        icon: 'none'
      })
    } finally {
      wx.hideLoading()
    }
  }
}) 