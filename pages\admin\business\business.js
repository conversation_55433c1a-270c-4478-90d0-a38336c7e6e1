const app = getApp()

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 基础数据
    loading: true,
    staffList: [],
    selectedStaff: '',
    selectedStaffName: '全部员工',
    
    // 日期相关
    today: '',
    yesterday: '',
    currentWeekStart: '',
    currentWeekEnd: '',
    currentMonthStart: '',
    currentMonthEnd: '',
    
    // 自定义日期范围
    startDate: '',
    endDate: '',
    showStartDatePicker: false,
    showEndDatePicker: false,
    
    // 业绩数据
    businessData: null,
    
    // 切换视图
    currentView: 'store', // 'store'、'staff' 或 'expense'
    
    // 员工业绩详情
    staffPerformance: null,
    showStaffPerformance: false,
    
    // 支出管理数据
    expenseData: null,
    expenseList: [],
    expensePagination: {
      page: 1,
      pageSize: 20,
      total: 0
    },
    
    // 支出详情
    currentExpense: null,
    showExpenseDetail: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 检查管理员状态
    if (!app.globalData.isAdmin) {
      wx.showToast({
        title: '请先登录管理员账号',
        icon: 'none',
        duration: 2000
      });
      
      setTimeout(() => {
        wx.navigateBack();
      }, 2000);
      return;
    }
    
    // 初始化日期
    this.initDates();
    
    // 获取员工列表
    this.fetchStaffList();
    
    // 获取店铺业绩数据
    this.fetchBusinessData();
  },

  /**
   * 初始化日期
   */
  initDates() {
    const today = new Date();
    const year = today.getFullYear();
    const month = today.getMonth() + 1;
    const day = today.getDate();
    
    // 格式化今天日期
    const todayStr = `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
    
    // 计算昨天
    const yesterday = new Date(today);
    yesterday.setDate(today.getDate() - 1);
    const yesterdayStr = `${yesterday.getFullYear()}-${String(yesterday.getMonth() + 1).padStart(2, '0')}-${String(yesterday.getDate()).padStart(2, '0')}`;
    
    // 计算本周开始和结束
    const currentWeekStart = new Date(today);
    currentWeekStart.setDate(today.getDate() - today.getDay());
    const currentWeekEnd = new Date(today);
    currentWeekEnd.setDate(currentWeekStart.getDate() + 6);
    
    const weekStartStr = `${currentWeekStart.getFullYear()}-${String(currentWeekStart.getMonth() + 1).padStart(2, '0')}-${String(currentWeekStart.getDate()).padStart(2, '0')}`;
    const weekEndStr = `${currentWeekEnd.getFullYear()}-${String(currentWeekEnd.getMonth() + 1).padStart(2, '0')}-${String(currentWeekEnd.getDate()).padStart(2, '0')}`;
    
    // 计算本月开始和结束
    const currentMonthStart = new Date(year, month - 1, 1);
    const currentMonthEnd = new Date(year, month, 0);
    
    const monthStartStr = `${currentMonthStart.getFullYear()}-${String(currentMonthStart.getMonth() + 1).padStart(2, '0')}-${String(currentMonthStart.getDate()).padStart(2, '0')}`;
    const monthEndStr = `${currentMonthEnd.getFullYear()}-${String(currentMonthEnd.getMonth() + 1).padStart(2, '0')}-${String(currentMonthEnd.getDate()).padStart(2, '0')}`;
    
    this.setData({
      today: todayStr,
      yesterday: yesterdayStr,
      currentWeekStart: weekStartStr,
      currentWeekEnd: weekEndStr,
      currentMonthStart: monthStartStr,
      currentMonthEnd: monthEndStr,
      startDate: todayStr,
      endDate: todayStr
    });
  },

  /**
   * 获取员工列表
   */
  fetchStaffList() {
    wx.cloud.callFunction({
      name: 'staffManager',
      data: {
        type: 'admin',
        action: 'getStaffList',
        data: {}
      },
      success: res => {
        console.log('获取员工列表成功：', res);
        
        if (res.result && res.result.code === 0) {
          const staffList = res.result.data.list || [];
          
          // 添加"全部"选项
          const staffOptions = [
            { _id: '', name: '全部员工' },
            ...staffList
          ];
          
          this.setData({
            staffList: staffOptions
          });
        } else {
          console.error('获取员工列表失败：', res.result);
          wx.showToast({
            title: '获取员工列表失败',
            icon: 'none'
          });
        }
      },
      fail: err => {
        console.error('获取员工列表失败：', err);
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 获取店铺业绩数据
   */
  fetchBusinessData() {
    const { startDate, endDate, selectedStaff } = this.data;
    
    this.setData({ loading: true });
    
    wx.cloud.callFunction({
      name: 'staffManager',
      data: {
        type: 'admin',
        action: 'getBusinessStats',
        data: {
          startDate: startDate,
          endDate: endDate,
          staffId: selectedStaff
        }
      },
      success: res => {
        console.log('获取业绩数据成功：', res);
        
        if (res.result && res.result.code === 0) {
          // 处理日期显示格式
          if (res.result.data && res.result.data.dateStats) {
            res.result.data.dateStats.forEach(item => {
              // 为每个日期添加简化格式
              item.shortDate = this.formatShortDate(item.date);
            });
          }
          
          this.setData({
            businessData: res.result.data,
            loading: false
          });
        } else {
          console.error('获取业绩数据失败：', res.result);
          wx.showToast({
            title: res.result?.message || '获取业绩数据失败',
            icon: 'none'
          });
          this.setData({ loading: false });
        }
      },
      fail: err => {
        console.error('获取业绩数据失败：', err);
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
        this.setData({ loading: false });
      }
    });
  },
  
  /**
   * 格式化简短日期 (YYYY-MM-DD 转为 MM.DD)
   */
  formatShortDate(dateStr) {
    if (!dateStr) return '';
    
    // 尝试解析日期字符串
    try {
      // 如果是标准格式YYYY-MM-DD
      if (dateStr.includes('-')) {
        const parts = dateStr.split('-');
        if (parts.length === 3) {
          // 提取月和日
          const month = parseInt(parts[1], 10);
          const day = parseInt(parts[2], 10);
          return `${month}.${day}`;
        }
      }
      
      // 如果是Date对象或其他格式，尝试转换
      const date = new Date(dateStr);
      if (!isNaN(date.getTime())) {
        const month = date.getMonth() + 1;
        const day = date.getDate();
        return `${month}.${day}`;
      }
      
      // 如果上述方法都失败，尝试匹配年月日格式
      const dateRegex = /(\d{4})[-/]?(\d{1,2})[-/]?(\d{1,2})/;
      const match = dateStr.match(dateRegex);
      if (match && match.length >= 3) {
        return `${parseInt(match[2], 10)}.${parseInt(match[3], 10)}`;
      }
      
      // 如果所有尝试都失败，返回原始字符串
      console.log('无法格式化日期:', dateStr);
      return dateStr;
    } catch (e) {
      console.error('日期格式化错误:', e);
      return dateStr;
    }
  },

  /**
   * 查看员工业绩详情
   */
  viewStaffPerformance(e) {
    const staffId = e.currentTarget.dataset.id;
    const staffName = e.currentTarget.dataset.name;
    
    if (!staffId) return;
    
    this.setData({ loading: true });
    
    wx.cloud.callFunction({
      name: 'staffManager',
      data: {
        type: 'admin',
        action: 'getStaffPerformance',
        data: {
          staffId: staffId,
          startDate: this.data.startDate,
          endDate: this.data.endDate
        }
      },
      success: res => {
        console.log('获取员工业绩详情成功：', res);
        
        if (res.result && res.result.code === 0) {
          // 处理业绩数据，确保所有金额都有正确的格式
          const performanceData = res.result.data;
          
          // 添加支付方式的中文显示
          if (performanceData.list) {
            performanceData.list.forEach(item => {
              item.paymentMethodText = item.paymentMethod === 'balance' ? '余额支付' : '现金支付';
              
              // 格式化日期和时间
              if (item.completeTime) {
                if (typeof item.completeTime === 'string') {
                  // 如果是字符串格式，直接使用
                  const parts = item.completeTime.split(' ');
                  const dateParts = parts[0].split('-');
                  if (dateParts.length === 3) {
                    // 只保留月份和日期
                    item.dateStr = `${parseInt(dateParts[1])}-${parseInt(dateParts[2])}`;
                  } else {
                    item.dateStr = parts[0] || '';
                  }
                  item.timeStr = parts[1] || '';
                } else {
                  // 如果是其他格式，尝试转换
                  try {
                    const date = new Date(item.completeTime);
                    // 只显示月份和日期
                    item.dateStr = `${date.getMonth() + 1}-${date.getDate()}`;
                    item.timeStr = `${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
                  } catch (err) {
                    console.error('日期格式化失败：', err);
                    item.dateStr = '';
                    item.timeStr = '';
                  }
                }
              }
            });
          }
          
          this.setData({
            staffPerformance: {
              ...performanceData,
              staffName: staffName,
              staffId: staffId,
              // 添加支付方式分类统计
              cashStats: performanceData.cashStats || {
                amount: 0,
                commission: 0,
                count: 0
              },
              balanceStats: performanceData.balanceStats || {
                amount: 0,
                commission: 0,
                count: 0
              }
            },
            showStaffPerformance: true,
            loading: false
          });
        } else {
          console.error('获取员工业绩详情失败：', res.result);
          wx.showToast({
            title: res.result?.message || '获取员工业绩详情失败',
            icon: 'none'
          });
          this.setData({ loading: false });
        }
      },
      fail: err => {
        console.error('获取员工业绩详情失败：', err);
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
        this.setData({ loading: false });
      }
    });
  },

  /**
   * 关闭员工业绩面板
   */
  closeStaffPerformance() {
    this.setData({
      showStaffPerformance: false
    });
  },

  /**
   * 切换视图
   */
  switchView(e) {
    const view = e.currentTarget.dataset.view;
    this.setData({ currentView: view });
    
    // 如果切换到支出管理视图，获取支出数据
    if (view === 'expense') {
      // 在支出管理视图中，不应受员工筛选影响，因此获取所有支出数据
      this.fetchExpenseData();
    } else if (view === 'store') {
      // 店铺业绩视图需要考虑员工筛选
      this.fetchBusinessData();
    }
  },

  /**
   * 选择员工
   */
  onStaffChange(e) {
    const index = e.detail.value;
    const staff = this.data.staffList[index];
    
    this.setData({
      selectedStaff: staff._id,
      selectedStaffName: staff.name
    });
    
    // 刷新数据
    this.fetchBusinessData();
  },

  /**
   * 显示开始日期选择器
   */
  showStartDatePicker() {
    this.setData({
      showStartDatePicker: true
    });
  },

  /**
   * 开始日期变化
   */
  onStartDateChange(e) {
    const date = e.detail.value;
    
    this.setData({
      startDate: date,
      showStartDatePicker: false
    });
  },

  /**
   * 显示结束日期选择器
   */
  showEndDatePicker() {
    this.setData({
      showEndDatePicker: true
    });
  },

  /**
   * 结束日期变化
   */
  onEndDateChange(e) {
    const date = e.detail.value;
    
    this.setData({
      endDate: date,
      showEndDatePicker: false
    });
  },

  /**
   * 查询按钮点击
   */
  queryData() {
    const { currentView } = this.data;
    if (currentView === 'expense') {
      this.fetchExpenseData();
    } else {
      this.fetchBusinessData();
    }
  },

  /**
   * 设置日期快捷方式
   */
  setDateRange(e) {
    const range = e.currentTarget.dataset.range;
    const { currentView } = this.data;
    
    switch(range) {
      case 'today':
        this.setData({
          startDate: this.data.today,
          endDate: this.data.today
        });
        break;
      case 'yesterday':
        this.setData({
          startDate: this.data.yesterday,
          endDate: this.data.yesterday
        });
        break;
      case 'week':
        this.setData({
          startDate: this.data.currentWeekStart,
          endDate: this.data.currentWeekEnd
        });
        break;
      case 'month':
        this.setData({
          startDate: this.data.currentMonthStart,
          endDate: this.data.currentMonthEnd
        });
        break;
    }
    
    // 自动查询
    if (currentView === 'expense') {
      this.fetchExpenseData();
    } else {
      this.fetchBusinessData();
    }
  },

  /**
   * 清除员工筛选
   */
  clearStaffFilter() {
    this.setData({
      selectedStaff: '',
      selectedStaffName: '全部员工'
    });
    
    // 刷新数据
    this.fetchBusinessData();
  },

  /**
   * 返回上一页
   */
  goBack() {
    wx.navigateBack({
      delta: 1
    });
  },

  /**
   * 获取支出数据
   */
  fetchExpenseData() {
    const { startDate, endDate } = this.data;
    
    this.setData({ loading: true });
    
    wx.cloud.callFunction({
      name: 'expenseManager',
      data: {
        type: 'getExpenseStats',
        data: {
          startDate: startDate,
          endDate: endDate
        }
      },
      success: res => {
        console.log('获取支出统计数据成功：', res);
        
        if (res.result && res.result.success) {
          this.setData({
            expenseData: res.result,
            loading: false
          });
          
          // 获取支出列表
          this.fetchExpenseList();
        } else {
          console.error('获取支出统计数据失败：', res.result);
          wx.showToast({
            title: res.result?.message || '获取支出统计数据失败',
            icon: 'none'
          });
          this.setData({ loading: false });
        }
      },
      fail: err => {
        console.error('获取支出统计数据失败：', err);
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
        this.setData({ loading: false });
      }
    });
  },
  
  /**
   * 获取支出列表
   */
  fetchExpenseList() {
    const { startDate, endDate, selectedStaff, expensePagination, currentView } = this.data;
    
    wx.cloud.callFunction({
      name: 'expenseManager',
      data: {
        type: 'getExpenses',
        data: {
          startDate: startDate,
          endDate: endDate,
          staffId: currentView === 'expense' ? '' : selectedStaff,
          page: expensePagination.page,
          pageSize: expensePagination.pageSize
        }
      },
      success: res => {
        console.log('获取支出列表成功：', res);
        
        if (res.result && res.result.success) {
          // 格式化日期和金额
          let expenseList = res.result.list || [];

          // 确保按时间排序（最新的在前面）
          expenseList.sort((a, b) => {
            const dateA = new Date(a.createTime);
            const dateB = new Date(b.createTime);
            return dateB - dateA; // 降序排列，最新的在前面
          });

          expenseList.forEach(item => {
            if (item.createTime) {
              // 兼容多种时间格式的处理
              let date;

              if (typeof item.createTime === 'string') {
                if (item.createTime.includes('T') && item.createTime.includes('Z')) {
                  // ISO格式: "2025-07-24T22:01:22.169Z"
                  date = new Date(item.createTime);
                } else {
                  // 普通格式: "2025-07-25 06:05:47"，转换为兼容iOS的格式
                  const isoString = item.createTime.replace(/-/g, '/');
                  date = new Date(isoString);
                }
              } else {
                // Date对象或其他格式
                date = new Date(item.createTime);
              }

              if (!isNaN(date.getTime())) {
                item.formattedDate = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
              } else {
                console.error('无法解析时间格式:', item.createTime);
                item.formattedDate = String(item.createTime);
              }
            }
            if (item.amount) {
              item.formattedAmount = parseFloat(item.amount).toFixed(2);
            }
          });
          
          this.setData({
            expenseList: expenseList,
            'expensePagination.total': res.result.total || 0
          });
        } else {
          console.error('获取支出列表失败：', res.result);
          wx.showToast({
            title: res.result?.message || '获取支出列表失败',
            icon: 'none'
          });
        }
      },
      fail: err => {
        console.error('获取支出列表失败：', err);
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      }
    });
  },
  
  /**
   * 查看支出详情
   */
  viewExpenseDetail(e) {
    const expenseId = e.currentTarget.dataset.id;
    const expense = this.data.expenseList.find(item => item._id === expenseId);
    
    if (expense) {
      this.setData({
        currentExpense: expense,
        showExpenseDetail: true
      });
    }
  },
  
  /**
   * 关闭支出详情
   */
  closeExpenseDetail() {
    this.setData({
      showExpenseDetail: false
    });
  },
  
  /**
   * 删除支出记录
   */
  deleteExpense(e) {
    const expenseId = e.currentTarget.dataset.id;
    
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这条支出记录吗？此操作不可恢复。',
      success: res => {
        if (res.confirm) {
          wx.showLoading({
            title: '删除中...',
            mask: true
          });
          
          wx.cloud.callFunction({
            name: 'expenseManager',
            data: {
              type: 'deleteExpense',
              data: {
                expenseId: expenseId
              }
            },
            success: res => {
              wx.hideLoading();
              
              if (res.result && res.result.success) {
                wx.showToast({
                  title: '删除成功',
                  icon: 'success'
                });
                
                // 关闭详情弹窗
                this.setData({
                  showExpenseDetail: false
                });
                
                // 重新获取支出数据
                this.fetchExpenseData();
              } else {
                console.error('删除支出记录失败：', res.result);
                wx.showToast({
                  title: res.result?.message || '删除失败',
                  icon: 'none'
                });
              }
            },
            fail: err => {
              wx.hideLoading();
              console.error('删除支出记录失败：', err);
              wx.showToast({
                title: '网络错误，请重试',
                icon: 'none'
              });
            }
          });
        }
      }
    });
  },

  /**
   * 预览图片
   */
  previewImage(e) {
    const current = e.currentTarget.dataset.current;
    const urls = e.currentTarget.dataset.urls;
    
    wx.previewImage({
      current,
      urls
    });
  }
}) 