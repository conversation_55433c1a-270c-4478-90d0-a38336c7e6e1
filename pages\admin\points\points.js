// pages/admin/points/points.js
const app = getApp()

Page({
  /**
   * 页面的初始数据
   */
  data: {
    activeTab: 'settings', // 当前激活的标签页：settings(积分设置), withdrawals(提现申请), records(积分记录)
    
    // 积分设置相关
    pointsSettings: {
      servicePoints: 10, // 分享服务项目获得的积分
      articlePoints: 5, // 分享灵感文章获得的积分
      videoPoints: 8, // 分享视频内容获得的积分
      exchangeRatio: 1 // 积分兑换比例，多少积分兑换1元
    },
    loadingSettings: true, // 加载设置状态
    
    // 提现申请相关
    withdrawals: [], // 提现申请列表
    loadingWithdrawals: true, // 加载提现申请状态
    showWithdrawalDetail: false, // 是否显示提现详情
    currentWithdrawal: null, // 当前查看的提现申请
    adminRemark: '', // 管理员处理提现申请的备注
    
    // 积分记录相关
    pointsRecords: [], // 积分记录列表
    loadingRecords: true, // 加载积分记录状态
    pointsStats: { // 积分记录统计数据
      totalRecords: 0,
      totalPoints: 0,
      totalAmount: '0.00', // 新增总金额字段
      uniqueUsers: 0
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    // 检查管理员登录状态
    if (!app.globalData.isAdmin) {
      wx.showToast({
        title: '请先登录管理员账号',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
      return;
    }
    
    // 加载积分设置
    this.loadPointsSettings();
    
    // 加载提现申请
    this.loadWithdrawals();
    
    // 加载积分记录
    this.loadPointsRecords();
  },

  /**
   * 切换标签页
   */
  switchTab: function (e) {
    const tab = e.currentTarget.dataset.tab;
    this.setData({
      activeTab: tab
    });
    
    // 根据标签页加载对应数据
    if (tab === 'settings' && this.data.loadingSettings) {
      this.loadPointsSettings();
    } else if (tab === 'withdrawals' && this.data.loadingWithdrawals) {
      this.loadWithdrawals();
    } else if (tab === 'records' && this.data.loadingRecords) {
      this.loadPointsRecords();
    }
  },

  /**
   * 加载积分设置
   */
  loadPointsSettings: function () {
    this.setData({ loadingSettings: true });
    
    wx.cloud.callFunction({
      name: 'pointsManager',
      data: {
        action: 'getPointsSettings'
      }
    }).then(res => {
      console.log('获取积分设置成功:', res.result);
      
      if (res.result && res.result.code === 0 && res.result.data) {
        this.setData({
          pointsSettings: res.result.data,
          loadingSettings: false
        });
      } else {
        // 如果没有设置，使用默认值
        this.setData({ loadingSettings: false });
      }
    }).catch(err => {
      console.error('获取积分设置失败:', err);
      this.setData({ loadingSettings: false });
      
      wx.showToast({
        title: '获取积分设置失败',
        icon: 'none'
      });
    });
  },

  /**
   * 输入框变化处理
   */
  onInputChange: function (e) {
    const field = e.currentTarget.dataset.field;
    const value = e.detail.value;
    
    // 更新对应字段
    this.setData({
      [`pointsSettings.${field}`]: value
    });
  },

  /**
   * 保存积分设置
   */
  saveSettings: function () {
    const { pointsSettings } = this.data;
    
    // 检查输入是否有效
    if (!pointsSettings.servicePoints || !pointsSettings.articlePoints || 
        !pointsSettings.videoPoints || !pointsSettings.exchangeRatio) {
      wx.showToast({
        title: '请填写完整的积分设置',
        icon: 'none'
      });
      return;
    }
    
    wx.showLoading({
      title: '保存中...',
      mask: true
    });
    
    wx.cloud.callFunction({
      name: 'pointsManager',
      data: {
        action: 'savePointsSettings',
        settings: pointsSettings
      }
    }).then(res => {
      wx.hideLoading();
      console.log('保存积分设置结果:', res.result);
      
      if (res.result && res.result.code === 0) {
        wx.showToast({
          title: '保存成功',
          icon: 'success'
        });
      } else {
        wx.showToast({
          title: '保存失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      wx.hideLoading();
      console.error('保存积分设置失败:', err);
      
      wx.showToast({
        title: '保存失败',
        icon: 'none'
      });
    });
  },

  /**
   * 加载提现申请
   */
  loadWithdrawals: function () {
    this.setData({ loadingWithdrawals: true });
    
    wx.cloud.callFunction({
      name: 'pointsManager',
      data: {
        action: 'getAllWithdrawals',
        data: {} // 添加空的data对象，避免undefined
      }
    }).then(res => {
      console.log('获取提现申请成功:', res.result);
      
      if (res.result && res.result.code === 0) {
        // 格式化提现申请数据
        const withdrawals = (res.result.data || []).map(item => {
          // 格式化时间
          const createTime = new Date(item.createTime);
          const dateStr = `${createTime.getFullYear()}-${String(createTime.getMonth() + 1).padStart(2, '0')}-${String(createTime.getDate()).padStart(2, '0')}`;
          const timeStr = `${String(createTime.getHours()).padStart(2, '0')}:${String(createTime.getMinutes()).padStart(2, '0')}`;
          const dateTimeStr = `${dateStr} ${timeStr}`;
          
          // 格式化状态
          let statusText = '';
          switch (item.status) {
            case 'pending':
              statusText = '待处理';
              break;
            case 'completed':
              statusText = '已完成';
              break;
            case 'rejected':
              statusText = '已拒绝';
              break;
            default:
              statusText = '未知状态';
          }
          
          // 计算金额
          const amount = item.points / (this.data.pointsSettings.exchangeRatio || 1);
          
          return {
            ...item,
            dateStr,
            timeStr,
            dateTimeStr,
            statusText,
            amount: amount.toFixed(2)
          };
        });
        
        // 按时间排序，最新的在前面
        withdrawals.sort((a, b) => {
          return new Date(b.createTime) - new Date(a.createTime);
        });
        
        this.setData({
          withdrawals,
          loadingWithdrawals: false
        });
      } else {
        this.setData({
          withdrawals: [],
          loadingWithdrawals: false
        });
      }
    }).catch(err => {
      console.error('获取提现申请失败:', err);
      
      this.setData({
        withdrawals: [],
        loadingWithdrawals: false
      });
      
      wx.showToast({
        title: '获取提现申请失败',
        icon: 'none'
      });
    });
  },

  /**
   * 查看提现详情
   */
  viewWithdrawal: function (e) {
    const id = e.currentTarget.dataset.id;
    const withdrawal = this.data.withdrawals.find(item => item._id === id);
    
    if (withdrawal) {
      this.setData({
        currentWithdrawal: withdrawal,
        showWithdrawalDetail: true,
        adminRemark: '' // 重置备注
      });
    }
  },

  /**
   * 关闭提现详情
   */
  closeWithdrawalDetail: function () {
    this.setData({
      showWithdrawalDetail: false
    });
  },

  /**
   * 预览收款码
   */
  previewQrCode: function () {
    if (this.data.currentWithdrawal && this.data.currentWithdrawal.paymentQrCode) {
      wx.previewImage({
        urls: [this.data.currentWithdrawal.paymentQrCode]
      });
    }
  },

  /**
   * 扫码转账 - 长按收款码触发
   */
  scanQrCode: function () {
    if (!this.data.currentWithdrawal || !this.data.currentWithdrawal.paymentQrCode) {
      wx.showToast({
        title: '收款码不存在',
        icon: 'none'
      });
      return;
    }

    const qrCodeUrl = this.data.currentWithdrawal.paymentQrCode;
    
    wx.showModal({
      title: '扫码转账',
      content: '是否要扫描此收款码进行转账？',
      confirmText: '扫码转账',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          // 方法1：尝试使用图片识别二维码
          this.recognizeQrCodeFromImage(qrCodeUrl);
        }
      }
    });
  },

  /**
   * 从图片识别二维码内容
   */
  recognizeQrCodeFromImage: function (imageUrl) {
    wx.showLoading({
      title: '识别中...',
      mask: true
    });

    // 下载图片到本地
    wx.downloadFile({
      url: imageUrl,
      success: (res) => {
        if (res.statusCode === 200) {
          const tempFilePath = res.tempFilePath;
          
          // 使用微信API识别二维码
          wx.scanCode({
            scanType: ['qrCode'],
            success: (scanRes) => {
              wx.hideLoading();
              console.log('扫码结果:', scanRes);
              
              // 处理扫码结果
              this.handleScanResult(scanRes);
            },
            fail: (err) => {
              wx.hideLoading();
              console.error('扫码失败:', err);
              
              // 如果直接扫码失败，尝试其他方法
              this.fallbackScanMethod(tempFilePath);
            }
          });
        } else {
          wx.hideLoading();
          wx.showToast({
            title: '图片下载失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        wx.hideLoading();
        console.error('下载图片失败:', err);
        
        // 如果下载失败，直接调用扫码功能
        this.directScanCode();
      }
    });
  },

  /**
   * 备用扫码方法
   */
  fallbackScanMethod: function (imagePath) {
    // 尝试直接调用系统扫码功能
    wx.scanCode({
      success: (res) => {
        console.log('备用扫码成功:', res);
        this.handleScanResult(res);
      },
      fail: (err) => {
        console.error('备用扫码失败:', err);
        wx.showModal({
          title: '提示',
          content: '无法自动识别二维码，请手动保存图片后使用其他应用扫码转账',
          showCancel: false
        });
      }
    });
  },

  /**
   * 直接调用扫码功能
   */
  directScanCode: function () {
    wx.scanCode({
      success: (res) => {
        console.log('直接扫码成功:', res);
        this.handleScanResult(res);
      },
      fail: (err) => {
        console.error('直接扫码失败:', err);
        wx.showToast({
          title: '扫码功能不可用',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 处理扫码结果
   */
  handleScanResult: function (scanResult) {
    console.log('处理扫码结果:', scanResult);
    
    if (scanResult.result) {
      // 检查是否是支付宝或微信支付码
      const result = scanResult.result;
      
      if (result.includes('alipay') || result.includes('支付宝')) {
        // 支付宝收款码
        wx.showModal({
          title: '支付宝转账',
          content: '检测到支付宝收款码，将跳转到支付宝进行转账',
          showCancel: false,
          success: () => {
            // 尝试跳转到支付宝
            wx.navigateToMiniProgram({
              appId: 'alipay', // 支付宝小程序ID
              path: '',
              success: () => {
                console.log('跳转支付宝成功');
              },
              fail: () => {
                wx.showToast({
                  title: '请手动打开支付宝扫码',
                  icon: 'none'
                });
              }
            });
          }
        });
      } else if (result.includes('wxp://') || result.includes('微信')) {
        // 微信收款码
        wx.showModal({
          title: '微信转账',
          content: '检测到微信收款码，请在微信中扫码转账',
          showCancel: false
        });
      } else {
        // 其他类型的二维码
        wx.showModal({
          title: '二维码内容',
          content: result,
          showCancel: false
        });
      }
    } else {
      wx.showToast({
        title: '未识别到有效内容',
        icon: 'none'
      });
    }
  },

  /**
   * 通过提现申请
   */
  approveWithdrawal: function (e) {
    const id = e.currentTarget.dataset.id;
    this.processWithdrawal(id, 'approve');
  },

  /**
   * 拒绝提现申请
   */
  rejectWithdrawal: function (e) {
    const id = e.currentTarget.dataset.id;
    this.processWithdrawal(id, 'reject');
  },

  /**
   * 通过当前提现申请
   */
  approveCurrentWithdrawal: function () {
    if (this.data.currentWithdrawal) {
      this.processWithdrawal(this.data.currentWithdrawal._id, 'approve');
    }
  },

  /**
   * 拒绝当前提现申请
   */
  rejectCurrentWithdrawal: function () {
    if (this.data.currentWithdrawal) {
      this.processWithdrawal(this.data.currentWithdrawal._id, 'reject');
    }
  },

  /**
   * 处理提现申请
   */
  processWithdrawal: function (id, action) {
    if (!id) {
      console.error('处理提现申请失败: 缺少ID参数');
      wx.showToast({
        title: '操作失败：缺少必要参数',
        icon: 'none'
      });
      return;
    }
    
    const actionText = action === 'approve' ? '通过' : '拒绝';
    const remark = this.data.adminRemark || (action === 'approve' ? '提现申请已通过，资金将尽快到账' : '提现申请被拒绝');
    
    // 使用固定的管理员标识符，只要用户登录了管理员账号，就认为有权限处理
    const adminId = 'admin'; // 固定的管理员ID
    const adminName = '管理员';
    
    // 打印当前处理的参数，便于调试
    console.log('处理提现申请参数:', {
      withdrawalId: id,
      status: action === 'approve' ? 'completed' : 'rejected',
      adminId: adminId,
      adminName: adminName,
      remark: remark
    });
    
    wx.showModal({
      title: '确认操作',
      content: `确定要${actionText}此提现申请吗？`,
      success: res => {
        if (res.confirm) {
          wx.showLoading({
            title: '处理中...',
            mask: true
          });
          
          wx.cloud.callFunction({
            name: 'pointsManager',
            data: {
              action: 'processWithdrawal',
              data: {
                withdrawalId: id,
                status: action === 'approve' ? 'completed' : 'rejected',
                adminId: adminId,
                adminName: adminName,
                remark: remark
              }
            }
          }).then(res => {
            wx.hideLoading();
            console.log(`${actionText}提现申请结果:`, res.result);
            
            if (res.result && res.result.code === 0) {
              wx.showToast({
                title: `${actionText}成功`,
                icon: 'success'
              });
              
              // 关闭详情弹窗
              this.setData({
                showWithdrawalDetail: false,
                adminRemark: '' // 清空备注
              });
              
              // 重新加载提现申请
              this.loadWithdrawals();
              // 重新加载积分记录，包含已通过的提现记录
              this.loadPointsRecords();
            } else {
              wx.showToast({
                title: res.result && res.result.message ? res.result.message : `${actionText}失败`,
                icon: 'none'
              });
            }
          }).catch(err => {
            wx.hideLoading();
            console.error(`${actionText}提现申请失败:`, err);
            
            // 显示更详细的错误信息
            wx.showToast({
              title: `${actionText}失败: ${err.message || '未知错误'}`,
              icon: 'none'
            });
          });
        }
      }
    });
  },

  /**
   * 加载积分记录
   */
  loadPointsRecords: function () {
    this.setData({ loadingRecords: true });
    
    // 获取已通过的提现申请
    wx.cloud.callFunction({
      name: 'pointsManager',
      data: {
        action: 'getAllWithdrawals',
        data: {
          status: 'completed' // 只获取已通过的提现申请
        }
      }
    }).then(res => {
      console.log('获取已通过的提现申请成功:', res.result);
      
      if (res.result && res.result.code === 0) {
        // 格式化提现记录数据
        const pointsRecords = (res.result.data || []).map(item => {
          // 格式化时间
          const createTime = new Date(item.processTime || item.createTime);
          const dateStr = `${createTime.getFullYear()}-${String(createTime.getMonth() + 1).padStart(2, '0')}-${String(createTime.getDate()).padStart(2, '0')}`;
          const timeStr = `${String(createTime.getHours()).padStart(2, '0')}:${String(createTime.getMinutes()).padStart(2, '0')}`;
          
          // 计算兑换金额，使用当前积分设置或默认比例(1:1)
          const exchangeRatio = this.data.pointsSettings.exchangeRatio || 1;
          const amount = (item.points / exchangeRatio).toFixed(2);
          
          return {
            _id: item._id,
            userName: item.userInfo ? item.userInfo.nickName : '用户',
            points: item.points,
            amount: amount, // 添加金额字段
            typeText: '积分提现',
            dateStr,
            timeStr,
            createTime: item.processTime || item.createTime
          };
        });
        
        // 按时间排序，最新的在前面
        pointsRecords.sort((a, b) => {
          return new Date(b.createTime) - new Date(a.createTime);
        });
        
        // 计算统计数据
        const totalRecords = pointsRecords.length;
        const totalPoints = pointsRecords.reduce((sum, item) => sum + item.points, 0);
        const totalAmount = pointsRecords.reduce((sum, item) => sum + parseFloat(item.amount), 0).toFixed(2);
        const uniqueUsers = new Set(pointsRecords.map(item => item.userName)).size;
        
        this.setData({
          pointsRecords,
          pointsStats: {
            totalRecords,
            totalPoints,
            totalAmount,
            uniqueUsers
          },
          loadingRecords: false
        });
      } else {
        this.setData({
          pointsRecords: [],
          pointsStats: {
            totalRecords: 0,
            totalPoints: 0,
            totalAmount: '0.00',
            uniqueUsers: 0
          },
          loadingRecords: false
        });
      }
    }).catch(err => {
      console.error('获取积分记录失败:', err);
      
      this.setData({
        pointsRecords: [],
        pointsStats: {
          totalRecords: 0,
          totalPoints: 0,
          totalAmount: '0.00',
          uniqueUsers: 0
        },
        loadingRecords: false
      });
      
      wx.showToast({
        title: '获取积分记录失败',
        icon: 'none'
      });
    });
  },

  /**
   * 刷新数据
   */
  refreshData: function () {
    const { activeTab } = this.data;
    
    if (activeTab === 'settings') {
      this.loadPointsSettings();
    } else if (activeTab === 'withdrawals') {
      this.loadWithdrawals();
    } else if (activeTab === 'records') {
      this.loadPointsRecords();
    }
  },

  /**
   * 返回上一页
   */
  goBack: function () {
    wx.navigateBack();
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh: function () {
    this.refreshData();
    wx.stopPullDownRefresh();
  },

  /**
   * 输入处理备注
   */
  onRemarkInput: function(e) {
    this.setData({
      adminRemark: e.detail.value
    });
  }
}) 