// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV }) // 使用当前云环境

const db = cloud.database()
const _ = db.command

// 初始化集合函数
async function initCollections() {
  try {
    // 检查并创建users集合
    try {
      await db.createCollection('users')
      console.log('创建users集合成功')
    } catch (err) {
      // 如果集合已存在，会抛出错误，这是正常的
      console.log('users集合已存在或创建失败:', err.message)
    }
    
    return true
  } catch (err) {
    console.error('初始化集合失败:', err)
    return false
  }
}

// 云函数入口函数
exports.main = async (event, context) => {
  // 初始化集合
  await initCollections()
  
  console.log('userManager云函数收到请求:', event)
  const { action } = event
  
  // 根据action参数调用不同的处理函数
  switch (action) {
    case 'getOpenid':
      return getOpenid(context)
    case 'saveUserInfo':
      return saveUserInfo(event, context)
    case 'updateUserInfo':
      return updateUserInfo(event, context)
    case 'getUserDetail':
      return getUserDetail(context)
    case 'login':
      return login(context)
    default:
      return {
        code: -1,
        message: `未知的action: ${action}`
      }
  }
}

// 获取用户openid
async function getOpenid(context) {
  const wxContext = cloud.getWXContext()
  return {
    code: 0,
    openid: wxContext.OPENID,
    appid: wxContext.APPID,
    unionid: wxContext.UNIONID,
  }
}

// 登录函数
async function login(context) {
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID
  
  console.log('用户登录, openid:', openid)
  
  try {
    // 查询用户是否已存在
    const user = await db.collection('users')
      .where({ openid })
      .get()
    
    return {
      code: 0,
      message: '登录成功',
      data: {
        openid,
        userInfo: user.data.length > 0 ? user.data[0] : null
      }
    }
  } catch (err) {
    console.error('登录失败:', err)
    return {
      code: -1,
      message: '登录失败: ' + err.message
    }
  }
}

// 保存用户信息
async function saveUserInfo(event, context) {
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID
  let userInfo = event.userInfo || {}
  
  console.log('保存用户信息, openid:', openid)
  console.log('收到的用户信息:', userInfo)
  
  // 特别重要: 添加时间戳
  const now = new Date()
  userInfo.updateTime = now
  
  try {
    // 检查用户是否已存在
    const userQueryResult = await db.collection('users')
      .where({ openid })
      .get()
    
    // 用来存储要保存的最终用户信息
    let finalUserInfo = {...userInfo}
    
    // 处理云存储头像和自定义标记
    if (finalUserInfo.avatarUrl && finalUserInfo.avatarUrl.startsWith('cloud://')) {
      console.log('检测到云存储头像，设置customAvatar为true')
      finalUserInfo.customAvatar = true
    }
    
    if (userQueryResult.data && userQueryResult.data.length > 0) {
      // 用户已存在，需要合并信息
      const existingUser = userQueryResult.data[0]
      console.log('找到现有用户记录:', existingUser)
      
      // 保护现有自定义头像
      if (existingUser.customAvatar === true && 
          existingUser.avatarUrl && 
          existingUser.avatarUrl.startsWith('cloud://')) {
        console.log('保留现有自定义头像:', existingUser.avatarUrl)
        finalUserInfo.avatarUrl = existingUser.avatarUrl
        finalUserInfo.customAvatar = true
      }
      
      // 保护自定义昵称
      if (existingUser.customNickname === true && existingUser.nickName) {
        console.log('保留自定义昵称:', existingUser.nickName)
        finalUserInfo.nickName = existingUser.nickName
        finalUserInfo.customNickname = true
      }
      
      // 保留手机号码
      if (existingUser.phoneNumber) {
        finalUserInfo.phoneNumber = existingUser.phoneNumber
      }
      
      // 保留签名
      if (existingUser.signature) {
        finalUserInfo.signature = existingUser.signature
      }
      
      // 更新用户信息
      await db.collection('users').where({ openid }).update({
        data: finalUserInfo
      })
      
      console.log('更新用户信息成功')
    } else {
      // 新用户，创建记录
      finalUserInfo.openid = openid
      finalUserInfo.createTime = now
      
      await db.collection('users').add({
        data: finalUserInfo
      })
      
      console.log('创建新用户成功')
    }
    
    // 查询更新后的完整用户信息
    const updatedUser = await db.collection('users')
      .where({ openid })
      .get()
    
    return {
      code: 0,
      message: '保存用户信息成功',
      data: {
        openid,
        userInfo: updatedUser.data[0] || finalUserInfo
      }
    }
  } catch (err) {
    console.error('保存用户信息失败:', err)
    return {
      code: -1,
      message: '保存用户信息失败: ' + err.message
    }
  }
}

// 更新用户信息
async function updateUserInfo(event, context) {
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID
  let userInfo = event.userInfo || {}
  
  console.log('更新用户信息, openid:', openid)
  console.log('收到的更新数据:', userInfo)
  
  // 添加时间戳
  const now = new Date()
  userInfo.updateTime = now
  
  // 最重要的修复: 检查是否有云存储头像
  if (userInfo.avatarUrl && userInfo.avatarUrl.startsWith('cloud://')) {
    console.log('检测到云存储头像，强制设置customAvatar为true')
    userInfo.customAvatar = true
  }
  
  // 如果是嵌套结构，扁平化处理
  if (userInfo.userInfo && typeof userInfo.userInfo === 'object') {
    console.log('检测到嵌套的userInfo结构，扁平化处理')
    
    // 如果嵌套结构中有云存储头像，优先使用
    if (userInfo.userInfo.avatarUrl && userInfo.userInfo.avatarUrl.startsWith('cloud://')) {
      console.log('从嵌套结构中提取云存储头像')
      userInfo.avatarUrl = userInfo.userInfo.avatarUrl
      userInfo.customAvatar = true
    }
    
    // 合并其他字段
    Object.keys(userInfo.userInfo).forEach(key => {
      if (key !== 'userInfo') { // 避免循环嵌套
        userInfo[key] = userInfo.userInfo[key]
      }
    })
    
    // 移除嵌套结构
    delete userInfo.userInfo
  }
  
  try {
    // 查询用户是否存在
    const userQueryResult = await db.collection('users')
      .where({ openid })
      .get()
    
    if (userQueryResult.data && userQueryResult.data.length > 0) {
      const existingUser = userQueryResult.data[0]
      console.log('找到现有用户记录:', existingUser)
      
      // 特殊逻辑: 如果现有用户有自定义头像但新数据要覆盖它
      if (existingUser.customAvatar === true && 
          existingUser.avatarUrl && 
          existingUser.avatarUrl.startsWith('cloud://')) {
        
        // 如果新数据没有头像或不是云存储头像，保留现有的
        if (!userInfo.avatarUrl || !userInfo.avatarUrl.startsWith('cloud://')) {
          console.log('保留现有自定义头像:', existingUser.avatarUrl)
          userInfo.avatarUrl = existingUser.avatarUrl
          userInfo.customAvatar = true
        }
      }
      
      // 更新用户信息
      await db.collection('users').where({ openid }).update({
        data: userInfo
      })
      
      console.log('更新用户信息成功')
    } else {
      // 用户不存在，创建新记录
      userInfo.openid = openid
      userInfo.createTime = now
      
      await db.collection('users').add({
        data: userInfo
      })
      
      console.log('创建新用户成功')
    }
    
    // 获取更新后的完整用户信息
    const updatedUser = await db.collection('users')
      .where({ openid })
      .get()
    
    return {
      code: 0,
      message: '更新用户信息成功',
      data: {
        userInfo: updatedUser.data[0] || userInfo
      }
    }
  } catch (err) {
    console.error('更新用户信息失败:', err)
    return {
      code: -1,
      message: '更新用户信息失败: ' + err.message
    }
  }
}

// 获取用户详细信息
async function getUserDetail(context) {
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID
  
  console.log('获取用户详细信息, openid:', openid)
  
  try {
    // 查询用户信息
    const userResult = await db.collection('users')
      .where({ openid })
      .get()
    
    if (userResult.data && userResult.data.length > 0) {
      const userInfo = userResult.data[0]
      
      // 确保customAvatar标记正确
      if (userInfo.avatarUrl && userInfo.avatarUrl.startsWith('cloud://') && userInfo.customAvatar !== true) {
        console.log('检测到云存储头像但customAvatar不为true，自动修复')
        userInfo.customAvatar = true
        
        // 更新数据库中的标记
        await db.collection('users').where({ openid }).update({
          data: { customAvatar: true }
        })
      }
      
      // 检查嵌套结构
      if (userInfo.userInfo && typeof userInfo.userInfo === 'object') {
        console.log('检测到嵌套的userInfo结构，扁平化处理')
        
        // 如果嵌套结构中有云存储头像，优先使用
        if (userInfo.userInfo.avatarUrl && userInfo.userInfo.avatarUrl.startsWith('cloud://')) {
          console.log('从嵌套结构中提取云存储头像')
          userInfo.avatarUrl = userInfo.userInfo.avatarUrl
          userInfo.customAvatar = true
          
          // 更新数据库
          await db.collection('users').where({ openid }).update({
            data: { 
              avatarUrl: userInfo.userInfo.avatarUrl,
              customAvatar: true
            }
          })
        }
      }
      
      console.log('返回用户信息:', userInfo)
      
      return {
        code: 0,
        message: '获取用户信息成功',
        data: userInfo
      }
    } else {
      return {
        code: 1,
        message: '未找到用户信息'
      }
    }
  } catch (err) {
    console.error('获取用户信息失败:', err)
    return {
      code: -1,
      message: '获取用户信息失败: ' + err.message
    }
  }
}
