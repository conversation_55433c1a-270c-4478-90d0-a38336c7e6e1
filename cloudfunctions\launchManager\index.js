// 云函数入口文件
const cloud = require('wx-server-sdk');

// 尝试导入配置
let envId;
try {
  // 直接使用require导入配置模块
  const configModule = require('../configManager/config');
  envId = configModule.ENV_ID;
} catch (error) {
  console.error('导入配置模块失败，尝试从环境变量获取', error);
  // 仅从环境变量获取，不再使用硬编码默认值
  envId = process.env.CLOUD_ENV;
  
  if (!envId) {
    console.error('无法获取云环境ID，请在环境变量或配置文件中设置CLOUD_ENV');
  }
}

// 初始化云环境
cloud.init({
  env: envId
});

const db = cloud.database();
const _ = db.command;

// 广告配置常量（原启动页配置）
const AD_CONFIG = {
  collection: 'launchConfig', // 广告配置集合名称（保留原集合名称以兼容旧数据）
  images: {
    first: 'launch_first',   // 第一张图片名称
    second: 'launch_second'  // 第二张图片名称
  },
  supportedFormats: ['jpg', 'jpeg', 'png', 'webp', 'gif'] // 支持的图片格式
};

/**
 * 广告管理云函数（原启动页管理）
 * 用于管理小程序广告图片
 */
exports.main = async (event, context) => {
  // 获取用户openid
  const { OPENID } = cloud.getWXContext();
  
  // 解析请求参数
  const { type, action, data } = event;
  
  // 确保广告配置集合存在
  await ensureAdConfigCollection();
  
  // 根据action执行不同操作
  switch (action) {
    // 前端API - 获取广告图片（原启动页图片）
    case 'getLaunchImages':
      return getAdImages(event);
    
    // 后端API - 更新广告图片（原启动页图片）
    case 'updateLaunchImage':
      // 临时跳过权限验证
      console.log('更新图片，临时跳过权限验证');
      return updateAdImage(data);
    
    // 后端API - 初始化广告配置（原启动页配置）
    case 'initLaunchConfig':
      return initAdConfig();
    
    // 前端API - 获取广告内容（兼容前端调用）
    case 'getPromoItems':
      return getAdImages(event);
    
    default:
      return {
        code: 404,
        message: '无效的操作类型'
      };
  }
};

/**
 * 检查是否有管理员权限
 * @param {string} openid 用户openid
 * @returns {boolean} 是否有管理员权限
 */
async function checkAdminPermission(openid) {
  try {
    // 查询管理员集合
    const adminResult = await db.collection('adminUsers').where({
      openid: openid
    }).get();
    
    // 如果查询结果中有记录，则表示是管理员
    return adminResult.data && adminResult.data.length > 0;
  } catch (error) {
    console.error('检查管理员权限失败:', error);
    return false;
  }
}

/**
 * 确保广告配置集合存在
 */
async function ensureAdConfigCollection() {
  try {
    // 尝试获取集合信息，如果不存在会抛出异常
    await db.collection(AD_CONFIG.collection).count();
  } catch (error) {
    // 如果集合不存在，创建集合
    console.log('广告配置集合不存在，尝试创建...');
    try {
      await db.createCollection(AD_CONFIG.collection);
      console.log('广告配置集合创建成功');
      
      // 创建初始记录
      await initAdConfig();
    } catch (createError) {
      console.error('创建广告配置集合失败:', createError);
    }
  }
}

/**
 * 获取广告图片
 * @param {Object} event 事件参数
 * @returns {Object} 广告图片信息
 */
async function getAdImages(event = {}) {
  console.log('开始获取广告图片', event);
  
  // 检查是否强制刷新临时URL
  const forceRefresh = event.timestamp ? true : false;
  if (forceRefresh) {
    console.log('收到时间戳参数，强制刷新临时URL:', event.timestamp);
  }

  try {
    // 从数据库获取广告配置
    const configResult = await db.collection(AD_CONFIG.collection).limit(1).get();
    
    if (!configResult.data || configResult.data.length === 0) {
      console.log('未找到广告配置，尝试初始化');
      await initAdConfig();
      // 重新获取配置
      const newConfigResult = await db.collection(AD_CONFIG.collection).limit(1).get();
      if (!newConfigResult.data || newConfigResult.data.length === 0) {
        throw new Error('无法获取广告配置');
      }
      configResult.data = newConfigResult.data;
    }
    
    const config = configResult.data[0];
    console.log('获取到广告配置:', config);
    
    // 检查是否有广告图片
    if (!config.first || !config.second) {
      throw new Error('广告图片不完整');
    }
    
    // 获取临时文件URL，有效期默认为2小时
    const expireTime = 7200; // 2小时，单位秒
    
    // 获取临时访问链接
    const [firstResult, secondResult] = await Promise.all([
      cloud.getTempFileURL({
        fileList: [config.first]
      }),
      cloud.getTempFileURL({
        fileList: [config.second]
      })
    ]);
    
    // 检查结果
    if (!firstResult.fileList || !firstResult.fileList[0] || !firstResult.fileList[0].tempFileURL ||
        !secondResult.fileList || !secondResult.fileList[0] || !secondResult.fileList[0].tempFileURL) {
      throw new Error('获取临时文件URL失败');
    }
    
    // 获取临时URL
    const first = firstResult.fileList[0].tempFileURL;
    const second = secondResult.fileList[0].tempFileURL;
    
    // 添加随机参数，确保不使用缓存的图片
    const randomParam = `r=${Date.now()}${Math.floor(Math.random() * 1000)}`;
    const firstUrl = first + (first.includes('?') ? '&' : '?') + randomParam;
    const secondUrl = second + (second.includes('?') ? '&' : '?') + randomParam;
    
    // 获取文件格式信息
    const firstFormat = config.first_format || getFormatFromFileID(config.first);
    const secondFormat = config.second_format || getFormatFromFileID(config.second);
    
    // 检查是否为支持透明度的格式
    const transparentFormats = ['png', 'webp', 'gif', 'svg'];
    const firstTransparent = transparentFormats.includes(firstFormat);
    const secondTransparent = transparentFormats.includes(secondFormat);

    return {
      code: 200,
      data: {
        first: firstUrl,
        second: secondUrl,
        first_format: firstFormat,
        second_format: secondFormat,
        first_transparent: firstTransparent,
        second_transparent: secondTransparent,
        expireTime: Date.now() + expireTime * 1000 // 毫秒
      },
      message: 'success'
    };
  } catch (error) {
    console.error('获取广告图片失败:', error);
    return {
      code: 404,
      message: error.message || '获取广告图片失败',
      error: JSON.stringify(error)  // 将错误对象转为字符串，便于排查
    };
  }
}

/**
 * 从文件ID获取文件格式
 * @param {string} fileID 文件ID
 * @returns {string} 文件格式
 */
function getFormatFromFileID(fileID) {
  try {
    const ext = fileID.split('.').pop().toLowerCase();
    return ext;
  } catch (error) {
    console.error('获取文件格式失败:', error);
    return 'unknown';
  }
}

/**
 * 更新广告图片
 * @param {Object} params 请求参数
 */
async function updateAdImage(params) {
  const { fileID, type } = params;
  
  if (!fileID || !type) {
    return {
      code: 404,
      message: '缺少必要参数'
    };
  }
  
  if (type !== 'first' && type !== 'second') {
    return {
      code: 404,
      message: '无效的图片类型'
    };
  }
  
  try {
    console.log('更新广告图片：', { fileID, type });
    
    // 获取文件格式
    const fileExt = fileID.split('.').pop().toLowerCase();
    
    // 检查文件格式是否支持
    if (!AD_CONFIG.supportedFormats.includes(fileExt)) {
      return {
        code: 404,
        message: `不支持的图片格式: ${fileExt}，支持的格式: ${AD_CONFIG.supportedFormats.join(', ')}`
      };
    }
    
    // 检查是否为支持透明度的格式
    const transparentFormats = ['png', 'webp', 'gif', 'svg'];
    const isTransparent = transparentFormats.includes(fileExt);
    
    // 更新数据库记录
    const adCollection = db.collection(AD_CONFIG.collection);
    const configCount = await adCollection.count();
    
    const updateData = {
      [type]: fileID,
      [`${type}_format`]: fileExt,
      [`${type}_transparent`]: isTransparent,
      updateTime: db.serverDate()
    };
    
    if (configCount.total === 0) {
      // 如果不存在配置，创建新配置
      await adCollection.add({
        data: updateData
      });
    } else {
      // 如果存在配置，更新配置
      await adCollection.where({
        _id: _.exists(true)
      }).limit(1).update({
        data: updateData
      });
    }
    
    // 获取临时访问链接
    const tempUrlResult = await cloud.getTempFileURL({
      fileList: [fileID]
    });
    
    if (!tempUrlResult.fileList || !tempUrlResult.fileList[0] || !tempUrlResult.fileList[0].tempFileURL) {
      throw new Error('获取临时文件URL失败');
    }
    
    const tempUrl = tempUrlResult.fileList[0].tempFileURL;
    
    return {
      code: 200,
      message: '更新广告图片成功',
      data: {
        fileID: fileID,
        url: tempUrl,
        fileType: fileExt,
        isTransparent: isTransparent
      }
    };
  } catch (error) {
    console.error('更新广告图片失败:', error);
    // 提供更详细的错误信息
    let errorMessage = '更新广告图片失败';
    if (error.message) {
      if (error.message.includes('exceed')) {
        errorMessage = '图片文件过大，请压缩后再上传';
      } else if (error.message.includes('format')) {
        errorMessage = '图片格式不支持，请转换为JPG、PNG或WEBP格式';
      } else {
        errorMessage = '更新广告图片失败: ' + error.message;
      }
    }
    return {
      code: 404,
      message: errorMessage
    };
  }
}

/**
 * 初始化广告配置
 */
async function initAdConfig() {
  try {
    // 检查集合是否存在
    const adCollection = db.collection(AD_CONFIG.collection);
    const countResult = await adCollection.count();
    
    if (countResult.total > 0) {
      return {
        code: 0,
        message: '广告配置已存在',
        count: countResult.total
      };
    }
    
    // 创建一个空文档初始化集合
    await db.collection(AD_CONFIG.collection).add({
      data: {
        first: '',
        second: '',
        first_format: '',
        second_format: '',
        first_transparent: false,
        second_transparent: false,
        created: db.serverDate(),
        updated: db.serverDate()
      }
    });
    
    return {
      code: 200,
      message: '初始化广告配置成功',
      data: { created: new Date() }
    };
  } catch (error) {
    console.error('初始化广告配置失败:', error);
    
    // 如果是因为集合不存在导致的错误，尝试创建集合
    try {
      await db.createCollection(AD_CONFIG.collection);
      
      await db.collection(AD_CONFIG.collection).add({
        data: {
          first: '',
          second: '',
          first_format: '',
          second_format: '',
          first_transparent: false,
          second_transparent: false,
          created: db.serverDate(),
          updated: db.serverDate()
        }
      });
      
      return {
        code: 200,
        message: '广告配置集合创建并初始化成功'
      };
    } catch (createError) {
      console.error('创建广告配置集合失败:', createError);
      return {
        code: 404,
        message: '初始化广告配置失败',
        error: createError
      };
    }
  }
} 