/**
 * 全局事件中心
 * 用于跨页面、跨组件的事件通信
 */

const EventCenter = {
  // 事件存储对象
  _events: {},

  // 注册事件监听
  on: function(eventName, callback) {
    if (!this._events[eventName]) {
      this._events[eventName] = [];
    }
    this._events[eventName].push(callback);
    return this; // 支持链式调用
  },

  // 触发事件
  emit: function(eventName, data) {
    const callbacks = this._events[eventName];
    if (callbacks) {
      callbacks.forEach(callback => {
        try {
          callback(data);
        } catch (err) {
          console.error(`执行事件 ${eventName} 回调时出错:`, err);
        }
      });
    }
    return this; // 支持链式调用
  },

  // 移除事件监听
  off: function(eventName, callback) {
    const callbacks = this._events[eventName];
    if (callbacks) {
      if (callback) {
        // 移除特定回调
        const index = callbacks.indexOf(callback);
        if (index !== -1) {
          callbacks.splice(index, 1);
        }
      } else {
        // 移除该事件的所有回调
        delete this._events[eventName];
      }
    }
    return this; // 支持链式调用
  },

  // 只监听一次事件
  once: function(eventName, callback) {
    const onceWrapper = (data) => {
      callback(data);
      this.off(eventName, onceWrapper);
    };
    this.on(eventName, onceWrapper);
    return this; // 支持链式调用
  }
};

// 导出事件中心单例
export default EventCenter; 