// 数据修复云函数 - 修复积分支出记录
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

exports.main = async (event, context) => {
  const { action } = event;
  
  if (action === 'fixPointsExpense') {
    return await fixPointsExpenseRecords();
  }
  
  return { success: false, message: '未知操作' };
};

// 修复积分支出记录
async function fixPointsExpenseRecords() {
  try {
    console.log('开始修复积分支出记录...');
    
    // 获取积分设置
    const settingsResult = await db.collection('system_settings').where({ type: 'points' }).get();
    const pointsSettings = settingsResult.data[0] || {};
    const exchangeRatio = (pointsSettings.settings && pointsSettings.settings.exchangeRatio) || 100;
    
    console.log('当前积分兑换比例:', exchangeRatio);
    
    // 查找所有积分支出记录
    const expenseResult = await db.collection('expenses').where({
      type: 'withdrawal'
    }).get();
    
    const expenseRecords = expenseResult.data;
    console.log('找到积分支出记录数量:', expenseRecords.length);
    
    let fixedCount = 0;
    
    for (const record of expenseRecords) {
      const amount = record.amount;
      
      // 如果金额很大（>1000），可能是积分数量，需要转换
      if (amount > 1000 && !record.points) {
        console.log('发现需要修复的记录:', {
          id: record._id,
          oldAmount: amount,
          newAmount: amount / exchangeRatio
        });
        
        // 更新记录
        await db.collection('expenses').doc(record._id).update({
          data: {
            amount: amount / exchangeRatio, // 转换为人民币
            points: amount, // 保存原始积分数量
            description: record.description + ` (已修复: ${amount}积分 → ¥${(amount / exchangeRatio).toFixed(2)})`
          }
        });
        
        fixedCount++;
      }
    }
    
    console.log('修复完成，共修复记录数:', fixedCount);
    
    return {
      success: true,
      message: `修复完成，共修复 ${fixedCount} 条记录`,
      fixedCount,
      totalRecords: expenseRecords.length,
      exchangeRatio
    };
    
  } catch (error) {
    console.error('修复积分支出记录失败:', error);
    return {
      success: false,
      message: '修复失败: ' + error.message,
      error: error.toString()
    };
  }
}
