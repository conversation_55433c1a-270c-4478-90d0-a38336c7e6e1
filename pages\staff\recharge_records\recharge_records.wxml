<!-- 充值核销记录页面 -->
<scroll-view scroll-y="true" class="recharge-records-scroll" bindscrolltolower="onReachBottom" enhanced="true" show-scrollbar="true">
  <view class="recharge-records-container">
    <!-- 顶部状态栏 -->
    <view class="page-header">
      <view class="back-btn" bindtap="goBack">←</view>
      <view class="header-title">充值核销记录</view>
      <view class="placeholder"></view>
    </view>
    
    <!-- 充值统计卡片 -->
    <view class="recharge-stats-section">
      <view class="stats-title">充值统计</view>
      
      <view wx:if="{{statsLoading}}" class="loading">
        <view class="loading-icon"></view>
        <view class="loading-text">加载中...</view>
      </view>
      
      <view wx:else class="stats-cards">
        <!-- 主要统计数据 -->
        <view class="main-stats">
          <view class="main-stats-card">
            <view class="main-card-title">总充值金额</view>
            <view class="main-card-value">¥{{totalRechargeAmount || '0.00'}}</view>
          </view>
          
          <view class="main-stats-card">
            <view class="main-card-title">今日充值</view>
            <view class="main-card-value">¥{{todayRechargeAmount || '0.00'}}</view>
          </view>
        </view>
        
        <!-- 次要统计数据 -->
        <view class="secondary-stats">
          <view class="stats-card">
            <view class="card-title">昨日充值</view>
            <view class="card-value">¥{{yesterdayRechargeAmount || '0.00'}}</view>
          </view>
          
          <view class="stats-card">
            <view class="card-title">本周充值</view>
            <view class="card-value">¥{{weekRechargeAmount || '0.00'}}</view>
          </view>
          
          <view class="stats-card">
            <view class="card-title">本月充值</view>
            <view class="card-value">¥{{monthRechargeAmount || '0.00'}}</view>
          </view>
        </view>
        
        <!-- 充值记录统计 -->
        <view class="recharge-count-info">
          <text>总充值笔数: {{totalRechargeCount || 0}}</text>
        </view>
      </view>
    </view>
    
    <!-- 日期筛选 -->
    <view class="filter-section">
      <view class="date-filter">
        <view class="date-picker-row">
          <view class="date-picker-item">
            <text class="date-label">开始日期</text>
            <picker mode="date" value="{{startDate}}" start="2020-01-01" end="2030-12-31" bindchange="bindStartDateChange">
              <view class="date-picker-value">{{startDate || '请选择'}}</view>
            </picker>
          </view>
          
          <view class="date-picker-item">
            <text class="date-label">结束日期</text>
            <picker mode="date" value="{{endDate}}" start="2020-01-01" end="2030-12-31" bindchange="bindEndDateChange">
              <view class="date-picker-value">{{endDate || '请选择'}}</view>
            </picker>
          </view>
        </view>
        
        <view class="filter-buttons">
          <button class="filter-btn apply" bindtap="applyDateFilter">应用筛选</button>
          <button class="filter-btn clear" bindtap="clearDateFilter">清除筛选</button>
        </view>
      </view>
    </view>
    
    <!-- 记录总数 -->
    <view class="total-count">共 {{totalCount}} 条记录</view>
    
    <!-- 记录列表 -->
    <view class="records-list">
      <block wx:if="{{records.length > 0}}">
        <view class="record-item" wx:for="{{records}}" wx:key="id">
          <view class="record-header">
            <view class="record-id">订单ID: {{item.orderId}}</view>
            <view class="record-status {{item.status === 'verified' ? 'verified' : ''}}">
              {{item.status === 'verified' ? '已核销' : '未核销'}}
            </view>
          </view>
          
          <view class="record-details">
            <view class="record-info-item">
              <text class="info-label">充值金额:</text>
              <text class="info-value highlight">¥{{item.amount || '0.00'}}</text>
            </view>
            
            <view class="record-info-item">
              <text class="info-label">赠送金额:</text>
              <text class="info-value">¥{{item.bonusAmount || item.bonus || '0.00'}}</text>
            </view>
            
            <view class="record-info-item">
              <text class="info-label">总金额:</text>
              <text class="info-value highlight">¥{{(item.amount + (item.bonusAmount || item.bonus || 0)) || '0.00'}}</text>
            </view>
            
            <view class="record-info-item">
              <text class="info-label">推广佣金:</text>
              <text class="info-value highlight-commission">¥{{item.promoterCommission || '0.00'}}</text>
            </view>
            
            <view class="record-info-item">
              <text class="info-label">用户:</text>
              <text class="info-value">{{item.userName || '未知用户'}}</text>
            </view>
            
            <view class="record-info-item" wx:if="{{item.status === 'verified'}}">
              <text class="info-label">核销时间:</text>
              <text class="info-value">{{item.formattedDate}} {{item.formattedTime}}</text>
            </view>
            
            <view class="record-info-item" wx:if="{{item.status === 'verified'}}">
              <text class="info-label">核销码:</text>
              <text class="info-value">{{item.verifyCode}}</text>
            </view>
          </view>
        </view>
      </block>
      
      <view wx:else class="no-records">
        <text>暂无充值核销记录</text>
      </view>
    </view>
    
    <!-- 加载中提示 -->
    <view class="loading-container" wx:if="{{loading}}">
      <view class="loading-icon"></view>
      <view class="loading-text">加载中...</view>
    </view>
    
    <!-- 底部提示 -->
    <view class="bottom-tip" wx:if="{{!hasMore && records.length > 0}}">
      -- 已经到底了 --
    </view>
  </view>
</scroll-view> 