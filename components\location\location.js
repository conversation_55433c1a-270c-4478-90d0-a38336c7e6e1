// components/location/location.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 地址文本
    address: {
      type: String,
      value: '点击选择位置'
    },
    // 图标路径
    iconPath: {
      type: String,
      value: '/static/定位图标.png'
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 跳转到地图页面
    goToMapPage() {
      wx.navigateTo({
        url: '/pages/map/map'
      });
    }
  }
}); 