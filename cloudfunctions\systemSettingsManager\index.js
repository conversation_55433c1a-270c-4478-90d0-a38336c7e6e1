// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV })

// 云函数入口函数
exports.main = async (event, context) => {
  const { action, data } = event;
  const db = cloud.database();
  const _ = db.command;
  
  try {
    console.log('云函数接收到的action:', action);
    console.log('云函数接收到的data:', data);
    
    switch (action) {
      // 初始化系统设置
      case 'initSettings':
        return await initSettings(db);
      
      // 获取系统设置
      case 'getSettings':
        return await getSettings(db);
      
      // 更新系统设置
      case 'updateSettings':
        return await updateSettings(db, data);
      
      // 获取可用时间段
      case 'getAvailableTimeSlots':
        return await getAvailableTimeSlots(db, data.date);
        
      // 获取基本信息
      case 'getBasicInfo':
        return await getBasicInfo(db);
        
      // 保存基本信息
      case 'saveBasicInfo':
        return await saveBasicInfo(db, event.basicInfo);
        
      // 保存营业时间
      case 'saveBusinessHours':
        return await saveBusinessHours(db, event.businessHours);
        
      // 备份数据
      case 'backupData':
        return await backupData(db);
        
      default:
        return {
          code: -1,
          message: '未知操作'
        };
    }
  } catch (error) {
    console.error('云函数执行出错:', error);
    return {
      code: -1,
      message: '云函数执行出错: ' + (error.message || JSON.stringify(error)),
      error: error
    };
  }
};

// 初始化系统设置
async function initSettings(db) {
  try {
    console.log('开始初始化系统设置');
    
    // 直接尝试创建集合，不检查是否存在
    try {
      await db.createCollection('system_settings');
      console.log('system_settings集合创建成功或已存在');
    } catch (createErr) {
      // 如果集合已存在，会抛出错误，但这不影响后续操作
      console.log('创建集合时出现错误(可能已存在):', createErr);
    }
    
    // 检查是否已有系统设置
    let settingsCheck;
    try {
      settingsCheck = await db.collection('system_settings').doc('system_settings').get();
      console.log('检查系统设置结果:', settingsCheck);
    } catch (checkErr) {
      console.log('检查系统设置失败，可能不存在:', checkErr);
      settingsCheck = { data: null };
    }
    
    // 如果已有系统设置，则返回
    if (settingsCheck.data) {
      console.log('系统设置已存在，返回现有设置');
      return {
        code: 0,
        message: '系统设置已存在',
        data: settingsCheck.data
      };
    }
    
    // 创建默认系统设置
    const defaultSettings = {
      // 营业设置
      business_settings: {
        business_hours: {
          monday: { open: "09:00", close: "21:00", is_open: true },
          tuesday: { open: "09:00", close: "21:00", is_open: true },
          wednesday: { open: "09:00", close: "21:00", is_open: true },
          thursday: { open: "09:00", close: "21:00", is_open: true },
          friday: { open: "09:00", close: "21:00", is_open: true },
          saturday: { open: "09:00", close: "21:00", is_open: true },
          sunday: { open: "09:00", close: "21:00", is_open: true }
        },
        appointment_interval: 30, // 分钟
        advance_booking_days: 30  // 最多可提前预约的天数
      },
      
      // 预约规则
      appointment_rules: {
        allow_multiple_bookings: false, // 不允许多次预约
        auto_confirm: false,            // 手动确认模式
        reminder_before_hours: 1        // 提前1小时提醒
      },
      
      // 核销设置
      verification_settings: {
        verification_methods: ["qrcode", "code"], // 支持扫码和验证码
        allow_early_verification: false,          // 不允许提前核销
        allow_late_verification: false            // 不允许延后核销
      },
      
      // 员工提成设置
      commission_settings: {
        default_commission_rate: 0.3  // 默认提成比例30%
      },
      
      // 通知设置
      notification_settings: {
        new_appointment: true,          // 新预约通知
        appointment_completed: true      // 预约完成通知
      },
      
      // 更新信息
      created_at: new Date(),
      last_updated: new Date(),
      updated_by: 'system'
    };
    
    console.log('准备添加默认系统设置');
    
    // 尝试直接添加文档，不使用特定ID
    try {
      console.log('尝试添加系统设置文档');
      const addResult = await db.collection('system_settings').add({
        data: defaultSettings
      });
      console.log('添加系统设置文档成功:', addResult);
      
      // 获取添加的文档ID
      const docId = addResult._id;
      
      // 更新文档ID为固定值
      try {
        await db.collection('system_settings').doc(docId).update({
          data: {
            _id: 'system_settings'
          }
        });
        console.log('更新文档ID成功');
      } catch (updateIdErr) {
        console.error('更新文档ID失败:', updateIdErr);
        // 继续使用生成的ID
      }
      
      return {
        code: 0,
        message: '系统设置初始化成功',
        data: {
          ...defaultSettings,
          _id: docId
        }
      };
    } catch (addErr) {
      console.error('添加系统设置文档失败:', addErr);
      
      // 尝试另一种方法
      try {
        console.log('尝试使用另一种方法添加系统设置');
        // 直接使用set方法
        await db.collection('system_settings').add({
          data: {
            _id: 'system_settings_' + Date.now(),  // 使用时间戳作为ID
            ...defaultSettings
          }
        });
        console.log('使用另一种方法添加系统设置成功');
        
        return {
          code: 0,
          message: '系统设置初始化成功(备用方法)',
          data: defaultSettings
        };
      } catch (setErr) {
        console.error('使用备用方法添加系统设置也失败:', setErr);
        
        return {
          code: -1,
          message: '初始化系统设置失败: ' + (setErr.message || JSON.stringify(setErr)),
          error: setErr
        };
      }
    }
  } catch (err) {
    console.error('初始化系统设置过程中发生错误:', err);
    return {
      code: -1,
      message: '初始化系统设置失败: ' + (err.message || JSON.stringify(err)),
      error: err
    };
  }
}

// 获取系统设置
async function getSettings(db) {
  try {
    console.log('开始获取系统设置');
    
    // 尝试获取系统设置
    try {
      // 首先尝试获取指定ID的系统设置
      const result = await db.collection('system_settings').doc('system_settings').get()
        .catch(() => null);
      
      if (result && result.data) {
        console.log('获取指定ID的系统设置成功');
        return {
          code: 0,
          message: '获取系统设置成功',
          data: result.data
        };
      }
      
      // 如果没有指定ID的设置，尝试获取任何系统设置
      console.log('尝试获取任何系统设置');
      const anyResult = await db.collection('system_settings').limit(1).get();
      
      if (anyResult && anyResult.data && anyResult.data.length > 0) {
        console.log('获取到系统设置:', anyResult.data[0]);
        return {
          code: 0,
          message: '获取系统设置成功',
          data: anyResult.data[0]
        };
      }
      
      console.log('未找到任何系统设置');
    } catch (getErr) {
      console.error('获取系统设置失败:', getErr);
    }
    
    // 如果获取失败或设置不存在，则初始化
    console.log('系统设置不存在，尝试初始化');
    return await initSettings(db);
  } catch (err) {
    console.error('获取系统设置过程中发生错误:', err);
    return {
      code: -1,
      message: '获取系统设置失败: ' + (err.message || JSON.stringify(err)),
      error: err
    };
  }
}

// 更新系统设置
async function updateSettings(db, data) {
  try {
    console.log('开始更新系统设置');
    
    if (!data) {
      return {
        code: -1,
        message: '更新数据不能为空'
      };
    }
    
    console.log('准备更新的数据:', data);
    
    // 检查数据格式
    if (!data.business_settings || !data.appointment_rules || 
        !data.verification_settings || !data.commission_settings || 
        !data.notification_settings) {
      return {
        code: -1,
        message: '更新数据格式不正确，缺少必要字段'
      };
    }
    
    // 添加更新时间
    const updateData = {
      business_settings: data.business_settings,
      appointment_rules: data.appointment_rules,
      verification_settings: data.verification_settings,
      commission_settings: data.commission_settings,
      notification_settings: data.notification_settings,
      last_updated: new Date(),
      updated_by: data.updated_by || 'admin'
    };
    
    console.log('格式化后的更新数据:', updateData);
    
    // 获取当前系统设置
    const settingsResult = await getSettings(db);
    
    // 如果获取失败，返回错误
    if (settingsResult.code !== 0) {
      console.error('获取系统设置失败，无法更新');
      return settingsResult;
    }
    
    // 获取系统设置ID
    const settingId = settingsResult.data._id;
    console.log('获取到的系统设置ID:', settingId);
    
    // 使用 update 方法更新系统设置
    try {
      console.log('尝试更新系统设置, ID:', settingId);
      await db.collection('system_settings').doc(settingId).update({
        data: updateData
      });
      console.log('更新系统设置成功');
      
      return {
        code: 0,
        message: '更新系统设置成功'
      };
    } catch (updateErr) {
      console.error('更新系统设置失败:', updateErr);
      
      // 尝试使用替代方法
      try {
        console.log('尝试使用替代方法更新系统设置');
        // 创建新的系统设置文档
        const addResult = await db.collection('system_settings').add({
          data: {
            ...updateData,
            created_at: new Date()  // 添加创建时间
          }
        });
        
        console.log('创建新的系统设置文档成功:', addResult);
        
        return {
          code: 0,
          message: '通过创建新文档更新系统设置成功'
        };
      } catch (addErr) {
        console.error('创建新的系统设置文档也失败:', addErr);
        
        return {
          code: -1,
          message: '更新系统设置失败: ' + (addErr.message || JSON.stringify(addErr)),
          error: addErr
        };
      }
    }
  } catch (err) {
    console.error('更新系统设置过程中发生错误:', err);
    return {
      code: -1,
      message: '更新系统设置失败: ' + (err.message || JSON.stringify(err)),
      error: err
    };
  }
}

// 获取可用时间段
async function getAvailableTimeSlots(db, date) {
  try {
    console.log('开始获取可用时间段, 日期:', date);
    
    if (!date) {
      return {
        code: -1,
        message: '日期参数不能为空'
      };
    }
    
    // 获取系统设置
    const settingsResult = await getSettings(db);
    if (settingsResult.code !== 0 || !settingsResult.data) {
      return {
        code: -1,
        message: '获取系统设置失败，无法生成时间段'
      };
    }
    
    const settings = settingsResult.data;
    
    // 获取当天是星期几
    const dayOfWeek = new Date(date).getDay(); // 0是周日，1是周一...
    const dayNames = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
    const dayName = dayNames[dayOfWeek];
    
    console.log('当天是:', dayName);
    
    // 获取当天的营业时间
    const daySettings = settings.business_settings.business_hours[dayName];
    
    // 检查当天是否营业
    if (!daySettings || !daySettings.is_open) {
      return {
        code: 0,
        data: {
          available: false,
          reason: '今日不营业'
        }
      };
    }
    
    // 获取预约时间间隔
    const interval = settings.business_settings.appointment_interval || 30;
    
    // 生成时间段
    const timeSlots = generateTimeSlots(daySettings.open, daySettings.close, interval);
    
    // 查询当天已有的预约
    let appointments = [];
    try {
      const appointmentsResult = await db.collection('appointments')
        .where({
          date: date,
          status: db.command.in(['pending', 'confirmed'])
        })
        .get();
      appointments = appointmentsResult.data || [];
    } catch (appErr) {
      console.error('获取预约数据失败:', appErr);
      // 即使获取预约失败，也继续生成时间段
    }
    
    // 标记已被预约的时间段
    const bookedTimeSlots = appointments.map(item => item.time);
    
    // 查询当天休息的员工
    let staffRest = [];
    try {
      const staffRestResult = await db.collection('staff')
        .where({
          $or: [
            { rest_days: dayName },
            { rest_dates: date },
            { 
              'temp_rest.is_resting': true, 
              'temp_rest.start_date': db.command.lte(date), 
              'temp_rest.end_date': db.command.gte(date) 
            }
          ]
        })
        .get();
      staffRest = staffRestResult.data || [];
    } catch (staffErr) {
      console.error('获取员工休息数据失败:', staffErr);
      // 即使获取员工休息数据失败，也继续生成时间段
    }
    
    // 返回可用时间段和员工休息情况
    return {
      code: 0,
      data: {
        available: true,
        timeSlots: timeSlots.map(time => ({
          time,
          available: !bookedTimeSlots.includes(time)
        })),
        restStaff: staffRest.map(staff => ({
          staff_id: staff._id,
          staff_name: staff.name
        }))
      }
    };
  } catch (err) {
    console.error('获取可用时间段失败:', err);
    return {
      code: -1,
      message: '获取可用时间段失败: ' + (err.message || JSON.stringify(err)),
      error: err
    };
  }
}

// 生成时间段
function generateTimeSlots(start, end, intervalMinutes) {
  try {
    console.log('生成时间段, 开始时间:', start, '结束时间:', end, '间隔:', intervalMinutes);
    
    const timeSlots = [];
    let current = new Date(`2000-01-01T${start}:00`);
    const endTime = new Date(`2000-01-01T${end}:00`);
    
    while (current < endTime) {
      const hours = current.getHours().toString().padStart(2, '0');
      const minutes = current.getMinutes().toString().padStart(2, '0');
      timeSlots.push(`${hours}:${minutes}`);
      
      current = new Date(current.getTime() + intervalMinutes * 60000);
    }
    
    console.log('生成的时间段:', timeSlots);
    return timeSlots;
  } catch (err) {
    console.error('生成时间段失败:', err);
    return [];
  }
}

// 获取基本信息
async function getBasicInfo(db) {
  try {
    console.log('开始获取基本信息');
    
    // 尝试获取基本信息
    try {
      const result = await db.collection('shop_info').doc('basic_info').get()
        .catch(() => null);
      
      if (result && result.data) {
        console.log('获取基本信息成功');
        return {
          code: 0,
          message: '获取基本信息成功',
          data: result.data
        };
      }
      
      // 如果没有指定ID的基本信息，尝试获取任何基本信息
      console.log('尝试获取任何基本信息');
      const anyResult = await db.collection('shop_info').limit(1).get();
      
      if (anyResult && anyResult.data && anyResult.data.length > 0) {
        console.log('获取到基本信息:', anyResult.data[0]);
        return {
          code: 0,
          message: '获取基本信息成功',
          data: anyResult.data[0]
        };
      }
      
      // 如果没有基本信息，创建默认基本信息
      console.log('未找到任何基本信息，创建默认基本信息');
      const defaultInfo = {
        shopName: '默认店铺名称',
        contactPhone: '',
        address: '',
        created_at: new Date(),
        last_updated: new Date()
      };
      
      // 创建基本信息
      try {
        await db.collection('shop_info').add({
          data: {
            _id: 'basic_info',
            ...defaultInfo
          }
        });
        
        console.log('创建默认基本信息成功');
        return {
          code: 0,
          message: '创建默认基本信息成功',
          data: defaultInfo
        };
      } catch (addErr) {
        console.error('创建默认基本信息失败:', addErr);
        
        // 尝试另一种方法添加
        try {
          const addResult = await db.collection('shop_info').add({
            data: defaultInfo
          });
          
          console.log('使用另一种方法创建默认基本信息成功:', addResult);
          return {
            code: 0,
            message: '创建默认基本信息成功(备用方法)',
            data: {
              ...defaultInfo,
              _id: addResult._id
            }
          };
        } catch (addErr2) {
          console.error('使用备用方法创建默认基本信息也失败:', addErr2);
          return {
            code: -1,
            message: '创建默认基本信息失败',
            error: addErr2
          };
        }
      }
    } catch (getErr) {
      console.error('获取基本信息失败:', getErr);
      return {
        code: -1,
        message: '获取基本信息失败: ' + (getErr.message || JSON.stringify(getErr)),
        error: getErr
      };
    }
  } catch (err) {
    console.error('获取基本信息过程中发生错误:', err);
    return {
      code: -1,
      message: '获取基本信息失败: ' + (err.message || JSON.stringify(err)),
      error: err
    };
  }
}

// 保存基本信息
async function saveBasicInfo(db, basicInfo) {
  try {
    console.log('开始保存基本信息:', basicInfo);
    
    if (!basicInfo) {
      return {
        code: -1,
        message: '基本信息不能为空'
      };
    }
    
    if (!basicInfo.shopName) {
      return {
        code: -1,
        message: '店铺名称不能为空'
      };
    }
    
    // 获取当前基本信息
    const infoResult = await getBasicInfo(db);
    
    // 如果获取失败，返回错误
    if (infoResult.code !== 0) {
      console.error('获取基本信息失败，无法更新');
      return infoResult;
    }
    
    // 获取基本信息ID
    const infoId = infoResult.data._id;
    console.log('获取到的基本信息ID:', infoId);
    
    // 更新数据
    const updateData = {
      shopName: basicInfo.shopName,
      contactPhone: basicInfo.contactPhone || '',
      address: basicInfo.address || '',
      last_updated: new Date()
    };
    
    // 使用 update 方法更新基本信息
    try {
      console.log('尝试更新基本信息, ID:', infoId);
      await db.collection('shop_info').doc(infoId).update({
        data: updateData
      });
      console.log('更新基本信息成功');
      
      return {
        code: 0,
        message: '更新基本信息成功'
      };
    } catch (updateErr) {
      console.error('更新基本信息失败:', updateErr);
      
      // 尝试使用替代方法
      try {
        console.log('尝试使用替代方法更新基本信息');
        // 创建新的基本信息文档
        const addResult = await db.collection('shop_info').add({
          data: {
            ...updateData,
            created_at: new Date()  // 添加创建时间
          }
        });
        
        console.log('创建新的基本信息文档成功:', addResult);
        
        return {
          code: 0,
          message: '通过创建新文档更新基本信息成功'
        };
      } catch (addErr) {
        console.error('创建新的基本信息文档也失败:', addErr);
        
        return {
          code: -1,
          message: '更新基本信息失败: ' + (addErr.message || JSON.stringify(addErr)),
          error: addErr
        };
      }
    }
  } catch (err) {
    console.error('保存基本信息过程中发生错误:', err);
    return {
      code: -1,
      message: '保存基本信息失败: ' + (err.message || JSON.stringify(err)),
      error: err
    };
  }
}

// 保存营业时间
async function saveBusinessHours(db, businessHours) {
  try {
    console.log('开始保存营业时间:', businessHours);
    
    if (!businessHours) {
      return {
        code: -1,
        message: '营业时间不能为空'
      };
    }
    
    // 验证营业时间格式
    if (businessHours.is_open && businessHours.open >= businessHours.close) {
      return {
        code: -1,
        message: '结束时间必须晚于开始时间'
      };
    }
    
    // 获取当前系统设置
    const settingsResult = await getSettings(db);
    
    // 如果获取失败，返回错误
    if (settingsResult.code !== 0) {
      console.error('获取系统设置失败，无法更新营业时间');
      return settingsResult;
    }
    
    // 获取系统设置ID
    const settingId = settingsResult.data._id;
    console.log('获取到的系统设置ID:', settingId);
    
    // 更新所有日期的营业时间
    const days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
    const updateData = {};
    
    days.forEach(day => {
      updateData[`business_settings.business_hours.${day}.is_open`] = businessHours.is_open;
      updateData[`business_settings.business_hours.${day}.open`] = businessHours.open;
      updateData[`business_settings.business_hours.${day}.close`] = businessHours.close;
    });
    
    // 添加更新时间
    updateData.last_updated = new Date();
    
    // 使用 update 方法更新系统设置
    try {
      console.log('尝试更新营业时间, ID:', settingId);
      await db.collection('system_settings').doc(settingId).update({
        data: updateData
      });
      console.log('更新营业时间成功');
      
      return {
        code: 0,
        message: '更新营业时间成功'
      };
    } catch (updateErr) {
      console.error('更新营业时间失败:', updateErr);
      return {
        code: -1,
        message: '更新营业时间失败: ' + (updateErr.message || JSON.stringify(updateErr)),
        error: updateErr
      };
    }
  } catch (err) {
    console.error('保存营业时间过程中发生错误:', err);
    return {
      code: -1,
      message: '保存营业时间失败: ' + (err.message || JSON.stringify(err)),
      error: err
    };
  }
}

// 备份数据
async function backupData(db) {
  try {
    console.log('开始备份数据');
    
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupId = `backup_${timestamp}`;
    
    // 获取需要备份的集合列表
    const collections = [
      'system_settings',
      'shop_info',
      'admin_users',
      'appointments',
      'staff',
      'customers',
      'categories',
      'videos'
    ];
    
    const backupData = {};
    
    // 遍历集合，获取数据
    for (const collection of collections) {
      try {
        console.log(`开始备份集合: ${collection}`);
        const result = await db.collection(collection).limit(1000).get();
        backupData[collection] = result.data || [];
        console.log(`集合 ${collection} 备份完成, 共 ${backupData[collection].length} 条数据`);
      } catch (collErr) {
        console.error(`备份集合 ${collection} 失败:`, collErr);
        backupData[collection] = [];
      }
    }
    
    // 创建备份记录
    try {
      console.log('创建备份记录');
      await db.collection('backups').add({
        data: {
          _id: backupId,
          timestamp: new Date(),
          data: backupData,
          created_by: 'admin'
        }
      });
      
      console.log('备份数据成功');
      return {
        code: 0,
        message: '备份数据成功',
        backupId: backupId
      };
    } catch (addErr) {
      console.error('创建备份记录失败:', addErr);
      
      // 如果数据太大，尝试分开备份
      try {
        console.log('尝试分开备份');
        const backupMeta = {
          _id: backupId,
          timestamp: new Date(),
          collections: collections,
          created_by: 'admin'
        };
        
        await db.collection('backups').add({
          data: backupMeta
        });
        
        // 为每个集合创建单独的备份
        for (const collection of collections) {
          if (backupData[collection] && backupData[collection].length > 0) {
            await db.collection('backup_data').add({
              data: {
                backupId: backupId,
                collection: collection,
                data: backupData[collection],
                timestamp: new Date()
              }
            });
          }
        }
        
        console.log('分开备份成功');
        return {
          code: 0,
          message: '备份数据成功(分开备份)',
          backupId: backupId
        };
      } catch (splitErr) {
        console.error('分开备份也失败:', splitErr);
        return {
          code: -1,
          message: '备份数据失败: ' + (splitErr.message || JSON.stringify(splitErr)),
          error: splitErr
        };
      }
    }
  } catch (err) {
    console.error('备份数据过程中发生错误:', err);
    return {
      code: -1,
      message: '备份数据失败: ' + (err.message || JSON.stringify(err)),
      error: err
    };
  }
}