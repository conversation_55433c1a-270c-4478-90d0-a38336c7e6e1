/**
 * 主控制器模块单元测试
 * 测试模块初始化、协调、API兼容性和错误处理
 */

const MainController = require('../modules/main-controller');
const { createMockPageContext, waitFor, createTestError } = require('./test-utils');

// Mock 所有依赖模块
jest.mock('../modules/data-manager');
jest.mock('../modules/video-list');
jest.mock('../modules/video-player');
jest.mock('../modules/search');
jest.mock('../modules/share');
jest.mock('../modules/navigation');
jest.mock('../modules/ui-state');
jest.mock('../modules/scroll-performance');

describe('MainController 主控制器', () => {
  let mainController;
  let mockPageContext;

  beforeEach(() => {
    // 重置所有 mock
    jest.clearAllMocks();
    
    // 创建模拟页面上下文
    mockPageContext = createMockPageContext();
    
    // 创建主控制器实例
    mainController = new MainController(mockPageContext);
  });

  afterEach(() => {
    // 清理资源
    if (mainController) {
      mainController.destroy();
    }
  });

  describe('模块初始化', () => {
    test('应该成功初始化主控制器', async () => {
      // 执行初始化
      await mainController.init();
      
      // 验证初始化状态
      expect(mainController.initialized).toBe(true);
      expect(mainController.pageState).toBe('loaded');
      expect(mainController.modules.size).toBeGreaterThan(0);
    });

    test('应该按正确顺序注册所有模块', () => {
      mainController.registerModules();
      
      // 验证模块注册
      expect(mainController.modules.has('UIState')).toBe(true);
      expect(mainController.modules.has('DataManager')).toBe(true);
      expect(mainController.modules.has('VideoList')).toBe(true);
      expect(mainController.modules.has('VideoPlayer')).toBe(true);
      expect(mainController.modules.has('Search')).toBe(true);
      expect(mainController.modules.has('Share')).toBe(true);
      expect(mainController.modules.has('Navigation')).toBe(true);
      expect(mainController.modules.has('ScrollPerformance')).toBe(true);
    });

    test('应该正确设置模块依赖关系', () => {
      mainController.setupModuleDependencies();
      
      // 验证依赖关系
      expect(mainController.moduleDependencies.get('VideoList')).toContain('DataManager');
      expect(mainController.moduleDependencies.get('VideoPlayer')).toContain('VideoList');
      expect(mainController.moduleDependencies.get('Search')).toContain('VideoList');
    });

    test('应该按依赖顺序获取模块初始化顺序', () => {
      mainController.registerModules();
      mainController.setupModuleDependencies();
      
      const initOrder = mainController.getModuleInitOrder();
      
      // 验证初始化顺序
      expect(initOrder).toBeInstanceOf(Array);
      expect(initOrder.length).toBe(mainController.modules.size);
      
      // DataManager 应该在 VideoList 之前
      const dataManagerIndex = initOrder.indexOf('DataManager');
      const videoListIndex = initOrder.indexOf('VideoList');
      expect(dataManagerIndex).toBeLessThan(videoListIndex);
    });
  });

  describe('模块管理', () => {
    beforeEach(async () => {
      await mainController.init();
    });

    test('应该能够获取模块实例', () => {
      const videoListModule = mainController.getModule('VideoList');
      expect(videoListModule).toBeDefined();
    });

    test('应该返回null当模块不存在时', () => {
      const nonExistentModule = mainController.getModule('NonExistent');
      expect(nonExistentModule).toBeNull();
    });

    test('应该正确获取所有模块状态', () => {
      const status = mainController.getModulesStatus();
      
      expect(status).toHaveProperty('totalModules');
      expect(status).toHaveProperty('initializedModules');
      expect(status).toHaveProperty('failedModules');
      expect(status).toHaveProperty('modules');
      expect(status.totalModules).toBeGreaterThan(0);
    });
  });

  describe('错误处理', () => {
    beforeEach(async () => {
      await mainController.init();
    });

    test('应该正确处理模块错误', async () => {
      const testError = createTestError('模块测试错误', 'MODULE_ERROR');
      
      // 模拟模块错误
      mainController.handleModuleError('VideoList', testError);
      
      // 验证错误被记录到失败模块列表
      expect(mainController.failedModules.has('VideoList')).toBe(true);
    });

    test('应该尝试恢复失败的模块', async () => {
      const testError = createTestError('模块恢复测试', 'MODULE_ERROR');
      
      // 模拟模块恢复
      const recoveryPromise = mainController.attemptModuleRecovery('VideoList', testError);
      
      // 等待恢复完成
      await waitFor(1100); // 等待超过恢复延迟时间
      
      // 验证恢复尝试
      expect(mainController.moduleStates.get('VideoList')).toBeDefined();
    });

    test('应该处理高频错误', () => {
      const highFrequencyData = {
        pattern: 'NETWORK_ERROR_VideoList',
        data: { count: 10, frequency: 6 }
      };
      
      // 处理高频错误
      mainController.handleHighFrequencyError(highFrequencyData);
      
      // 验证处理逻辑被调用
      // 这里可以验证相应的处理方法被调用
    });
  });

  describe('健康检查', () => {
    beforeEach(async () => {
      await mainController.init();
    });

    test('应该执行健康检查', () => {
      // 执行健康检查
      mainController.performHealthCheck();
      
      // 验证健康检查逻辑
      // 由于健康检查是异步的，这里主要验证方法不抛出错误
      expect(mainController.modules.size).toBeGreaterThan(0);
    });

    test('应该处理不健康的模块', () => {
      const unhealthyModules = [
        { name: 'VideoList', reason: 'not_responding' },
        { name: 'Search', reason: 'error_state' }
      ];
      
      // 处理不健康模块
      mainController.handleUnhealthyModules(unhealthyModules);
      
      // 验证处理逻辑
      expect(mainController.failedModules.size).toBeGreaterThanOrEqual(0);
    });
  });

  describe('API兼容性', () => {
    beforeEach(async () => {
      await mainController.init();
    });

    test('应该初始化API兼容层', () => {
      expect(mainController.apiCompatibility).toBeDefined();
    });

    test('应该提供兼容性报告', () => {
      if (mainController.apiCompatibility) {
        const report = mainController.apiCompatibility.getCompatibilityReport();
        expect(report).toHaveProperty('totalMethods');
        expect(report).toHaveProperty('mappedMethods');
      }
    });
  });

  describe('统一错误处理', () => {
    beforeEach(async () => {
      await mainController.init();
    });

    test('应该初始化统一错误处理器', () => {
      expect(mainController.unifiedErrorHandler).toBeDefined();
    });

    test('应该获取错误统计信息', () => {
      const errorStats = mainController.getErrorStats();
      expect(errorStats).toBeDefined();
    });

    test('应该处理错误分析结果', () => {
      const analysis = {
        totalErrors: 10,
        recoveryRate: '60%',
        recommendations: [
          { type: 'network_optimization', priority: 'high' }
        ]
      };
      
      // 处理错误分析
      mainController.handleErrorAnalysis(analysis);
      
      // 验证处理逻辑不抛出错误
      expect(mainController.unifiedErrorHandler).toBeDefined();
    });
  });

  describe('系统优化', () => {
    beforeEach(async () => {
      await mainController.init();
    });

    test('应该应用优化建议', () => {
      const recommendations = [
        { type: 'network_optimization', priority: 'high' },
        { type: 'module_stability', priority: 'medium' }
      ];
      
      // 应用优化建议
      mainController.applyOptimizationRecommendations(recommendations);
      
      // 验证优化逻辑不抛出错误
      expect(mainController.initialized).toBe(true);
    });

    test('应该触发系统优化', () => {
      // 触发系统优化
      mainController.triggerSystemOptimization();
      
      // 验证优化逻辑不抛出错误
      expect(mainController.initialized).toBe(true);
    });

    test('应该切换到离线模式', () => {
      // 切换到离线模式
      mainController.switchToOfflineMode();
      
      // 验证切换逻辑不抛出错误
      expect(mainController.initialized).toBe(true);
    });
  });

  describe('模块通信', () => {
    beforeEach(async () => {
      await mainController.init();
    });

    test('应该设置模块间事件转发', () => {
      // 设置事件转发
      mainController.setupModuleEventForwarding();
      
      // 验证通信器存在
      expect(mainController.communicator).toBeDefined();
    });

    test('应该启动模块协调', () => {
      // 启动模块协调
      mainController.startModuleCoordination();
      
      // 验证协调逻辑不抛出错误
      expect(mainController.initialized).toBe(true);
    });
  });

  describe('生命周期管理', () => {
    test('应该正确处理所有模块初始化完成', () => {
      // 模拟所有模块初始化完成
      mainController.modules.forEach((moduleInfo) => {
        moduleInfo.initialized = true;
      });
      
      // 检查所有模块初始化状态
      mainController.checkAllModulesInitialized();
      
      // 验证页面状态
      expect(mainController.pageState).toBe('loaded');
    });

    test('应该正确销毁主控制器', () => {
      // 初始化后销毁
      mainController.init().then(() => {
        mainController.destroy();
        
        // 验证清理状态
        expect(mainController.modules.size).toBe(0);
        expect(mainController.moduleStates.size).toBe(0);
      });
    });
  });

  describe('错误边界情况', () => {
    test('应该处理初始化失败', async () => {
      // 模拟初始化错误
      const originalRegisterModules = mainController.registerModules;
      mainController.registerModules = jest.fn(() => {
        throw new Error('注册模块失败');
      });
      
      // 尝试初始化
      await expect(mainController.init()).rejects.toThrow();
      
      // 恢复原方法
      mainController.registerModules = originalRegisterModules;
    });

    test('应该处理模块获取错误', () => {
      // 模拟获取不存在的模块
      const result = mainController.getModule('NonExistentModule');
      expect(result).toBeNull();
    });

    test('应该处理循环依赖', () => {
      // 设置循环依赖
      mainController.moduleDependencies.set('ModuleA', ['ModuleB']);
      mainController.moduleDependencies.set('ModuleB', ['ModuleA']);
      
      // 尝试获取初始化顺序
      expect(() => {
        mainController.getModuleInitOrder();
      }).not.toThrow(); // 应该有错误处理机制
    });
  });

  describe('性能测试', () => {
    test('应该在合理时间内完成初始化', async () => {
      const startTime = Date.now();
      
      await mainController.init();
      
      const endTime = Date.now();
      const initTime = endTime - startTime;
      
      // 初始化应该在2秒内完成
      expect(initTime).toBeLessThan(2000);
    });

    test('应该高效处理大量模块', () => {
      // 这个测试验证模块管理的效率
      const startTime = Date.now();
      
      // 执行多次模块状态检查
      for (let i = 0; i < 1000; i++) {
        mainController.getModulesStatus();
      }
      
      const endTime = Date.now();
      const executionTime = endTime - startTime;
      
      // 1000次状态检查应该在100ms内完成
      expect(executionTime).toBeLessThan(100);
    });
  });
});