<!--pages/admin/business/business.wxml-->
<view class="business-container">
  <!-- 顶部标题栏 -->
  <view class="header">
    <view class="back-icon" bindtap="goBack">
      <text>←</text>
    </view>
    <view class="title">经营管理</view>
  </view>
  
  <!-- 切换视图按钮 -->
  <view class="view-tabs">
    <view class="tab-item {{currentView === 'store' ? 'active' : ''}}" bindtap="switchView" data-view="store">
      店铺业绩
    </view>
    <view class="tab-item {{currentView === 'staff' ? 'active' : ''}}" bindtap="switchView" data-view="staff">
      员工业绩
    </view>
    <view class="tab-item {{currentView === 'expense' ? 'active' : ''}}" bindtap="switchView" data-view="expense">
      支出管理
    </view>
  </view>
  
  <!-- 日期选择区域 -->
  <view class="date-filter">
    <view class="date-shortcuts">
      <view class="shortcut-btn {{startDate === today && endDate === today ? 'active' : ''}}" bindtap="setDateRange" data-range="today">今日</view>
      <view class="shortcut-btn {{startDate === yesterday && endDate === yesterday ? 'active' : ''}}" bindtap="setDateRange" data-range="yesterday">昨日</view>
      <view class="shortcut-btn {{startDate === currentWeekStart && endDate === currentWeekEnd ? 'active' : ''}}" bindtap="setDateRange" data-range="week">本周</view>
      <view class="shortcut-btn {{startDate === currentMonthStart && endDate === currentMonthEnd ? 'active' : ''}}" bindtap="setDateRange" data-range="month">本月</view>
    </view>
    
    <view class="date-range-picker">
      <view class="date-picker-item">
        <text class="date-label">开始日期：</text>
        <picker mode="date" bindchange="onStartDateChange" value="{{startDate}}">
          <view class="date-value">{{startDate || '请选择'}}</view>
        </picker>
      </view>
      
      <view class="date-picker-item">
        <text class="date-label">结束日期：</text>
        <picker mode="date" bindchange="onEndDateChange" value="{{endDate}}">
          <view class="date-value">{{endDate || '请选择'}}</view>
        </picker>
      </view>
      
      <view class="query-btn" bindtap="queryData">查询</view>
    </view>
    
    <!-- 员工筛选（仅在店铺视图中显示） -->
    <view class="staff-filter" wx:if="{{currentView === 'store'}}">
      <view class="filter-label">员工筛选：</view>
      <picker mode="selector" range="{{staffList}}" range-key="name" bindchange="onStaffChange">
        <view class="filter-picker">
          <text>{{selectedStaffName}}</text>
          <text class="picker-arrow">▼</text>
        </view>
      </picker>
      <view class="clear-filter" wx:if="{{selectedStaff}}" bindtap="clearStaffFilter">
        <text>×</text>
      </view>
    </view>
  </view>
  
  <!-- 加载中 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>
  
  <!-- 店铺业绩视图 -->
  <view class="business-content" wx:if="{{currentView === 'store' && !loading}}">
    <!-- 业绩概览卡片 -->
    <view class="stats-card">
      <view class="stats-card-title overview-title">业绩概览</view>
      <view class="stats-basic">
        <view class="stats-basic-item profit-item {{businessData.totalProfit < 0 ? 'negative' : ''}}">
          <text class="stats-basic-value">¥{{businessData.totalProfit || 0}}</text>
          <text class="stats-basic-label">净利润</text>
        </view>
        
        <view class="stats-basic-item orders-item">
          <text class="stats-basic-value">{{businessData.totalOrders || 0}}</text>
          <text class="stats-basic-label">总订单数</text>
        </view>
        <view class="stats-basic-item income-item">
          <text class="stats-basic-value">¥{{businessData.totalCashIncome || 0}}</text>
          <text class="stats-basic-label">现金收入</text>
        </view>
        <view class="stats-basic-item recharge-income-item">
          <text class="stats-basic-value">¥{{businessData.rechargeIncome || 0}}</text>
          <text class="stats-basic-label">充值收入</text>
        </view>
        <view class="stats-basic-item balance-income-item">
          <text class="stats-basic-value">¥{{businessData.totalBalanceIncome || 0}}</text>
          <text class="stats-basic-label">余额消费(非收入)</text>
        </view>
        <view class="stats-basic-item commission-item">
          <text class="stats-basic-value">¥{{businessData.totalCommission || 0}}</text>
          <text class="stats-basic-label">总提成</text>
        </view>
        <view class="stats-basic-item expense-item">
          <text class="stats-basic-value">¥{{businessData.totalExpense || 0}}</text>
          <text class="stats-basic-label">总支出</text>
        </view>
        <view class="stats-basic-item points-expense-item">
          <text class="stats-basic-value">¥{{businessData.totalPointsExpense || 0}}</text>
          <text class="stats-basic-label">积分支出</text>
        </view>
        <view class="stats-basic-item refund-expense-item">
          <text class="stats-basic-value">¥{{businessData.totalRefundExpense || 0}}</text>
          <text class="stats-basic-label">退款支出</text>
        </view>
        <view class="stats-basic-item promotion-commission-item">
          <text class="stats-basic-value">¥{{businessData.totalPromotionCommission || 0}}</text>
          <text class="stats-basic-label">推广佣金</text>
        </view>
      </view>
    </view>
    
    <!-- 员工业绩卡片 -->
    <view class="stats-card" wx:if="{{businessData.staffStats && businessData.staffStats.length > 0}}">
      <view class="stats-card-title">员工业绩</view>
      <view class="staff-stats">
        <view class="staff-stats-header">
          <text class="staff-name-header">员工</text>
          <text class="staff-orders-header">订单数</text>
          <text class="staff-income-header">现金收入</text>
          <text class="staff-balance-header">余额消费</text>
          <text class="staff-commission-header">提成</text>
          <text class="staff-action-header">操作</text>
        </view>
        
        <view class="staff-stats-item" wx:for="{{businessData.staffStats}}" wx:key="staffId">
          <text class="staff-name">{{item.staffName}}</text>
          <text class="staff-orders">{{item.orderCount}}</text>
          <text class="staff-income">¥{{item.cashIncome}}</text>
          <text class="staff-balance">¥{{item.balanceIncome}}</text>
          <text class="staff-commission">¥{{item.totalCommission}}</text>
          <view class="staff-action">
            <view class="view-detail-btn" bindtap="viewStaffPerformance" data-id="{{item.staffId}}" data-name="{{item.staffName}}">详情</view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 日期业绩卡片 -->
    <view class="stats-card" wx:if="{{businessData.dateStats && businessData.dateStats.length > 0}}">
      <view class="stats-card-title">日期业绩</view>
      <view class="date-stats">
        <view class="date-stats-header">
          <text class="date-header">日期</text>
          <text class="date-orders-header">订单</text>
          <text class="date-income-header">现金收入</text>
          <text class="date-balance-header">余额消费</text>
          <text class="date-recharge-header">充值</text>
          <text class="date-commission-header">提成</text>
          <text class="date-expense-header">支出</text>
          <text class="date-refund-header">退款支出</text>
          <text class="date-profit-header">利润</text>
        </view>
        
        <view class="date-stats-item" wx:for="{{businessData.dateStats}}" wx:key="date">
          <text class="date-value">{{item.shortDate}}</text>
          <text class="date-orders">{{item.orderCount}}</text>
          <text class="date-income">¥{{item.cashIncome}}</text>
          <text class="date-balance">¥{{item.balanceIncome}}</text>
          <text class="date-recharge">¥{{item.rechargeIncome || 0}}</text>
          <text class="date-commission">¥{{item.totalCommission}}</text>
          <text class="date-expense expense-color">¥{{item.expense}}</text>
          <text class="date-refund expense-color">¥{{item.refundExpense || 0}}</text>
          <text class="date-profit {{item.profit >= 0 ? 'positive' : 'negative'}}">¥{{item.profit}}</text>
        </view>
      </view>
    </view>
    
    <!-- 服务项目业绩卡片 -->
    <view class="stats-card" wx:if="{{businessData.serviceStats && businessData.serviceStats.length > 0}}">
      <view class="stats-card-title">服务项目业绩</view>
      <view class="service-stats">
        <view class="service-stats-header">
          <text class="service-name-header">服务项目</text>
          <text class="service-count-header">次数</text>
          <text class="service-cash-header">现金收入</text>
          <text class="service-balance-header">余额消费</text>
          <text class="service-total-header">总额</text>
        </view>
        
        <view class="service-stats-item" wx:for="{{businessData.serviceStats}}" wx:key="serviceId">
          <text class="service-name">{{item.serviceName}}</text>
          <text class="service-count">{{item.count}}</text>
          <text class="service-cash">¥{{item.cashIncome}}</text>
          <text class="service-balance">¥{{item.balanceIncome}}</text>
          <text class="service-total">¥{{item.totalIncome}}</text>
        </view>
      </view>
    </view>
  </view>
  
  <!-- 员工业绩视图 -->
  <view class="business-content" wx:if="{{currentView === 'staff' && !loading}}">
    <!-- 员工列表 -->
    <view class="staff-list">
      <view class="staff-list-header">
        <text class="staff-name-header">员工姓名</text>
        <text class="staff-phone-header">联系电话</text>
        <text class="staff-commission-header">提成比例</text>
        <text class="staff-action-header">操作</text>
      </view>
      
      <view class="staff-list-item" wx:for="{{staffList}}" wx:key="_id" wx:if="{{item._id}}">
        <text class="staff-name">{{item.name}}</text>
        <text class="staff-phone">{{item.phoneNumber || '无'}}</text>
        <text class="staff-commission-rate">{{item.commissionRate ? (item.commissionRate * 100) + '%' : '30%'}}</text>
        <view class="staff-action">
          <view class="view-detail-btn" bindtap="viewStaffPerformance" data-id="{{item._id}}" data-name="{{item.name}}">查看业绩</view>
        </view>
      </view>
      
      <!-- 空状态 -->
      <view class="empty-staff" wx:if="{{staffList.length <= 1}}">
        <text>暂无员工数据</text>
      </view>
    </view>
  </view>
  
  <!-- 支出管理视图 -->
  <view class="business-content currentView-expense" wx:if="{{currentView === 'expense' && !loading}}">
    <!-- 支出概览卡片 -->
    <view class="stats-card">
      <view class="stats-card-title overview-title">支出概览</view>
      <view class="stats-basic">
        <view class="stats-basic-item expense-item">
          <text class="stats-basic-value">¥{{expenseData.totalExpense || 0}}</text>
          <text class="stats-basic-label">总支出金额</text>
        </view>
        <view class="stats-basic-item records-item">
          <text class="stats-basic-value">{{expenseData.expenseList.length || 0}}</text>
          <text class="stats-basic-label">总记录数</text>
        </view>
      </view>
    </view>
    
    <!-- 积分提现统计卡片 -->
    <view class="stats-card" wx:if="{{expenseData.withdrawalStats}}">
      <view class="stats-card-title">积分提现统计</view>
      <view class="stats-basic">
        <view class="stats-basic-item withdrawal-item">
          <text class="stats-basic-value">¥{{expenseData.withdrawalStats.totalAmount || 0}}</text>
          <text class="stats-basic-label">提现总金额</text>
        </view>
        <view class="stats-basic-item records-item">
          <text class="stats-basic-value">{{expenseData.withdrawalStats.count || 0}}</text>
          <text class="stats-basic-label">提现记录数</text>
        </view>
      </view>
    </view>
    
    <!-- 员工支出卡片 -->
    <view class="stats-card" wx:if="{{expenseData.staffStats && expenseData.staffStats.length > 0}}">
      <view class="stats-card-title">员工支出统计</view>
      <view class="staff-stats">
        <view class="staff-stats-header">
          <text class="staff-name-header">员工</text>
          <text class="staff-orders-header">记录数</text>
          <text class="staff-income-header expense-color">支出金额</text>
        </view>
        
        <view class="staff-stats-item" wx:for="{{expenseData.staffStats}}" wx:key="staffId">
          <text class="staff-name">{{item.staffName}}</text>
          <text class="staff-orders">{{item.count}}</text>
          <text class="staff-income expense-color">¥{{item.totalAmount}}</text>
        </view>
      </view>
    </view>
    
    <!-- 支出记录列表 -->
    <view class="stats-card">
      <view class="stats-card-title">支出记录明细</view>
      <view class="expense-list">
        <view class="expense-list-header">
          <text class="expense-date-header">时间</text>
          <text class="expense-staff-header">员工</text>
          <text class="expense-amount-header expense-color">金额</text>
          <text class="expense-remark-header">备注</text>
          <text class="expense-action-header">操作</text>
        </view>
        
        <view class="expense-list-item" wx:for="{{expenseList}}" wx:key="_id">
          <text class="expense-date">{{item.formattedDate}}</text>
          <text class="expense-staff">{{item.staffName}}</text>
          <text class="expense-amount expense-color">¥{{item.formattedAmount}}</text>
          <text class="expense-remark">{{item.remark || '无'}}</text>
          <view class="expense-action">
            <view class="view-detail-btn" bindtap="viewExpenseDetail" data-id="{{item._id}}">详情</view>
          </view>
        </view>
        
        <!-- 空状态 -->
        <view class="empty-expense" wx:if="{{expenseList.length === 0}}">
          <text>暂无支出记录</text>
        </view>
      </view>
    </view>
  </view>
  
  <!-- 员工业绩详情弹窗 -->
  <view class="staff-performance-panel {{showStaffPerformance ? 'show' : ''}}" wx:if="{{showStaffPerformance}}">
    <view class="panel-mask" bindtap="closeStaffPerformance"></view>
    <view class="panel-content">
      <view class="panel-header">
        <text class="panel-title">员工业绩详情 - {{staffPerformance.staffName}}</text>
        <view class="panel-close" bindtap="closeStaffPerformance">×</view>
      </view>
      
      <view class="panel-body">
        <!-- 业绩概览 -->
        <view class="performance-overview">
          <view class="overview-item">
            <text class="overview-value">{{staffPerformance.count || 0}}</text>
            <text class="overview-label">总订单数</text>
          </view>
          <view class="overview-item">
            <text class="overview-value">¥{{staffPerformance.totalAmount || 0}}</text>
            <text class="overview-label">总金额</text>
          </view>
          <view class="overview-item">
            <text class="overview-value">¥{{staffPerformance.totalCommission || 0}}</text>
            <text class="overview-label">总提成</text>
          </view>
        </view>
        
        <!-- 支付方式分类统计 -->
        <view class="payment-stats">
          <view class="payment-stats-title">支付方式统计</view>
          <view class="payment-stats-content">
            <!-- 现金支付统计 -->
            <view class="payment-stats-item cash-stats">
              <view class="payment-stats-header">现金支付</view>
              <view class="payment-stats-data">
                <view class="payment-data-item">
                  <text class="payment-data-value">{{staffPerformance.cashStats.count || 0}}</text>
                  <text class="payment-data-label">订单数</text>
                </view>
                <view class="payment-data-item">
                  <text class="payment-data-value">¥{{staffPerformance.cashStats.amount || 0}}</text>
                  <text class="payment-data-label">金额</text>
                </view>
                <view class="payment-data-item">
                  <text class="payment-data-value">¥{{staffPerformance.cashStats.commission || 0}}</text>
                  <text class="payment-data-label">提成</text>
                </view>
              </view>
            </view>
            
            <!-- 余额支付统计 -->
            <view class="payment-stats-item balance-stats">
              <view class="payment-stats-header">余额支付</view>
              <view class="payment-stats-data">
                <view class="payment-data-item">
                  <text class="payment-data-value">{{staffPerformance.balanceStats.count || 0}}</text>
                  <text class="payment-data-label">订单数</text>
                </view>
                <view class="payment-data-item">
                  <text class="payment-data-value">¥{{staffPerformance.balanceStats.amount || 0}}</text>
                  <text class="payment-data-label">金额</text>
                </view>
                <view class="payment-data-item">
                  <text class="payment-data-value">¥{{staffPerformance.balanceStats.commission || 0}}</text>
                  <text class="payment-data-label">提成</text>
                </view>
              </view>
            </view>
          </view>
        </view>
        
        <!-- 订单列表 -->
        <view class="performance-orders" wx:if="{{staffPerformance.list && staffPerformance.list.length > 0}}">
          <view class="orders-title">订单明细</view>
          <view class="orders-header">
            <text class="order-date-header">日期</text>
            <text class="order-service-header">服务项目</text>
            <text class="order-payment-header">支付方式</text>
            <text class="order-price-header">价格</text>
            <text class="order-commission-header">提成</text>
          </view>
          
          <view class="order-item" wx:for="{{staffPerformance.list}}" wx:key="appointmentId">
            <text class="order-date">{{item.dateStr}}</text>
            <text class="order-service">{{item.serviceName}}</text>
            <text class="order-payment {{item.paymentMethod === 'balance' ? 'balance-payment' : 'cash-payment'}}">{{item.paymentMethodText}}</text>
            <text class="order-price">¥{{item.servicePrice}}</text>
            <text class="order-commission">¥{{item.commission}}</text>
          </view>
        </view>
        
        <!-- 空状态 -->
        <view class="empty-orders" wx:if="{{!staffPerformance.list || staffPerformance.list.length === 0}}">
          <text>暂无订单数据</text>
        </view>
      </view>
    </view>
  </view>
  
  <!-- 支出详情弹窗 -->
  <view class="expense-detail-panel {{showExpenseDetail ? 'show' : ''}}" wx:if="{{showExpenseDetail}}">
    <view class="panel-mask" bindtap="closeExpenseDetail"></view>
    <view class="panel-content">
      <view class="panel-header">
        <text class="panel-title">支出详情</text>
        <view class="panel-close" bindtap="closeExpenseDetail">×</view>
      </view>
      
      <view class="panel-body">
        <!-- 支出信息 -->
        <view class="expense-detail-info">
          <view class="detail-item">
            <text class="detail-label">员工：</text>
            <text class="detail-value">{{currentExpense.staffName}}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">金额：</text>
            <text class="detail-value expense-color">¥{{currentExpense.formattedAmount}}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">时间：</text>
            <text class="detail-value">{{currentExpense.formattedDate}}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">备注：</text>
            <text class="detail-value">{{currentExpense.remark || '无'}}</text>
          </view>
        </view>
        
        <!-- 凭证图片 -->
        <view class="expense-images" wx:if="{{currentExpense.images && currentExpense.images.length > 0}}">
          <view class="images-title">凭证图片</view>
          <view class="images-container">
            <image 
              wx:for="{{currentExpense.images}}" 
              wx:key="index" 
              src="{{item}}" 
              mode="aspectFill" 
              class="expense-image" 
              bindtap="previewImage" 
              data-urls="{{currentExpense.images}}" 
              data-current="{{item}}"
            ></image>
          </view>
        </view>
        
        <!-- 无图片提示 -->
        <view class="no-images" wx:if="{{!currentExpense.images || currentExpense.images.length === 0}}">
          <text>无凭证图片</text>
        </view>
        
        <!-- 操作按钮 -->
        <view class="expense-actions">
          <view class="delete-btn" bindtap="deleteExpense" data-id="{{currentExpense._id}}">删除记录</view>
        </view>
      </view>
    </view>
  </view>
</view> 