.nav-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 88rpx;
  transform: translateY(-100%);
  transition: transform 0.3s ease-out;
  z-index: 100;
  overflow: hidden;
  opacity: 0; /* 初始完全透明，防止闪现 */
  visibility: hidden; /* 初始隐藏，防止闪现 */
}

.nav-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
}

.nav-visible {
  transform: translateY(0);
  opacity: 1; /* 显示时恢复透明度 */
  visibility: visible; /* 显示时恢复可见性 */
}

.nav-content {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 30rpx;
  position: relative;
  z-index: 1;
}

.nav-title {
  color: #ffffff;
  font-size: 32rpx;
  font-weight: 500;
} 