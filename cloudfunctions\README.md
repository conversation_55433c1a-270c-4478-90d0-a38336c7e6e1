# 云函数部署说明

## 需要部署的云函数

以下云函数需要部署以支持员工系统功能：

1. **staffManager** - 员工管理云函数
   - 处理员工登录
   - 获取员工业绩数据
   - 管理员工账号

2. **orderManager** - 订单管理云函数
   - 获取待验证订单
   - 获取员工处理过的订单
   - 获取订单详情
   - 完成订单
   - 通过核销码验证订单

## 部署步骤

1. 打开微信开发者工具
2. 点击"云开发"按钮进入云开发控制台
3. 选择"云函数"选项卡
4. 对于每个云函数:
   - 找到对应的云函数文件夹
   - 右键点击选择"上传并部署：云端安装依赖"
   - 等待部署完成

## 数据库集合

以下数据库集合将被自动创建（如果不存在）：

- `staff` - 存储员工信息
- `orders` - 存储订单信息
- `staff_performance` - 存储员工业绩记录

## 测试功能

部署完成后，可以测试以下功能：

1. **员工登录**
   - 使用员工手机号和密码登录系统

2. **查看业绩**
   - 登录后在员工首页查看业绩概览
   - 点击"业绩详情"查看更多业绩信息

3. **订单验证**
   - 在员工首页点击"订单验证"
   - 输入核销码验证订单
   - 或查看待验证订单列表

4. **查看订单记录**
   - 在员工首页点击"订单记录"
   - 查看已处理的订单列表
   - 点击订单查看详情

## 注意事项

- 确保所有云函数都已成功部署
- 首次使用需要在管理员后台创建员工账号
- 测试前确保有测试订单数据 