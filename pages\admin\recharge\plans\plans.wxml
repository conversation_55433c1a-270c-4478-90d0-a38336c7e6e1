<view class="page-container">
  <!-- 状态栏安全区域 -->
  <view class="status-bar"></view>
  
  <!-- 导航栏 -->
  <view class="nav-header">
    <view class="back-btn" bindtap="navigateBack">
      <view class="arrow-left"></view>
    </view>
    <view class="header-title">充值方案管理</view>
    <view class="placeholder-btn"></view>
  </view>
  
  <!-- 内容区域 - 使用页面级别的滚动 -->
  <view class="content-area">
    <!-- 加载中 -->
    <view class="loading-container" wx:if="{{loading}}">
      <view class="loading-spinner"></view>
      <text class="loading-text">加载中...</text>
    </view>
    
    <!-- 空状态 -->
    <view class="empty-container" wx:elif="{{rechargePlans.length === 0}}">
      <text class="empty-text">暂无充值方案</text>
      <view class="empty-btn" bindtap="showAddPlanModal">添加方案</view>
    </view>
    
    <!-- 充值方案列表 -->
    <view class="plans-list" wx:else>
      <view class="plan-item" wx:for="{{rechargePlans}}" wx:key="_id">
        <view class="plan-card {{!item.isVisible ? 'hidden' : ''}}">
          <!-- 方案图片 -->
          <image class="plan-image" src="{{item.image || '/static/default-recharge.png'}}" mode="aspectFit"></image>
          
          <!-- 方案内容 -->
          <view class="plan-content">
            <view class="plan-header">
              <text class="plan-title">{{item.title}}</text>
              <view class="plan-status {{item.isVisible ? 'visible' : 'hidden'}}">
                <text wx:if="{{item.isVisible}}">已上线</text>
                <text wx:else>已隐藏</text>
              </view>
            </view>
            
            <view class="plan-desc">{{item.description || '暂无描述'}}</view>
            
            <view class="plan-price-info">
              <view class="plan-price">
                <text class="price-label">充值金额:</text>
                <text class="price-value">¥{{item.amount}}</text>
              </view>
              <view class="plan-bonus">
                <text class="bonus-label">赠送金额:</text>
                <text class="bonus-value">¥{{item.bonus}}</text>
              </view>
              <view class="plan-total">
                <text class="total-label">总金额:</text>
                <text class="total-value">¥{{item.totalAmount}}</text>
              </view>
              <view class="plan-commission">
                <text class="commission-label">推广佣金:</text>
                <text class="commission-value">¥{{item.promotionCommission}}</text>
              </view>
            </view>
          </view>
          
          <!-- 操作按钮 -->
          <view class="plan-actions">
            <view class="action-btn edit" bindtap="showEditPlanModal" data-plan="{{item}}">编辑</view>
            <view class="action-btn visibility" bindtap="toggleVisibility" data-id="{{item._id}}" data-visible="{{item.isVisible}}">
              {{item.isVisible ? '隐藏' : '显示'}}
            </view>
            <view class="action-btn delete" bindtap="deletePlan" data-id="{{item._id}}">删除</view>
          </view>
        </view>
      </view>
      
      <!-- 底部安全区域 -->
      <view class="bottom-safe-area"></view>
    </view>
  </view>
  
  <!-- 添加按钮 - 固定在底部 -->
  <view class="floating-add-btn" bindtap="showAddPlanModal">
    <text>+</text>
    <text class="add-btn-text">添加方案</text>
  </view>
  
  <!-- 添加方案弹窗 -->
  <view class="modal" wx:if="{{showAddModal}}">
    <view class="modal-mask" bindtap="closeAddModal"></view>
    <view class="modal-content">
      <view class="modal-header">
        <text class="modal-title">添加充值方案</text>
        <view class="modal-close" bindtap="closeAddModal">×</view>
      </view>
      
      <scroll-view scroll-y="true" class="modal-body" enhanced="true" show-scrollbar="true">
        <view class="form-group">
          <text class="form-label">方案标题</text>
          <input class="form-input" placeholder="请输入方案标题" value="{{formData.title}}" bindinput="onInput" data-field="title"></input>
        </view>
        
        <view class="form-group">
          <text class="form-label">方案描述</text>
          <textarea class="form-textarea" placeholder="请输入方案描述" value="{{formData.description}}" bindinput="onInput" data-field="description"></textarea>
        </view>
        
        <view class="form-group">
          <text class="form-label">充值金额 (¥)</text>
          <input class="form-input" type="digit" placeholder="请输入充值金额" value="{{formData.amount}}" bindinput="onInput" data-field="amount"></input>
        </view>
        
        <view class="form-group">
          <text class="form-label">赠送金额 (¥)</text>
          <input class="form-input" type="digit" placeholder="请输入赠送金额" value="{{formData.bonus}}" bindinput="onInput" data-field="bonus"></input>
        </view>
        
        <view class="form-group">
          <text class="form-label">推广佣金 (¥)</text>
          <input class="form-input" type="digit" placeholder="请输入推广佣金" value="{{formData.promotionCommission}}" bindinput="onInput" data-field="promotionCommission"></input>
        </view>
        
        <view class="form-group">
          <text class="form-label">方案图片</text>
          <view class="image-uploader" bindtap="uploadImage">
            <block wx:if="{{tempImageUrl}}">
              <image class="upload-preview" src="{{tempImageUrl}}" mode="aspectFill"></image>
            </block>
            <block wx:else>
              <image class="upload-icon" src="/static/icons/upload.png" mode="aspectFit"></image>
              <view class="upload-text">点击上传图片</view>
            </block>
            <view class="upload-loading" wx:if="{{uploadLoading}}">
              <view class="loading-spinner"></view>
            </view>
          </view>
        </view>
        
        <view class="form-group">
          <text class="form-label">是否显示</text>
          <switch checked="{{formData.isVisible}}" bindchange="onSwitchChange" data-field="isVisible"></switch>
        </view>
      </scroll-view>
      
      <view class="modal-footer">
        <view class="modal-btn cancel" bindtap="closeAddModal">取消</view>
        <view class="modal-btn confirm" bindtap="addRechargePlan">确认添加</view>
      </view>
    </view>
  </view>
  
  <!-- 编辑方案弹窗 -->
  <view class="modal" wx:if="{{showEditModal}}">
    <view class="modal-mask" bindtap="closeEditModal"></view>
    <view class="modal-content">
      <view class="modal-header">
        <text class="modal-title">编辑充值方案</text>
        <view class="modal-close" bindtap="closeEditModal">×</view>
      </view>
      
      <scroll-view scroll-y="true" class="modal-body" enhanced="true" show-scrollbar="true">
        <view class="form-group">
          <text class="form-label">方案标题</text>
          <input class="form-input" placeholder="请输入方案标题" value="{{formData.title}}" bindinput="onInput" data-field="title"></input>
        </view>
        
        <view class="form-group">
          <text class="form-label">方案描述</text>
          <textarea class="form-textarea" placeholder="请输入方案描述" value="{{formData.description}}" bindinput="onInput" data-field="description"></textarea>
        </view>
        
        <view class="form-group">
          <text class="form-label">充值金额 (¥)</text>
          <input class="form-input" type="digit" placeholder="请输入充值金额" value="{{formData.amount}}" bindinput="onInput" data-field="amount"></input>
        </view>
        
        <view class="form-group">
          <text class="form-label">赠送金额 (¥)</text>
          <input class="form-input" type="digit" placeholder="请输入赠送金额" value="{{formData.bonus}}" bindinput="onInput" data-field="bonus"></input>
        </view>
        
        <view class="form-group">
          <text class="form-label">推广佣金 (¥)</text>
          <input class="form-input" type="digit" placeholder="请输入推广佣金" value="{{formData.promotionCommission}}" bindinput="onInput" data-field="promotionCommission"></input>
        </view>
        
        <view class="form-group">
          <text class="form-label">方案图片</text>
          <view class="image-uploader" bindtap="uploadImage">
            <block wx:if="{{tempImageUrl}}">
              <image class="upload-preview" src="{{tempImageUrl}}" mode="aspectFill"></image>
            </block>
            <block wx:else>
              <image class="upload-icon" src="/static/icons/upload.png" mode="aspectFit"></image>
              <view class="upload-text">点击上传图片</view>
            </block>
            <view class="upload-loading" wx:if="{{uploadLoading}}">
              <view class="loading-spinner"></view>
            </view>
          </view>
        </view>
        
        <view class="form-group">
          <text class="form-label">是否显示</text>
          <switch checked="{{formData.isVisible}}" bindchange="onSwitchChange" data-field="isVisible"></switch>
        </view>
      </scroll-view>
      
      <view class="modal-footer">
        <view class="modal-btn cancel" bindtap="closeEditModal">取消</view>
        <view class="modal-btn confirm" bindtap="updateRechargePlan">确认修改</view>
      </view>
    </view>
  </view>
</view> 