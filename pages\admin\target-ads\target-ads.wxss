/* 指向广告管理页面样式 */
.page-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f7;
  margin: 0; /* 确保页面容器没有外边距 */
  padding: 0; /* 确保页面容器没有内边距 */
  box-sizing: border-box; /* 确保盒模型计算正确 */
}

.page-header {
  position: sticky;
  top: 0;
  z-index: 100;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  padding-top: 90rpx; /* 增加顶部内边距，确保在胶囊按钮下方 */
  margin-bottom: 20rpx;
  background-color: #ffffff;
  border-bottom: 1rpx solid #e0e0e0;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.header-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #4a4a4a;
}

.header-actions {
  display: flex;
  align-items: center;
}

.back-icon {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  color: #333333;
}

.content-admin-container {
  flex: 1;
  padding: 0 30rpx; /* 调整为30rpx，与页面头部保持一致 */
  padding-bottom: 40rpx;
  margin: 0; /* 确保没有外边距 */
  box-sizing: border-box; /* 确保padding计算正确 */
}



/* 表单容器 */
.form-container {
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin: 0; /* 重置所有外边距 */
  margin-bottom: 20rpx; /* 只保留底部外边距 */
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  width: 100%; /* 确保宽度100% */
  box-sizing: border-box; /* 确保padding计算正确 */
}

/* 表单项 */
.form-item {
  margin-bottom: 40rpx;
}

.form-item:last-child {
  margin-bottom: 0;
}

.form-label {
  font-size: 28rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 20rpx;
  display: block;
}

/* 图片上传 */
.upload-container {
  width: 100%;
}

.upload-area {
  width: 100%;
  height: 300rpx;
  border: 2rpx dashed #cccccc;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  background-color: #fafafa;
}

.preview-image {
  width: 100%;
  height: 100%;
  border-radius: 10rpx;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #999999;
}

.upload-icon {
  font-size: 60rpx;
  margin-bottom: 10rpx;
}

.upload-text {
  font-size: 24rpx;
}

.upload-loading {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 10rpx;
}

.loading-text {
  color: #ffffff;
  font-size: 28rpx;
}

.upload-tips {
  margin-top: 10rpx;
}

.upload-tips text {
  font-size: 22rpx;
  color: #999999;
}

/* 内容选择器 */
.content-selector {
  width: 100%;
}

.selector-display {
  height: 80rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  padding: 0 20rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #ffffff;
  font-size: 28rpx;
}

.selected-content {
  color: #333333;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.placeholder-text {
  color: #999999;
  flex: 1;
}

.selector-arrow {
  color: #cccccc;
  font-size: 24rpx;
}

/* 选择器 */
.form-picker {
  width: 100%;
}

.picker-display {
  height: 80rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  padding: 0 20rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #ffffff;
  font-size: 28rpx;
  color: #222222; /* 深色文字，确保在白色背景下清晰可见 */
}

.picker-arrow {
  color: #cccccc;
  font-size: 24rpx;
}

/* 开关 */
.form-switch {
  transform: scale(1.2);
}

/* 输入提示 */
.input-tips {
  margin-top: 10rpx;
}

.input-tips text {
  font-size: 22rpx;
  color: #999999;
}

/* 保存按钮 */
.save-container {
  padding: 30rpx 0;
  margin-top: 20rpx;
}

.save-btn {
  width: 100%;
  height: 88rpx;
  background-color: #0070c9;
  color: #ffffff;
  border: none;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 112, 201, 0.2);
  transition: all 0.2s ease;
}

.save-btn:active {
  background-color: #005a9e;
  transform: translateY(2rpx);
}

.save-btn.saving {
  background-color: #cccccc;
  color: #999999;
  box-shadow: none;
}

.save-btn[disabled] {
  background-color: #cccccc;
  color: #999999;
  box-shadow: none;
}

/* 内容选择弹窗 */
.content-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 9999;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
}

.modal-content {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 70%;
  background-color: #ffffff;
  border-radius: 20rpx 20rpx 0 0;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #e0e0e0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

.modal-close {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  color: #666666;
}

.content-tabs {
  display: flex;
  border-bottom: 1rpx solid #e0e0e0;
}

.tab-item {
  flex: 1;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 26rpx;
  color: #666666;
  position: relative;
}

.tab-item.active {
  color: #0070c9;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 4rpx;
  background-color: #0070c9;
  border-radius: 2rpx;
}

.content-list {
  flex: 1;
  padding: 20rpx;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200rpx;
}

.loading-text {
  color: #999999;
  font-size: 28rpx;
}

.content-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  margin-bottom: 20rpx;
  background-color: #f8f8f8;
  border-radius: 12rpx;
  position: relative;
}

.content-cover {
  width: 120rpx;
  height: 80rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
}

/* 无跳转选项特殊样式 */
.no-jump-indicator {
  width: 120rpx;
  height: 80rpx;
  margin-right: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.no-jump-text {
  font-size: 32rpx;
  font-weight: 700;
  color: #0070c9;
  text-align: center;
}

.content-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.content-title {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.content-subtitle {
  font-size: 24rpx;
  color: #666666;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.select-icon {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  background-color: #0070c9;
  color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: bold;
}

.empty-content {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200rpx;
  color: #999999;
  font-size: 28rpx;
}
