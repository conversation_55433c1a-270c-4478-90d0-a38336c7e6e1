<view class="verify-container">
  <!-- 顶部状态栏 -->
  <view class="page-header">
    <view class="back-icon" bindtap="goBack">
      <text class="iconfont icon-back">←</text>
    </view>
    <view class="header-title">订单验证</view>
    <view class="placeholder"></view>
  </view>

  <!-- 核销码输入区域 -->
  <view class="verify-code-section">
    <view class="verify-code-title">输入核销码</view>
    <view class="verify-code-input-container">
      <input 
        class="verify-code-input" 
        type="number" 
        placeholder="请输入预约核销码" 
        value="{{verifyCode}}" 
        bindinput="inputVerifyCode"
        maxlength="8"
      />
      <button 
        class="verify-button" 
        bindtap="verifyByCode" 
        loading="{{isVerifying}}"
      >验证</button>
    </view>
  </view>

  <!-- 内容区域 -->
  <view class="content">
    <!-- 待验证订单标题 -->
    <view class="section-title">待验证订单</view>
    
    <!-- 加载中 -->
    <view class="loading" wx:if="{{isLoading}}">
      <view class="loading-icon"></view>
      <view class="loading-text">加载中...</view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{!isLoading && isEmpty}}">
      <view class="empty-icon">📋</view>
      <view class="empty-text">暂无待验证订单</view>
      <view class="empty-tips">有新订单时会在这里显示</view>
    </view>

    <!-- 订单列表 -->
    <view class="order-list" wx:if="{{!isLoading && !isEmpty}}">
      <block wx:for="{{orderList}}" wx:key="_id">
        <view class="order-item" bindtap="viewOrderDetail" data-id="{{item._id}}">
          <view class="order-header">
            <view class="order-number">订单号: {{item.orderNumber}}</view>
            <view class="order-status">{{item.statusText}}</view>
          </view>
          
          <view class="order-info">
            <view class="info-row">
              <view class="info-label">预约时间:</view>
              <view class="info-value">{{item.formattedDate}} {{item.appointmentTime}}</view>
            </view>
            
            <view class="info-row">
              <view class="info-label">服务项目:</view>
              <view class="info-value">{{item.serviceName}}</view>
            </view>
            
            <view class="info-row">
              <view class="info-label">客户姓名:</view>
              <view class="info-value">{{item.userName}}</view>
            </view>
            
            <view class="info-row">
              <view class="info-label">联系电话:</view>
              <view class="info-value">{{item.userPhone}}</view>
            </view>
          </view>
          
          <view class="order-footer">
            <view class="order-time">下单时间: {{item.createTime}}</view>
            <view class="verify-btn" catchtap="verifyOrder" data-id="{{item._id}}" data-index="{{index}}">
              验证订单
            </view>
          </view>
        </view>
      </block>
    </view>
  </view>
</view> 