/**
 * 视频列表模块测试
 * 包含单元测试和集成测试，验证视频加载、刷新、分页等功能
 * @version 1.0.0
 */

const VideoListModule = require('../modules/video-list');
const videoUtils = require('../utils/video-utils');
const { 
  PAGINATION,
  LOADING_CONFIG,
  VIDEO_CONFIG 
} = require('../constants/index-constants');

// Mock 微信小程序 API
global.wx = {
  nextTick: (callback) => setTimeout(callback, 0),
  removeStorageSync: jest.fn(),
  stopPullDownRefresh: jest.fn(),
  setStorageSync: jest.fn(),
  getStorageSync: jest.fn()
};

global.getApp = jest.fn(() => ({
  globalData: {
    debugMode: false,
    videoList: []
  }
}));

describe('VideoListModule', () => {
  let videoListModule;
  let mockPageContext;
  let mockVideoUtils;

  beforeEach(() => {
    // 创建模拟的页面上下文
    mockPageContext = {
      data: {
        videoList: [],
        loading: false,
        firstLoading: true,
        hasMore: true,
        page: 0,
        pageSize: 4,
        urlCache: {},
        urlFetchingIds: [],
        lastRefreshTime: 0,
        isRefreshing: false
      },
      setData: jest.fn((data, callback) => {
        Object.assign(mockPageContext.data, data);
        if (callback) callback();
      }),
      app: getApp(),
      dataManager: {
        cacheVideoList: jest.fn(),
        batchCacheVideoUrls: jest.fn()
      }
    };

    // 创建模拟的视频工具函数
    mockVideoUtils = {
      getVideoList: jest.fn(),
      getVideoUrl: jest.fn(),
      cleanExpiredCache: jest.fn()
    };

    // 创建视频列表模块实例
    videoListModule = new VideoListModule(mockPageContext);
    
    // 替换视频工具函数
    videoListModule.videoUtils = mockVideoUtils;
    videoListModule.getVideoList = mockVideoUtils.getVideoList;
    videoListModule.getVideoUrl = mockVideoUtils.getVideoUrl;
    videoListModule.cleanExpiredCache = mockVideoUtils.cleanExpiredCache;

    // 清除所有 mock 调用记录
    jest.clearAllMocks();
  });

  afterEach(() => {
    if (videoListModule) {
      videoListModule.destroy();
    }
  });

  describe('初始化测试', () => {
    test('应该正确初始化模块', () => {
      expect(videoListModule.moduleName).toBe('VideoList');
      expect(videoListModule.initialized).toBe(false);
      expect(videoListModule.page).toBe(mockPageContext);
    });

    test('应该正确初始化列表状态', () => {
      videoListModule.init();
      
      expect(videoListModule.initialized).toBe(true);
      expect(mockPageContext.setData).toHaveBeenCalled();
    });

    test('初始化失败时应该处理错误', () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
      
      // 模拟初始化错误
      videoListModule.initListState = jest.fn(() => {
        throw new Error('初始化失败');
      });
      
      videoListModule.init();
      
      expect(consoleSpy).toHaveBeenCalled();
      consoleSpy.mockRestore();
    });
  });

  describe('视频列表加载测试', () => {
    beforeEach(() => {
      videoListModule.init();
    });

    test('应该成功加载视频列表', async () => {
      const mockVideoList = [
        { id: '1', mainTitle: '测试视频1', baseId: '1' },
        { id: '2', mainTitle: '测试视频2', baseId: '2' }
      ];

      mockVideoUtils.getVideoList.mockResolvedValue(mockVideoList);
      mockVideoUtils.getVideoUrl.mockResolvedValue('http://test-url.com/video.mp4');

      const result = await videoListModule.loadVideoList();

      expect(mockVideoUtils.getVideoList).toHaveBeenCalledWith(false);
      expect(result).toHaveLength(2);
      expect(mockPageContext.setData).toHaveBeenCalledWith(
        expect.objectContaining({
          loading: false,
          firstLoading: false,
          showContent: true
        })
      );
    });

    test('应该正确处理刷新操作', async () => {
      const mockVideoList = [
        { id: '1', mainTitle: '测试视频1', baseId: '1' }
      ];

      mockVideoUtils.getVideoList.mockResolvedValue(mockVideoList);
      mockVideoUtils.getVideoUrl.mockResolvedValue('http://test-url.com/video.mp4');

      await videoListModule.loadVideoList(true);

      expect(mockVideoUtils.getVideoList).toHaveBeenCalledWith(true);
      expect(mockPageContext.setData).toHaveBeenCalledWith(
        expect.objectContaining({
          page: PAGINATION.DEFAULT_PAGE,
          lastRefreshTime: expect.any(Number)
        })
      );
    });

    test('应该处理空数据情况', async () => {
      mockVideoUtils.getVideoList.mockResolvedValue([]);

      const result = await videoListModule.loadVideoList();

      expect(result).toEqual([]);
      expect(mockPageContext.setData).toHaveBeenCalledWith(
        expect.objectContaining({
          videoList: [],
          hasMore: false
        })
      );
    });

    test('应该处理加载错误', async () => {
      const error = new Error('网络错误');
      mockVideoUtils.getVideoList.mockRejectedValue(error);

      await expect(videoListModule.loadVideoList()).rejects.toThrow('网络错误');
      
      expect(mockPageContext.setData).toHaveBeenCalledWith(
        expect.objectContaining({
          loading: false,
          firstLoading: false,
          isRefreshing: false
        })
      );
    });

    test('应该防止重复加载', async () => {
      // 设置为正在加载状态
      mockPageContext.data.loading = true;

      const result = await videoListModule.loadVideoList();

      expect(mockVideoUtils.getVideoList).not.toHaveBeenCalled();
      expect(result).toBeUndefined();
    });
  });

  describe('下拉刷新测试', () => {
    beforeEach(() => {
      videoListModule.init();
    });

    test('应该正确处理下拉刷新', async () => {
      const mockVideoList = [
        { id: '1', mainTitle: '测试视频1', baseId: '1' }
      ];

      mockVideoUtils.getVideoList.mockResolvedValue(mockVideoList);
      mockVideoUtils.getVideoUrl.mockResolvedValue('http://test-url.com/video.mp4');

      await videoListModule.handleRefresh();

      expect(wx.removeStorageSync).toHaveBeenCalledWith('video_list_cache');
      expect(mockVideoUtils.getVideoList).toHaveBeenCalledWith(true);
      expect(wx.stopPullDownRefresh).toHaveBeenCalled();
    });

    test('应该防止重复刷新', async () => {
      // 设置为正在刷新状态
      mockPageContext.data.isRefreshing = true;

      await videoListModule.handleRefresh();

      expect(mockVideoUtils.getVideoList).not.toHaveBeenCalled();
      expect(wx.stopPullDownRefresh).toHaveBeenCalled();
    });

    test('应该处理刷新错误', async () => {
      const error = new Error('刷新失败');
      mockVideoUtils.getVideoList.mockRejectedValue(error);

      await videoListModule.handleRefresh();

      expect(mockPageContext.setData).toHaveBeenCalledWith(
        expect.objectContaining({
          isRefreshing: false
        })
      );
      expect(wx.stopPullDownRefresh).toHaveBeenCalled();
    });
  });

  describe('上拉加载更多测试', () => {
    beforeEach(() => {
      videoListModule.init();
    });

    test('应该在有更多数据时加载', async () => {
      mockPageContext.data.hasMore = true;
      mockPageContext.data.loading = false;

      const mockVideoList = [
        { id: '1', mainTitle: '测试视频1', baseId: '1' }
      ];

      mockVideoUtils.getVideoList.mockResolvedValue(mockVideoList);
      mockVideoUtils.getVideoUrl.mockResolvedValue('http://test-url.com/video.mp4');

      await videoListModule.handleLoadMore();

      expect(mockVideoUtils.getVideoList).toHaveBeenCalledWith(false);
    });

    test('应该在没有更多数据时不加载', async () => {
      mockPageContext.data.hasMore = false;

      const result = await videoListModule.handleLoadMore();

      expect(mockVideoUtils.getVideoList).not.toHaveBeenCalled();
      expect(result).toBeUndefined();
    });

    test('应该在正在加载时不重复加载', async () => {
      mockPageContext.data.hasMore = true;
      mockPageContext.data.loading = true;

      const result = await videoListModule.handleLoadMore();

      expect(mockVideoUtils.getVideoList).not.toHaveBeenCalled();
      expect(result).toBeUndefined();
    });
  });

  describe('批量视频处理测试', () => {
    beforeEach(() => {
      videoListModule.init();
    });

    test('应该正确处理有缓存的视频', async () => {
      const mockVideoList = [
        { id: '1', mainTitle: '测试视频1', baseId: '1' }
      ];

      // 设置缓存
      mockPageContext.data.urlCache = {
        '1': {
          url: 'http://cached-url.com/video.mp4',
          timestamp: Date.now()
        }
      };

      const result = await videoListModule.batchProcessVideos(mockVideoList);

      expect(result).toHaveLength(1);
      expect(result[0].videoUrl).toBe('http://cached-url.com/video.mp4');
      expect(mockVideoUtils.getVideoUrl).not.toHaveBeenCalled();
    });

    test('应该批量获取没有缓存的视频URL', async () => {
      const mockVideoList = [
        { id: '1', mainTitle: '测试视频1', baseId: '1' },
        { id: '2', mainTitle: '测试视频2', baseId: '2' }
      ];

      mockVideoUtils.getVideoUrl.mockResolvedValue('http://test-url.com/video.mp4');

      const result = await videoListModule.batchProcessVideos(mockVideoList);

      expect(result).toHaveLength(2);
      expect(mockVideoUtils.getVideoUrl).toHaveBeenCalledTimes(2);
    });

    test('应该处理空视频列表', async () => {
      const result = await videoListModule.batchProcessVideos([]);

      expect(result).toEqual([]);
      expect(mockVideoUtils.getVideoUrl).not.toHaveBeenCalled();
    });
  });

  describe('URL缓存管理测试', () => {
    beforeEach(() => {
      videoListModule.init();
    });

    test('应该正确更新URL缓存', () => {
      const mockVideoList = [
        { id: '1', baseId: '1', fileKey: 'key1' }
      ];
      const mockUrls = ['http://test-url.com/video.mp4'];

      videoListModule.updateUrlCache(mockVideoList, mockUrls);

      expect(mockPageContext.setData).toHaveBeenCalledWith({
        urlCache: expect.objectContaining({
          '1': expect.objectContaining({
            url: 'http://test-url.com/video.mp4',
            timestamp: expect.any(Number),
            baseId: '1',
            fileKey: 'key1'
          })
        })
      });
    });

    test('应该正确检查缓存过期', () => {
      const expiredCache = {
        timestamp: Date.now() - 2 * 60 * 60 * 1000 // 2小时前
      };
      const validCache = {
        timestamp: Date.now() - 30 * 60 * 1000 // 30分钟前
      };

      expect(videoListModule.isUrlCacheExpired(expiredCache)).toBe(true);
      expect(videoListModule.isUrlCacheExpired(validCache)).toBe(false);
      expect(videoListModule.isUrlCacheExpired(null)).toBe(true);
    });

    test('应该清理过期的URL缓存', () => {
      mockPageContext.data.urlCache = {
        '1': {
          url: 'http://test1.com',
          timestamp: Date.now() - 2 * 60 * 60 * 1000 // 过期
        },
        '2': {
          url: 'http://test2.com',
          timestamp: Date.now() - 30 * 60 * 1000 // 有效
        }
      };

      videoListModule.cleanExpiredUrlCache();

      expect(mockPageContext.setData).toHaveBeenCalledWith({
        urlCache: {
          '2': expect.objectContaining({
            url: 'http://test2.com'
          })
        }
      });
    });
  });

  describe('视频数据处理测试', () => {
    beforeEach(() => {
      videoListModule.init();
    });

    test('应该正确去重视频列表', () => {
      const mockVideoList = [
        { id: '1', mainTitle: '测试视频1' },
        { id: '2', mainTitle: '测试视频2' },
        { id: '1', mainTitle: '重复视频1' }
      ];

      const result = videoListModule.deduplicateVideoList(mockVideoList);

      expect(result).toHaveLength(2);
      expect(result.map(v => v.id)).toEqual(['1', '2']);
    });

    test('应该正确清理视频数据', () => {
      const mockVideoData = {
        id: '1',
        mainTitle: '测试视频',
        playCount: 1000,
        author: '测试作者'
      };

      const result = videoListModule.sanitizeVideoData(mockVideoData);

      expect(result).toMatchObject({
        id: '1',
        mainTitle: '测试视频',
        playCount: '1k',
        author: '测试作者',
        isPlaying: false
      });
    });

    test('应该正确格式化播放量', () => {
      expect(videoListModule.formatCount(500)).toBe('500');
      expect(videoListModule.formatCount(1500)).toBe('1.5k');
      expect(videoListModule.formatCount(15000)).toBe('1.5w');
    });
  });

  describe('列表状态管理测试', () => {
    beforeEach(() => {
      videoListModule.init();
    });

    test('应该正确更新视频列表', () => {
      const mockVideos = [
        { id: '1', mainTitle: '测试视频1' },
        { id: '2', mainTitle: '测试视频2' }
      ];

      videoListModule.updateVideoList(mockVideos);

      expect(mockPageContext.setData).toHaveBeenCalledWith({
        videoList: mockVideos
      });
    });

    test('应该正确获取当前视频列表', () => {
      mockPageContext.data.videoList = [
        { id: '1', mainTitle: '测试视频1' }
      ];

      const result = videoListModule.getVideoList();

      expect(result).toEqual(mockPageContext.data.videoList);
    });

    test('应该正确重置列表状态', () => {
      videoListModule.resetList();

      expect(mockPageContext.setData).toHaveBeenCalledWith({
        videoList: [],
        loading: false,
        firstLoading: true,
        hasMore: true,
        page: PAGINATION.DEFAULT_PAGE,
        lastRefreshTime: 0,
        isRefreshing: false,
        originalVideoList: null
      });
    });

    test('应该正确获取列表状态', () => {
      mockPageContext.data.videoList = [{ id: '1' }, { id: '2' }];
      mockPageContext.data.loading = true;
      mockPageContext.data.hasMore = false;

      const status = videoListModule.getListStatus();

      expect(status).toMatchObject({
        videoCount: 2,
        loading: true,
        hasMore: false
      });
    });
  });

  describe('错误处理测试', () => {
    beforeEach(() => {
      videoListModule.init();
    });

    test('应该处理无效的视频列表更新', () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

      videoListModule.updateVideoList('invalid');

      expect(consoleSpy).toHaveBeenCalled();
      consoleSpy.mockRestore();
    });

    test('应该处理URL缓存更新错误', () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
      
      // 模拟setData错误
      mockPageContext.setData = jest.fn(() => {
        throw new Error('setData失败');
      });

      videoListModule.updateUrlCache([{ id: '1' }], ['http://test.com']);

      expect(consoleSpy).toHaveBeenCalled();
      consoleSpy.mockRestore();
    });
  });

  describe('模块销毁测试', () => {
    test('应该正确销毁模块', () => {
      videoListModule.init();
      expect(videoListModule.initialized).toBe(true);

      videoListModule.destroy();
      expect(videoListModule.initialized).toBe(false);
    });
  });
});

describe('VideoListModule 集成测试', () => {
  let videoListModule;
  let mockPageContext;

  beforeEach(() => {
    mockPageContext = {
      data: {
        videoList: [],
        loading: false,
        firstLoading: true,
        hasMore: true,
        page: 0,
        pageSize: 4,
        urlCache: {},
        urlFetchingIds: [],
        lastRefreshTime: 0,
        isRefreshing: false
      },
      setData: jest.fn((data, callback) => {
        Object.assign(mockPageContext.data, data);
        if (callback) callback();
      }),
      app: getApp(),
      dataManager: {
        cacheVideoList: jest.fn(),
        batchCacheVideoUrls: jest.fn()
      }
    };

    videoListModule = new VideoListModule(mockPageContext);
    jest.clearAllMocks();
  });

  test('完整的视频加载流程', async () => {
    // 模拟完整的视频加载流程
    const mockVideoList = [
      { id: '1', mainTitle: '测试视频1', baseId: '1', fileKey: 'key1' },
      { id: '2', mainTitle: '测试视频2', baseId: '2', fileKey: 'key2' }
    ];

    videoListModule.videoUtils = {
      getVideoList: jest.fn().mockResolvedValue(mockVideoList),
      getVideoUrl: jest.fn().mockResolvedValue('http://test-url.com/video.mp4')
    };
    videoListModule.getVideoList = videoListModule.videoUtils.getVideoList;
    videoListModule.getVideoUrl = videoListModule.videoUtils.getVideoUrl;

    videoListModule.init();

    // 执行加载
    const result = await videoListModule.loadVideoList();

    // 验证结果
    expect(result).toHaveLength(2);
    expect(mockPageContext.data.videoList).toHaveLength(2);
    expect(mockPageContext.data.loading).toBe(false);
    expect(mockPageContext.data.firstLoading).toBe(false);
    expect(mockPageContext.data.showContent).toBe(true);
  });

  test('完整的刷新流程', async () => {
    const mockVideoList = [
      { id: '3', mainTitle: '新视频', baseId: '3', fileKey: 'key3' }
    ];

    videoListModule.videoUtils = {
      getVideoList: jest.fn().mockResolvedValue(mockVideoList),
      getVideoUrl: jest.fn().mockResolvedValue('http://test-url.com/video.mp4')
    };
    videoListModule.getVideoList = videoListModule.videoUtils.getVideoList;
    videoListModule.getVideoUrl = videoListModule.videoUtils.getVideoUrl;

    videoListModule.init();

    // 先加载一些数据
    mockPageContext.data.videoList = [
      { id: '1', mainTitle: '旧视频' }
    ];

    // 执行刷新
    await videoListModule.handleRefresh();

    // 验证刷新结果
    expect(mockPageContext.data.videoList).toHaveLength(1);
    expect(mockPageContext.data.videoList[0].id).toBe('3');
    expect(mockPageContext.data.isRefreshing).toBe(false);
  });
});