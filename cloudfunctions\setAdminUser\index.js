// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 初始化集合函数
async function initCollections() {
  try {
    // 检查并创建system_users集合
    try {
      await db.createCollection('system_users')
      console.log('创建system_users集合成功')
    } catch (err) {
      // 如果集合已存在，会抛出错误，这是正常的
      console.log('system_users集合已存在或创建失败:', err.message)
    }
    
    return true
  } catch (err) {
    console.error('初始化集合失败:', err)
    return false
  }
}

// 云函数入口函数
exports.main = async (event, context) => {
  // 初始化集合
  await initCollections()
  
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID
  const username = event.username || 'admin'
  
  if (!openid) {
    return {
      code: 1,
      message: '获取用户ID失败'
    }
  }
  
  try {
    // 查询system_users集合
    const userCollection = db.collection('system_users')
    
    // 检查用户是否已存在
    const userResult = await userCollection.where({
      openid: openid
    }).get()
    
    let userId = ''
    
    if (userResult.data.length === 0) {
      // 如果用户不存在，创建新用户
      const addResult = await userCollection.add({
        data: {
          openid: openid,
          username: username,
          isAdmin: true,
          createTime: db.serverDate(),
          updateTime: db.serverDate()
        }
      })
      userId = addResult._id
    } else {
      // 如果用户已存在，更新为管理员
      const user = userResult.data[0]
      userId = user._id
      
      await userCollection.doc(userId).update({
        data: {
          isAdmin: true,
          updateTime: db.serverDate()
        }
      })
    }
    
    return {
      code: 0,
      message: '',
      userId: userId,
      openid: openid
    }
  } catch (error) {
    console.error('设置管理员失败：', error)
    return {
      code: 2,
      message: '设置管理员失败',
      error: error
    }
  }
} 