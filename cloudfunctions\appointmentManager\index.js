// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV }) // 使用当前云环境
const db = cloud.database()
const appointmentsCollection = db.collection('appointments')
const servicesCollection = db.collection('services')
const carouselCollection = db.collection('carousel') // 添加轮播图集合
const _ = db.command

// 将fileID转换为临时URL的辅助函数
async function convertFileIDsToURLs(items) {
  if (!items || !Array.isArray(items) || items.length === 0) {
    return items;
  }
  
  try {
    // 收集所有需要转换的fileID
    const fileIDs = [];
    
    items.forEach(item => {
      // 检查各种可能的图片字段
      if (item.image && item.image.startsWith('cloud://')) {
        fileIDs.push(item.image);
      }
      if (item.detailImage && item.detailImage.startsWith('cloud://')) {
        fileIDs.push(item.detailImage);
      }
      if (item.coverUrl && item.coverUrl.startsWith('cloud://')) {
        fileIDs.push(item.coverUrl);
      }
      if (item.imageUrl && item.imageUrl.startsWith('cloud://')) {
        fileIDs.push(item.imageUrl);
      }
    });
    
    if (fileIDs.length === 0) {
      return items;
    }
    
    // 去重
    const uniqueFileIDs = [...new Set(fileIDs)];
    console.log('需要转换的fileID数量:', uniqueFileIDs.length);
    
    // 获取临时URL
    const result = await cloud.getTempFileURL({
      fileList: uniqueFileIDs
    });
    
    console.log('获取临时URL结果:', result);
    
    // 创建fileID到URL的映射
    const urlMap = {};
    if (result.fileList) {
      result.fileList.forEach(file => {
        if (file.status === 0) {
          urlMap[file.fileID] = file.tempFileURL;
        }
      });
    }
    
    // 替换items中的fileID为临时URL
    const processedItems = items.map(item => {
      const newItem = { ...item };

      if (newItem.image && urlMap[newItem.image]) {
        newItem.image = urlMap[newItem.image];
      }
      if (newItem.detailImage && urlMap[newItem.detailImage]) {
        newItem.detailImage = urlMap[newItem.detailImage];
      }
      if (newItem.coverUrl && urlMap[newItem.coverUrl]) {
        newItem.coverUrl = urlMap[newItem.coverUrl];
      }
      if (newItem.imageUrl && urlMap[newItem.imageUrl]) {
        newItem.imageUrl = urlMap[newItem.imageUrl];
      }

      return newItem;
    });
    
    console.log('图片URL转换完成');
    return processedItems;
  } catch (err) {
    console.error('转换图片URL失败:', err);
    // 如果转换失败，返回原始数据
    return items;
  }
}

// 获取北京时间的辅助函数
function getBJTime() {
  // 获取当前UTC时间
  const now = new Date();
  // 计算北京时间（UTC+8）
  const bjTime = new Date(now.getTime() + 8 * 60 * 60 * 1000);
  return bjTime;
}

// 格式化日期时间的辅助函数
function formatDateTime(date) {
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
}

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID
  
  // 初始化数据库集合
  await initCollections();
  
  // 如果是请求添加管理员权限
  if (event.action === 'addAdminPermission') {
    return addAdminPermission(openid);
  }
  
  // 根据不同的action执行不同的操作
  const { type, action } = event
  
  console.log('云函数被调用，参数：', event);
  
  // 检查权限 - 临时禁用权限检查，用于测试
  // if (type === 'admin' && !await isAdmin(openid)) {
  //   return { code: 403, message: '无权限' };
  // }
  
  // 前端API
  if (action === 'getServices') {
    return getServices(event);
  } else if (action === 'getCarousel') {
    return getCarousel(event); // 添加获取轮播图接口
  } else if (action === 'deleteAppointment') {
    // 直接处理删除预约的请求
    return deleteAppointment(event, context);
  } else if (action === 'updateAppointment') {
    // 直接处理更新预约的请求
    return updateAppointment(event, context);
  } else if (type === 'frontend') {
    switch (action) {
      case 'getAvailableTimeSlots': return getAvailableTimeSlots(event);
      case 'createAppointment': return createAppointment(openid, event);
      case 'getUserAppointments': return getUserAppointments(openid, event);
      case 'cancelAppointment': return cancelAppointment(openid, event);
      case 'checkExistingAppointments': return checkExistingAppointments(openid, event);
      default: return { code: 400, message: '无效的操作' };
    }
  }
  
  // 后端API
  if (type === 'admin') {
    switch (action) {
      case 'getAppointmentList': return getAppointmentList(event);
      case 'confirmAppointment': return confirmAppointment(event);
      case 'completeAppointment': return completeAppointment(event);
      case 'rejectAppointment': return rejectAppointment(event);
      case 'updateAppointment': return updateAppointment(event);
      case 'addService': return addService(event);
      case 'updateService': return updateService(event);
      case 'deleteService': return deleteService(event);
      case 'getAppointmentStats': return getAppointmentStats(event);
      case 'toggleServiceVisibility': return toggleServiceVisibility(event);
      case 'getCarousel': return getCarousel(event); // 获取轮播图
      case 'addCarousel': return addCarousel(event); // 添加轮播图
      case 'updateCarousel': return updateCarousel(event); // 更新轮播图
      case 'deleteCarousel': return deleteCarousel(event); // 删除轮播图
      case 'initCarousel': return initCarousel(event); // 初始化轮播图
      default: return { code: 400, message: '无效的操作' };
    }
  }

  // 员工API
  if (type === 'staff') {
    switch (action) {
      case 'verifyAppointment': return verifyAppointment(event);
      case 'getAppointmentDetail': return getAppointmentDetail(event);
      case 'getAssignedAppointments': return getAssignedAppointments(event);
      default: return { code: 400, message: '无效的操作' };
    }
  }
  
  return { code: 400, message: '无效的请求类型' };
}

// 初始化数据库集合
async function initCollections() {
  try {
    // 检查并创建appointments集合
    try {
      await db.createCollection('appointments');
      console.log('创建appointments集合成功');
    } catch (err) {
      // 如果集合已存在，会抛出错误，这是正常的
      console.log('appointments集合已存在或创建失败:', err.message);
    }
    
    // 检查并创建services集合
    try {
      await db.createCollection('services');
      console.log('创建services集合成功');
    } catch (err) {
      // 如果集合已存在，会抛出错误，这是正常的
      console.log('services集合已存在或创建失败:', err.message);
    }

    // 检查并创建staff_performance集合
    try {
      await db.createCollection('staff_performance');
      console.log('创建staff_performance集合成功');
    } catch (err) {
      // 如果集合已存在，会抛出错误，这是正常的
      console.log('staff_performance集合已存在或创建失败:', err.message);
    }
    
    // 检查并创建carousel集合（轮播图）
    try {
      await db.createCollection('carousel');
      console.log('创建carousel集合成功');
    } catch (err) {
      // 如果集合已存在，会抛出错误，这是正常的
      console.log('carousel集合已存在或创建失败:', err.message);
    }
    
    return true;
  } catch (err) {
    console.error('初始化集合失败:', err);
    return false;
  }
}

// 检查是否为管理员
async function isAdmin(openid) {
  try {
    console.log('正在检查用户权限，openid:', openid);
    
    const adminRes = await db.collection('system_users').where({
      _openid: openid,
      role: 'admin'
    }).get();
    
    console.log('管理员检查结果:', adminRes);
    
    const isAdminUser = adminRes.data && adminRes.data.length > 0;
    console.log('用户是否为管理员:', isAdminUser);
    
    return isAdminUser;
  } catch (err) {
    console.error('检查管理员权限失败', err);
    return false;
  }
}

// ==================== 前端API实现 ====================

// 获取所有服务
async function getServices(data) {
  try {
    console.log('获取服务列表，类型:', data.type);
    // 如果是前端请求，只返回可见的服务并按更新时间排序
    let query = servicesCollection;
    if (data.type !== 'admin') {
      query = query.where({
        isVisible: true
      }).orderBy('updateTime', 'desc');
    } else {
      // 管理员请求也按更新时间排序，但不过滤可见性
      query = query.orderBy('updateTime', 'desc');
    }
    
    const services = await query.get();
    console.log('查询到的服务数量:', services.data.length);
    
    // 转换图片fileID为临时URL
    const processedServices = await convertFileIDsToURLs(services.data);
    console.log('图片URL转换完成');
    
    return {
      code: 0,
      success: true,
      data: processedServices,
      message: '获取服务列表成功'
    };
  } catch (err) {
    console.error('获取服务列表失败', err);
    return {
      code: -1,
      success: false,
      message: '获取服务列表失败: ' + err.message
    };
  }
}

// 获取可用时间段
async function getAvailableTimeSlots(data) {
  const { date, serviceId } = data;
  
  if (!date) {
    return {
      code: 400,
      message: '缺少日期参数'
    };
  }
  
  try {
    // 获取服务的时长信息
    let serviceDuration = 30; // 默认30分钟
    if (serviceId) {
      const serviceInfo = await servicesCollection.doc(serviceId).get();
      if (serviceInfo.data && serviceInfo.data.duration) {
        serviceDuration = serviceInfo.data.duration;
      }
    }
    
    // 获取当天已有的预约 - 保留此代码以便将来可能的扩展功能
    const appointments = await appointmentsCollection.where({
      date: date,
      status: _.neq('cancelled') // 排除已取消的预约
    }).get();
    
    // 生成时间段（10:00 - 20:00，每半小时一个时间段）
    const timeSlots = [];
    const startHour = 10;
    const endHour = 20;
    
    for (let hour = startHour; hour < endHour; hour++) {
      for (let minute of ['00', '30']) {
        const time = `${hour.toString().padStart(2, '0')}:${minute}`;
        
        // 修改：所有时间段都设置为可用，不再检查是否已被预约
        timeSlots.push({
          time: time,
          available: true
        });
      }
    }
    
    return {
      code: 0,
      data: timeSlots,
      message: '获取可用时间段成功'
    };
  } catch (err) {
    console.error('获取可用时间段失败', err);
    return {
      code: -1,
      message: '获取可用时间段失败: ' + err.message
    };
  }
}

// 创建预约
async function createAppointment(openid, data) {
  const { serviceId, date, time, phoneNumber, staffId, staffName, preferredStaffId, preferredStaffName, assignment_type, imageFileIDs } = data;
  
  console.log('创建预约，参数：', { serviceId, date, time, phoneNumber, staffId, staffName, preferredStaffId, preferredStaffName, assignment_type, imageFileIDs });
  
  if (!serviceId || !date || !time) {
    return {
      code: 400,
      message: '缺少必要参数'
    };
  }
  
  try {
    // 尝试不同的方式获取服务信息
    let serviceInfo;
    
    try {
      // 先尝试直接使用serviceId作为文档ID
      serviceInfo = await servicesCollection.doc(serviceId).get();
      console.log('使用原始serviceId查询成功:', serviceId);
    } catch (err) {
      console.log('使用原始serviceId查询失败:', err);
      
      // 如果失败，尝试添加"service_"前缀
      try {
        const serviceIdWithPrefix = `service_${serviceId}`;
        serviceInfo = await servicesCollection.doc(serviceIdWithPrefix).get();
        console.log('使用添加前缀的serviceId查询成功:', serviceIdWithPrefix);
      } catch (err2) {
        console.log('使用添加前缀的serviceId查询失败:', err2);
        
        // 如果还是失败，尝试使用where查询
        try {
          const queryResult = await servicesCollection.where({
            id: serviceId
          }).get();
          
          if (queryResult.data && queryResult.data.length > 0) {
            serviceInfo = { data: queryResult.data[0] };
            console.log('使用where查询成功:', queryResult.data[0]);
          } else {
            console.log('使用where查询无结果');
            return {
              code: 404,
              message: '未找到服务信息'
            };
          }
        } catch (err3) {
          console.log('使用where查询失败:', err3);
          return {
            code: 404,
            message: '未找到服务信息'
          };
        }
      }
    }
    
    if (!serviceInfo || !serviceInfo.data) {
      return {
        code: 404,
        message: '未找到服务信息'
      };
    }
    
    console.log('获取到的服务信息:', serviceInfo.data);
    
    // 生成6位随机核销码
    const verifyCode = Math.floor(100000 + Math.random() * 900000).toString();
    
    // 创建预约记录
    const appointmentData = {
      _openid: openid,
      serviceId: serviceId,
      serviceName: serviceInfo.data.name,
      servicePrice: serviceInfo.data.price,
      date: date,
      time: time,
      phoneNumber: phoneNumber || '', // 添加手机号字段
      status: 'pending', // 预约状态：pending=待确认, confirmed=已确认, completed=已完成, cancelled=已取消, rejected=已拒绝
      createTime: db.serverDate(),
      updateTime: db.serverDate(),
      // 新增核销相关字段
      verifyCode: verifyCode,
      verified: false,
      verifyTime: null,
      verifyStaffId: '',
      verifyStaffName: '',
      // 添加员工指定相关字段
      assignment_type: assignment_type || 'random', // 分配类型：random=随机分配, specified=指定员工
      preferredStaffId: preferredStaffId || staffId || '', // 兼容两种参数名
      preferredStaffName: preferredStaffName || staffName || '', // 兼容两种参数名
      originalPrice: serviceInfo.data.originalPrice || null, // 添加originalPrice字段
      // 添加图片相关字段
      imageFileIDs: imageFileIDs || [] // 存储图片的fileID数组
    };
    
    console.log('准备创建预约记录：', appointmentData);
    
    const result = await appointmentsCollection.add({
      data: appointmentData
    });
    
    console.log('预约创建结果：', result);
    
    return {
      code: 0,
      data: {
        appointmentId: result._id,
        verifyCode: verifyCode // 返回核销码
      },
      message: '预约创建成功'
    };
  } catch (err) {
    console.error('创建预约失败', err);
    return {
      code: -1,
      message: '创建预约失败: ' + err.message
    };
  }
}

// 获取用户预约列表
async function getUserAppointments(openid, data) {
  const { status } = data || {};
  
  try {
    let query = appointmentsCollection.where({
      _openid: openid,
      isDeleted: _.or(_.eq(false), _.exists(false)) // 不显示已标记为删除的记录
    });
    
    // 如果指定了状态，则按状态筛选
    if (status) {
      query = query.where({
        status: status
      });
    }
    
    // 按创建时间倒序排列
    const appointments = await query.orderBy('createTime', 'desc').get();
    
    // 确保已完成订单显示核销时间和服务人员名称
    const appointmentsData = appointments.data.map(appointment => {
      // 确保核销时间和服务人员名称正确显示
      if (appointment.status === 'completed') {
        // 如果有核销记录但没有正确格式的核销时间，使用北京时间格式化
        if (appointment.verified && !appointment.verifyTime) {
          const bjTime = getBJTime();
          appointment.verifyTime = formatDateTime(bjTime);
        }
        
        // 确保服务人员名称存在
        if (!appointment.verifyStaffName && appointment.verifyStaffId) {
          appointment.verifyStaffName = '服务人员';
        }
      }
      return appointment;
    });
    
    return {
      code: 0,
      data: appointmentsData,
      message: '获取预约列表成功'
    };
  } catch (err) {
    console.error('获取预约列表失败', err);
    return {
      code: -1,
      message: '获取预约列表失败: ' + err.message
    };
  }
}

// 取消预约
async function cancelAppointment(openid, data) {
  const { appointmentId } = data;
  
  if (!appointmentId) {
    return {
      code: 400,
      message: '缺少预约ID'
    };
  }
  
  try {
    // 检查预约是否存在且属于该用户
    const appointment = await appointmentsCollection.doc(appointmentId).get();
    
    if (!appointment.data) {
      return {
        code: 404,
        message: '未找到预约信息'
      };
    }
    
    if (appointment.data._openid !== openid) {
      return {
        code: 403,
        message: '无权操作此预约'
      };
    }
    
    // 检查预约状态是否允许取消
    if (['completed', 'cancelled', 'rejected'].includes(appointment.data.status)) {
      return {
        code: 400,
        message: `预约已${appointment.data.status === 'completed' ? '完成' : appointment.data.status === 'cancelled' ? '取消' : '拒绝'}，无法取消`
      };
    }
    
    // 更新预约状态为已取消
    await appointmentsCollection.doc(appointmentId).update({
      data: {
        status: 'cancelled',
        updateTime: db.serverDate()
      }
    });
    
    return {
      code: 0,
      message: '预约取消成功'
    };
  } catch (err) {
    console.error('取消预约失败', err);
    return {
      code: -1,
      message: '取消预约失败: ' + err.message
    };
  }
}

// 检查是否有相同时间段的预约
async function checkExistingAppointments(openid, data) {
  const { date, time, type } = data;

  // 验证必要参数
  if ((type !== 'frontend' && type !== 'admin' && type !== 'staff') || !openid) {
    return {
      code: 400,
      message: '缺少必要参数'
    };
  }

  try {
    // 检查用户是否有任何未完成的预约（不限于特定时间段）
    const existingAppointments = await appointmentsCollection.where({
      _openid: openid,
      status: _.in(['pending', 'confirmed']) // 只检查待确认和已确认的预约
    }).get();

    console.log(`检查用户 ${openid} 是否有未完成预约，结果:`, existingAppointments.data);

    return {
      code: 0,
      hasExisting: existingAppointments.data && existingAppointments.data.length > 0,
      message: '检查成功'
    };
  } catch (err) {
    console.error('检查预约冲突失败', err);
    return {
      code: -1,
      message: '检查预约冲突失败: ' + err.message
    };
  }
}

// 清理用户预约状态的辅助函数（供核销完成后调用）
async function clearUserAppointmentStatus(openid) {
  try {
    console.log(`尝试向客户端发送核销完成事件，清除用户预约状态标记, openid: ${openid}`);
    
    // 确保集合存在
    const collectionName = 'user_status_updates';
    try {
      // 检查集合是否存在（简单方法：尝试获取一条数据）
      await db.collection(collectionName).limit(1).get();
      console.log(`集合 ${collectionName} 已存在`);
    } catch (err) {
      if (err.errCode === -502005) { // 集合不存在错误码
        console.log(`集合 ${collectionName} 不存在，创建中...`);
        // 创建集合的方式是尝试添加一条记录
        try {
          // 这里将直接添加正式记录，不需要临时记录
          await db.createCollection(collectionName);
          console.log(`集合 ${collectionName} 创建成功`);
        } catch (createErr) {
          // 如果是因为集合已存在而失败，忽略错误
          if (createErr.errCode !== -502001) { // 非"集合已存在"错误
            console.error(`创建集合失败:`, createErr);
            // 继续执行，因为添加记录时如果集合不存在会自动创建
          }
        }
      } else {
        console.error(`检查集合存在时出错:`, err);
        // 继续执行，因为添加记录时如果集合不存在会自动创建
      }
    }
    
    // 添加用户状态更新记录
    await db.collection(collectionName).add({
      data: {
        openid: openid,
        type: 'clear_appointment_status',
        handled: false,
        createTime: db.serverDate()
      }
    });
    
    console.log(`成功添加用户状态更新记录，用户: ${openid}`);
    return true;
  } catch (err) {
    console.error('记录用户状态更新失败:', err);
    return false;
  }
}

// ==================== 后端API实现 ====================

// 获取预约列表
async function getAppointmentList(data) {
  const { date, status, page = 1, pageSize = 10, staffId, includeImageUrls = false, dateFilterType = '' } = data || {};
  
  try {
    let query = appointmentsCollection;
    
    // 按日期筛选
    if (date) {
      query = query.where({
        date: date
      });
    } 
    // 根据日期类型筛选
    else if (dateFilterType) {
      const today = new Date();
      const year = today.getFullYear();
      const month = String(today.getMonth() + 1).padStart(2, '0');
      const day = String(today.getDate()).padStart(2, '0');
      
      if (dateFilterType === 'today') {
        // 今日 - 使用具体日期
        const todayStr = `${year}-${month}-${day}`;
        query = query.where({
          date: todayStr
        });
      } 
      else if (dateFilterType === 'week') {
        // 本周 - 计算本周的起始日期（周一）和结束日期（周日）
        const dayOfWeek = today.getDay() || 7; // 获取星期几，星期天时转为7
        const mondayOffset = dayOfWeek - 1; // 计算与周一的天数差
        
        // 设置日期为本周的星期一
        const startDate = new Date(today);
        startDate.setDate(today.getDate() - mondayOffset);
        
        // 设置日期为本周的星期日
        const endDate = new Date(startDate);
        endDate.setDate(startDate.getDate() + 6);
        
        // 格式化日期
        const startDateStr = `${startDate.getFullYear()}-${String(startDate.getMonth() + 1).padStart(2, '0')}-${String(startDate.getDate()).padStart(2, '0')}`;
        const endDateStr = `${endDate.getFullYear()}-${String(endDate.getMonth() + 1).padStart(2, '0')}-${String(endDate.getDate()).padStart(2, '0')}`;
        
        query = query.where({
          date: _.gte(startDateStr).and(_.lte(endDateStr))
        });
      } 
      else if (dateFilterType === 'month') {
        // 本月 - 使用月份开始和结束日期
        const startDateStr = `${year}-${month}-01`;
        
        // 计算下个月的第一天作为结束日期
        let nextMonth, nextYear;
        if (month === '12') {
          nextMonth = '01';
          nextYear = String(year + 1);
        } else {
          nextMonth = String(Number(month) + 1).padStart(2, '0');
          nextYear = String(year);
        }
        
        // 本月最后一天（下个月第一天的前一天）
        const nextMonthFirstDay = new Date(`${nextYear}-${nextMonth}-01`);
        const lastDayOfMonth = new Date(nextMonthFirstDay);
        lastDayOfMonth.setDate(lastDayOfMonth.getDate() - 1);
        
        const endDateStr = `${lastDayOfMonth.getFullYear()}-${String(lastDayOfMonth.getMonth() + 1).padStart(2, '0')}-${String(lastDayOfMonth.getDate()).padStart(2, '0')}`;
        
        query = query.where({
          date: _.gte(startDateStr).and(_.lte(endDateStr))
        });
      }
    }
    
    // 按状态筛选
    if (status) {
      query = query.where({
        status: status
      });
    }
    
    // 按员工ID筛选 - 仅根据核销员工筛选
    if (staffId) {
      query = query.where({
        verifyStaffId: staffId // 严格筛选核销员工，不考虑被指定员工
      });
    }
    
    // 计算总数
    const countResult = await query.count();
    
    // 分页查询
    const skip = (page - 1) * pageSize;
    const appointments = await query
      .orderBy('createTime', 'desc')
      .skip(skip)
      .limit(pageSize)
      .get();
    
    // 获取用户信息
    const userOpenids = [...new Set(appointments.data.map(item => item._openid))];
    const userInfos = {};
    
    if (userOpenids.length > 0) {
      // 修改查询条件，使用openid字段而不是_openid
      const userResults = await db.collection('users').where({
        openid: _.in(userOpenids)
      }).get();
      
      userResults.data.forEach(user => {
        // 使用openid作为键，而不是_openid
        userInfos[user.openid] = user;
      });
      
      // 如果没有找到足够的用户信息，尝试使用_openid查询（兼容旧数据）
      if (userResults.data.length < userOpenids.length) {
        console.log('部分用户信息未找到，尝试使用_openid查询');
        const backupUserResults = await db.collection('users').where({
          _openid: _.in(userOpenids)
        }).get();
        
        backupUserResults.data.forEach(user => {
          // 如果用户有_openid字段，使用它作为键
          if (user._openid) {
            userInfos[user._openid] = user;
          }
        });
      }
    }
    
    // 获取员工信息（如果有员工ID）
    let staffInfos = {};
    
    if (appointments.data.some(item => item.staffId)) {
      const staffIds = [...new Set(appointments.data
        .filter(item => item.staffId)
        .map(item => item.staffId))];
      
      if (staffIds.length > 0) {
        try {
          const staffResults = await db.collection('staff').where({
            _id: _.in(staffIds)
          }).get();
          
          staffResults.data.forEach(staff => {
            staffInfos[staff._id] = staff;
          });
        } catch (staffErr) {
          console.error('获取员工信息失败', staffErr);
        }
      }
    }
    
    // 处理图片URL
    let appointmentsWithImages = appointments.data;
    if (includeImageUrls) {
      // 收集所有需要获取临时URL的图片fileID
      const allImageFileIDs = [];
      appointments.data.forEach(appointment => {
        if (appointment.imageFileIDs && appointment.imageFileIDs.length > 0) {
          allImageFileIDs.push(...appointment.imageFileIDs);
        }
      });
      
      // 获取临时URL
      let imageUrlMap = {};
      if (allImageFileIDs.length > 0) {
        try {
          const result = await cloud.getTempFileURL({
            fileList: allImageFileIDs
          });
          
          if (result.fileList) {
            result.fileList.forEach(file => {
              if (file.status === 0) {
                imageUrlMap[file.fileID] = file.tempFileURL;
              }
            });
          }
        } catch (err) {
          console.error('获取图片临时URL失败', err);
        }
      }
      
      // 将临时URL添加到预约记录中
      appointmentsWithImages = appointments.data.map(appointment => {
        if (appointment.imageFileIDs && appointment.imageFileIDs.length > 0) {
          const imageUrls = appointment.imageFileIDs
            .map(fileID => imageUrlMap[fileID])
            .filter(url => url); // 过滤掉undefined
          
          if (imageUrls.length > 0) {
            return {
              ...appointment,
              imageUrls: imageUrls
            };
          }
        }
        return appointment;
      });
    }
    
    // 组装返回数据
    const appointmentsWithUserInfo = appointmentsWithImages.map(appointment => {
      // 先尝试使用openid查找用户信息，如果没有则使用_openid
      let userInfo = userInfos[appointment._openid] || {};
      
      // 确保用户信息字段存在
      if (!userInfo.nickName && !userInfo.avatarUrl) {
        // 尝试从userInfo嵌套对象中获取
        if (userInfo.userInfo) {
          userInfo = {
            ...userInfo,
            nickName: userInfo.userInfo.nickName,
            avatarUrl: userInfo.userInfo.avatarUrl
          };
        }
      }
      
      const staff = appointment.staffId ? staffInfos[appointment.staffId] || {} : null;
      
      return {
        ...appointment,
        userInfo: {
          nickName: userInfo.nickName || '未知用户',
          avatarUrl: userInfo.avatarUrl || '',
          // 可以添加更多用户信息字段
        },
        staffInfo: staff ? {
          name: staff.name || '未知员工',
          phoneNumber: staff.phoneNumber || '',
          commissionRate: staff.commissionRate || 0
        } : null
      };
    });
    
    return {
      code: 0,
      data: {
        list: appointmentsWithUserInfo,
        total: countResult.total,
        page: page,
        pageSize: pageSize,
        pageCount: Math.ceil(countResult.total / pageSize)
      },
      message: '获取预约列表成功'
    };
  } catch (err) {
    console.error('获取预约列表失败', err);
    return {
      code: -1,
      message: '获取预约列表失败: ' + err.message
    };
  }
}

// 确认预约
async function confirmAppointment(data) {
  const { appointmentId } = data;
  
  if (!appointmentId) {
    return {
      code: 400,
      message: '缺少预约ID'
    };
  }
  
  try {
    // 检查预约是否存在
    const appointment = await appointmentsCollection.doc(appointmentId).get();
    
    if (!appointment.data) {
      return {
        code: 404,
        message: '未找到预约信息'
      };
    }
    
    // 检查预约状态是否允许确认
    if (appointment.data.status !== 'pending') {
      return {
        code: 400,
        message: `预约状态为${appointment.data.status}，无法确认`
      };
    }
    
    // 更新预约状态为已确认
    await appointmentsCollection.doc(appointmentId).update({
      data: {
        status: 'confirmed',
        updateTime: db.serverDate()
      }
    });
    
    return {
      code: 0,
      message: '预约确认成功'
    };
  } catch (err) {
    console.error('确认预约失败', err);
    return {
      code: -1,
      message: '确认预约失败: ' + err.message
    };
  }
}

// 完成预约
async function completeAppointment(data) {
  const { appointmentId } = data;
  
  if (!appointmentId) {
    return {
      code: 400,
      message: '缺少预约ID'
    };
  }
  
  try {
    // 检查预约是否存在
    const appointment = await appointmentsCollection.doc(appointmentId).get();
    
    if (!appointment.data) {
      return {
        code: 404,
        message: '未找到预约信息'
      };
    }
    
    // 检查预约状态是否允许标记为完成
    if (!['confirmed', 'pending'].includes(appointment.data.status)) {
      return {
        code: 400,
        message: `预约状态为${appointment.data.status}，无法标记为完成`
      };
    }
    
    // 更新预约状态为已完成
    await appointmentsCollection.doc(appointmentId).update({
      data: {
        status: 'completed',
        updateTime: db.serverDate()
      }
    });
    
    return {
      code: 0,
      message: '预约已标记为完成'
    };
  } catch (err) {
    console.error('完成预约失败', err);
    return {
      code: -1,
      message: '完成预约失败: ' + err.message
    };
  }
}

// 拒绝预约
async function rejectAppointment(data) {
  const { appointmentId, reason } = data;
  
  if (!appointmentId) {
    return {
      code: 400,
      message: '缺少预约ID'
    };
  }
  
  try {
    // 检查预约是否存在
    const appointment = await appointmentsCollection.doc(appointmentId).get();
    
    if (!appointment.data) {
      return {
        code: 404,
        message: '未找到预约信息'
      };
    }
    
    // 检查预约状态是否允许拒绝
    if (!['pending'].includes(appointment.data.status)) {
      return {
        code: 400,
        message: `预约状态为${appointment.data.status}，无法拒绝`
      };
    }
    
    // 更新预约状态为已拒绝
    await appointmentsCollection.doc(appointmentId).update({
      data: {
        status: 'rejected',
        rejectReason: reason || '',
        updateTime: db.serverDate()
      }
    });
    
    return {
      code: 0,
      message: '预约已拒绝'
    };
  } catch (err) {
    console.error('拒绝预约失败', err);
    return {
      code: -1,
      message: '拒绝预约失败: ' + err.message
    };
  }
}

// 更新预约
async function updateAppointment(event, context) {
  const { OPENID } = cloud.getWXContext();
  const { appointmentData } = event;
  
  if (!appointmentData || !appointmentData.appointmentId) {
    return { code: -1, message: '预约数据不完整' };
  }
  
  try {
    // 先检查预约是否存在且属于当前用户
    const appointmentRecord = await db.collection('appointments').doc(appointmentData.appointmentId).get();
    
    if (!appointmentRecord.data) {
      return { code: -1, message: '预约不存在' };
    }
    
    if (appointmentRecord.data._openid !== OPENID) {
      return { code: -1, message: '无权修改此预约' };
    }
    
    // 检查预约状态，只有待确认的预约才能修改
    if (appointmentRecord.data.status !== 'pending') {
      return { code: -1, message: '只能修改待确认状态的预约' };
    }
    
    // 准备更新数据
    const updateData = {
      date: appointmentData.date,
      time: appointmentData.time,
      phoneNumber: appointmentData.phoneNumber,
      updateTime: db.serverDate()
    };
    
    // 如果提供了员工信息，则更新员工选择相关字段
    if (appointmentData.preferredStaffId && appointmentData.preferredStaffName) {
      updateData.assignment_type = 'specified'; // 指定员工
      updateData.preferredStaffId = appointmentData.preferredStaffId;
      updateData.preferredStaffName = appointmentData.preferredStaffName;
    } else if (appointmentData.staffId && appointmentData.staffName) {
      // 兼容旧参数名
      updateData.assignment_type = 'specified'; // 指定员工
      updateData.preferredStaffId = appointmentData.staffId;
      updateData.preferredStaffName = appointmentData.staffName;
    } else if (appointmentData.assignment_type === 'random') {
      // 如果明确指定为随机分配，则清除员工选择
      updateData.assignment_type = 'random';
      updateData.preferredStaffId = '';
      updateData.preferredStaffName = '';
    }
    
    // 更新预约信息
    await db.collection('appointments').doc(appointmentData.appointmentId).update({
      data: updateData
    });
    
    return { code: 0, message: '预约更新成功' };
  } catch (error) {
    console.error('更新预约失败：', error);
    return { code: -1, message: '更新预约失败：' + error.message };
  }
}

// 添加服务
async function addService(data) {
  console.log('addService函数被调用，参数：', data);
  
  try {
    // 检查必要参数
    if (!data.name || data.price === undefined) {
      console.log('添加服务失败：缺少必要参数');
      return {
        success: false,
        message: '缺少必要参数'
      };
    }
    
    const currentTime = Date.now();
    
    // 构建服务数据
    const serviceData = {
      id: `service_${currentTime}`, // 生成唯一ID
      name: data.name,
      price: Number(data.price),
      originalPrice: data.originalPrice ? Number(data.originalPrice) : null, // 添加原价字段
      description: data.description || '',
      image: data.image || '',
      detailImage: data.detailImage || '', // 添加详情图片字段
      createTime: db.serverDate(),
      updateTime: db.serverDate(),
      isVisible: true // 默认为可见
    };
    
    console.log('准备添加服务数据：', serviceData);
    
    // 添加到services集合
    const result = await servicesCollection.add({
      data: serviceData
    });
    
    console.log('服务添加结果：', result);
    
    return {
      success: true,
      data: serviceData
    };
  } catch (err) {
    console.error('添加服务出错：', err);
    return {
      success: false,
      message: '添加服务失败',
      error: err.message
    };
  }
}

// 更新服务
async function updateService(data) {
  const { serviceId, name, price, originalPrice, description, duration, image, detailImage } = data;
  
  if (!serviceId) {
    return {
      success: false,
      message: '缺少服务ID'
    };
  }
  
  try {
    // 检查服务是否存在
    const service = await servicesCollection.where({
      id: serviceId
    }).get();
    
    if (!service.data || service.data.length === 0) {
      return {
        success: false,
        message: '未找到服务信息'
      };
    }
    
    // 准备更新数据
    const updateData = {
      updateTime: db.serverDate()
    };
    
    if (name) updateData.name = name;
    if (price !== undefined) updateData.price = Number(price);
    if (originalPrice !== undefined) updateData.originalPrice = originalPrice ? Number(originalPrice) : null; // 添加原价字段
    if (description !== undefined) updateData.description = description;
    if (duration !== undefined) updateData.duration = Number(duration);
    if (image !== undefined) updateData.image = image;
    if (detailImage !== undefined) updateData.detailImage = detailImage; // 添加详情图片字段
    
    // 更新服务信息
    await servicesCollection.where({
      id: serviceId
    }).update({
      data: updateData
    });
    
    return {
      success: true,
      message: '服务更新成功'
    };
  } catch (err) {
    console.error('更新服务失败', err);
    return {
      success: false,
      message: '更新服务失败: ' + err.message
    };
  }
}

// 删除服务
async function deleteService(data) {
  const { serviceId } = data;
  
  if (!serviceId) {
    return {
      success: false,
      message: '缺少服务ID'
    };
  }
  
  try {
    // 检查服务是否存在
    const service = await servicesCollection.where({
      id: serviceId
    }).get();
    
    if (!service.data || service.data.length === 0) {
      return {
        success: false,
        message: '未找到服务信息'
      };
    }
    
    // 检查是否有关联的预约
    const appointments = await appointmentsCollection.where({
      serviceId: serviceId,
      status: _.in(['pending', 'confirmed']) // 只检查待确认和已确认的预约
    }).count();
    
    if (appointments.total > 0) {
      return {
        success: false,
        message: '该服务有关联的预约，无法删除'
      };
    }
    
    // 删除服务
    await servicesCollection.where({
      id: serviceId
    }).remove();
    
    return {
      success: true,
      message: '服务删除成功'
    };
  } catch (err) {
    console.error('删除服务失败', err);
    return {
      success: false,
      message: '删除服务失败: ' + err.message
    };
  }
}

// 获取预约统计数据
async function getAppointmentStats(data) {
  const { startDate, endDate, staffId } = data || {};
  
  if (!startDate || !endDate) {
    return {
      code: 400,
      message: '缺少日期范围参数'
    };
  }
  
  try {
    // 构建查询条件
    let query = {
      date: _.gte(startDate).and(_.lte(endDate))
    };
    
    // 如果指定了员工ID，则进一步筛选
    if (staffId) {
      query.staffId = staffId;
    }
    
    // 查询符合条件的所有预约
    const appointments = await appointmentsCollection.where(query).get();
    
    if (!appointments.data) {
      return {
        code: 0,
        data: {
          total: 0,
          statusStats: {},
          serviceStats: [],
          dateStats: [],
          income: {
            total: 0,
            completed: 0
          }
        },
        message: '获取统计数据成功'
      };
    }
    
    // 总预约数
    const total = appointments.data.length;
    
    // 状态统计
    const statusStats = {
      pending: 0,
      confirmed: 0,
      completed: 0,
      cancelled: 0,
      rejected: 0
    };
    
    // 收入统计
    const income = {
      total: 0,      // 所有预约的总价值
      completed: 0    // 已完成预约的总价值
    };
    
    // 服务统计
    const serviceMap = {};
    
    // 日期统计
    const dateMap = {};
    
    // 员工统计
    const staffMap = {};
    
    appointments.data.forEach(appointment => {
      // 状态统计
      if (statusStats.hasOwnProperty(appointment.status)) {
        statusStats[appointment.status]++;
      }
      
      // 收入统计
      const price = parseFloat(appointment.servicePrice) || 0;
      income.total += price;
      if (appointment.status === 'completed') {
        income.completed += price;
      }
      
      // 服务统计
      const serviceId = appointment.serviceId;
      if (serviceId) {
        if (!serviceMap[serviceId]) {
          serviceMap[serviceId] = {
            serviceId,
            name: appointment.serviceName || '未知服务',
            count: 0,
            income: 0
          };
        }
        serviceMap[serviceId].count++;
        serviceMap[serviceId].income += price;
      }
      
      // 日期统计
      const date = appointment.date;
      if (date) {
        if (!dateMap[date]) {
          dateMap[date] = {
            date,
            count: 0,
            income: 0
          };
        }
        dateMap[date].count++;
        dateMap[date].income += price;
      }
      
      // 员工统计
      if (appointment.staffId) {
        if (!staffMap[appointment.staffId]) {
          staffMap[appointment.staffId] = {
            staffId: appointment.staffId,
            name: appointment.staffName || '未知员工',
            count: 0,
            income: 0,
            commission: 0
          };
        }
        staffMap[appointment.staffId].count++;
        staffMap[appointment.staffId].income += price;
        staffMap[appointment.staffId].commission += (appointment.commission || 0);
      }
    });
    
    // 转换为数组并排序
    const serviceStats = Object.values(serviceMap).sort((a, b) => b.count - a.count);
    const dateStats = Object.values(dateMap).sort((a, b) => a.date.localeCompare(b.date));
    const staffStats = Object.values(staffMap).sort((a, b) => b.income - a.income);
    
    return {
      code: 0,
      data: {
        total,
        statusStats,
        serviceStats,
        dateStats,
        staffStats,
        income,
        dateRange: {
          startDate,
          endDate
        }
      },
      message: '获取统计数据成功'
    };
  } catch (err) {
    console.error('获取统计数据失败', err);
    return {
      code: -1,
      message: '获取统计数据失败: ' + err.message
    };
  }
}

// 添加管理员权限
async function addAdminPermission(openid) {
  try {
    // 检查system_users集合是否存在
    try {
      await db.createCollection('system_users');
      console.log('创建system_users集合成功');
    } catch (err) {
      // 如果集合已存在，这是正常的
      console.log('system_users集合已存在或创建失败:', err.message);
    }
    
    // 检查用户是否已经是管理员
    const adminCheck = await db.collection('system_users').where({
      _openid: openid,
      role: 'admin'
    }).get();
    
    if (adminCheck.data && adminCheck.data.length > 0) {
      return {
        success: true,
        message: '您已经是管理员'
      };
    }
    
    // 添加管理员记录
    await db.collection('system_users').add({
      data: {
        _openid: openid,
        role: 'admin',
        createTime: db.serverDate()
      }
    });
    
    return {
      success: true,
      message: '已成功添加为管理员'
    };
  } catch (err) {
    console.error('添加管理员权限失败:', err);
    return {
      success: false,
      message: '添加管理员权限失败: ' + err.message
    };
  }
}

// 核销预约
async function verifyAppointment(data) {
  const { verifyCode, appointmentId, staffId, staffName, forceCash } = data;
  
  console.log('核销预约，参数：', { verifyCode, appointmentId, staffId, staffName, forceCash });
  
  if ((!verifyCode && !appointmentId) || !staffId || !staffName) {
    return {
      code: 400,
      message: '缺少必要参数'
    };
  }
  
  try {
    let appointmentData;
    
    // 根据核销码或预约ID查找预约
    if (verifyCode) {
      // 通过核销码查找
      const appointment = await appointmentsCollection.where({
        verifyCode: verifyCode,
        verified: false
      }).get();
      
      if (!appointment.data || appointment.data.length === 0) {
        return {
          code: 404,
          message: '预约不存在或已核销'
        };
      }
      
      appointmentData = appointment.data[0];
    } else if (appointmentId) {
      // 通过预约ID查找
      try {
        const appointment = await appointmentsCollection.doc(appointmentId).get();
        if (!appointment.data) {
          return {
            code: 404,
            message: '预约不存在'
          };
        }
        
        if (appointment.data.verified) {
          return {
            code: 400,
            message: '该预约已被核销'
          };
        }
        
        appointmentData = appointment.data;
      } catch (err) {
        return {
          code: 404,
          message: '预约不存在'
        };
      }
    }
    
    console.log('找到预约记录:', appointmentData);
    
    // 检查用户余额，决定支付方式
    let paymentMethod = 'cash'; // 默认为现金支付
    let userBalance = 0;
    let userBonusBalance = 0;
    let userName = '';
    let userId = '';
    
    // 获取用户信息和余额
    if (appointmentData._openid) {
      try {
        console.log('开始查询用户信息，openid:', appointmentData._openid);
        
        const userResult = await db.collection('users').where({
          openid: appointmentData._openid
        }).get();
        
        console.log('用户查询结果:', userResult);
        
        if (userResult.data && userResult.data.length > 0) {
          const user = userResult.data[0];
          userBalance = user.balance || 0;
          userBonusBalance = user.bonusBalance || 0;
          userName = user.nickName || '用户';
          userId = user._id;
          const totalBalance = userBalance + userBonusBalance;
          
          console.log('用户余额信息:', {
            balance: userBalance,
            bonusBalance: userBonusBalance,
            totalBalance: totalBalance,
            servicePrice: appointmentData.servicePrice,
            isSufficient: totalBalance >= appointmentData.servicePrice,
            forceCash: forceCash
          });
          
          // 如果余额不足且没有强制使用现金支付
          if (totalBalance < appointmentData.servicePrice && forceCash !== true) {
            console.log('用户余额不足，需要确认支付方式，返回特殊状态码201');
            return {
              code: 201, // 使用特殊状态码表示余额不足
              message: '用户余额不足',
              data: {
                appointmentId: appointmentData._id,
                serviceName: appointmentData.serviceName,
                servicePrice: appointmentData.servicePrice,
                userBalance: totalBalance,
                shortage: appointmentData.servicePrice - totalBalance,
                userName: userName
              }
            };
          }
          
          // 如果余额足够支付服务费用且没有强制使用现金支付
          if (totalBalance >= appointmentData.servicePrice && forceCash !== true) {
            paymentMethod = 'balance';
            
            // 计算扣除规则（优先使用赠送金额）
            let deductBonus = Math.min(userBonusBalance, appointmentData.servicePrice);
            let deductBalance = appointmentData.servicePrice - deductBonus;
            
            console.log('使用余额支付:', {
              deductBonus: deductBonus,
              deductBalance: deductBalance,
              totalDeduct: deductBonus + deductBalance
            });
            
            // 更新用户余额
            await db.collection('users').doc(user._id).update({
              data: {
                balance: userBalance - deductBalance,
                bonusBalance: userBonusBalance - deductBonus,
                updateTime: db.serverDate()
              }
            });
            
            // 记录余额消费记录
            await db.collection('balance_consumption').add({
              data: {
                openid: appointmentData._openid,
                amount: appointmentData.servicePrice,
                deductBalance: deductBalance,
                deductBonus: deductBonus,
                operatorId: staffId,
                type: 'consumption',
                remark: `核销服务: ${appointmentData.serviceName}`,
                createTime: db.serverDate()
              }
            });
            
            console.log('余额支付成功，已更新用户余额并记录消费记录');
          } else {
            console.log('用户余额不足或强制现金支付，使用现金支付');
            paymentMethod = 'cash';
          }
        } else {
          console.log('未找到用户信息，使用现金支付');
        }
      } catch (err) {
        console.error('获取用户余额失败，使用现金支付:', err);
      }
    }
    
    // 获取员工信息，获取提成比例
    let staffCommissionRate = 0.3; // 默认值，如果查询失败则使用这个
    try {
      const staffInfo = await db.collection('staff').doc(staffId).get();
      if (staffInfo.data) {
        if (paymentMethod === 'balance' && staffInfo.data.balanceCommissionRate !== undefined) {
          // 使用余额支付提成比例
          staffCommissionRate = staffInfo.data.balanceCommissionRate;
          console.log(`使用余额支付提成比例: ${staffCommissionRate * 100}%`);
        } else if (staffInfo.data.commissionRate !== undefined) {
          // 使用现金支付提成比例
          staffCommissionRate = staffInfo.data.commissionRate;
          console.log(`使用现金支付提成比例: ${staffCommissionRate * 100}%`);
        } else {
          console.log(`未找到员工 ${staffName}(${staffId}) 的提成比例，使用默认值: ${staffCommissionRate * 100}%`);
        }
      }
    } catch (err) {
      console.error(`获取员工 ${staffId} 信息失败，使用默认提成比例:`, err);
    }
    
    // 更新预约状态 - 使用北京时间（UTC+8）
    const beijingTime = getBJTime();
    const verifyTimeStr = formatDateTime(beijingTime);
    
    console.log('核销时间（北京时间）:', verifyTimeStr);
    
    const updateData = {
      verified: true,
      verifyTime: verifyTimeStr,
      verifyStaffId: staffId,
      verifyStaffName: staffName,
      status: 'completed', // 核销后直接标记为已完成
      updateTime: db.serverDate(),
      paymentMethod: paymentMethod // 添加支付方式字段
    };
    
    console.log('更新预约状态:', updateData);
    
    await appointmentsCollection.doc(appointmentData._id).update({
      data: updateData
    });
    
    // 尝试向用户发送核销成功消息并清除用户的预约状态标记
    try {
      // 清除用户预约状态标记
      await clearUserAppointmentStatus(appointmentData._openid);
      
      // 在数据库中记录一条消息
      await db.collection('user_messages').add({
        data: {
          openid: appointmentData._openid,
          type: 'appointment_completed',
          title: '预约服务已完成',
          content: `您预约的"${appointmentData.serviceName}"服务已由${staffName}完成核销，感谢您的信任！`,
          isRead: false,
          createTime: db.serverDate()
        }
      });
      
      console.log('已添加核销成功消息到用户消息中心并清除预约状态标记');
    } catch (err) {
      console.error('发送核销消息或清除状态失败:', err);
      // 这个错误不影响核销结果，所以只记录日志
    }
    
    // 计算提成金额
    const commissionAmount = appointmentData.servicePrice * staffCommissionRate;
    
    // 记录业绩
    const performanceData = {
      staffId: staffId,
      staffName: staffName,
      appointmentId: appointmentData._id,
      serviceId: appointmentData.serviceId,
      serviceName: appointmentData.serviceName,
      servicePrice: appointmentData.servicePrice,
      commission: commissionAmount, // 使用实际的提成比例计算
      commissionRate: staffCommissionRate, // 记录使用的提成比例
      completeTime: verifyTimeStr, // 使用与核销时间相同的北京时间字符串
      customerPhone: appointmentData.phoneNumber,
      paymentMethod: paymentMethod // 记录支付方式
    };
    
    console.log('记录业绩数据:', performanceData);
    
    await db.collection('staff_performance').add({
      data: performanceData
    });
    
    // 核销成功后，自动将员工状态改为空闲
    try {
      await db.collection('staff').doc(staffId).update({
        data: {
          serviceStatus: 'available', // 自动将状态改为空闲
          updateTime: db.serverDate()
        }
      });
      console.log('已自动将员工状态更新为空闲');
    } catch (err) {
      console.error('自动更新员工状态失败:', err);
      // 不影响核销结果，只记录错误
    }
    
    console.log('核销成功');
    
    return {
      code: 0,
      data: {
        ...appointmentData,
        commission: commissionAmount,
        commissionRate: staffCommissionRate,
        paymentMethod: paymentMethod
      },
      message: '核销成功'
    };
  } catch (err) {
    console.error('核销预约失败', err);
    return {
      code: -1,
      message: '核销预约失败: ' + err.message
    };
  }
}

// 删除预约记录（允许删除已完成、已取消、已拒绝的预约，但不允许删除待确认和已确认的预约）
async function deleteAppointment(event, context) {
  const { OPENID } = cloud.getWXContext();
  const { appointmentId } = event;
  
  console.log('删除预约记录，参数：', { appointmentId, OPENID });
  
  if (!appointmentId) {
    return { code: -1, message: '缺少预约ID' };
  }
  
  try {
    // 先检查预约是否存在且属于当前用户
    const appointmentRecord = await db.collection('appointments').doc(appointmentId).get();
    
    console.log('查询到的预约记录：', appointmentRecord.data);
    
    if (!appointmentRecord.data) {
      return { code: -1, message: '预约不存在' };
    }
    
    if (appointmentRecord.data._openid !== OPENID) {
      console.log('无权删除，记录openid:', appointmentRecord.data._openid, '当前用户openid:', OPENID);
      return { code: -1, message: '无权删除此预约' };
    }
    
    // 检查预约状态，不允许删除待确认和已确认的预约
    if (appointmentRecord.data.status === 'pending' || appointmentRecord.data.status === 'confirmed') {
      console.log('预约状态为待确认或已确认，不允许删除，当前状态:', appointmentRecord.data.status);
      return { code: -1, message: '未完成的预约不能删除，请先取消预约' };
    }
    
    // 不真正删除记录，而是添加isDeleted标记
    await db.collection('appointments').doc(appointmentId).update({
      data: {
        isDeleted: true,
        deleteTime: db.serverDate()
      }
    });
    
    console.log('预约记录已标记为删除');
    
    return { code: 0, message: '删除成功' };
  } catch (error) {
    console.error('删除预约失败：', error);
    return { code: -1, message: '删除失败：' + error.message };
  }
}

// 获取预约详情
async function getAppointmentDetail(data) {
  const { appointmentId } = data;
  
  if (!appointmentId) {
    return {
      code: 400,
      message: '缺少预约ID'
    };
  }
  
  try {
    // 查询预约详情
    const appointment = await appointmentsCollection.doc(appointmentId).get();
    
    if (!appointment.data) {
      return {
        code: 404,
        message: '预约不存在'
      };
    }
    
    // 默认提成比例
    let staffCommissionRate = 0.3;
    
    // 如果预约已核销，查询对应的员工信息和业绩记录
    let performanceData = null;
    if (appointment.data.verified && appointment.data.verifyStaffId) {
      // 获取员工信息，获取提成比例
      try {
        const staffInfo = await db.collection('staff').doc(appointment.data.verifyStaffId).get();
        if (staffInfo.data && staffInfo.data.commissionRate !== undefined) {
          staffCommissionRate = staffInfo.data.commissionRate;
          console.log(`获取到员工 ${staffInfo.data.name}(${appointment.data.verifyStaffId}) 的提成比例: ${staffCommissionRate * 100}%`);
        } else {
          console.log(`未找到员工 ${appointment.data.verifyStaffName}(${appointment.data.verifyStaffId}) 的提成比例，使用默认值: 30%`);
        }
      } catch (err) {
        console.error(`获取员工 ${appointment.data.verifyStaffId} 信息失败，使用默认提成比例:`, err);
      }
      
      // 获取业绩记录
      const performanceResult = await db.collection('staff_performance')
        .where({
          appointmentId: appointmentId,
          staffId: appointment.data.verifyStaffId
        })
        .get();
      
      if (performanceResult.data && performanceResult.data.length > 0) {
        performanceData = performanceResult.data[0];
      }
    }
    
    // 合并预约信息和业绩信息
    const appointmentDetail = {
      ...appointment.data,
      // 如果有业绩记录，使用记录中的提成信息，否则使用员工的实际提成比例
      commission: performanceData?.commission || (appointment.data.servicePrice * staffCommissionRate),
      commissionRate: performanceData?.commissionRate || staffCommissionRate
    };
    
    // 如果有图片，获取临时访问链接
    if (appointmentDetail.imageFileIDs && appointmentDetail.imageFileIDs.length > 0) {
      try {
        const result = await cloud.getTempFileURL({
          fileList: appointmentDetail.imageFileIDs
        });
        
        appointmentDetail.imageUrls = result.fileList.map(file => file.tempFileURL);
        console.log('获取图片临时链接成功:', appointmentDetail.imageUrls);
      } catch (err) {
        console.error('获取图片临时链接失败', err);
        appointmentDetail.imageUrls = [];
      }
    } else {
      appointmentDetail.imageUrls = [];
    }
    
    return {
      code: 0,
      data: appointmentDetail,
      message: '获取预约详情成功'
    };
  } catch (err) {
    console.error('获取预约详情失败', err);
    return {
      code: -1,
      message: '获取预约详情失败: ' + err.message
    };
  }
}

// 切换服务可见性
async function toggleServiceVisibility(data) {
  const { serviceId, visible } = data;
  
  if (!serviceId) {
    return {
      success: false,
      message: '缺少服务ID'
    };
  }
  
  try {
    // 检查服务是否存在
    const service = await servicesCollection.where({
      id: serviceId
    }).get();
    
    if (!service.data || service.data.length === 0) {
      return {
        success: false,
        message: '未找到服务信息'
      };
    }
    
    // 更新可见性状态和更新时间
    const updateData = {
      isVisible: !!visible, // 确保是布尔值
      updateTime: db.serverDate() // 使用服务器时间而不是客户端时间
    };
    
    // 如果是设置为可见，确保它有一个最新的时间戳
    if (visible) {
      // 使用服务器时间加上一个小延迟，确保它是最新的
      updateData.updateTime = db.serverDate({
        offset: 1000 // 增加1秒的偏移量，确保它比其他记录更新
      });
      console.log(`服务${serviceId}设为可见，更新时间为最新`);
    }
    
    // 更新服务信息
    await servicesCollection.where({
      id: serviceId
    }).update({
      data: updateData
    });
    
    return {
      success: true,
      message: visible ? '服务已设为可见' : '服务已隐藏'
    };
  } catch (err) {
    console.error('切换服务可见性失败', err);
    return {
      success: false,
      message: '切换服务可见性失败: ' + err.message
    };
  }
}

// 获取轮播图列表
async function getCarousel(data) {
  try {
    console.log('获取轮播图列表，请求参数:', data);
    
    // 查询轮播图列表，按照排序字段排序
    const carousel = await carouselCollection.orderBy('order', 'asc').get();
    
    console.log('查询到的轮播图数量:', carousel.data.length);
    
    // 如果没有轮播图，自动初始化默认轮播图
    if (carousel.data.length === 0) {
      console.log('轮播图为空，自动初始化');
      await initCarousel();
      const newCarousel = await carouselCollection.orderBy('order', 'asc').get();
      
      // 转换图片fileID为临时URL
      const processedCarousel = await convertFileIDsToURLs(newCarousel.data);
      console.log('轮播图URL转换完成');
      
      return {
        code: 0,
        success: true,
        data: processedCarousel,
        message: '轮播图已初始化'
      };
    }
    
    // 转换图片fileID为临时URL
    const processedCarousel = await convertFileIDsToURLs(carousel.data);
    console.log('轮播图URL转换完成');
    
    return {
      code: 0,
      success: true,
      data: processedCarousel,
      message: '获取轮播图列表成功'
    };
  } catch (err) {
    console.error('获取轮播图列表失败', err);
    return {
      code: -1,
      success: false,
      message: '获取轮播图列表失败: ' + err.message
    };
  }
}

// 添加轮播图
async function addCarousel(data) {
  console.log('addCarousel函数被调用，参数：', data);
  
  try {
    // 检查必要参数
    if (!data.title || !data.imageUrl) {
      console.log('添加轮播图失败：缺少必要参数');
      return {
        success: false,
        message: '缺少必要参数'
      };
    }
    
    // 获取当前轮播图数量，用于设置顺序
    const count = await carouselCollection.count();
    const order = count.total + 1;
    
    // 构建轮播图数据
    const carouselData = {
      id: `carousel_${Date.now()}`, // 生成唯一ID
      title: data.title,
      imageUrl: data.imageUrl,
      order: data.order || order, // 如果指定了顺序，使用指定的顺序，否则放在最后
      createTime: db.serverDate(),
      updateTime: db.serverDate()
    };
    
    console.log('准备添加轮播图数据：', carouselData);
    
    // 添加到carousel集合
    const result = await carouselCollection.add({
      data: carouselData
    });
    
    console.log('轮播图添加结果：', result);
    
    return {
      success: true,
      data: carouselData,
      message: '轮播图添加成功'
    };
  } catch (err) {
    console.error('添加轮播图出错：', err);
    return {
      success: false,
      message: '添加轮播图失败',
      error: err.message
    };
  }
}

// 更新轮播图
async function updateCarousel(data) {
  const { carouselId, title, imageUrl, order } = data;
  
  if (!carouselId) {
    return {
      success: false,
      message: '缺少轮播图ID'
    };
  }
  
  try {
    // 检查轮播图是否存在
    const carousel = await carouselCollection.where({
      id: carouselId
    }).get();
    
    if (!carousel.data || carousel.data.length === 0) {
      return {
        success: false,
        message: '未找到轮播图信息'
      };
    }
    
    // 准备更新数据
    const updateData = {
      updateTime: db.serverDate()
    };
    
    if (title !== undefined) updateData.title = title;
    if (imageUrl !== undefined) updateData.imageUrl = imageUrl;
    if (order !== undefined) updateData.order = Number(order);
    
    // 更新轮播图信息
    await carouselCollection.where({
      id: carouselId
    }).update({
      data: updateData
    });
    
    return {
      success: true,
      message: '轮播图更新成功'
    };
  } catch (err) {
    console.error('更新轮播图失败', err);
    return {
      success: false,
      message: '更新轮播图失败: ' + err.message
    };
  }
}

// 删除轮播图
async function deleteCarousel(data) {
  const { carouselId } = data;
  
  if (!carouselId) {
    return {
      success: false,
      message: '缺少轮播图ID'
    };
  }
  
  try {
    // 检查轮播图是否存在
    const carousel = await carouselCollection.where({
      id: carouselId
    }).get();
    
    if (!carousel.data || carousel.data.length === 0) {
      return {
        success: false,
        message: '未找到轮播图信息'
      };
    }
    
    // 删除轮播图
    await carouselCollection.where({
      id: carouselId
    }).remove();
    
    // 重新排序剩余轮播图
    const remainingCarousels = await carouselCollection.orderBy('order', 'asc').get();
    
    // 更新剩余轮播图的顺序
    for (let i = 0; i < remainingCarousels.data.length; i++) {
      await carouselCollection.where({
        id: remainingCarousels.data[i].id
      }).update({
        data: {
          order: i + 1
        }
      });
    }
    
    return {
      success: true,
      message: '轮播图删除成功'
    };
  } catch (err) {
    console.error('删除轮播图失败', err);
    return {
      success: false,
      message: '删除轮播图失败: ' + err.message
    };
  }
}

// 初始化轮播图
async function initCarousel() {
  try {
    console.log('开始初始化轮播图');
    
    // 检查是否已有轮播图
    const count = await carouselCollection.count();
    
    if (count.total > 0) {
      console.log('轮播图已存在，不需要初始化');
      return {
        success: true,
        message: '轮播图已存在，不需要初始化'
      };
    }
    
    // 创建默认轮播图数据
    const defaultCarousels = [
      {
        id: `carousel_${Date.now()}_1`,
        title: '轮播图1',
        imageUrl: '',
        order: 1,
        createTime: db.serverDate(),
        updateTime: db.serverDate()
      },
      {
        id: `carousel_${Date.now()}_2`,
        title: '轮播图2',
        imageUrl: '',
        order: 2,
        createTime: db.serverDate(),
        updateTime: db.serverDate()
      },
      {
        id: `carousel_${Date.now()}_3`,
        title: '轮播图3',
        imageUrl: '',
        order: 3,
        createTime: db.serverDate(),
        updateTime: db.serverDate()
      }
    ];
    
    // 批量添加默认轮播图
    for (const carousel of defaultCarousels) {
      await carouselCollection.add({
        data: carousel
      });
      console.log(`添加默认轮播图: ${carousel.title}`);
    }
    
    console.log('轮播图初始化完成');
    
    return {
      success: true,
      message: '轮播图初始化成功'
    };
  } catch (err) {
    console.error('初始化轮播图失败', err);
    return {
      success: false,
      message: '初始化轮播图失败: ' + err.message
    };
  }
} 

// 获取分配给员工的预约
async function getAssignedAppointments(data) {
  const { staffId, status, page = 1, pageSize = 10, includeImageUrls = false } = data;
  
  if (!staffId) {
    return {
      code: 400,
      message: '缺少员工ID'
    };
  }
  
  try {
    // 基础查询条件：只查询明确指定给该员工的预约
    let queryCondition = {
      preferredStaffId: staffId // 只查询客户明确指定的员工
    };
    
    // 如果指定了状态，添加状态过滤
    if (status) {
      queryCondition.status = status;
    }
    
    // 创建查询对象
    let query = appointmentsCollection.where(queryCondition);
    
    // 计算总数
    const countResult = await query.count();
    
    // 分页查询
    const skip = (page - 1) * pageSize;
    const appointments = await query
      .orderBy('createTime', 'desc')
      .skip(skip)
      .limit(pageSize)
      .get();
    
    // 处理图片URL
    let appointmentsWithImages = appointments.data;
    if (includeImageUrls) {
      // 收集所有需要获取临时URL的图片fileID
      const allImageFileIDs = [];
      appointments.data.forEach(appointment => {
        if (appointment.imageFileIDs && appointment.imageFileIDs.length > 0) {
          allImageFileIDs.push(...appointment.imageFileIDs);
        }
      });
      
      // 获取临时URL
      let imageUrlMap = {};
      if (allImageFileIDs.length > 0) {
        try {
          const result = await cloud.getTempFileURL({
            fileList: allImageFileIDs
          });
          
          if (result.fileList) {
            result.fileList.forEach(file => {
              if (file.status === 0) {
                imageUrlMap[file.fileID] = file.tempFileURL;
              }
            });
          }
        } catch (err) {
          console.error('获取图片临时URL失败', err);
        }
      }
      
      // 将临时URL添加到预约记录中
      appointmentsWithImages = appointments.data.map(appointment => {
        if (appointment.imageFileIDs && appointment.imageFileIDs.length > 0) {
          const imageUrls = appointment.imageFileIDs
            .map(fileID => imageUrlMap[fileID])
            .filter(url => url); // 过滤掉undefined
          
          if (imageUrls.length > 0) {
            return {
              ...appointment,
              imageUrls: imageUrls
            };
          }
        }
        return appointment;
      });
    }
    
    // 获取用户信息
    const userOpenids = [...new Set(appointments.data.map(item => item._openid))];
    const userInfos = {};
    
    if (userOpenids.length > 0) {
      // 尝试使用openid查询
      const userResults = await db.collection('users').where({
        openid: _.in(userOpenids)
      }).get();
      
      userResults.data.forEach(user => {
        userInfos[user.openid] = user;
      });
      
      // 如果没有找到足够的用户信息，尝试使用_openid查询
      if (userResults.data.length < userOpenids.length) {
        const backupUserResults = await db.collection('users').where({
          _openid: _.in(userOpenids)
        }).get();
        
        backupUserResults.data.forEach(user => {
          if (user._openid) {
            userInfos[user._openid] = user;
          }
        });
      }
    }
    
    // 组装返回数据
    const appointmentsWithUserInfo = appointmentsWithImages.map(appointment => {
      // 先尝试使用openid查找用户信息，如果没有则使用_openid
      let userInfo = userInfos[appointment._openid] || {};
      
      // 确保用户信息字段存在
      if (!userInfo.nickName && !userInfo.avatarUrl) {
        // 尝试从userInfo嵌套对象中获取
        if (userInfo.userInfo) {
          userInfo = {
            ...userInfo,
            nickName: userInfo.userInfo.nickName,
            avatarUrl: userInfo.userInfo.avatarUrl
          };
        }
      }
      
      return {
        ...appointment,
        userInfo: {
          nickName: userInfo.nickName || '未知用户',
          avatarUrl: userInfo.avatarUrl || '',
        }
      };
    });
    
    return {
      code: 0,
      data: {
        list: appointmentsWithUserInfo,
        total: countResult.total,
        page: page,
        pageSize: pageSize,
        pageCount: Math.ceil(countResult.total / pageSize)
      },
      message: '获取分配给员工的预约列表成功'
    };
  } catch (err) {
    console.error('获取分配给员工的预约列表失败', err);
    return {
      code: -1,
      message: '获取分配给员工的预约列表失败: ' + err.message
    };
  }
} 