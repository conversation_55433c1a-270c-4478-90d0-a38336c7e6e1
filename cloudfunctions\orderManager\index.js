// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV }) // 使用当前云环境

// 检查并创建必要的集合
async function ensureCollectionsExist() {
  const db = cloud.database();
  
  try {
    // 获取所有集合
    const collections = await db.listCollections().get();
    const collectionNames = collections.data.map(collection => collection.name);
    
    // 检查orders集合是否存在，如果不存在则创建
    if (!collectionNames.includes('orders')) {
      await db.createCollection('orders');
      console.log('已创建orders集合');
    }
    
    return true;
  } catch (error) {
    console.error('确保集合存在时出错：', error);
    return false;
  }
}

// 获取员工待验证订单
async function getStaffPendingOrders(event, context) {
  const db = cloud.database();
  const _ = db.command;
  const { staffId } = event;
  
  try {
    // 获取待验证的订单（已确认但未完成的订单）
    const orders = await db.collection('orders')
      .where({
        status: 'confirmed', // 已确认状态
        isPaid: true // 已支付
      })
      .orderBy('appointmentDate', 'asc') // 按预约日期升序
      .orderBy('appointmentTime', 'asc') // 按预约时间升序
      .get();
    
    return {
      success: true,
      data: orders.data
    };
  } catch (error) {
    console.error('获取待验证订单失败：', error);
    return {
      success: false,
      message: '获取订单失败'
    };
  }
}

// 获取员工处理过的订单
async function getStaffOrders(event, context) {
  const db = cloud.database();
  const _ = db.command;
  const { staffId, status } = event;
  
  try {
    // 构建查询条件
    let whereCondition = {};
    
    // 如果指定了状态，添加状态过滤
    if (status) {
      whereCondition.status = status;
    }
    
    if (staffId) {
      // 如果提供了staffId，查询由该员工处理的订单
      whereCondition.staffId = staffId;
    }
    
    // 获取订单列表
    const orders = await db.collection('orders')
      .where(whereCondition)
      .orderBy('completeTime', 'desc') // 按完成时间降序
      .limit(50) // 限制返回数量
      .get();
    
    return {
      success: true,
      data: orders.data
    };
  } catch (error) {
    console.error('获取员工订单失败：', error);
    return {
      success: false,
      message: '获取订单失败'
    };
  }
}

// 获取订单详情
async function getOrderDetail(event, context) {
  const db = cloud.database();
  const { orderId } = event;
  
  if (!orderId) {
    return {
      success: false,
      message: '缺少订单ID'
    };
  }
  
  try {
    // 获取订单详情
    const order = await db.collection('orders').doc(orderId).get();
    
    if (!order.data) {
      return {
        success: false,
        message: '订单不存在'
      };
    }
    
    return {
      success: true,
      data: order.data
    };
  } catch (error) {
    console.error('获取订单详情失败：', error);
    return {
      success: false,
      message: '获取订单详情失败'
    };
  }
}

// 完成订单
async function completeOrder(event, context) {
  const db = cloud.database();
  const _ = db.command;
  const { orderId, staffId } = event;
  
  if (!orderId) {
    return {
      success: false,
      message: '缺少订单ID'
    };
  }
  
  try {
    // 获取订单信息
    const orderResult = await db.collection('orders').doc(orderId).get();
    const order = orderResult.data;
    
    if (!order) {
      return {
        success: false,
        message: '订单不存在'
      };
    }
    
    if (order.status === 'completed') {
      return {
        success: false,
        message: '订单已完成'
      };
    }
    
    if (order.status === 'cancelled') {
      return {
        success: false,
        message: '订单已取消'
      };
    }
    
    // 更新订单状态
    const now = new Date();
    const completeTime = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')} ${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}`;
    
    await db.collection('orders').doc(orderId).update({
      data: {
        status: 'completed',
        staffId: staffId,
        completeTime: completeTime
      }
    });
    
    return {
      success: true,
      message: '订单已完成'
    };
  } catch (error) {
    console.error('完成订单失败：', error);
    return {
      success: false,
      message: '完成订单失败'
    };
  }
}

// 通过核销码验证订单
async function verifyOrderByCode(event, context) {
  const db = cloud.database();
  const _ = db.command;
  const { verifyCode, staffId } = event;
  
  if (!verifyCode) {
    return {
      success: false,
      message: '缺少核销码'
    };
  }
  
  try {
    // 查找对应核销码的订单
    const orderResult = await db.collection('orders')
      .where({
        verifyCode: verifyCode
      })
      .get();
    
    if (orderResult.data.length === 0) {
      return {
        success: false,
        message: '核销码无效'
      };
    }
    
    const order = orderResult.data[0];
    
    if (order.status === 'completed') {
      return {
        success: false,
        message: '订单已完成'
      };
    }
    
    if (order.status === 'cancelled') {
      return {
        success: false,
        message: '订单已取消'
      };
    }
    
    if (order.status !== 'confirmed') {
      return {
        success: false,
        message: '订单未确认，无法验证'
      };
    }
    
    // 更新订单状态
    const now = new Date();
    const completeTime = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')} ${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}`;
    
    await db.collection('orders').doc(order._id).update({
      data: {
        status: 'completed',
        staffId: staffId,
        completeTime: completeTime
      }
    });
    
    return {
      success: true,
      message: '订单已完成',
      data: order
    };
  } catch (error) {
    console.error('验证订单失败：', error);
    return {
      success: false,
      message: '验证订单失败'
    };
  }
}

// 云函数入口函数
exports.main = async (event, context) => {
  // 确保集合存在
  await ensureCollectionsExist();
  
  const { action } = event;
  
  // 根据action执行不同的操作
  switch (action) {
    case 'getStaffPendingOrders':
      return await getStaffPendingOrders(event, context);
    case 'getStaffOrders':
      return await getStaffOrders(event, context);
    case 'getOrderDetail':
      return await getOrderDetail(event, context);
    case 'completeOrder':
      return await completeOrder(event, context);
    case 'verifyOrderByCode':
      return await verifyOrderByCode(event, context);
    default:
      return {
        success: false,
        message: '未知的操作类型'
      };
  }
} 