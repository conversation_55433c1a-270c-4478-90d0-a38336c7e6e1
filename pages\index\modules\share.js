/**
 * 分享功能模块
 * 负责分享功能处理、分享参数验证、积分奖励处理和分享链接生成
 * @version 1.0.0
 */

const BaseModule = require('./base-module');
const videoUtils = require('../utils/video-utils');
const { 
  ERROR_TYPES 
} = require('../constants/index-constants');

class ShareModule extends BaseModule {
  constructor(pageContext) {
    super(pageContext);
    this.moduleName = 'Share';
    
    // 分享统计和缓存
    this.shareHistory = [];
    this.shareCache = new Map();
  }

  /**
   * 初始化分享模块
   */
  init() {
    try {
      // 减少分享模块初始化日志
      // console.log('[Share] 初始化分享模块');
      
      // 初始化分享状态
      this.initShareState();
      
      // 加载分享历史
      this.loadShareHistory();
      
      this.initialized = true;
      // 减少分享模块初始化完成日志
      // console.log('[Share] 分享模块初始化完成');
    } catch (error) {
      this.handleError(error, 'init');
    }
  }

  /**
   * 初始化分享状态
   */
  initShareState() {
    try {
      const currentData = this.data;
      
      // 确保必要的分享状态字段存在
      const requiredFields = {
        sharedVideoId: currentData.sharedVideoId || null
      };

      // 只更新缺失的字段
      const updateData = {};
      Object.keys(requiredFields).forEach(key => {
        if (currentData[key] === undefined) {
          updateData[key] = requiredFields[key];
        }
      });

      if (Object.keys(updateData).length > 0) {
        this.safeSetData(updateData);
      }

      // 减少分享状态初始化完成日志
      // console.log('[Share] 分享状态初始化完成');
    } catch (error) {
      this.handleError(error, 'initShareState');
    }
  }

  /**
   * 处理分享小程序事件
   * @param {object} e - 事件对象
   * @returns {object} 分享配置对象
   */
  onShareAppMessage(e) {
    try {
      console.log('[Share] 触发视频分享事件:', e);
      
      // 引入设备工具函数
      const deviceUtils = require('../../../utils/device.js');
      
      // 检查是否有来自视频卡片的分享信息
      if (e && e.target && e.target.dataset && e.target.dataset.video) {
        return this.handleVideoCardShare(e.target.dataset.video);
      } 
      // 从分享按钮组件获取视频信息
      else if (e && e.from === 'button' && e.target) {
        return this.handleButtonShare(e.target.dataset.video);
      }
      
      // 默认分享信息
      return this.getDefaultShareData();
    } catch (error) {
      this.handleError(error, 'onShareAppMessage');
      return this.getDefaultShareData();
    }
  }

  /**
   * 处理视频卡片分享
   * @param {object} videoInfo - 视频信息
   * @returns {object} 分享配置对象
   */
  handleVideoCardShare(videoInfo) {
    try {
      console.log('[Share] 分享视频信息:', videoInfo);
      
      // 验证视频信息完整性
      if (!videoInfo || !videoInfo.id) {
        console.error('[Share] 分享的视频信息不完整');
        return this.getDefaultShareData();
      }
      
      const shareData = this.generateShareData(videoInfo);
      
      // 记录分享行为
      this.recordShareAction(videoInfo, 'video_card');
      
      // 使用全局分享辅助函数（同步版本）
      return getApp().shareWithPointsSync(shareData);
    } catch (error) {
      this.handleError(error, 'handleVideoCardShare');
      return this.getDefaultShareData();
    }
  }

  /**
   * 处理按钮分享
   * @param {object} videoInfo - 视频信息
   * @returns {object} 分享配置对象
   */
  handleButtonShare(videoInfo) {
    try {
      console.log('[Share] 从按钮分享视频信息:', videoInfo);
      
      if (videoInfo && videoInfo.id) {
        const shareData = this.generateShareData(videoInfo);
        
        // 记录分享行为
        this.recordShareAction(videoInfo, 'share_button');
        
        // 使用全局分享辅助函数（同步版本）
        return getApp().shareWithPointsSync(shareData);
      }
      
      return this.getDefaultShareData();
    } catch (error) {
      this.handleError(error, 'handleButtonShare');
      return this.getDefaultShareData();
    }
  }

  /**
   * 生成分享数据
   * @param {object} video - 视频对象
   * @returns {object} 分享数据对象
   */
  generateShareData(video) {
    try {
      if (!video || !video.id) {
        throw new Error('视频信息不完整');
      }

      // 使用视频工具函数生成分享数据
      const shareData = videoUtils.createVideoShareData(video);
      
      if (!shareData) {
        throw new Error('生成分享数据失败');
      }

      console.log('[Share] 准备分享视频，ID:', video.id, '标题:', shareData.title);
      
      return shareData;
    } catch (error) {
      this.handleError(error, 'generateShareData');
      return this.getDefaultShareData();
    }
  }

  /**
   * 获取默认分享数据
   * @returns {object} 默认分享配置
   */
  getDefaultShareData() {
    return {
      title: '精彩视频',
      path: '/pages/index/index'
    };
  }

  /**
   * 处理分享参数
   * @param {object} options - 页面参数
   */
  processShareParams(options) {
    try {
      console.log('[Share] 处理分享参数:', options);
      
      // 处理分享参数
      if (options.sharer && options.contentType && options.contentId) {
        this.verifyShareAndAddPoints(options);
      }
      
      // 如果有视频ID参数，打开视频详情
      if (options.videoId) {
        this.safeSetData({
          sharedVideoId: options.videoId
        });
        
        // 延迟打开分享的视频
        setTimeout(() => {
          this.openSharedVideo(options.videoId);
        }, 1000);
      }
    } catch (error) {
      this.handleError(error, 'processShareParams');
    }
  }

  /**
   * 验证分享并奖励积分
   * @param {object} options - 分享参数
   */
  verifyShareAndAddPoints(options) {
    try {
      // 引入设备工具函数
      const deviceUtils = require('../../../utils/device.js');
      
      // 获取设备标识 - 优先使用URL中传递的设备标识，如果没有则生成新的
      let deviceIdentifier;
      if (options.deviceIdentifier) {
        // 使用URL中传递的设备标识
        deviceIdentifier = decodeURIComponent(options.deviceIdentifier);
        console.log('[Share] 使用URL传递的设备标识:', deviceIdentifier);
      } else {
        // 生成新的设备标识
        deviceIdentifier = deviceUtils.getDeviceIdentifier();
        console.log('[Share] 生成新的设备标识:', deviceIdentifier);
      }
      
      // 验证分享并奖励积分
      wx.cloud.callFunction({
        name: 'pointsManager',
        data: {
          action: 'verifyShareAndAddPoints',
          data: {
            sharer: options.sharer,
            deviceIdentifier: deviceIdentifier,
            contentId: options.contentId,
            contentType: options.contentType
          }
        }
      }).then(res => {
        console.log('[Share] 分享验证结果:', res.result);
        this.handleShareVerificationResult(res.result, options);
      }).catch(err => {
        console.error('[Share] 分享验证失败:', err);
        this.handleError(err, 'verifyShareAndAddPoints');
      });
    } catch (error) {
      this.handleError(error, 'verifyShareAndAddPoints');
    }
  }

  /**
   * 处理分享验证结果
   * @param {object} result - 验证结果
   * @param {object} options - 分享参数
   */
  handleShareVerificationResult(result, options) {
    try {
      if (result && result.code === 0) {
        if (result.alreadyRewarded) {
          console.log('[Share] 该内容已被此设备打开过，不重复奖励积分');
          
          // 触发重复分享事件
          this.emit('shareAlreadyRewarded', {
            contentId: options.contentId,
            contentType: options.contentType
          });
        } else {
          console.log('[Share] 分享积分添加成功，积分:', result.pointsAdded);
          
          // 触发积分奖励事件
          this.emit('sharePointsAwarded', {
            contentId: options.contentId,
            contentType: options.contentType,
            points: result.pointsAdded
          });
          
          // 积分奖励已静默发放，不显示弹窗提示
          // this.showPointsRewardToast(result.pointsAdded);
        }
      } else {
        console.error('[Share] 分享积分未添加:', result ? result.message : '未知原因');
        
        // 触发分享失败事件
        this.emit('shareVerificationFailed', {
          error: result ? result.message : '未知原因',
          options: options
        });
      }
    } catch (error) {
      this.handleError(error, 'handleShareVerificationResult');
    }
  }

  /**
   * 显示积分奖励提示
   * @param {number} points - 奖励积分
   */
  showPointsRewardToast(points) {
    try {
      if (points > 0) {
        wx.showToast({
          title: `获得 ${points} 积分奖励！`,
          icon: 'success',
          duration: 2000
        });
      }
    } catch (error) {
      this.handleError(error, 'showPointsRewardToast');
    }
  }

  /**
   * 打开分享链接中的视频
   * @param {string} videoId - 视频ID
   */
  openSharedVideo(videoId) {
    try {
      if (!videoId) return;
      
      console.log('[Share] 打开分享的视频:', videoId);
      
      // 在视频列表中查找对应ID的视频
      const videoList = this.data.videoList || [];
      const videoInfo = videoList.find(item => item.id === videoId || item.baseId === videoId);
      
      if (videoInfo) {
        // 找到视频，打开详情
        this.openVideoDetail(videoInfo);
        
        // 记录分享视频打开行为
        this.recordShareVideoOpen(videoInfo);
      } else {
        // 未找到视频，可能需要加载更多或者视频已删除
        console.log('[Share] 未找到分享的视频，ID:', videoId);
        wx.showToast({
          title: '视频不存在或已删除',
          icon: 'none',
          duration: 2000
        });
        
        // 触发分享视频未找到事件
        this.emit('sharedVideoNotFound', {
          videoId: videoId
        });
      }
    } catch (error) {
      this.handleError(error, 'openSharedVideo');
    }
  }

  /**
   * 打开视频详情
   * @param {object} videoInfo - 视频信息
   */
  openVideoDetail(videoInfo) {
    try {
      if (!videoInfo) return;
      
      // 暂停所有视频
      if (this.page.videoPlayerModule) {
        this.page.videoPlayerModule.pauseAllVideos();
      }
      
      // 打开详情弹窗
      const videoDetailModal = this.page.selectComponent('#videoDetailModal');
      if (videoDetailModal) {
        videoDetailModal.showModal(videoInfo);
      }
      
      console.log('[Share] 视频详情已打开:', videoInfo.id);
    } catch (error) {
      this.handleError(error, 'openVideoDetail');
    }
  }

  /**
   * 记录分享行为
   * @param {object} video - 视频对象
   * @param {string} source - 分享来源
   */
  recordShareAction(video, source) {
    try {
      const shareRecord = {
        videoId: video.id,
        videoTitle: video.mainTitle,
        source: source,
        timestamp: Date.now()
      };

      // 添加到分享历史
      this.shareHistory.unshift(shareRecord);
      
      // 限制历史记录数量
      if (this.shareHistory.length > 50) {
        this.shareHistory = this.shareHistory.slice(0, 50);
      }

      // 保存到本地存储
      wx.setStorageSync('share_history', this.shareHistory);

      // 触发分享记录事件
      this.emit('shareRecorded', shareRecord);

      console.log('[Share] 分享行为已记录:', video.id, source);
    } catch (error) {
      this.handleError(error, 'recordShareAction');
    }
  }

  /**
   * 记录分享视频打开行为
   * @param {object} video - 视频对象
   */
  recordShareVideoOpen(video) {
    try {
      const openRecord = {
        videoId: video.id,
        videoTitle: video.mainTitle,
        action: 'open_shared_video',
        timestamp: Date.now()
      };

      // 触发分享视频打开事件
      this.emit('sharedVideoOpened', openRecord);

      console.log('[Share] 分享视频打开已记录:', video.id);
    } catch (error) {
      this.handleError(error, 'recordShareVideoOpen');
    }
  }

  /**
   * 加载分享历史
   */
  loadShareHistory() {
    try {
      const history = wx.getStorageSync('share_history') || [];
      this.shareHistory = history;
      // console.log(`[Share] 分享历史已加载，数量: ${history.length}`);
    } catch (error) {
      this.handleError(error, 'loadShareHistory');
      this.shareHistory = [];
    }
  }

  /**
   * 获取分享历史
   * @returns {Array} 分享历史列表
   */
  getShareHistory() {
    try {
      return this.shareHistory;
    } catch (error) {
      this.handleError(error, 'getShareHistory');
      return [];
    }
  }

  /**
   * 清除分享历史
   */
  clearShareHistory() {
    try {
      this.shareHistory = [];
      wx.removeStorageSync('share_history');
      console.log('[Share] 分享历史已清除');
    } catch (error) {
      this.handleError(error, 'clearShareHistory');
    }
  }

  /**
   * 获取分享统计
   * @returns {object} 分享统计信息
   */
  getShareStatistics() {
    try {
      const history = this.shareHistory;
      
      // 统计今天的分享次数
      const today = new Date().toDateString();
      const todayShares = history.filter(item => {
        const shareDate = new Date(item.timestamp).toDateString();
        return shareDate === today;
      });

      // 统计分享来源
      const sourceCounts = {};
      history.forEach(item => {
        sourceCounts[item.source] = (sourceCounts[item.source] || 0) + 1;
      });

      // 统计最常分享的视频
      const videoCounts = {};
      history.forEach(item => {
        videoCounts[item.videoId] = (videoCounts[item.videoId] || 0) + 1;
      });

      const mostSharedVideos = Object.entries(videoCounts)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 5)
        .map(([videoId, count]) => ({ videoId, count }));

      return {
        totalShares: history.length,
        todayShares: todayShares.length,
        sourceCounts: sourceCounts,
        mostSharedVideos: mostSharedVideos,
        lastShareTime: history.length > 0 ? history[0].timestamp : null
      };
    } catch (error) {
      this.handleError(error, 'getShareStatistics');
      return {};
    }
  }

  /**
   * 批量生成分享数据
   * @param {Array} videoList - 视频列表
   * @returns {Array} 分享数据列表
   */
  batchGenerateShareData(videoList) {
    try {
      if (!Array.isArray(videoList)) {
        return [];
      }

      return videoUtils.createVideoShareDataBatch(videoList);
    } catch (error) {
      this.handleError(error, 'batchGenerateShareData');
      return [];
    }
  }

  /**
   * 验证分享参数
   * @param {object} params - 分享参数
   * @returns {object} 验证结果
   */
  validateShareParams(params) {
    try {
      const result = {
        valid: true,
        errors: [],
        warnings: []
      };

      // 必需参数检查
      if (!params) {
        result.valid = false;
        result.errors.push('分享参数为空');
        return result;
      }

      // 检查分享者ID
      if (!params.sharer) {
        result.valid = false;
        result.errors.push('缺少分享者ID');
      }

      // 检查内容类型
      if (!params.contentType) {
        result.valid = false;
        result.errors.push('缺少内容类型');
      } else if (!['video', 'article', 'page'].includes(params.contentType)) {
        result.warnings.push('未知的内容类型: ' + params.contentType);
      }

      // 检查内容ID
      if (!params.contentId) {
        result.valid = false;
        result.errors.push('缺少内容ID');
      }

      // 检查设备标识
      if (!params.deviceIdentifier) {
        result.warnings.push('缺少设备标识');
      }

      // 检查视频ID格式（如果是视频分享）
      if (params.contentType === 'video' && params.videoId) {
        const videoIdPattern = /^[a-zA-Z0-9_-]+$/;
        if (!videoIdPattern.test(params.videoId)) {
          result.warnings.push('视频ID格式可能不正确');
        }
      }

      return result;
    } catch (error) {
      this.handleError(error, 'validateShareParams');
      return {
        valid: false,
        errors: ['参数验证失败'],
        warnings: []
      };
    }
  }

  /**
   * 清理和标准化分享参数
   * @param {object} params - 原始参数
   * @returns {object} 清理后的参数
   */
  sanitizeShareParams(params) {
    try {
      if (!params) return {};

      const sanitized = {};

      // 清理字符串参数
      ['sharer', 'contentType', 'contentId', 'videoId', 'deviceIdentifier'].forEach(key => {
        if (params[key]) {
          sanitized[key] = String(params[key]).trim();
        }
      });

      // 标准化内容类型
      if (sanitized.contentType) {
        sanitized.contentType = sanitized.contentType.toLowerCase();
      }

      // 解码URL编码的参数
      if (sanitized.deviceIdentifier) {
        try {
          sanitized.deviceIdentifier = decodeURIComponent(sanitized.deviceIdentifier);
        } catch (e) {
          console.warn('[Share] 设备标识解码失败:', e);
        }
      }

      return sanitized;
    } catch (error) {
      this.handleError(error, 'sanitizeShareParams');
      return {};
    }
  }

  /**
   * 处理分享参数（增强版）
   * @param {object} options - 页面参数
   */
  processShareParams(options) {
    try {
      console.log('[Share] 处理分享参数:', options);
      
      // 清理和验证参数
      const sanitizedParams = this.sanitizeShareParams(options);
      const validation = this.validateShareParams(sanitizedParams);

      // 记录验证结果
      if (!validation.valid) {
        console.error('[Share] 分享参数验证失败:', validation.errors);
        this.emit('shareParamsValidationFailed', {
          errors: validation.errors,
          warnings: validation.warnings,
          params: sanitizedParams
        });
        return;
      }

      if (validation.warnings.length > 0) {
        console.warn('[Share] 分享参数验证警告:', validation.warnings);
      }

      // 处理分享参数
      if (sanitizedParams.sharer && sanitizedParams.contentType && sanitizedParams.contentId) {
        this.verifyShareAndAddPoints(sanitizedParams);
      }
      
      // 如果有视频ID参数，打开视频详情
      if (sanitizedParams.videoId || sanitizedParams.contentId) {
        const videoId = sanitizedParams.videoId || sanitizedParams.contentId;
        this.safeSetData({
          sharedVideoId: videoId
        });
        
        // 延迟打开分享的视频
        setTimeout(() => {
          this.openSharedVideo(videoId);
        }, 1000);
      }

      // 触发参数处理完成事件
      this.emit('shareParamsProcessed', {
        params: sanitizedParams,
        validation: validation
      });
    } catch (error) {
      this.handleError(error, 'processShareParams');
    }
  }

  /**
   * 生成分享链接
   * @param {object} video - 视频对象
   * @param {object} options - 选项
   * @returns {string} 分享链接
   */
  generateShareLink(video, options = {}) {
    try {
      if (!video || !video.id) {
        throw new Error('视频信息不完整');
      }

      const baseUrl = options.baseUrl || '';
      const params = new URLSearchParams();

      // 基础参数
      params.append('videoId', video.id);
      params.append('contentType', 'video');
      params.append('contentId', video.id);

      // 分享者信息（如果有）
      if (options.sharerId) {
        params.append('sharer', options.sharerId);
      }

      // 设备标识（如果有）
      if (options.deviceIdentifier) {
        params.append('deviceIdentifier', encodeURIComponent(options.deviceIdentifier));
      }

      // 额外参数
      if (options.extraParams) {
        Object.entries(options.extraParams).forEach(([key, value]) => {
          params.append(key, value);
        });
      }

      const shareLink = `${baseUrl}/pages/index/index?${params.toString()}`;
      
      console.log('[Share] 生成分享链接:', shareLink);
      return shareLink;
    } catch (error) {
      this.handleError(error, 'generateShareLink');
      return '/pages/index/index';
    }
  }

  /**
   * 解析分享链接
   * @param {string} shareLink - 分享链接
   * @returns {object} 解析结果
   */
  parseShareLink(shareLink) {
    try {
      if (!shareLink) {
        return { valid: false, error: '分享链接为空' };
      }

      const url = new URL(shareLink, 'https://example.com');
      const params = {};

      // 提取查询参数
      url.searchParams.forEach((value, key) => {
        params[key] = value;
      });

      // 验证必要参数
      const validation = this.validateShareParams(params);

      return {
        valid: validation.valid,
        params: params,
        errors: validation.errors,
        warnings: validation.warnings
      };
    } catch (error) {
      this.handleError(error, 'parseShareLink');
      return { 
        valid: false, 
        error: '分享链接解析失败',
        params: {}
      };
    }
  }

  /**
   * 检查分享链接有效性
   * @param {string} shareLink - 分享链接
   * @returns {boolean} 是否有效
   */
  isValidShareLink(shareLink) {
    try {
      const parseResult = this.parseShareLink(shareLink);
      return parseResult.valid;
    } catch (error) {
      this.handleError(error, 'isValidShareLink');
      return false;
    }
  }

  /**
   * 处理分享链接点击
   * @param {string} shareLink - 分享链接
   */
  handleShareLinkClick(shareLink) {
    try {
      const parseResult = this.parseShareLink(shareLink);
      
      if (!parseResult.valid) {
        console.error('[Share] 无效的分享链接:', parseResult.error);
        wx.showToast({
          title: '分享链接无效',
          icon: 'none'
        });
        return;
      }

      // 处理解析出的参数
      this.processShareParams(parseResult.params);
    } catch (error) {
      this.handleError(error, 'handleShareLinkClick');
    }
  }

  /**
   * 获取分享参数模板
   * @param {string} contentType - 内容类型
   * @returns {object} 参数模板
   */
  getShareParamsTemplate(contentType = 'video') {
    try {
      const templates = {
        video: {
          contentType: 'video',
          contentId: '',
          videoId: '',
          sharer: '',
          deviceIdentifier: ''
        },
        article: {
          contentType: 'article',
          contentId: '',
          articleId: '',
          sharer: '',
          deviceIdentifier: ''
        },
        page: {
          contentType: 'page',
          contentId: '',
          pageId: '',
          sharer: '',
          deviceIdentifier: ''
        }
      };

      return templates[contentType] || templates.video;
    } catch (error) {
      this.handleError(error, 'getShareParamsTemplate');
      return {};
    }
  }

  /**
   * 批量验证分享参数
   * @param {Array} paramsList - 参数列表
   * @returns {Array} 验证结果列表
   */
  batchValidateShareParams(paramsList) {
    try {
      if (!Array.isArray(paramsList)) {
        return [];
      }

      return paramsList.map((params, index) => {
        const validation = this.validateShareParams(params);
        return {
          index: index,
          params: params,
          ...validation
        };
      });
    } catch (error) {
      this.handleError(error, 'batchValidateShareParams');
      return [];
    }
  }

  /**
   * 获取分享参数安全等级
   * @param {object} params - 分享参数
   * @returns {string} 安全等级 ('high', 'medium', 'low')
   */
  getShareParamsSecurity(params) {
    try {
      const validation = this.validateShareParams(params);
      
      if (!validation.valid) {
        return 'low';
      }

      let score = 0;
      
      // 基础参数完整性
      if (params.sharer && params.contentType && params.contentId) {
        score += 3;
      }

      // 设备标识存在
      if (params.deviceIdentifier) {
        score += 2;
      }

      // 参数格式正确
      if (validation.warnings.length === 0) {
        score += 1;
      }

      if (score >= 5) return 'high';
      if (score >= 3) return 'medium';
      return 'low';
    } catch (error) {
      this.handleError(error, 'getShareParamsSecurity');
      return 'low';
    }
  }

  /**
   * 获取分享状态
   * @returns {object} 分享状态信息
   */
  getShareStatus() {
    try {
      const statistics = this.getShareStatistics();
      
      return {
        sharedVideoId: this.data.sharedVideoId,
        hasSharedVideo: !!this.data.sharedVideoId,
        shareHistoryCount: this.shareHistory.length,
        statistics: statistics
      };
    } catch (error) {
      this.handleError(error, 'getShareStatus');
      return {};
    }
  }

  /**
   * 销毁模块，清理资源
   */
  destroy() {
    try {
      console.log('[Share] 销毁分享模块');
      
      // 清除分享缓存
      this.shareCache.clear();
      
      super.destroy();
    } catch (error) {
      this.handleError(error, 'destroy');
    }
  }
}

module.exports = ShareModule;