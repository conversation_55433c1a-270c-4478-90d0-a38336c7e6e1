/* 系统设置页面样式 */
.settings-container {
  min-height: 100vh;
  background-color: #f5f5f7;
  padding-bottom: 30rpx;
  width: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* 顶部标题栏 */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 20rpx;
  padding-top: 90rpx; /* 适配顶部状态栏 */
  background-color: #ffffff;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  width: 100%;
  box-sizing: border-box;
}

.back-icon {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  color: #333333;
}

.title {
  font-size: 34rpx;
  font-weight: bold;
  color: #333333;
}

.refresh-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.refresh-icon {
  width: 40rpx;
  height: 40rpx;
}

/* 加载中状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  width: 100%;
}

.loading-spinner {
  width: 80rpx;
  height: 80rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #0070c9;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #999999;
}

/* 设置内容 */
.settings-content {
  margin-top: 180rpx; /* 顶部标题栏高度 + 一些间距 */
  padding: 0 30rpx;
  height: calc(100vh - 180rpx);
  width: 100%;
  max-width: 700rpx; /* 设置最大宽度，确保在大屏幕上也能居中 */
  box-sizing: border-box;
}

/* 设置部分 */
.settings-section {
  background-color: #ffffff;
  border-radius: 6rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;
  width: 100%;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25rpx 30rpx;
  border-bottom: 1rpx solid #e0e0e0;
}

/* 锁定状态样式 */
.locked-section {
  /* 移除红色边框线 */
  /* background-color: #f9f9f9; */
  /* border-left: 4rpx solid #ff6b6b; */
}

.section-toggle-wrapper {
  display: flex;
  align-items: center;
}

.lock-icon {
  /* 移除锁图标样式 */
  /* margin-right: 10rpx; */
  /* font-size: 28rpx; */
  /* color: #ff6b6b; */
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
}

.section-toggle {
  font-size: 28rpx;
  color: #0070c9;
}

.section-content {
  padding: 20rpx 30rpx;
}

/* 设置项 */
.setting-item {
  margin-bottom: 25rpx;
  display: flex;
  flex-direction: column;
}

.setting-label {
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 15rpx;
}

.setting-value {
  font-size: 28rpx;
  color: #333333;
}

.setting-input {
  height: 80rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 4rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  background-color: #ffffff;
  color: #333333; /* 设置输入框文字颜色 */
}

.setting-note {
  font-size: 24rpx;
  color: #999999;
  margin: 10rpx 0 20rpx;
}

/* 时间选择器 */
.time-settings {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.time-settings .setting-item {
  width: 48%;
}

.picker-view {
  height: 80rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 4rpx;
  padding: 0 20rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #ffffff;
  color: #333333; /* 设置选择器文字颜色 */
}

.picker-arrow {
  color: #999999;
  font-size: 24rpx;
}

/* 按钮样式 */
.action-button {
  height: 80rpx;
  background-color: #0070c9;
  color: #ffffff;
  border-radius: 4rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30rpx;
  margin-top: 20rpx;
  box-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.action-button:active {
  opacity: 0.9;
  transform: translateY(1rpx);
}

.small-button {
  height: 60rpx;
  font-size: 26rpx;
  margin-top: 0;
}

/* 密码输入框样式 */
.setting-input[password] {
  letter-spacing: 5rpx;
  color: #333333; /* 确保密码输入框文字颜色 */
}

/* 统一营业时间设置 */
.unified-business-hours {
  margin-bottom: 20rpx;
}

/* 复选框组 */
.checkbox-group {
  display: flex;
  flex-wrap: wrap;
}

.checkbox-item {
  display: flex;
  align-items: center;
  margin-right: 30rpx;
  margin-bottom: 10rpx;
}

.checkbox-item text {
  font-size: 28rpx;
  color: #333333;
  margin-left: 10rpx;
} 