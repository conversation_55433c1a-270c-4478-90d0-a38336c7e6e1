/**
 * 微信API工具类
 * 注意：此文件依赖于微信小程序环境中的全局wx对象
 */

/* global wx */
const wxUtils = {
  /**
   * 显示加载提示
   * @param {string} title 提示文字
   */
  showLoading(title = '加载中...') {
    wx.showLoading({
      title,
      mask: true
    });
  },

  /**
   * 隐藏加载提示
   */
  hideLoading() {
    wx.hideLoading();
  },

  /**
   * 显示提示
   * @param {string} title 提示文字
   * @param {string} icon 图标类型
   */
  showToast(title, icon = 'none') {
    wx.showToast({
      title,
      icon,
      duration: 2000
    });
  },

  /**
   * Promise化的wx.request
   * @param {Object} options 请求配置
   */
  request(options) {
    return new Promise((resolve, reject) => {
      wx.request(Object.assign({}, options, {
        success: resolve,
        fail: reject
      }));
    });
  },

  /**
   * 获取系统信息（使用新推荐的API）
   * 返回合并的窗口信息和设备信息
   */
  async getSystemInfo() {
    return new Promise((resolve, reject) => {
      try {
        // 使用新推荐的API获取信息
        const windowInfo = wx.getWindowInfo();
        const deviceInfo = wx.getDeviceInfo();
        const appBaseInfo = wx.getAppBaseInfo();
        
        // 合并信息以保持与旧API返回格式兼容
        const result = Object.assign({}, 
          windowInfo,
          deviceInfo,
          appBaseInfo,
          // 确保保留常用属性
          {
            windowWidth: windowInfo.windowWidth,
            windowHeight: windowInfo.windowHeight,
            statusBarHeight: windowInfo.statusBarHeight,
            screenWidth: windowInfo.screenWidth,
            screenHeight: windowInfo.screenHeight,
            brand: deviceInfo.brand,
            model: deviceInfo.model,
            system: deviceInfo.system,
            platform: deviceInfo.platform
          }
        );
        
        resolve(result);
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 检查更新
   */
  checkForUpdate() {
    const updateManager = wx.getUpdateManager();
    updateManager.onCheckForUpdate(res => {
      if (res.hasUpdate) {
        updateManager.onUpdateReady(() => {
          wx.showModal({
            title: '更新提示',
            content: '新版本已经准备好，是否重启应用？',
            success: res => {
              if (res.confirm) {
                updateManager.applyUpdate();
              }
            }
          });
        });
      }
    });
  },

  /**
   * 创建视频上下文
   * @param {string} videoId 视频组件id
   * @param {Component} component 组件实例
   */
  createVideoContext(videoId, component) {
    return wx.createVideoContext(videoId, component);
  }
};

module.exports = wxUtils; 