// configManager云函数测试页面
Page({
  data: {
    testResults: [],
    loading: false
  },

  onLoad() {
    // 页面加载时自动运行所有测试
    this.runAllTests();
  },

  // 运行所有测试
  async runAllTests() {
    this.setData({ loading: true, testResults: [] });
    
    await this.testGetAllConfig();
    await this.testGetCloudStorageConfig();
    await this.testGetLaunchConfig();
    await this.testGetFileNamingRules();
    await this.testGetRequestConfig();
    await this.testGetConfig();
    
    this.setData({ loading: false });
    console.log('所有测试完成');
  },

  // 测试getAllConfig API
  async testGetAllConfig() {
    try {
      const result = await this.callCloudFunction('configManager', {
        action: 'getAllConfig'
      });
      
      this.addTestResult('getAllConfig', result);
      return result;
    } catch (error) {
      this.addTestResult('getAllConfig', { error: error.message || '调用失败' });
    }
  },

  // 测试getCloudStorageConfig API
  async testGetCloudStorageConfig() {
    try {
      const result = await this.callCloudFunction('configManager', {
        action: 'getCloudStorageConfig'
      });
      
      this.addTestResult('getCloudStorageConfig', result);
      return result;
    } catch (error) {
      this.addTestResult('getCloudStorageConfig', { error: error.message || '调用失败' });
    }
  },

  // 测试getLaunchConfig API
  async testGetLaunchConfig() {
    try {
      const result = await this.callCloudFunction('configManager', {
        action: 'getLaunchConfig'
      });
      
      this.addTestResult('getLaunchConfig', result);
      return result;
    } catch (error) {
      this.addTestResult('getLaunchConfig', { error: error.message || '调用失败' });
    }
  },

  // 测试getFileNamingRules API - 获取所有规则
  async testGetFileNamingRules() {
    try {
      const result = await this.callCloudFunction('configManager', {
        action: 'getFileNamingRules'
      });
      
      this.addTestResult('getFileNamingRules(all)', result);
      
      // 测试获取特定类型的规则
      await this.testGetFileNamingRulesByType('launch');
      
      return result;
    } catch (error) {
      this.addTestResult('getFileNamingRules(all)', { error: error.message || '调用失败' });
    }
  },

  // 测试getFileNamingRules API - 获取特定类型规则
  async testGetFileNamingRulesByType(fileType) {
    try {
      const result = await this.callCloudFunction('configManager', {
        action: 'getFileNamingRules',
        data: { fileType }
      });
      
      this.addTestResult(`getFileNamingRules(${fileType})`, result);
      return result;
    } catch (error) {
      this.addTestResult(`getFileNamingRules(${fileType})`, { error: error.message || '调用失败' });
    }
  },

  // 测试getRequestConfig API
  async testGetRequestConfig() {
    try {
      const result = await this.callCloudFunction('configManager', {
        action: 'getRequestConfig'
      });
      
      this.addTestResult('getRequestConfig', result);
      return result;
    } catch (error) {
      this.addTestResult('getRequestConfig', { error: error.message || '调用失败' });
    }
  },

  // 测试getConfig API
  async testGetConfig() {
    try {
      // 测试获取ENV_ID
      const result1 = await this.callCloudFunction('configManager', {
        action: 'getConfig',
        data: { configName: 'ENV_ID' }
      });
      
      this.addTestResult('getConfig(ENV_ID)', result1);
      
      // 测试获取不存在的配置项
      const result2 = await this.callCloudFunction('configManager', {
        action: 'getConfig',
        data: { configName: 'NON_EXISTENT_CONFIG' }
      });
      
      this.addTestResult('getConfig(NON_EXISTENT_CONFIG)', result2);
      
      // 测试不提供configName参数
      const result3 = await this.callCloudFunction('configManager', {
        action: 'getConfig',
        data: {}
      });
      
      this.addTestResult('getConfig(无参数)', result3);
      
      return { result1, result2, result3 };
    } catch (error) {
      this.addTestResult('getConfig', { error: error.message || '调用失败' });
    }
  },

  // 调用云函数的封装
  callCloudFunction(name, data) {
    return new Promise((resolve, reject) => {
      wx.cloud.callFunction({
        name,
        data,
        success: res => {
          resolve(res.result);
        },
        fail: err => {
          console.error(`调用云函数${name}失败:`, err);
          reject(err);
        }
      });
    });
  },

  // 添加测试结果
  addTestResult(apiName, result) {
    const testResults = this.data.testResults;
    testResults.push({
      apiName,
      result: JSON.stringify(result, null, 2),
      success: result.code === 200,
      timestamp: new Date().toLocaleTimeString()
    });
    
    this.setData({ testResults });
    
    console.log(`测试结果 - ${apiName}:`, result);
  },

  // 复制测试结果到剪贴板
  copyTestResult(e) {
    const index = e.currentTarget.dataset.index;
    const result = this.data.testResults[index].result;
    
    wx.setClipboardData({
      data: result,
      success: () => {
        wx.showToast({ title: '已复制到剪贴板' });
      }
    });
  },

  // 重新运行测试
  onTapRerunTests() {
    this.runAllTests();
  }
}); 