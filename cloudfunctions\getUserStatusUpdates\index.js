// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 确保集合存在的辅助函数
async function ensureCollectionExists(collectionName) {
  try {
    // 检查集合是否存在
    const collections = await db.listCollections().get();
    const collectionExists = collections.data.some(col => col.name === collectionName);
    
    if (!collectionExists) {
      console.log(`集合 ${collectionName} 不存在，正在创建...`);
      
      // 创建集合 - 添加一个临时文档然后删除，这样可以触发集合自动创建
      const tempDocId = `temp_${Date.now()}`;
      await db.collection(collectionName).add({
        data: {
          _id: tempDocId,
          temp: true,
          createTime: db.serverDate()
        }
      });
      
      // 删除临时文档
      await db.collection(collectionName).doc(tempDocId).remove();
      
      console.log(`集合 ${collectionName} 创建成功`);
    } else {
      console.log(`集合 ${collectionName} 已存在`);
    }
    
    return true;
  } catch (err) {
    console.error(`确保集合 ${collectionName} 存在时出错:`, err);
    // 如果错误是因为其他原因，则返回false
    // 但如果是因为集合已经存在而无法创建，则返回true
    return err.errCode === -502001; // 集合已存在的错误码
  }
}

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID
  
  console.log('getUserStatusUpdates 被调用，参数:', event);
  console.log('当前用户 openid:', openid);
  
  if (!openid) {
    return {
      code: -1,
      message: '未获取到用户身份'
    };
  }
  
  try {
    // 确保集合存在
    const collectionName = 'user_status_updates';
    await ensureCollectionExists(collectionName);
    
    const { type } = event;
    
    // 查询用户状态更新记录
    const query = {
      openid: openid,
      handled: false
    };
    
    // 如果指定了类型，只查询该类型的更新
    if (type) {
      query.type = type;
    }
    
    const updates = await db.collection(collectionName)
      .where(query)
      .get();
    
    console.log('查询到的状态更新:', updates.data);
    
    if (updates.data && updates.data.length > 0) {
      // 将这些记录标记为已处理
      const updateIds = updates.data.map(item => item._id);
      
      // 使用批量写入更新多条记录
      const tasks = updateIds.map(id => {
        return db.collection(collectionName).doc(id).update({
          data: {
            handled: true,
            handleTime: db.serverDate()
          }
        });
      });
      
      // 并行执行所有更新任务
      await Promise.all(tasks);
      
      console.log('已将状态更新标记为已处理');
      
      return {
        code: 0,
        hasUpdates: true,
        updates: updates.data,
        message: '成功获取状态更新'
      };
    } else {
      return {
        code: 0,
        hasUpdates: false,
        updates: [],
        message: '没有新的状态更新'
      };
    }
  } catch (err) {
    console.error('获取状态更新失败:', err);
    return {
      code: -1,
      message: '获取状态更新失败: ' + err.message
    };
  }
} 