<view class="service-container">
  <!-- 页面标题 -->
  <view class="page-header">
    <view class="back-btn" bindtap="navigateBack">
      <text class="back-icon">←</text>
    </view>
    <view class="header-title" bindlongpress="getAdminPermission">服务管理</view>
    <view style="width: 60rpx;"></view> <!-- 占位，保持标题居中 -->
  </view>

  <!-- 服务列表 -->
  <view class="service-list">
    <!-- 加载中 -->
    <view class="loading-container" wx:if="{{loading}}">
      <view class="loading-spinner"></view>
      <text class="loading-text">加载中...</text>
    </view>
    
    <!-- 空状态 -->
    <view class="empty-container" wx:elif="{{services.length === 0}}">
      <text class="empty-text">暂无服务项目</text>
    </view>
    
    <!-- 服务列表 -->
    <view class="service-items" wx:else>
      <view class="service-item" wx:for="{{services}}" wx:key="id">
        <view class="service-card">
          <!-- 可见性状态指示器 -->
          <view class="visibility-indicator {{item.isVisible ? 'visible' : 'hidden'}}"></view>
          
          <!-- 服务内容区域 -->
          <view class="service-content">
            <view class="service-info">
              <view class="service-header">
                <text class="service-name">{{item.name}}</text>
              </view>
              <view class="service-price-row">
                <text class="service-price">¥{{item.price}}</text>
                <text class="original-price" wx:if="{{item.originalPrice && item.originalPrice > item.price}}">原价{{item.originalPrice}}</text>
              </view>
              
              <!-- 操作按钮区域 -->
              <view class="service-actions">
                <view class="action-btns">
                  <view class="action-btn visibility-btn {{item.isVisible ? 'hide-btn' : 'show-btn'}}" bindtap="toggleServiceVisibility" data-service="{{item}}">
                    {{item.isVisible ? '隐藏' : '显示'}}
                  </view>
                  <view class="action-btn edit-btn" bindtap="showEditForm" data-service="{{item}}">编辑</view>
                  <view class="action-btn delete-btn" bindtap="deleteService" data-service="{{item}}">删除</view>
                </view>
              </view>
            </view>
            
            <!-- 服务图片区域 -->
            <image class="service-image" src="{{item.image || '/static/default-service.png'}}" mode="aspectFill"></image>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 底部安全区域 -->
    <view class="safe-bottom-area"></view>
  </view>
  
  <!-- 底部添加按钮 -->
  <view class="floating-add-button" bindtap="showAddForm">
    <text class="floating-add-icon">+</text>
  </view>

  <!-- 添加服务表单 -->
  <view class="form-modal" wx:if="{{showAddForm}}">
    <view class="form-container">
      <view class="form-header">
        <text class="form-title">添加服务</text>
        <view class="close-btn" bindtap="closeForm">×</view>
      </view>
      
      <view class="form-body">
        <view class="form-item">
          <text class="form-label">服务名称</text>
          <input class="form-input" value="{{formData.name}}" placeholder="请输入服务名称" placeholder-style="color: #999;" bindinput="inputChange" data-field="name" />
        </view>

        <view class="form-item">
          <text class="form-label">副标题</text>
          <input class="form-input" value="{{formData.description}}" placeholder="请输入副标题" placeholder-style="color: #999;" bindinput="inputChange" data-field="description" />
        </view>

        <view class="form-item">
          <text class="form-label">服务价格</text>
          <input class="form-input" type="digit" value="{{formData.price}}" placeholder="请输入服务价格" placeholder-style="color: #999;" bindinput="inputChange" data-field="price" />
        </view>

        <view class="form-item">
          <text class="form-label">原价</text>
          <input class="form-input" type="digit" value="{{formData.originalPrice}}" placeholder="请输入原价（可选）" placeholder-style="color: #999;" bindinput="inputChange" data-field="originalPrice" />
        </view>
        
        <view class="form-item">
          <text class="form-label">服务图片</text>
          <view class="image-uploader" bindtap="chooseImage">
            <image class="uploaded-image" src="{{tempImagePath || formData.image}}" mode="aspectFill"></image>
            <view class="upload-overlay" wx:if="{{!tempImagePath && (!formData.image || formData.image === '/static/default-service.png')}}">
              <view class="upload-icon">+</view>
              <view class="upload-text">点击上传图片</view>
              <view class="upload-hint">建议尺寸 300x300</view>
            </view>
            <view class="upload-edit-hint" wx:else>
              <view class="edit-icon">✎</view>
              <view class="edit-text">点击更换图片</view>
            </view>
          </view>
        </view>

        <view class="form-item">
          <text class="form-label">详情图片</text>
          <view class="image-uploader" bindtap="chooseDetailImage">
            <image class="uploaded-image" src="{{tempDetailImagePath || formData.detailImage}}" mode="aspectFill"></image>
            <view class="upload-overlay" wx:if="{{!tempDetailImagePath && (!formData.detailImage || formData.detailImage === '')}}">
              <view class="upload-icon">+</view>
              <view class="upload-text">点击上传详情图片</view>
              <view class="upload-hint">用户点击服务图片时显示</view>
            </view>
            <view class="upload-edit-hint" wx:else>
              <view class="edit-icon">✎</view>
              <view class="edit-text">点击更换详情图片</view>
            </view>
          </view>
        </view>
      </view>
      
      <view class="form-footer">
        <view class="form-btn cancel-btn" bindtap="closeForm">取消</view>
        <view class="form-btn submit-btn" bindtap="submitAddForm">确认添加</view>
      </view>
    </view>
  </view>

  <!-- 编辑服务表单 -->
  <view class="form-modal" wx:if="{{showEditForm}}">
    <view class="form-container">
      <view class="form-header">
        <text class="form-title">编辑服务</text>
        <view class="close-btn" bindtap="closeForm">×</view>
      </view>
      
      <view class="form-body">
        <view class="form-item">
          <text class="form-label">服务名称</text>
          <input class="form-input" value="{{formData.name}}" placeholder="请输入服务名称" placeholder-style="color: #999;" bindinput="inputChange" data-field="name" />
        </view>

        <view class="form-item">
          <text class="form-label">副标题</text>
          <input class="form-input" value="{{formData.description}}" placeholder="请输入副标题" placeholder-style="color: #999;" bindinput="inputChange" data-field="description" />
        </view>

        <view class="form-item">
          <text class="form-label">服务价格</text>
          <input class="form-input" type="digit" value="{{formData.price}}" placeholder="请输入服务价格" placeholder-style="color: #999;" bindinput="inputChange" data-field="price" />
        </view>

        <view class="form-item">
          <text class="form-label">原价</text>
          <input class="form-input" type="digit" value="{{formData.originalPrice}}" placeholder="请输入原价（可选）" placeholder-style="color: #999;" bindinput="inputChange" data-field="originalPrice" />
        </view>
        
        <view class="form-item">
          <text class="form-label">服务图片</text>
          <view class="image-uploader" bindtap="chooseImage">
            <image class="uploaded-image" src="{{tempImagePath || formData.image}}" mode="aspectFill"></image>
            <view class="upload-overlay" wx:if="{{!tempImagePath && (!formData.image || formData.image === '/static/default-service.png')}}">
              <view class="upload-icon">+</view>
              <view class="upload-text">点击上传图片</view>
              <view class="upload-hint">建议尺寸 300x300</view>
            </view>
            <view class="upload-edit-hint" wx:else>
              <view class="edit-icon">✎</view>
              <view class="edit-text">点击更换图片</view>
            </view>
          </view>
        </view>

        <view class="form-item">
          <text class="form-label">详情图片</text>
          <view class="image-uploader" bindtap="chooseDetailImage">
            <image class="uploaded-image" src="{{tempDetailImagePath || formData.detailImage}}" mode="aspectFill"></image>
            <view class="upload-overlay" wx:if="{{!tempDetailImagePath && (!formData.detailImage || formData.detailImage === '')}}">
              <view class="upload-icon">+</view>
              <view class="upload-text">点击上传详情图片</view>
              <view class="upload-hint">用户点击服务图片时显示</view>
            </view>
            <view class="upload-edit-hint" wx:else>
              <view class="edit-icon">✎</view>
              <view class="edit-text">点击更换详情图片</view>
            </view>
          </view>
        </view>
      </view>
      
      <view class="form-footer">
        <view class="form-btn cancel-btn" bindtap="closeForm">取消</view>
        <view class="form-btn submit-btn" bindtap="submitEditForm">确认修改</view>
      </view>
    </view>
  </view>
</view> 