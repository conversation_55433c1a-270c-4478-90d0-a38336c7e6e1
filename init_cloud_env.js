// 云环境初始化脚本
const cloud = require('wx-server-sdk');

// 初始化云环境
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

// 主函数
exports.main = async (event, context) => {
  console.log('开始初始化云环境');
  const result = {
    success: true,
    collections: [],
    errors: []
  };
  
  try {
    // 1. 创建支出记录集合
    try {
      await db.createCollection('expenses');
      console.log('创建expenses集合成功');
      result.collections.push('expenses');
    } catch (e) {
      console.log('expenses集合已存在或创建失败:', e.message);
      result.errors.push({
        collection: 'expenses',
        error: e.message
      });
    }
    
    // 2. 设置集合权限
    try {
      await db.collection('expenses').get();
      console.log('expenses集合权限检查通过');
    } catch (e) {
      console.error('expenses集合权限检查失败:', e.message);
      result.errors.push({
        collection: 'expenses',
        error: `权限检查失败: ${e.message}`
      });
    }
    
    console.log('云环境初始化完成');
    return result;
  } catch (error) {
    console.error('云环境初始化失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
}; 