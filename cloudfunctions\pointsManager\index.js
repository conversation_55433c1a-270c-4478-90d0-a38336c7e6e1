// 积分管理云函数
const cloud = require('wx-server-sdk');
cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV });
const db = cloud.database();
const _ = db.command;

// 获取当前时间字符串
function getCurrentTime() {
  const date = new Date();
  return date.toLocaleString('zh-CN', { hour12: false });
}

// 确保必要的集合存在
async function ensureCollectionsExist() {
  console.log('检查并创建必要的集合');
  
  try {
    // 获取所有集合
    let collections;
    try {
      collections = await db.listCollections().get();
      const collectionNames = collections.data.map(collection => collection.name);
      console.log('现有集合:', collectionNames);
      
      // 需要确保存在的集合
      const requiredCollections = ['share_records', 'withdrawals', 'system_settings', 'users'];
      
      // 创建不存在的集合
      for (const collectionName of requiredCollections) {
        if (!collectionNames.includes(collectionName)) {
          try {
            console.log(`创建集合: ${collectionName}`);
            await db.createCollection(collectionName);
            console.log(`成功创建集合: ${collectionName}`);
            
            // 如果是system_settings集合，添加默认积分设置
            if (collectionName === 'system_settings') {
              await initializePointsSettings();
            }
          } catch (createErr) {
            console.error(`创建集合 ${collectionName} 失败:`, createErr);
            // 继续创建其他集合，不中断流程
          }
        } else {
          console.log(`集合 ${collectionName} 已存在`);
        }
      }
    } catch (listErr) {
      console.error('获取集合列表失败:', listErr);
      
      // 如果无法获取集合列表，尝试直接创建所有必要的集合
      console.log('尝试直接创建所有必要的集合');
      
      // 直接尝试创建share_records集合
      try {
        await db.createCollection('share_records');
        console.log('成功创建share_records集合');
      } catch (err) {
        console.log('share_records集合已存在或创建失败:', err.message);
      }
      
      // 直接尝试创建withdrawals集合
      try {
        await db.createCollection('withdrawals');
        console.log('成功创建withdrawals集合');
      } catch (err) {
        console.log('withdrawals集合已存在或创建失败:', err.message);
      }
      
      // 直接尝试创建system_settings集合
      try {
        await db.createCollection('system_settings');
        console.log('成功创建system_settings集合');
        
        // 初始化积分设置
        await initializePointsSettings();
      } catch (err) {
        console.log('system_settings集合已存在或创建失败:', err.message);
      }
      
      // 直接尝试创建users集合
      try {
        await db.createCollection('users');
        console.log('成功创建users集合');
      } catch (err) {
        console.log('users集合已存在或创建失败:', err.message);
      }
    }
    
    return true;
  } catch (err) {
    console.error('创建集合过程中发生未知错误:', err);
    // 即使初始化失败，也继续执行后续代码
    return false;
  }
}

// 初始化积分设置
async function initializePointsSettings() {
  try {
    // 检查是否已存在积分设置
    const settingsResult = await db.collection('system_settings').where({
      type: 'points'
    }).get();
    
    if (settingsResult.data.length === 0) {
      console.log('创建默认积分设置');
      // 创建默认积分设置
      await db.collection('system_settings').add({
        data: {
          type: 'points',
          settings: {
            servicePoints: 10,
            articlePoints: 5,
            videoPoints: 8,
            exchangeRatio: 1
          },
          createTime: db.serverDate(),
          updateTime: db.serverDate()
        }
      });
    }
  } catch (err) {
    console.error('初始化积分设置失败:', err);
  }
}

// 云函数入口函数
exports.main = async (event, context) => {
  const { action, data } = event;
  const wxContext = cloud.getWXContext();
  
  // 确保必要的集合存在
  await ensureCollectionsExist();
  
  // 根据不同的操作类型执行不同的逻辑
  switch (action) {
    // 用户分享获取积分
    case 'verifyShareAndAddPoints':
      return await verifyShareAndAddPoints(data, wxContext);
    
    // 获取用户积分
    case 'getUserPoints':
      return await getUserPoints(wxContext);
    
    // 提交提现申请
    case 'submitWithdrawal':
      return await submitWithdrawal(data, wxContext);
    
    // 获取用户提现记录
    case 'getUserWithdrawals':
      return await getUserWithdrawals(wxContext);
    
    // 管理员获取所有提现申请
    case 'getAllWithdrawals':
      return await getAllWithdrawals(data);
    
    // 管理员处理提现申请
    case 'processWithdrawal':
      return await processWithdrawal(data);
    
    // 获取用户分享记录
    case 'getUserShareRecords':
      return await getUserShareRecords(wxContext);
    
    // 管理员获取所有用户分享记录
    case 'getAllShareRecords':
      return await getAllShareRecords(data);
    
    // 获取积分设置
    case 'getPointsSettings':
      return await getPointsSettings();
    
    // 保存积分设置
    case 'savePointsSettings':
      return await savePointsSettings(event.settings);
    
    default:
      return {
        code: -1,
        message: '未知操作类型'
      };
  }
};

// 验证分享并添加积分
async function verifyShareAndAddPoints(data, wxContext) {
  const { sharer, deviceIdentifier, contentId, contentType } = data;
  
  console.log('验证分享参数:', {
    sharer: sharer,
    deviceIdentifier: deviceIdentifier,
    contentId: contentId,
    contentType: contentType,
    wxContext: { OPENID: wxContext.OPENID, APPID: wxContext.APPID }
  });
  
  // 验证必要参数
  if (!sharer || !deviceIdentifier || !contentId || !contentType) {
    console.error('分享验证失败: 缺少必要参数', { sharer, deviceIdentifier, contentId, contentType });
    return {
      code: 400,
      message: '分享验证失败: 缺少必要参数'
    };
  }
  
  try {
    // 确保share_records集合存在
    try {
      // 直接尝试创建share_records集合
      try {
        await db.createCollection('share_records');
        console.log('成功创建share_records集合');
      } catch (err) {
        // 如果集合已存在，会抛出错误，这是正常的
        console.log('share_records集合已存在或创建失败:', err.message);
      }
    } catch (collErr) {
      console.error('检查或创建分享记录集合失败:', collErr);
      // 继续执行，让后续操作决定是否成功
    }
    
    // 创建唯一分享标识：分享者ID + 内容ID + 设备标识
    const uniqueShareId = `${sharer}_${contentId}_${deviceIdentifier}`;
    console.log('生成唯一分享标识:', uniqueShareId);
    
    // 检查是否已经奖励过积分（使用唯一标识）
    let shareRecord;
    try {
      shareRecord = await db.collection('share_records').where({
        uniqueShareId: uniqueShareId
      }).get();
    } catch (queryErr) {
      console.error('查询分享记录失败:', queryErr);
      // 如果查询失败，假设没有记录
      shareRecord = { data: [] };
    }
    
    console.log('查询分享记录结果:', {
      recordCount: shareRecord.data.length,
      records: shareRecord.data,
      uniqueShareId: uniqueShareId
    });
    
    // 如果已经奖励过积分，不重复奖励
    if (shareRecord.data.length > 0) {
      console.log('该内容已被此设备打开过，不重复奖励积分');
      return {
        code: 0,
        message: '该内容已被此设备打开过，不重复奖励积分',
        alreadyRewarded: true
      };
    }
    
    // 确定积分奖励数量（根据内容类型）
    let pointsToAdd = await getPointsByContentType(contentType);
    console.log('将添加积分:', pointsToAdd, '内容类型:', contentType);
    
    // 记录分享记录
    try {
      const recordResult = await db.collection('share_records').add({
        data: {
          sharer: sharer,
          deviceIdentifier: deviceIdentifier, // 使用设备标识替代receiver
          contentId: contentId,
          contentType: contentType,
          uniqueShareId: uniqueShareId, // 存储唯一标识
          points: pointsToAdd,
          createTime: db.serverDate()
        }
      });
      
      console.log('分享记录已添加:', recordResult);
      
      // 更新分享者积分
      const updateResult = await updateUserPoints(sharer, pointsToAdd);
      console.log('用户积分已更新:', updateResult);
      
      if (!updateResult) {
        console.error('更新用户积分失败，但分享记录已创建');
        return {
          code: 0,
          message: '分享验证部分成功：记录已创建但积分更新失败',
          pointsAdded: 0,
          recordId: recordResult._id
        };
      }
      
      return {
        code: 0,
        message: '分享验证成功，积分已奖励',
        pointsAdded: pointsToAdd,
        recordId: recordResult._id
      };
    } catch (addErr) {
      console.error('添加分享记录失败:', addErr);
      return {
        code: 0,
        message: '添加分享记录失败，但将继续尝试',
        pointsAdded: 0
      };
    }
  } catch (err) {
    console.error('分享验证失败:', err);
    return {
      code: 0,
      message: '分享验证过程中发生错误，但将继续尝试',
      pointsAdded: 0
    };
  }
}

// 根据内容类型获取积分
async function getPointsByContentType(contentType) {
  try {
    // 确保system_settings集合存在
    try {
      // 直接尝试创建system_settings集合
      try {
        await db.createCollection('system_settings');
        console.log('成功创建system_settings集合');
        
        // 初始化积分设置
        await initializePointsSettings();
      } catch (err) {
        // 如果集合已存在，会抛出错误，这是正常的
        console.log('system_settings集合已存在或创建失败:', err.message);
      }
    } catch (collErr) {
      console.error('检查或创建系统设置集合失败:', collErr);
      // 继续执行，让后续操作决定是否成功
    }
    
    // 从数据库获取积分设置
    let settingsResult;
    try {
      settingsResult = await db.collection('system_settings').where({
        type: 'points'
      }).get();
    } catch (queryErr) {
      console.error('查询积分设置失败:', queryErr);
      // 如果查询失败，使用默认值
      console.log('查询积分设置失败，使用默认值');
      
      // 返回默认值
      switch (contentType) {
        case 'service': return 10;
        case 'article': return 5;
        case 'video': return 8;
        default: return 2;
      }
    }
    
    // 如果有设置，使用设置中的值
    if (settingsResult.data.length > 0) {
      const settings = settingsResult.data[0].settings;
      
      switch (contentType) {
        case 'service': return parseInt(settings.servicePoints) || 10;
        case 'article': return parseInt(settings.articlePoints) || 5;
        case 'video': return parseInt(settings.videoPoints) || 8;
        default: return 2;
      }
    } else {
      // 如果没有设置，创建默认设置
      console.log('未找到积分设置，创建默认设置');
      
      const defaultSettings = {
        servicePoints: 10,
        articlePoints: 5,
        videoPoints: 8,
        exchangeRatio: 1
      };
      
      try {
        await db.collection('system_settings').add({
          data: {
            type: 'points',
            settings: defaultSettings,
            createTime: db.serverDate(),
            updateTime: db.serverDate()
          }
        });
        console.log('成功创建默认积分设置');
      } catch (addErr) {
        console.error('创建默认积分设置失败:', addErr);
      }
      
      // 返回默认值
      switch (contentType) {
        case 'service': return 10;
        case 'article': return 5;
        case 'video': return 8;
        default: return 2;
      }
    }
  } catch (err) {
    console.error('获取积分设置失败:', err);
    // 出错时使用默认值
    switch (contentType) {
      case 'service': return 10;
      case 'article': return 5;
      case 'video': return 8;
      default: return 2;
    }
  }
}

// 更新用户积分
async function updateUserPoints(openid, points) {
  const _ = db.command;
  
  console.log('更新用户积分:', { openid, points });
  
  if (!openid || !points) {
    console.error('更新用户积分失败: 缺少必要参数', { openid, points });
    return false;
  }
  
  try {
    // 确保users集合存在
    try {
      // 直接尝试创建users集合
      try {
        await db.createCollection('users');
        console.log('成功创建users集合');
      } catch (err) {
        // 如果集合已存在，会抛出错误，这是正常的
        console.log('users集合已存在或创建失败:', err.message);
      }
    } catch (collErr) {
      console.error('检查或创建用户集合失败:', collErr);
      // 继续执行，让后续操作决定是否成功
    }
    
    // 查询用户是否存在
    let userResult;
    try {
      userResult = await db.collection('users').where({
        openid: openid
      }).get();
    } catch (queryErr) {
      console.error('查询用户失败:', queryErr);
      // 如果查询失败，假设用户不存在
      userResult = { data: [] };
    }
    
    console.log('查询用户结果:', {
      userExists: userResult.data.length > 0,
      userData: userResult.data.length > 0 ? {
        openid: userResult.data[0].openid,
        points: userResult.data[0].points,
        totalPoints: userResult.data[0].totalPoints
      } : null
    });
    
    if (userResult.data.length === 0) {
      // 用户不存在，创建新用户
      console.log('用户不存在，创建新用户并添加积分');
      try {
        await db.collection('users').add({
          data: {
            openid: openid,
            points: points,
            totalPoints: points,
            frozenPoints: 0,
            createTime: db.serverDate(),
            updateTime: db.serverDate()
          }
        });
        
        console.log('成功创建用户并添加积分');
        return true;
      } catch (addErr) {
        console.error('创建用户失败:', addErr);
        return false;
      }
    } else {
      // 用户存在，更新积分
      console.log('用户存在，更新积分');
      try {
        const currentPoints = userResult.data[0].points || 0;
        const currentTotalPoints = userResult.data[0].totalPoints || 0;
        
        await db.collection('users').where({
          openid: openid
        }).update({
          data: {
            points: _.inc(points),
            totalPoints: _.inc(points),
            updateTime: db.serverDate()
          }
        });
        
        console.log('成功更新用户积分');
        return true;
      } catch (updateErr) {
        console.error('更新用户积分失败:', updateErr);
        return false;
      }
    }
  } catch (err) {
    console.error('更新用户积分过程中发生错误:', err);
    return false;
  }
}

// 获取用户积分
async function getUserPoints(wxContext) {
  const openid = wxContext.OPENID;
  
  try {
    const userResult = await db.collection('users').where({
      openid: openid
    }).get();
    
    if (userResult.data.length === 0) {
      return {
        code: 0,
        message: '用户不存在',
        points: 0,
        totalPoints: 0,
        frozenPoints: 0
      };
    }
    
    const user = userResult.data[0];
    
    return {
      code: 0,
      message: '获取积分成功',
      points: user.points || 0,
      totalPoints: user.totalPoints || 0,
      frozenPoints: user.frozenPoints || 0
    };
  } catch (err) {
    console.error('获取用户积分失败:', err);
    return {
      code: 500,
      message: '获取用户积分失败: ' + err.message
    };
  }
}

// 提交提现申请
async function submitWithdrawal(data, wxContext) {
  const { points, paymentQrCode, description } = data;
  const openid = wxContext.OPENID;
  
  if (!points || !paymentQrCode) {
    return {
      code: 400,
      message: '缺少必要参数'
    };
  }
  
  try {
    // 确保withdrawals集合存在
    try {
      const collections = await db.listCollections().get();
      const collectionNames = collections.data.map(collection => collection.name);
      
      if (!collectionNames.includes('withdrawals')) {
        console.log('提现记录集合不存在，正在创建...');
        await db.createCollection('withdrawals');
      }
      
      // 确保users集合存在
      if (!collectionNames.includes('users')) {
        console.log('用户集合不存在，正在创建...');
        await db.createCollection('users');
      }
    } catch (collErr) {
      console.error('检查或创建集合失败:', collErr);
      // 继续执行，让后续操作决定是否成功
    }
    
    // 检查用户积分是否足够
    const userResult = await db.collection('users').where({
      openid: openid
    }).get();
    
    if (userResult.data.length === 0) {
      return {
        code: 404,
        message: '用户不存在'
      };
    }
    
    const user = userResult.data[0];
    const currentPoints = user.points || 0;
    
    if (currentPoints < points) {
      return {
        code: 400,
        message: '积分不足'
      };
    }
    
    // 检查是否有未处理的提现申请
    const pendingWithdrawals = await db.collection('withdrawals').where({
      openid: openid,
      status: 'pending'
    }).count();
    
    if (pendingWithdrawals.total > 0) {
      return {
        code: 400,
        message: '您有未处理的提现申请，请等待处理完成后再提交'
      };
    }
    
    // 创建提现记录
    const withdrawalResult = await db.collection('withdrawals').add({
      data: {
        openid: openid,
        userName: user.nickName || '用户',
        points: points,
        paymentQrCode: paymentQrCode,
        description: description || '',
        status: 'pending', // pending: 提现中, completed: 已提现, rejected: 已拒绝
        createTime: db.serverDate(),
        updateTime: db.serverDate(),
        processTime: null,
        adminId: null,
        adminName: null,
        remark: ''
      }
    });
    
    // 更新用户积分状态为冻结
    await db.collection('users').where({
      openid: openid
    }).update({
      data: {
        frozenPoints: _.inc(points),
        points: _.inc(-points),
        updateTime: db.serverDate()
      }
    });
    
    return {
      code: 0,
      message: '提现申请提交成功',
      withdrawalId: withdrawalResult._id
    };
  } catch (err) {
    console.error('提交提现申请失败:', err);
    return {
      code: 500,
      message: '提交提现申请失败: ' + err.message
    };
  }
}

// 获取用户提现记录
async function getUserWithdrawals(wxContext) {
  const openid = wxContext.OPENID;
  
  try {
    // 确保withdrawals集合存在
    try {
      const collections = await db.listCollections().get();
      const collectionNames = collections.data.map(collection => collection.name);
      
      if (!collectionNames.includes('withdrawals')) {
        console.log('提现记录集合不存在，正在创建...');
        await db.createCollection('withdrawals');
        
        // 如果集合是新创建的，直接返回空记录
        console.log('提现记录集合刚刚创建，返回空记录');
        return {
          code: 0,
          message: '获取提现记录成功',
          withdrawals: []
        };
      }
    } catch (collErr) {
      console.error('检查或创建提现记录集合失败:', collErr);
      // 继续执行，让后续操作决定是否成功
    }
    
    const withdrawals = await db.collection('withdrawals').where({
      openid: openid
    }).orderBy('createTime', 'desc').get();
    
    return {
      code: 0,
      message: '获取提现记录成功',
      withdrawals: withdrawals.data
    };
  } catch (err) {
    console.error('获取用户提现记录失败:', err);
    return {
      code: 500,
      message: '获取用户提现记录失败: ' + err.message
    };
  }
}

// 管理员获取所有提现申请
async function getAllWithdrawals(data) {
  // 添加默认值，避免解构错误
  const { status, page = 1, pageSize = 20 } = data || {};
  
  try {
    let query = {};
    
    // 根据状态筛选
    if (status) {
      query.status = status;
    }
    
    // 计算总数
    const total = await db.collection('withdrawals').where(query).count();
    
    // 分页查询
    const withdrawals = await db.collection('withdrawals')
      .where(query)
      .orderBy('createTime', 'desc')
      .skip((page - 1) * pageSize)
      .limit(pageSize)
      .get();
    
    // 获取用户信息
    const userIds = [...new Set(withdrawals.data.map(item => item.openid))];
    const userInfos = {};
    
    if (userIds.length > 0) {
      const userResults = await db.collection('users').where({
        openid: _.in(userIds)
      }).field({
        openid: true,
        nickName: true,
        avatarUrl: true,
        totalPoints: true
      }).get();
      
      userResults.data.forEach(user => {
        userInfos[user.openid] = user;
      });
    }
    
    // 合并用户信息
    const withdrawalsWithUserInfo = withdrawals.data.map(item => {
      const userInfo = userInfos[item.openid] || {};
      return {
        ...item,
        userInfo
      };
    });
    
    return {
      code: 0,
      message: '获取提现申请成功',
      data: withdrawalsWithUserInfo, // 修改返回字段名为data，与其他接口保持一致
      total: total.total,
      page,
      pageSize
    };
  } catch (err) {
    console.error('获取提现申请失败:', err);
    return {
      code: 500,
      message: '获取提现申请失败: ' + err.message
    };
  }
}

// 管理员处理提现申请
async function processWithdrawal(data) {
  const { withdrawalId, status, remark, adminId, adminName } = data;
  
  if (!withdrawalId || !status || !adminId) {
    return {
      code: 400,
      message: '缺少必要参数'
    };
  }
  
  try {
    // 获取提现申请
    const withdrawalResult = await db.collection('withdrawals').doc(withdrawalId).get();
    
    if (!withdrawalResult.data) {
      return {
        code: 404,
        message: '提现申请不存在'
      };
    }
    
    const withdrawal = withdrawalResult.data;
    
    // 检查状态是否为待处理
    if (withdrawal.status !== 'pending') {
      return {
        code: 400,
        message: '该提现申请已被处理'
      };
    }
    
    // 更新提现申请状态
    await db.collection('withdrawals').doc(withdrawalId).update({
      data: {
        status: status,
        processTime: db.serverDate(),
        adminId: adminId,
        adminName: adminName || '管理员',
        remark: remark || '',
        updateTime: db.serverDate()
      }
    });
    
    // 如果拒绝提现，返还用户积分
    if (status === 'rejected') {
      await db.collection('users').where({
        openid: withdrawal.openid
      }).update({
        data: {
          points: _.inc(withdrawal.points),
          frozenPoints: _.inc(-withdrawal.points),
          updateTime: db.serverDate()
        }
      });
    } 
    // 如果完成提现，清除冻结积分
    else if (status === 'completed') {
      await db.collection('users').where({
        openid: withdrawal.openid
      }).update({
        data: {
          frozenPoints: _.inc(-withdrawal.points),
          updateTime: db.serverDate()
        }
      });
      
      // 获取积分设置，计算实际支出金额
      const settingsResult = await db.collection('system_settings').where({ type: 'points' }).get();
      const pointsSettings = settingsResult.data[0] || {};
      const exchangeRatio = (pointsSettings.settings && pointsSettings.settings.exchangeRatio) || 1;

      // 计算实际支出金额（人民币）
      const actualAmount = withdrawal.points / exchangeRatio;

      // 记录支出（按人民币金额记录）
      await db.collection('expenses').add({
        data: {
          type: 'withdrawal',
          amount: actualAmount, // 使用转换后的人民币金额
          points: withdrawal.points, // 保留积分数量用于显示
          relatedId: withdrawalId,
          description: `用户积分提现 - ${withdrawal.points}积分 (¥${actualAmount.toFixed(2)})`,
          createTime: db.serverDate(),
          adminId: adminId,
          adminName: adminName || '管理员'
        }
      });
    }
    
    return {
      code: 0,
      message: status === 'completed' ? '提现已完成' : '提现已拒绝'
    };
  } catch (err) {
    console.error('处理提现申请失败:', err);
    return {
      code: 500,
      message: '处理提现申请失败: ' + err.message
    };
  }
}

// 获取用户分享记录
async function getUserShareRecords(wxContext) {
  const openid = wxContext.OPENID;
  
  console.log('获取用户分享记录:', { 
    openid,
    wxContext: { OPENID: wxContext.OPENID, APPID: wxContext.APPID }
  });
  
  if (!openid) {
    console.error('获取用户分享记录失败: 缺少用户ID');
    return {
      code: 400,
      message: '获取用户分享记录失败: 缺少用户ID'
    };
  }
  
  try {
    // 确保share_records集合存在
    try {
      // 直接尝试创建share_records集合
      try {
        await db.createCollection('share_records');
        console.log('成功创建share_records集合');
        
        // 如果集合是新创建的，直接返回空记录
        console.log('分享记录集合刚刚创建，返回空记录');
        return {
          code: 0,
          message: '获取分享记录成功',
          shareRecords: [],
          total: 0
        };
      } catch (err) {
        // 如果集合已存在，会抛出错误，这是正常的
        console.log('share_records集合已存在或创建失败:', err.message);
      }
    } catch (collErr) {
      console.error('检查或创建分享记录集合失败:', collErr);
      // 继续执行，让后续操作决定是否成功
    }
    
    // 查询用户分享记录总数
    let countResult;
    try {
      countResult = await db.collection('share_records').where({
        sharer: openid
      }).count();
    } catch (countErr) {
      console.error('查询分享记录总数失败:', countErr);
      // 如果查询失败，可能是集合不存在，返回空记录
      return {
        code: 0,
        message: '获取分享记录成功（集合可能不存在）',
        shareRecords: [],
        total: 0
      };
    }
    
    const total = countResult.total;
    console.log('用户分享记录总数:', total, '用户ID:', openid);
    
    // 如果没有记录，直接返回空数组
    if (total === 0) {
      console.log('用户没有分享记录');
      return {
        code: 0,
        message: '获取分享记录成功',
        shareRecords: [],
        total: 0
      };
    }
    
    // 获取最新的50条记录
    const limit = Math.min(total, 50);
    
    // 查询分享记录
    try {
      const shareRecords = await db.collection('share_records')
        .where({
          sharer: openid
        })
        .orderBy('createTime', 'desc')
        .limit(limit)
        .get();
      
      console.log('获取到用户分享记录:', {
        count: shareRecords.data.length,
        firstRecord: shareRecords.data.length > 0 ? {
          contentId: shareRecords.data[0].contentId,
          contentType: shareRecords.data[0].contentType,
          points: shareRecords.data[0].points,
          createTime: shareRecords.data[0].createTime
        } : null,
        lastRecord: shareRecords.data.length > 0 ? {
          contentId: shareRecords.data[shareRecords.data.length - 1].contentId,
          contentType: shareRecords.data[shareRecords.data.length - 1].contentType,
          points: shareRecords.data[shareRecords.data.length - 1].points,
          createTime: shareRecords.data[shareRecords.data.length - 1].createTime
        } : null
      });
      
      return {
        code: 0,
        message: '获取分享记录成功',
        shareRecords: shareRecords.data,
        total: total
      };
    } catch (queryErr) {
      console.error('查询分享记录失败:', queryErr);
      return {
        code: 0,
        message: '查询分享记录失败，返回空记录',
        shareRecords: [],
        total: 0
      };
    }
  } catch (err) {
    console.error('获取用户分享记录失败:', err);
    return {
      code: 0,
      message: '获取用户分享记录时发生错误，返回空记录',
      shareRecords: [],
      total: 0
    };
  }
}

// 管理员获取所有用户分享记录
async function getAllShareRecords(data) {
  const { openid, contentType, page = 1, pageSize = 20 } = data;
  
  try {
    let query = {};
    
    // 根据用户ID筛选
    if (openid) {
      query.sharer = openid;
    }
    
    // 根据内容类型筛选
    if (contentType) {
      query.contentType = contentType;
    }
    
    // 计算总数
    const total = await db.collection('share_records').where(query).count();
    
    // 分页查询
    const shareRecords = await db.collection('share_records')
      .where(query)
      .orderBy('createTime', 'desc')
      .skip((page - 1) * pageSize)
      .limit(pageSize)
      .get();
    
    // 获取用户信息
    const userIds = [...new Set(shareRecords.data.map(item => item.sharer))];
    const userInfos = {};
    
    if (userIds.length > 0) {
      const userResults = await db.collection('users').where({
        openid: _.in(userIds)
      }).field({
        openid: true,
        nickName: true,
        avatarUrl: true
      }).get();
      
      userResults.data.forEach(user => {
        userInfos[user.openid] = user;
      });
    }
    
    // 合并用户信息
    const recordsWithUserInfo = shareRecords.data.map(item => {
      const userInfo = userInfos[item.sharer] || {};
      return {
        ...item,
        userInfo
      };
    });
    
    return {
      code: 0,
      message: '获取分享记录成功',
      shareRecords: recordsWithUserInfo,
      total: total.total,
      page,
      pageSize
    };
  } catch (err) {
    console.error('获取分享记录失败:', err);
    return {
      code: 500,
      message: '获取分享记录失败: ' + err.message
    };
  }
} 

// 获取积分设置
async function getPointsSettings() {
  try {
    // 从配置集合中获取积分设置
    const settingsResult = await db.collection('system_settings').where({
      type: 'points'
    }).get();
    
    if (settingsResult.data.length === 0) {
      // 如果没有设置，返回默认值
      return {
        code: 0,
        message: '获取积分设置成功（默认值）',
        data: {
          servicePoints: 10,
          articlePoints: 5,
          videoPoints: 8,
          exchangeRatio: 1
        }
      };
    }
    
    return {
      code: 0,
      message: '获取积分设置成功',
      data: settingsResult.data[0].settings
    };
  } catch (err) {
    console.error('获取积分设置失败:', err);
    return {
      code: 500,
      message: '获取积分设置失败: ' + err.message
    };
  }
}

// 保存积分设置
async function savePointsSettings(settings) {
  if (!settings) {
    return {
      code: 400,
      message: '缺少设置参数'
    };
  }
  
  try {
    // 检查是否已存在设置
    const settingsResult = await db.collection('system_settings').where({
      type: 'points'
    }).get();
    
    if (settingsResult.data.length === 0) {
      // 如果不存在，创建新设置
      await db.collection('system_settings').add({
        data: {
          type: 'points',
          settings: settings,
          createTime: db.serverDate(),
          updateTime: db.serverDate()
        }
      });
    } else {
      // 如果存在，更新设置
      await db.collection('system_settings').where({
        type: 'points'
      }).update({
        data: {
          settings: settings,
          updateTime: db.serverDate()
        }
      });
    }
    
    return {
      code: 0,
      message: '保存积分设置成功'
    };
  } catch (err) {
    console.error('保存积分设置失败:', err);
    return {
      code: 500,
      message: '保存积分设置失败: ' + err.message
    };
  }
} 