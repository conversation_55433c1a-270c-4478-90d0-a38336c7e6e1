const app = getApp()

Page({
  data: {
    isLoading: false,
    isSaving: false,
    dataSource: 'cloud', // 数据来源：'cloud'表示微信云开发存储
    launchImages: {
      first: {
        path: '',
        uploaded: false,
        uploading: false,
        progress: 0
      },
      second: {
        path: '',
        uploaded: false,
        uploading: false,
        progress: 0
      }
    }
  },

  onLoad() {
    // 检查管理员登录状态
    if (!app.globalData.isAdmin) {
      this.redirectToLogin()
      return
    }
    
    // 加载广告图片数据
    this.loadLaunchImages()
  },
  
  onShow() {
    // 每次显示页面时检查管理员状态
    if (!app.globalData.isAdmin) {
      this.redirectToLogin()
      return
    }
  },
  
  // 重定向到登录页
  redirectToLogin() {
    wx.showToast({
      title: '请先登录',
      icon: 'none'
    })
    
    setTimeout(() => {
      wx.navigateTo({
        url: '/pages/admin/login'
      })
    }, 1500)
  },
  
  // 加载广告图片
  loadLaunchImages() {
    this.setData({ isLoading: true })
    
    console.log('开始加载广告图片配置')
    
    // 检查网络状态
    wx.getNetworkType({
      success: (res) => {
        console.log('当前网络状态:', res.networkType)
        if (res.networkType === 'none') {
          console.error('网络连接失败')
          this.setData({ isLoading: false })
          wx.showToast({
            title: '网络连接失败，请检查网络设置',
            icon: 'none',
            duration: 2000
          })
          return
        }
        
        // 调用云函数获取广告配置
        wx.cloud.callFunction({
          name: 'launchManager',
          data: {
            action: 'getLaunchImages',
            timestamp: Date.now() // 添加时间戳防止缓存
          },
          success: res => {
            console.log('获取广告配置成功', res)
            
            if (res.result && (res.result.code === 0 || res.result.code === 200)) {
              const { first, second } = res.result.data
              
              console.log('获取到的图片链接:', { first, second })
              
              // 更新图片数据
              const launchImages = { ...this.data.launchImages }
              
              if (first) {
                // 添加时间戳参数防止浏览器缓存图片
                launchImages.first.path = first + (first.includes('?') ? '&' : '?') + 't=' + Date.now()
                launchImages.first.uploaded = true
              } else {
                launchImages.first.path = ''
                launchImages.first.uploaded = false
              }
              
              if (second) {
                // 添加时间戳参数防止浏览器缓存图片
                launchImages.second.path = second + (second.includes('?') ? '&' : '?') + 't=' + Date.now()
                launchImages.second.uploaded = true
              } else {
                launchImages.second.path = ''
                launchImages.second.uploaded = false
              }
              
              this.setData({
                launchImages,
                isLoading: false,
                dataSource: 'cloud' // 设置数据来源为云开发
              })
              
              console.log('页面数据已更新', this.data.launchImages)
              
              // 显示数据来源提示
              wx.showToast({
                title: '数据来自微信云开发存储',
                icon: 'none',
                duration: 2000
              });
            } else {
              console.log('获取配置返回无数据或错误码', res.result)
              
              // 检查是否是集合不存在的错误
              if (res.result && res.result.error && (
                  res.result.error.errCode === -502005 || // 旧的错误码
                  (typeof res.result.error === 'string' && res.result.error.includes('COLLECTION_NOT_EXIST')) // 新的错误格式
                )) {
                this.createLaunchCollection()
              } else {
                this.setData({ isLoading: false })
                
                // 如果返回了404状态码但没有集合不存在的错误，可能是其他原因
                if (res.result && res.result.code === 404) {
                  wx.showToast({
                    title: res.result.message || '加载失败，请重试',
                    icon: 'none'
                  });
                }
              }
            }
          },
          fail: err => {
            console.error('获取广告配置失败', err)
            this.setData({ isLoading: false })
            
            wx.showToast({
              title: '加载失败，请重试',
              icon: 'none'
            })
            
            // 如果是云函数不存在的错误，提示重新部署
            if (err.errCode === -404011) {
              setTimeout(() => {
                wx.showModal({
                  title: '提示',
                  content: '云函数未部署或不存在，请检查云函数部署状态',
                  showCancel: false
                })
              }, 1500)
            }
          }
        })
      },
      fail: (err) => {
        console.error('获取网络状态失败', err)
        this.setData({ isLoading: false })
      }
    })
  },
  
  // 创建广告配置集合
  createLaunchCollection() {
    console.log('尝试创建广告配置集合')
    
    wx.cloud.callFunction({
      name: 'launchManager',
      data: {
        type: 'admin',
        action: 'initLaunchConfig'
      },
      success: res => {
        console.log('创建广告配置集合成功', res)
        
        // 检查返回状态码
        if (res.result && (res.result.code === 0 || res.result.code === 200)) {
          wx.showToast({
            title: '初始化成功',
            icon: 'success'
          })
          
          // 不再重新加载广告图片
          // setTimeout(() => {
          //   this.loadLaunchImages()
          // }, 1000)
        } else {
          wx.showToast({
            title: res.result?.message || '初始化失败，请重试',
            icon: 'none'
          })
        }
        
        this.setData({ isLoading: false })
      },
      fail: err => {
        console.error('创建广告配置集合失败', err)
        this.setData({ isLoading: false })
        
        wx.showToast({
          title: '初始化失败，请重试',
          icon: 'none'
        })
      }
    })
  },
  
  // 上传第一张广告图片
  uploadFirstImage() {
    this.chooseAndUploadImage('first')
  },
  
  // 上传第二张广告图片
  uploadSecondImage() {
    this.chooseAndUploadImage('second')
  },
  
  // 选择并上传图片
  chooseAndUploadImage(type) {
    wx.chooseMedia({
      count: 1,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const tempPath = res.tempFiles[0].tempFilePath
        
        // 更新临时图片路径
        const launchImages = { ...this.data.launchImages }
        launchImages[type].path = tempPath
        launchImages[type].uploading = true
        launchImages[type].progress = 0
        
        this.setData({ launchImages })
        
        // 显示文件大小提示
        const fileSizeMB = (res.tempFiles[0].size / (1024 * 1024)).toFixed(2);
        console.log(`文件大小: ${fileSizeMB}MB`);
        
        if (fileSizeMB > 2) {
          wx.showToast({
            title: `文件较大(${fileSizeMB}MB)，上传可能较慢`,
            icon: 'none',
            duration: 2000
          });
        }
        
        // 实际上传图片到云存储
        this.uploadImageToCloud(type, tempPath)
      }
    })
  },
  
  // 上传图片到云存储
  uploadImageToCloud(type, filePath) {
    // 根据类型确定文件名
    const fileName = type === 'first' ? 'launch_first' : 'launch_second';
    const extension = filePath.match(/\.(\w+)$/)[1].toLowerCase();
    
    console.log(`开始上传图片: ${fileName}.${extension}`);
    
    // 更新上传状态
    const launchImages = { ...this.data.launchImages }
    launchImages[type].uploading = true
    launchImages[type].progress = 10
    this.setData({ launchImages })
    
    // 直接上传到云存储正式路径
    const cloudPath = `launch/${fileName}.${extension}`;
    
    wx.cloud.uploadFile({
      cloudPath: cloudPath,
      filePath: filePath,
      success: res => {
        console.log('上传到云存储成功', res);
        const fileID = res.fileID;
        
        // 更新上传进度
        launchImages[type].progress = 50;
        this.setData({ launchImages });
        
        // 调用云函数更新广告图片配置
        wx.cloud.callFunction({
          name: 'launchManager',
          data: {
            type: 'admin',
            action: 'updateLaunchImage',
            data: {
              type: type,
              fileID: fileID
            }
          },
          success: res => {
            console.log('更新广告图片配置成功', res);
            
            // 更新上传进度
            launchImages[type].progress = 100;
            this.setData({ launchImages });
            
            if (res.result && (res.result.code === 0 || res.result.code === 200)) {
              // 更新完成
              launchImages[type].uploading = false;
              launchImages[type].uploaded = true;
              
              // 如果返回了临时URL，使用它
              if (res.result.data && res.result.data.url) {
                launchImages[type].path = res.result.data.url + (res.result.data.url.includes('?') ? '&' : '?') + 't=' + Date.now();
              } else {
                // 否则使用本地临时路径
                launchImages[type].path = filePath;
              }
              
              this.setData({ launchImages });
              
              wx.showToast({
                title: '上传成功',
                icon: 'success'
              });
            } else {
              // 处理业务逻辑错误
              console.error('上传返回错误', res.result);
              
              // 更新失败处理
              launchImages[type].uploading = false;
              
              this.setData({ launchImages });
              
              wx.showToast({
                title: res.result?.message || '上传失败，请重试',
                icon: 'none'
              });
            }
          },
          fail: err => {
            console.error('更新广告图片配置失败', err);
            
            // 上传失败处理
            launchImages[type].uploading = false;
            launchImages[type].uploaded = false;
            
            this.setData({ launchImages });
            
            // 提供更详细的错误信息
            let errorMsg = '上传失败，请重试';
            
            if (err.errCode === -404011) {
              errorMsg = '云函数未部署或不存在';
            } else if (err.errMsg) {
              errorMsg = err.errMsg.substring(0, 30); // 截取部分错误信息显示
            }
            
            wx.showToast({
              title: errorMsg,
              icon: 'none'
            });
          }
        });
      },
      fail: err => {
        console.error('上传到云存储失败', err);
        
        // 上传失败处理
        launchImages[type].uploading = false;
        
        this.setData({ launchImages });
        
        wx.showToast({
          title: '上传文件失败，请重试',
          icon: 'none'
        });
      }
    });
  },
  
  // 返回管理主页
  navigateBack() {
    wx.navigateBack()
  }
}) 