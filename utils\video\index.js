/**
 * 视频相关工具函数
 */

/* global wx, console */

// 获取全局应用实例
const app = getApp();

// 视频列表缓存时间（30分钟）
const VIDEO_LIST_CACHE_EXPIRE = 30 * 60 * 1000;
// 视频URL缓存时间（1小时）
const VIDEO_URL_CACHE_EXPIRE = 60 * 60 * 1000;

// 缓存键名
const CACHE_KEYS = {
  VIDEO_LIST: 'video_list_cache',
  VIDEO_URL_PREFIX: 'video_url_'
};

/**
 * 获取视频列表，添加缓存机制
 * @param {boolean} forceRefresh 是否强制刷新
 * @returns {Promise<Array>} 视频列表数据
 */
async function getVideoList(forceRefresh = false) {
  try {
    // 检查缓存
    if (!forceRefresh) {
      const cache = wx.getStorageSync(CACHE_KEYS.VIDEO_LIST);
      if (cache && cache.data && cache.expireTime > Date.now()) {
        console.log('[Video] 使用缓存的视频列表');
        
        // 即使使用缓存，也要确保排序正确
        const cachedVideos = cache.data;
        cachedVideos.sort((a, b) => {
          // 如果启用了显示时间排序，优先使用显示时间
          if (a.displayTime && b.displayTime) {
            // 显示时间越新排越前面（降序）
            return new Date(b.displayTime) - new Date(a.displayTime);
          }
          
          // 否则使用sortOrder排序
          const aOrder = typeof a.sortOrder === 'number' ? a.sortOrder : 
                       (typeof a.order === 'number' ? a.order : Number.MAX_SAFE_INTEGER);
          const bOrder = typeof b.sortOrder === 'number' ? b.sortOrder : 
                       (typeof b.order === 'number' ? b.order : Number.MAX_SAFE_INTEGER);
          
          // 升序排列: 数字小的排在前面
          return aOrder - bOrder;
        });
        
        return cachedVideos;
      }
    }
    
    // 使用云函数获取视频列表 - 兼容新旧API格式
    let result;
    try {
      // 首先尝试新的API格式
      const response = await wx.cloud.callFunction({
        name: 'videoManager',
        data: {
          type: 'frontend', // 指定为前端API
          action: 'getVisibleVideos', // 获取可见视频列表
          data: {
            page: 1,
            pageSize: 50, // 获取足够多的视频
            useDisplayTime: forceRefresh // 在强制刷新时使用displayTime排序（下拉刷新时）
          }
        }
      });
      result = response.result;
    } catch (newApiError) {
      console.log('[Video] 新API调用失败，尝试旧API格式:', newApiError.message);
      
      // 如果新API失败，尝试旧的API格式
      try {
        const response = await wx.cloud.callFunction({
          name: 'videoManager',
          data: {
            action: 'getVisibleVideos', // 旧格式
            page: 1,
            pageSize: 50
          }
        });
        result = response.result;
      } catch (oldApiError) {
        console.error('[Video] 旧API也调用失败:', oldApiError.message);
        throw oldApiError;
      }
    }
    
    if (result && result.code === 200) {
      // 新API返回格式: result.data.videos 包含视频列表
      const videos = result.data.videos || [];
      
      // 处理数据，确保字段名称兼容
      const processedVideos = videos.map(item => {
        // 检查是否为新显示的视频（最近从隐藏变为可见的视频）
        const displayTime = item.displayTime || item.updateTime || item.createTime;
        const isRecent = displayTime ? 
                       ((new Date() - new Date(displayTime)) < 24 * 60 * 60 * 1000) : // 24小时内
                       false;

        return {
          id: item._id || item.id, // 确保id字段存在
          title: item.mainTitle || item.title || '', // 兼容旧字段名
          subtitle: item.subTitle || item.subtitle || '', // 兼容旧字段名
          mainTitle: item.mainTitle || item.title || '', // 新字段名
          subTitle: item.subTitle || item.subtitle || '', // 新字段名
          description: item.description || '',
          playCount: item.playCount || 0,
          order: item.sortOrder || item.order || 0, // 兼容旧字段名
          sortOrder: item.sortOrder || item.order || 0, // 新字段名
          coverUrl: item.coverUrl || '', // 封面图URL
          videoUrl: item.videoUrl || '', // 视频URL
          videoKey: item.videoKey || '', // 视频文件Key
          coverKey: item.coverKey || '', // 封面图Key
          detailKey: item.detailKey || '', // 详情图Key
          detailUrl: item.detailUrl || '', // 详情图URL
          detailKeys: item.detailKeys || [], // 多张详情图Keys
          detailUrls: item.detailUrls || [], // 多张详情图URLs
          createTime: item.createTime || new Date().toISOString(),
          updateTime: item.updateTime || item.createTime || new Date().toISOString(),
          displayTime: displayTime || new Date().toISOString(), // 显示时间（用于排序）
          isRecent: isRecent // 是否为最近更新的视频
        };
      });
      
      // 确保视频列表中没有重复的视频（根据id去重）
      const uniqueVideos = [];
      const videoIds = new Set();
      
      for (const video of processedVideos) {
        if (!videoIds.has(video.id)) {
          videoIds.add(video.id);
          uniqueVideos.push(video);
        } else {
          console.log('[Video] 检测到重复视频ID，已过滤:', video.id);
        }
      }
      
      console.log(`[Video] 原始视频数量: ${processedVideos.length}, 去重后: ${uniqueVideos.length}`);
      
      // 对于强制刷新（下拉刷新），优先显示最近更新的视频
      if (forceRefresh) {
        // 使用displayTime排序，最近的排在前面
        uniqueVideos.sort((a, b) => {
          // 如果都有displayTime，按照时间排序（新的在前面）
          if (a.displayTime && b.displayTime) {
            return new Date(b.displayTime) - new Date(a.displayTime);
          }
          
          // 如果只有一个有displayTime，有的排在前面
          if (a.displayTime && !b.displayTime) return -1;
          if (!a.displayTime && b.displayTime) return 1;
          
          // 否则按照sortOrder排序
          const aOrder = typeof a.sortOrder === 'number' ? a.sortOrder : 
                       (typeof a.order === 'number' ? a.order : Number.MAX_SAFE_INTEGER);
          const bOrder = typeof b.sortOrder === 'number' ? b.sortOrder : 
                       (typeof b.order === 'number' ? b.order : Number.MAX_SAFE_INTEGER);
          
          return aOrder - bOrder;
        });
        
        console.log('[Video] 下拉刷新模式：最近更新的视频优先显示');
      } else {
        // 非强制刷新时，仍然使用displayTime排序，但优先级低于sortOrder
        uniqueVideos.sort((a, b) => {
          // 首先按照sortOrder排序
          const aOrder = typeof a.sortOrder === 'number' ? a.sortOrder : 
                       (typeof a.order === 'number' ? a.order : Number.MAX_SAFE_INTEGER);
          const bOrder = typeof b.sortOrder === 'number' ? b.sortOrder : 
                       (typeof b.order === 'number' ? b.order : Number.MAX_SAFE_INTEGER);
          
          // 如果sortOrder相同，再按displayTime排序
          if (aOrder === bOrder && a.displayTime && b.displayTime) {
            return new Date(b.displayTime) - new Date(a.displayTime);
          }
          
          // 否则按照sortOrder排序
          return aOrder - bOrder;
        });
      }
      
      // 更新缓存
      wx.setStorageSync(CACHE_KEYS.VIDEO_LIST, {
        data: uniqueVideos,
        expireTime: Date.now() + VIDEO_LIST_CACHE_EXPIRE
      });
      
      console.log('[Video] 已获取最新视频列表并排序');
      return uniqueVideos;
    }
    
    console.error('[Video] 获取视频列表失败:', result);
    return [];
  } catch (error) {
    console.error('[Video] 获取视频列表失败:', error);
    return [];
  }
}

/**
 * 获取视频URL
 * @param {string} videoId 视频ID
 * @param {string} baseId 基础ID
 * @param {string} fileKey 文件Key
 * @param {boolean} force 是否强制刷新URL（不使用缓存）
 * @returns {Promise<string>} 视频URL
 */
async function getVideoUrl(videoId, baseId = '', fileKey = '', force = false) {
  const app = getApp();
  const debugMode = app && app.globalData && app.globalData.debugMode;
  
  // 确保至少有一个参数
  if (!videoId && !baseId && !fileKey) {
    console.error('[视频URL] 无效的参数：videoId、baseId和fileKey不能同时为空');
    return '';
  }
  
  // 确定目标Key
  const targetKey = fileKey || (baseId ? `${baseId}.mp4` : `${videoId}.mp4`);
  
  try {
    // 检查缓存
    const cacheKey = `${CACHE_KEYS.VIDEO_URL_PREFIX}${targetKey}`;
    if (debugMode) {
      console.log('[视频URL] 缓存键:', cacheKey);
    }
    
    // 如果不强制刷新，尝试使用缓存
    if (!force) {
      const cache = wx.getStorageSync(cacheKey);
      if (cache && cache.url && cache.expireTime > Date.now()) {
        if (debugMode) {
          console.log(`[视频URL] 使用缓存的视频URL: ${targetKey}`);
        }
        return cache.url;
      }
    } else if (debugMode) {
      console.log(`[视频URL] 强制刷新，跳过缓存: ${targetKey}`);
    }
    
    if (debugMode) {
      console.log('[视频URL] 缓存不存在或已过期，调用云函数获取...');
    }
    
    // 从云函数获取URL - 兼容新旧API格式
    let result;
    try {
      // 首先尝试新的API格式
      const response = await wx.cloud.callFunction({
        name: 'videoManager',
        data: {
          type: 'frontend', // 指定为前端API
          action: 'getVideoDetail', // 获取视频详情
          data: {
            id: videoId, // 使用视频ID
            fileKey: targetKey, // 同时提供fileKey以兼容
            forceRefresh: force // 添加强制刷新参数
          }
        }
      });
      result = response.result;
    } catch (newApiError) {
      if (debugMode) {
        console.log('[视频URL] 新API调用失败，尝试旧API格式:', newApiError.message);
      }
      
      // 如果新API失败，尝试旧的API格式
      try {
        const response = await wx.cloud.callFunction({
          name: 'videoManager',
          data: {
            action: 'getVideoUrl', // 旧格式
            videoId: videoId,
            baseId: baseId,
            fileKey: targetKey
          }
        });
        result = response.result;
      } catch (oldApiError) {
        console.error('[视频URL] 新旧API都调用失败:', oldApiError.message);
        throw oldApiError;
      }
    }
    
    if (debugMode) {
      console.log('[视频URL] 云函数返回结果:', result ? '成功' : '失败');
    }
    
    if (result && result.code === 200 && result.data && result.data.videoUrl) {
      const videoUrl = result.data.videoUrl;
      if (debugMode) {
        console.log('[视频URL] 成功获取视频URL');
      }
      
      // 更新缓存
      wx.setStorageSync(cacheKey, {
        url: videoUrl,
        expireTime: Date.now() + VIDEO_URL_CACHE_EXPIRE
      });
      
      return videoUrl;
    }
    
    console.error('[视频URL] 云函数返回异常结果:', result ? result.message : '未知错误');
    return '';
  } catch (error) {
    console.error(`[视频URL] 获取视频URL失败: ${targetKey}`, error.message || error);
    return '';
  }
}

/**
 * 获取视频详情图片
 * @param {string} videoId 视频ID
 * @returns {Promise<Array>} 详情图片URL数组
 */
async function getVideoDetailImages(videoId) {
  const debugMode = app && app.globalData && app.globalData.debugMode;
  
  if (!videoId) {
    console.error('[详情图片] 无效的视频ID');
    return [];
  }
  
  try {
    if (debugMode) {
      console.log(`[详情图片] 开始获取视频 ${videoId} 的详情图片`);
    }
    
    // 检查缓存
    const cacheKey = `detail_images_${videoId}`;
    const cache = wx.getStorageSync(cacheKey);
    if (cache && cache.urls && cache.expireTime > Date.now()) {
      if (debugMode) {
        console.log(`[详情图片] 使用缓存的详情图片: ${videoId}`);
      }
      return cache.urls;
    }
    
    // 从云函数获取详情
    const { result } = await wx.cloud.callFunction({
      name: 'videoManager',
      data: {
        type: 'frontend',
        action: 'getVideoDetail',
        data: {
          id: videoId
        }
      }
    });
    
    if (result && result.code === 200 && result.data) {
      // 获取详情图片URL
      let detailUrls = [];
      
      // 处理多张详情图片
      if (Array.isArray(result.data.detailUrls) && result.data.detailUrls.length > 0) {
        detailUrls = result.data.detailUrls;
      } 
      // 处理单张详情图片
      else if (result.data.detailUrl) {
        detailUrls = [result.data.detailUrl];
      }
      
      if (debugMode) {
        console.log(`[详情图片] 成功获取视频 ${videoId} 的详情图片:`, detailUrls.length);
      }
      
      // 更新缓存
      wx.setStorageSync(cacheKey, {
        urls: detailUrls,
        expireTime: Date.now() + VIDEO_URL_CACHE_EXPIRE
      });
      
      return detailUrls;
    }
    
    console.error('[详情图片] 获取详情图片失败:', result ? result.message : '未知错误');
    return [];
  } catch (error) {
    console.error(`[详情图片] 获取详情图片失败: ${videoId}`, error.message || error);
    return [];
  }
}

/**
 * 清理过期的视频缓存
 */
function cleanExpiredCache() {
  try {
    // 清理视频列表缓存
    const listCache = wx.getStorageSync(CACHE_KEYS.VIDEO_LIST);
    if (listCache && listCache.expireTime < Date.now()) {
      wx.removeStorageSync(CACHE_KEYS.VIDEO_LIST);
    }
    
    // 清理视频URL缓存
    const storageInfo = wx.getStorageInfoSync();
    const keys = storageInfo.keys || [];
    
    keys.forEach(key => {
      if (key.startsWith(CACHE_KEYS.VIDEO_URL_PREFIX)) {
        const cache = wx.getStorageSync(key);
        if (cache && cache.expireTime < Date.now()) {
          wx.removeStorageSync(key);
        }
      }
    });
  } catch (error) {
    console.error('[Video] 清理缓存失败:', error.message || error);
  }
}

module.exports = {
  getVideoList,
  getVideoUrl,
  getVideoDetailImages,
  cleanExpiredCache
}; 