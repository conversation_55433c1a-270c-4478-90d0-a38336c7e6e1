const app = getApp()

Page({
  data: {
    // 预约详情
    appointment: null,
    
    // 状态映射
    statusMap: {
      'pending': '待确认',
      'confirmed': '已确认',
      'completed': '已完成',
      'cancelled': '已取消',
      'rejected': '已拒绝'
    }
  },

  onLoad() {
    // 从本地存储获取预约详情
    const appointment = wx.getStorageSync('appointment_detail');
    console.log('获取到的预约详情：', appointment);
    
    if (!appointment) {
      wx.showToast({
        title: '预约详情获取失败',
        icon: 'none'
      });
      
      // 返回上一页
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
      return;
    }
    
    this.setData({
      appointment: appointment
    });
  },
  
  // 取消预约
  cancelAppointment() {
    const { _id } = this.data.appointment;
    
    wx.showModal({
      title: '取消预约',
      content: '确定要取消此预约吗？',
      confirmText: '确定',
      confirmColor: '#E02020',
      cancelText: '取消',
      cancelColor: '#999999',
      success: (res) => {
        if (res.confirm) {
          wx.showLoading({
            title: '处理中...',
            mask: true
          });
          
          wx.cloud.callFunction({
            name: 'appointmentManager',
            data: {
              action: 'cancelAppointment',
              type: 'frontend',
              appointmentId: _id
            },
            success: res => {
              console.log('取消预约结果：', res);
              if (res.result && res.result.code === 0) {
                wx.showToast({
                  title: '预约已取消',
                  icon: 'success'
                });
                
                // 清除本地存储的预约标记
                wx.removeStorageSync('hasActiveAppointment');
                // 清除全局状态
                if (getApp().globalData) {
                  getApp().globalData.hasActiveAppointment = false;
                }
                
                // 更新状态
                const appointment = this.data.appointment;
                appointment.status = 'cancelled';
                
                this.setData({
                  appointment: appointment
                });
                
                // 刷新上一页列表
                const pages = getCurrentPages();
                const prevPage = pages[pages.length - 2];
                if (prevPage && prevPage.fetchAppointmentList) {
                  prevPage.fetchAppointmentList();
                }
              } else {
                wx.showToast({
                  title: res.result && res.result.message ? res.result.message : '操作失败',
                  icon: 'none'
                });
              }
            },
            fail: err => {
              console.error('取消预约失败：', err);
              wx.showToast({
                title: '网络错误，请重试',
                icon: 'none'
              });
            },
            complete: () => {
              wx.hideLoading();
            }
          });
        }
      }
    });
  },
  
  // 再次预约
  reAppointment() {
    // 跳转到预约页面
    wx.switchTab({
      url: '/pages/appointment/appointment'
    });
  },
  
  // 返回上一页
  goBack() {
    wx.navigateBack();
  },
  
  // 预览图片
  previewImage(e) {
    const index = e.currentTarget.dataset.index;
    wx.previewImage({
      current: this.data.appointment.imageUrls[index],
      urls: this.data.appointment.imageUrls
    });
  },
  
  // 格式化时间显示
  formatTime(dateStr) {
    if (!dateStr) return '';
    
    try {
      const date = new Date(dateStr);
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const day = date.getDate().toString().padStart(2, '0');
      const hours = date.getHours().toString().padStart(2, '0');
      const minutes = date.getMinutes().toString().padStart(2, '0');
      
      return `${year}-${month}-${day} ${hours}:${minutes}`;
    } catch (e) {
      console.error('日期格式化错误', e);
      return dateStr;
    }
  }
}) 