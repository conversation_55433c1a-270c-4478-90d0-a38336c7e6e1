// ================== 主页面控制器 (重构版本) ==================
// @feature: 主页面模块化控制器
// @version: 2.0.0
// @description: 轻量级控制器，协调各功能模块

/* global Page, wx, getApp, console */

// 引入核心模块
const MainController = require("./modules/main-controller");
const ApiCompatibility = require("./modules/api-compatibility");
const UnifiedErrorHandler = require("./modules/unified-error-handler");

Page({
  data: {
    userInfo: null,
    videoList: [],
    loading: true, // 初始为true，显示骨架屏
    firstLoading: true,
    hasMore: true,
    page: 0,
    pageSize: 4,
    urlCache: {},
    urlFetchingIds: [],
    lastRefreshTime: 0,
    isFromLaunch: false,
    showContent: true, // 初始为true，确保内容区域显示
    forceHideNavbar: false,
    isRefreshing: false,
    navigationHeight: 0,
    searchKeyword: "",
    isSearching: false,
    searchFocused: false,
    currentPlayingVideo: null,
    originalVideoList: null,
    startX: null,
    startY: null,
    // 视频详情弹窗相关
    showVideoDetail: false,
    selectedVideoDetail: null,

    // 指向广告相关
    showTargetAd: false,
    targetAdData: {
      imageUrl: '',
      jumpUrl: ''
    },

    app: getApp(),
  },

  onLoad: function (options) {
    try {
      console.log("[Index] 开始初始化页面, 参数:", options);

      // 引入紧急修复模块
      const emergencyFix = require("./emergency-fix");

      // 先执行紧急修复，确保页面基本可用
      emergencyFix.emergencyFix(this);

      // 统一错误处理将由主控制器初始化，避免重复创建
      this.errorHandler = null;

      // 初始化主控制器
      this.mainController = new MainController(this);
      this.mainController.init();

      // 初始化API兼容层
      this.apiCompatibility = new ApiCompatibility(this.mainController, this);
      this.apiCompatibility.init();

      // 确保API兼容性模块能找到MainController
      if (
        this.apiCompatibility.mainController &&
        this.apiCompatibility.mainController.modules
      ) {
        this.apiCompatibility.mainController.modules.set(
          "MainController",
          this.mainController
        );
      }

      console.log("[Index] 主页面控制器初始化完成");

      // 处理页面参数（如分享参数）
      if (options && Object.keys(options).length > 0) {
        console.log("[Index] 处理页面参数:", options);
        if (
          this.mainController &&
          typeof this.mainController.handlePageLoad === "function"
        ) {
          this.mainController.handlePageLoad(options);
        }
      }

      // 开始加载数据（延迟执行，避免与紧急修复冲突）
      setTimeout(() => {
        this.loadInitialData();
      }, 1000);

      // 监听指向广告显示事件
      this.setupTargetAdListener();

      // 启动紧急修复检查（减少频率，避免过度检查）
      setTimeout(() => {
        emergencyFix.checkAndFix(this);
      }, 5000); // 5秒后检查一次即可
    } catch (error) {
      console.error("[Index] 初始化失败:", error);

      // 如果初始化失败，直接执行紧急修复
      try {
        const emergencyFix = require("./emergency-fix");
        emergencyFix.emergencyFix(this);
      } catch (fixError) {
        console.error("[Index] 紧急修复也失败:", fixError);

        // 最后的降级处理 - 也添加延迟以确保动画效果
        this.setData({
          loading: false,
          firstLoading: false,
        });

        // 延迟显示内容，让用户能看到0.8秒的渐进动画
        setTimeout(() => {
          this.setData({
            showContent: true,
          });
        }, 100);
      }

      wx.showToast({
        title: "页面初始化失败",
        icon: "none",
      });
    }
  },

  // 加载初始数据
  loadInitialData: function () {
    try {
      // 检查紧急修复是否已完成
      if (this.data.emergencyFixCompleted) {
        // console.log("[Index] 紧急修复已完成，跳过正常数据加载");
        return;
      }

      console.log("[Index] 开始加载初始数据");

      // 设置基本状态
      this.setData({
        loading: true,
        firstLoading: true,
        showContent: true, // 确保内容区域显示，包含骨架屏
        videoList: [], // 确保初始为空数组，触发骨架屏
      });

      // 延迟一点时间确保模块初始化完成
      setTimeout(() => {
        // 再次检查紧急修复状态
        if (this.data.emergencyFixCompleted) {
          console.log("[Index] 紧急修复已完成，取消正常数据加载");
          return;
        }

        // 调用视频列表模块加载数据
        if (this.apiCompatibility) {
          this.apiCompatibility
            .handleMethod("loadVideoList", [false])
            .then(() => {
              console.log("[Index] 数据加载完成");
              // 数据加载完成，关闭骨架屏
              this.setData({
                loading: false,
                firstLoading: false,
                showContent: true, // 确保内容显示
              });
            })
            .catch((error) => {
              console.error("[Index] 数据加载失败:", error);
              // 即使失败也要关闭骨架屏并显示内容
              this.setData({
                loading: false,
                firstLoading: false,
                showContent: true,
              });
            });
        } else if (this.mainController) {
          // 直接调用主控制器的数据加载方法
          this.mainController.loadInitialData();
        } else {
          // 降级处理 - 添加延迟以确保动画效果
          console.warn("[Index] 模块未就绪，使用降级处理");
          this.setData({
            loading: false,
            firstLoading: false,
          });

          // 延迟显示内容，让用户能看到0.8秒的渐进动画
          setTimeout(() => {
            this.setData({
              showContent: true,
            });
          }, 100);
        }
      }, 500); // 增加延迟时间，确保模块完全初始化

      // 添加超时保护，防止永远卡在加载状态 - 也添加延迟以确保动画效果
      setTimeout(() => {
        if (this.data.loading || this.data.firstLoading) {
          console.warn("[Index] 检测到加载超时，强制显示内容");
          this.setData({
            loading: false,
            firstLoading: false,
          });

          // 延迟显示内容，让用户能看到0.8秒的渐进动画
          setTimeout(() => {
            this.setData({
              showContent: true,
            });
          }, 100);
        }
      }, 8000); // 8秒超时保护
    } catch (error) {
      console.error("[Index] 加载初始数据失败:", error);
      // 即使出错也添加延迟以确保动画效果
      this.setData({
        loading: false,
        firstLoading: false,
      });

      // 延迟显示内容，让用户能看到0.8秒的渐进动画
      setTimeout(() => {
        this.setData({
          showContent: true,
        });
      }, 100);
    }
  },

  onShow: function () {
    try {
      // 完全按照gallery页面的方式实现
      const app = getApp();
      if (app) {
        app.globalData.currentTabIndex = 1; // index页面对应索引1
      }

      // 更新自定义tabBar状态 - 与gallery页面完全一致的实现
      if (typeof this.getTabBar === "function") {
        this.getTabBar().setData({
          selected: 1, // index页面对应的索引为1（展示）
        });
      }

      // 立即隐藏导航栏，防止闪现
      this.setData({
        forceHideNavbar: true,
      });

      // 检查是否有可用数据
      const hasVideoData =
        this.data.videoList && this.data.videoList.length > 0;
      const hasGlobalVideoData =
        app &&
        app.globalData &&
        app.globalData.videoList &&
        app.globalData.videoList.length > 0;
      const hasEmergencyData = this.data.emergencyFixCompleted && hasVideoData;
      const isVideoDataReady = app && app.globalData && app.globalData.videoDataReady;

      console.log("[Index] onShow数据检查:", {
        hasVideoData,
        hasGlobalVideoData,
        hasEmergencyData,
        isVideoDataReady,
        videoListLength: this.data.videoList ? this.data.videoList.length : 0,
        globalVideoListLength: hasGlobalVideoData
          ? app.globalData.videoList.length
          : 0,
      });

      // 确定要使用的视频数据
      const videoListToUse = hasVideoData
        ? this.data.videoList
        : hasGlobalVideoData
        ? app.globalData.videoList
        : [];

      // 始终确保内容区域显示
      this.setData({
        showContent: true,
        isFromTab: true,
      });

      // 如果数据已准备好或有数据，显示真实内容
      if (isVideoDataReady || videoListToUse.length > 0) {
        this.setData({
          loading: false,
          firstLoading: false,
          videoList: videoListToUse,
        });
        console.log("[Index] 显示真实内容，视频数量:", videoListToUse.length);
      } else {
        // 没有数据时显示骨架屏
        this.setData({
          loading: true,
          firstLoading: true,
          videoList: [], // 空数组，确保骨架屏显示
        });
        console.log("[Index] 显示骨架屏，开始加载数据");

        // 立即尝试加载数据
        this.loadVideoDataIfNeeded();
      }

      // 主控制器相关逻辑
      if (this.mainController) {
        this.mainController.onShow();
      }
    } catch (error) {
      if (this.errorHandler) {
        this.errorHandler.handleError(error, "onShow");
      } else {
        console.error("[Index] onShow错误:", error);
      }
    }
  },

  /**
   * 如果需要，加载视频数据
   */
  loadVideoDataIfNeeded() {
    if (this.apiCompatibility) {
      this.apiCompatibility
        .handleMethod("loadVideoList", [false])
        .then(() => {
          console.log("[Index] 骨架屏数据加载完成");
          // 数据加载完成后，关闭loading状态
          this.setData({
            loading: false,
            firstLoading: false,
          });
        })
        .catch((error) => {
          console.error("[Index] 骨架屏数据加载失败:", error);
          // 即使失败也要关闭loading状态
          this.setData({
            loading: false,
            firstLoading: false,
          });
        });
    }
  },

  onHide: function () {
    try {
      if (this.mainController) {
        this.mainController.onHide();
      }
    } catch (error) {
      if (this.errorHandler) {
        this.errorHandler.handleError(error, "onHide");
      } else {
        console.error("[Index] onHide错误:", error);
      }
    }
  },

  onUnload: function () {
    try {
      // 销毁主控制器
      if (this.mainController) {
        this.mainController.destroy();
        this.mainController = null;
      }

      // 销毁错误处理器
      if (this.errorHandler) {
        this.errorHandler.destroy();
        this.errorHandler = null;
      }
    } catch (error) {
      console.error("[Index] onUnload错误:", error);
    }
  },

  // 检查并显示内容
  checkAndShowContent: function () {
    try {
      const app = getApp();
      const hasVideoData =
        this.data.videoList && this.data.videoList.length > 0;
      const hasGlobalVideoData =
        app &&
        app.globalData &&
        app.globalData.videoList &&
        app.globalData.videoList.length > 0;
      const hasEmergencyData = this.data.emergencyFixCompleted && hasVideoData;

      console.log("[Index] 延迟检查数据状态:", {
        hasVideoData,
        hasGlobalVideoData,
        hasEmergencyData,
        emergencyFixCompleted: this.data.emergencyFixCompleted,
        showContent: this.data.showContent,
      });

      if (hasVideoData || hasGlobalVideoData || hasEmergencyData) {
        const videoListToUse = hasVideoData
          ? this.data.videoList
          : hasGlobalVideoData
          ? app.globalData.videoList
          : this.data.videoList;

        // 先设置数据，然后延迟显示内容以确保渐进动画效果
        this.setData({
          firstLoading: false,
          loading: false,
          videoList: videoListToUse,
        });

        // 立即显示内容，不要延迟
        this.setData({
          showContent: true,
        });

        console.log(
          "[Index] 立即检查后显示内容，视频数量:",
          videoListToUse.length
        );
      } else {
        // 如果仍然没有数据，立即再次检查
        setTimeout(() => {
          if (!this.data.showContent) {
            this.checkAndShowContent();
          }
        }, 10); // 减少到10ms，几乎立即检查
      }
    } catch (error) {
      console.error("[Index] checkAndShowContent错误:", error);
    }
  },

  // ================== 页面事件方法 ==================
  // 这些方法需要直接定义，因为小程序框架会直接调用

  onPullDownRefresh: function () {
    try {
      if (this.apiCompatibility) {
        return this.apiCompatibility.handleMethod(
          "onPullDownRefresh",
          arguments
        );
      } else {
        // 降级处理
        wx.stopPullDownRefresh();
      }
    } catch (error) {
      console.error("[Index] onPullDownRefresh错误:", error);
      wx.stopPullDownRefresh();
    }
  },

  onReachBottom: function () {
    try {
      if (this.apiCompatibility) {
        return this.apiCompatibility.handleMethod("onReachBottom", arguments);
      }
    } catch (error) {
      console.error("[Index] onReachBottom错误:", error);
    }
  },

  onShareAppMessage: function (e) {
    try {
      if (this.apiCompatibility) {
        return this.apiCompatibility.handleMethod(
          "onShareAppMessage",
          arguments
        );
      } else {
        // 降级处理
        return {
          title: "分享视频",
          path: "/pages/index/index",
        };
      }
    } catch (error) {
      console.error("[Index] onShareAppMessage错误:", error);
      return {
        title: "分享视频",
        path: "/pages/index/index",
      };
    }
  },

  // 导航栏控制方法
  onNavbarControl: function (e) {
    try {
      if (this.apiCompatibility) {
        return this.apiCompatibility.handleMethod("onNavbarControl", arguments);
      }
    } catch (error) {
      console.error("[Index] onNavbarControl错误:", error);
    }
  },

  // 根据位置显示视频详情（新的灵活方式）
  showVideoDetailByPosition(position, title) {
    console.log(`[Index] 尝试显示第${position}个视频，标题: ${title}`);

    const { videoList } = this.data;
    const app = getApp();

    // 尝试从当前页面数据查找
    let video = null;
    if (videoList && videoList.length >= position) {
      video = videoList[position - 1]; // 数组索引从0开始，位置从1开始
      console.log('[Index] 从当前页面数据找到视频:', video.title || video.mainTitle);
    }

    // 如果没找到，从全局数据查找
    if (!video && app.globalData.videoList && app.globalData.videoList.length >= position) {
      video = app.globalData.videoList[position - 1];
      console.log('[Index] 从全局数据找到视频:', video.title || video.mainTitle);
    }

    // 如果还是没找到，尝试按标题匹配
    if (!video) {
      if (videoList && videoList.length > 0) {
        video = videoList.find(item =>
          (item.title && item.title.includes(title)) ||
          (item.mainTitle && item.mainTitle.includes(title))
        );
        if (video) {
          console.log('[Index] 通过标题匹配找到视频:', video.title || video.mainTitle);
        }
      }

      if (!video && app.globalData.videoList && app.globalData.videoList.length > 0) {
        video = app.globalData.videoList.find(item =>
          (item.title && item.title.includes(title)) ||
          (item.mainTitle && item.mainTitle.includes(title))
        );
        if (video) {
          console.log('[Index] 从全局数据通过标题匹配找到视频:', video.title || video.mainTitle);
        }
      }
    }

    if (video) {
      console.log('[Index] 找到视频，显示详情:', {
        _id: video._id,
        title: video.title || video.mainTitle,
        position: position
      });

      // 确保视频数据有正确的ID字段供详情弹窗使用
      const videoWithId = {
        ...video,
        id: video.id || video.baseId || video._id,
        baseId: video.baseId || video.id || video._id
      };

      this.setData({
        selectedVideoDetail: videoWithId,
        showVideoDetail: true
      });
    } else {
      console.warn(`[Index] 未找到第${position}个视频或标题包含"${title}"的视频`);
      console.log('[Index] 当前视频列表长度:', videoList ? videoList.length : 0);
      console.log('[Index] 全局视频列表长度:', app.globalData.videoList ? app.globalData.videoList.length : 0);

      wx.showToast({
        title: '视频不存在',
        icon: 'none'
      });
    }
  },

  // 视频详情弹窗关闭事件
  onVideoDetailClose: function() {
    try {
      console.log("[Index] 视频详情弹窗关闭");
      this.setData({
        showVideoDetail: false,
        selectedVideoDetail: null
      });

      // 如果有视频播放器模块，通知其处理关闭事件
      if (this.videoPlayerModule && typeof this.videoPlayerModule.onVideoDetailClose === 'function') {
        this.videoPlayerModule.onVideoDetailClose();
      }
    } catch (error) {
      console.error("[Index] onVideoDetailClose错误:", error);
    }
  },

  // 视频全屏状态变化事件
  onVideoFullscreenChange: function(e) {
    try {
      console.log("[Index] 视频全屏状态变化:", e.detail);
      // 如果有视频播放器模块，通知其处理全屏变化事件
      if (this.videoPlayerModule && typeof this.videoPlayerModule.onVideoFullscreenChange === 'function') {
        this.videoPlayerModule.onVideoFullscreenChange(e);
      }
    } catch (error) {
      console.error("[Index] onVideoFullscreenChange错误:", error);
    }
  },

  // 触摸事件处理
  catchHorizontalMove: function (e) {
    try {
      if (this.apiCompatibility) {
        return this.apiCompatibility.handleMethod(
          "catchHorizontalMove",
          arguments
        );
      }
    } catch (error) {
      console.error("[Index] catchHorizontalMove错误:", error);
    }
  },

  // ================== 指向广告相关方法 ==================

  // 设置指向广告事件监听
  setupTargetAdListener() {
    const app = getApp();
    if (app.globalData.eventCenter) {
      app.globalData.eventCenter.on('showTargetAd', (data) => {
        console.log('[Index] 收到显示指向广告事件:', data);

        // 只在当前页面是目标页面时显示广告
        const currentRoute = 'pages/index/index';
        if (!data.targetPage || data.targetPage === currentRoute) {
          this.setData({
            showTargetAd: true,
            targetAdData: {
              imageUrl: data.imageUrl || '',
              jumpUrl: data.jumpUrl || ''
            }
          });
        }
      });

      // 监听全局关闭广告事件
      app.globalData.eventCenter.on('closeTargetAdGlobally', () => {
        console.log('[Index] 收到全局关闭广告事件');
        this.setData({
          showTargetAd: false,
          targetAdData: {
            imageUrl: '',
            jumpUrl: ''
          }
        });
      });

      // 监听显示视频详情事件（来自指向广告跳转）
      app.globalData.eventCenter.on('showVideoDetail', (data) => {
        console.log('[Index] 收到显示视频详情事件:', data);
        if (data.position && data.title) {
          // 新的位置+标题方式
          this.showVideoDetailByPosition(data.position, data.title);
        } else if (data.videoId) {
          // 兼容旧的ID方式
          this.showVideoDetailById(data.videoId);
        }
      });
    }
  },

  // 根据视频ID显示视频详情
  showVideoDetailById(videoId, retryCount = 0) {
    const app = getApp();
    const maxRetries = 3;

    console.log(`[Index] 尝试显示视频详情，ID: ${videoId}, 重试次数: ${retryCount}`);

    // 尝试从多个数据源查找视频
    let video = null;

    // 1. 从当前页面数据查找
    if (this.data.videoList && this.data.videoList.length > 0) {
      video = this.data.videoList.find(item =>
        item._id === videoId ||
        item.id === videoId ||
        item.baseId === videoId
      );
      if (video) {
        console.log('[Index] 从当前页面数据找到视频:', video.title || video.mainTitle);
      }
    }

    // 2. 如果没找到，从全局数据查找
    if (!video && app.globalData.videoList && app.globalData.videoList.length > 0) {
      video = app.globalData.videoList.find(item =>
        item._id === videoId ||
        item.id === videoId ||
        item.baseId === videoId
      );
      if (video) {
        console.log('[Index] 从全局数据找到视频:', video.title || video.mainTitle);
      }
    }

    if (video) {
      console.log('[Index] 找到视频，显示详情:', {
        _id: video._id,
        id: video.id,
        baseId: video.baseId,
        title: video.title || video.mainTitle,
        hasVideoUrl: !!video.videoUrl,
        hasCoverUrl: !!video.coverUrl,
        fullVideoData: video
      });

      // 确保视频数据有正确的ID字段供详情弹窗使用
      const videoWithId = {
        ...video,
        id: video.id || video.baseId || video._id, // 确保有id字段
        baseId: video.baseId || video.id || video._id // 确保有baseId字段
      };

      this.setData({
        selectedVideoDetail: videoWithId,
        showVideoDetail: true
      });
    } else {
      console.warn('[Index] 未找到指定的视频:', videoId);
      console.log('[Index] 数据状态:', {
        currentVideoListLength: this.data.videoList ? this.data.videoList.length : 0,
        globalVideoListLength: app.globalData.videoList ? app.globalData.videoList.length : 0,
        retryCount: retryCount
      });

      // 输出当前视频列表中的所有ID，便于调试
      if (this.data.videoList && this.data.videoList.length > 0) {
        console.log('[Index] 当前视频列表中的所有ID:');
        this.data.videoList.forEach((video, index) => {
          console.log(`  ${index}: _id=${video._id}, id=${video.id}, baseId=${video.baseId}, title=${video.title || video.mainTitle}`);
        });
      }

      if (app.globalData.videoList && app.globalData.videoList.length > 0) {
        console.log('[Index] 全局视频列表中的所有ID:');
        app.globalData.videoList.forEach((video, index) => {
          console.log(`  ${index}: _id=${video._id}, id=${video.id}, baseId=${video.baseId}, title=${video.title || video.mainTitle}`);
        });
      }

      // 如果数据还没加载完成且未达到最大重试次数，等待后重试
      if (retryCount < maxRetries &&
          ((!this.data.videoList || this.data.videoList.length === 0) ||
           (!app.globalData.videoList || app.globalData.videoList.length === 0))) {
        console.log(`[Index] 视频数据可能还在加载，${1 + retryCount}秒后重试`);
        setTimeout(() => {
          this.showVideoDetailById(videoId, retryCount + 1);
        }, (1 + retryCount) * 1000); // 递增延迟时间
        return;
      }

      // 如果有数据但找不到指定视频，显示错误
      wx.showToast({
        title: '视频不存在',
        icon: 'none'
      });
    }
  },

  // 指向广告关闭事件
  onTargetAdClose() {
    console.log('[Index] 指向广告关闭');
    this.setData({
      showTargetAd: false,
      targetAdData: {
        imageUrl: '',
        jumpUrl: ''
      }
    });
  },

  // ================== API 兼容层 ==================
  // 其他方法已经在初始化时通过API兼容层自动创建了代理
});
