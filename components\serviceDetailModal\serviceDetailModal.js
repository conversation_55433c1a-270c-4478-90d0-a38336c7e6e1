// components/serviceDetailModal/serviceDetailModal.js
Component({
  properties: {
    // 详情图片URL
    detailImageUrl: {
      type: String,
      value: ''
    }
  },

  data: {
    show: false,
    showAnimation: false
  },

  lifetimes: {
    attached() {
      console.log('serviceDetailModal组件已加载');
    }
  },

  methods: {
    // 显示弹窗
    showModal(detailImageUrl) {
      console.log('serviceDetailModal.showModal被调用，参数:', detailImageUrl);

      if (!detailImageUrl) {
        wx.showToast({
          title: '暂无详情图片',
          icon: 'none'
        });
        return;
      }

      console.log('准备显示弹窗');
      this.setData({
        detailImageUrl: detailImageUrl,
        show: true
      });

      // 延迟显示动画，确保DOM已渲染
      setTimeout(() => {
        this.setData({
          showAnimation: true
        });
        console.log('弹窗动画已启动');
      }, 50);
    },

    // 关闭弹窗
    closeModal() {
      this.setData({
        showAnimation: false
      });

      // 等待动画完成后隐藏弹窗
      setTimeout(() => {
        this.setData({
          show: false
        });
      }, 300);
    },

    // 阻止点击图片时关闭弹窗
    preventClose(e) {
      // 添加安全检查，确保事件对象存在且有stopPropagation方法
      if (e && typeof e.stopPropagation === 'function') {
        e.stopPropagation();
      }
    },

    // 图片加载成功
    onImageLoad() {
      console.log('详情图片加载成功');
    },

    // 图片加载失败
    onImageError() {
      console.error('详情图片加载失败');
      wx.showToast({
        title: '图片加载失败',
        icon: 'none'
      });
      this.closeModal();
    }
  }
});
