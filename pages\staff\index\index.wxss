/* 滚动视图样式 */
.staff-index-scroll {
  height: 100vh;
  width: 100%;
}

/* 员工首页样式 */
.staff-index-container {
  background-color: #f8f8f8; /* 更浅的背景色 */
  min-height: 100vh;
  padding-bottom: 50rpx;
  position: relative;
  padding-top: 160rpx; /* 为固定头部留出空间 */
}

/* 佣金组成样式 */
.commission-breakdown {
  margin-top: 30rpx;
  padding-top: 20rpx;
  border-top: 1rpx solid #eee;
}

.commission-items {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.commission-item {
  flex: 1;
  text-align: center;
  background-color: #f9f9f9;
  border-radius: 8rpx;
  padding: 20rpx 15rpx;
  margin: 0 10rpx;
}

.commission-item:first-child {
  margin-left: 0;
}

.commission-item:last-child {
  margin-right: 0;
}

.commission-label {
  font-size: 24rpx;
  color: #999999;
  margin-bottom: 12rpx;
  display: block;
}

.commission-value {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
}

.commission-value.highlight {
  color: #333333;
}

.plus-icon {
  color: #07C160;
  font-weight: bold;
  margin-right: 4rpx;
  font-size: 24rpx;
}

/* 在充值记录页面添加推广佣金高亮样式 */
.info-value.highlight-commission {
  color: #6467F0; /* 紫色，和充值颜色一致 */
  font-weight: bold;
}

/* 顶部状态栏样式 */
.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  padding-top: 90rpx;
  background-color: #ffffff;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
}

.header-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  flex: 1;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 返回按钮样式 */
.back-btn {
  font-size: 40rpx;
  font-weight: bold;
  color: #333333;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 占位元素 */
.placeholder {
  width: 60rpx;
}

/* 业绩概览样式 - 重新设计为更突出的卡片 */
.performance-overview {
  margin: 20rpx 30rpx;
  background-color: #ffffff;
  border-radius: 15rpx; /* 增大圆角 */
  padding: 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08); /* 增强阴影 */
}

/* 已删除 performance-title 样式，因为不再需要标题 */

.loading {
  padding: 40rpx 0;
  text-align: center;
}

.loading-icon {
  display: inline-block;
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #07C160; /* 微信绿色 */
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 24rpx;
  color: #999999;
  margin-top: 20rpx;
}

.performance-cards {
  display: flex;
  flex-direction: column;
}

/* 主要提成数据样式 - 更加突出 */
.main-performance {
  display: flex;
  justify-content: space-between;
  margin-bottom: 30rpx;
}

.main-performance-card {
  width: 48%;
  background: linear-gradient(to right, #07C160, #09BB5D); /* 微信绿色渐变 */
  border-radius: 12rpx; /* 增大圆角 */
  padding: 25rpx;
  box-sizing: border-box;
  box-shadow: 0 4rpx 12rpx rgba(7, 193, 96, 0.2); /* 绿色阴影 */
}

/* 总提成卡片 - 淡化处理 */
.main-performance-card:first-child {
  background: linear-gradient(to right, rgba(7, 193, 96, 0.6), rgba(9, 187, 93, 0.6)); /* 淡化的绿色渐变 */
  box-shadow: 0 4rpx 12rpx rgba(7, 193, 96, 0.1); /* 淡化阴影 */
}

.main-card-title {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 15rpx;
}

.main-card-value {
  font-size: 40rpx; /* 增大字体 */
  font-weight: bold;
  color: #ffffff;
}

/* 次要提成数据样式 - 简化设计 */
.secondary-performance {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  background-color: #f9f9f9;
  border-radius: 12rpx;
  padding: 20rpx;
}

.performance-card {
  width: 31%;
  padding: 12rpx 0;
  margin-bottom: 10rpx;
  box-sizing: border-box;
  text-align: center;
}

.card-title {
  font-size: 24rpx;
  color: #999999; /* 更淡的颜色 */
  margin-bottom: 8rpx;
}

.card-value {
  font-size: 28rpx;
  font-weight: bold;
  color: #333333; /* 更深的颜色增加对比度 */
}

/* 日期范围查询按钮 */
.date-query-toggle {
  margin-top: 20rpx;
  padding: 15rpx 0;
  text-align: center;
  font-size: 26rpx;
  color: #07C160; /* 微信绿色 */
  border-top: 1rpx solid #eee;
  display: flex;
  justify-content: center;
  align-items: center;
}

.toggle-icon {
  margin-left: 10rpx;
  font-size: 24rpx;
}

/* 核销码输入区域样式 - 简化设计 */
.verify-code-section {
  margin: 20rpx 30rpx;
  background-color: #ffffff;
  border-radius: 15rpx; /* 增大圆角 */
  padding: 25rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

.verify-code-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #666666; /* 淡化标题 */
  margin-bottom: 20rpx;
}

.verify-code-input-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.verify-code-input {
  flex: 1;
  height: 80rpx;
  background-color: #f9f9f9;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333333;
  margin-right: 20rpx;
}

.verify-button {
  width: 160rpx;
  height: 80rpx;
  line-height: 80rpx;
  background-color: #07C160; /* 微信绿色 */
  color: #ffffff;
  font-size: 28rpx;
  border-radius: 8rpx;
  text-align: center;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.verify-button:active {
  opacity: 0.9;
  transform: translateY(1rpx);
}

/* 功能菜单样式 */
.function-menu {
  margin: 20rpx 30rpx;
  display: flex;
  justify-content: space-between;
  flex-wrap: nowrap;
  background-color: #ffffff;
  border-radius: 15rpx;
  padding: 25rpx 15rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

.function-item {
  width: 18%;
  background-color: transparent;
  border-radius: 0;
  padding: 15rpx 0;
  box-shadow: none;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 0;
  transition: all 0.2s ease;
}

.function-item:active {
  transform: scale(0.95);
  box-shadow: none;
}

.function-icon {
  width: 70rpx;
  height: 70rpx;
  border-radius: 35rpx; /* 圆形图标 */
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30rpx;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 10rpx;
}

.order-icon {
  background-color: #07C160; /* 微信绿色 */
}

.appointment-icon {
  background-color: #8a2be2; /* 紫色背景 */
}

.balance-icon {
  background-color: #10AEFF; /* 微信蓝色 */
}

.recharge-icon {
  background-color: #6467F0; /* 紫色 */
}

.expense-icon {
  background-color: #FA9D3B; /* 橙色 */
}

.function-name {
  font-size: 24rpx;
  color: #333333;
  text-align: center;
}

/* 提示信息样式 - 简化 */
.tips {
  margin: 20rpx 30rpx;
  padding: 20rpx;
  background-color: #ffffff;
  border-radius: 15rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

.tip-item {
  font-size: 24rpx;
  color: #999999;
  margin-bottom: 10rpx;
  line-height: 1.5;
}

/* 底部退出按钮容器 */
.logout-container {
  padding: 30rpx;
  margin-top: 30rpx;
}

/* 底部退出按钮样式 */
.logout-btn-bottom {
  width: 100%;
  height: 90rpx;
  background-color: #f2f2f2;
  color: #999999;
  font-size: 28rpx;
  border-radius: 45rpx;
  box-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.logout-btn-bottom:active {
  opacity: 0.9;
  transform: translateY(1rpx);
}

/* 充值核销按钮样式 */
.recharge-verify-button {
  background-color: #6467F0; /* 紫色，表示充值 */
}

/* 员工信息样式 */
.staff-info {
  margin: 30rpx;
  background-color: #ffffff; /* 卡片背景 */
  border-radius: 6rpx; /* 卡片中等圆角 */
  padding: 25rpx 30rpx; /* 卡片内边?*/
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05); /* 卡片阴影 */
  text-align: center;
}

.staff-name {
  font-size: 32rpx; /* 规范的副标题 */
  font-weight: bold;
  color: #333333; /* 文字主色 */
  margin-bottom: 10rpx;
}

.staff-phone {
  font-size: 28rpx; /* 规范的正文内?*/
  color: #666666; /* 次要文字?*/
}

/* 刷新按钮样式 */
.refresh-btn {
  font-size: 24rpx; /* 规范的说明文�?*/
  color: #0070c9; /* 强调�?主按�?*/
  font-weight: normal;
  background: rgba(0, 112, 201, 0.1); /* 浅蓝色背�?*/
  padding: 6rpx 20rpx; /* 小型按钮内边�?*/
  border-radius: 4rpx; /* 按钮小圆�?*/
}

/* 功能菜单样式 */
.function-menu {
  margin: 30rpx;
  display: flex;
  justify-content: space-around;
}

.function-item {
  width: 40%;
  background-color: #ffffff; /* 卡片背景 */
  border-radius: 6rpx; /* 卡片中等圆角 */
  padding: 30rpx 0;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05); /* 卡片阴影 */
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 0 10rpx;
  transition: all 0.2s ease;
}

.function-item:active {
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.function-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 4rpx; /* 按钮小圆�?*/
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx; /* 规范的副标题 */
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 20rpx;
}

.verify-icon {
  background-color: #0070c9; /* 强调�?主按�?*/
}

.performance-icon {
  background-color: #34c759; /* 成功�?*/
}

/* 日期范围查询面板 */
.date-query-panel {
  margin: 0 30rpx 30rpx;
  background-color: #ffffff; /* 卡片背景 */
  border-radius: 6rpx; /* 卡片中等圆角 */
  padding: 25rpx 30rpx; /* 卡片内边�?*/
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05); /* 卡片阴影 */
}

.date-query-title {
  font-size: 32rpx; /* 规范的标题字�?*/
  font-weight: bold;
  color: #333333; /* 文字主色 */
  margin-bottom: 20rpx;
}

.date-picker-container {
  display: flex;
  justify-content: space-between;
  margin-bottom: 30rpx;
}

.date-picker-item {
  width: 48%;
}

.date-label {
  font-size: 26rpx; /* 规范的说明文�?*/
  color: #666666; /* 次要文字�?*/
  margin-bottom: 10rpx;
  display: block;
}

.date-picker-value {
  height: 70rpx;
  line-height: 70rpx;
  background-color: #f9f9f9; /* 极浅背景 */
  border-radius: 4rpx; /* 按钮小圆�?*/
  padding: 0 20rpx;
  font-size: 28rpx; /* 规范的正文内�?*/
  color: #333333; /* 文字主色 */
}

/* 查询按钮 */
.query-button {
  width: 100%;
  height: 80rpx;
  background-color: #8e9aaf; /* 柔和的蓝灰色，更加低调 */
  color: #ffffff;
  font-size: 28rpx; /* 规范的正文内容 */
  border-radius: 10rpx; /* 增加圆角 */
  margin-top: 20rpx;
  box-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.1); /* 按钮阴影 */
  display: flex;  /* 使用flex布局 */
  align-items: center;  /* 垂直居中 */
  justify-content: center;  /* 水平居中 */
  transition: all 0.2s ease;
}

.query-button:active {
  opacity: 0.9;
  transform: translateY(1rpx);
}

/* 查询结果样式 */
.date-range-result {
  margin-top: 30rpx;
  padding-top: 20rpx;
  border-top: 1rpx solid #e0e0e0; /* 边框颜色 */
}

.result-title {
  font-size: 30rpx; /* 规范的副标题 */
  font-weight: bold;
  color: #333333; /* 文字主色 */
  margin-bottom: 20rpx;
}

.result-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}

.result-label {
  font-size: 26rpx; /* 规范的说明文�?*/
  color: #666666; /* 次要文字�?*/
}

.result-value {
  font-size: 28rpx; /* 规范的正文内�?*/
  color: #333333; /* 文字主色 */
  font-weight: 500;
}

.result-value.highlight {
  color: #34c759; /* 成功�?*/
  font-size: 32rpx; /* 规范的副标题 */
  font-weight: bold;
  padding: 8rpx 20rpx;
  border-radius: 4rpx; /* 按钮小圆�?*/
  background-color: rgba(52, 199, 89, 0.08); /* 成功色淡化背�?*/
  border-left: 4rpx solid #34c759; /* 特殊数据�?*/
} 

/* 功能菜单项样式 */
.function-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 10rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 36rpx;
  font-weight: bold;
  color: #fff;
  margin-bottom: 10rpx;
}

.appointment-icon {
  background-color: #8a2be2; /* 紫色背景，区别于其他图标 */
} 

/* 状态控制区域样式 */
.status-control {
  margin: 20rpx 30rpx;
  padding: 25rpx;
  background: #ffffff;
  border-radius: 15rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

.status-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #666666;
  margin-bottom: 20rpx;
}

.status-buttons {
  display: flex;
  justify-content: space-between;
}

.status-btn {
  flex: 1;
  margin: 0 10rpx;
  font-size: 28rpx;
  padding: 15rpx 0;
  background: #f5f5f5;
  color: #666;
  border: none;
  border-radius: 8rpx;
  transition: all 0.3s ease;
}

/* 不同状态按钮的激活样式 */
.status-btn[data-status="available"].active {
  background: #07C160; /* 绿色 - 空闲 */
  color: #fff;
}

.status-btn[data-status="busy"].active {
  background: #FFA300; /* 黄色 - 服务中 */
  color: #fff;
}

.status-btn[data-status="rest"].active {
  background: #FF6B6B; /* 淡红色 - 休息 */
  color: #fff;
}

/* 移除默认的激活样式 */
.status-btn.active {
  background: #07C160;
  color: #fff;
}

.status-btn:first-child {
  margin-left: 0;
}

.status-btn:last-child {
  margin-right: 0;
} 
