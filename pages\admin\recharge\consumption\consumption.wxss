/* 容器 */
.consumption-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 30rpx;
  box-sizing: border-box;
  position: relative;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

/* 状态栏安全区域 */
.status-bar {
  width: 100%;
  height: 88rpx;
  background-color: #ffffff;
}

/* 头部导航栏 */
.header {
  width: 100%;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #ffffff;
  position: relative;
  padding: 0 30rpx;
  box-sizing: border-box;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.back-btn {
  width: 70rpx;
  height: 70rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.arrow-left {
  width: 20rpx;
  height: 20rpx;
  border-top: 4rpx solid #4a4a4a;
  border-left: 4rpx solid #4a4a4a;
  transform: rotate(-45deg);
}

.header-title {
  font-size: 34rpx;
  font-weight: 500;
  color: #4a4a4a;
  flex: 1;
  text-align: center;
}

.placeholder-btn {
  width: 60rpx;
  height: 60rpx;
  opacity: 0;
}

.filter-btn {
  padding: 10rpx 20rpx;
  background-color: #f0f0f0;
  color: #333;
  border-radius: 8rpx;
  font-size: 28rpx;
}

/* 搜索框 */
.search-box {
  display: flex;
  padding: 20rpx;
  background-color: #ffffff;
  border-bottom: 1rpx solid #eee;
}

.search-input {
  flex: 1;
  height: 70rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
}

.search-btn {
  width: 120rpx;
  height: 70rpx;
  line-height: 70rpx;
  text-align: center;
  background-color: #07c160;
  color: #fff;
  border-radius: 8rpx;
  margin-left: 20rpx;
  font-size: 28rpx;
}

/* 标签页 */
.tab-container {
  background-color: #ffffff;
  margin-bottom: 20rpx;
}

.tabs {
  display: flex;
  white-space: nowrap;
  border-bottom: 1rpx solid #eee;
}

.tab-item {
  display: inline-block;
  padding: 20rpx 30rpx;
  font-size: 28rpx;
  position: relative;
  color: #666;
}

.tab-item.active {
  color: #07c160;
  font-weight: 500;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background-color: #07c160;
}

/* 消费记录列表 */
.consumption-list {
  background-color: #ffffff;
  margin: 20rpx;
  border-radius: 12rpx;
  overflow: hidden;
  max-height: calc(100vh - 338rpx); /* 预留状态栏、头部、搜索框和标签页的高度 */
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

.record-item {
  padding: 20rpx;
  border-bottom: 1rpx solid #eee;
  position: relative;
}

.item-content {
  display: flex;
  flex-direction: column;
}

.item-row {
  display: flex;
  margin-bottom: 10rpx;
}

.item-row .label {
  width: 150rpx;
  color: #666;
  font-size: 26rpx;
}

.item-row .value {
  flex: 1;
  color: #333;
  font-size: 26rpx;
}

.user-id {
  font-size: 24rpx;
  color: #666;
  word-break: break-all;
}

.amount {
  font-weight: 500;
}

.amount.consumption {
  color: #ff4d4f;
}

.amount.refund {
  color: #52c41a;
}

.type {
  padding: 4rpx 10rpx;
  border-radius: 6rpx;
  font-size: 24rpx;
  display: inline-block;
}

.type.consumption {
  background-color: #fff1f0;
  color: #ff4d4f;
}

.type.refund {
  background-color: #e6ffed;
  color: #52c41a;
}

.remark {
  color: #666;
}

/* 加载更多 */
.load-more, .no-more {
  text-align: center;
  padding: 30rpx 0;
  color: #999;
  font-size: 24rpx;
}

/* 无数据提示 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
  background-color: #ffffff;
  margin: 20rpx;
  border-radius: 12rpx;
}

.empty-text {
  color: #999;
  font-size: 28rpx;
  margin-top: 20rpx;
}

/* 加载中 */
.loading-container {
  padding: 40rpx;
  text-align: center;
}

.loading {
  display: inline-block;
  color: #999;
  font-size: 28rpx;
}

/* 筛选弹窗 */
.filter-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  visibility: hidden;
  opacity: 0;
  transition: all 0.3s;
}

.filter-modal.show {
  visibility: visible;
  opacity: 1;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  border-radius: 24rpx 24rpx 0 0;
  transform: translateY(100%);
  transition: transform 0.3s;
}

.filter-modal.show .modal-content {
  transform: translateY(0);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #eee;
}

.modal-header text {
  font-size: 32rpx;
  font-weight: 500;
}

.close-icon {
  font-size: 40rpx;
  color: #999;
  padding: 10rpx;
}

.filter-form {
  padding: 30rpx;
}

.form-item {
  margin-bottom: 20rpx;
}

.form-label {
  font-size: 28rpx;
  margin-bottom: 10rpx;
  color: #333;
}

.picker {
  height: 80rpx;
  line-height: 80rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333;
}

.filter-btns {
  display: flex;
  margin-top: 40rpx;
}

.btn-reset, .btn-apply {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  font-size: 30rpx;
  border-radius: 8rpx;
}

.btn-reset {
  background-color: #f5f5f5;
  color: #333;
  margin-right: 10rpx;
}

.btn-apply {
  background-color: #07c160;
  color: #fff;
  margin-left: 10rpx;
}

/* 用户余额统计样式 */
.balance-stats-container {
  padding: 20rpx;
  max-height: calc(100vh - 338rpx);
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

.balance-summary-card {
  background-color: #ffffff;
  margin: 20rpx;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  position: relative;
  overflow: hidden;
}

.balance-summary-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6rpx;
  background: linear-gradient(90deg, #07c160, #39de7d);
}

.summary-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 30rpx;
  border-left: 6rpx solid #07c160;
  padding-left: 20rpx;
  display: flex;
  align-items: center;
}

.summary-title::after {
  content: '';
  flex: 1;
  height: 1rpx;
  background-color: #eee;
  margin-left: 20rpx;
}

.summary-content {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.summary-item {
  width: 48%;
  padding: 25rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: #f9f9f9;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  transition: all 0.3s;
  position: relative;
  overflow: hidden;
}

.summary-item::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: linear-gradient(90deg, #07c160, #39de7d);
  opacity: 0.7;
}

.summary-item:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.05);
}

.summary-value {
  font-size: 36rpx;
  font-weight: 500;
  color: #07c160;
  margin-bottom: 10rpx;
}

.summary-label {
  font-size: 24rpx;
  color: #666;
}

.balance-list {
  background-color: #ffffff;
  margin: 20rpx;
  border-radius: 12rpx;
  overflow: hidden;
  max-height: calc(100vh - 488rpx); /* 预留状态栏、头部、搜索框、标签页和余额统计摘要的高度 */
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

.balance-item {
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;
  display: flex;
  align-items: flex-start;
  position: relative;
  background-color: #ffffff;
  transition: all 0.3s;
  border-radius: 8rpx;
  margin: 10rpx 0;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
}

.balance-item:hover {
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.user-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 30rpx;
  width: 120rpx;
}

.user-avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  margin-bottom: 10rpx;
  background-color: #f0f0f0;
  border: 4rpx solid #f8f8f8;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.user-name {
  font-size: 24rpx;
  color: #333;
  text-align: center;
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-weight: 500;
}

.balance-info {
  flex: 1;
  background-color: #f9f9f9;
  padding: 15rpx 20rpx;
  border-radius: 8rpx;
}

.balance-row {
  display: flex;
  margin-bottom: 14rpx;
  align-items: center;
}

.balance-row:last-child {
  margin-bottom: 0;
}

.balance-label {
  width: 150rpx;
  color: #666;
  font-size: 26rpx;
}

.balance-value {
  flex: 1;
  color: #333;
  font-size: 26rpx;
  background-color: #ffffff;
  padding: 6rpx 12rpx;
  border-radius: 6rpx;
  border-left: 4rpx solid #e6e6e6;
}

.balance-value.user-id {
  font-size: 22rpx;
  color: #666;
  word-break: break-all;
  font-family: monospace;
  background-color: #f0f0f0;
}

.balance-value.total {
  color: #07c160;
  font-weight: 500;
  border-left-color: #07c160;
}

/* 退款按钮相关样式 */
.refund-row {
  margin-top: 15rpx;
}

.refund-container {
  display: flex;
  justify-content: flex-end;
  background-color: transparent !important;
  border-left: none !important;
  padding: 0 !important;
}

.refund-btn {
  background: linear-gradient(135deg, #ff6b6b, #e74c3c);
  color: #fff;
  font-size: 22rpx;
  padding: 4rpx 12rpx;
  border-radius: 30rpx;
  border: none;
  line-height: 1.5;
  box-shadow: 0 2rpx 8rpx rgba(231, 76, 60, 0.3);
  transition: all 0.3s;
  margin: 0;
  width: 160rpx;
  min-width: 160rpx;
  max-width: 160rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.refund-btn:active {
  opacity: 0.8;
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 5rpx rgba(231, 76, 60, 0.2);
}

/* 退款弹窗样式 */
.refund-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s;
}

.refund-modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(2px);
}

.modal-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80%;
  max-width: 600rpx;
  background-color: #fff;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.15);
  animation: modal-in 0.3s ease-out;
}

@keyframes modal-in {
  from {
    opacity: 0;
    transform: translate(-50%, -60%);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -50%);
  }
}

.modal-header {
  padding: 30rpx;
  background: linear-gradient(to right, #f8f8f8, #ffffff);
  border-bottom: 1rpx solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
}

.modal-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 6rpx;
  height: 100%;
  background-color: #e74c3c;
}

.modal-header text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-left: 10rpx;
}

.close-icon {
  font-size: 40rpx;
  color: #999;
  cursor: pointer;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s;
}

.close-icon:hover {
  background-color: #f0f0f0;
  color: #666;
}

.refund-form {
  padding: 30rpx;
}

.form-item {
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  position: relative;
}

.form-label {
  width: 180rpx;
  font-size: 28rpx;
  color: #666;
}

.form-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  flex: 1;
  background-color: #f9f9f9;
  padding: 12rpx 20rpx;
  border-radius: 8rpx;
}

.form-input {
  flex: 1;
  height: 70rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333;
  background-color: #f9f9f9;
  box-shadow: inset 0 1rpx 3rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s;
}

.form-input:focus {
  border-color: #07c160;
  background-color: #fff;
  box-shadow: 0 0 0 2rpx rgba(7, 193, 96, 0.1);
}

.form-input::placeholder {
  color: #999;
}

/* 退款金额输入框特殊样式 */
.refund-amount-input {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  background-color: #fff0f0;
  border-color: #ffcccb;
}

.refund-amount-input:focus {
  border-color: #e74c3c;
  background-color: #fff;
  box-shadow: 0 0 0 2rpx rgba(231, 76, 60, 0.15);
}

.checkbox-item {
  display: flex;
  align-items: center;
  margin: 30rpx 0;
  background-color: #fff9f9;
  padding: 15rpx 20rpx;
  border-radius: 8rpx;
  border-left: 4rpx solid #e74c3c;
}

.checkbox-label {
  margin-left: 10rpx;
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.checkbox-tip {
  font-size: 24rpx;
  color: #999;
  margin-left: 10rpx;
}

.refund-btns {
  display: flex;
  justify-content: space-between;
  margin-top: 40rpx;
}

.btn-cancel, .btn-confirm {
  width: 45%;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 40rpx;
  font-size: 30rpx;
  transition: all 0.3s;
}

.btn-cancel {
  background-color: #f5f5f5;
  color: #666;
  border: 1rpx solid #ddd;
}

.btn-cancel:active {
  background-color: #e9e9e9;
}

.btn-confirm {
  background: linear-gradient(135deg, #ff6b6b, #e74c3c);
  color: #fff;
  border: none;
  box-shadow: 0 4rpx 10rpx rgba(231, 76, 60, 0.3);
}

.btn-confirm:active {
  opacity: 0.9;
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 5rpx rgba(231, 76, 60, 0.2);
} 