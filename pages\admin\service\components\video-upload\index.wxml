<view class="{{visible ? '' : 'modal-hidden'}}">
  <view class="modal-mask" bindtap="hideModal"></view>
  <view class="modal-content">
    <view class="modal-header">
      <view class="close-button" bindtap="hideModal">
        <text class="iconfont">←</text>
      </view>
      <text class="modal-title">{{isEdit ? '编辑视频' : '上传新视频'}}</text>
    </view>
    
    <view class="form-container">
      <!-- 基本信息 -->
      <view class="form-section">
        <view class="section-title">基本信息</view>
        
        <!-- 视频标题 -->
        <view class="input-field">
          <view class="input-label">视频标题<text class="required-mark">*</text></view>
          <input class="text-input" value="{{formData.mainTitle}}" bindinput="onTitleInput" placeholder="请输入视频标题" maxlength="50" />
        </view>
        
        <!-- 视频副标题 -->
        <view class="input-field">
          <view class="input-label">视频副标题</view>
          <input class="text-input" value="{{formData.subTitle}}" bindinput="onSubtitleInput" placeholder="请输入视频副标题" maxlength="100" />
        </view>
        
        <!-- 视频排序 -->
        <view class="input-field">
          <view class="input-label">排序序号<text class="non-required">(无需填写)</text></view>
          <input class="text-input" type="number" value="{{formData.sortOrder}}" bindinput="onOrderInput" placeholder="请输入排序序号（数字越小，显示越靠前）" />
          <view class="input-tip">排序规则：数字越小排序越靠前，比如0排在1前面</view>
        </view>
        
        <!-- 播放量 -->
        <view class="input-field">
          <view class="input-label">播放量</view>
          <input class="text-input" type="number" value="{{formData.playCount || ''}}" bindinput="onPlayCountInput" placeholder="请输入营销用途的播放量数字（不建议为0）" />
          <view class="input-tip">营销用途：前端展示的播放量数字，不影响实际播放计数</view>
        </view>
        
        <!-- 视频可见性 -->
        <view class="switch-container">
          <view class="switch-label">可见性</view>
          <switch checked="{{formData.isVisible}}" bindchange="toggleVisibility" color="#ff9a9e" />
        </view>
      </view>
      
      <!-- 视频封面 -->
      <view class="form-section">
        <view class="section-title">视频封面</view>
        <view class="upload-field">
          <view class="upload-title">封面图片<text class="required-mark">*</text></view>
          <view class="upload-container" bindtap="uploadCover" wx:if="{{!formData.coverUrl}}">
            <view class="video-placeholder">
              <view class="placeholder-icon">+</view>
              <view class="placeholder-text">点击上传封面图片</view>
            </view>
          </view>
          <view class="upload-container" wx:else>
            <view class="upload-image">
              <image src="{{formData.coverUrl}}" mode="aspectFill"></image>
              <view class="remove-detail-image" catchtap="removeCover">×</view>
            </view>
            <view class="upload-button" bindtap="uploadCover">
              <view class="upload-icon">↑</view>
              <text>更换封面</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 视频文件 -->
      <view class="form-section">
        <view class="section-title">视频文件</view>
        <view class="upload-field">
          <view class="upload-title">上传视频（建议使用压缩MP4格式）<text class="required-mark">*</text></view>
          <view class="upload-container" bindtap="uploadVideo" wx:if="{{!formData.videoUrl}}">
            <view class="video-placeholder">
              <view class="placeholder-icon">+</view>
              <view class="placeholder-text">点击上传视频文件</view>
            </view>
          </view>
          <view class="upload-container" wx:else>
            <view class="upload-image video-container">
              <video src="{{formData.videoUrl}}" controls show-play-btn="true" object-fit="contain" show-center-play-btn="true" enable-progress-gesture="true"></video>
            </view>
            <view class="upload-button" bindtap="uploadVideo">
              <view class="upload-icon">↑</view>
              <text>更换视频</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 视频详情图片 -->
      <view class="form-section">
        <view class="section-title">详情图片</view>
        <view class="upload-field">
          <view class="upload-title">详情图片（支持上传长图）<text class="required-mark">*</text></view>
          <!-- 未上传详情图片时的显示 -->
          <view class="upload-container" bindtap="uploadDetailImage" wx:if="{{!formData.detailImages.length}}">
            <view class="video-placeholder">
              <view class="placeholder-icon">+</view>
              <view class="placeholder-text">点击上传详情图片</view>
            </view>
          </view>
          <!-- 已上传详情图片时的显示 -->
          <view class="upload-container" wx:else>
            <!-- 主显示区域显示第一张图片 -->
            <view class="upload-image">
              <image src="{{formData.detailImages[0].url}}" mode="aspectFill"></image>
              <view class="remove-detail-image" catchtap="removeDetailImage" data-index="0">×</view>
            </view>
            <!-- 更换详情按钮 -->
            <view class="upload-button" bindtap="uploadDetailImage">
              <view class="upload-icon">↑</view>
              <text>更换详情</text>
            </view>
          </view>
        </view>
      </view>
      
      <view class="safe-bottom-area"></view>
    </view>
    
    <!-- 底部保存按钮 -->
    <view class="bottom-save-container {{isSubmitting ? 'disabled' : ''}}">
      <view class="bottom-save-button" bindtap="submitForm" wx:if="{{!isSubmitting}}">保存视频</view>
      <view class="bottom-save-button disabled" wx:else>保存中...</view>
    </view>
    
    <!-- 加载状态 -->
    <view class="loading-container" wx:if="{{isSubmitting}}">
      <view class="loading-spinner"></view>
      <text class="loading-text">正在处理，请稍候...</text>
    </view>
  </view>
</view> 