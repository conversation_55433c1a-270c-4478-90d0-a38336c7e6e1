/**
 * UI状态管理模块
 * 负责页面UI状态管理、加载状态、错误状态、内容显示状态等
 * @version 1.0.0
 */

const BaseModule = require('./base-module');
const { 
  UI_CONFIG,
  UI_EVENTS,
  ERROR_TYPES 
} = require('../constants/index-constants');

class UIStateModule extends BaseModule {
  constructor(pageContext) {
    super(pageContext);
    this.moduleName = 'UIState';
    
    // UI状态缓存
    this.stateHistory = [];
    this.maxHistorySize = 10;
  }

  /**
   * 初始化UI状态模块
   */
  init() {
    try {
      // 减少UI状态模块初始化日志
    // console.log('[UIState] 初始化UI状态模块');
      
      // 初始化UI状态
      this.initUIState();
      
      this.initialized = true;
      // 减少UI状态模块初始化完成日志
    // console.log('[UIState] UI状态模块初始化完成');
    } catch (error) {
      this.handleError(error, 'init');
    }
  }

  /**
   * 初始化UI状态
   */
  initUIState() {
    try {
      const currentData = this.data;
      
      // 确保必要的UI状态字段存在
      const requiredFields = {
        loading: currentData.loading !== undefined ? currentData.loading : false,
        firstLoading: currentData.firstLoading !== undefined ? currentData.firstLoading : true,
        showContent: currentData.showContent !== undefined ? currentData.showContent : false,
        showNavbar: currentData.showNavbar !== undefined ? currentData.showNavbar : true,
        isRefreshing: currentData.isRefreshing !== undefined ? currentData.isRefreshing : false
      };

      // 只更新缺失的字段
      const updateData = {};
      Object.keys(requiredFields).forEach(key => {
        if (currentData[key] === undefined) {
          updateData[key] = requiredFields[key];
        }
      });

      if (Object.keys(updateData).length > 0) {
        this.safeSetData(updateData);
      }

      // 减少UI状态初始化完成日志
      // console.log('[UIState] UI状态初始化完成');
    } catch (error) {
      this.handleError(error, 'initUIState');
    }
  }

  /**
   * 显示加载状态
   * @param {object} options - 选项
   */
  showLoading(options = {}) {
    try {
      const updateData = {
        loading: true
      };

      // 如果是首次加载
      if (options.firstLoad) {
        updateData.firstLoading = true;
        updateData.showContent = false;
      }

      // 如果是刷新操作
      if (options.refresh) {
        updateData.isRefreshing = true;
      }

      this.safeSetData(updateData);

      // 保存状态历史
      this.saveStateHistory('showLoading', updateData);

      // 触发加载状态变化事件
      this.emit('loadingStateChange', { 
        loading: true, 
        firstLoad: options.firstLoad,
        refresh: options.refresh
      });

      console.log('[UIState] 显示加载状态', options);
    } catch (error) {
      this.handleError(error, 'showLoading');
    }
  }

  /**
   * 隐藏加载状态
   * @param {object} options - 选项
   */
  hideLoading(options = {}) {
    try {
      const updateData = {
        loading: false,
        firstLoading: false,
        isRefreshing: false
      };

      // 显示内容
      if (options.showContent !== false) {
        updateData.showContent = true;
      }

      this.safeSetData(updateData);

      // 保存状态历史
      this.saveStateHistory('hideLoading', updateData);

      // 触发加载状态变化事件
      this.emit('loadingStateChange', { 
        loading: false,
        showContent: updateData.showContent
      });

      console.log('[UIState] 隐藏加载状态');
    } catch (error) {
      this.handleError(error, 'hideLoading');
    }
  }

  /**
   * 显示内容
   */
  showContent() {
    try {
      this.safeSetData({ 
        showContent: true,
        firstLoading: false
      });

      // 保存状态历史
      this.saveStateHistory('showContent', { showContent: true });

      // 触发内容显示事件
      this.emit('contentVisibilityChange', { visible: true });

      console.log('[UIState] 显示内容');
    } catch (error) {
      this.handleError(error, 'showContent');
    }
  }

  /**
   * 隐藏内容
   */
  hideContent() {
    try {
      this.safeSetData({ showContent: false });

      // 保存状态历史
      this.saveStateHistory('hideContent', { showContent: false });

      // 触发内容隐藏事件
      this.emit('contentVisibilityChange', { visible: false });

      console.log('[UIState] 隐藏内容');
    } catch (error) {
      this.handleError(error, 'hideContent');
    }
  }

  /**
   * 显示导航栏
   */
  showNavbar() {
    try {
      this.safeSetData({ showNavbar: true });

      // 保存状态历史
      this.saveStateHistory('showNavbar', { showNavbar: true });

      // 触发导航栏显示事件
      this.emit('navbarVisibilityChange', { visible: true });

      console.log('[UIState] 显示导航栏');
    } catch (error) {
      this.handleError(error, 'showNavbar');
    }
  }

  /**
   * 隐藏导航栏
   */
  hideNavbar() {
    try {
      this.safeSetData({ showNavbar: false });

      // 保存状态历史
      this.saveStateHistory('hideNavbar', { showNavbar: false });

      // 触发导航栏隐藏事件
      this.emit('navbarVisibilityChange', { visible: false });

      console.log('[UIState] 隐藏导航栏');
    } catch (error) {
      this.handleError(error, 'hideNavbar');
    }
  }

  /**
   * 切换导航栏显示状态
   */
  toggleNavbar() {
    try {
      const currentVisible = this.data.showNavbar;
      if (currentVisible) {
        this.hideNavbar();
      } else {
        this.showNavbar();
      }
    } catch (error) {
      this.handleError(error, 'toggleNavbar');
    }
  }

  /**
   * 处理导航栏控制事件
   * @param {object} e - 事件对象
   */
  onNavbarControl(e) {
    try {
      const action = e.detail.action;
      console.log('[UIState] 导航栏控制事件:', action);
      
      if (action === 'hide') {
        this.hideNavbar();
      } else if (action === 'show') {
        this.showNavbar();
      }
    } catch (error) {
      this.handleError(error, 'onNavbarControl');
    }
  }

  /**
   * 设置刷新状态
   * @param {boolean} refreshing - 是否正在刷新
   */
  setRefreshing(refreshing) {
    try {
      this.safeSetData({ isRefreshing: refreshing });

      // 保存状态历史
      this.saveStateHistory('setRefreshing', { isRefreshing: refreshing });

      // 触发刷新状态变化事件
      this.emit('refreshStateChange', { refreshing });

      console.log('[UIState] 设置刷新状态:', refreshing);
    } catch (error) {
      this.handleError(error, 'setRefreshing');
    }
  }

  /**
   * 显示Toast提示
   * @param {object} options - Toast选项
   */
  showToast(options = {}) {
    try {
      const toastOptions = {
        title: options.title || '',
        icon: options.icon || 'none',
        duration: options.duration || 1500,
        mask: options.mask || false
      };

      wx.showToast(toastOptions);

      // 触发Toast显示事件
      this.emit('toastShow', toastOptions);

      console.log('[UIState] 显示Toast:', options.title);
    } catch (error) {
      this.handleError(error, 'showToast');
    }
  }

  /**
   * 隐藏Toast提示
   */
  hideToast() {
    try {
      wx.hideToast();

      // 触发Toast隐藏事件
      this.emit('toastHide', {});

      console.log('[UIState] 隐藏Toast');
    } catch (error) {
      this.handleError(error, 'hideToast');
    }
  }

  /**
   * 显示加载提示
   * @param {object} options - 选项
   */
  showLoadingToast(options = {}) {
    try {
      const loadingOptions = {
        title: options.title || '加载中...',
        mask: options.mask !== false
      };

      wx.showLoading(loadingOptions);

      // 触发加载提示显示事件
      this.emit('loadingToastShow', loadingOptions);

      console.log('[UIState] 显示加载提示:', options.title);
    } catch (error) {
      this.handleError(error, 'showLoadingToast');
    }
  }

  /**
   * 隐藏加载提示
   */
  hideLoadingToast() {
    try {
      wx.hideLoading();

      // 触发加载提示隐藏事件
      this.emit('loadingToastHide', {});

      console.log('[UIState] 隐藏加载提示');
    } catch (error) {
      this.handleError(error, 'hideLoadingToast');
    }
  }

  /**
   * 显示模态框
   * @param {string} modalId - 模态框ID
   * @param {object} data - 模态框数据
   */
  showModal(modalId, data = {}) {
    try {
      if (!modalId) {
        console.warn('[UIState] 模态框ID不能为空');
        return;
      }

      // 查找模态框组件
      const modal = this.page.selectComponent(modalId);
      if (!modal) {
        console.warn('[UIState] 未找到模态框组件:', modalId);
        return;
      }

      // 调用模态框的显示方法
      if (typeof modal.showModal === 'function') {
        modal.showModal(data);
      } else if (typeof modal.show === 'function') {
        modal.show(data);
      } else {
        console.warn('[UIState] 模态框组件没有显示方法:', modalId);
        return;
      }

      // 触发模态框显示事件
      this.emit('modalShow', { modalId, data });

      console.log('[UIState] 显示模态框:', modalId);
    } catch (error) {
      this.handleError(error, 'showModal');
    }
  }

  /**
   * 隐藏模态框
   * @param {string} modalId - 模态框ID
   */
  hideModal(modalId) {
    try {
      if (!modalId) {
        console.warn('[UIState] 模态框ID不能为空');
        return;
      }

      // 查找模态框组件
      const modal = this.page.selectComponent(modalId);
      if (!modal) {
        console.warn('[UIState] 未找到模态框组件:', modalId);
        return;
      }

      // 调用模态框的隐藏方法
      if (typeof modal.hideModal === 'function') {
        modal.hideModal();
      } else if (typeof modal.hide === 'function') {
        modal.hide();
      } else {
        console.warn('[UIState] 模态框组件没有隐藏方法:', modalId);
        return;
      }

      // 触发模态框隐藏事件
      this.emit('modalHide', { modalId });

      console.log('[UIState] 隐藏模态框:', modalId);
    } catch (error) {
      this.handleError(error, 'hideModal');
    }
  }

  /**
   * 显示确认对话框
   * @param {object} options - 对话框选项
   * @returns {Promise<boolean>} 用户选择结果
   */
  showConfirm(options = {}) {
    return new Promise((resolve, reject) => {
      try {
        const confirmOptions = {
          title: options.title || '提示',
          content: options.content || '',
          showCancel: options.showCancel !== false,
          cancelText: options.cancelText || '取消',
          cancelColor: options.cancelColor || '#000000',
          confirmText: options.confirmText || '确定',
          confirmColor: options.confirmColor || '#576B95',
          success: (res) => {
            const confirmed = res.confirm;
            
            // 触发确认对话框结果事件
            this.emit('confirmResult', { confirmed, options });
            
            console.log('[UIState] 确认对话框结果:', confirmed);
            resolve(confirmed);
          },
          fail: (err) => {
            console.error('[UIState] 确认对话框失败:', err);
            reject(err);
          }
        };

        wx.showModal(confirmOptions);

        // 触发确认对话框显示事件
        this.emit('confirmShow', options);

        console.log('[UIState] 显示确认对话框:', options.title);
      } catch (error) {
        this.handleError(error, 'showConfirm');
        reject(error);
      }
    });
  }

  /**
   * 显示操作菜单
   * @param {object} options - 菜单选项
   * @returns {Promise<number>} 选择的菜单索引
   */
  showActionSheet(options = {}) {
    return new Promise((resolve, reject) => {
      try {
        const actionOptions = {
          itemList: options.itemList || [],
          itemColor: options.itemColor || '#000000',
          success: (res) => {
            const tapIndex = res.tapIndex;
            
            // 触发操作菜单选择事件
            this.emit('actionSheetSelect', { tapIndex, options });
            
            console.log('[UIState] 操作菜单选择:', tapIndex);
            resolve(tapIndex);
          },
          fail: (err) => {
            console.log('[UIState] 操作菜单取消或失败');
            reject(err);
          }
        };

        wx.showActionSheet(actionOptions);

        // 触发操作菜单显示事件
        this.emit('actionSheetShow', options);

        console.log('[UIState] 显示操作菜单');
      } catch (error) {
        this.handleError(error, 'showActionSheet');
        reject(error);
      }
    });
  }

  /**
   * 保存状态历史
   * @param {string} action - 操作名称
   * @param {object} state - 状态数据
   */
  saveStateHistory(action, state) {
    try {
      const historyItem = {
        action,
        state,
        timestamp: Date.now()
      };

      this.stateHistory.push(historyItem);

      // 限制历史记录大小
      if (this.stateHistory.length > this.maxHistorySize) {
        this.stateHistory.shift();
      }
    } catch (error) {
      this.handleError(error, 'saveStateHistory');
    }
  }

  /**
   * 获取状态历史
   * @returns {Array} 状态历史记录
   */
  getStateHistory() {
    try {
      return [...this.stateHistory];
    } catch (error) {
      this.handleError(error, 'getStateHistory');
      return [];
    }
  }

  /**
   * 清除状态历史
   */
  clearStateHistory() {
    try {
      this.stateHistory = [];
      console.log('[UIState] 状态历史已清除');
    } catch (error) {
      this.handleError(error, 'clearStateHistory');
    }
  }

  /**
   * 获取当前UI状态
   * @returns {object} 当前UI状态
   */
  getCurrentState() {
    try {
      return {
        loading: this.data.loading,
        firstLoading: this.data.firstLoading,
        showContent: this.data.showContent,
        showNavbar: this.data.showNavbar,
        isRefreshing: this.data.isRefreshing
      };
    } catch (error) {
      this.handleError(error, 'getCurrentState');
      return {};
    }
  }

  /**
   * 检查是否正在加载
   * @returns {boolean} 是否正在加载
   */
  isLoading() {
    try {
      return this.data.loading || this.data.firstLoading || this.data.isRefreshing;
    } catch (error) {
      this.handleError(error, 'isLoading');
      return false;
    }
  }

  /**
   * 检查内容是否可见
   * @returns {boolean} 内容是否可见
   */
  isContentVisible() {
    try {
      return this.data.showContent;
    } catch (error) {
      this.handleError(error, 'isContentVisible');
      return false;
    }
  }

  /**
   * 检查导航栏是否可见
   * @returns {boolean} 导航栏是否可见
   */
  isNavbarVisible() {
    try {
      return this.data.showNavbar;
    } catch (error) {
      this.handleError(error, 'isNavbarVisible');
      return true;
    }
  }

  /**
   * 重置UI状态
   */
  resetState() {
    try {
      const resetData = {
        loading: false,
        firstLoading: true,
        showContent: false,
        showNavbar: true,
        isRefreshing: false
      };

      this.safeSetData(resetData);

      // 保存状态历史
      this.saveStateHistory('resetState', resetData);

      // 触发状态重置事件
      this.emit('stateReset', resetData);

      console.log('[UIState] UI状态已重置');
    } catch (error) {
      this.handleError(error, 'resetState');
    }
  }

  /**
   * 销毁模块，清理资源
   */
  destroy() {
    try {
      console.log('[UIState] 销毁UI状态模块');
      
      // 清除状态历史
      this.clearStateHistory();
      
      // 隐藏所有Toast和Loading
      this.hideToast();
      this.hideLoadingToast();
      
      super.destroy();
    } catch (error) {
      this.handleError(error, 'destroy');
    }
  }
}

module.exports = UIStateModule;