const app = getApp();
const utils = require('../../../utils/util.js');

Page({
  data: {
    staffInfo: null,
    amount: '',
    remark: '',
    images: [],
    
    loading: true,
    expenseList: [],
    
    // 分页
    currentPage: 1,
    totalPages: 1,
    pageSize: 10,
    
    // 支出详情
    showExpenseDetail: false,
    currentExpense: null
  },

  onLoad: function (options) {
    // 检查员工登录状态
    if (!app.globalData.isStaff) {
      this.redirectToLogin();
      return;
    }
    
    // 获取员工信息
    this.setData({
      staffInfo: app.globalData.staffInfo
    });
    
    // 获取支出记录
    this.getExpenseList();
  },
  
  // 获取支出记录列表
  getExpenseList: function() {
    this.setData({
      loading: true
    });
    
    const { staffInfo, currentPage, pageSize } = this.data;
    
    if (!staffInfo || !staffInfo._id) {
      this.setData({
        loading: false,
        expenseList: []
      });
      return;
    }
    
    wx.cloud.callFunction({
      name: 'expenseManager',
      data: {
        type: 'getExpenses',
        data: {
          staffId: staffInfo._id,
          page: currentPage,
          pageSize
        }
      }
    }).then(res => {
      if (res.result && res.result.success) {
        const expenseList = res.result.list || [];
        
        // 格式化日期和金额
        expenseList.forEach(item => {
          if (item.createTime) {
            // 兼容多种时间格式的处理
            let date;

            if (typeof item.createTime === 'string') {
              if (item.createTime.includes('T') && item.createTime.includes('Z')) {
                // ISO格式: "2025-07-24T22:01:22.169Z" - 这种格式通常兼容性较好
                date = new Date(item.createTime);
              } else if (item.createTime.includes('-')) {
                // 普通格式: "2025-07-25 06:05:47" 或 "2025-07-25 06:38"
                // 转换为兼容iOS的格式: "2025/07/25 06:05:47"
                const isoString = item.createTime.replace(/-/g, '/');
                date = new Date(isoString);
              } else {
                // 其他字符串格式
                date = new Date(item.createTime);
              }
            } else {
              // Date对象或其他格式
              date = new Date(item.createTime);
            }

            if (!isNaN(date.getTime())) {
              item.createTime = utils.formatDateTime(date);
            } else {
              console.error('无法解析时间格式:', item.createTime);
              item.createTime = String(item.createTime);
            }
          }
          if (item.amount) {
            item.amount = parseFloat(item.amount).toFixed(2);
          }
        });
        
        this.setData({
          expenseList,
          totalPages: Math.ceil(res.result.total / pageSize) || 1,
          loading: false
        });
      } else {
        this.setData({
          loading: false
        });
        wx.showToast({
          title: '获取支出记录失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      console.error('获取支出记录失败：', err);
      this.setData({
        loading: false
      });
      wx.showToast({
        title: '获取支出记录失败',
        icon: 'none'
      });
    });
  },
  
  // 金额输入
  onAmountInput: function(e) {
    this.setData({
      amount: e.detail.value
    });
  },
  
  // 备注输入
  onRemarkInput: function(e) {
    this.setData({
      remark: e.detail.value
    });
  },
  
  // 选择图片
  chooseImage: function() {
    const { images } = this.data;
    const remainCount = 9 - images.length;
    
    if (remainCount <= 0) {
      wx.showToast({
        title: '最多上传9张图片',
        icon: 'none'
      });
      return;
    }
    
    wx.chooseImage({
      count: remainCount,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        // 显示上传中
        wx.showLoading({
          title: '上传中...',
          mask: true
        });
        
        const tempFilePaths = res.tempFilePaths;
        const uploadTasks = [];
        
        // 上传图片到云存储
        tempFilePaths.forEach(filePath => {
          const uploadTask = new Promise((resolve, reject) => {
            const fileName = `expense_${Date.now()}_${Math.floor(Math.random() * 1000)}${filePath.match(/\.[^.]+?$/)[0]}`;
            
            wx.cloud.uploadFile({
              cloudPath: `expenses/${fileName}`,
              filePath: filePath,
              success: res => {
                resolve(res.fileID);
              },
              fail: err => {
                console.error('上传图片失败：', err);
                reject(err);
              }
            });
          });
          
          uploadTasks.push(uploadTask);
        });
        
        // 等待所有图片上传完成
        Promise.all(uploadTasks).then(fileIDs => {
          wx.hideLoading();
          
          this.setData({
            images: this.data.images.concat(fileIDs)
          });
          
          wx.showToast({
            title: '上传成功',
            icon: 'success'
          });
        }).catch(err => {
          wx.hideLoading();
          
          wx.showToast({
            title: '上传失败',
            icon: 'none'
          });
        });
      }
    });
  },
  
  // 删除图片
  deleteImage: function(e) {
    const index = e.currentTarget.dataset.index;
    const { images } = this.data;
    
    images.splice(index, 1);
    
    this.setData({
      images
    });
  },
  
  // 预览图片
  previewImage: function(e) {
    const url = e.currentTarget.dataset.url;
    const urls = e.currentTarget.dataset.urls || [url];
    const current = e.currentTarget.dataset.current || url;
    
    wx.previewImage({
      urls,
      current
    });
  },
  
  // 提交支出记录
  submitExpense: function() {
    const { staffInfo, amount, remark, images } = this.data;
    
    if (!staffInfo || !staffInfo._id) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }
    
    if (!amount || parseFloat(amount) <= 0) {
      wx.showToast({
        title: '请输入有效金额',
        icon: 'none'
      });
      return;
    }
    
    wx.showLoading({
      title: '提交中...',
      mask: true
    });
    
    console.log('准备提交支出记录:', {
      staffId: staffInfo._id,
      staffName: staffInfo.name,
      amount: parseFloat(amount),
      remark,
      images
    });
    
    wx.cloud.callFunction({
      name: 'expenseManager',
      data: {
        type: 'addExpense',
        data: {
          staffId: staffInfo._id,
          staffName: staffInfo.name,
          amount: parseFloat(amount),
          remark,
          images
        }
      },
      success: res => {
        console.log('提交支出记录结果:', JSON.stringify(res.result));
        wx.hideLoading();
        
        if (res.result && res.result.success) {
          wx.showToast({
            title: '提交成功',
            icon: 'success'
          });
          
          // 清空表单
          this.setData({
            amount: '',
            remark: '',
            images: []
          });
          
          // 重新获取支出记录
          this.getExpenseList();
        } else {
          const errorMsg = res.result?.message || '提交失败';
          console.error('提交失败详情:', res.result?.error || errorMsg);
          
          wx.showModal({
            title: '提交失败',
            content: errorMsg,
            showCancel: false
          });
        }
      },
      fail: err => {
        console.error('调用云函数失败:', err);
        wx.hideLoading();
        
        wx.showModal({
          title: '提交失败',
          content: '云函数调用失败: ' + (err.errMsg || JSON.stringify(err)),
          showCancel: false
        });
      }
    });
  },
  
  // 查看支出详情
  viewExpenseDetail: function(e) {
    const expense = e.currentTarget.dataset.expense;
    this.setData({
      currentExpense: expense,
      showExpenseDetail: true
    });
  },
  
  // 关闭支出详情
  closeExpenseDetail: function() {
    this.setData({
      showExpenseDetail: false
    });
  },
  
  // 上一页
  prevPage: function() {
    if (this.data.currentPage > 1) {
      this.setData({
        currentPage: this.data.currentPage - 1
      });
      this.getExpenseList();
    }
  },
  
  // 下一页
  nextPage: function() {
    if (this.data.currentPage < this.data.totalPages) {
      this.setData({
        currentPage: this.data.currentPage + 1
      });
      this.getExpenseList();
    }
  },
  
  // 返回上一页
  goBack: function() {
    wx.navigateBack();
  },
  
  // 重定向到登录页
  redirectToLogin: function() {
    wx.showToast({
      title: '请先登录',
      icon: 'none'
    });
    
    setTimeout(() => {
      wx.redirectTo({
        url: '/pages/staff/login/login'
      });
    }, 1500);
  }
}); 