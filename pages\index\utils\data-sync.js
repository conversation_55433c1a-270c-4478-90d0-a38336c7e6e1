/**
 * 数据同步管理器
 * 负责页面数据与全局数据的同步，以及模块间数据状态的一致性
 * @version 1.0.0
 */

const { 
  PAGE_EVENTS, 
  VIDEO_EVENTS,
  MODULE_EVENTS 
} = require('../constants/index-constants');

class DataSyncManager {
  constructor() {
    this.syncRules = new Map();
    this.syncQueue = [];
    this.isProcessing = false;
    this.listeners = new Map();
    this.globalDataKeys = [
      'userInfo',
      'currentTabIndex',
      'videoList',
      'pendingVideoToOpen'
    ];
    
    this.init();
  }

  /**
   * 初始化数据同步管理器
   */
  init() {
    try {
      // 设置默认同步规则
      this.setupDefaultSyncRules();
      
      // 监听全局数据变化
      this.setupGlobalDataWatcher();
      
      console.log('[DataSync] 数据同步管理器初始化完成');
    } catch (error) {
      console.error('[DataSync] 初始化失败:', error);
    }
  }

  /**
   * 设置默认同步规则
   */
  setupDefaultSyncRules() {
    // 用户信息同步规则
    this.addSyncRule('userInfo', {
      source: 'global',
      target: 'page',
      bidirectional: false,
      validator: (data) => data && typeof data === 'object',
      transformer: (data) => data
    });

    // 视频列表同步规则
    this.addSyncRule('videoList', {
      source: 'page',
      target: 'global',
      bidirectional: true,
      validator: (data) => Array.isArray(data),
      transformer: (data) => data.slice() // 创建副本
    });

    // 当前播放视频同步规则
    this.addSyncRule('currentPlayingVideo', {
      source: 'page',
      target: 'global',
      bidirectional: false,
      validator: (data) => !data || (data && data.id),
      transformer: (data) => data
    });
  }

  /**
   * 添加同步规则
   * @param {string} key - 数据键名
   * @param {object} rule - 同步规则
   */
  addSyncRule(key, rule) {
    const defaultRule = {
      source: 'page',
      target: 'global',
      bidirectional: false,
      validator: () => true,
      transformer: (data) => data,
      priority: 0
    };

    this.syncRules.set(key, { ...defaultRule, ...rule });
    console.log(`[DataSync] 添加同步规则: ${key}`);
  }

  /**
   * 移除同步规则
   * @param {string} key - 数据键名
   */
  removeSyncRule(key) {
    this.syncRules.delete(key);
    console.log(`[DataSync] 移除同步规则: ${key}`);
  }

  /**
   * 同步页面数据到全局
   * @param {object} pageContext - 页面上下文
   * @param {string|Array} keys - 要同步的数据键，不传则同步所有
   */
  syncToGlobal(pageContext, keys) {
    try {
      const app = getApp();
      if (!app || !app.globalData) {
        console.warn('[DataSync] 全局应用或globalData不存在');
        return;
      }

      const keysToSync = keys ? (Array.isArray(keys) ? keys : [keys]) : Array.from(this.syncRules.keys());
      
      keysToSync.forEach(key => {
        const rule = this.syncRules.get(key);
        if (!rule || (rule.source !== 'page' && !rule.bidirectional)) {
          return;
        }

        const pageData = pageContext.data[key];
        
        // 验证数据
        if (!rule.validator(pageData)) {
          console.warn(`[DataSync] 数据验证失败: ${key}`, pageData);
          return;
        }

        // 转换数据
        const transformedData = rule.transformer(pageData);
        
        // 同步到全局
        app.globalData[key] = transformedData;
        
        console.log(`[DataSync] 同步到全局: ${key}`);
        
        // 触发同步事件
        this.emit('syncToGlobal', { key, data: transformedData });
      });

    } catch (error) {
      console.error('[DataSync] 同步到全局失败:', error);
    }
  }

  /**
   * 同步全局数据到页面
   * @param {object} pageContext - 页面上下文
   * @param {string|Array} keys - 要同步的数据键，不传则同步所有
   */
  syncToPage(pageContext, keys) {
    try {
      const app = getApp();
      if (!app || !app.globalData) {
        console.warn('[DataSync] 全局应用或globalData不存在');
        return;
      }

      const keysToSync = keys ? (Array.isArray(keys) ? keys : [keys]) : Array.from(this.syncRules.keys());
      const updateData = {};
      
      keysToSync.forEach(key => {
        const rule = this.syncRules.get(key);
        if (!rule || (rule.source !== 'global' && !rule.bidirectional)) {
          return;
        }

        const globalData = app.globalData[key];
        
        // 验证数据
        if (!rule.validator(globalData)) {
          console.warn(`[DataSync] 全局数据验证失败: ${key}`, globalData);
          return;
        }

        // 检查数据是否有变化
        const currentPageData = pageContext.data[key];
        if (this.isDataEqual(currentPageData, globalData)) {
          return;
        }

        // 转换数据
        const transformedData = rule.transformer(globalData);
        updateData[key] = transformedData;
        
        console.log(`[DataSync] 同步到页面: ${key}`);
      });

      // 批量更新页面数据
      if (Object.keys(updateData).length > 0) {
        pageContext.setData(updateData);
        
        // 触发同步事件
        this.emit('syncToPage', { keys: Object.keys(updateData), data: updateData });
      }

    } catch (error) {
      console.error('[DataSync] 同步到页面失败:', error);
    }
  }

  /**
   * 双向同步数据
   * @param {object} pageContext - 页面上下文
   * @param {string|Array} keys - 要同步的数据键
   */
  bidirectionalSync(pageContext, keys) {
    try {
      // 先同步全局到页面
      this.syncToPage(pageContext, keys);
      
      // 再同步页面到全局
      this.syncToGlobal(pageContext, keys);
      
      console.log('[DataSync] 双向同步完成');
    } catch (error) {
      console.error('[DataSync] 双向同步失败:', error);
    }
  }

  /**
   * 添加到同步队列
   * @param {object} syncTask - 同步任务
   */
  addToSyncQueue(syncTask) {
    this.syncQueue.push({
      ...syncTask,
      timestamp: Date.now()
    });

    // 如果没有在处理，立即处理队列
    if (!this.isProcessing) {
      this.processSyncQueue();
    }
  }

  /**
   * 处理同步队列
   */
  async processSyncQueue() {
    if (this.isProcessing || this.syncQueue.length === 0) {
      return;
    }

    this.isProcessing = true;

    try {
      // 按优先级排序
      this.syncQueue.sort((a, b) => (b.priority || 0) - (a.priority || 0));

      while (this.syncQueue.length > 0) {
        const task = this.syncQueue.shift();
        
        try {
          await this.executeSyncTask(task);
        } catch (error) {
          console.error('[DataSync] 执行同步任务失败:', error, task);
        }
      }
    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * 执行同步任务
   * @param {object} task - 同步任务
   */
  async executeSyncTask(task) {
    const { type, pageContext, keys, options } = task;

    switch (type) {
      case 'toGlobal':
        this.syncToGlobal(pageContext, keys);
        break;
      case 'toPage':
        this.syncToPage(pageContext, keys);
        break;
      case 'bidirectional':
        this.bidirectionalSync(pageContext, keys);
        break;
      default:
        console.warn('[DataSync] 未知的同步任务类型:', type);
    }

    // 如果有延迟，等待指定时间
    if (options && options.delay) {
      await new Promise(resolve => setTimeout(resolve, options.delay));
    }
  }

  /**
   * 设置全局数据监听器
   */
  setupGlobalDataWatcher() {
    try {
      const app = getApp();
      if (!app || !app.globalData) {
        return;
      }

      // 创建全局数据变化监听机制
      const originalGlobalData = app.globalData;
      const self = this;

      // 使用Proxy监听全局数据变化（如果支持）
      if (typeof Proxy !== 'undefined') {
        app.globalData = new Proxy(originalGlobalData, {
          set(target, key, value) {
            const oldValue = target[key];
            target[key] = value;

            // 如果数据有变化，触发同步事件
            if (!self.isDataEqual(oldValue, value)) {
              self.emit('globalDataChange', { key, oldValue, newValue: value });
            }

            return true;
          }
        });
      }

      console.log('[DataSync] 全局数据监听器设置完成');
    } catch (error) {
      console.error('[DataSync] 设置全局数据监听器失败:', error);
    }
  }

  /**
   * 比较两个数据是否相等
   * @param {*} data1 - 数据1
   * @param {*} data2 - 数据2
   * @returns {boolean} 是否相等
   */
  isDataEqual(data1, data2) {
    try {
      // 简单类型比较
      if (data1 === data2) {
        return true;
      }

      // null/undefined比较
      if (data1 == null || data2 == null) {
        return data1 == data2;
      }

      // 类型不同
      if (typeof data1 !== typeof data2) {
        return false;
      }

      // 数组比较
      if (Array.isArray(data1) && Array.isArray(data2)) {
        if (data1.length !== data2.length) {
          return false;
        }
        return data1.every((item, index) => this.isDataEqual(item, data2[index]));
      }

      // 对象比较
      if (typeof data1 === 'object') {
        const keys1 = Object.keys(data1);
        const keys2 = Object.keys(data2);
        
        if (keys1.length !== keys2.length) {
          return false;
        }
        
        return keys1.every(key => this.isDataEqual(data1[key], data2[key]));
      }

      return false;
    } catch (error) {
      console.error('[DataSync] 数据比较失败:', error);
      return false;
    }
  }

  /**
   * 监听事件
   * @param {string} event - 事件名称
   * @param {function} listener - 监听器函数
   */
  on(event, listener) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event).push(listener);
  }

  /**
   * 移除事件监听器
   * @param {string} event - 事件名称
   * @param {function} listener - 监听器函数
   */
  off(event, listener) {
    const eventListeners = this.listeners.get(event);
    if (eventListeners) {
      const index = eventListeners.indexOf(listener);
      if (index > -1) {
        eventListeners.splice(index, 1);
      }
    }
  }

  /**
   * 触发事件
   * @param {string} event - 事件名称
   * @param {*} data - 事件数据
   */
  emit(event, data) {
    const eventListeners = this.listeners.get(event) || [];
    eventListeners.forEach(listener => {
      try {
        listener(data);
      } catch (error) {
        console.error('[DataSync] 事件监听器执行失败:', error);
      }
    });
  }

  /**
   * 获取同步状态
   * @returns {object} 同步状态信息
   */
  getSyncStatus() {
    return {
      rulesCount: this.syncRules.size,
      queueLength: this.syncQueue.length,
      isProcessing: this.isProcessing,
      listenersCount: Array.from(this.listeners.values()).reduce((sum, arr) => sum + arr.length, 0)
    };
  }

  /**
   * 清理资源
   */
  destroy() {
    try {
      this.syncRules.clear();
      this.syncQueue = [];
      this.listeners.clear();
      this.isProcessing = false;
      
      console.log('[DataSync] 数据同步管理器已销毁');
    } catch (error) {
      console.error('[DataSync] 销毁失败:', error);
    }
  }
}

// 创建单例实例
const dataSyncManager = new DataSyncManager();

module.exports = dataSyncManager;