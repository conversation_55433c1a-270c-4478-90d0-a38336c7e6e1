const app = getApp()

Page({
  data: {
    rechargeRecords: [],
    totalRecords: 0,
    page: 1,
    pageSize: 20,
    loading: false,
    loadingMore: false,
    currentTab: 'all',
    tabs: [
      { value: 'all', label: '全部' },
      { value: 'pending', label: '待核销' },
      { value: 'verified', label: '已核销' },
      { value: 'cancelled', label: '已取消' }
    ],
    dateRange: {
      start: '',
      end: ''
    },
    searchKeyword: '',
    showFilterModal: false
  },

  onLoad() {
    try {
      // 设置日期范围（默认近30天）
      const today = new Date()
      const thirtyDaysAgo = new Date()
      thirtyDaysAgo.setDate(today.getDate() - 30)
      
      this.setData({
        dateRange: {
          start: this.formatDate(thirtyDaysAgo),
          end: this.formatDate(today)
        }
      })
      
      console.log('初始日期范围:', this.data.dateRange)
      
      // 加载初始数据
      this.loadRechargeRecords()
    } catch (err) {
      console.error('页面加载过程中发生错误:', err)
      wx.showToast({
        title: '页面加载错误，请重试',
        icon: 'none'
      })
    }
  },
  
  onPullDownRefresh() {
    this.setData({ page: 1 })
    this.loadRechargeRecords().then(() => {
      wx.stopPullDownRefresh()
    })
  },
  
  onReachBottom() {
    if (this.data.loadingMore || this.data.rechargeRecords.length >= this.data.totalRecords) {
      return
    }
    
    this.setData({
      loadingMore: true,
      page: this.data.page + 1
    })
    
    this.loadRechargeRecords(true)
  },
  
  // 加载充值记录
  async loadRechargeRecords(loadMore = false) {
    if (this.data.loading) return
    
    this.setData({ loading: true })
    
    try {
      const { page, pageSize, currentTab, dateRange, searchKeyword } = this.data
      
      // 准备请求参数
      const data = {
        page,
        pageSize,
        startDate: dateRange.start + ' 00:00:00', // 确保日期格式正确
        endDate: dateRange.end + ' 23:59:59'     // 确保包含整天时间
      }
      
      // 如果选择了状态筛选
      if (currentTab !== 'all') {
        data.status = currentTab
      }
      
      // 如果有搜索关键词，尝试作为openid搜索
      if (searchKeyword) {
        data.openid = searchKeyword
      }
      
      console.log('请求充值记录参数:', data)
      
      // 调用云函数
      const res = await wx.cloud.callFunction({
        name: 'rechargeManager',
        data: {
          type: 'admin',
          action: 'getRechargeRecords',
          data
        }
      })
      
      console.log('充值记录响应:', res.result)
      
      if (res.result && res.result.code === 0) {
        const newRecords = res.result.data.list || []
        
        console.log('获取到充值记录:', newRecords.length, '条')
        
        // 格式化数据
        newRecords.forEach(record => {
          try {
            // 格式化日期
            if (record.createTime) {
              try {
                record.createTimeStr = this.formatDateTime(new Date(record.createTime))
              } catch (err) {
                record.createTimeStr = '日期格式错误'
                console.error('创建时间格式化错误:', err)
              }
            }
            if (record.verifyTime) {
              try {
                record.verifyTimeStr = this.formatDateTime(new Date(record.verifyTime))
              } catch (err) {
                record.verifyTimeStr = '日期格式错误'
                console.error('验证时间格式化错误:', err)
              }
            }
            if (record.cancelTime) {
              try {
                record.cancelTimeStr = this.formatDateTime(new Date(record.cancelTime))
              } catch (err) {
                record.cancelTimeStr = '日期格式错误'
                console.error('取消时间格式化错误:', err)
              }
            }
            
            // 确保金额字段存在并正确
            // 如果amount不存在但price存在，使用price值
            if (record.amount === undefined && record.price !== undefined) {
              record.amount = parseFloat(record.price);
            } else if (record.amount !== undefined) {
              record.amount = parseFloat(record.amount);
            } else {
              record.amount = 0;
            }
            
            // 如果bonus不存在但bonusAmount存在，使用bonusAmount值
            if (record.bonus === undefined && record.bonusAmount !== undefined) {
              record.bonus = parseFloat(record.bonusAmount);
            } else if (record.bonus !== undefined) {
              record.bonus = parseFloat(record.bonus);
            } else {
              record.bonus = 0;
            }
            
            // 确保totalAmount是正确计算的
            record.totalAmount = record.amount + record.bonus;
            
            // 记录金额信息，方便调试
            console.log('管理端记录金额信息:', {
              amount: record.amount,
              bonus: record.bonus,
              totalAmount: record.totalAmount,
              recordId: record._id
            });
            
            // 格式化状态
            record.statusText = this.getStatusText(record.status)
            // 格式化金额
            record.amountStr = '￥' + (record.amount).toFixed(2)
            record.bonusStr = '￥' + (record.bonus).toFixed(2)
            record.totalAmountStr = '￥' + (record.totalAmount).toFixed(2)
          } catch (err) {
            console.error('处理充值记录项出错:', err, record)
          }
        })
        
        this.setData({
          rechargeRecords: loadMore ? [...this.data.rechargeRecords, ...newRecords] : newRecords,
          totalRecords: res.result.data.total || 0,
          loading: false,
          loadingMore: false
        })
      } else {
        console.error('获取充值记录失败:', res.result)
        wx.showToast({
          title: res.result?.message || '加载失败',
          icon: 'none'
        })
        this.setData({ loading: false, loadingMore: false })
      }
    } catch (err) {
      console.error('加载充值记录失败:', err)
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      })
      this.setData({ loading: false, loadingMore: false })
    }
  },
  
  // 切换标签页
  switchTab(e) {
    const tab = e.currentTarget.dataset.tab
    
    if (tab === this.data.currentTab) return
    
    this.setData({
      currentTab: tab,
      page: 1,
      rechargeRecords: []
    })
    
    this.loadRechargeRecords()
  },
  
  // 打开筛选弹窗
  showFilter() {
    this.setData({
      showFilterModal: true
    })
  },
  
  // 关闭筛选弹窗
  closeFilter() {
    this.setData({
      showFilterModal: false
    })
  },
  
  // 应用筛选条件
  applyFilter() {
    this.setData({
      page: 1,
      rechargeRecords: [],
      showFilterModal: false
    })
    
    this.loadRechargeRecords()
  },
  
  // 重置筛选条件
  resetFilter() {
    // 设置日期范围（默认近30天）
    const today = new Date()
    const thirtyDaysAgo = new Date()
    thirtyDaysAgo.setDate(today.getDate() - 30)
    
    this.setData({
      dateRange: {
        start: this.formatDate(thirtyDaysAgo),
        end: this.formatDate(today)
      },
      searchKeyword: '',
      currentTab: 'all',
      page: 1,
      rechargeRecords: []
    })
    
    this.loadRechargeRecords()
    this.closeFilter()
  },
  
  // 输入搜索关键词
  inputKeyword(e) {
    this.setData({
      searchKeyword: e.detail.value.trim()
    })
  },
  
  // 搜索
  search() {
    this.setData({
      page: 1,
      rechargeRecords: []
    })
    
    this.loadRechargeRecords()
  },
  
  // 选择开始日期
  bindStartDateChange(e) {
    this.setData({
      'dateRange.start': e.detail.value
    })
  },
  
  // 选择结束日期
  bindEndDateChange(e) {
    this.setData({
      'dateRange.end': e.detail.value
    })
  },
  
  // 查看充值记录详情
  viewRecordDetail(e) {
    const recordId = e.currentTarget.dataset.id
    const record = this.data.rechargeRecords.find(r => r._id === recordId)
    
    if (!record) return
    
    // 将记录存储到全局数据中，方便详情页使用
    app.globalData.tempRechargeRecord = record
    
    wx.navigateTo({
      url: './detail/detail?id=' + recordId
    })
  },
  
  // 格式化日期：YYYY-MM-DD
  formatDate(date) {
    try {
      if (!(date instanceof Date) || isNaN(date)) {
        console.error('无效的日期对象:', date)
        return '无效日期'
      }
      
      const year = date.getFullYear()
      const month = (date.getMonth() + 1).toString().padStart(2, '0')
      const day = date.getDate().toString().padStart(2, '0')
      return `${year}-${month}-${day}`
    } catch (err) {
      console.error('日期格式化错误:', err)
      return '日期错误'
    }
  },
  
  // 格式化日期时间：YYYY-MM-DD HH:MM:SS
  formatDateTime(date) {
    try {
      if (!(date instanceof Date) || isNaN(date)) {
        console.error('无效的日期时间对象:', date)
        return '无效日期'
      }
      
      const year = date.getFullYear()
      const month = (date.getMonth() + 1).toString().padStart(2, '0')
      const day = date.getDate().toString().padStart(2, '0')
      const hours = date.getHours().toString().padStart(2, '0')
      const minutes = date.getMinutes().toString().padStart(2, '0')
      
      return `${year}-${month}-${day} ${hours}:${minutes}`
    } catch (err) {
      console.error('日期时间格式化错误:', err)
      return '日期错误'
    }
  },
  
  // 获取状态文本
  getStatusText(status) {
    const statusMap = {
      'pending': '待核销',
      'verified': '已核销',
      'cancelled': '已取消'
    }

    return statusMap[status] || '未知状态'
  },
  
  // 返回上一页
  navigateBack() {
    wx.navigateBack()
  }
}) 