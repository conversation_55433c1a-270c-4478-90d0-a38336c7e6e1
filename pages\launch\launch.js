/**
 * 启动页
 * 负责资源预加载和动画展示
 */
// ================== 启动页功能 (请勿修改) ==================
// @feature: 启动页动画和跳转
// @version: 1.0.0
// @warning: 以下代码已完成启动页功能，请勿修改

const { LAUNCH_CONFIG } = require("../../config/index");
const { callCloudFunction } = require("../../utils/request/index");
const { getVideoList } = require("../../utils/video/index");

// 扩展启动页配置
Object.assign(LAUNCH_CONFIG, {
  animation: {
    firstImageDuration: 1200, // 第一张图片显示持续时间(1.2秒)
    secondImageDelay: 1000, // 第二张图片开始显示的延迟时间(1秒)
    totalDuration: 3000, // 启动页最短显示时间(3秒)
    fadeOutDuration: 500, // 淡出动画持续时间(0.5秒)
  },
  // 添加本地图片路径配置
  localImages: {
    useLocalImages: true, // 是否使用本地图片（测试用）
    first: "../../static/icons/launch_first.png", // 本地第一张图片路径(改为PNG格式)
    second: "../../static/icons/launch_second.png", // 本地第二张图片路径
  },
});

Page({
  data: {
    firstImageUrl: "", // 第一张图片URL
    secondImageUrl: "", // 第二张图片URL
    isFirstImageLoaded: false, // 第一张图片是否加载完成
    showSecondImage: false, // 是否显示第二张图片
    isFadingOut: false, // 是否正在淡出
    isDataPreloaded: false, // 数据是否预加载完成
    isGalleryDataReady: false, // 画廊数据是否准备就绪
    canSkip: false, // 是否允许点击跳过
    transitionTimer: null,
    canSkipTimer: null, // 控制允许跳过的定时器
    startTime: 0, // 启动页开始时间
    firstShimmerClass: "", // 第一张图片闪光效果类名
    secondShimmerClass: "", // 第二张图片闪光效果类名
    useLocalImages: LAUNCH_CONFIG.localImages.useLocalImages, // 是否使用本地图片
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {
    // 设置启动页状态
    wx.setStorageSync("showingLaunch", true);
    this.setData({
      startTime: Date.now(), // 记录启动页开始时间
    });

    // 判断是使用本地图片还是云端图片
    if (this.data.useLocalImages) {
      this.loadLocalImages();
    } else {
      this.loadLaunchImages();
    }

    // 同时预加载多个页面的数据
    Promise.all([
      this.preloadGalleryData(), // 画廊页数据
      this.preloadVideoData(), // 视频列表数据
      this.preloadServiceData(), // 服务列表数据
      this.preloadCarouselData(), // 轮播图数据
      this.preloadAdData(), // 广告数据
    ])
      .then(() => {
        console.log("[LaunchPage] 所有数据预加载完成");
      })
      .catch((error) => {
        console.error("[LaunchPage] 数据预加载出错:", error);
      });

    // 设置定时器，在规定时间后才允许点击跳过
    const canSkipTimer = setTimeout(() => {
      this.setData({ canSkip: true });
    }, LAUNCH_CONFIG.animation.totalDuration);

    this.setData({ canSkipTimer });
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
    // 清除启动页状态
    wx.setStorageSync("showingLaunch", false);
    wx.setStorageSync("hasShownLaunch", true);
    // 清理定时器
    if (this.data.transitionTimer) {
      clearTimeout(this.data.transitionTimer);
    }
    if (this.data.canSkipTimer) {
      clearTimeout(this.data.canSkipTimer);
    }
  },

  // 加载本地静态图片（测试用）
  loadLocalImages() {
    console.log("使用本地图片作为启动页");

    // 使用绝对路径确保在手机上也能正确加载
    const firstImagePath = this.getCorrectPath(LAUNCH_CONFIG.localImages.first);
    const secondImagePath = this.getCorrectPath(
      LAUNCH_CONFIG.localImages.second
    );

    this.setData({
      firstImageUrl: firstImagePath,
      secondImageUrl: secondImagePath,
    });

    console.log("已设置本地启动页图片:", firstImagePath, secondImagePath);
  },

  // 获取正确的图片路径（适配小程序环境）
  getCorrectPath(path) {
    // 如果路径已经以'/'开头，则移除它
    if (path.startsWith("/")) {
      path = path.substring(1);
    }
    // 添加必要的前缀，确保在小程序环境中正确解析
    return path;
  },

  // 加载启动页图片
  async loadLaunchImages() {
    try {
      console.log("开始加载启动页图片");

      // 调用launchManager云函数获取最新的启动页图片
      const result = await callCloudFunction("launchManager", {
        action: "getLaunchImages",
        timestamp: Date.now(), // 添加时间戳参数强制刷新
      });
      console.log("获取启动页图片结果:", result);

      if ((result.code === 0 || result.code === 200) && result.data) {
        console.log("获取到启动页图片:", result.data);

        // 设置图片URL，添加时间戳防止浏览器缓存
        this.setData({
          firstImageUrl:
            result.data.first +
            (result.data.first.includes("?") ? "&" : "?") +
            "t=" +
            Date.now(),
          secondImageUrl:
            result.data.second +
            (result.data.second.includes("?") ? "&" : "?") +
            "t=" +
            Date.now(),
        });

        console.log(
          "已设置启动页图片:",
          this.data.firstImageUrl,
          this.data.secondImageUrl
        );
      } else {
        console.error("[LaunchPage] 获取启动页图片失败:", result);
        // 如果没有获取到图片，使用本地图片作为备选
        this.loadLocalImages();
        // 确保最小显示时间
        this.ensureMinimumDuration();
      }
    } catch (error) {
      console.error("[LaunchPage] 加载启动页图片失败:", error);
      // 发生错误时，使用本地图片作为备选
      this.loadLocalImages();
      // 确保最小显示时间
      this.ensureMinimumDuration();
    }
  },

  // 预加载视频数据
  async preloadVideoData() {
    try {
      console.log("[LaunchPage] 开始预加载视频数据");

      // 强制刷新视频列表缓存
      wx.removeStorageSync("video_list_cache");

      const videoList = await getVideoList(true); // 添加true参数强制刷新
      console.log(
        "[LaunchPage] 预加载视频数据成功，视频数量:",
        videoList ? videoList.length : 0
      );

      // 确保返回的是有效数组
      if (!videoList || !Array.isArray(videoList) || videoList.length === 0) {
        console.warn("[LaunchPage] 预加载视频列表为空或无效");
        // 创建一个模拟的视频列表，避免后续使用时出错
        getApp().globalData.videoList = [];
        this.setData({ isDataPreloaded: true });
        return [];
      }

      // 立即将数据存储到全局，确保页面切换时可用
      const app = getApp();
      if (app && app.globalData) {
        app.globalData.videoList = videoList;
        app.globalData.videoDataReady = true; // 标记数据已准备好
        console.log("[LaunchPage] 视频数据已存储到全局，立即可用");
      }

      // 将预加载的数据存储到全局状态
      getApp().globalData.videoList = videoList;

      // 预加载封面图片
      this.preloadCoverImages(videoList);

      this.setData({ isDataPreloaded: true });

      // 检查是否可以结束启动页
      this.checkAndNavigate();

      return videoList;
    } catch (error) {
      console.error("[LaunchPage] 预加载视频数据失败:", error);
      // 即使预加载失败，也继续执行
      this.setData({ isDataPreloaded: true });
      // 创建一个模拟的视频列表，避免后续使用时出错
      getApp().globalData.videoList = [];
      return [];
    }
  },

  // 预加载服务列表数据
  async preloadServiceData() {
    try {
      console.log("[LaunchPage] 开始预加载服务列表数据");

      // 调用正确的云函数获取服务列表数据
      const result = await wx.cloud.callFunction({
        name: "appointmentManager",
        data: {
          action: "getServices",
          type: "user", // 使用user类型，服务端会自动筛选可见的服务
          timestamp: Date.now(), // 添加时间戳防止缓存
        },
      });

      let serviceData = [];

      // 尝试多种可能的数据结构
      if (result.result && result.result.data) {
        serviceData = result.result.data;
      } else if (
        result.result &&
        result.result.code === 0 &&
        result.result.services
      ) {
        serviceData = result.result.services;
      } else if (Array.isArray(result.result)) {
        serviceData = result.result;
      } else if (result.result) {
        // 尝试其他可能的结构
        const possibleArrays = Object.values(result.result).filter((val) =>
          Array.isArray(val)
        );
        if (possibleArrays.length > 0) {
          serviceData = possibleArrays[0];
        }
      }

      console.log(
        "[LaunchPage] 预加载服务列表数据成功，服务数量:",
        serviceData.length
      );

      // 将预加载的数据存储到全局状态
      const app = getApp();
      app.globalData.serviceList = serviceData;
      app.globalData.serviceLoaded = true;

      return serviceData;
    } catch (error) {
      console.error("[LaunchPage] 预加载服务列表数据出错:", error);
      // 创建一个空的服务列表，避免后续使用时出错
      const app = getApp();
      app.globalData.serviceList = [];
      app.globalData.serviceLoaded = true;
      return [];
    }
  },

  // 预加载轮播图数据
  async preloadCarouselData() {
    try {
      console.log("[LaunchPage] 开始预加载轮播图数据");

      // 调用正确的云函数获取轮播图数据
      const result = await wx.cloud.callFunction({
        name: "appointmentManager",
        data: {
          action: "getCarousel",
          timestamp: Date.now(), // 添加时间戳防止缓存
        },
      });

      if (result && result.result && result.result.data) {
        const carouselList = result.result.data || [];
        console.log(
          "[LaunchPage] 预加载轮播图数据成功，轮播图数量:",
          carouselList.length
        );

        // 将预加载的数据存储到全局状态
        const app = getApp();
        app.globalData.carouselList = carouselList;
        app.globalData.carouselLoaded = true;

        return carouselList;
      } else {
        console.error("[LaunchPage] 预加载轮播图数据失败:", result);
        // 创建一个空的轮播图数据，避免后续使用时出错
        const app = getApp();
        app.globalData.carouselList = [];
        app.globalData.carouselLoaded = true;
        return [];
      }
    } catch (error) {
      console.error("[LaunchPage] 预加载轮播图数据出错:", error);
      // 创建一个空的轮播图数据，避免后续使用时出错
      const app = getApp();
      app.globalData.carouselList = [];
      app.globalData.carouselLoaded = true;
      return [];
    }
  },

  // 预加载画廊数据
  async preloadGalleryData() {
    try {
      console.log("[LaunchPage] 开始预加载画廊数据");

      // 调用云函数获取画廊数据
      const result = await wx.cloud.callFunction({
        name: "galleryManager",
        data: {
          action: "getVisibleGalleryList",
          data: {
            page: 1,
            pageSize: 10, // 只预加载前10条数据，确保加载速度
            timestamp: Date.now(), // 添加时间戳防止缓存
            includeVideoInfo: true, // 添加参数，请求包含视频信息
          },
        },
      });

      if (
        result.result &&
        result.result.success === true &&
        result.result.data &&
        result.result.data.list
      ) {
        const serverArticles = result.result.data.list || [];
        console.log(
          "[LaunchPage] 预加载画廊数据成功，文章数量:",
          serverArticles.length
        );

        // 处理文章数据，确保包含正确的ID字段和必要的属性
        const processedArticles = serverArticles.map((item) => {
          // 确保时间戳是数字类型
          let timestamp = Date.now();
          if (item.createTime) {
            if (typeof item.createTime === "string") {
              timestamp = new Date(item.createTime).getTime();
            } else if (item.createTime instanceof Date) {
              timestamp = item.createTime.getTime();
            } else if (item.createTime["$date"]) {
              // 处理MongoDB日期格式
              timestamp = new Date(item.createTime["$date"]).getTime();
            }
          }

          // 处理视频信息，确保预加载视频封面图
          let videoInfo = null;
          if (item.videoInfo) {
            videoInfo = {
              ...item.videoInfo,
              coverUrl: item.videoInfo.coverUrl || "",
              videoUrl: "", // 不预加载视频URL，只预加载封面图
            };

            // 预加载视频封面图
            if (videoInfo.coverUrl) {
              wx.getImageInfo({
                src: videoInfo.coverUrl,
                success: () =>
                  console.log(
                    "[LaunchPage] 视频封面图预加载成功:",
                    videoInfo.coverUrl
                  ),
                fail: (err) =>
                  console.error("[LaunchPage] 视频封面图预加载失败:", err),
              });
            }
          }

          return {
            id: item._id, // 确保ID字段存在
            originalId: item._id,
            title: item.mainTitle || "",
            subtitle: item.subTitle || "",
            description: "",
            coverImage: item.coverUrl || "/static/logo.png",
            content: item.content || "",
            detailImages: item.detailImages || [],
            authorName: "---今禧美学---",
            authorAvatar: "/static/logo.png",
            type: "gallery",
            timestamp: timestamp,
            viewCount: Math.floor(Math.random() * 100),
            order: item.order || 0,
            isVisible: item.isVisible,
            categoryId: item.categoryId || "",
            videoInfo: videoInfo, // 添加视频信息
          };
        });

        // 将预加载的数据存储到全局状态
        const app = getApp();

        // 初始化画廊数据缓存结构
        if (!app.globalData.galleryArticles) {
          app.globalData.galleryArticles = {
            allArticles: [],
            categoryArticles: { all: [] },
            currentArticles: [],
            currentCategory: "all",
          };
        }

        // 存储预加载的数据
        app.globalData.galleryArticles.allArticles = processedArticles;
        app.globalData.galleryArticles.categoryArticles["all"] =
          processedArticles;
        app.globalData.galleryArticles.currentArticles = processedArticles;

        // 标记画廊数据已预加载
        app.globalData.galleryLoaded = true;

        this.setData({ isGalleryDataReady: true });

        // 检查是否可以结束启动页
        this.checkAndNavigate();

        return processedArticles;
      } else {
        console.error("[LaunchPage] 预加载画廊数据失败:", result);
        return null;
      }
    } catch (error) {
      console.error("[LaunchPage] 预加载画廊数据出错:", error);
      return null;
    }
  },

  // 第一张图片加载完成
  onFirstImageLoad() {
    this.setData({ isFirstImageLoaded: true });

    // 添加延迟后显示闪光效果
    setTimeout(() => {
      this.setData({ firstShimmerClass: "launch-shimmer-effect" });
    }, 300);

    // 设置第二张图片显示定时器，使用新的secondImageDelay参数
    const transitionTimer = setTimeout(() => {
      this.setData({ showSecondImage: true });
    }, LAUNCH_CONFIG.animation.secondImageDelay);

    this.setData({ transitionTimer });
  },

  // 第二张图片加载完成
  onSecondImageLoad() {
    // 图片加载完成后，延迟显示第二张图的闪光效果
    setTimeout(() => {
      this.setData({ secondShimmerClass: "launch-shimmer-effect" });
    }, 300);
    console.log("第二张图片加载完成，闪光效果已启动");

    // 检查是否可以结束启动页
    this.checkAndNavigate();
  },

  // 图片加载失败处理
  onImageLoadError(e) {
    // 获取失败的图片类型和源
    const imageType = e.currentTarget.dataset.type || "未知";
    const imageSrc = e.currentTarget.dataset.src || "未知路径";

    console.error(`[LaunchPage] ${imageType}图片(${imageSrc})加载失败`);
    console.error("[LaunchPage] 请检查图片路径是否正确，以及图片是否存在");

    // 图片加载失败时，确保最低显示时间
    this.ensureMinimumDuration();
  },

  // 确保最低显示时间
  ensureMinimumDuration() {
    const elapsedTime = Date.now() - this.data.startTime;
    const remainingTime = Math.max(
      0,
      LAUNCH_CONFIG.animation.totalDuration - elapsedTime
    );

    // 仅设置可点击状态，不自动跳转
    setTimeout(() => {
      this.setData({ canSkip: true });
      // 检查是否可以结束启动页
      this.checkAndNavigate();
    }, remainingTime);
  },

  /**
   * 点击跳过
   */
  onTapToSkip() {
    // 如果未达到最小显示时间，不允许跳过
    if (!this.data.canSkip) {
      return;
    }

    // 清除所有定时器
    if (this.data.transitionTimer) {
      clearTimeout(this.data.transitionTimer);
    }
    this.startFadeOut();
  },

  // 检查是否可以结束启动页并跳转
  checkAndNavigate() {
    // 检查是否满足跳转条件：
    // 1. 已经达到最小显示时间（canSkip为true）
    // 2. 画廊数据已准备就绪
    // 3. 视频数据已预加载完成
    if (
      this.data.canSkip &&
      this.data.isGalleryDataReady &&
      this.data.isDataPreloaded
    ) {
      console.log("[LaunchPage] 满足跳转条件，准备结束启动页");
      this.startFadeOut();
    } else {
      console.log(
        "[LaunchPage] 尚未满足跳转条件，canSkip:",
        this.data.canSkip,
        "isGalleryDataReady:",
        this.data.isGalleryDataReady,
        "isDataPreloaded:",
        this.data.isDataPreloaded
      );
    }
  },

  // 跳转到首页
  navigateToIndex() {
    if (this.isNavigating) return;
    this.isNavigating = true;

    // 清除所有定时器
    if (this.data.transitionTimer) clearTimeout(this.data.transitionTimer);
    if (this.data.canSkipTimer) clearTimeout(this.data.canSkipTimer);

    this.setData({ isFadingOut: true });

    // 将跳转状态存储到全局数据中
    const app = getApp();

    // 设置全局状态，表明正从启动页跳转而来，但不立即启用转场动画
    app.globalData.isFromLaunch = true;
    app.globalData.launchTransitionComplete = false; // 先设为false，等主页完全加载后再设为true

    // 延迟跳转，等待淡出动画完成
    setTimeout(() => {
      wx.switchTab({
        url: "/pages/gallery/gallery",
        success: () => {
          console.log("[LaunchPage] 成功跳转到画廊页面");
          this.isNavigating = false;
        },
        fail: (error) => {
          console.error("[LaunchPage] 跳转到画廊页面失败:", error);
          this.isNavigating = false;
        },
      });
    }, LAUNCH_CONFIG.animation.fadeOutDuration);
  },

  // 开始淡出动画
  startFadeOut() {
    this.setData({ isFadingOut: true });
    setTimeout(() => {
      this.navigateToIndex();
    }, LAUNCH_CONFIG.animation.fadeOutDuration);
  },

  // 阻止触摸移动事件
  catchTouchMove() {
    return false;
  },

  // 预加载广告数据
  async preloadAdData() {
    try {
      console.log("[LaunchPage] 开始预加载广告数据");

      // 首先尝试使用launchManager云函数获取广告图片
      const launchResult = await wx.cloud.callFunction({
        name: "launchManager",
        data: {
          action: "getLaunchImages",
          timestamp: Date.now(), // 添加时间戳确保不使用缓存
        },
      });

      let adItems = [];
      let hasAdContent = false;

      if (
        launchResult.result &&
        launchResult.result.code === 200 &&
        launchResult.result.data
      ) {
        const launchData = launchResult.result.data;
        // 使用广告图片
        adItems = [
          {
            imageUrl: launchData.first,
            linkUrl: "/pages/appointment/appointment",
          },
          {
            imageUrl: launchData.second,
            linkUrl: "/pages/gallery/gallery",
          },
        ];
        hasAdContent = true;
        console.log("[LaunchPage] 预加载广告数据成功（来自launchManager）");
      } else {
        // 如果广告图片获取失败，尝试从contentManager获取
        console.log("[LaunchPage] 广告图片获取失败，尝试从contentManager获取");

        try {
          const contentResult = await wx.cloud.callFunction({
            name: "contentManager",
            data: {
              action: "getPromoItems", // 暂时保留原有action名称，等云函数修改后再更新
            },
          });

          if (
            contentResult.result &&
            contentResult.result.code === 0 &&
            contentResult.result.data &&
            contentResult.result.data.length > 0
          ) {
            // 获取成功
            adItems = contentResult.result.data;
            hasAdContent = true;
            console.log(
              "[LaunchPage] 预加载广告数据成功（来自contentManager）"
            );
          } else {
            // 没有广告内容，设置空状态
            adItems = [];
            hasAdContent = false;
            console.log("[LaunchPage] 暂无任何展示信息");
          }
        } catch (contentError) {
          console.error(
            "[LaunchPage] 从contentManager获取广告数据失败:",
            contentError
          );
          // 没有广告内容，设置空状态
          adItems = [];
          hasAdContent = false;
          console.log("[LaunchPage] 暂无任何展示信息");
        }
      }

      // 将预加载的广告数据存储到全局状态
      const app = getApp();
      app.globalData.adItems = adItems;
      app.globalData.adLoaded = true;
      app.globalData.hasAdContent = hasAdContent; // 新增标志，表示是否有广告内容

      console.log("[LaunchPage] 广告数据预加载完成，数量:", adItems.length);
      return adItems;
    } catch (error) {
      console.error("[LaunchPage] 预加载广告数据出错:", error);

      // 设置为空数组，表示没有广告内容
      const app = getApp();
      app.globalData.adItems = [];
      app.globalData.adLoaded = true;
      app.globalData.hasAdContent = false; // 新增标志，表示没有广告内容

      return [];
    }
  },

  // 预加载封面图片
  preloadCoverImages(videoList) {
    if (!videoList || !Array.isArray(videoList) || videoList.length === 0) {
      console.log("[LaunchPage] 没有视频列表，跳过封面图预加载");
      return;
    }

    console.log("[LaunchPage] 开始预加载视频封面图，数量:", videoList.length);

    // 限制同时预加载的图片数量，避免占用过多资源
    const maxConcurrentLoads = 3;
    let loadedCount = 0;
    let errorCount = 0;

    const loadCoverImage = (video, index) => {
      return new Promise((resolve) => {
        if (!video.coverUrl) {
          console.warn(
            "[LaunchPage] 视频封面图URL为空，跳过:",
            video.mainTitle || video.id
          );
          resolve({ success: false, reason: "empty_url" });
          return;
        }

        // 检查URL格式
        if (
          !video.coverUrl.startsWith("http://") &&
          !video.coverUrl.startsWith("https://") &&
          !video.coverUrl.startsWith("/")
        ) {
          console.warn(
            "[LaunchPage] 视频封面图URL格式无效，跳过:",
            video.mainTitle,
            video.coverUrl
          );
          resolve({ success: false, reason: "invalid_url" });
          return;
        }

        // 使用微信小程序的图片预加载API，增加超时处理
        const timeoutId = setTimeout(() => {
          errorCount++;
          console.warn(`[LaunchPage] 封面图预加载超时:`, video.mainTitle);
          resolve({ success: false, video: video, reason: "timeout" });
        }, 8000); // 8秒超时

        wx.getImageInfo({
          src: video.coverUrl,
          success: (res) => {
            clearTimeout(timeoutId);
            loadedCount++;
            console.log(
              `[LaunchPage] 封面图预加载成功 (${loadedCount}/${videoList.length}):`,
              video.mainTitle
            );
            resolve({ success: true, video: video, imageInfo: res });
          },
          fail: (err) => {
            clearTimeout(timeoutId);
            errorCount++;
            // 减少日志输出，只记录失败次数而不是每个失败详情
            // console.warn(`[LaunchPage] 封面图预加载失败:`, video.mainTitle, "错误:", err.errMsg);

            // 移除默认封面图逻辑 - 所有封面图都应该来自云端
            // 封面图加载失败就是失败，不尝试使用默认图片
            resolve({ success: false, video: video, error: err });
          },
        });
      });
    };

    // 分批预加载，避免同时发起太多请求
    const loadInBatches = async () => {
      for (let i = 0; i < videoList.length; i += maxConcurrentLoads) {
        const batch = videoList.slice(i, i + maxConcurrentLoads);
        const promises = batch.map((video, index) =>
          loadCoverImage(video, i + index)
        );

        try {
          await Promise.all(promises);
        } catch (error) {
          console.error("[LaunchPage] 批量预加载封面图出错:", error);
        }

        // 短暂延迟，避免请求过于频繁
        if (i + maxConcurrentLoads < videoList.length) {
          await new Promise((resolve) => setTimeout(resolve, 100));
        }
      }

      // 只输出总体结果
      console.log(`[LaunchPage] 封面图预加载完成，成功/总数: ${loadedCount}/${videoList.length}`);
    };

    // 开始预加载
    loadInBatches().catch((error) => {
      console.error("[LaunchPage] 封面图预加载过程出错:", error);
    });
  },
});
