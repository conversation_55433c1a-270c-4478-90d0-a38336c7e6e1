// ================ 基础框架配置 (请勿修改) ================
// @feature: 框架初始化
// @version: 1.0.0
// @author: Claude
// @warning: 以下代码已完成框架配置，请勿修改
/* global getCurrentPages wx */
const { initCloud } = require('./utils/cloud');
const wxUtils = require('./utils/wx');
const EventCenter = require('./utils/eventCenter').default; // 导入事件中心
const deviceUtils = require('./utils/device'); // 导入设备工具函数

App({
  onLaunch() {
    // 初始化云开发环境
    initCloud();
    
    // 自动静默登录获取openid
    this.autoSilentLogin();
    
    // 使用新API获取系统信息并存储到全局数据中
    try {
      // 使用工具类中的新API实现
      wxUtils.getSystemInfo()
        .then(systemInfo => {
          this.globalData.systemInfo = systemInfo;
        })
        .catch(err => {
          console.error('获取系统信息失败:', err);
        });
    } catch (err) {
      console.error('获取系统信息出错:', err);
    }
    
    // 初始化全局事件中心
    this.globalData.eventCenter = EventCenter;
    
    // 初始化全局客服按钮状态
    try {
      const isCustomerServiceHidden = wx.getStorageSync('globalCustomerServiceHidden');
      this.globalData.isCustomerServiceHidden = isCustomerServiceHidden || false;
    } catch (e) {
      console.error('获取客服按钮状态失败', e);
      this.globalData.isCustomerServiceHidden = false;
    }
    
    // 重写wx.getSystemInfoSync方法，使用新API替代
    const originalGetSystemInfoSync = wx.getSystemInfoSync;
    wx.getSystemInfoSync = function() {
      // console.warn('wx.getSystemInfoSync已废弃，请使用新API。此处返回兼容数据。');
      // 使用新API获取信息
      try {
        const windowInfo = wx.getWindowInfo();
        const deviceInfo = wx.getDeviceInfo();
        const appBaseInfo = wx.getAppBaseInfo();
        
        // 合并信息以保持与旧API返回格式兼容
        return Object.assign({}, 
          windowInfo,
          deviceInfo,
          appBaseInfo,
          {
            windowWidth: windowInfo.windowWidth,
            windowHeight: windowInfo.windowHeight,
            statusBarHeight: windowInfo.statusBarHeight,
            screenWidth: windowInfo.screenWidth,
            screenHeight: windowInfo.screenHeight,
            brand: deviceInfo.brand,
            model: deviceInfo.model,
            system: deviceInfo.system,
            platform: deviceInfo.platform
          }
        );
      } catch (error) {
        console.error('重写的getSystemInfoSync出错:', error);
        // 如果新API出错，回退到原始方法
        return originalGetSystemInfoSync.call(wx);
      }
    };
    
    // 启动时自动获取用户信息和登录状态
    this.autoLogin();
    
    // 检查员工登录状态
    this.checkStaffLogin();

    // 优化页面切换体验，设置默认页面切换动画
    this.setupPageTransition();

    // 初始化指向广告计时器
    this.initTargetAdTimer();
  },

  // 自动静默登录获取openid
  autoSilentLogin() {
    console.log('开始自动静默登录...');
    
    // 检查本地是否已有openid
    const existingOpenid = wx.getStorageSync('openid');
    if (existingOpenid) {
      console.log('本地已有openid:', existingOpenid);
      this.globalData.openid = existingOpenid;
      return;
    }
    
    // 静默获取openid
    wx.cloud.callFunction({
      name: 'userManager',
      data: {
        action: 'getOpenid'
      }
    }).then(res => {
      console.log('静默登录结果:', res);
      if (res.result && res.result.openid) {
        const openid = res.result.openid;
        console.log('静默登录成功，获得openid:', openid);
        
        // 保存到全局数据和本地存储
        this.globalData.openid = openid;
        wx.setStorageSync('openid', openid);
        
        // 触发openid准备完成事件
        if (this.openidReadyCallback) {
          this.openidReadyCallback(openid);
        }
      } else {
        console.error('静默登录失败，未获得openid');
      }
    }).catch(err => {
      console.error('静默登录出错:', err);
    });
  },

  // 确保openid可用的辅助函数
  ensureOpenid() {
    return new Promise((resolve, reject) => {
      const openid = wx.getStorageSync('openid') || this.globalData.openid;
      
      if (openid) {
        resolve(openid);
        return;
      }
      
      // 如果没有openid，尝试获取
      console.log('openid不存在，尝试获取...');
      wx.cloud.callFunction({
        name: 'userManager',
        data: {
          action: 'getOpenid'
        }
      }).then(res => {
        if (res.result && res.result.openid) {
          const newOpenid = res.result.openid;
          this.globalData.openid = newOpenid;
          wx.setStorageSync('openid', newOpenid);
          resolve(newOpenid);
        } else {
          reject(new Error('无法获取openid'));
        }
      }).catch(err => {
        reject(err);
      });
    });
  },

  // 分享辅助函数 - 用于处理分享获取积分功能
  shareWithPoints: function(options) {
    // 使用ensureOpenid确保openid可用
    return this.ensureOpenid().then(openid => {
      // 获取设备标识
      const deviceIdentifier = deviceUtils.getDeviceIdentifier();
      
      console.log('分享辅助函数调用:', {
        openid: openid,
        deviceIdentifier: deviceIdentifier,
        contentType: options.contentType,
        contentId: options.contentId,
        title: options.title,
        originalPath: options.path
      });
      
      // 验证必要参数
      if (!options.contentType || !options.contentId) {
        console.error('分享失败: 缺少内容类型或ID', options);
        return {
          title: options.title,
          path: options.path,
          imageUrl: options.imageUrl,
          fail: function(res) {
            console.error('分享失败:', res);
          }
        };
      }
      
      // 获取内容类型和ID
      const contentType = options.contentType; // 'service', 'article', 'video'等
      const contentId = options.contentId;
      
      // 构建分享路径
      let path = options.path;
      
      // 检查路径中是否已包含参数
      if (path.indexOf('?') > -1) {
        path = `${path}&sharer=${openid}&contentType=${contentType}&contentId=${contentId}&deviceIdentifier=${encodeURIComponent(deviceIdentifier)}`;
      } else {
        path = `${path}?sharer=${openid}&contentType=${contentType}&contentId=${contentId}&deviceIdentifier=${encodeURIComponent(deviceIdentifier)}`;
      }
      
      console.log('构建的分享路径:', path);
      
      // 返回分享对象
      return {
        title: options.title,
        path: path,
        imageUrl: options.imageUrl,
        success: function(res) {
          console.log('分享成功:', {
            contentId: contentId,
            contentType: contentType,
            openid: openid,
            deviceIdentifier: deviceIdentifier,
            result: res
          });
        },
        fail: function(res) {
          console.error('分享失败:', res);
        }
      };
    }).catch(err => {
      console.error('获取openid失败，使用基础分享:', err);
      // 如果无法获取openid，返回基础分享对象（不带积分功能）
      return {
        title: options.title,
        path: options.path,
        imageUrl: options.imageUrl,
        fail: function(res) {
          console.error('分享失败:', res);
        }
      };
    });
  },

  // 同步版本的分享函数（为了兼容现有代码）
  shareWithPointsSync: function(options) {
    // 获取openid和设备标识
    const openid = wx.getStorageSync('openid') || this.globalData.openid;
    const deviceIdentifier = deviceUtils.getDeviceIdentifier();
    
    if (!options.contentType || !options.contentId) {
      console.error('分享失败: 缺少内容类型或ID', options);
      return {
        title: options.title,
        path: options.path,
        imageUrl: options.imageUrl,
        fail: function(res) {
          console.error('分享失败:', res);
        }
      };
    }
    
    // 如果没有openid，返回基础分享（不带积分功能）
    if (!openid) {
      console.warn('分享时未获取到openid，将使用基础分享功能');
      return {
        title: options.title,
        path: options.path,
        imageUrl: options.imageUrl,
        fail: function(res) {
          console.error('分享失败:', res);
        }
      };
    }
    
    // 获取内容类型和ID
    const contentType = options.contentType; // 'service', 'article', 'video'等
    const contentId = options.contentId;
    
    // 构建分享路径
    let path = options.path;
    
    // 检查路径中是否已包含参数
    if (path.indexOf('?') > -1) {
      path = `${path}&sharer=${openid}&contentType=${contentType}&contentId=${contentId}&deviceIdentifier=${encodeURIComponent(deviceIdentifier)}`;
    } else {
      path = `${path}?sharer=${openid}&contentType=${contentType}&contentId=${contentId}&deviceIdentifier=${encodeURIComponent(deviceIdentifier)}`;
    }
    
    console.log('构建的分享路径:', path);
    
    // 返回分享对象
    return {
      title: options.title,
      path: path,
      imageUrl: options.imageUrl,
      success: function(res) {
        console.log('分享成功:', {
          contentId: contentId,
          contentType: contentType,
          openid: openid,
          deviceIdentifier: deviceIdentifier,
          result: res
        });
      },
      fail: function(res) {
        console.error('分享失败:', res);
      }
    };
  },

  // 设置页面过渡动画配置
  setupPageTransition() {
    // 重写页面跳转方法，添加统一的过渡效果
    const originalRedirectTo = wx.redirectTo;
    wx.redirectTo = function(options) {
      // 标记页面切换状态
      getApp().globalData.isPageSwitching = true;
      
      // 添加完成回调
      const originalComplete = options.complete;
      options.complete = function(res) {
        // 页面切换完成后重置状态
        getApp().globalData.isPageSwitching = false;
        
        // 调用原始完成回调
        if (typeof originalComplete === 'function') {
          originalComplete(res);
        }
      };
      
      // 调用原始方法
      return originalRedirectTo.call(this, options);
    };

    // 同样处理navigateTo方法
    const originalNavigateTo = wx.navigateTo;
    wx.navigateTo = function(options) {
      // 标记页面切换状态
      getApp().globalData.isPageSwitching = true;
      
      // 添加完成回调
      const originalComplete = options.complete;
      options.complete = function(res) {
        // 页面切换完成后重置状态
        getApp().globalData.isPageSwitching = false;
        
        // 调用原始完成回调
        if (typeof originalComplete === 'function') {
          originalComplete(res);
        }
      };
      
      // 调用原始方法
      return originalNavigateTo.call(this, options);
    };
  },
  
  // 检查员工登录状态
  checkStaffLogin() {
    const staffInfo = wx.getStorageSync('staffInfo');
    const isStaff = wx.getStorageSync('isStaff');
    
    if (staffInfo && isStaff) {
      this.globalData.staffInfo = staffInfo;
      this.globalData.isStaff = true;
      console.log('从本地存储恢复员工信息', staffInfo);
    }
  },
  
  // 检查本地存储是否有用户信息，如果有则自动登录
  autoLogin() {
    const userInfo = wx.getStorageSync('userInfo');
    console.log('从本地存储恢复基本用户信息', userInfo);
    
    if (userInfo) {
      // 设置登录状态和基本用户信息（临时，后面会用云端数据替换）
      this.globalData.userInfo = userInfo;
      this.globalData.hasUserInfo = true;
      
      // 检查是否存在 openid
      const openid = wx.getStorageSync('openid');
      if (openid) {
        console.log('从本地存储恢复openid', openid);
        this.globalData.openid = openid;
        
        // 有openid后同步最新的用户信息
        this.syncUserInfo();
      } else {
        // 尝试获取 openid
        this.getOpenidAndSyncUserInfo();
      }
    }
  },
  
  // 获取openid并同步用户信息
  getOpenidAndSyncUserInfo() {
    wx.cloud.callFunction({
      name: 'userManager',
      data: {
        action: 'getOpenid'
      },
      success: res => {
        if (res.result && res.result.openid) {
          console.log('成功获取openid', res.result.openid);
          
          // 保存 openid
          this.globalData.openid = res.result.openid;
          wx.setStorageSync('openid', res.result.openid);
          
          // 同步用户信息
          this.syncUserInfo();
        }
      },
      fail: err => {
        console.error('获取openid失败', err);
      }
    });
  },
  
  // 从云端同步用户信息（包括自定义头像、昵称等）
  syncUserInfo() {
    wx.cloud.callFunction({
      name: 'userManager',
      data: {
        action: 'getUserDetail'
      },
      success: res => {
        console.log('从云端获取最新用户信息', res);
        
        if (res.result && res.result.code === 0 && res.result.data) {
          // 获取到了用户的完整信息
          const fullUserInfo = res.result.data;
          console.log('云端用户完整信息', fullUserInfo);
          
          // 确保用户信息完整
          if (!fullUserInfo.nickName) {
            fullUserInfo.nickName = '昵称';
          }
          
          if (!fullUserInfo.signature) {
            fullUserInfo.signature = '欢迎使用';
          }
          
          // 更新全局数据
          this.globalData.userInfo = fullUserInfo;
          this.globalData.hasUserInfo = true;
          
          // 更新到本地存储
          wx.setStorageSync('userInfo', fullUserInfo);
          
          console.log('用户信息已从云端更新到本地', fullUserInfo);
        }
      },
      fail: err => {
        console.error('同步用户信息失败', err);
      }
    });
  },
  
  // 判断云端用户信息是否比本地信息更新
  isCloudUserInfoNewer(cloudInfo, localInfo) {
    // 如果本地没有信息，云端有，则云端信息更新
    if (!localInfo && cloudInfo) return true;
    
    // 如果本地有云端没有，则本地信息更新（理论上不应该出现这种情况）
    if (localInfo && !cloudInfo) return false;
    
    // 如果都有信息，比较更新时间
    if (cloudInfo.updateTime && localInfo.updateTime) {
      const cloudTime = new Date(cloudInfo.updateTime).getTime();
      const localTime = new Date(localInfo.updateTime).getTime();
      return cloudTime > localTime;
    }
    
    // 比较特定字段是否有变化
    if (cloudInfo.avatarUrl !== localInfo.avatarUrl) return true;
    if (cloudInfo.nickName !== localInfo.nickName) return true;
    if (cloudInfo.signature !== localInfo.signature) return true;
    
    // 默认返回false，表示不需要更新
    return false;
  },
  
  // 静默刷新用户信息
  refreshUserInfo() {
    if (!this.globalData.openid) {
      console.error('无法刷新用户信息: openid不存在');
      return;
    }
    
    console.log('刷新用户信息...');
    wx.cloud.callFunction({
      name: 'userManager',
      data: {
        action: 'getUserDetail'
      },
      success: res => {
        if (res.result && res.result.code === 0 && res.result.data) {
          const userInfo = res.result.data;
          console.log('获取到最新用户信息:', userInfo);
          
          // 更新全局数据
          this.globalData.userInfo = userInfo;
          
          // 更新本地存储
          wx.setStorageSync('userInfo', userInfo);
          console.log('用户信息已刷新');
          
          // 标记需要刷新
          this.globalData.needRefreshUserInfo = true;
          
          // 触发用户信息更新事件
          if (this.userInfoReadyCallback) {
            this.userInfoReadyCallback(userInfo);
          }
        } else {
          console.log('云端未找到用户信息');
        }
      },
      fail: err => {
        console.error('刷新用户信息失败', err);
      }
    });
  },
  
  // 初始化应用数据的方法，用于清除缓存后重新初始化
  initApp() {
    console.log('重新初始化应用数据');
    
    // 重置全局数据（除了用户信息和管理员状态）
    this.globalData = {
      userInfo: this.globalData.userInfo, // 保留用户信息
      isAdmin: this.globalData.isAdmin, // 保留管理员状态
      adminInfo: this.globalData.adminInfo, // 保留管理员信息
      isStaff: this.globalData.isStaff, // 保留员工状态
      staffInfo: this.globalData.staffInfo, // 保留员工信息
      videoList: null,
      currentTabIndex: 0,
      isTabSwitching: false,
      isFromLaunch: false,
      launchTransitionComplete: false,
      galleryLoaded: false,
      galleryArticles: null,
      pendingVideoToOpen: null,
      pendingArticleToOpen: null,
      systemInfo: null,
      globalMuted: true, // 保持全局静音状态
      debugMode: false, // 控制日志输出，设置为true时显示详细日志
      needRefreshUserInfo: false, // 标记是否需要刷新用户信息
      needShowUserEditor: false, // 标记是否需要显示用户信息编辑器
      redirectToLogin: false,
      showAppointments: false, // 控制是否展开预约记录面板
      isModifyingAppointment: false, // 是否正在修改预约
      appointmentToModify: null, // 要修改的预约信息
      eventCenter: null, // 全局事件中心
      isCustomerServiceHidden: false, // 客服按钮是否隐藏
      
      // 服务列表数据
      serviceList: null,
      serviceLoaded: false,
      
      // 轮播图数据
      carouselList: null,
      carouselLoaded: false,
      
      // 广告数据
      adItems: null,
      adLoaded: false
    };
    
    // 重新获取系统信息
    wxUtils.getSystemInfo()
      .then(systemInfo => {
        this.globalData.systemInfo = systemInfo;
      })
      .catch(err => {
        console.error('重新获取系统信息失败:', err);
      });
    
    // 重新初始化云开发环境
    initCloud();
  },
  
  // 登录并获取用户信息
  getUserInfo: function(cb) {
    if (this.globalData.userInfo) {
      typeof cb === 'function' && cb(this.globalData.userInfo);
    } else {
      // 调用登录接口
      wx.login({
        success: () => {
          wx.getSetting({
            success: res => {
              if (res.authSetting['scope.userInfo']) {
                // 已经授权，可以直接调用 getUserInfo 获取头像昵称
                wx.getUserInfo({
                  success: res => {
                    const userInfo = res.userInfo;
                    this.globalData.userInfo = userInfo;
                    
                    // 调用回调函数
                    typeof cb === 'function' && cb(this.globalData.userInfo);
                    
                    // 调用云函数保存用户信息并获取openid
                    wx.cloud.callFunction({
                      name: 'userManager',
                      data: {
                        action: 'saveUserInfo',
                        userInfo: userInfo
                      },
                      success: res => {
                        if (res.result && res.result.code === 0) {
                          // 保存openid到全局变量
                          if (res.result.data && res.result.data.openid) {
                            this.globalData.openid = res.result.data.openid;
                          }
                          
                          // 保存到本地存储
                          wx.setStorageSync('userInfo', userInfo);
                          
                          // 获取完整用户信息
                          this.syncUserInfo();
                        }
                      },
                      fail: err => {
                        console.error('保存用户信息失败', err);
                      }
                    });
                  }
                });
              }
            }
          });
        }
      });
    }
  },

  // 小程序显示时
  onShow() {
    // 恢复指向广告计时器
    this.resumeTargetAdTimer();
  },

  // 小程序隐藏时
  onHide() {
    // 暂停指向广告计时器
    this.pauseTargetAdTimer();
  },

  // 初始化指向广告计时器
  initTargetAdTimer() {
    console.log('初始化指向广告计时器');

    // 重置计时器状态
    this.globalData.targetAdTimer = null;
    this.globalData.targetAdStartTime = null;
    this.globalData.targetAdElapsedTime = 0;
    this.globalData.targetAdShown = false;
    this.globalData.targetAdConfig = null;

    // 获取广告配置并开始计时
    this.loadTargetAdConfig();
  },

  // 加载指向广告配置
  loadTargetAdConfig() {
    wx.cloud.callFunction({
      name: 'targetAdManager',
      data: {
        action: 'getConfig'
      }
    }).then(res => {
      console.log('获取指向广告配置结果:', res);

      if (res.result && res.result.success && res.result.data) {
        this.globalData.targetAdConfig = res.result.data;
        console.log('指向广告配置加载成功:', res.result.data);

        // 开始计时
        this.startTargetAdTimer();
      } else {
        console.log('暂无启用的指向广告配置');
      }
    }).catch(err => {
      console.error('获取指向广告配置失败:', err);
    });
  },

  // 开始指向广告计时
  startTargetAdTimer() {
    const config = this.globalData.targetAdConfig;
    if (!config || this.globalData.targetAdShown) {
      return;
    }

    console.log('开始指向广告计时，触发时间:', config.triggerTime, '秒');

    this.globalData.targetAdStartTime = Date.now();
    this.globalData.targetAdElapsedTime = 0;

    this.globalData.targetAdTimer = setInterval(() => {
      this.globalData.targetAdElapsedTime = Date.now() - this.globalData.targetAdStartTime;

      // 检查是否到达触发时间
      if (this.globalData.targetAdElapsedTime >= config.triggerTime * 1000) {
        this.showTargetAd();
      }
    }, 1000);
  },

  // 暂停指向广告计时器
  pauseTargetAdTimer() {
    if (this.globalData.targetAdTimer) {
      clearInterval(this.globalData.targetAdTimer);
      this.globalData.targetAdTimer = null;

      // 记录已经过的时间
      if (this.globalData.targetAdStartTime) {
        this.globalData.targetAdElapsedTime = Date.now() - this.globalData.targetAdStartTime;
      }

      console.log('指向广告计时器已暂停，已经过时间:', this.globalData.targetAdElapsedTime);
    }
  },

  // 恢复指向广告计时器
  resumeTargetAdTimer() {
    const config = this.globalData.targetAdConfig;
    if (!config || this.globalData.targetAdShown || this.globalData.targetAdTimer) {
      return;
    }

    console.log('恢复指向广告计时器，已经过时间:', this.globalData.targetAdElapsedTime);

    // 更新开始时间，减去已经过的时间
    this.globalData.targetAdStartTime = Date.now() - this.globalData.targetAdElapsedTime;

    this.globalData.targetAdTimer = setInterval(() => {
      this.globalData.targetAdElapsedTime = Date.now() - this.globalData.targetAdStartTime;

      // 检查是否到达触发时间
      if (this.globalData.targetAdElapsedTime >= config.triggerTime * 1000) {
        this.showTargetAd();
      }
    }, 1000);
  },

  // 显示指向广告
  showTargetAd() {
    const config = this.globalData.targetAdConfig;
    if (!config || this.globalData.targetAdShown) {
      return;
    }

    console.log('显示指向广告:', config);

    // 停止计时器
    if (this.globalData.targetAdTimer) {
      clearInterval(this.globalData.targetAdTimer);
      this.globalData.targetAdTimer = null;
    }

    // 标记已显示
    this.globalData.targetAdShown = true;

    // 获取当前页面路径
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];
    const currentRoute = currentPage ? currentPage.route : '';

    console.log('当前页面路径:', currentRoute);

    // 只向当前页面发送广告显示事件
    if (this.globalData.eventCenter) {
      this.globalData.eventCenter.emit('showTargetAd', {
        imageUrl: config.imageUrl,
        jumpUrl: config.jumpUrl,
        config: config,
        targetPage: currentRoute // 指定目标页面
      });
    }
  },

  // 全局关闭指向广告（任何页面关闭广告时调用）
  closeTargetAdGlobally() {
    console.log('全局关闭指向广告');

    // 标记广告已处理，防止在其他页面再次显示
    this.globalData.targetAdShown = true;

    // 通知所有页面关闭广告
    if (this.globalData.eventCenter) {
      this.globalData.eventCenter.emit('closeTargetAdGlobally');
    }
  },

  // 处理指向广告跳转
  handleTargetAdJump(jumpUrl) {
    console.log('处理指向广告跳转:', jumpUrl);

    // 解析URL和参数
    const [basePath, queryString] = jumpUrl.split('?');

    // TabBar页面列表
    const tabBarPages = [
      '/pages/index/index',
      '/pages/gallery/gallery',
      '/pages/appointment/appointment',
      '/pages/my/my'
    ];

    // 解析参数
    const params = {};
    if (queryString) {
      queryString.split('&').forEach(param => {
        const [key, value] = param.split('=');
        if (key && value) {
          params[key] = decodeURIComponent(value);
        }
      });
    }

    // 检查是否为TabBar页面
    if (tabBarPages.includes(basePath)) {
      // TabBar页面跳转
      wx.switchTab({
        url: basePath,
        success: () => {
          console.log('TabBar页面跳转成功:', basePath);

          // 延迟处理参数，确保页面已经切换和数据加载完成
          setTimeout(() => {
            this.handleTabBarParams(basePath, params);
          }, 1500); // 增加延迟时间，确保数据加载完成
        },
        fail: (err) => {
          console.error('TabBar页面跳转失败:', err);
          wx.showToast({
            title: '跳转失败',
            icon: 'none'
          });
        }
      });
    } else if (jumpUrl.startsWith('/pages/')) {
      // 普通页面跳转
      wx.navigateTo({
        url: jumpUrl,
        success: () => {
          console.log('普通页面跳转成功:', jumpUrl);
        },
        fail: (err) => {
          console.error('页面跳转失败:', err);
          wx.redirectTo({
            url: jumpUrl,
            success: () => {
              console.log('页面重定向成功:', jumpUrl);
            },
            fail: (redirectErr) => {
              console.error('页面重定向也失败:', redirectErr);
              wx.showToast({
                title: '跳转失败',
                icon: 'none'
              });
            }
          });
        }
      });
    }
  },

  // 处理TabBar页面的参数传递
  handleTabBarParams(basePath, params) {
    console.log('处理TabBar页面参数:', basePath, params);

    if (this.globalData.eventCenter) {
      if (basePath === '/pages/index/index') {
        // 首页视频参数处理
        if (params.videoPosition && params.videoTitle) {
          // 新的位置+标题方式
          this.globalData.eventCenter.emit('showVideoDetail', {
            position: parseInt(params.videoPosition),
            title: decodeURIComponent(params.videoTitle)
          });
        } else if (params.videoId) {
          // 兼容旧的ID方式
          this.globalData.eventCenter.emit('showVideoDetail', {
            videoId: params.videoId
          });
        }
      } else if (basePath === '/pages/gallery/gallery') {
        // 画廊页面参数处理
        if (params.galleryPosition && params.galleryTitle) {
          // 新的位置+标题方式
          this.globalData.eventCenter.emit('showGalleryDetail', {
            position: parseInt(params.galleryPosition),
            title: decodeURIComponent(params.galleryTitle)
          });
        } else if (params.articleId) {
          // 兼容旧的ID方式
          this.globalData.eventCenter.emit('showGalleryDetail', {
            articleId: params.articleId
          });
        }
      } else if (basePath === '/pages/my/my') {
        // 个人中心充值参数处理
        if (params.rechargePosition && params.rechargeAmount) {
          // 新的位置+金额方式
          this.globalData.eventCenter.emit('showRechargeDetail', {
            position: parseInt(params.rechargePosition),
            amount: params.rechargeAmount
          });
        } else if (params.planId) {
          // 兼容旧的ID方式
          this.globalData.eventCenter.emit('showRechargeDetail', {
            planId: params.planId
          });
        }
      } else if (basePath === '/pages/my/points/points') {
        // 兼容旧的积分页面充值跳转
        if (params.rechargePosition && params.rechargeAmount) {
          // 重定向到我的页面
          wx.switchTab({
            url: '/pages/my/my'
          });
          // 延迟触发充值事件
          setTimeout(() => {
            this.globalData.eventCenter.emit('showRechargeDetail', {
              position: parseInt(params.rechargePosition),
              amount: params.rechargeAmount
            });
          }, 500);
        } else if (params.planId) {
          // 重定向到我的页面
          wx.switchTab({
            url: '/pages/my/my'
          });
          // 延迟触发充值事件
          setTimeout(() => {
            this.globalData.eventCenter.emit('showRechargeDetail', {
              planId: params.planId
            });
          }, 500);
        }
      }
    }
  },

  globalData: {
    userInfo: null,
    hasUserInfo: false,
    openid: null,
    systemInfo: null,
    isAdmin: false, // 是否是管理员
    isStaff: false, // 是否是员工
    staffInfo: null, // 员工信息
    isPageSwitching: false, // 页面切换状态
    redirectToLogin: false, // 是否需要重定向到登录页
    needRefreshUserInfo: false, // 是否需要刷新用户信息
    needShowUserEditor: false, // 是否需要显示用户编辑器
    showAppointments: false, // 是否显示预约记录
    isModifyingAppointment: false, // 是否正在修改预约
    appointmentToModify: null, // 要修改的预约信息
    eventCenter: null, // 全局事件中心
    isCustomerServiceHidden: false, // 客服按钮是否隐藏
    isTabSwitching: false,
    currentTabIndex: 0,
    debugMode: false,
    
    // 启动页和过渡相关
    isFromLaunch: false,
    launchTransitionComplete: false,
    
    // 画廊数据相关
    galleryLoaded: false,
    galleryArticles: {
      allArticles: [],         // 所有文章
      categoryArticles: {},    // 按分类存储的文章
      currentArticles: [],     // 当前显示的文章
      currentCategory: 'all'   // 当前分类
    },
    
    // 视频数据相关
    videoList: null,
    
    // 服务列表数据
    serviceList: null,
    serviceLoaded: false,
    
    // 轮播图数据
    carouselList: null,
    carouselLoaded: false,
    
    // 广告数据
    adItems: null,
    adLoaded: false,

    // 指向广告相关
    targetAdTimer: null,        // 计时器对象
    targetAdStartTime: null,    // 开始计时时间
    targetAdElapsedTime: 0,     // 已经过时间
    targetAdShown: false,       // 是否已显示广告
    targetAdConfig: null        // 广告配置
  }
});
