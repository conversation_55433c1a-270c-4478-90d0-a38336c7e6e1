<!--pages/admin/expense/expense.wxml-->
<view class="expense-container">
  <!-- 顶部标题栏 -->
  <view class="header">
    <view class="back-icon" bindtap="goBack">
      <text>←</text>
    </view>
    <view class="title">支出管理</view>
  </view>
  
  <!-- 可滚动的内容区域 -->
  <view class="content-container">
    <!-- 日期筛选区域 -->
    <view class="date-filter">
      <view class="date-shortcuts">
        <view class="shortcut-btn {{startDate === today && endDate === today ? 'active' : ''}}" bindtap="setDateRange" data-range="today">今日</view>
        <view class="shortcut-btn {{startDate === yesterday && endDate === yesterday ? 'active' : ''}}" bindtap="setDateRange" data-range="yesterday">昨日</view>
        <view class="shortcut-btn {{startDate === currentWeekStart && endDate === currentWeekEnd ? 'active' : ''}}" bindtap="setDateRange" data-range="week">本周</view>
        <view class="shortcut-btn {{startDate === currentMonthStart && endDate === currentMonthEnd ? 'active' : ''}}" bindtap="setDateRange" data-range="month">本月</view>
      </view>
      
      <view class="date-range-picker">
        <view class="date-picker-item">
          <text class="date-label">开始日期：</text>
          <picker mode="date" bindchange="onStartDateChange" value="{{startDate}}">
            <view class="date-value">{{startDate || '请选择'}}</view>
          </picker>
        </view>
        
        <view class="date-picker-item">
          <text class="date-label">结束日期：</text>
          <picker mode="date" bindchange="onEndDateChange" value="{{endDate}}">
            <view class="date-value">{{endDate || '请选择'}}</view>
          </picker>
        </view>
        
        <view class="query-btn" bindtap="queryData">查询</view>
      </view>
      
      <!-- 员工筛选 -->
      <view class="staff-filter">
        <view class="filter-label">员工筛选：</view>
        <picker mode="selector" range="{{staffList}}" range-key="name" bindchange="onStaffChange">
          <view class="filter-picker">
            <text>{{selectedStaffName}}</text>
            <text class="picker-arrow">▼</text>
          </view>
        </picker>
        <view class="clear-filter" wx:if="{{selectedStaff}}" bindtap="clearStaffFilter">
          <text>×</text>
        </view>
      </view>
    </view>
    
    <!-- 加载中 -->
    <view class="loading-container" wx:if="{{loading}}">
      <view class="loading-spinner"></view>
      <text class="loading-text">加载中...</text>
    </view>
    
    <!-- 支出统计卡片 -->
    <view class="stats-card" wx:if="{{!loading}}">
      <view class="stats-card-title">支出统计</view>
      <view class="stats-basic">
        <view class="stats-basic-item">
          <text class="stats-basic-value">{{expenseData.totalCount || 0}}</text>
          <text class="stats-basic-label">总记录数</text>
        </view>
        <view class="stats-basic-item">
          <text class="stats-basic-value">¥{{expenseData.totalExpense || 0}}</text>
          <text class="stats-basic-label">总支出</text>
        </view>
      </view>
    </view>
    
    <!-- 员工支出统计 -->
    <view class="stats-card" wx:if="{{expenseData.staffStats && expenseData.staffStats.length > 0 && !loading}}">
      <view class="stats-card-title">员工支出统计</view>
      <view class="staff-stats">
        <view class="staff-stats-header">
          <text class="staff-name-header">员工</text>
          <text class="staff-count-header">记录数</text>
          <text class="staff-amount-header">支出金额</text>
        </view>
        
        <view class="staff-stats-item" wx:for="{{expenseData.staffStats}}" wx:key="staffId">
          <text class="staff-name">{{item.staffName}}</text>
          <text class="staff-count">{{item.count}}</text>
          <text class="staff-amount">¥{{item.totalAmount}}</text>
        </view>
      </view>
    </view>
    
    <!-- 支出记录列表 -->
    <view class="stats-card" wx:if="{{expenseData.expenseList && expenseData.expenseList.length > 0 && !loading}}">
      <view class="stats-card-title">支出记录明细</view>
      <view class="expense-list">
        <view class="expense-item" wx:for="{{expenseData.expenseList}}" wx:key="_id">
          <view class="expense-header">
            <text class="expense-staff">{{item.staffName}}</text>
            <text class="expense-time">{{item.createTime}}</text>
          </view>
          <view class="expense-body">
            <view class="expense-left">
              <view class="expense-amount">¥{{item.amount}}</view>
              <view class="expense-remark" wx:if="{{item.remark}}">{{item.remark}}</view>
            </view>
            <view class="expense-time-tag">购买时间</view>
          </view>
          <view class="expense-meta">
            <view class="expense-images" wx:if="{{item.images && item.images.length > 0}}">
              <image class="expense-image-thumb" src="{{item.images[0]}}" mode="aspectFill" catchtap="previewImage" data-urls="{{item.images}}" data-current="{{item.images[0]}}"></image>
              <text class="image-count" wx:if="{{item.images.length > 1}}">+{{item.images.length - 1}}</text>
            </view>
            <view class="expense-action">
              <view class="view-detail-btn" catchtap="viewExpenseDetail" data-expense="{{item}}">查看详情</view>
              <view class="delete-btn" catchtap="deleteExpense" data-id="{{item._id}}">删除</view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 分页 -->
      <view class="pagination" wx:if="{{expenseData.total > expenseData.pageSize}}">
        <view class="page-btn {{currentPage <= 1 ? 'disabled' : ''}}" bindtap="prevPage">上一页</view>
        <view class="page-info">{{currentPage}}/{{totalPages}}</view>
        <view class="page-btn {{currentPage >= totalPages ? 'disabled' : ''}}" bindtap="nextPage">下一页</view>
      </view>
    </view>
    
    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{!loading && (!expenseData.expenseList || expenseData.expenseList.length === 0)}}">
      <text class="empty-text">暂无支出记录</text>
    </view>
  </view>
</view>

<!-- 支出详情弹窗 -->
<view class="expense-detail-panel {{showExpenseDetail ? 'show' : ''}}" wx:if="{{showExpenseDetail}}">
  <view class="panel-mask" bindtap="closeExpenseDetail"></view>
  <view class="panel-content">
    <view class="panel-header">
      <text class="panel-title">支出详情</text>
      <view class="panel-close" bindtap="closeExpenseDetail">×</view>
    </view>
    
    <view class="panel-body">
      <view class="detail-item">
        <text class="detail-label">员工：</text>
        <text class="detail-value">{{currentExpense.staffName}}</text>
      </view>
      
      <view class="detail-item">
        <text class="detail-label">金额：</text>
        <text class="detail-value">¥{{currentExpense.amount}}</text>
      </view>
      
      <view class="detail-item">
        <text class="detail-label">时间：</text>
        <text class="detail-value">{{currentExpense.createTime}}</text>
      </view>
      
      <view class="detail-item">
        <text class="detail-label">备注：</text>
        <text class="detail-value">{{currentExpense.remark || '无备注'}}</text>
      </view>
      
      <view class="detail-item" wx:if="{{currentExpense.images && currentExpense.images.length > 0}}">
        <text class="detail-label">凭证：</text>
        <view class="detail-images">
          <image 
            class="detail-image" 
            wx:for="{{currentExpense.images}}" 
            wx:key="index" 
            src="{{item}}" 
            mode="aspectFill"
            bindtap="previewImage"
            data-urls="{{currentExpense.images}}"
            data-current="{{item}}"
          ></image>
        </view>
      </view>
      
      <view class="detail-action">
        <view class="delete-btn" bindtap="deleteCurrentExpense">删除记录</view>
      </view>
    </view>
  </view>
</view> 