/* 页面容器 - 使用页面级滚动 */
.page-container {
  height: 100vh;
  background-color: #f5f5f7;
  position: relative;
  overflow: hidden;
}

/* 状态栏安全区域 */
.status-bar {
  width: 100%;
  height: 88rpx;
  background-color: #ffffff;
  flex-shrink: 0;
}

/* 导航栏 */
.nav-header {
  width: 100%;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #ffffff;
  position: relative;
  padding: 0 30rpx;
  box-sizing: border-box;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  flex-shrink: 0;
}

/* 内容区域 - 可滚动 */
.content-area {
  height: calc(100vh - 176rpx); /* 减去状态栏和导航栏的高度 */
  overflow-y: auto;
  -webkit-overflow-scrolling: touch; /* 增强iOS滚动体验 */
}

.back-btn {
  width: 70rpx;
  height: 70rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.arrow-left {
  width: 20rpx;
  height: 20rpx;
  border-top: 4rpx solid #4a4a4a;
  border-left: 4rpx solid #4a4a4a;
  transform: rotate(-45deg);
}

.header-title {
  font-size: 34rpx;
  font-weight: 500;
  color: #4a4a4a;
  flex: 1;
  text-align: center;
}

/* 占位按钮，替代原有的add-btn */
.placeholder-btn {
  width: 60rpx;
  height: 60rpx;
  opacity: 0;
}

/* 浮动添加按钮 */
.floating-add-btn {
  position: fixed;
  bottom: 50rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 300rpx;
  height: 90rpx;
  background-color: #0070c9;
  color: #fff;
  border-radius: 45rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  font-weight: 500;
  box-shadow: 0 4rpx 15rpx rgba(0, 112, 201, 0.3);
  transition: all 0.2s ease;
  z-index: 100;
}

.add-btn-text {
  font-size: 30rpx;
  margin-left: 10rpx;
}

.floating-add-btn:active {
  opacity: 0.9;
  transform: translateX(-50%) scale(0.98);
}

/* 底部安全区域 */
.bottom-safe-area {
  height: 140rpx;
  width: 100%;
}

/* 加载中 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #0070c9;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 20rpx;
  color: #999999;
  font-size: 28rpx;
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
  margin: 30rpx;
  background-color: #ffffff;
  border-radius: 6rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  color: #999999;
  font-size: 28rpx;
  margin-bottom: 30rpx;
}

.empty-btn {
  padding: 10rpx 30rpx;
  background-color: #0070c9;
  color: #fff;
  border-radius: 4rpx;
  font-size: 28rpx;
  box-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.empty-btn:active {
  opacity: 0.9;
  transform: translateY(1rpx);
}

/* 充值方案列表 */
.plans-list {
  padding: 30rpx;
  box-sizing: border-box;
  width: 100%;
}

.plan-item {
  margin-bottom: 30rpx;
}

.plan-card {
  background-color: #ffffff;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.plan-card:active {
  transform: scale(0.98);
}

.plan-card.hidden {
  opacity: 0.7;
  background-color: #f9f9f9;
}

.plan-image {
  width: 100%;
  height: 180rpx; /* 从300rpx减少到180rpx，减少图片高度 */
  background-color: #f0f0f0;
  object-fit: contain; /* 确保图片完整显示，不被裁剪 */
}

.plan-content {
  padding: 20rpx 25rpx; /* 减少内边距，从25rpx 30rpx改为20rpx 25rpx */
}

.plan-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx; /* 从16rpx减少到12rpx */
}

.plan-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

.plan-status {
  font-size: 24rpx;
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  font-weight: 500;
}

.plan-status.visible {
  background: linear-gradient(135deg, #0070c9, #00a1ff);
  color: #ffffff;
  box-shadow: 0 2rpx 8rpx rgba(0, 112, 201, 0.3);
}

.plan-status.hidden {
  background-color: #f2f2f2;
  color: #999999;
}

.plan-desc {
  font-size: 26rpx; /* 从28rpx减少到26rpx，使文字更紧凑 */
  color: #666666;
  margin-bottom: 15rpx; /* 从20rpx减少到15rpx */
  line-height: 1.4; /* 从1.5减少到1.4，使行高更紧凑 */
}

.plan-price-info {
  margin-bottom: 12rpx; /* 从16rpx减少到12rpx */
  background-color: #f9f9f9;
  border-radius: 8rpx;
  padding: 12rpx; /* 从15rpx减少到12rpx */
}

.plan-price, .plan-bonus, .plan-total, .plan-commission {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx; /* 从10rpx减少到8rpx */
}

.plan-commission {
  margin-bottom: 0;
}

.price-label, .bonus-label, .total-label, .commission-label {
  font-size: 26rpx;
  color: #666666;
  width: 150rpx;
}

.price-value {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
}

.bonus-value {
  font-size: 28rpx;
  color: #34c759;
  font-weight: 500;
}

.total-value {
  font-size: 28rpx;
  color: #0070c9;
  font-weight: 500;
}

.commission-value {
  font-size: 28rpx;
  color: #ff3b30;
  font-weight: 500;
}

.plan-actions {
  display: flex;
  border-top: 1rpx solid #eeeeee;
  margin-top: 10rpx;
}

.action-btn {
  flex: 1;
  height: 70rpx; /* 从90rpx减少到70rpx */
  line-height: 70rpx; /* 从90rpx减少到70rpx */
  text-align: center;
  font-size: 26rpx; /* 从28rpx减少到26rpx */
  position: relative;
  transition: all 0.2s ease;
  font-weight: 500;
}

.action-btn:not(:last-child)::after {
  content: "";
  position: absolute;
  right: 0;
  top: 20rpx; /* 从25rpx调整到20rpx，适应新的按钮高度 */
  bottom: 20rpx; /* 从25rpx调整到20rpx，适应新的按钮高度 */
  width: 1rpx;
  background-color: #eeeeee;
}

.action-btn.edit {
  color: #0070c9;
}

.action-btn.visibility {
  color: #666666;
}

.action-btn.delete {
  color: #ff3b30;
}

.action-btn:active {
  background-color: #f9f9f9;
}

/* 弹窗样式 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: -1;
}

.modal-content {
  width: 92%;
  max-width: 650rpx;
  max-height: 90%;
  background-color: #ffffff;
  border-radius: 12rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.modal-header {
  padding: 25rpx 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid #e0e0e0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
}

.modal-close {
  font-size: 40rpx;
  color: #999999;
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: -20rpx;
}

.modal-body {
  flex: 1;
  padding: 25rpx 30rpx;
  max-height: 800rpx;
  box-sizing: border-box;
  width: 100%;
}

.form-group {
  margin-bottom: 24rpx;
  width: 100%;
  box-sizing: border-box;
}

.form-label {
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 12rpx;
  display: block;
}

.form-input, .form-textarea {
  width: 100%;
  border: 1rpx solid #e0e0e0;
  border-radius: 6rpx;
  padding: 16rpx 20rpx;
  font-size: 28rpx;
  color: #333333;
  box-sizing: border-box;
  background-color: #ffffff;
  min-height: 80rpx;
  transition: border-color 0.3s;
}

.form-input:focus, .form-textarea:focus {
  border-color: #0070c9;
}

.form-input::placeholder, .form-textarea::placeholder {
  color: #bbbbbb;
  font-size: 26rpx;
}

.form-textarea {
  height: 120rpx;
  line-height: 1.5;
  padding: 16rpx 20rpx;
}

.image-uploader {
  width: 100%;
  height: 300rpx;
  background-color: #f9f9f9;
  position: relative;
  border: 1rpx dashed #e0e0e0;
  border-radius: 6rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.upload-preview {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.upload-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 20rpx;
}

.upload-text {
  font-size: 28rpx;
  color: #999999;
  text-align: center;
  width: 100%;
}

.upload-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-footer {
  padding: 20rpx;
  display: flex;
  border-top: 1rpx solid #e0e0e0;
}

.modal-btn {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 6rpx;
  font-size: 28rpx;
  transition: all 0.2s ease;
}

.modal-btn.cancel {
  background-color: #f5f5f7;
  color: #666666;
  margin-right: 10rpx;
}

.modal-btn.confirm {
  background-color: #0070c9;
  color: #ffffff;
  margin-left: 10rpx;
  box-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.1);
}

.modal-btn:active {
  opacity: 0.9;
  transform: translateY(1rpx);
} 