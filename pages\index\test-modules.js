/**
 * 模块测试文件
 * 用于测试基础架构组件是否正常工作
 */

// 引入基础组件
const BaseModule = require('./modules/base-module.js');
const ModuleCommunicator = require('./modules/module-communicator.js');
const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, globalErrorHandler } = require('./utils/error-handler.js');
const { RollbackManager, globalRollbackManager } = require('./utils/rollback-manager.js');

/**
 * 测试模块类（用于测试）
 */
class TestModule extends BaseModule {
  constructor(pageContext) {
    super(pageContext);
    this.moduleName = 'TestModule';
  }
  
  async init() {
    this.log('log', '测试模块初始化');
    this.initialized = true;
    return true;
  }
  
  testMethod() {
    this.log('log', '测试方法被调用');
    return 'test result';
  }
  
  testError() {
    throw new Error('这是一个测试错误');
  }
}

/**
 * 运行基础架构测试
 */
function runModuleTests() {
  console.log('=== 开始基础架构和工具函数测试 ===');
  
  // 模拟页面上下文
  const mockPageContext = {
    data: { testData: 'mock data' },
    setData: function(data, callback) {
      console.log('[MockPage] setData called:', data);
      Object.assign(this.data, data);
      if (callback) callback();
    }
  };
  
  const testResults = {
    baseModule: false,
    communicator: false,
    errorHandler: false,
    rollbackManager: false,
    videoUtils: false,
    uiUtils: false,
    constants: false
  };
  
  try {
    // 1. 测试基础模块类
    console.log('\n--- 测试基础模块类 ---');
    const testModule = new TestModule(mockPageContext);
    
    // 测试初始化
    testModule.init().then(() => {
      console.log('✅ 模块初始化成功');
      
      // 测试方法调用
      const result = testModule.testMethod();
      console.log('✅ 模块方法调用成功:', result);
      
      // 测试安全数据设置
      testModule.safeSetData({ newData: 'test' });
      console.log('✅ 安全数据设置成功');
      
      // 测试错误处理
      try {
        testModule.testError();
      } catch (error) {
        console.log('✅ 错误处理测试成功');
      }
      
      testResults.baseModule = true;
    });
    
    // 2. 测试模块通信器
    console.log('\n--- 测试模块通信器 ---');
    const communicator = new ModuleCommunicator();
    
    // 注册模块
    communicator.registerModule('test', testModule);
    console.log('✅ 模块注册成功');
    
    // 测试事件通信
    let eventReceived = false;
    communicator.on('test-event', (data) => {
      console.log('✅ 事件接收成功:', data);
      eventReceived = true;
    });
    
    communicator.emit('test-event', { message: 'test message' });
    
    // 测试模块调用
    try {
      const callResult = communicator.callModule('test-caller', 'test', 'testMethod');
      console.log('✅ 模块调用成功:', callResult);
    } catch (error) {
      console.log('✅ 模块调用错误处理成功');
    }
    
    testResults.communicator = true;
    
    // 3. 测试错误处理器
    console.log('\n--- 测试错误处理器 ---');
    
    // 测试模块错误处理
    globalErrorHandler.handleModuleError('TestModule', new Error('测试模块错误'), 'test');
    console.log('✅ 模块错误处理成功');
    
    // 测试网络错误处理
    globalErrorHandler.handleNetworkError(new Error('网络连接失败'), {
      url: 'https://test.com/api',
      method: 'GET'
    });
    console.log('✅ 网络错误处理成功');
    
    // 获取错误统计
    const errorStats = globalErrorHandler.getErrorStats();
    console.log('✅ 错误统计获取成功:', errorStats.total, '个错误');
    
    testResults.errorHandler = true;
    
    // 4. 测试回滚管理器
    console.log('\n--- 测试回滚管理器 ---');
    
    // 创建回滚点
    globalRollbackManager.createRollbackPoint('测试回滚点', ['pages/index/index.js'])
      .then(rollbackId => {
        console.log('✅ 回滚点创建成功:', rollbackId);
        
        // 获取回滚点信息
        const rollbackPoint = globalRollbackManager.getRollbackPoint(rollbackId);
        console.log('✅ 回滚点信息获取成功:', rollbackPoint.description);
        
        // 验证回滚点
        globalRollbackManager.validateRollbackPoint(rollbackId)
          .then(validation => {
            console.log('✅ 回滚点验证成功:', validation.valid ? '有效' : '无效');
            testResults.rollbackManager = true;
            
            // 继续测试工具函数模块
            testUtilityModules(testResults);
          });
      });
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
    printTestResults(testResults);
  }
}

/**
 * 打印测试结果
 */
function printTestResults(results) {
  console.log('\n=== 测试结果汇总 ===');
  console.log('基础模块类:', results.baseModule ? '✅ 通过' : '❌ 失败');
  console.log('模块通信器:', results.communicator ? '✅ 通过' : '❌ 失败');
  console.log('错误处理器:', results.errorHandler ? '✅ 通过' : '❌ 失败');
  console.log('回滚管理器:', results.rollbackManager ? '✅ 通过' : '❌ 失败');
  
  const passedCount = Object.values(results).filter(Boolean).length;
  const totalCount = Object.keys(results).length;
  
  console.log(`\n总体结果: ${passedCount}/${totalCount} 项测试通过`);
  
  if (passedCount === totalCount) {
    console.log('🎉 所有基础架构组件测试通过！可以安全进入下一阶段。');
  } else {
    console.log('⚠️ 部分测试失败，请检查相关组件。');
  }
}

/**
 * 测试工具函数模块
 */
function testUtilityModules(testResults) {
  console.log('\n=== 开始测试工具函数模块 ===');
  
  try {
    // 5. 测试视频工具模块
    console.log('\n--- 测试视频工具模块 ---');
    const videoUtils = require('./utils/video-utils.js');
    
    // 测试格式化播放量
    const formattedCount = videoUtils.formatCount(12345);
    console.log('✅ 播放量格式化成功:', formattedCount);
    
    // 测试视频数据清理
    const testVideoData = {
      id: 'test123',
      mainTitle: '测试视频',
      playCount: 1000
    };
    const sanitizedData = videoUtils.sanitizeVideoData(testVideoData);
    console.log('✅ 视频数据清理成功:', sanitizedData.id);
    
    // 测试视频列表去重
    const testVideoList = [
      { id: '1', title: '视频1' },
      { id: '2', title: '视频2' },
      { id: '1', title: '重复视频' }
    ];
    const deduplicatedList = videoUtils.deduplicateVideoList(testVideoList);
    console.log('✅ 视频去重成功，去重前:', testVideoList.length, '去重后:', deduplicatedList.length);
    
    testResults.videoUtils = true;
    
    // 6. 测试UI工具模块
    console.log('\n--- 测试UI工具模块 ---');
    const uiUtils = require('./utils/ui-utils.js');
    
    // 测试节流函数
    let throttleCallCount = 0;
    const throttledFn = uiUtils.throttle(() => {
      throttleCallCount++;
    }, 100);
    
    // 快速调用多次
    throttledFn();
    throttledFn();
    throttledFn();
    
    setTimeout(() => {
      console.log('✅ 节流函数测试成功，调用次数:', throttleCallCount);
    }, 50);
    
    // 测试滚动管理器
    const scrollManager = uiUtils.createScrollManager();
    const scrollInfo = scrollManager.updateScrollPosition(100);
    console.log('✅ 滚动管理器测试成功:', scrollInfo.direction);
    
    // 测试触摸处理器
    const touchHandler = uiUtils.createTouchHandler();
    console.log('✅ 触摸处理器创建成功');
    
    testResults.uiUtils = true;
    
    // 7. 测试常量模块
    console.log('\n--- 测试常量模块 ---');
    const constants = require('./constants/index-constants.js');
    
    // 测试分页常量
    console.log('✅ 分页配置加载成功:', constants.PAGINATION.PAGE_SIZE);
    
    // 测试错误类型常量
    console.log('✅ 错误类型加载成功:', constants.ERROR_TYPES.NETWORK_ERROR);
    
    // 测试默认数据常量
    console.log('✅ 默认数据加载成功:', constants.DEFAULT_PAGE_DATA.loading);
    
    testResults.constants = true;
    
    // 输出最终测试结果
    printTestResults(testResults);
    
  } catch (error) {
    console.error('❌ 工具函数测试失败:', error);
    printTestResults(testResults);
  }
}

/**
 * 获取模块状态信息
 */
function getModuleStatus() {
  console.log('\n=== 模块状态信息 ===');
  
  // 通信器状态
  const communicator = new ModuleCommunicator();
  console.log('通信器状态:', communicator.getStats());
  
  // 错误处理器状态
  console.log('错误处理器状态:', globalErrorHandler.getErrorStats());
  
  // 回滚管理器状态
  console.log('回滚管理器状态:', globalRollbackManager.getStatus());
  
  // 工具函数模块状态
  try {
    const videoUtils = require('./utils/video-utils.js');
    const uiUtils = require('./utils/ui-utils.js');
    const constants = require('./constants/index-constants.js');
    
    console.log('视频工具模块:', '✅ 已加载');
    console.log('UI工具模块:', '✅ 已加载');
    console.log('常量模块:', '✅ 已加载');
  } catch (error) {
    console.error('工具函数模块状态检查失败:', error);
  }
}

// 导出测试函数
module.exports = {
  runModuleTests,
  getModuleStatus,
  TestModule
};