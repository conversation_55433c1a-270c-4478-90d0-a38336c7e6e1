/**
 * 视频列表模块性能测试
 * 测试大数据量下的性能表现和内存使用
 */

const VideoListModule = require('../modules/video-list');
const { createMockPageContext, createMockVideoList, createMockVideoUtils } = require('./test-utils');

describe('VideoListModule 性能测试', () => {
  let videoListModule;
  let mockPageContext;

  beforeEach(() => {
    mockPageContext = createMockPageContext();
    videoListModule = new VideoListModule(mockPageContext);
    videoListModule.init();
  });

  afterEach(() => {
    if (videoListModule) {
      videoListModule.destroy();
    }
  });

  describe('大数据量处理性能', () => {
    test('应该能够处理大量视频数据', async () => {
      const largeVideoList = createMockVideoList(1000); // 1000个视频
      
      const startTime = performance.now();
      
      // 模拟批量处理
      videoListModule.videoUtils = createMockVideoUtils({
        getVideoUrl: jest.fn().mockResolvedValue('http://test.com/video.mp4')
      });
      videoListModule.getVideoUrl = videoListModule.videoUtils.getVideoUrl;
      
      const result = await videoListModule.batchProcessVideos(largeVideoList);
      
      const endTime = performance.now();
      const processingTime = endTime - startTime;
      
      expect(result).toHaveLength(1000);
      expect(processingTime).toBeLessThan(5000); // 应该在5秒内完成
      
      console.log(`处理1000个视频耗时: ${processingTime.toFixed(2)}ms`);
    });

    test('应该能够高效去重大量重复数据', () => {
      // 创建包含重复数据的大列表
      const baseVideos = createMockVideoList(100);
      const duplicatedList = [];
      
      // 每个视频重复10次
      for (let i = 0; i < 10; i++) {
        duplicatedList.push(...baseVideos);
      }
      
      const startTime = performance.now();
      
      const result = videoListModule.deduplicateVideoList(duplicatedList);
      
      const endTime = performance.now();
      const processingTime = endTime - startTime;
      
      expect(result).toHaveLength(100);
      expect(processingTime).toBeLessThan(100); // 应该在100ms内完成
      
      console.log(`去重1000个视频（100个唯一）耗时: ${processingTime.toFixed(2)}ms`);
    });

    test('应该能够高效管理大量URL缓存', () => {
      const cacheSize = 1000;
      const videoList = createMockVideoList(cacheSize);
      const urls = videoList.map((_, index) => `http://test.com/video_${index}.mp4`);
      
      const startTime = performance.now();
      
      videoListModule.updateUrlCache(videoList, urls);
      
      const endTime = performance.now();
      const processingTime = endTime - startTime;
      
      expect(Object.keys(mockPageContext.data.urlCache)).toHaveLength(cacheSize);
      expect(processingTime).toBeLessThan(500); // 应该在500ms内完成
      
      console.log(`缓存${cacheSize}个URL耗时: ${processingTime.toFixed(2)}ms`);
    });
  });

  describe('并发处理性能', () => {
    test('应该能够高效处理并发URL请求', async () => {
      const videoList = createMockVideoList(50);
      let requestCount = 0;
      
      videoListModule.videoUtils = createMockVideoUtils({
        getVideoUrl: jest.fn().mockImplementation(() => {
          requestCount++;
          return Promise.resolve(`http://test.com/video_${requestCount}.mp4`);
        })
      });
      videoListModule.getVideoUrl = videoListModule.videoUtils.getVideoUrl;
      
      const startTime = performance.now();
      
      const urls = await videoListModule.batchFetchVideoUrls(videoList);
      
      const endTime = performance.now();
      const processingTime = endTime - startTime;
      
      expect(urls).toHaveLength(50);
      expect(requestCount).toBe(50);
      expect(processingTime).toBeLessThan(2000); // 应该在2秒内完成
      
      console.log(`并发获取50个URL耗时: ${processingTime.toFixed(2)}ms`);
    });

    test('应该正确限制并发请求数量', async () => {
      const videoList = createMockVideoList(20);
      const concurrentRequests = [];
      let maxConcurrent = 0;
      let currentConcurrent = 0;
      
      videoListModule.videoUtils = createMockVideoUtils({
        getVideoUrl: jest.fn().mockImplementation(() => {
          currentConcurrent++;
          maxConcurrent = Math.max(maxConcurrent, currentConcurrent);
          
          return new Promise(resolve => {
            setTimeout(() => {
              currentConcurrent--;
              resolve('http://test.com/video.mp4');
            }, 10);
          });
        })
      });
      videoListModule.getVideoUrl = videoListModule.videoUtils.getVideoUrl;
      
      await videoListModule.batchFetchVideoUrls(videoList);
      
      // 验证并发数量不超过限制（默认为5）
      expect(maxConcurrent).toBeLessThanOrEqual(5);
      
      console.log(`最大并发请求数: ${maxConcurrent}`);
    });
  });

  describe('内存使用优化', () => {
    test('应该能够清理过期缓存释放内存', () => {
      // 创建大量过期缓存
      const expiredCache = {};
      const validCache = {};
      const expiredTime = Date.now() - 2 * 60 * 60 * 1000; // 2小时前
      const validTime = Date.now() - 30 * 60 * 1000; // 30分钟前
      
      for (let i = 0; i < 500; i++) {
        expiredCache[`expired_${i}`] = {
          url: `http://test.com/expired_${i}.mp4`,
          timestamp: expiredTime
        };
      }
      
      for (let i = 0; i < 100; i++) {
        validCache[`valid_${i}`] = {
          url: `http://test.com/valid_${i}.mp4`,
          timestamp: validTime
        };
      }
      
      mockPageContext.data.urlCache = { ...expiredCache, ...validCache };
      
      const initialCacheSize = Object.keys(mockPageContext.data.urlCache).length;
      expect(initialCacheSize).toBe(600);
      
      videoListModule.cleanExpiredUrlCache();
      
      const finalCacheSize = Object.keys(mockPageContext.data.urlCache).length;
      expect(finalCacheSize).toBe(100); // 只保留有效缓存
      
      console.log(`缓存清理: ${initialCacheSize} -> ${finalCacheSize}`);
    });

    test('应该避免内存泄漏', () => {
      // 模拟多次加载和销毁
      const iterations = 100;
      
      for (let i = 0; i < iterations; i++) {
        const tempModule = new VideoListModule(createMockPageContext());
        tempModule.init();
        
        // 模拟一些操作
        tempModule.updateVideoList(createMockVideoList(10));
        tempModule.updateUrlCache(
          createMockVideoList(5), 
          ['url1', 'url2', 'url3', 'url4', 'url5']
        );
        
        // 销毁模块
        tempModule.destroy();
      }
      
      // 如果有内存泄漏，这里应该会有大量的对象残留
      // 在实际环境中可以使用内存分析工具检测
      expect(true).toBe(true); // 占位断言
      
      console.log(`完成${iterations}次模块创建和销毁循环`);
    });
  });

  describe('缓存命中率测试', () => {
    test('应该有较高的缓存命中率', async () => {
      const videoList = createMockVideoList(20);
      let cacheHits = 0;
      let cacheMisses = 0;
      
      // 第一次加载，全部缓存未命中
      videoListModule.videoUtils = createMockVideoUtils({
        getVideoUrl: jest.fn().mockResolvedValue('http://test.com/video.mp4')
      });
      videoListModule.getVideoUrl = videoListModule.videoUtils.getVideoUrl;
      
      await videoListModule.batchProcessVideos(videoList);
      cacheMisses += 20; // 第一次全部未命中
      
      // 第二次加载相同数据，应该大部分命中缓存
      const result2 = await videoListModule.batchProcessVideos(videoList);
      cacheHits += 20; // 第二次全部命中
      
      const hitRate = cacheHits / (cacheHits + cacheMisses) * 100;
      
      expect(result2).toHaveLength(20);
      expect(hitRate).toBeGreaterThan(50); // 命中率应该大于50%
      
      console.log(`缓存命中率: ${hitRate.toFixed(2)}%`);
    });
  });

  describe('边界条件性能', () => {
    test('应该能够处理空数据', async () => {
      const startTime = performance.now();
      
      const result = await videoListModule.batchProcessVideos([]);
      
      const endTime = performance.now();
      const processingTime = endTime - startTime;
      
      expect(result).toEqual([]);
      expect(processingTime).toBeLessThan(10); // 应该几乎立即完成
      
      console.log(`处理空数据耗时: ${processingTime.toFixed(2)}ms`);
    });

    test('应该能够处理单个视频', async () => {
      const singleVideo = createMockVideoList(1);
      
      videoListModule.videoUtils = createMockVideoUtils();
      videoListModule.getVideoUrl = videoListModule.videoUtils.getVideoUrl;
      
      const startTime = performance.now();
      
      const result = await videoListModule.batchProcessVideos(singleVideo);
      
      const endTime = performance.now();
      const processingTime = endTime - startTime;
      
      expect(result).toHaveLength(1);
      expect(processingTime).toBeLessThan(100); // 应该在100ms内完成
      
      console.log(`处理单个视频耗时: ${processingTime.toFixed(2)}ms`);
    });
  });
});