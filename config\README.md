# 配置文件说明

// ================== 配置文件结构说明 (请勿修改) ==================
// @feature: 配置文件结构定义
// @version: 1.0.0
// @warning: 以下配置结构已确定，请勿修改

## 配置文件结构
```javascript
{
  // 云开发环境ID
  ENV_ID: 'your-env-id',
  
  // 小程序ID
  APP_ID: 'your-app-id',
  
  // 启动页配置
  LAUNCH_CONFIG: {
    images: {
      first: 'cloud://path/to/first/image',
      second: 'cloud://path/to/second/image'
    },
    autoJumpDelay: 3500
  },
  
  // 网络请求配置
  REQUEST_CONFIG: {
    timeout: 10000,
    header: {
      'content-type': 'application/json'
    }
  }
}
```

// ================== 配置项说明 (请勿修改) ==================
// @feature: 配置项详细说明
// @version: 1.0.0
// @warning: 以下配置说明已确定，请勿修改

## 配置项说明

### ENV_ID
- 类型：String
- 说明：云开发环境ID，用于初始化云开发环境
- 示例：'nail-spray-cloud-1f7-2bpf0776c5e'

### APP_ID
- 类型：String
- 说明：小程序ID，用于标识小程序
- 示例：'wx5ddbeeb1977b655d'

### LAUNCH_CONFIG
启动页相关配置

#### images
- first：第一张启动页图片的云存储路径
- second：第二张启动页图片的云存储路径

#### autoJumpDelay
- 类型：Number
- 说明：启动页自动跳转延时（毫秒）
- 默认值：3500

### REQUEST_CONFIG
网络请求相关配置

#### timeout
- 类型：Number
- 说明：请求超时时间（毫秒）
- 默认值：10000

#### header
- 类型：Object
- 说明：请求头配置
- 默认值：`{ 'content-type': 'application/json' }`

// ================== 使用说明 (请勿修改) ==================
// @feature: 配置使用方法说明
// @version: 1.0.0
// @warning: 以下使用说明已确定，请勿修改

## 使用方法

1. 在需要使用配置的文件中导入：
```javascript
const { ENV_ID, LAUNCH_CONFIG, REQUEST_CONFIG } = require('../../config/index');
```

2. 使用示例：
```javascript
// 使用环境ID
wx.cloud.init({ env: ENV_ID });

// 使用启动页配置
const { first, second } = LAUNCH_CONFIG.images;

// 使用请求配置
const { timeout, header } = REQUEST_CONFIG;
```

// ================== 在此线以下添加新配置说明 ================== 

## 云存储配置说明

### CLOUD_STORAGE_CONFIG
腾讯云对象存储(COS)相关配置

#### baseUrl
- 类型：String
- 说明：COS存储桶访问域名，用于构建资源URL
- 格式：`https://存储桶名称.cos.地域.myqcloud.com/`
- 示例：`https://nail-1349187357.cos.ap-guangzhou.myqcloud.com/`
- 获取方法：登录腾讯云控制台 > 对象存储 > 存储桶列表 > 选择存储桶 > 概览 > 访问域名

#### dirs
- 类型：Object
- 说明：存储桶中的文件夹路径配置
- 属性：
  - launch: 启动页图片文件夹名称
  - video: 视频文件文件夹名称
  - cover: 视频封面图文件夹名称
  - article: 视频详情HTML文件夹名称

#### tempUrlExpire
- 类型：Number
- 说明：临时链接有效期（秒）
- 默认值：3600（1小时）

## 使用示例

```javascript
// 导入云存储配置
const { CLOUD_STORAGE_CONFIG } = require('../../config/index');

// 获取COS基础URL
const cosBaseUrl = CLOUD_STORAGE_CONFIG.baseUrl;

// 构建资源完整URL
const videoFolder = CLOUD_STORAGE_CONFIG.dirs.video;
const videoUrl = `${cosBaseUrl}${videoFolder}/001_视频名称_副标题_1000_video.mp4`;
``` 