# 主页面模块化架构

## 目录结构

```
pages/index/
├── index.js                 # 主控制器
├── index.wxml              # 页面模板
├── index.wxss              # 页面样式
├── index.json              # 页面配置
├── modules/                # 功能模块目录
│   ├── base-module.js      # 基础模块类
│   ├── module-communicator.js # 模块通信管理器
│   ├── video-list.js       # 视频列表管理模块
│   ├── video-player.js     # 视频播放控制模块
│   ├── search.js           # 搜索功能模块
│   ├── share.js            # 分享功能模块
│   ├── navigation.js       # 导航和UI控制模块
│   └── data-manager.js     # 数据状态管理模块
├── utils/                  # 工具函数目录
│   ├── video-utils.js      # 视频相关工具函数
│   ├── ui-utils.js         # UI相关工具函数
│   ├── format-utils.js     # 格式化工具函数
│   ├── error-handler.js    # 错误处理工具
│   └── rollback-manager.js # 回滚管理工具
├── constants/              # 常量定义目录
│   └── index-constants.js  # 页面常量定义
└── README.md               # 架构说明文档
```

## 模块规范

### 命名规范
- 模块文件使用 kebab-case 命名：`video-list.js`
- 类名使用 PascalCase：`VideoListModule`
- 方法名使用 camelCase：`loadVideoList`
- 常量使用 UPPER_SNAKE_CASE：`DEFAULT_PAGE_SIZE`

### 模块结构
每个模块都应该继承自 `BaseModule` 并实现以下结构：

```javascript
class ModuleName extends BaseModule {
  constructor(pageContext) {
    super(pageContext);
  }
  
  // 初始化方法（必须实现）
  init() {
    // 模块初始化逻辑
  }
  
  // 销毁方法（可选）
  destroy() {
    // 清理资源
    super.destroy();
  }
  
  // 其他业务方法...
}

module.exports = ModuleName;
```

### 通信规范
- 模块间通信通过 `ModuleCommunicator` 进行
- 使用事件发布订阅模式
- 避免模块间直接依赖

### 错误处理规范
- 所有模块都应该有完善的错误处理
- 使用统一的错误处理器
- 错误不应该影响其他模块的正常运行

## 重构进度

- [x] 基础架构搭建
- [ ] 工具函数模块拆分
- [ ] 数据管理模块拆分
- [ ] 核心功能模块拆分
- [ ] 测试和优化

## 注意事项

1. **向后兼容**：确保所有现有API调用方式不变
2. **渐进式重构**：每个阶段都可以独立验证
3. **完整测试**：每个模块都需要有对应的测试
4. **文档更新**：及时更新相关文档