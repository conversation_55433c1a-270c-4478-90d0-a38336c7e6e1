/* 页面容器 */
.carousel-container {
  min-height: 100vh;
  background-color: #f5f5f7; /* 更新为规范的浅色背景 */
  display: flex;
  flex-direction: column;
  position: relative;
}

/* 导航栏 */
.nav-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  padding-top: 90rpx; /* 增加顶部内边距，确保在胶囊按钮下方 */
  background-color: #ffffff; /* 更新为纯白色背景 */
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05); /* 添加轻微阴影 */
}

.back-icon {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  color: #4a4a4a; /* 更新为规范的主色调 */
}

.back-arrow {
  width: 20rpx;
  height: 20rpx;
  border-left: 4rpx solid #4a4a4a; /* 更新为规范的主色调 */
  border-bottom: 4rpx solid #4a4a4a; /* 更新为规范的主色调 */
  transform: rotate(45deg);
}

.page-title {
  font-size: 34rpx; /* 稍微减小字体 */
  font-weight: 600;
  color: #333333; /* 更新为规范的文字主色 */
}

.header-actions {
  display: flex;
  align-items: center;
  width: 60rpx; /* 保持导航栏平衡 */
}

/* 添加按钮 */
.add-button {
  position: fixed;
  left: 50%;
  bottom: 60rpx; /* 与底部保持安全距离 */
  transform: translateX(-50%); /* 水平居中 */
  width: 110rpx;
  height: 110rpx;
  border-radius: 50%;
  background-color: #0070c9; /* 更新为规范的类苹果蓝 */
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 112, 201, 0.4); /* 蓝色阴影 */
  z-index: 99;
  transition: all 0.2s ease;
}

.add-button:active {
  transform: translateX(-50%) scale(0.95);
  opacity: 0.9;
  box-shadow: 0 2rpx 6rpx rgba(0, 112, 201, 0.3);
}

.add-icon {
  color: #ffffff;
  font-size: 60rpx;
  font-weight: bold;
  margin-bottom: 3rpx; /* 轻微下移，视觉上更居中 */
}

/* 安全区域，确保内容不被底部遮挡 */
.safe-bottom-area {
  height: 200rpx; /* 增加高度，为添加按钮留出足够空间 */
  width: 100%;
}

/* 滚动区域 */
.scroll-area {
  position: absolute;
  top: 160rpx; /* 导航栏高度 + 内边距 */
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

/* 轮播图列表 */
.carousel-list {
  padding: 20rpx 30rpx 30rpx;
  box-sizing: border-box;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid rgba(0, 112, 201, 0.1);
  border-top: 4rpx solid #0070c9; /* 更新为规范的类苹果蓝 */
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

.carousel-items {
  display: flex;
  flex-direction: column;
}

.carousel-item {
  margin-bottom: 30rpx;
}

/* 轮播图卡片 */
.carousel-card {
  background-color: #fff;
  border-radius: 6rpx; /* 统一卡片圆角 */
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05); /* 调整阴影效果 */
  position: relative;
  transition: all 0.2s ease;
}

.carousel-card:active {
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}

.carousel-order {
  position: absolute;
  top: 20rpx;
  left: 20rpx;
  width: 40rpx;
  height: 40rpx;
  background-color: rgba(0, 0, 0, 0.6);
  color: #fff;
  font-size: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  z-index: 2;
}

.carousel-image {
  width: 100%;
  height: 360rpx;
  object-fit: cover;
}

.carousel-info {
  padding: 20rpx;
}

.carousel-title {
  font-size: 30rpx; /* 减小字体大小 */
  font-weight: bold;
  color: #333333; /* 更新为规范的文字主色 */
}

.carousel-actions {
  display: flex;
  padding: 0 20rpx 20rpx;
}

.action-btn {
  padding: 8rpx 20rpx;
  font-size: 24rpx;
  border-radius: 4rpx; /* 统一按钮圆角 */
  margin-right: 20rpx;
  box-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.1); /* 统一按钮阴影 */
  transition: all 0.2s ease;
}

.action-btn:active {
  opacity: 0.9;
  transform: translateY(1rpx);
}

.edit-btn {
  background-color: #0070c9; /* 更新为规范的类苹果蓝 */
  color: #fff;
}

.delete-btn {
  background-color: #ff3b30; /* 更新为规范的危险色 */
  color: #fff;
}

.order-actions {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  display: flex;
  flex-direction: column;
  z-index: 2;
}

.order-btn {
  width: 40rpx;
  height: 40rpx;
  background-color: rgba(0, 0, 0, 0.6);
  color: #fff;
  font-size: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10rpx;
  border-radius: 4rpx; /* 统一按钮圆角 */
  transition: all 0.2s ease;
}

.order-btn:active {
  opacity: 0.9;
  transform: scale(0.95);
}

.order-btn.disabled {
  background-color: rgba(0, 0, 0, 0.3);
  color: rgba(255, 255, 255, 0.5);
}

/* 表单弹窗 - 完全重写以解决重叠问题 */
.form-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999; /* 提高层级确保在最上层 */
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  pointer-events: none; /* 默认不接收点击事件 */
  transition: all 0.3s ease;
}

.form-modal.show {
  opacity: 1;
  visibility: visible;
  pointer-events: auto; /* 可以接收点击事件 */
}

.form-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 1; /* 确保蒙层在内容下方 */
}

.form-content {
  position: relative;
  width: 80%;
  max-width: 600rpx;
  background-color: #ffffff;
  border-radius: 12rpx;
  overflow: hidden;
  z-index: 2; /* 确保内容在蒙层上方 */
  transform: translateY(50rpx);
  transition: transform 0.3s ease;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);

  /* 使用flex布局避免内容溢出 */
  display: flex;
  flex-direction: column;
  max-height: 90vh; /* 最大高度限制 */
}

.form-modal.show .form-content {
  transform: translateY(0);
}

/* 表单头部 */
.form-header {
  padding: 20rpx 30rpx;
  background-color: #f8f8f8;
  border-bottom: 1rpx solid #eeeeee;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative; /* 相对定位供子元素定位参考 */
  flex-shrink: 0; /* 防止压缩 */
  width: 100%;
  box-sizing: border-box;
  overflow: visible; /* 确保内容不被裁剪 */
}

/* 表单标题 */
.form-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  position: relative;
  padding-left: 20rpx;
  flex: 1;

  /* 文本溢出处理 */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 70%;
  display: block; /* 确保是块级元素 */
}

/* 标题装饰线 */
.form-title::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 6rpx;
  height: 32rpx;
  background-color: #0070c9;
  border-radius: 3rpx;
}

/* 关闭按钮 */
.form-close {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  color: #999999;
  border-radius: 50%;
  background-color: transparent; /* 确保背景透明 */
  z-index: 3; /* 确保在最上层 */
  position: relative; /* 相对定位 */
  flex-shrink: 0; /* 防止压缩 */
}

.form-close:active {
  background-color: rgba(0, 0, 0, 0.05);
  transform: scale(0.95);
}

/* 表单内容区域 */
.form-body {
  padding: 30rpx;
  overflow-y: auto; /* 内容过多可滚动 */
  flex: 1; /* 自动伸缩 */
  box-sizing: border-box;
  width: 100%;
  -webkit-overflow-scrolling: touch; /* 提升iOS滚动体验 */
}

/* 表单项 */
.form-item {
  margin-bottom: 30rpx;
  width: 100%;
  box-sizing: border-box;
}

/* 表单标签 */
.form-label {
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 12rpx;
  width: 100%;
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  box-sizing: border-box;
}

/* 输入框 */
.form-input {
  width: 100%;
  height: 80rpx;
  border: 1rpx solid #dddddd;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333333;
  background-color: #ffffff;
  box-sizing: border-box;
  transition: all 0.2s ease;
}

.form-input:focus {
  border-color: #0070c9;
}

/* 图片上传区域 - 彻底重构 */
.image-uploader {
  width: 100%;
  height: 240rpx;
  border: 2rpx dashed #0070c9;
  border-radius: 8rpx;
  background-color: rgba(0, 112, 201, 0.05);
  position: relative;
  box-sizing: border-box;
  overflow: hidden; /* 确保内容不溢出 */
}

/* 重要：将加号放在一个独立的视图层 */
.upload-placeholder {
  width: 100%;
  height: 100%;
  display: flex !important;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 999 !important; /* 确保在最上层 */
  background-color: rgba(0, 112, 201, 0.05); /* 轻微背景便于调试 */
}

/* 加号图标 */
.upload-icon {
  font-size: 80rpx;
  line-height: 1;
  color: #ff3b30;
  margin-bottom: 10rpx;
  font-weight: bold;
}

/* 上传文字 */
.upload-text {
  font-size: 28rpx;
  color: #666666;
}

/* 已上传图片 */
.uploaded-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1; /* 确保在加号下方 */
}

/* 明确定义：当有图片时隐藏加号 */
.hide-placeholder {
  display: none !important; /* 使用!important确保规则生效 */
}

/* 当有图片时隐藏文字，只显示加号 */
.hide-text {
  display: none !important;
}

/* 表单底部 */
.form-footer {
  display: flex;
  border-top: 1rpx solid #eeeeee;
  width: 100%;
  flex-shrink: 0; /* 防止压缩 */
  box-sizing: border-box;
}

/* 表单按钮 */
.form-btn {
  flex: 1;
  height: 90rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30rpx;
  transition: all 0.2s ease;
}

.form-btn:active {
  opacity: 0.8;
}

.cancel-btn {
  background-color: #f5f5f7;
  color: #666666;
}

.confirm-btn {
  background-color: #0070c9;
  color: #ffffff;
}
