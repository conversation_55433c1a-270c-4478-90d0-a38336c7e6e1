<!-- 文章列表页面 -->
<!-- 固定的渐变背景层，始终显示 -->
<view class="fixed-background"></view>

<!-- 全屏加载动画，显示在背景层之上 -->
<view class="loading-full-screen" wx:if="{{!showContent && !isFromTab && isFirstLoad && loading}}">
  <view class="loading-spinner"></view>
  <text class="loading-text">加载中...</text>
</view>

<!-- 内容层，加载完成后显示 -->
<view class="container {{showContent ? 'show' : ''}} {{isFromTab ? 'from-tab' : ''}}">
  <!-- 自定义下拉刷新指示器 -->
  <view class="custom-refresh-indicator {{isRefreshing ? 'refreshing' : ''}}" wx:if="{{isRefreshing}}">
    <view class="refresh-spinner"></view>
    <text class="refresh-text">正在刷新...</text>
  </view>
  
  <!-- 顶部加载指示器，在后台加载新内容时显示 -->
  <view class="custom-refresh-indicator {{loading && articles.length > 0 ? 'refreshing' : ''}}" wx:if="{{loading && articles.length > 0}}">
    <view class="refresh-spinner"></view>
    <text class="refresh-text">加载中...</text>
  </view>
  
  <!-- 添加下拉刷新提示，在列表顶部 -->
  <view class="refresh-tip" wx:if="{{isRefreshing}}">
    <view class="refresh-icon"></view>
    <text>正在刷新...</text>
  </view>
  
  <scroll-view 
    class="main-scroll {{showContent ? 'visible' : ''}} {{isFromTab ? 'from-tab' : ''}} {{(isRefreshing || loading) ? 'refreshing' : ''}} fade-transition" 
    scroll-y 
    enable-flex 
    bindscrolltolower="onReachBottom" 
    refresher-enabled="{{true}}"
    refresher-triggered="{{isRefreshing}}"
    refresher-threshold="{{80}}"
    refresher-background="#000000"
    bindrefresherrefresh="onPullDownRefresh"
    bindrefresherpulling="onRefresherPulling"
    enhanced="{{true}}"
    show-scrollbar="{{false}}"
    bounces="{{true}}"
    scroll-anchoring="{{true}}"
    scroll-top="{{scrollTop}}"
  >
    <view class="info-list-container">
      <block wx:for="{{articles}}" wx:key="id" wx:for-index="idx">
        <view 
          class="info-list {{showContent ? 'fade-in' : ''}} {{item.isTouched ? 'touched' : ''}} {{item.isMoving ? (item.touchDirection === 'up' ? 'moving-up' : 'moving-down') : ''}} {{item.isReleased ? 'released' : ''}} {{loading ? 'loading-content-placeholder' : ''}}"
          style="animation-delay: {{idx * 100}}ms;"
          bindtap="onTapArticle"
          data-article="{{item}}"
          data-index="{{idx}}"
          bindtouchstart="handleTouchStart"
          bindtouchmove="handleTouchMove"
          bindtouchend="handleTouchEnd"
          bindtouchcancel="handleTouchEnd"
        >
          <view class="image-container">
            <image 
              class="cover-image" 
              mode="aspectFill" 
              src="{{item.coverImage}}"
              lazy-load
            ></image>
            
            <!-- 玻璃效果容器 - 移动到图片容器内部 -->
            <view class="glass-container">
              <!-- 分享按钮 - 右上角 -->
              <view class="share-button-container" catchtap="stopPropagation">
                <share-button videoInfo="{{item}}" bind:share="onShareArticle"></share-button>
              </view>
              
              <!-- 主标题 -->
              <view class="main-title-first" wx:if="{{item.title}}">
                <text class="font">{{item.title}}</text>
              </view>
              
              <!-- 底部信息栏 - 包含logo、副标题、店铺位置和营业时间在同一水平线上 -->
              <view class="bottom-info-bar">
                <!-- 左侧：logo和副标题 -->
                <view class="left-info">
                  <image class="logo-icon" src="/static/logo.png" mode="aspectFit"></image>
                  <text class="font_2" wx:if="{{item.subtitle}}">{{item.subtitle}}</text>
                </view>
                
                <!-- 中间：店铺位置 -->
                <view class="location-icon" catchtap="navigateToMap">
                  <image class="icon-image" src="/static/定位图标.png" mode="aspectFit"></image>
                  <text class="location-text">店铺位置</text>
                </view>
                
                <!-- 右侧：营业时间 -->
                <view class="business-hours">
                  <image class="business-icon" src="/static/营业图标.png" mode="aspectFit"></image>
                  <text class="business-text">09:30至23:00</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </block>
      
      <!-- 简化空状态，只显示暂无内容文字 -->
      <view class="empty" wx:if="{{!loading && articles.length === 0}}">
        <text class="empty-text">暂无内容</text>
      </view>
      
      <!-- 添加全页面加载状态，只在没有现有内容时显示 -->
      <view class="loading-full" wx:if="{{loading && articles.length === 0}}">
        <view class="loading-spinner"></view>
        <text class="loading-text">加载中...</text>
      </view>
      
      <!-- 仅在触底且showNoMoreTip为true时显示"没有更多了"提示 -->
      <view class="no-more" wx:if="{{!loading && showNoMoreTip && articles.length > 0}}">
        <text class="no-more-text">没有更多了</text>
      </view>
    </view>
  </scroll-view>
</view>

<!-- 详情弹窗 -->
<gallery-detail-modal
  id="galleryDetailModal"
  visible="{{showGalleryDetail}}"
  articleInfo="{{selectedGallery}}"
  bindclose="onGalleryDetailClose"
  bindnavbarControl="onNavbarControl"
/>

<!-- 分类导航栏 -->
<category-nav
  currentCategoryId="{{currentCategory}}"
  bind:categorychange="onCategoryChange"
  categories="{{categoryList}}"
/>



<!-- 悬浮客服按钮 -->
<floating-customer-service
  iconUrl="/images/客服图标.png"
  title="欢迎咨询"
  path="pages/gallery/gallery"
  imageUrl="/images/客服图标.png"
/>

<!-- 指向广告弹窗 -->
<target-ad-modal
  show="{{showTargetAd}}"
  imageUrl="{{targetAdData.imageUrl}}"
  jumpUrl="{{targetAdData.jumpUrl}}"
  bind:close="onTargetAdClose"
/>
