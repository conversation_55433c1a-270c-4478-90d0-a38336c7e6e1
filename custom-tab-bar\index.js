/* global Component, wx */

Component({
  data: {
    selected: 0,
    color: "#AEAEAE",
    selectedColor: "#ffffff",
    visible: true, // 默认显示，但可以通过事件控制
    isVisible: true, // 始终保持可见
    currentPage: '',
    hasVisitedGallery: false,
    list: [{
      pagePath: "/pages/gallery/gallery",
      text: "主页",
      iconPath: "/static/视频图标暗.png",
      selectedIconPath: "/static/视频图标亮.png"
    }, {
      pagePath: "/pages/index/index",
      text: "展示",
      iconPath: "/static/主页图标暗.png",
      selectedIconPath: "/static/主页图标亮.png"
    }, {
      pagePath: "/pages/appointment/appointment",
      text: "服务",
      iconPath: "/static/服务图标暗.png",
      selectedIconPath: "/static/服务图标亮.png"
    }, {
      pagePath: "/pages/my/my",
      text: "我的",
      iconPath: "/static/我的图标暗.png",
      selectedIconPath: "/static/我的图标亮.png"
    }]
  },

  lifetimes: {
    attached() {
      // 组件加载时，同步当前页面状态
      this.updateSelectedFromCurrentPage();
      
      // 移除监听显示隐藏事件，确保导航栏始终显示
      // 底部导航栏应该始终保持可见，作为第一渲染层
    },

    detached() {
      // 在组件实例被从页面节点树移除时执行
      if (wx.offTabBarVisibilityChange) {
        wx.offTabBarVisibilityChange();
      }
    }
  },

  pageLifetimes: {
    show() {
      // 不在这里自动更新状态，让页面的onShow方法负责更新
      // 这样避免了状态冲突和图标回退问题
      // console.log('[TabBar] 页面显示，等待页面onShow更新状态');
    }
  },

  methods: {
    updateSelectedFromCurrentPage() {
      try {
        const pages = getCurrentPages();
        if (!pages || pages.length === 0) return;
        
        const currentPage = pages[pages.length - 1];
        const route = '/' + currentPage.route;
        const index = this.data.list.findIndex(item => item.pagePath === route);
        
        if (index !== -1 && this.data.selected !== index) {
          console.log('[TabBar] 更新选中状态:', route, 'index:', index);
          // 立即更新状态，不使用nextTick避免延迟
          this.setData({ selected: index });
        }
        
        // 如果当前页面是gallery，则标记为已访问
        if (route === '/pages/gallery/gallery') {
          this.setData({ hasVisitedGallery: true });
        }
      } catch (error) {
        console.error('更新 TabBar 状态失败:', error);
      }
    },

    switchTab(e) {
      const { path: url, index } = e.currentTarget.dataset;
      
      // 如果点击已选中的标签页，不执行任何操作
      if (this.data.selected === index) {
        return;
      }
      
      // 触发短振动
      wx.vibrateShort({
        type: 'medium' // 适中的振动强度，在支持的设备上提供更好的反馈
      });
      
      // 获取全局app实例
      const app = getApp();
      
      // 判断是否是切换到gallery(索引为0)
      const isGalleryTab = index === 0;
      
      // 每次切换到gallery页面时都设置isTabSwitching标志
      if (isGalleryTab && app && app.globalData) {
        app.globalData.isTabSwitching = true;
      }
      
      // 立即更新选中状态
      this.setData({ selected: index });
      console.log('[TabBar] 切换到标签:', index, 'URL:', url);
      
      // 直接切换页面，不使用nextTick避免延迟
      wx.switchTab({
        url,
        success: () => {
          console.log('[TabBar] 页面切换成功:', url);
          
          // 标记gallery页面已被访问
          if (isGalleryTab) {
            this.setData({ hasVisitedGallery: true });
          }
          
          // 确保状态正确更新
          setTimeout(() => {
            this.setData({ selected: index });
          }, 100);
        },
        fail: (err) => {
          console.error('Tab切换失败:', err);
          
          // 如果切换到gallery失败，重置切换标记
          if (isGalleryTab && app && app.globalData) {
            app.globalData.isTabSwitching = false;
          }
          
          // 尝试使用reLaunch作为后备方案
          console.log('尝试使用reLaunch重新加载页面');
          wx.reLaunch({
            url: url,
            success: () => {
              console.log('[TabBar] reLaunch成功:', url);
            },
            fail: (secondErr) => {
              console.error('reLaunch也失败:', secondErr);
              wx.showToast({
                title: '页面切换失败，请重试',
                icon: 'none'
              });
              
              // 恢复之前的选中状态
              this.updateSelectedFromCurrentPage();
            }
          });
        }
      });
    },

    show() {
      console.log('[TabBar] 显示导航栏');
      this.setData({
        visible: true
      });
    },

    hide() {
      console.log('[TabBar] 隐藏导航栏');
      this.setData({
        visible: false
      });
    }
  }
}); 