/* global wx */

// 图片资源管理器
class GalleryManager {
  constructor() {
    this.cache = new Map();
    this.loadingQueue = new Map();
    this.pageSize = 20;
    this.currentPage = 0;
    this.hasMore = true;
    this.isLoading = false;
  }

  // 获取图片列表
  async getGalleryList(refresh = false) {
    if (this.isLoading) return null;
    
    try {
      this.isLoading = true;
      
      if (refresh) {
        this.currentPage = 0;
        this.hasMore = true;
        this.cache.clear();
      }

      const result = await wx.cloud.callFunction({
        name: 'getGalleryFiles',
        data: {
          action: 'list',
          data: {
            prefix: 'gallery/', // COS中的文件夹前缀
            page: this.currentPage,
            pageSize: this.pageSize
          }
        }
      });

      if (!result || !result.result || !result.result.data) {
        throw new Error('获取图片列表失败');
      }

      const images = result.result.data.map(item => this.formatImageData(item));
      
      // 更新分页信息
      this.hasMore = images.length === this.pageSize;
      this.currentPage++;

      // 缓存结果
      images.forEach(img => {
        if (!this.cache.has(img.id)) {
          this.cache.set(img.id, img);
        }
      });

      return {
        images,
        hasMore: this.hasMore,
        currentPage: this.currentPage
      };

    } catch (error) {
      console.error('获取图片列表失败:', error);
      return null;
    } finally {
      this.isLoading = false;
    }
  }

  // 格式化图片数据
  formatImageData(item) {
    // 从文件名中提取信息
    // 建议的文件命名格式：category_YYYYMMDD_sequence.jpg
    // 例如：nailart_20240315_001.jpg
    const fileName = item.Key || '';
    const parts = fileName.split('_');
    
    // 安全地获取sequence
    let sequence = '';
    if (parts[2] && parts[2].split) {
      sequence = parts[2].split('.')[0] || '';
    }
    
    return {
      id: item.Key || '',
      url: item.url || '',
      category: parts[0] || 'unknown',
      date: parts[1] || '',
      sequence: sequence,
      title: this.generateTitle(parts[0]),
      imageUrl: item.url || '',
      thumbnailUrl: item.thumbnailUrl || item.url || '',
      width: item.width || 0,
      height: item.height || 0,
      aspectRatio: item.width && item.height ? item.width / item.height : 1,
      createTime: item.createTime || Date.now()
    };
  }

  // 生成图片标题
  generateTitle(category) {
    const categoryMap = {
      'nailart': '美甲作品',
      'training': '培训课程',
      'salon': '店铺环境',
      'default': '图片作品'
    };
    return categoryMap[category] || categoryMap.default;
  }

  // 预加载图片
  preloadImage(url) {
    if (this.loadingQueue.has(url)) {
      return this.loadingQueue.get(url);
    }

    const promise = new Promise((resolve, reject) => {
      const img = wx.createImage();
      img.onload = () => {
        this.loadingQueue.delete(url);
        resolve(url);
      };
      img.onerror = () => {
        this.loadingQueue.delete(url);
        reject(new Error('图片加载失败'));
      };
      img.src = url;
    });

    this.loadingQueue.set(url, promise);
    return promise;
  }

  // 清理缓存
  clearCache() {
    this.cache.clear();
    this.loadingQueue.clear();
    this.currentPage = 0;
    this.hasMore = true;
  }
}

// 导出单例实例
const galleryManager = new GalleryManager();
module.exports = {
  galleryManager: galleryManager
}; 