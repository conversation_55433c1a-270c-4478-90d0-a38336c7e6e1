// 导入事件中心
import EventCenter from '../../utils/eventCenter';

Component({
  properties: {
    // 客服图标URL
    iconUrl: {
      type: String,
      value: ''
    },
    // 客服消息卡片标题
    title: {
      type: String,
      value: '欢迎咨询'
    },
    // 客服消息卡片路径
    path: {
      type: String,
      value: 'pages/index/index'
    },
    // 客服消息卡片图片
    imageUrl: {
      type: String,
      value: ''
    }
  },
  
  data: {
    top: null, // 初始纵坐标
    left: null, // 初始横坐标
    windowWidth: 0,
    windowHeight: 0,
    startX: 0,
    startY: 0,
    isMoving: false, // 是否正在移动
    isHidden: false // 控制客服按钮显示/隐藏
  },
  
  lifetimes: {
    attached() {
      // 获取系统信息
      const systemInfo = wx.getSystemInfoSync();
      
      // 计算rpx到px的转换比例
      const rpxToPxRatio = systemInfo.windowWidth / 750;
      
      this.setData({
        windowWidth: systemInfo.windowWidth,
        windowHeight: systemInfo.windowHeight,
        // 初始位置设置在右下角，向右移动50rpx
        top: systemInfo.windowHeight - 180,
        left: systemInfo.windowWidth - 45 // 向右移动50rpx，比之前的95减少了50
      });
      
      // 从本地存储获取上次保存的位置
      try {
        const savedPosition = wx.getStorageSync('customerServicePosition');
        if (savedPosition) {
          this.setData({
            top: savedPosition.top,
            left: savedPosition.left
          });
        }
      } catch (e) {
        console.error('获取客服按钮位置失败', e);
      }
      
      // 检查客服按钮是否被全局隐藏
      try {
        // 首先检查全局应用状态
        const app = getApp();
        let isHidden = false;
        
        if (app && app.globalData) {
          isHidden = app.globalData.isCustomerServiceHidden;
        }
        
        // 如果全局状态不存在，则检查本地存储
        if (isHidden === undefined || isHidden === null) {
          isHidden = wx.getStorageSync('globalCustomerServiceHidden') || false;
          
          // 同步到全局状态
          if (app && app.globalData) {
            app.globalData.isCustomerServiceHidden = isHidden;
          }
        }
        
        this.setData({
          isHidden: isHidden
        });
      } catch (e) {
        console.error('获取客服按钮显示状态失败', e);
      }
      
      // 监听全局客服按钮隐藏事件
      this.globalHideListener = () => {
        this.setData({
          isHidden: true
        });
      };
      
      // 注册事件监听
      EventCenter.on('hideCustomerService', this.globalHideListener);
    },
    
    detached() {
      // 移除事件监听，防止内存泄漏
      if (this.globalHideListener) {
        EventCenter.off('hideCustomerService', this.globalHideListener);
      }
    }
  },
  
  methods: {
    // 隐藏客服按钮
    hideCustomerService(e) {
      // 阻止事件冒泡，避免触发拖动
      if (e && e.stopPropagation) {
        e.stopPropagation();
      }
      
      this.setData({
        isHidden: true
      });
      
      // 更新全局状态
      const app = getApp();
      if (app && app.globalData) {
        app.globalData.isCustomerServiceHidden = true;
      }
      
      // 保存隐藏状态到本地存储，使用全局变量名称
      try {
        wx.setStorageSync('globalCustomerServiceHidden', true);
      } catch (e) {
        console.error('保存客服按钮显示状态失败', e);
      }
      
      // 触发全局事件，通知所有页面隐藏客服按钮
      EventCenter.emit('hideCustomerService');
    },
    
    // 开始触摸
    touchStart(e) {
      console.log('触摸开始', e);
      this.setData({
        startX: e.touches[0].clientX,
        startY: e.touches[0].clientY,
        isMoving: false
      });
    },
    
    // 触摸移动
    touchMove(e) {
      console.log('触摸移动', e);
      this.setData({
        isMoving: true
      });
      
      const { startX, startY, windowWidth, windowHeight } = this.data;
      let top = this.data.top + (e.touches[0].clientY - startY);
      let left = this.data.left + (e.touches[0].clientX - startX);
      
      // 限制不超出屏幕边界
      if (top < 0) top = 0;
      if (left < 0) left = 0;
      if (top > windowHeight - 150) top = windowHeight - 150;
      if (left > windowWidth - 80) left = windowWidth - 80;
      
      this.setData({
        top,
        left,
        startX: e.touches[0].clientX,
        startY: e.touches[0].clientY
      });
    },
    
    // 触摸结束
    touchEnd(e) {
      console.log('触摸结束', e);
      // 如果没有移动，则视为点击事件，不做处理
      if (!this.data.isMoving) {
        return;
      }
      
      // 保存位置到本地存储
      try {
        wx.setStorageSync('customerServicePosition', {
          top: this.data.top,
          left: this.data.left
        });
      } catch (e) {
        console.error('保存客服按钮位置失败', e);
      }
      
      this.setData({
        isMoving: false
      });
    }
  }
}) 