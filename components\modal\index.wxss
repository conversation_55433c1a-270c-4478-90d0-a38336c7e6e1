.modal-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9999; /* 使用最高z-index */
  display: flex;
  justify-content: center;
  align-items: center;
  visibility: hidden;
  opacity: 0;
  transition: opacity 0.3s, visibility 0.3s;
  margin: 0;
  padding: 0;
}

.modal-visible {
  visibility: visible;
  opacity: 1;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  z-index: -1;
}

.modal-content {
  width: 100vw; /* 使用视口宽度单位 */
  height: 100vh;
  max-width: 100vw; /* 确保不超过视口宽度 */
  overflow: hidden;
  transform: translateY(0);
  transition: transform 0.3s;
  box-shadow: none;
  border-radius: 0;
  display: flex;
  flex-direction: column;
  transform-origin: center;
  margin: 0;
  padding: 0;
  background-color: transparent; /* 修改：设置透明背景，允许子组件控制背景色 */
}

.modal-visible .modal-content {
  transform: translateY(0);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 34rpx;
  border-bottom: 1rpx solid #f0f0f0;
  position: relative;
}

.modal-title {
  font-size: 36rpx;
  font-weight: 500;
  color: #333;
}

.modal-close {
  font-size: 46rpx;
  color: #999;
  line-height: 1;
  padding: 12rpx;
}

.modal-body {
  padding: 0; /* 移除内边距 */
  flex: 1;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch; /* 增加惯性滚动效果 */
  width: 100%; /* 确保宽度100% */
  margin: 0;
  padding-bottom: calc(40rpx + env(safe-area-inset-bottom)); /* 适配底部安全区域 */
} 