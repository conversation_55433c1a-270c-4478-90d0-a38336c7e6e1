const app = getApp()

Page({
  /**
   * 页面的初始数据
   */
  data: {
    staffList: [],
    loading: true,
    showAddModal: false,
    showEditModal: false,
    currentStaff: null,
    formData: {
      name: '',
      phoneNumber: '',
      password: '',
      status: 'active',
      commissionRate: 30, // 默认提成比例为30%
      balanceCommissionRate: 20, // 默认余额支付提成比例为20%
      avatarUrl: '' // 员工头像URL
    },
    statusOptions: [
      { value: 'active', label: '正常' },
      { value: 'inactive', label: '停用' }
    ]
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.fetchStaffList();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 检查管理员登录状态
    if (!app.globalData.isAdmin) {
      wx.navigateTo({
        url: '/pages/admin/login'
      });
      return;
    }
  },

  // 返回上一页
  navigateBack() {
    wx.navigateBack({
      delta: 1
    });
  },

  // 获取员工列表
  fetchStaffList() {
    this.setData({ loading: true });
    
    wx.cloud.callFunction({
      name: 'staffManager',
      data: {
        type: 'admin',
        action: 'getStaffList'
      }
    }).then(res => {
      console.log('获取员工列表成功：', res);
      
      if (res.result && res.result.code === 0) {
        // 对员工列表进行排序：正常状态的排在前面，停用状态的排在后面
        const staffList = res.result.data.list || [];
        staffList.sort((a, b) => {
          // 首先按状态排序，正常（active）排在前面
          if (a.status === 'active' && b.status !== 'active') return -1;
          if (a.status !== 'active' && b.status === 'active') return 1;
          
          // 如果状态相同，按创建时间降序排序（新添加的员工排在前面）
          const timeA = a.createTime ? new Date(a.createTime).getTime() : 0;
          const timeB = b.createTime ? new Date(b.createTime).getTime() : 0;
          return timeB - timeA;
        });
        
        // 标记状态变更位置，用于在视图中添加分隔线
        let prevStatus = null;
        staffList.forEach((staff, index) => {
          if (prevStatus && prevStatus !== staff.status) {
            staff.isStatusChange = true; // 标记状态变更点
          }
          prevStatus = staff.status;
        });
        
        // 计算正常和停用员工的数量
        const activeCount = staffList.filter(item => item.status === 'active').length;
        const inactiveCount = staffList.length - activeCount;
        
        this.setData({
          staffList: staffList,
          activeCount: activeCount,
          inactiveCount: inactiveCount,
          loading: false
        });
      } else {
        this.setData({ loading: false });
        wx.showToast({
          title: '获取员工列表失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      console.error('获取员工列表失败：', err);
      this.setData({ loading: false });
      wx.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      });
    });
  },

  // 显示添加员工弹窗
  showAddStaffModal() {
    this.setData({
      showAddModal: true,
      formData: {
        name: '',
        phoneNumber: '',
        password: '123456', // 默认初始密码
        status: 'active',
        commissionRate: 30, // 默认提成比例为30%
        balanceCommissionRate: 20, // 默认余额支付提成比例为20%
        avatarUrl: '' // 重置头像URL
      }
    });
  },

  // 关闭添加员工弹窗
  closeAddModal() {
    this.setData({
      showAddModal: false
    });
  },

  // 显示编辑员工弹窗
  showEditStaffModal(e) {
    const staffId = e.currentTarget.dataset.id;
    const staff = this.data.staffList.find(item => item._id === staffId);
    
    if (staff) {
      this.setData({
        showEditModal: true,
        currentStaff: staff,
        formData: {
          name: staff.name,
          phoneNumber: staff.phoneNumber,
          password: '', // 编辑时密码为空，表示不修改
          status: staff.status || 'active',
          commissionRate: staff.commissionRate ? Math.round(staff.commissionRate * 100) : 30, // 转换为百分比
          balanceCommissionRate: staff.balanceCommissionRate ? Math.round(staff.balanceCommissionRate * 100) : 20, // 转换为百分比
          avatarUrl: staff.avatar || '' // 设置已有的头像URL
        }
      });
    }
  },

  // 关闭编辑员工弹窗
  closeEditModal() {
    this.setData({
      showEditModal: false,
      currentStaff: null
    });
  },

  // 选择头像
  chooseAvatar(e) {
    const type = e.currentTarget.dataset.type; // 'add' 或 'edit'
    
    wx.chooseMedia({
      count: 1,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      camera: 'back',
      success: (res) => {
        const tempFilePath = res.tempFiles[0].tempFilePath;
        
        // 显示上传中提示
        wx.showLoading({
          title: '上传中...',
          mask: true
        });
        
        // 上传到云存储
        const cloudPath = `staff/avatar_${Date.now()}_${Math.floor(Math.random() * 1000)}.${tempFilePath.match(/\.([^\.]+)$/)[1]}`;
        
        wx.cloud.uploadFile({
          cloudPath: cloudPath,
          filePath: tempFilePath,
          success: (uploadRes) => {
            // 获取上传后的文件ID
            const fileID = uploadRes.fileID;
            
            // 更新表单数据中的头像URL
            this.setData({
              [`formData.avatarUrl`]: fileID
            });
            
            wx.hideLoading();
            wx.showToast({
              title: '头像上传成功',
              icon: 'success'
            });
          },
          fail: (err) => {
            console.error('上传头像失败：', err);
            wx.hideLoading();
            wx.showToast({
              title: '上传失败，请重试',
              icon: 'none'
            });
          }
        });
      }
    });
  },

  // 表单输入处理
  onInput(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;
    
    this.setData({
      [`formData.${field}`]: value
    });
  },

  // 提成比例变更
  onCommissionRateChange(e) {
    const value = e.detail.value;
    
    this.setData({
      'formData.commissionRate': value
    });
  },

  // 余额支付提成比例变更
  onBalanceCommissionRateChange(e) {
    const value = e.detail.value;
    
    this.setData({
      'formData.balanceCommissionRate': value
    });
  },

  // 状态选择处理
  onStatusChange(e) {
    this.setData({
      'formData.status': e.detail.value
    });
  },

  // 添加员工
  addStaff() {
    const { name, phoneNumber, password, status, commissionRate, balanceCommissionRate, avatarUrl } = this.data.formData;
    
    // 表单验证
    if (!name.trim()) {
      wx.showToast({
        title: '请输入员工姓名',
        icon: 'none'
      });
      return;
    }
    
    if (!phoneNumber.trim() || !/^1\d{10}$/.test(phoneNumber)) {
      wx.showToast({
        title: '请输入正确的手机号',
        icon: 'none'
      });
      return;
    }
    
    if (!password.trim()) {
      wx.showToast({
        title: '请设置初始密码',
        icon: 'none'
      });
      return;
    }
    
    wx.showLoading({
      title: '添加中...',
      mask: true
    });
    
    wx.cloud.callFunction({
      name: 'staffManager',
      data: {
        type: 'admin',
        action: 'addStaff',
        data: {
          name,
          phoneNumber,
          password,
          status,
          commissionRate: commissionRate / 100, // 转换为小数
          balanceCommissionRate: balanceCommissionRate / 100, // 转换为小数
          avatar: avatarUrl // 添加头像URL
        }
      }
    }).then(res => {
      wx.hideLoading();
      
      if (res.result && res.result.code === 0) {
        wx.showToast({
          title: '添加成功',
          icon: 'success'
        });
        
        this.setData({
          showAddModal: false
        });
        
        // 刷新员工列表
        this.fetchStaffList();
      } else {
        wx.showToast({
          title: res.result?.message || '添加失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      console.error('添加员工失败：', err);
      wx.hideLoading();
      wx.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      });
    });
  },

  // 更新员工信息
  updateStaff() {
    const { name, phoneNumber, password, status, commissionRate, balanceCommissionRate, avatarUrl } = this.data.formData;
    const staffId = this.data.currentStaff._id;
    
    // 表单验证
    if (!name.trim()) {
      wx.showToast({
        title: '请输入员工姓名',
        icon: 'none'
      });
      return;
    }
    
    if (!phoneNumber.trim() || !/^1\d{10}$/.test(phoneNumber)) {
      wx.showToast({
        title: '请输入正确的手机号',
        icon: 'none'
      });
      return;
    }
    
    wx.showLoading({
      title: '更新中...',
      mask: true
    });
    
    const staffData = {
      name,
      phoneNumber,
      status,
      commissionRate: commissionRate / 100, // 转换为小数
      balanceCommissionRate: balanceCommissionRate / 100 // 转换为小数
    };
    
    // 如果上传了新头像，则更新头像
    if (avatarUrl) {
      staffData.avatar = avatarUrl;
    }
    
    // 如果输入了新密码，则更新密码
    if (password.trim()) {
      staffData.password = password;
    }
    
    wx.cloud.callFunction({
      name: 'staffManager',
      data: {
        type: 'admin',
        action: 'updateStaff',
        data: {
          staffId,
          ...staffData
        }
      }
    }).then(res => {
      wx.hideLoading();
      
      if (res.result && res.result.code === 0) {
        wx.showToast({
          title: '更新成功',
          icon: 'success'
        });
        
        this.setData({
          showEditModal: false,
          currentStaff: null
        });
        
        // 刷新员工列表
        this.fetchStaffList();
      } else {
        wx.showToast({
          title: res.result?.message || '更新失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      console.error('更新员工失败：', err);
      wx.hideLoading();
      wx.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      });
    });
  },

  // 切换员工状态
  toggleStaffStatus(e) {
    const staffId = e.currentTarget.dataset.id;
    const staff = this.data.staffList.find(item => item._id === staffId);
    
    if (!staff) return;
    
    const newStatus = staff.status === 'active' ? 'inactive' : 'active';
    const statusText = newStatus === 'active' ? '启用' : '停用';
    
    wx.showModal({
      title: '确认操作',
      content: `确定要${statusText}该员工账号吗？`,
      success: (res) => {
        if (res.confirm) {
          wx.showLoading({
            title: '处理中...',
            mask: true
          });
          
          wx.cloud.callFunction({
            name: 'staffManager',
            data: {
              type: 'admin',
              action: 'updateStaff',
              data: {
                staffId,
                status: newStatus
              }
            }
          }).then(res => {
            wx.hideLoading();
            
            if (res.result && res.result.code === 0) {
              wx.showToast({
                title: `${statusText}成功`,
                icon: 'success'
              });
              
              // 刷新员工列表
              this.fetchStaffList();
            } else {
              wx.showToast({
                title: res.result?.message || `${statusText}失败`,
                icon: 'none'
              });
            }
          }).catch(err => {
            console.error(`${statusText}员工失败：`, err);
            wx.hideLoading();
            wx.showToast({
              title: '网络错误，请重试',
              icon: 'none'
            });
          });
        }
      }
    });
  }
}) 