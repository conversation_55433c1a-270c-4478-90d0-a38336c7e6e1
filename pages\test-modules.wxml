<!--模块测试页面-->
<view class="container">
  <view class="header">
    <text class="title">基础架构模块测试</text>
    <text class="subtitle">测试第一阶段的基础组件</text>
  </view>

  <view class="status-section">
    <view class="status-card">
      <text class="status-text">{{testStatus}}</text>
    </view>
  </view>

  <view class="button-section">
    <button class="test-button" bindtap="startTest" type="primary">
      开始测试
    </button>
    
    <button class="status-button" bindtap="viewStatus" type="default">
      查看状态
    </button>
    
    <button class="clear-button" bindtap="clearTest" type="warn" size="mini">
      清理数据
    </button>
  </view>

  <view class="results-section" wx:if="{{showResults}}">
    <view class="results-header">
      <text class="results-title">测试结果</text>
    </view>
    
    <view class="result-item" wx:for="{{testResults}}" wx:key="name">
      <view class="result-header">
        <text class="result-name">{{item.name}}</text>
        <view class="result-status {{item.status}}">
          <text class="status-icon">{{item.status === 'pass' ? '✅' : '❌'}}</text>
          <text class="status-text">{{item.status === 'pass' ? '通过' : '失败'}}</text>
        </view>
      </view>
      <text class="result-message">{{item.message}}</text>
    </view>
  </view>

  <view class="info-section">
    <text class="info-title">测试说明</text>
    <text class="info-text">
      本测试验证以下基础架构组件：
      
      1. 基础模块类 - 模块创建和生命周期管理
      2. 模块通信器 - 模块注册和事件通信
      3. 错误处理器 - 错误捕获和处理策略
      4. 回滚管理器 - 备份和回滚功能
      
      所有测试通过后，可以安全进入下一阶段的重构工作。
    </text>
  </view>
</view>