// 分类管理组件
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    visible: {
      type: Boolean,
      value: false
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    categories: [], // 分类列表
    loading: false, // 加载状态
    editingCategory: null, // 正在编辑的分类
    newCategoryName: '', // 新分类名称
    isAdding: false, // 是否在添加分类
    isEditing: false, // 是否在编辑分类
  },

  lifetimes: {
    attached() {
      // 在组件实例进入页面节点树时执行
      if (this.properties.visible) {
        this.loadCategories();
      }
    }
  },

  observers: {
    'visible': function(visible) {
      if (visible) {
        this.loadCategories();
      }
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 加载分类列表
    loadCategories() {
      this.setData({ loading: true });

      wx.cloud.callFunction({
        name: 'galleryManager',
        data: {
          action: 'getCategoryList'
        }
      }).then(res => {
        console.log('获取分类列表成功:', res.result);
        
        if (res.result && res.result.success && res.result.data) {
          this.setData({
            categories: res.result.data,
            loading: false
          });
        } else {
          this.showToast('获取分类列表失败：' + (res.result.message || '未知错误'));
          this.setData({ loading: false });
        }
      }).catch(err => {
        console.error('获取分类列表失败:', err);
        this.showToast('获取分类列表失败，请重试');
        this.setData({ loading: false });
      });
    },

    // 显示添加分类表单
    showAddForm() {
      this.setData({
        isAdding: true,
        newCategoryName: ''
      });
    },

    // 隐藏添加分类表单
    hideAddForm() {
      this.setData({
        isAdding: false,
        newCategoryName: ''
      });
    },

    // 输入新分类名称
    onInputNewCategory(e) {
      this.setData({
        newCategoryName: e.detail.value
      });
    },

    // 添加分类
    addCategory() {
      const { newCategoryName } = this.data;

      if (!newCategoryName.trim()) {
        this.showToast('请输入分类名称');
        return;
      }

      this.setData({ loading: true });

      wx.cloud.callFunction({
        name: 'galleryManager',
        data: {
          action: 'addCategory',
          data: {
            name: newCategoryName.trim()
          }
        }
      }).then(res => {
        console.log('添加分类结果:', res.result);
        
        if (res.result && res.result.success) {
          this.showToast('添加分类成功');
          this.hideAddForm();
          this.loadCategories();
        } else {
          this.showToast('添加分类失败：' + (res.result.message || '未知错误'));
          this.setData({ loading: false });
        }
      }).catch(err => {
        console.error('添加分类失败:', err);
        this.showToast('添加分类失败，请重试');
        this.setData({ loading: false });
      });
    },

    // 显示编辑分类表单
    showEditForm(e) {
      const categoryId = e.currentTarget.dataset.id;
      const category = this.data.categories.find(item => item._id === categoryId);
      
      if (category) {
        this.setData({
          isEditing: true,
          editingCategory: category,
          newCategoryName: category.name
        });
      }
    },

    // 隐藏编辑分类表单
    hideEditForm() {
      this.setData({
        isEditing: false,
        editingCategory: null,
        newCategoryName: ''
      });
    },

    // 更新分类
    updateCategory() {
      const { editingCategory, newCategoryName } = this.data;

      if (!editingCategory) {
        this.hideEditForm();
        return;
      }

      if (!newCategoryName.trim()) {
        this.showToast('请输入分类名称');
        return;
      }

      this.setData({ loading: true });

      wx.cloud.callFunction({
        name: 'galleryManager',
        data: {
          action: 'updateCategory',
          data: {
            _id: editingCategory._id,
            name: newCategoryName.trim()
          }
        }
      }).then(res => {
        console.log('更新分类结果:', res.result);
        
        if (res.result && res.result.success) {
          this.showToast('更新分类成功');
          this.hideEditForm();
          this.loadCategories();
        } else {
          this.showToast('更新分类失败：' + (res.result.message || '未知错误'));
          this.setData({ loading: false });
        }
      }).catch(err => {
        console.error('更新分类失败:', err);
        this.showToast('更新分类失败，请重试');
        this.setData({ loading: false });
      });
    },

    // 删除分类
    deleteCategory(e) {
      const categoryId = e.currentTarget.dataset.id;
      
      wx.showModal({
        title: '确认删除',
        content: '确定要删除该分类吗？如果该分类下有画廊项目，将无法删除。',
        success: res => {
          if (res.confirm) {
            this.setData({ loading: true });

            wx.cloud.callFunction({
              name: 'galleryManager',
              data: {
                action: 'deleteCategory',
                data: {
                  _id: categoryId
                }
              }
            }).then(res => {
              console.log('删除分类结果:', res.result);
              
              if (res.result && res.result.success) {
                this.showToast('删除分类成功');
                this.loadCategories();
              } else {
                // 显示详细的错误信息
                const errorMsg = res.result && res.result.message ? res.result.message : '未知错误';
                this.showToast('删除分类失败：' + errorMsg);
                this.setData({ loading: false });
              }
            }).catch(err => {
              console.error('删除分类失败:', err);
              this.showToast('删除分类失败，请重试');
              this.setData({ loading: false });
            });
          }
        }
      });
    },

    // 关闭分类管理器
    onClose() {
      if (this.data.loading) return;
      
      this.setData({
        visible: false,
        isAdding: false,
        isEditing: false,
        editingCategory: null,
        newCategoryName: ''
      });

      this.triggerEvent('close');
    },

    // 防止冒泡
    onContentTap() {
      return false;
    },

    // 显示提示信息
    showToast(message) {
      wx.showToast({
        title: message,
        icon: 'none',
        duration: 2000
      });
    }
  }
}) 