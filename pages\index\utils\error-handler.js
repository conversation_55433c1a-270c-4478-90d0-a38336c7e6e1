/**
 * 统一错误处理器
 * 负责处理模块错误、网络错误、UI错误等各种类型的错误
 */
class ErrorHandler {
  constructor() {
    // 错误日志
    this.errorLog = [];
    
    // 最大日志条数
    this.maxLogEntries = 200;
    
    // 错误统计
    this.errorStats = {
      total: 0,
      byType: {},
      byModule: {},
      byContext: {}
    };
    
    // 错误处理策略
    this.strategies = new Map();
    
    // 是否启用调试模式
    this.debugMode = false;
    
    // 初始化默认错误处理策略
    this.initDefaultStrategies();
    
    console.log('[ErrorHandler] 错误处理器已初始化');
  }
  
  /**
   * 初始化默认错误处理策略
   */
  initDefaultStrategies() {
    // 网络错误处理策略
    this.strategies.set('NETWORK_ERROR', {
      showToast: true,
      message: '网络连接异常，请重试',
      icon: 'none',
      duration: 2000,
      retry: true,
      maxRetries: 3
    });
    
    // 数据错误处理策略
    this.strategies.set('DATA_ERROR', {
      showToast: false,
      fallbackData: true,
      logLevel: 'warn'
    });
    
    // UI错误处理策略
    this.strategies.set('UI_ERROR', {
      showToast: false,
      resetUI: true,
      logLevel: 'warn'
    });
    
    // 模块错误处理策略
    this.strategies.set('MODULE_ERROR', {
      showToast: false,
      isolate: true,
      logLevel: 'error'
    });
    
    // 通用错误处理策略
    this.strategies.set('GENERIC_ERROR', {
      showToast: true,
      message: '操作失败，请重试',
      icon: 'none',
      duration: 1500,
      logLevel: 'error'
    });
  }
  
  /**
   * 处理模块错误
   * @param {string} moduleName - 模块名称
   * @param {Error} error - 错误对象
   * @param {string} context - 错误上下文
   */
  handleModuleError(moduleName, error, context = 'unknown') {
    const errorInfo = this.createErrorInfo(error, {
      type: 'MODULE_ERROR',
      module: moduleName,
      context: context
    });
    
    this.logError(errorInfo);
    this.updateStats(errorInfo);
    
    // 应用错误处理策略
    this.applyStrategy('MODULE_ERROR', errorInfo);
    
    // 触发错误事件（如果有通信管理器）
    this.emitErrorEvent('module:error', errorInfo);
  }
  
  /**
   * 处理网络错误
   * @param {Error} error - 错误对象
   * @param {Object} options - 选项
   */
  handleNetworkError(error, options = {}) {
    const errorInfo = this.createErrorInfo(error, {
      type: 'NETWORK_ERROR',
      url: options.url,
      method: options.method,
      statusCode: options.statusCode
    });
    
    this.logError(errorInfo);
    this.updateStats(errorInfo);
    
    // 应用错误处理策略
    this.applyStrategy('NETWORK_ERROR', errorInfo, options);
  }
  
  /**
   * 处理数据错误
   * @param {Error} error - 错误对象
   * @param {Object} options - 选项
   */
  handleDataError(error, options = {}) {
    const errorInfo = this.createErrorInfo(error, {
      type: 'DATA_ERROR',
      dataType: options.dataType,
      operation: options.operation
    });
    
    this.logError(errorInfo);
    this.updateStats(errorInfo);
    
    // 应用错误处理策略
    this.applyStrategy('DATA_ERROR', errorInfo, options);
    
    // 尝试使用备用数据
    if (options.fallbackData) {
      return options.fallbackData;
    }
    
    return null;
  }
  
  /**
   * 处理UI错误
   * @param {Error} error - 错误对象
   * @param {Object} options - 选项
   */
  handleUIError(error, options = {}) {
    const errorInfo = this.createErrorInfo(error, {
      type: 'UI_ERROR',
      component: options.component,
      operation: options.operation
    });
    
    this.logError(errorInfo);
    this.updateStats(errorInfo);
    
    // 应用错误处理策略
    this.applyStrategy('UI_ERROR', errorInfo, options);
  }
  
  /**
   * 处理通用错误
   * @param {Error} error - 错误对象
   * @param {Object} options - 选项
   */
  handleGenericError(error, options = {}) {
    const errorInfo = this.createErrorInfo(error, {
      type: 'GENERIC_ERROR',
      ...options
    });
    
    this.logError(errorInfo);
    this.updateStats(errorInfo);
    
    // 应用错误处理策略
    this.applyStrategy('GENERIC_ERROR', errorInfo, options);
  }
  
  /**
   * 创建错误信息对象
   * @param {Error} error - 错误对象
   * @param {Object} additional - 附加信息
   * @returns {Object}
   */
  createErrorInfo(error, additional = {}) {
    return {
      message: error.message || error.toString(),
      stack: error.stack,
      timestamp: new Date().toISOString(),
      id: this.generateErrorId(),
      ...additional
    };
  }
  
  /**
   * 记录错误日志
   * @param {Object} errorInfo - 错误信息
   */
  logError(errorInfo) {
    this.errorLog.push(errorInfo);
    
    // 限制日志条数
    if (this.errorLog.length > this.maxLogEntries) {
      this.errorLog.shift();
    }
    
    // 根据错误类型选择日志级别
    const logLevel = this.getLogLevel(errorInfo.type);
    const logMessage = `[${errorInfo.type}] ${errorInfo.message}`;
    
    if (errorInfo.module) {
      console[logLevel](`[${errorInfo.module}] ${logMessage}`, errorInfo);
    } else {
      console[logLevel](logMessage, errorInfo);
    }
  }
  
  /**
   * 更新错误统计
   * @param {Object} errorInfo - 错误信息
   */
  updateStats(errorInfo) {
    this.errorStats.total++;
    
    // 按类型统计
    if (!this.errorStats.byType[errorInfo.type]) {
      this.errorStats.byType[errorInfo.type] = 0;
    }
    this.errorStats.byType[errorInfo.type]++;
    
    // 按模块统计
    if (errorInfo.module) {
      if (!this.errorStats.byModule[errorInfo.module]) {
        this.errorStats.byModule[errorInfo.module] = 0;
      }
      this.errorStats.byModule[errorInfo.module]++;
    }
    
    // 按上下文统计
    if (errorInfo.context) {
      if (!this.errorStats.byContext[errorInfo.context]) {
        this.errorStats.byContext[errorInfo.context] = 0;
      }
      this.errorStats.byContext[errorInfo.context]++;
    }
  }
  
  /**
   * 应用错误处理策略
   * @param {string} errorType - 错误类型
   * @param {Object} errorInfo - 错误信息
   * @param {Object} options - 选项
   */
  applyStrategy(errorType, errorInfo, options = {}) {
    const strategy = this.strategies.get(errorType) || this.strategies.get('GENERIC_ERROR');
    
    // 显示用户提示
    if (strategy.showToast) {
      const message = options.message || strategy.message || errorInfo.message;
      wx.showToast({
        title: message,
        icon: strategy.icon || 'none',
        duration: strategy.duration || 2000
      });
    }
    
    // 重试机制
    if (strategy.retry && options.retryFn && typeof options.retryFn === 'function') {
      const maxRetries = options.maxRetries || strategy.maxRetries || 1;
      const currentRetries = options.currentRetries || 0;
      
      if (currentRetries < maxRetries) {
        setTimeout(() => {
          options.retryFn({ ...options, currentRetries: currentRetries + 1 });
        }, (currentRetries + 1) * 1000); // 递增延迟
      }
    }
    
    // UI重置
    if (strategy.resetUI && options.resetUIFn && typeof options.resetUIFn === 'function') {
      try {
        options.resetUIFn();
      } catch (resetError) {
        console.error('[ErrorHandler] UI重置失败:', resetError);
      }
    }
    
    // 使用备用数据
    if (strategy.fallbackData && options.fallbackData) {
      return options.fallbackData;
    }
  }
  
  /**
   * 获取日志级别
   * @param {string} errorType - 错误类型
   * @returns {string}
   */
  getLogLevel(errorType) {
    const strategy = this.strategies.get(errorType);
    return strategy?.logLevel || 'error';
  }
  
  /**
   * 生成错误ID
   * @returns {string}
   */
  generateErrorId() {
    return `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
  
  /**
   * 触发错误事件
   * @param {string} event - 事件名称
   * @param {Object} errorInfo - 错误信息
   */
  emitErrorEvent(event, errorInfo) {
    // 如果有全局通信管理器，触发错误事件
    const app = getApp();
    if (app && app.communicator && typeof app.communicator.emit === 'function') {
      app.communicator.emit(event, errorInfo);
    }
  }
  
  /**
   * 设置错误处理策略
   * @param {string} errorType - 错误类型
   * @param {Object} strategy - 处理策略
   */
  setStrategy(errorType, strategy) {
    this.strategies.set(errorType, strategy);
  }
  
  /**
   * 获取错误处理策略
   * @param {string} errorType - 错误类型
   * @returns {Object|null}
   */
  getStrategy(errorType) {
    return this.strategies.get(errorType) || null;
  }
  
  /**
   * 获取错误日志
   * @param {Object} filters - 过滤条件
   * @returns {Array}
   */
  getErrorLog(filters = {}) {
    let logs = [...this.errorLog];
    
    // 按类型过滤
    if (filters.type) {
      logs = logs.filter(log => log.type === filters.type);
    }
    
    // 按模块过滤
    if (filters.module) {
      logs = logs.filter(log => log.module === filters.module);
    }
    
    // 按时间范围过滤
    if (filters.startTime) {
      logs = logs.filter(log => new Date(log.timestamp) >= new Date(filters.startTime));
    }
    
    if (filters.endTime) {
      logs = logs.filter(log => new Date(log.timestamp) <= new Date(filters.endTime));
    }
    
    // 限制返回数量
    if (filters.limit) {
      logs = logs.slice(-filters.limit);
    }
    
    return logs;
  }
  
  /**
   * 获取错误统计信息
   * @returns {Object}
   */
  getErrorStats() {
    return {
      ...this.errorStats,
      recentErrors: this.errorLog.slice(-10),
      logCount: this.errorLog.length
    };
  }
  
  /**
   * 清除错误日志
   * @param {Object} filters - 过滤条件（可选）
   */
  clearErrorLog(filters = {}) {
    if (Object.keys(filters).length === 0) {
      // 清除所有日志
      this.errorLog = [];
      this.errorStats = {
        total: 0,
        byType: {},
        byModule: {},
        byContext: {}
      };
    } else {
      // 根据条件清除部分日志
      const logsToKeep = this.errorLog.filter(log => {
        if (filters.type && log.type === filters.type) return false;
        if (filters.module && log.module === filters.module) return false;
        return true;
      });
      
      this.errorLog = logsToKeep;
      // 重新计算统计信息
      this.recalculateStats();
    }
    
    console.log('[ErrorHandler] 错误日志已清除');
  }
  
  /**
   * 重新计算统计信息
   */
  recalculateStats() {
    this.errorStats = {
      total: 0,
      byType: {},
      byModule: {},
      byContext: {}
    };
    
    this.errorLog.forEach(errorInfo => {
      this.updateStats(errorInfo);
    });
  }
  
  /**
   * 设置调试模式
   * @param {boolean} enabled - 是否启用
   */
  setDebugMode(enabled) {
    this.debugMode = enabled;
    console.log(`[ErrorHandler] 调试模式${enabled ? '已启用' : '已禁用'}`);
  }
  
  /**
   * 导出错误日志
   * @param {string} format - 导出格式 ('json' | 'csv')
   * @returns {string}
   */
  exportErrorLog(format = 'json') {
    if (format === 'json') {
      return JSON.stringify({
        logs: this.errorLog,
        stats: this.errorStats,
        exportTime: new Date().toISOString()
      }, null, 2);
    } else if (format === 'csv') {
      const headers = ['timestamp', 'type', 'module', 'context', 'message'];
      const rows = this.errorLog.map(log => [
        log.timestamp,
        log.type,
        log.module || '',
        log.context || '',
        log.message.replace(/"/g, '""') // 转义CSV中的引号
      ]);
      
      return [
        headers.join(','),
        ...rows.map(row => row.map(cell => `"${cell}"`).join(','))
      ].join('\n');
    }
    
    throw new Error(`不支持的导出格式: ${format}`);
  }
  
  /**
   * 销毁错误处理器
   */
  destroy() {
    this.clearErrorLog();
    this.strategies.clear();
    console.log('[ErrorHandler] 错误处理器已销毁');
  }
}

// 创建全局错误处理器实例
const globalErrorHandler = new ErrorHandler();

// 导出类和全局实例
module.exports = {
  ErrorHandler,
  globalErrorHandler
};