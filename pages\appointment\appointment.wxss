/* 页面基础设置 */
page {
  height: 100%;
  width: 100%;
  background: none; /* 移除页面背景，使用固定背景层 */
}

/* 固定的渐变背景层 */
.fixed-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--bg-gradient); /* 使用与页面相同的渐变背景 */
  z-index: 1; /* 最底层 */
}

/* 整体容器 */
.appointment-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  height: 100%;
  background-color: transparent; /* 改为透明背景，显示下方的固定背景层 */
  color: #ffffff;
  z-index: 2; /* 确保在固定背景层之上 */
  position: relative;
}

/* 页面滚动视图 - 新增 */
.page-scroll-view {
  flex: 1;
  height: 100vh;
  width: 100%;
  box-sizing: border-box;
  z-index: 3;
  -webkit-overflow-scrolling: touch; /* 增加滚动的流畅度 */
  opacity: 0; /* 默认隐藏 */
  transform: translateY(40rpx); /* 增加初始偏移量，让动画更明显 */
  transition: opacity 0.8s ease-out, transform 0.8s ease-out; /* 0.8秒舒缓动画 */
}

/* 页面内容显示状态 */
.page-scroll-view.show-content {
  opacity: 1;
  transform: translateY(0);
}

/* 页面内容区域 - 替代scroll-view */
.page-content {
  flex: 1;
  width: 100%;
  box-sizing: border-box;
  z-index: 3;
  position: relative;
  overflow-y: auto; /* 添加垂直滚动 */
  -webkit-overflow-scrolling: touch; /* 增加滚动的流畅度 */
  min-height: 100vh; /* 确保至少有一个屏幕的高度 */
}

/* 主内容区域 */
.appointment-content {
  flex: 1;
  padding: 20rpx 30rpx;
  padding-top: 150rpx; /* 减小顶部内边距，因为轮播图标题容器高度减小了 */
  padding-bottom: 120rpx; /* 为底部按钮预留空间 */
  z-index: 10; /* 确保在标题容器之上 */
  position: relative; /* 添加相对定位以支持z-index */
  height: auto; /* 自动高度，不限制内容高度 */
  margin-top: -150rpx; /* 上移，与标题容器重叠，但调整为与标题容器高度一致 */
}

/* 步骤指示器 */
.step-indicator {
  margin-bottom: 30rpx;
}

.back-button {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #aeaeae;
}

.icon-back {
  width: 36rpx;
  height: 36rpx;
  margin-right: 10rpx;
}

/* 步骤内容区域 */
.step-content {
  display: flex;
  flex-direction: column;
}

/* 区域标题 */
.section-title {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 30rpx;
  font-weight: 500;
  letter-spacing: 2rpx;
  position: relative;
  padding-left: 16rpx;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  height: 24rpx;
  width: 6rpx;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 3rpx;
}

/* 居中标题样式 */
.section-title-centered {
  justify-content: center;
  padding-left: 0;
  text-align: center;
}

/* 无装饰的标题样式 */
.no-decoration::before {
  display: none;
}

.date-hint {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.4);
  font-weight: normal;
  display: inline-block;
  line-height: 1.2;
  margin-left: 6rpx;
}

/* 服务选择器 */
.service-selector {
  margin-bottom: 40rpx;
  margin-top: 30rpx; /* 减小顶部外边距，因为轮播图标题容器高度减小了 */
}

/* 加载中容器 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.1);
  border-top: 4rpx solid #ffffff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 增加加载中的内容显示效果 - 闪光效果 */
.loading-content-placeholder {
  opacity: 0.7;
  position: relative;
}

.loading-content-placeholder::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  animation: loading-shimmer 1.5s infinite;
  z-index: 10;
  pointer-events: none;
}

@keyframes loading-shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* 优化加载体验，增加平滑过渡效果 */
.fade-transition {
  transition: opacity 0.3s ease-in-out;
}

.loading-text {
  font-size: 26rpx;
  color: #aeaeae;
}

/* 空状态容器 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 0;
}

.empty-icon {
  width: 100rpx;
  height: 100rpx;
  margin-bottom: 20rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 28rpx;
  color: #aeaeae;
}

/* 服务列表 */
.service-list {
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 10rpx 0;
}

/* 服务项目 */
.service-item {
  position: relative;
  margin-bottom: 40rpx; /* 增加项目之间的间距 */
  transition: transform 0.3s ease;
}

/* 服务卡片相关样式重新设计 */
.service-card {
  background-color: rgba(30, 30, 30, 0.8); /* 稍微透明的深色背景 */
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  position: relative;
  border: 1rpx solid rgba(255, 255, 255, 0.1); /* 添加微妙的边框 */
}

.service-item.selected .service-card {
  background-color: rgba(50, 50, 50, 0.95);
  box-shadow: 0 10rpx 25rpx rgba(0, 0, 0, 0.4);
  transform: translateY(-8rpx);
  border: 2rpx solid rgba(255, 204, 51, 0.6);
}

.service-image {
  width: 100%;
  height: 380rpx; /* 恢复：从320rpx恢复到380rpx */
  object-fit: cover; /* 确保图片正确填充 */
  transition: all 0.3s ease;
}

.service-item.selected .service-image {
  filter: brightness(1.1) contrast(1.05); /* 选中时图片更亮并增加对比度 */
}

/* 内容区域重新设计 - 进一步压缩高度 */
.service-content {
  padding: 8rpx 20rpx; /* 减少：从12rpx 20rpx减少到8rpx 20rpx */
  position: relative;
  display: flex;
  flex-direction: column;
}

/* 服务头部：名称和分享按钮 - 进一步压缩高度 */
.service-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4rpx; /* 减少：从6rpx减少到4rpx */
  width: 100%;
}

/* 描述和价格容器 - 水平排列 */
.desc-price-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin-top: 2rpx;
  min-height: 52rpx;
}

/* 服务标题容器 */
.service-title-container {
  display: flex;
  flex-direction: column;
  max-width: 65%;
  padding-left: 30rpx; /* 增加左侧内边距，与其他元素保持一致 */
}

/* 服务名称样式 */
.service-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #ffcc33; /* 改为金黄色，与优惠价颜色一致 */
  margin-bottom: 4rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
}

/* 服务副标题样式 */
.service-subtitle {
  font-size: 24rpx;
  color: #cccccc;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
}

/* 下拉菜单中的样式 */
.dropdown-item .service-title-container {
  max-width: 60%;
}

.dropdown-item .service-name {
  font-size: 28rpx;
  color: #ffffff;
  margin-bottom: 2rpx;
}

.dropdown-item .service-subtitle {
  font-size: 22rpx;
  color: #cccccc;
}

/* 头部操作区域（分享和预约按钮） */
.header-actions {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  min-width: 170rpx; /* 确保有足够的宽度 */
}

/* 分享按钮容器 */
.share-button-wrapper {
  width: 50rpx;
  height: 50rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx; /* 增加：从20rpx增加到24rpx，增加间距 */
  position: relative; /* 添加相对定位 */
  z-index: 6; /* 确保在预约按钮之上 */
  padding: 5rpx; /* 添加内边距，增加可点击区域 */
}

/* 预约按钮包装容器 */
.appointment-btn-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 6;
  padding: 5rpx; /* 添加内边距，增加可点击区域 */
}

/* 预约按钮重新设计 - 移到顶部并稍微放大 */
.appointment-btn {
  width: 100rpx; /* 增加：从90rpx增加到100rpx */
  height: 48rpx; /* 增加：从44rpx增加到48rpx */
  background-color: rgba(255, 255, 255, 0.15);
  color: #ffffff;
  border: 1rpx solid rgba(255, 204, 51, 0.6); /* 将边框颜色改为金黄色 */
  border-radius: 24rpx; /* 增加：从22rpx增加到24rpx */
  font-size: 24rpx; /* 增加：从22rpx增加到24rpx */
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(5px);
  transition: all 0.3s ease;
  font-weight: 500;
  position: relative; /* 添加相对定位 */
  z-index: 5; /* 确保在合适的层级 */
}

.appointment-btn:active {
  transform: scale(0.95);
  background-color: rgba(255, 255, 255, 0.25);
}

/* 服务描述 - 与价格水平对齐 */
.service-desc {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.2;
  max-height: 52rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  flex: 1;
  margin-right: 10rpx;
  align-self: center; /* 确保垂直居中 */
}

/* 价格信息 - 放在右侧 */
.price-info {
  display: flex;
  align-items: center; /* 改为居中对齐 */
  justify-content: flex-end;
  min-width: 140rpx; /* 增加最小宽度 */
  text-align: right;
  padding-left: 8rpx; /* 添加左侧内边距，与描述保持一定距离 */
  transform: translateX(-15rpx); /* 向左移动15rpx */
}

/* 价格组 - 原价和现价水平排列 */
.price-group {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

/* 价格行样式 */
.price-row {
  display: flex;
  align-items: center;
  margin-bottom: 6rpx;
  margin-right: 0;
}

/* 优惠价行样式 */
.discount-price-row {
  margin-top: 4rpx;
}

/* 原价标签样式 */
.original-price-label {
  font-size: 24rpx;
  color: #999999;
  margin-right: 4rpx;
}

/* 优惠价标签样式 */
.discount-price-label {
  font-size: 24rpx;
  color: #ffcc33;
  margin-right: 4rpx;
}

/* 原价样式 */
.service-original-price {
  font-size: 24rpx;
  color: #999999;
  text-decoration: line-through;
  margin-right: 8rpx;
}

/* 服务价格样式 */
.service-price {
  font-size: 32rpx; /* 从28rpx增加约15%到32rpx */
  color: #ffcc33; /* 使用金黄色作为醒目颜色 */
  font-weight: 500;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.3); /* 添加轻微文字阴影增强可读性 */
}

/* 选中指示器重新设计 */
.select-indicator {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  width: 44rpx;
  height: 44rpx;
  background-color: #ffcc33;
  color: #333;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 10rpx rgba(255, 204, 51, 0.5);
  z-index: 5;
  border: 2rpx solid rgba(255, 255, 255, 0.8);
}

.check-icon {
  font-size: 24rpx;
  font-weight: bold;
}

/* 已选服务信息 */
.selected-service-info {
  background-color: #252525;
  border-radius: 16rpx;
  padding: 25rpx; /* 减少内边距 */
  margin-bottom: 30rpx; /* 增加底部外边距 */
  position: relative;
  border: 1rpx solid #333333; /* 使用实线边框 */
}

/* 添加切换指示器 */
.selected-service-info::after {
  content: "▼";
  position: absolute;
  right: 10rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.6);
  transition: transform 0.3s ease;
}

.selected-service {
  width: 100%;
}

.selected-service-name {
  font-size: 32rpx;
  color: #ffcc33; /* 改为金黄色，与优惠价颜色一致 */
  font-weight: 500;
}

.selected-service-price {
  font-size: 32rpx; /* 从28rpx增加约15%到32rpx */
  color: #ffcc33; /* 使用金黄色作为醒目颜色 */
  font-weight: 500;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.3); /* 添加轻微文字阴影增强可读性 */
}

/* 确保在第二步和弹窗中价格信息也向左移动 */
.price-info-step2,
.price-info-modal {
  transform: translateX(-15rpx); /* 向左移动15rpx */
}

/* 确保在第二步和弹窗中原价和现价字体大小一致 */
.service-info-step2 .service-original-price,
.price-info-modal .service-original-price {
  font-size: 24rpx;
}

/* 确保在第二步和弹窗中原价标签字体大小一致 */
.service-info-step2 .original-price-label,
.price-info-modal .original-price-label {
  font-size: 24rpx;
  color: #999999;
  margin-right: 2rpx;
}

/* 统一所有箭头图标样式 */
.arrow-down,
.toggle-icon {
  font-size: 24rpx;
  color: #999999;
  transform: rotate(0);
  transition: transform 0.3s;
  position: absolute;
  right: 30rpx; /* 调整右侧位置，使箭头图标对齐 */
  top: 50%;
  margin-top: -12rpx;
}

/* 展开状态下的箭头图标 */
.toggle-icon.expanded {
  transform: rotate(180deg);
}

/* 日期选择器优化 */
.date-selector {
  position: relative; /* 添加相对定位 */
}

/* 日期选择器优化 */
.date-picker {
  height: 90rpx;
  background-color: #252525;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
  margin: 25rpx 0; /* 统一上下外边距 */
  font-size: 28rpx;
  color: #ffffff; /* 默认白色 */
  position: relative;
  border: 1rpx solid #333333;
  transition: all 0.3s ease; /* 添加过渡效果 */
}

/* 用户选择后的日期选择器样式 */
.date-picker.user-selected {
  color: #ffcc33; /* 用户选择后变为金黄色 */
}

/* 点击激活状态的日期选择器样式 */
.date-picker.date-picker-active {
  color: #ffcc33; /* 点击后文字变为金黄色 */
  /* 移除边框和背景颜色的变化，只保留文字颜色变化 */
}

/* 手机号输入优化 */
.phone-input-container {
  position: relative;
  margin-top: 8rpx;
}

.phone-tip-top {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.4);
  margin-bottom: 8rpx;
  text-align: center;
}

.phone-input {
  height: 90rpx;
  background-color: #252525;
  border-radius: 12rpx;
  padding: 0 30rpx;
  color: #ffcc33; /* 改为金黄色，与价格颜色一致 */
  font-size: 28rpx;
  border: 1rpx solid #333333;
  transition: all 0.3s ease;
  text-align: left; /* 将文本从居中改为靠左对齐 */
  margin: 25rpx 0; /* 统一上下外边距 */
}

/* 手机号输入框高亮效果 */
.phone-input-highlight {
  border: 2rpx solid #ffcc33;
  background-color: #2a2a2a;
  box-shadow: 0 0 15rpx rgba(255, 204, 51, 0.3);
  animation: pulse 1s infinite alternate;
}

@keyframes pulse {
  from {
    box-shadow: 0 0 10rpx rgba(255, 204, 51, 0.2);
  }
  to {
    box-shadow: 0 0 15rpx rgba(255, 204, 51, 0.4);
  }
}

.phone-tip {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.4);
  margin-top: 8rpx;
  display: block;
}

/* 操作按钮区域 */
.action-section {
  position: relative;
  padding: 20rpx 0;
  margin-top: 20rpx;
  z-index: 10; /* 确保在内容之上 */
}

.action-btn {
  width: 100%;
  height: 90rpx;
  line-height: 90rpx;
  text-align: center;
  border-radius: 45rpx;
  font-size: 32rpx;
  font-weight: bold;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.next-btn {
  background-color: #333333;
  color: #ffffff;
}

.next-btn.disabled {
  background-color: #2a2a2a;
  color: #666666;
  opacity: 0.8;
}

.submit-btn {
  background-color: #333333;
  color: #ffffff;
}

.submit-btn.disabled {
  background-color: #2a2a2a;
  color: #666666;
  opacity: 0.8;
}

.action-btn:active {
  opacity: 0.8;
  transform: scale(0.98);
}

.btn-icon {
  width: 24rpx;
  height: 24rpx;
  margin-left: 10rpx;
}

/* 预约弹窗 - 修改为底部弹出样式 */
.appointment-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: flex-end;
  justify-content: center;
  /* 添加过渡动画，让弹窗关闭时平滑滑出 */
  transition: opacity 0.3s ease-out;
  width: 100%;
}

/* 弹窗关闭时的样式 */
.appointment-modal.closing {
  opacity: 0;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.75);
  backdrop-filter: blur(5px);
  z-index: 1001;
}

/* 预约弹窗的遮罩层 */
.appointment-modal .modal-mask {
  position: fixed;
  width: 100%;
  height: 100%;
}

/* 预约弹窗的内容区域 - 使用更具体的选择器并增加特异性 */
.appointment-modal .modal-content {
  width: 100% !important; /* 使用!important确保宽度100% */
  margin: 0;
  padding: 0;
  background-color: #1a1a1a;
  border-radius: 24rpx 24rpx 0 0;
  overflow: hidden;
  position: fixed; /* 修改为fixed定位以避免相对定位冲突 */
  bottom: 0;
  left: 0;
  right: 0; /* 确保右侧也设置到0 */
  z-index: 1002;
  box-shadow: 0 -8rpx 30rpx rgba(0, 0, 0, 0.5);
  display: flex;
  flex-direction: column;
  padding-bottom: env(safe-area-inset-bottom); /* 添加底部安全区域距离 */
  max-height: 85vh;
  transform: translateY(100%); /* 初始位置在屏幕底部 */
  transition: transform 0.3s ease-out;
  box-sizing: border-box;
}

/* 预约弹窗显示时的样式 */
.appointment-modal .modal-content.show {
  transform: translateY(0); /* 显示时移动到正常位置 */
  box-sizing: border-box;
}

/* 预约弹窗关闭时的向下滑出样式 */
.appointment-modal.closing .modal-content {
  transform: translateY(100%);
}



/* 登录弹窗相关样式 */
.login-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center; /* 垂直居中 */
  justify-content: center; /* 水平居中 */
}

.login-modal .modal-content {
  width: 80%; /* 宽度为屏幕的80% */
  max-width: 600rpx; /* 设置最大宽度 */
  background-color: #1a1a1a;
  border-radius: 20rpx; /* 四周都是圆角 */
  overflow: hidden;
  position: relative;
  z-index: 1002;
  box-shadow: 0 0 30rpx rgba(0, 0, 0, 0.5); /* 四周阴影 */
  display: flex;
  flex-direction: column;
  max-height: 60vh;
  transform: none; /* 移除任何可能的transform */
  margin: 0; /* 移除可能的外边距 */
  animation: fadeIn 0.3s ease-out; /* 使用淡入动画 */
}

/* 添加淡入动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 登录提示文字样式 */
.login-tip {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin: 20rpx 0 40rpx;
  text-align: center;
  width: 100%;
}

.login-tip-text {
  font-size: 32rpx;
  color: #ffffff;
  font-weight: 500;
  text-align: center;
  margin-bottom: 20rpx;
  padding: 0 20rpx;
}

/* 登录按钮样式 */
.login-btn {
  width: 80%;
  height: 90rpx;
  line-height: 90rpx;
  background-color: #07c160; /* 微信官方绿色 */
  color: #ffffff;
  font-size: 32rpx;
  font-weight: 500;
  border-radius: 45rpx;
  text-align: center;
  margin: 20rpx auto 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(7, 193, 96, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  border: none;
}

/* 添加：向下滑出动画（可以在JS中添加类名触发） */
.modal-content.slide-down {
  animation: slideDown 0.3s ease-out forwards;
}

@keyframes slideDown {
  from {
    transform: translateY(0);
  }
  to {
    transform: translateY(100%);
  }
}

/* 预约弹窗的头部样式 */
.appointment-modal .modal-header {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx 30rpx 30rpx; /* 增加左右内边距 */
  border-bottom: 1rpx solid #2a2a2a;
  position: relative;
  box-sizing: border-box;
  width: 100%;
}

/* 通用的modal-header样式（用于其他弹窗） */
.modal-header {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx 30rpx 30rpx; /* 保持原有内边距 */
  border-bottom: 1rpx solid #2a2a2a;
  position: relative;
}

/* 顶部拖动条优化 */
.modal-header::before {
  content: "";
  position: absolute;
  top: 20rpx; /* 调整位置 */
  left: 50%;
  transform: translateX(-50%);
  width: 70rpx; /* 增加宽度 */
  height: 6rpx;
  background-color: #464646;
  border-radius: 3rpx;
}

.modal-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #ffffff;
  letter-spacing: 1rpx;
  padding-top: 16rpx;
  text-align: center;
}

.modal-close {
  font-size: 42rpx;
  color: #999999;
  padding: 10rpx 15rpx;
  height: 44rpx;
  width: 44rpx;
  line-height: 44rpx;
  text-align: center;
  border-radius: 50%;
  transition: all 0.2s;
  position: absolute;
  right: 20rpx;
  top: 40rpx; /* 调整位置与顶部内边距一致 */
}

.modal-close:active {
  background-color: rgba(255, 255, 255, 0.15);
}

/* 预约弹窗的主体样式 */
.appointment-modal .modal-body {
  flex: 1;
  padding: 30rpx 30rpx; /* 增加左右内边距，确保内容不被截断 */
  overflow-y: auto;
  max-height: 65vh;
  box-sizing: border-box;
  width: 100%;
}

/* 通用的modal-body样式（用于其他弹窗） */
.modal-body {
  flex: 1;
  padding: 40rpx; /* 保持原有内边距 */
  overflow-y: auto;
  max-height: 65vh;
}

.login-modal .modal-body {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30rpx 40rpx 20rpx;
  overflow-y: auto;
  text-align: center;
}

/* 预约弹窗的底部样式 */
.appointment-modal .modal-footer {
  display: flex;
  justify-content: space-around; /* 改为均匀分布，让左边距、中间距、右边距一致 */
  align-items: center; /* 垂直居中对齐 */
  padding: 30rpx 40rpx 50rpx; /* 减少左右内边距，确保按钮不被截屏 */
  border-top: 1rpx solid #2a2a2a;
  box-sizing: border-box;
  width: 100%;
}

/* 通用的modal-footer样式（用于其他弹窗） */
.modal-footer {
  display: flex;
  justify-content: space-around; /* 改为均匀分布，让左边距、中间距、右边距一致 */
  align-items: center; /* 垂直居中对齐 */
  padding: 30rpx 60rpx 50rpx; /* 保持原有内边距 */
  border-top: 1rpx solid #2a2a2a;
}

.cancel-btn,
.confirm-btn {
  flex: 0 0 auto; /* 改为不伸缩，固定宽度 */
  width: 200rpx; /* 设置固定宽度，比原来更小 */
  height: 90rpx;
  border-radius: 20rpx; /* 减小圆角，从45rpx改为20rpx */
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  transition: all 0.3s;
  letter-spacing: 2rpx;
}

.cancel-btn {
  background-color: #2a2a2a;
  color: #b0b0b0;
  margin-right: 30rpx; /* 增加按钮间距 */
  font-weight: 500;
}

.confirm-btn {
  background: #f3dfc4; /* 使用淡雅的米黄色 */
  color: #333333; /* 改为深色文字，确保在浅色背景上清晰可见 */
  font-weight: 600;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(243, 223, 196, 0.15); /* 添加相应的米黄色阴影 */
}

.confirm-btn::after {
  content: "";
  position: absolute;
  top: -10%;
  left: -10%;
  width: 120%;
  height: 120%;
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  transform: translateX(-100%) skewX(-15deg);
  transition: transform 0.6s;
}

.confirm-btn:active::after {
  transform: translateX(100%) skewX(-15deg);
}

.confirm-btn.disabled {
  background: rgba(243, 223, 196, 0.4); /* 禁用状态使用相同的米黄色，但更透明 */
  color: rgba(51, 51, 51, 0.6); /* 禁用状态文字也相应变淡 */
  box-shadow: none; /* 移除阴影 */
  opacity: 0.7; /* 稍微提高透明度 */
}

/* 预约弹窗中的服务信息样式 */
.appointment-modal .selected-service-info {
  background-color: #252525;
  border-radius: 16rpx;
  padding: 20rpx 25rpx; /* 增加左右内边距，确保内容不被截断 */
  margin-bottom: 30rpx; /* 增加底部外边距 */
  position: relative; /* 添加相对定位 */
  border: 1rpx solid #333333; /* 使用实线边框 */
  box-sizing: border-box;
  width: 100%;
}

/* 通用的服务信息样式（用于其他地方） */
.selected-service-info {
  background-color: #252525;
  border-radius: 16rpx;
  padding: 25rpx; /* 保持原有内边距 */
  margin-bottom: 30rpx; /* 增加底部外边距 */
  position: relative; /* 添加相对定位 */
  border: 1rpx solid #333333; /* 使用实线边框 */
}

/* 弹窗中的服务名称样式 */
.selected-service-info .service-name {
  color: #ffcc33; /* 弹窗中的服务名称改为金黄色，与优惠价颜色一致 */
}

/* 预约弹窗中的服务头部样式 */
.appointment-modal .service-header-modal {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  position: relative;
  padding-right: 30rpx; /* 减少右边距，为箭头图标留出空间 */
  box-sizing: border-box;
  padding-left: 5rpx; /* 添加左边距 */
}

/* 通用的服务头部样式（用于其他地方） */
.service-header-modal {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  position: relative;
  padding-right: 40rpx; /* 保持原有右边距 */
}

/* 价格信息样式 */
.price-info-modal {
  display: flex;
  align-items: flex-start;
  justify-content: flex-end;
}

/* 价格组 - 垂直排列 */
.price-group {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

/* 价格行样式 */
.price-row {
  display: flex;
  align-items: center;
  margin-right: 12rpx;
}

/* 原价样式 */
.service-original-price {
  font-size: 24rpx;
  color: #999999;
  text-decoration: line-through;
}

/* 价格样式 */
.service-price {
  font-size: 32rpx;
  color: #ffcc33; /* 保持金色主题 */
  font-weight: 600;
}

/* 服务下拉菜单 */
.service-dropdown {
  margin-top: 20rpx;
  background-color: #222222;
  border-radius: 12rpx;
  overflow: hidden;
  max-height: 0;
  transition: max-height 0.3s ease;
  border: 1rpx dashed #444444; /* 改为虚线边框并调整颜色 */
  width: 100%;
  box-sizing: border-box;
}

.service-dropdown.show {
  max-height: 500rpx;
  overflow-y: auto;
}

.dropdown-item {
  padding: 25rpx 30rpx; /* 增加左右内边距 */
  border-bottom: 1rpx dashed #444444; /* 改为虚线边框并调整颜色 */
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: background-color 0.2s ease;
  box-sizing: border-box;
  width: 100%;
}

.dropdown-item:last-child {
  border-bottom: none;
}

.dropdown-item.active {
  background-color: #2a2a2a;
}

.dropdown-item:active {
  background-color: #333333;
}

/* 下拉菜单中的价格信息样式 */
.dropdown-item .price-info {
  display: flex;
  align-items: flex-start;
  justify-content: flex-end;
  margin-right: 0; /* 下拉菜单项不需要为折叠图标留空间 */
}

.dropdown-item .price-group {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.dropdown-item .price-row {
  display: flex;
  align-items: center;
  margin-bottom: 2rpx;
  margin-right: 0;
}

.dropdown-item .discount-price-row {
  margin-top: 2rpx;
}

.dropdown-item .original-price-label {
  font-size: 22rpx;
  color: #999999;
  margin-right: 4rpx;
}

.dropdown-item .discount-price-label {
  font-size: 22rpx;
  color: #ffcc33;
  margin-right: 4rpx;
}

/* 下拉菜单中的价格样式 */
.dropdown-item .service-price {
  font-size: 26rpx;
  color: #ffcc33;
  font-weight: 500;
}

.dropdown-item .service-original-price {
  font-size: 22rpx;
  color: #999999;
  text-decoration: line-through;
}

/* 店铺地址信息 */
.shop-location {
  background-color: #252525;
  border-radius: 12rpx;
  padding: 20rpx 30rpx; /* 增加左右内边距 */
  display: flex;
  align-items: center;
  margin: 25rpx 0; /* 减少上下外边距 */
  border: 1rpx solid #333333;
  width: 100%;
  box-sizing: border-box;
}

.location-icon {
  width: 36rpx;
  height: 36rpx;
  margin-right: 15rpx; /* 减少图标与文字间距 */
}

.location-text {
  font-size: 28rpx;
  color: #ffcc33; /* 修改为金黄色 */
  flex: 1;
}

/* 小提示文本 */
.small-tip-left {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.4);
  margin-bottom: 8rpx;
  margin-left: 10rpx;
}

/* 图片上传区域样式 */
.image-upload-section {
  position: relative;
  margin: 25rpx 0; /* 调整为25rpx，与其他折叠窗口保持一致 */
}

/* 图片上传标题容器 */
.image-header-container {
  background-color: #252525;
  border-radius: 12rpx;
  padding: 20rpx 30rpx;
  border: 1rpx solid #333333;
  position: relative; /* 确保有相对定位 */
  width: 100%;
  box-sizing: border-box;
}

.image-header-container.expanded {
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}

/* 图片上传标题文本 */
.image-header-text {
  font-size: 28rpx;
  color: #fff;
  display: flex;
  align-items: center;
  padding-left: 10rpx; /* 减少左侧内边距 */
  padding-right: 40rpx; /* 为箭头图标留出空间 */
}

/* 当有图片上传时的标题文本样式 */
.image-header-text.images-selected {
  color: #ffcc33; /* 金黄色 */
}

.image-optional {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
  margin-left: 10rpx;
}

.image-container {
  background-color: rgba(255, 255, 255, 0.05);
  padding: 20rpx 30rpx; /* 增加左右内边距 */
  border-bottom-left-radius: 10rpx;
  border-bottom-right-radius: 10rpx;
  border-left: 1rpx solid #333333;
  border-right: 1rpx solid #333333;
  border-bottom: 1rpx solid #333333;
  width: 100%;
  box-sizing: border-box;
}

.image-preview-area {
  width: 100%;
}

.uploaded-images {
  display: flex;
  justify-content: space-between; /* 使元素均匀分布 */
  width: 100%;
}

.image-item {
  width: 30%; /* 设置为容器宽度的30%，确保三个元素均匀分布 */
  height: 160rpx;
  position: relative;
  border-radius: 8rpx;
  overflow: hidden;
}

.preview-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.delete-image {
  position: absolute;
  top: 0;
  right: 0;
  width: 40rpx;
  height: 40rpx;
  background-color: rgba(0, 0, 0, 0.5);
  color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 28rpx;
}

.add-image-btn {
  width: 30%; /* 与image-item保持一致 */
  height: 160rpx;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 8rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

/* 添加禁用状态样式 */
.add-image-btn.disabled {
  opacity: 0.4;
  background-color: rgba(255, 255, 255, 0.05);
  pointer-events: none; /* 禁止点击 */
}

.add-icon {
  font-size: 48rpx;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1;
}

.add-text {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
  margin-top: 10rpx;
}

/* 员工选择区域相关样式 */
.staff-selection-section {
  position: relative;
  margin: 25rpx 0; /* 调整为25rpx，与其他折叠窗口保持一致 */
}

.staff-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  cursor: pointer;
  margin: 0;
  padding: 0;
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.4);
}

/* 员工选择标题容器 */
.staff-header-container {
  background-color: #252525;
  border-radius: 12rpx;
  padding: 20rpx 30rpx; /* 增加左右内边距 */
  border: 1rpx solid #333333;
  position: relative; /* 确保有相对定位 */
  width: 100%;
  box-sizing: border-box;
}

/* 员工选择标题文本 */
.staff-header-text {
  font-size: 28rpx;
  color: #ffffff; /* 默认白色 */
  text-align: left; /* 将文本从居中改为靠左对齐 */
  position: relative;
  padding-left: 10rpx; /* 减少左侧内边距 */
  padding-right: 40rpx; /* 为箭头图标留出空间 */
}

/* 选择了服务人员后的样式 */
.staff-header-text.staff-selected {
  color: #ffcc33; /* 选择服务人员后变为金黄色 */
}

/* 可选标签 */
.staff-optional {
  font-size: 24rpx;
  color: #999999;
  margin-left: 10rpx;
}

/* 展开状态下的标题容器 */
.staff-header-container.expanded {
  border-radius: 12rpx 12rpx 0 0;
  border-bottom: none;
}

/* 员工选择容器（可折叠） */
.staff-container {
  margin-top: 0;
  animation: slideDown 0.3s ease;
  background-color: #252525;
  border-radius: 12rpx;
  border: 1rpx solid #333333;
  padding: 10rpx 0; /* 移除左右内边距，只保留上下内边距 */
  overflow: hidden;
  width: 100%;
  box-sizing: border-box;
}

/* 调整列表高度以适应新的项目高度 */
.staff-list {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  padding: 25rpx 0; /* 移除左右内边距，只保留上下内边距 */
  overflow-x: scroll; /* 确保可以滚动 */
  white-space: nowrap;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  gap: 15rpx; /* 稍微减少间距 */
  width: 100%;
  box-sizing: border-box;
  scroll-behavior: smooth; /* 添加平滑滚动效果 */
  padding-left: 15rpx; /* 添加少量左边距，避免第一个元素贴边 */
  -webkit-overflow-scrolling: touch; /* 增强iOS滚动体验 */
}

/* 隐藏滚动条 */
.staff-list::-webkit-scrollbar {
  display: none;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.staff-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 25rpx 0;
}

.staff-loading .loading-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #333333;
  border-top: 4rpx solid #ffcc33;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.staff-loading .loading-text {
  margin-top: 15rpx;
  font-size: 24rpx;
  color: #999999;
}

.staff-empty {
  padding: 25rpx 0;
  text-align: center;
}

.staff-empty .empty-text {
  font-size: 24rpx;
  color: #999999;
}

.staff-item {
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  padding: 15rpx;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 12rpx;
  min-width: 140rpx;
  position: relative;
  transition: all 0.3s ease;
  justify-content: center;
  box-sizing: border-box;
  flex-shrink: 0;
  margin-right: 5rpx; /* 添加右边距，使元素之间有间隔 */
}

/* 确保最后一个元素也有右边距，避免贴边 */
.staff-item:last-child {
  margin-right: 15rpx;
}

.staff-item.selected {
  background-color: rgba(255, 255, 255, 0.2);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
}

.staff-item.disabled {
  opacity: 0.6;
  background-color: rgba(255, 255, 255, 0.05);
}

.staff-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-bottom: 8rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  object-fit: cover;
}

.staff-name {
  font-size: 24rpx;
  color: #ffffff;
  margin-bottom: 6rpx;
  width: 100%;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  letter-spacing: 0.5rpx;
}

.staff-status {
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  color: #ffffff;
  min-width: 80rpx;
  text-align: center;
  line-height: 1.4;
  box-sizing: border-box;
}

.staff-status.available {
  background-color: #07c160; /* 绿色 - 空闲 */
}

.staff-status.busy {
  background-color: #ffa300; /* 黄色 - 服务中 */
}

.staff-status.rest {
  background-color: #ff6b6b; /* 淡红色 - 休息 */
}

.staff-selected-icon {
  position: absolute;
  top: 5rpx;
  right: 5rpx;
  width: 24rpx;
  height: 24rpx;
  background-color: #07c160;
  color: #ffffff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.2);
}

/* 轮播图样式 */
.carousel-container {
  width: 100%;
  padding: 0; /* 移除内边距，使轮播图占满屏幕宽度 */
  box-sizing: border-box;
  margin: 0; /* 移除外边距 */
  position: relative; /* 添加相对定位 */
  z-index: 5; /* 确保在背景之上 */
  margin-bottom: 100rpx; /* 为标题容器预留空间 */
}

/* 轮播图占位符 */
.carousel-placeholder {
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.05);
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 轮播图区域 */
.carousel-swiper {
  width: 100%;
  height: 650rpx; /* 将高度从720rpx调整为650rpx，更加适合页面布局 */
  overflow: hidden;
}

.carousel-image {
  width: 100%;
  height: 100%;
}

/* 轮播图底部渐变遮罩 */
.carousel-bottom-gradient {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 33.33%; /* 占轮播图高度的1/3 */
  background: linear-gradient(
    to top,
    rgba(0, 0, 0, 1) 0%,
    rgba(0, 0, 0, 0.6) 25%,
    rgba(0, 0, 0, 0.3) 50%,
    rgba(0, 0, 0, 0.1) 75%,
    transparent 100%
  );
  z-index: 7; /* 确保在轮播图之上，但在标题容器之下 */
  pointer-events: none; /* 不阻挡轮播图的交互 */
}

/* 轮播图底部渐变遮罩 */
.carousel-bottom-gradient {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 33.33%; /* 占轮播图高度的1/3 */
  background: linear-gradient(
    to top,
    rgba(0, 0, 0, 0.4) 0%,
    rgba(0, 0, 0, 0.2) 30%,
    rgba(0, 0, 0, 0.1) 60%,
    transparent 100%
  );
  z-index: 7; /* 确保在轮播图之上，但在标题容器之下 */
  pointer-events: none; /* 不阻挡轮播图的交互 */
}

/* 固定的标题容器 */
.carousel-title-container {
  position: relative; /* 改回相对定位 */
  margin-top: -150rpx; /* 使容器上移，进入轮播图区域 */
  left: 50%; /* 水平居中 */
  transform: translateX(-50%); /* 水平居中 */
  width: 92%; /* 增加宽度，与服务列表宽度一致，只留少量边距 */
  height: 224rpx; /* 将高度从204rpx增加到224rpx，为装饰线提供更多空间 */
  padding: 20rpx 36rpx; /* 增加上下内边距，从15rpx增加到20rpx */
  background: linear-gradient(
    to top,
    rgba(40, 40, 40, 0.9),
    rgba(0, 0, 0, 0.9)
  ); /* 顶部改为深灰色，底部保持不变 */
  backdrop-filter: blur(5px); /* 减少模糊效果 */
  border-radius: 16rpx 16rpx 36rpx 36rpx; /* 增加上方圆角，从10rpx增加到16rpx，保持下方圆角不变 */
  color: #ffffff;
  box-sizing: border-box;
  z-index: 6; /* 确保在轮播图之上，但在服务列表之下 */
  display: flex;
  flex-direction: column;
  justify-content: flex-start; /* 改为从顶部开始布局 */
  box-shadow: 0 0 40rpx 0 rgba(255, 255, 255, 0.1); /* 四周均匀扩散的更淡白色阴影 */
  /* 添加可点击的视觉效果 */
  cursor: pointer;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

/* 添加点击状态效果 */
.carousel-title-container:active {
  transform: translateX(-50%) scale(0.98);
  box-shadow: 0 0 30rpx 0 rgba(255, 255, 255, 0.05);
}

/* 主标题区域 - 使用更高级的立体文字效果 */
.carousel-title-main {
  position: relative;
  width: 100%;
  max-height: 110rpx;
  overflow: hidden;
  box-sizing: border-box;
  margin-top: 18rpx;
  margin-bottom: 0; /* 移除底部外边距 */
  font-size: 38rpx;
  line-height: 1.3;
  font-weight: 600;
  /* 修改为高端香槟金色调，更具品牌质感 */
  color: rgba(253, 227, 167, 0.92);
  /* 修改基础文字阴影，只在底部添加非常淡的阴影 */
  text-shadow: 0 2rpx 1rpx rgba(30, 20, 0, 0.15);
  white-space: normal;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  letter-spacing: 2rpx;
  text-align: center;
  padding-bottom: 0; /* 移除内边距 */
  /* 使用更高级的3D效果技术 */
  position: relative;
  z-index: 2;
}

/* 创建文字阴影效果的伪元素 - 第一层阴影 */
.carousel-title-main::before {
  content: attr(data-text);
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  text-align: center;
  font-size: 38rpx;
  line-height: 1.3;
  font-weight: 600;
  letter-spacing: 2rpx;
  color: transparent;
  text-shadow: none;
  -webkit-text-stroke: 0.3rpx rgba(120, 80, 0, 0.05);
  transform: translateY(2rpx);
  filter: blur(2rpx);
}

/* 移除原来的装饰线伪元素 */
.carousel-title-main::after {
  display: none;
}

/* 添加新的独立装饰线元素 */
.title-decorator {
  width: 65%;
  height: 2.5rpx;
  /* 调整装饰线颜色，与标题颜色协调 */
  background: linear-gradient(
    to right,
    rgba(253, 227, 167, 0),
    rgba(253, 227, 167, 0.8),
    rgba(253, 227, 167, 0)
  );
  margin: 30rpx auto 15rpx; /* 增加上方外边距到30rpx，创造更大的与文字间距 */
  position: relative;
  z-index: 1;
  border-radius: 1.5rpx; /* 添加圆角，使线条更加精致 */
}

/* 添加额外的立体效果样式，使用更淡的底部阴影 */
@supports (-webkit-text-stroke: 1px black) {
  .carousel-title-main {
    position: relative;
    /* 添加轻微的金色光泽效果，增强高端质感 */
    text-shadow: 0 2rpx 2rpx rgba(30, 20, 0, 0.12),
      0 -1rpx 0 rgba(255, 235, 200, 0.1);
    -webkit-text-stroke: 0.3rpx rgba(120, 80, 0, 0.03);
  }
}

/* 底部信息栏 */
.carousel-info-bar {
  display: flex;
  justify-content: center; /* 保持居中对齐 */
  align-items: flex-end;
  width: 100%;
  height: 50rpx; /* 增加高度，从44rpx增加到50rpx */
  margin-top: 12rpx; /* 增加上边距，从10rpx增加到12rpx */
  margin-bottom: 18rpx; /* 增加下边距，从15rpx增加到18rpx */
  position: relative;
}

/* 左侧信息区域 */
.carousel-left-info {
  display: flex;
  align-items: flex-end;
  height: 100%;
  flex: 0 auto; /* 修改为不伸缩，从flex: 1改为flex: 0 auto */
  min-width: 0;
  margin-right: 15rpx; /* 保持右侧间距 */
}

/* Logo图标样式 */
.carousel-logo-icon {
  width: 42rpx; /* 增加图标大小，从37.4rpx到42rpx */
  height: 42rpx; /* 增加图标大小，从37.4rpx到42rpx */
  margin-right: 10rpx; /* 增加右侧间距，从8rpx到10rpx */
  object-fit: contain;
  vertical-align: bottom;
  padding: 2rpx;
  display: inline-block;
  flex-shrink: 0;
}

/* 副标题文字样式 */
.carousel-subtitle {
  font-size: 22rpx; /* 增加字体大小，从20rpx到22rpx */
  line-height: 22rpx; /* 调整行高与字体大小一致 */
  color: rgba(255, 255, 255, 0.9); /* 恢复为淡白色 */
  word-break: break-word;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.8);
  vertical-align: bottom;
  display: inline-block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 180rpx;
  transform: translateY(-10rpx);
  font-weight: 500; /* 添加字体粗细 */
}

/* 定位图标 */
.carousel-location {
  display: flex;
  justify-content: center;
  align-items: flex-end;
  height: 100%;
  flex: 1;
  min-width: 0;
  margin: 0 15rpx; /* 增加两侧间距，从10rpx到15rpx */
}

.carousel-location-icon {
  width: 44rpx; /* 增加图标大小，从40rpx到44rpx */
  height: 44rpx; /* 增加图标大小，从40rpx到44rpx */
  object-fit: contain;
  vertical-align: bottom;
  display: inline-block;
  flex-shrink: 0;
}

/* 定位文字样式 */
.carousel-location-text {
  font-size: 20rpx; /* 调整字体大小，从22rpx减小到20rpx */
  line-height: 20rpx; /* 调整行高与字体大小一致 */
  color: rgba(255, 255, 255, 0.9); /* 恢复为淡白色 */
  margin-left: 10rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.8);
  vertical-align: bottom;
  display: inline-block;
  transform: translateY(-10rpx);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 180rpx;
  font-weight: 500; /* 保持字体粗细 */
}

/* 营业时间样式 */
.carousel-business-hours {
  display: flex;
  align-items: flex-end;
  height: 100%;
  flex: 1;
  min-width: 0;
  margin-left: 15rpx; /* 添加左侧间距 */
}

.carousel-business-icon {
  width: 44rpx; /* 增加图标大小，从40rpx到44rpx */
  height: 44rpx; /* 增加图标大小，从40rpx到44rpx */
  margin-right: 10rpx; /* 增加右侧间距，从8rpx到10rpx */
  vertical-align: bottom;
  display: inline-block;
  flex-shrink: 0;
}

/* 营业时间文字样式 */
.carousel-business-text {
  font-size: 22rpx; /* 增加字体大小，从20rpx到22rpx */
  line-height: 22rpx; /* 调整行高与字体大小一致 */
  color: rgba(255, 255, 255, 0.9); /* 恢复为淡白色 */
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.8);
  vertical-align: bottom;
  display: inline-block;
  transform: translateY(-10rpx);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 180rpx;
  font-weight: 500; /* 添加字体粗细 */
}

/* 原有的标题样式保留但不再使用 */
.carousel-title {
  display: none;
}

/* 引导文字样式统一调整 */
.guide-text {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.4);
  margin-bottom: 8rpx;
  margin-top: 20rpx;
  font-weight: normal;
  letter-spacing: 0.5rpx;
}

/* 员工选择区域标题 */
.staff-header-container .section-title {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.4);
  font-weight: normal;
}

/* 服务标题容器 */
.service-title-container {
  display: flex;
  flex-direction: column;
  max-width: 65%;
  box-sizing: border-box;
  padding-right: 10rpx;
}

/* 服务副标题样式 */
.service-subtitle {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
  margin-top: 2rpx;
}

/* 下拉菜单中的标题容器 */
.dropdown-item .service-title-container {
  max-width: 65%;
  box-sizing: border-box;
  padding-right: 10rpx;
}

/* 下拉菜单中的副标题 */
.dropdown-item .service-subtitle {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.6);
}

/* 分享按钮样式 */
.share-button {
  background: none;
  border: none;
  padding: 0;
  margin: 0;
  line-height: normal;
  border-radius: 0;
  width: 50rpx;
  height: 50rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.share-button::after {
  border: none;
}

.share-icon {
  width: 40rpx;
  height: 40rpx;
}

/* 员工选择区域的箭头图标特殊位置调整 */
.staff-header-container .toggle-icon {
  right: 15rpx; /* 比其他箭头图标再向右移动15rpx */
}

/* 服务详情图片弹窗样式 */
.service-detail-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

/* 确保服务详情弹窗不受预约弹窗样式影响 */
.service-detail-modal .detail-modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: opacity 0.3s ease;
}

.service-detail-modal .detail-modal-overlay.fade-in {
  opacity: 1;
}

.service-detail-modal .detail-modal-overlay.fade-out {
  opacity: 0;
}

/* 详情弹窗内容 - 使用独立的类名避免与预约弹窗冲突 */
.service-detail-modal .detail-modal-content {
  position: relative;
  width: 90vw; /* 增加宽度给图片更多空间 */
  max-width: 90vw;
  max-height: 85vh; /* 增加最大高度 */
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  transform: scale(0.8);
  transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  padding: 10rpx; /* 减少内边距 */
  box-sizing: border-box;
  background-color: transparent; /* 透明背景 */
  border: none; /* 去掉边框 */
  border-radius: 15rpx; /* 稍小的圆角 */
}

/* 图片滚动容器 */
.detail-image-scroll-container {
  width: 100%;
  height: 100%;
  max-height: 75vh; /* 限制滚动容器的最大高度 */
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 详情弹窗呼吸式动画 - 进入 */
.service-detail-modal .detail-modal-content.breathe-in {
  animation: breatheIn 0.5s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
}

/* 详情弹窗呼吸式动画 - 退出 */
.service-detail-modal .detail-modal-content.breathe-out {
  animation: breatheOut 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

@keyframes breatheIn {
  0% {
    transform: scale(0.7) translateY(30rpx);
    opacity: 0;
    filter: blur(4rpx);
  }
  30% {
    transform: scale(0.9) translateY(10rpx);
    opacity: 0.6;
    filter: blur(2rpx);
  }
  70% {
    transform: scale(1.08) translateY(-5rpx);
    opacity: 0.9;
    filter: blur(0);
  }
  100% {
    transform: scale(1) translateY(0);
    opacity: 1;
    filter: blur(0);
  }
}

@keyframes breatheOut {
  0% {
    transform: scale(1) translateY(0);
    opacity: 1;
    filter: blur(0);
  }
  30% {
    transform: scale(1.05) translateY(-10rpx);
    opacity: 0.8;
    filter: blur(1rpx);
  }
  70% {
    transform: scale(0.9) translateY(10rpx);
    opacity: 0.4;
    filter: blur(2rpx);
  }
  100% {
    transform: scale(0.7) translateY(30rpx);
    opacity: 0;
    filter: blur(4rpx);
  }
}

/* 详情图片 */
.detail-image {
  width: 100%;
  height: auto;
  max-width: 100%; /* 适应滚动容器宽度 */
  /* 移除max-height限制，让长图完整显示 */
  border-radius: 16rpx;
  box-shadow: 0 12rpx 48rpx rgba(0, 0, 0, 0.4);
  background-color: rgba(255, 255, 255, 0.05);
  display: block;
}

/* 详情弹窗关闭按钮 - 固定在弹窗底部 */
.detail-close-button {
  position: relative; /* 改为相对定位 */
  margin-top: 20rpx; /* 与滚动容器保持间距 */
  width: 66rpx;
  height: 66rpx;
  background-color: rgba(255, 255, 255, 0.2); /* 透明白色背景 */
  border-radius: 50%; /* 圆形 */
  display: flex;
  align-items: center; /* 居中对齐 */
  justify-content: center;
  border: none; /* 去掉边框 */
  transition: all 0.3s ease;
  z-index: 999999; /* 最高层级 */
  opacity: 1; /* 确保可见 */
  box-shadow: 0 2rpx 10rpx rgba(255, 255, 255, 0.1); /* 淡白色阴影 */
}

.detail-close-button:active {
  transform: scale(0.9); /* 缩放效果 */
  background-color: rgba(255, 255, 255, 0.3); /* 稍微增加透明度 */
}

/* 关闭图标 - 纯白色叉叉 */
.close-icon {
  color: #ffffff; /* 纯白色 */
  font-size: 24rpx; /* 缩小字体以适应更小的按钮 */
  font-weight: bold;
  line-height: 1;
  text-align: center;
}

@keyframes fadeInDelay {
  from {
    opacity: 0;
    transform: translateY(-20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

