.admin-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f7;
}

.admin-header {
  background-color: #ffffff;
  padding: 90rpx 30rpx 40rpx;
  border-radius: 0 0 6rpx 6rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  position: relative;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  z-index: 10;
}

.back-icon {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.arrow-left {
  width: 20rpx;
  height: 20rpx;
  border-top: 4rpx solid #333333;
  border-left: 4rpx solid #333333;
  transform: rotate(-45deg);
}

.header-title {
  font-size: 36rpx;
  color: #333333;
  font-weight: bold;
  text-align: left;
}

.admin-menu {
  flex: 1;
  padding: 20rpx;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

/* 分类菜单样式 */
.menu-categories {
  display: flex;
  flex-direction: column;
  width: 100%;
}

/* 分类标题样式 */
.category-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25rpx 30rpx;
  background-color: #ffffff;
  border-radius: 6rpx;
  margin-bottom: 15rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  position: relative;
  z-index: 2;
  transition: all 0.3s ease;
}

.category-header.active {
  background-color: #0070c9;
  box-shadow: 0 2rpx 8rpx rgba(0, 112, 201, 0.2);
}

.category-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #4a4a4a;
  transition: color 0.3s ease;
}

.category-header.active .category-name {
  color: #ffffff;
}

/* 箭头样式 */
.category-arrow {
  width: 16rpx;
  height: 16rpx;
  border-right: 3rpx solid #4a4a4a;
  border-bottom: 3rpx solid #4a4a4a;
  transform: rotate(45deg);
  transition: all 0.3s ease;
}

.category-header.active .category-arrow {
  border-color: #ffffff;
  transform: rotate(-135deg);
  margin-top: 6rpx;
}

/* 分类内容样式 */
.category-content {
  overflow: hidden;
  transition: all 0.3s ease-in-out;
  margin-top: -5rpx;
  margin-bottom: 15rpx;
}

/* 菜单项容器 */
.menu-items {
  display: flex;
  flex-direction: column;
  padding: 10rpx;
  background-color: rgba(255, 255, 255, 0.5);
  border-radius: 6rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.03);
}

/* 菜单项样式 */
.menu-item {
  height: 110rpx;
  border-radius: 4rpx;
  background-color: #ffffff;
  box-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
  margin-bottom: 15rpx;
}

.menu-item:last-child {
  margin-bottom: 0;
}

/* 为不同类型设置柔和的独特颜色 */
/* 展示类型 */
.menu-categories .category-content:nth-of-type(2) .menu-item {
  background-color: rgba(0, 112, 201, 0.1);
  border-left: 4rpx solid #0070c9;
}

/* 数据管理类型 */
.menu-categories .category-content:nth-of-type(4) .menu-item {
  background-color: rgba(52, 199, 89, 0.08);
  border-left: 4rpx solid #34c759;
}

/* 经营统计类型 */
.menu-categories .category-content:nth-of-type(6) .menu-item {
  background-color: rgba(255, 59, 48, 0.08);
  border-left: 4rpx solid #ff3b30;
}

/* 系统类型 */
.menu-categories .category-content:nth-of-type(8) .menu-item {
  background-color: #f9f9f9;
  border-left: 4rpx solid #666666;
}

.menu-item:active {
  transform: translateY(1rpx);
  opacity: 0.9;
}

.menu-name {
  font-size: 28rpx;
  color: #333333;
  font-weight: 600;
  text-align: center;
  padding: 0 20rpx;
  line-height: 1.4;
}

.admin-footer {
  padding: 30rpx;
  display: flex;
  justify-content: center;
  margin-bottom: 30rpx;
  position: relative;
  z-index: 5;
}

.exit-btn {
  width: 60%;
  height: 80rpx;
  background-color: #0070c9;
  border: none;
  border-radius: 4rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 28rpx;
  color: #ffffff;
  margin: 20rpx 0 60rpx 0;
  box-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.exit-btn:active {
  opacity: 0.9;
  transform: translateY(1rpx);
} 