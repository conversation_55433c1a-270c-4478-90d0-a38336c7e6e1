/**
 * 测试工具函数
 * 提供测试中常用的辅助函数和模拟数据
 */

/**
 * 创建模拟的页面上下文
 * @param {object} customData - 自定义数据
 * @returns {object} 模拟的页面上下文
 */
function createMockPageContext(customData = {}) {
  const defaultData = {
    videoList: [],
    loading: false,
    firstLoading: true,
    hasMore: true,
    page: 0,
    pageSize: 4,
    urlCache: {},
    urlFetchingIds: [],
    lastRefreshTime: 0,
    isRefreshing: false,
    searchKeyword: '',
    isSearching: false,
    currentPlayingVideo: null,
    originalVideoList: null
  };

  const data = { ...defaultData, ...customData };
  
  return {
    data: data,
    setData: jest.fn((newData, callback) => {
      Object.assign(data, newData);
      if (callback) callback();
    }),
    app: getApp(),
    dataManager: {
      cacheVideoList: jest.fn(),
      batchCacheVideoUrls: jest.fn(),
      getCachedVideoList: jest.fn(),
      getCachedVideoUrl: jest.fn()
    }
  };
}

/**
 * 创建模拟的视频数据
 * @param {number} count - 视频数量
 * @param {object} customFields - 自定义字段
 * @returns {Array} 模拟的视频列表
 */
function createMockVideoList(count = 3, customFields = {}) {
  const videos = [];
  
  for (let i = 1; i <= count; i++) {
    videos.push({
      id: `video_${i}`,
      baseId: `base_${i}`,
      mainTitle: `测试视频 ${i}`,
      subTitle: `副标题 ${i}`,
      coverUrl: `http://test.com/cover_${i}.jpg`,
      videoUrl: `http://test.com/video_${i}.mp4`,
      playCount: i * 1000,
      author: `作者 ${i}`,
      authorAvatar: `http://test.com/avatar_${i}.jpg`,
      description: `这是测试视频 ${i} 的描述`,
      fileKey: `file_key_${i}`,
      createTime: new Date(Date.now() - i * 24 * 60 * 60 * 1000).toISOString(),
      isPlaying: false,
      urlError: false,
      ...customFields
    });
  }
  
  return videos;
}

/**
 * 创建模拟的视频工具函数
 * @param {object} customMethods - 自定义方法
 * @returns {object} 模拟的视频工具函数
 */
function createMockVideoUtils(customMethods = {}) {
  return {
    getVideoList: jest.fn().mockResolvedValue(createMockVideoList()),
    getVideoUrl: jest.fn().mockResolvedValue('http://test.com/video.mp4'),
    cleanExpiredCache: jest.fn(),
    ...customMethods
  };
}

/**
 * 创建模拟的URL缓存
 * @param {Array} videoIds - 视频ID列表
 * @param {boolean} expired - 是否过期
 * @returns {object} 模拟的URL缓存
 */
function createMockUrlCache(videoIds = ['1', '2'], expired = false) {
  const cache = {};
  const timestamp = expired ? 
    Date.now() - 2 * 60 * 60 * 1000 : // 2小时前（过期）
    Date.now() - 30 * 60 * 1000;      // 30分钟前（有效）
  
  videoIds.forEach(id => {
    cache[id] = {
      url: `http://test.com/video_${id}.mp4`,
      timestamp: timestamp,
      baseId: `base_${id}`,
      fileKey: `key_${id}`
    };
  });
  
  return cache;
}

/**
 * 等待异步操作完成
 * @param {number} ms - 等待时间（毫秒）
 * @returns {Promise} Promise对象
 */
function waitFor(ms = 0) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * 模拟网络延迟
 * @param {number} delay - 延迟时间（毫秒）
 * @returns {Promise} Promise对象
 */
function mockNetworkDelay(delay = 100) {
  return new Promise(resolve => setTimeout(resolve, delay));
}

/**
 * 验证视频数据结构
 * @param {object} video - 视频数据
 * @returns {boolean} 是否有效
 */
function isValidVideoData(video) {
  const requiredFields = ['id', 'mainTitle', 'baseId'];
  return requiredFields.every(field => video && video[field]);
}

/**
 * 验证视频列表数据结构
 * @param {Array} videoList - 视频列表
 * @returns {object} 验证结果
 */
function validateVideoList(videoList) {
  if (!Array.isArray(videoList)) {
    return { valid: false, error: '不是数组' };
  }
  
  const invalidVideos = videoList.filter(video => !isValidVideoData(video));
  
  return {
    valid: invalidVideos.length === 0,
    total: videoList.length,
    validCount: videoList.length - invalidVideos.length,
    invalidCount: invalidVideos.length,
    invalidVideos: invalidVideos
  };
}

/**
 * 创建测试用的错误对象
 * @param {string} message - 错误消息
 * @param {string} type - 错误类型
 * @returns {Error} 错误对象
 */
function createTestError(message = '测试错误', type = 'TEST_ERROR') {
  const error = new Error(message);
  error.type = type;
  return error;
}

/**
 * 模拟微信API调用
 * @param {string} apiName - API名称
 * @param {object} options - 调用选项
 * @returns {object} 模拟结果
 */
function mockWxApi(apiName, options = {}) {
  const { success = true, data = {}, delay = 0 } = options;
  
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      if (success) {
        resolve({ data, errMsg: `${apiName}:ok` });
      } else {
        reject({ errMsg: `${apiName}:fail`, ...data });
      }
    }, delay);
  });
}

/**
 * 断言函数：验证setData调用
 * @param {jest.Mock} setDataMock - setData的mock函数
 * @param {object} expectedData - 期望的数据
 * @param {number} callIndex - 调用索引
 */
function expectSetDataToBeCalled(setDataMock, expectedData, callIndex = -1) {
  expect(setDataMock).toHaveBeenCalled();
  
  const calls = setDataMock.mock.calls;
  const targetCall = callIndex >= 0 ? calls[callIndex] : calls[calls.length - 1];
  
  if (targetCall) {
    expect(targetCall[0]).toMatchObject(expectedData);
  }
}

/**
 * 断言函数：验证事件触发
 * @param {object} module - 模块实例
 * @param {string} eventName - 事件名称
 * @param {object} expectedData - 期望的事件数据
 */
function expectEventToBeEmitted(module, eventName, expectedData) {
  // 这里需要根据实际的事件系统实现来调整
  // 假设模块有一个 _emittedEvents 属性记录触发的事件
  if (module._emittedEvents) {
    const events = module._emittedEvents.filter(e => e.name === eventName);
    expect(events.length).toBeGreaterThan(0);
    
    if (expectedData) {
      const lastEvent = events[events.length - 1];
      expect(lastEvent.data).toMatchObject(expectedData);
    }
  }
}

module.exports = {
  createMockPageContext,
  createMockVideoList,
  createMockVideoUtils,
  createMockUrlCache,
  waitFor,
  mockNetworkDelay,
  isValidVideoData,
  validateVideoList,
  createTestError,
  mockWxApi,
  expectSetDataToBeCalled,
  expectEventToBeEmitted
};