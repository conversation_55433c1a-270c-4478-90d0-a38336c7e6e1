/* 我的预约页面样式 */
.my-appointments-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}

/* 标题栏样式 */
.header {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #ffffff;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 10;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
}

/* 筛选工具栏 */
.filter-bar {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #ffffff;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.filter-label {
  font-size: 28rpx;
  color: #666666;
  margin-right: 20rpx;
}

.filter-picker {
  flex: 1;
  height: 60rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 8rpx;
  padding: 0 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 28rpx;
  color: #333333;
  background-color: #f9f9f9;
}

.picker-arrow {
  font-size: 24rpx;
  color: #999999;
}

/* 预约列表 */
.appointment-list {
  flex: 1;
  overflow-y: auto;
  padding: 0 30rpx;
}

/* 加载中和空状态 */
.loading-container, .empty-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 100rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid rgba(74, 144, 226, 0.2);
  border-top: 4rpx solid #4a90e2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text, .empty-text {
  font-size: 28rpx;
  color: #999999;
  margin-bottom: 30rpx;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
  opacity: 0.5;
}

.go-appointment-btn {
  margin-top: 30rpx;
  padding: 16rpx 40rpx;
  background-color: #4a90e2;
  color: #ffffff;
  font-size: 28rpx;
  border-radius: 100rpx;
  box-shadow: 0 4rpx 12rpx rgba(74, 144, 226, 0.3);
}

/* 预约卡片 */
.appointment-item {
  background-color: #ffffff;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.appointment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.appointment-status {
  font-size: 24rpx;
  padding: 4rpx 16rpx;
  border-radius: 100rpx;
  color: #ffffff;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 40rpx; /* 固定高度 */
  box-sizing: border-box; /* 确保padding不会增加元素总高度 */
  line-height: 1; /* 重置行高 */
}

.appointment-status.pending {
  background-color: #f5a623;
}

.appointment-status.confirmed {
  background-color: #4a90e2;
}

.appointment-status.completed {
  background-color: #7ed321;
}

.appointment-status.cancelled {
  background-color: #9b9b9b;
}

.appointment-status.rejected {
  background-color: #d0021b;
}

.appointment-date {
  font-size: 24rpx;
  color: #666666;
}

.appointment-content {
  padding: 20rpx 30rpx;
}

.service-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.service-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
}

.service-price {
  font-size: 32rpx;
  color: #e02020;
  font-weight: bold;
}

.reject-reason {
  font-size: 24rpx;
  color: #666666;
  background-color: #fff6f6;
  padding: 16rpx 20rpx;
  border-radius: 8rpx;
  border-left: 4rpx solid #e02020;
}

.reason-label {
  color: #e02020;
  margin-right: 10rpx;
}

/* 操作按钮 */
.appointment-actions {
  display: flex;
  justify-content: flex-end;
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #f0f0f0;
}

.action-btn {
  margin-left: 20rpx;
  padding: 12rpx 30rpx;
  border-radius: 100rpx;
  font-size: 28rpx;
}

.action-btn.cancel {
  background-color: #ffffff;
  color: #e02020;
  border: 1rpx solid #e02020;
}

.action-btn.re-appointment {
  background-color: #4a90e2;
  color: #ffffff;
}

/* 加载更多 */
.load-more {
  text-align: center;
  padding: 30rpx 0;
  font-size: 24rpx;
  color: #999999;
}

/* 员工信息样式 */
.staff-info {
  display: flex;
  align-items: center;
  margin-top: 8px;
  font-size: 12px;
  color: #666;
}

.staff-label {
  color: #999;
  margin-right: 4px;
}

.staff-name {
  color: #333;
} 