/* 页面容器 */
.assigned-appointments-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
  padding-top: 230rpx; /* 调整为顶部状态栏+筛选栏的高度 */
}

/* 顶部状态栏 */
.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
  height: 100rpx;
  background-color: #ffffff;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  padding-top: 90rpx; /* 为胶囊按钮留出空间 */
  box-shadow: none; /* 去掉阴影，让筛选栏无缝连接 */
}

.back-icon {
  font-size: 40rpx;
  color: #333;
  width: 60rpx;
  display: flex;
  align-items: center;
}

.header-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.placeholder {
  width: 60rpx;
}

/* 内容区域 */
.content-scroll {
  flex: 1;
  height: calc(100vh - 230rpx); /* 调整减去头部+筛选栏的总高度 */
  width: 100%;
}

.content {
  padding: 20rpx;
  padding-bottom: 120rpx; /* 增加底部内边距，确保最后一条内容可以完全滚动到顶部 */
}

/* 加载中 */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
}

.loading-icon {
  width: 80rpx;
  height: 80rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-icon.small {
  width: 40rpx;
  height: 40rpx;
  border-width: 4rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #999;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.empty-tips {
  font-size: 26rpx;
  color: #999;
}

/* 预约列表 */
.appointment-list {
  margin-top: 10rpx;
}

.appointment-item {
  background-color: #fff;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.appointment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding-bottom: 15rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.appointment-status {
  font-size: 24rpx;
  padding: 6rpx 16rpx;
  border-radius: 100rpx;
  color: #fff;
  background-color: #999;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 40rpx; /* 固定高度 */
  box-sizing: border-box; /* 确保padding不会增加元素总高度 */
  line-height: 1; /* 重置行高 */
}

.appointment-status.pending {
  background-color: #faad14;
}

.appointment-status.confirmed {
  background-color: #1890ff;
}

.appointment-status.completed {
  background-color: #52c41a;
}

.appointment-status.cancelled {
  background-color: #f5222d;
}

.appointment-status.rejected {
  background-color: #ff4d4f;
}

.appointment-time {
  font-size: 24rpx;
  color: #666;
}

.appointment-info {
  padding-top: 10rpx;
}

.info-row {
  display: flex;
  margin-bottom: 16rpx;
}

.info-label {
  width: 160rpx;
  font-size: 26rpx;
  color: #666;
  flex-shrink: 0;
}

.info-value {
  flex: 1;
  font-size: 26rpx;
  color: #333;
  word-break: break-all;
}

.info-value.price {
  color: #ff4d4f;
  font-weight: 500;
}

.info-value.verified {
  color: #52c41a;
  font-weight: 500;
}

.info-value.unverified {
  color: #faad14;
  font-weight: 500;
}

/* 参考图片预览 */
.reference-images {
  margin-top: 15rpx;
  display: flex;
  flex-direction: column;
  border-top: 1rpx dashed #f0f0f0;
  padding-top: 15rpx;
}

.image-preview {
  display: flex;
  flex-wrap: wrap;
  margin-top: 10rpx;
}

.preview-image {
  width: 140rpx;
  height: 140rpx;
  margin-right: 10rpx;
  margin-bottom: 10rpx;
  border-radius: 8rpx;
  object-fit: cover;
}

/* 预约底部 */
.appointment-footer {
  display: flex;
  justify-content: flex-end;
  margin-top: 20rpx;
  padding-top: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}

.view-detail-btn {
  font-size: 24rpx;
  color: #1890ff;
  padding: 8rpx 20rpx;
  border: 1rpx solid #1890ff;
  border-radius: 100rpx;
}

/* 加载更多 */
.load-more, .loading-more {
  text-align: center;
  padding: 20rpx 0;
  font-size: 24rpx;
  color: #999;
}

.loading-more {
  display: flex;
  justify-content: center;
  align-items: center;
}

.loading-more .loading-icon {
  margin-right: 10rpx;
} 

/* 电话号码容器和拨打按钮 */
.phone-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.call-btn {
  font-size: 22rpx;
  color: #1890ff;
  background-color: #e6f7ff;
  padding: 4rpx 16rpx;
  border-radius: 100rpx;
  border: 1rpx solid #91d5ff;
  margin-left: 15rpx;
  flex-shrink: 0;
} 

/* 状态筛选区域样式 */
.status-filter {
  padding: 5rpx 20rpx 10rpx 20rpx;
  background-color: #ffffff;
  border-bottom: 1rpx solid #f0f0f0;
  position: fixed;
  top: 190rpx; /* 顶部状态栏的高度 */
  left: 0;
  right: 0;
  z-index: 999;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05); /* 将阴影移至筛选栏底部 */
}

.filter-scroll {
  white-space: nowrap;
  overflow-x: auto;
  margin: 0;
  padding: 10rpx 0; /* 添加上下内边距 */
}

.filter-item {
  display: inline-block;
  padding: 8rpx 25rpx;
  margin: 0 15rpx;
  font-size: 28rpx;
  color: #666666;
  background-color: transparent; /* 更改为透明背景 */
  border-radius: 0; /* 无圆角 */
  transition: all 0.2s;
  position: relative; /* 用于下边框定位 */
}

.filter-item:first-child {
  margin-left: 0;
}

.filter-item.active {
  color: #07c160;
  font-weight: 500;
  background-color: transparent; /* 活动状态也是透明背景 */
}

/* 添加活动状态下的底部指示线 */
.filter-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 20%;
  right: 20%;
  height: 4rpx;
  background-color: #07c160;
} 