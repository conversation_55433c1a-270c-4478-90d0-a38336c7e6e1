<view class="consumption-container">
  <!-- 状态栏安全区域 -->
  <view class="status-bar"></view>
  
  <!-- 头部导航栏 -->
  <view class="header">
    <view class="back-btn" bindtap="navigateBack">
      <view class="arrow-left"></view>
    </view>
    <view class="header-title">余额消费记录</view>
    <view class="placeholder-btn"></view>
  </view>

  <!-- 搜索框 -->
  <view class="search-box">
    <input class="search-input" placeholder="请输入用户ID搜索" value="{{searchKeyword}}" bindinput="inputKeyword" confirm-type="search" bindconfirm="search"></input>
    <view class="search-btn" bindtap="search">搜索</view>
    <view class="filter-btn" bindtap="showFilter">筛选</view>
  </view>

  <!-- 标签页 -->
  <view class="tab-container">
    <scroll-view scroll-x class="tabs" enable-flex>
      <block wx:for="{{tabs}}" wx:key="value">
        <view class="tab-item {{currentTab === item.value ? 'active' : ''}}" bindtap="switchTab" data-tab="{{item.value}}">{{item.label}}</view>
      </block>
    </scroll-view>
  </view>

  <!-- 消费记录列表 -->
  <view class="consumption-list" wx:if="{{!loading && currentTab !== 'balance' && consumptionRecords.length > 0}}">
    <block wx:for="{{consumptionRecords}}" wx:key="_id">
      <view class="record-item">
        <view class="item-content">
          <view class="item-row">
            <view class="label">用户ID:</view>
            <view class="value user-id">{{item.openid}}</view>
          </view>
          <view class="item-row">
            <view class="label">金额:</view>
            <view class="value amount {{item.type}}">{{item.amountStr}}</view>
          </view>
          <view class="item-row">
            <view class="label">扣除余额:</view>
            <view class="value">{{item.deductBalanceStr}}</view>
          </view>
          <view class="item-row">
            <view class="label">扣除赠送:</view>
            <view class="value">{{item.deductBonusStr}}</view>
          </view>
          <view class="item-row">
            <view class="label">类型:</view>
            <view class="value type {{item.type}}">{{item.typeText}}</view>
          </view>
          <view class="item-row">
            <view class="label">操作员:</view>
            <view class="value">{{item.operatorId || '系统操作'}}</view>
          </view>
          <view class="item-row">
            <view class="label">消费时间:</view>
            <view class="value">{{item.createTimeStr}}</view>
          </view>
          <view class="item-row">
            <view class="label">备注:</view>
            <view class="value remark">{{item.remark || '无'}}</view>
          </view>
        </view>
      </view>
    </block>

    <!-- 加载更多 -->
    <view class="load-more" wx:if="{{loadingMore}}">加载中...</view>
    <view class="no-more" wx:if="{{!loadingMore && consumptionRecords.length >= totalRecords}}">没有更多数据</view>
  </view>

  <!-- 余额统计视图 -->
  <view class="balance-stats-container" wx:if="{{currentTab === 'balance'}}">
    <!-- 余额统计摘要卡片 -->
    <view class="balance-summary-card" wx:if="{{!loadingBalance}}">
      <view class="summary-title">余额统计摘要</view>
      <view class="summary-content">
        <view class="summary-item">
          <view class="summary-value">{{totalBalanceStats.totalUsers || 0}}</view>
          <view class="summary-label">总用户数</view>
        </view>
        <view class="summary-item">
          <view class="summary-value">{{totalBalanceStats.totalBalanceStr || '￥0.00'}}</view>
          <view class="summary-label">现金余额总额</view>
        </view>
        <view class="summary-item">
          <view class="summary-value">{{totalBalanceStats.totalBonusBalanceStr || '￥0.00'}}</view>
          <view class="summary-label">赠送余额总额</view>
        </view>
        <view class="summary-item">
          <view class="summary-value">{{totalBalanceStats.totalAmountStr || '￥0.00'}}</view>
          <view class="summary-label">余额总计</view>
        </view>
      </view>
    </view>

    <!-- 用户余额列表 -->
    <view class="balance-list" wx:if="{{!loadingBalance && userBalanceList.length > 0}}">
      <block wx:for="{{userBalanceList}}" wx:key="openid">
        <view class="balance-item">
          <view class="user-info">
            <image class="user-avatar" src="{{item.avatarUrl || '/images/default-avatar.png'}}" mode="aspectFill"></image>
            <view class="user-name">{{item.nickName}}</view>
          </view>
          <view class="balance-info">
            <view class="balance-row">
              <view class="balance-label">用户ID:</view>
              <view class="balance-value user-id">{{item.openid}}</view>
            </view>
            <view class="balance-row">
              <view class="balance-label">现金余额:</view>
              <view class="balance-value">{{item.balanceStr}}</view>
            </view>
            <view class="balance-row">
              <view class="balance-label">赠送余额:</view>
              <view class="balance-value">{{item.bonusBalanceStr}}</view>
            </view>
            <view class="balance-row">
              <view class="balance-label">总余额:</view>
              <view class="balance-value total">{{item.totalBalanceStr}}</view>
            </view>
            <view class="balance-row" wx:if="{{item.lastUpdateTimeStr}}">
              <view class="balance-label">最后更新:</view>
              <view class="balance-value">{{item.lastUpdateTimeStr}}</view>
            </view>
            <view class="balance-row refund-row" wx:if="{{item.totalBalance > 0}}">
              <view class="balance-label"></view>
              <view class="balance-value refund-container">
                <button class="refund-btn" bindtap="showRefundModal" data-user="{{item}}">退款</button>
              </view>
            </view>
          </view>
        </view>
      </block>

      <!-- 加载更多 -->
      <view class="load-more" wx:if="{{loadingMoreBalance}}">加载中...</view>
      <view class="no-more" wx:if="{{!loadingMoreBalance && userBalanceList.length >= totalUserBalance}}">没有更多数据</view>
    </view>

    <!-- 无数据提示 -->
    <view class="empty-container" wx:if="{{!loadingBalance && userBalanceList.length === 0}}">
      <view class="empty-text">暂无余额数据~</view>
    </view>
  </view>

  <!-- 无数据提示 -->
  <view class="empty-container" wx:if="{{!loading && currentTab !== 'balance' && consumptionRecords.length === 0}}">
    <view class="empty-text">暂无消费记录~</view>
  </view>

  <!-- 加载中 -->
  <view class="loading-container" wx:if="{{(loading && page === 1) || (loadingBalance && balancePage === 1)}}">
    <view class="loading">加载中...</view>
  </view>

  <!-- 筛选弹窗 -->
  <view class="filter-modal {{showFilterModal ? 'show' : ''}}" wx:if="{{showFilterModal}}">
    <view class="modal-mask" bindtap="closeFilter"></view>
    <view class="modal-content">
      <view class="modal-header">
        <text>筛选条件</text>
        <view class="close-icon" bindtap="closeFilter">×</view>
      </view>

      <view class="filter-form">
        <view class="form-item">
          <view class="form-label">开始日期</view>
          <picker mode="date" value="{{dateRange.start}}" bindchange="bindStartDateChange">
            <view class="picker">{{dateRange.start || '请选择开始日期'}}</view>
          </picker>
        </view>

        <view class="form-item">
          <view class="form-label">结束日期</view>
          <picker mode="date" value="{{dateRange.end}}" bindchange="bindEndDateChange">
            <view class="picker">{{dateRange.end || '请选择结束日期'}}</view>
          </picker>
        </view>

        <view class="filter-btns">
          <button class="btn-reset" bindtap="resetFilter">重置</button>
          <button class="btn-apply" bindtap="applyFilter">应用</button>
        </view>
      </view>
    </view>
  </view>

  <!-- 退款弹窗 -->
  <view class="refund-modal {{showRefundModal ? 'show' : ''}}" wx:if="{{showRefundModal}}">
    <view class="modal-mask" bindtap="closeRefundModal"></view>
    <view class="modal-content">
      <view class="modal-header">
        <text>余额退款</text>
        <view class="close-icon" bindtap="closeRefundModal">×</view>
      </view>

      <view class="refund-form">
        <view class="form-item">
          <view class="form-label">用户:</view>
          <view class="form-value">{{currentUser.nickName}}</view>
        </view>
        <view class="form-item">
          <view class="form-label">现金余额:</view>
          <view class="form-value">{{currentUser.balanceStr}}</view>
        </view>
        <view class="form-item">
          <view class="form-label">赠送余额:</view>
          <view class="form-value">{{currentUser.bonusBalanceStr}}</view>
        </view>
        <view class="form-item">
          <view class="form-label">总余额:</view>
          <view class="form-value">{{currentUser.totalBalanceStr}}</view>
        </view>

        <view class="form-item">
          <view class="form-label">退款金额:</view>
          <input class="form-input refund-amount-input" type="digit" placeholder="请输入退款金额" value="{{refundAmount}}" bindinput="inputRefundAmount" />
        </view>
        <view class="form-item">
          <view class="form-label">备注:</view>
          <input class="form-input" placeholder="请输入退款备注" value="{{refundRemark}}" bindinput="inputRefundRemark" />
        </view>

        <view class="checkbox-item">
          <checkbox checked="{{clearBalance}}" bindtap="toggleClearBalance"></checkbox>
          <text class="checkbox-label">清零余额</text>
          <text class="checkbox-tip">(选中将清零用户所有余额)</text>
        </view>

        <view class="refund-btns">
          <button class="btn-cancel" bindtap="closeRefundModal">取消</button>
          <button class="btn-confirm" bindtap="confirmRefund">确认退款</button>
        </view>
      </view>
    </view>
  </view>
</view> 