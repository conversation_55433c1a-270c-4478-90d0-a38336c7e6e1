<!--pages/admin/service/service.wxml-->
<view class="page-container">
  <view class="page-header">
    <view class="back-icon" bindtap="navigateBack">
      <text class="iconfont icon-back">←</text>
    </view>
    <view class="header-title">视频管理后台</view>
    <view class="header-actions">
    </view>
  </view>
  
  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{isLoading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>
  
  <!-- 内容区域 - 添加可滚动视图 -->
  <scroll-view class="content-admin-container" scroll-y="true" enable-back-to-top="true" lower-threshold="50" bindscrolltolower="onReachBottom" wx:else>
    <!-- 视频列表 -->
    <view class="video-list" wx:if="{{videoList.length > 0}}">
      <view class="video-item" wx:for="{{videoList}}" wx:key="id">
        <!-- 视频封面 -->
        <image class="video-cover" src="{{item.coverUrl}}" mode="aspectFill" bindtap="viewVideoDetail" data-id="{{item._id}}"></image>
        
        <!-- 右侧内容区域 -->
        <view class="right-content">
          <!-- 标题行 -->
          <view class="title-row">
            <view class="video-title" bindtap="viewVideoDetail" data-id="{{item._id}}">{{item.title || item.mainTitle}}</view>
            <!-- 序号 -->
            <view class="video-order">{{item.order || item.sortOrder}}</view>
          </view>
          
          <!-- 副标题 -->
          <view class="video-subtitle">{{item.subtitle || item.subTitle}}</view>
          
          <!-- 操作按钮区域 -->
          <view class="video-actions">
            <view class="action-button visibility" catchtap="toggleVideoVisibility" data-id="{{item._id}}" data-visible="{{item.isVisible}}">
              <text>{{item.isVisible ? '隐藏' : '显示'}}</text>
            </view>
            <view class="action-button edit" catchtap="editVideo" data-id="{{item._id}}">
              <text>编辑</text>
            </view>
            <view class="action-button delete" catchtap="deleteVideo" data-id="{{item._id}}">
              <text>删除</text>
            </view>
          </view>
          
          <!-- 视频可见性状态移到右上角 -->
          <view class="status-container">
            <view class="video-visibility {{item.isVisible ? 'visible' : 'hidden'}}">
              <view class="status-indicator"></view>
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 空状态 -->
    <view class="empty-state" wx:else>
      <text class="empty-text">暂无视频内容</text>
      <text class="empty-subtext">点击下方按钮添加新视频</text>
    </view>
    
    <!-- 添加底部安全区域 -->
    <view class="safe-bottom-area"></view>
  </scroll-view>
  
  <!-- 添加按钮 -->
  <view class="add-button" bindtap="addNewVideo">
    <text class="add-icon">+</text>
  </view>
  
  <!-- 视频详情模态框 -->
  <video-detail-modal id="videoDetailModal"></video-detail-modal>
  
  <!-- 视频上传/编辑模态框 -->
  <video-upload 
    visible="{{uploadModalVisible}}" 
    videoInfo="{{currentVideoInfo}}" 
    bind:close="closeUploadModal"
    bind:success="onUploadSuccess">
  </video-upload>
</view>