<!-- configManager云函数测试页面 -->
<view class="container">
  <view class="header">
    <view class="title">configManager云函数测试</view>
    <button class="rerun-btn" bindtap="onTapRerunTests" disabled="{{loading}}">
      {{loading ? '测试中...' : '重新测试'}}
    </button>
  </view>
  
  <scroll-view scroll-y="true" class="results-container">
    <block wx:if="{{testResults.length > 0}}">
      <view class="results-list">
        <view wx:for="{{testResults}}" wx:key="index" class="result-item {{item.success ? 'success' : 'error'}}">
          <view class="result-header">
            <view class="result-api">{{item.apiName}}</view>
            <view class="result-time">{{item.timestamp}}</view>
            <view class="result-status">{{item.success ? '成功' : '失败'}}</view>
          </view>
          <view class="result-content">
            <text selectable="true">{{item.result}}</text>
          </view>
          <view class="result-actions">
            <button size="mini" bindtap="copyTestResult" data-index="{{index}}">复制结果</button>
          </view>
        </view>
      </view>
    </block>
    
    <view wx:if="{{loading}}" class="loading">
      <view class="loading-text">正在进行测试，请稍候...</view>
    </view>
    
    <view wx:if="{{!loading && testResults.length === 0}}" class="no-results">
      <view class="no-results-text">暂无测试结果</view>
    </view>
  </scroll-view>
</view> 