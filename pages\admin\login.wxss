.admin-login-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: calc(100vh + 200rpx); /* 进一步增加额外高度确保完全覆盖 */
  background: linear-gradient(to top,rgb(75, 75, 75), #000000);
  padding: 30rpx;
  padding-bottom: 250rpx; /* 大幅增加底部内边距，确保背景完全覆盖到底部 */
  justify-content: center;
  margin-top: -150rpx; /* 将整体向上移动的距离从-100rpx调整为-150rpx */
  box-sizing: border-box; /* 确保padding计算在容器高度内 */
  position: relative; /* 确保定位正确 */
}

.login-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 40rpx; /* 减少头部与表单之间的间距 */
}

.logo-container {
  width: 200rpx;
  height: 200rpx;
  border-radius: 50%;
  overflow: hidden;
  background-color: transparent;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 20rpx;
}

.logo {
  width: 180rpx;
  height: 180rpx;
}

/* 管理员入口文字样式 */
.admin-title {
  font-size: 36rpx;
  font-weight: 500;
  color: #ffffff;
  margin-top: 20rpx;
  letter-spacing: 2rpx;
}

.login-form {
  width: 90%;
  background-color: transparent;
  border-radius: 20rpx;
  padding: 20rpx 30rpx;
}

.input-group {
  margin-bottom: 30rpx;
  position: relative;
}

.input-label {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 12rpx;
  font-weight: normal;
}

.input-control {
  height: 88rpx;
  background-color: rgba(255, 255, 255, 0.08);
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 30rpx;
  color: #ffffff;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.input-control::placeholder {
  color: rgba(255, 255, 255, 0.4);
}

/* 输入框获得焦点时的样式 */
.input-control:focus {
  border-color: rgba(255, 255, 255, 0.5);
  background-color: rgba(255, 255, 255, 0.12);
}

.login-btn {
  width: 100%;
  height: 88rpx;
  background: rgba(255, 255, 255, 0.9);
  color: #333333;
  border-radius: 8rpx;
  font-size: 32rpx;
  margin-top: 50rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: 500;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  border: none;
}

.btn-hover {
  opacity: 0.9;
  background: rgba(255, 255, 255, 0.85);
}

.back-btn {
  position: absolute;
  bottom: 300rpx; /* 进一步调整返回按钮位置，适应更高的容器 */
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
  padding: 12rpx 30rpx;
  border-radius: 8rpx;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.back-btn:active {
  opacity: 0.8;
  background: rgba(255, 255, 255, 0.15);
}

/* 品牌运营管理系统文字样式 */
.system-title {
  position: absolute;
  bottom: 240rpx; /* 进一步调整系统标题位置，适应更高的容器 */
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.6);
  letter-spacing: 1rpx;
}