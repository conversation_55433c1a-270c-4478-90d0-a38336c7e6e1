<view class="tab-bar" wx:if="{{visible}}">
  <!-- 移除多余的加载指示器组件 -->
  
  <view class="flex-col justify-start relative">
    <image class="nav-bar-bg" src="/static/导航栏.svg" mode="aspectFill"></image>
    
    <!-- 四个均匀分布的点击区域 -->
    <view class="tab-hit-area area-1" data-path="{{list[0].pagePath}}" data-index="0" bindtap="switchTab"></view>
    <view class="tab-hit-area area-2" data-path="{{list[1].pagePath}}" data-index="1" bindtap="switchTab"></view>
    <view class="tab-hit-area area-3" data-path="{{list[2].pagePath}}" data-index="2" bindtap="switchTab"></view>
    <view class="tab-hit-area area-4" data-path="{{list[3].pagePath}}" data-index="3" bindtap="switchTab"></view>
    
    <!-- 四个导航项 -->
    <view wx:for="{{list}}" wx:key="index" data-path="{{item.pagePath}}" data-index="{{index}}" bindtap="switchTab" 
          class="flex-col {{index === 0 ? 'nav-item-1' : (index === 1 ? 'nav-item-2' : (index === 2 ? 'nav-item-3' : 'nav-item-4'))}}">
      <view class="self-center icon-container">
        <image src="{{selected === index ? item.selectedIconPath : item.iconPath}}" style="width: 100%; height: 100%;"></image>
      </view>
      <view class="flex-col justify-start self-stretch text-container">
        <text class="nav-text" style="color: {{selected === index ? selectedColor : color}}">{{item.text}}</text>
      </view>
    </view>
  </view>
</view> 