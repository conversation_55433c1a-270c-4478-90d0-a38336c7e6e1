// 视频链接测试页面
const app = getApp()

Page({
  data: {
    videoId: '',
    baseId: '',
    videoKey: '',
    videoUrl: '',
    testResults: [],
    isLoading: false
  },

  onLoad() {
    // 页面加载时，获取全局配置
    const cosBaseUrl = (app.globalData && app.globalData.cosBaseUrl) || 'https://naildidi-13603.cos.ap-guangzhou.myqcloud.com/';
    const videoDir = (app.globalData && app.globalData.videoDirName) || '视频列表';
    
    this.setData({
      cosBaseUrl: cosBaseUrl,
      videoDir: videoDir
    });
  },

  // 输入框内容变化处理
  onInputChange(e) {
    const { field } = e.currentTarget.dataset;
    this.setData({
      [field]: e.detail.value
    });
  },

  // 测试通过云函数获取视频URL
  testCloudFunction() {
    this.setData({ isLoading: true });
    const { videoId, baseId, videoKey } = this.data;
    
    if (!videoId && !baseId && !videoKey) {
      wx.showToast({
        title: '请输入至少一个参数',
        icon: 'none'
      });
      this.setData({ isLoading: false });
      return;
    }

    // 记录测试开始
    this._addTestResult('开始测试云函数获取视频URL');
    
    // 准备参数
    const params = {};
    if (videoKey) {
      params.fileKey = videoKey;
      this._addTestResult(`使用fileKey参数: ${videoKey}`);
    } else if (baseId) {
      params.baseId = baseId;
      this._addTestResult(`使用baseId参数: ${baseId}`);
    } else {
      // 如果只有videoId，尝试构造baseId
      const paddedId = videoId.toString().padStart(3, '0');
      params.baseId = `${paddedId}_视频_无副标题_0`;
      this._addTestResult(`使用构造的baseId参数: ${params.baseId}`);
    }
    
    // 添加时间戳
    params.timestamp = Date.now();
    
    // 调用云函数
    wx.cloud.callFunction({
      name: 'resourceManager',
      data: {
        action: 'getVideoUrl',
        params: params
      },
      success: res => {
        this._addTestResult(`云函数调用成功: ${JSON.stringify(res.result)}`);
        
        if (res.result && res.result.code === 0 && res.result.data && res.result.data.videoUrl) {
          const videoUrl = res.result.data.videoUrl;
          this._addTestResult(`成功获取视频URL: ${videoUrl}`);
          this.setData({ videoUrl });
        } else {
          this._addTestResult(`获取视频URL失败: ${res.result ? res.result.message : '未知错误'}`);
        }
        
        this.setData({ isLoading: false });
      },
      fail: err => {
        this._addTestResult(`云函数调用失败: ${JSON.stringify(err)}`);
        this.setData({ isLoading: false });
      }
    });
  },
  
  // 测试直接构造视频URL
  testDirectConstruction() {
    this.setData({ isLoading: true });
    const { videoId, baseId } = this.data;
    const { cosBaseUrl, videoDir } = this.data;
    
    if (!videoId && !baseId) {
      wx.showToast({
        title: '请输入视频ID或baseId',
        icon: 'none'
      });
      this.setData({ isLoading: false });
      return;
    }
    
    // 记录测试开始
    this._addTestResult('开始测试直接构造视频URL');
    
    // 确定要使用的baseId
    let targetBaseId = baseId;
    if (!targetBaseId && videoId) {
      // 如果没有提供baseId，但有videoId，构造一个简单的baseId
      const paddedId = videoId.toString().padStart(3, '0');
      targetBaseId = `${paddedId}_视频_无副标题_0`;
      this._addTestResult(`使用构造的baseId: ${targetBaseId}`);
    }
    
    // 尝试不同的格式和命名方式
    const formats = [
      // 格式1: baseId_video.mp4
      `${targetBaseId}_video.mp4`,
      // 格式2: baseId_video.mov
      `${targetBaseId}_video.mov`,
      // 格式3: baseId.mp4
      `${targetBaseId}.mp4`,
      // 格式4: baseId.mov
      `${targetBaseId}.mov`,
      // 格式5: 只使用ID.mp4
      `${videoId ? videoId.toString().padStart(3, '0') : ''}.mp4`
    ];
    
    // 测试所有格式
    this._testAllFormats(formats, 0);
  },
  
  // 递归测试所有格式
  _testAllFormats(formats, index) {
    if (index >= formats.length) {
      this._addTestResult('所有格式测试完成');
      this.setData({ isLoading: false });
      return;
    }
    
    const format = formats[index];
    if (!format) {
      this._testAllFormats(formats, index + 1);
      return;
    }
    
    const { cosBaseUrl, videoDir } = this.data;
    const videoUrl = `${cosBaseUrl}${videoDir}/${format}`;
    
    this._addTestResult(`测试格式 ${index + 1}: ${videoUrl}`);
    
    // 尝试请求视频URL
    wx.request({
      url: videoUrl,
      method: 'HEAD',
      success: (res) => {
        if (res.statusCode === 200) {
          this._addTestResult(`✅ 格式 ${index + 1} 有效: ${videoUrl}`);
          this.setData({ videoUrl });
        } else {
          this._addTestResult(`❌ 格式 ${index + 1} 无效: 状态码 ${res.statusCode}`);
        }
        this._testAllFormats(formats, index + 1);
      },
      fail: (err) => {
        this._addTestResult(`❌ 格式 ${index + 1} 请求失败: ${JSON.stringify(err)}`);
        this._testAllFormats(formats, index + 1);
      }
    });
  },
  
  // 测试自定义URL
  testCustomUrl() {
    const customUrl = this.data.customUrl;
    if (!customUrl) {
      wx.showToast({
        title: '请输入自定义URL',
        icon: 'none'
      });
      return;
    }
    
    this._addTestResult(`测试自定义URL: ${customUrl}`);
    
    wx.request({
      url: customUrl,
      method: 'HEAD',
      success: (res) => {
        if (res.statusCode === 200) {
          this._addTestResult(`✅ 自定义URL有效: ${customUrl}`);
          this.setData({ videoUrl: customUrl });
        } else {
          this._addTestResult(`❌ 自定义URL无效: 状态码 ${res.statusCode}`);
        }
      },
      fail: (err) => {
        this._addTestResult(`❌ 自定义URL请求失败: ${JSON.stringify(err)}`);
      }
    });
  },
  
  // 播放视频
  playVideo() {
    if (!this.data.videoUrl) {
      wx.showToast({
        title: '请先获取视频URL',
        icon: 'none'
      });
      return;
    }
    
    this._addTestResult(`尝试播放视频: ${this.data.videoUrl}`);
    
    // 创建视频上下文
    const videoContext = wx.createVideoContext('testVideo', this);
    if (videoContext) {
      videoContext.play();
      this._addTestResult('已调用播放方法');
    } else {
      this._addTestResult('创建视频上下文失败');
    }
  },
  
  // 视频播放事件
  onVideoPlay() {
    this._addTestResult('✅ 视频开始播放');
  },
  
  // 视频错误事件
  onVideoError(e) {
    this._addTestResult(`❌ 视频播放错误: ${JSON.stringify(e.detail)}`);
  },
  
  // 添加测试结果
  _addTestResult(result) {
    const timestamp = new Date().toLocaleTimeString();
    const testResults = this.data.testResults;
    testResults.unshift(`[${timestamp}] ${result}`);
    
    // 限制结果数量，避免过多
    if (testResults.length > 50) {
      testResults.pop();
    }
    
    this.setData({ testResults });
    
    // 同时输出到控制台
    console.log(`[测试] ${result}`);
  },
  
  // 清空测试结果
  clearResults() {
    this.setData({ testResults: [] });
  },
  
  // 复制视频URL
  copyVideoUrl() {
    if (!this.data.videoUrl) {
      wx.showToast({
        title: '没有可复制的URL',
        icon: 'none'
      });
      return;
    }
    
    wx.setClipboardData({
      data: this.data.videoUrl,
      success: () => {
        wx.showToast({
          title: 'URL已复制',
          icon: 'success'
        });
      }
    });
  }
}) 