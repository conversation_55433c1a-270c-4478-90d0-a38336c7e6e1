/* configManager云函数测试页面样式 */
.container {
  padding: 20rpx;
  height: 100vh;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  background-color: #f5f5f5;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #e0e0e0;
  margin-bottom: 20rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.rerun-btn {
  font-size: 28rpx;
  background-color: #4a90e2;
  color: white;
  padding: 10rpx 30rpx;
  border-radius: 8rpx;
}

.results-container {
  flex: 1;
  width: 100%;
}

.results-list {
  width: 100%;
}

.result-item {
  margin-bottom: 30rpx;
  border-radius: 8rpx;
  background-color: white;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  border-bottom: 1rpx solid #e0e0e0;
}

.result-api {
  font-weight: bold;
  font-size: 28rpx;
  color: #333;
  flex: 1;
}

.result-time {
  font-size: 24rpx;
  color: #999;
  margin-right: 20rpx;
}

.result-status {
  font-size: 24rpx;
  padding: 6rpx 20rpx;
  border-radius: 30rpx;
  text-align: center;
}

.success .result-status {
  background-color: #e8f5e9;
  color: #4caf50;
}

.error .result-status {
  background-color: #ffebee;
  color: #f44336;
}

.result-content {
  padding: 20rpx;
  font-size: 24rpx;
  color: #666;
  background-color: #fafafa;
  word-break: break-all;
  max-height: 600rpx;
  overflow-y: auto;
}

.result-content text {
  white-space: pre-wrap;
  font-family: 'Courier New', Courier, monospace;
}

.result-actions {
  padding: 20rpx;
  display: flex;
  justify-content: flex-end;
  border-top: 1rpx solid #e0e0e0;
}

.result-actions button {
  background-color: #f5f5f5;
  color: #4a90e2;
  font-size: 24rpx;
}

.loading {
  display: flex;
  justify-content: center;
  padding: 40rpx 0;
}

.loading-text {
  color: #999;
  font-size: 28rpx;
}

.no-results {
  display: flex;
  justify-content: center;
  padding: 40rpx 0;
}

.no-results-text {
  color: #999;
  font-size: 28rpx;
} 